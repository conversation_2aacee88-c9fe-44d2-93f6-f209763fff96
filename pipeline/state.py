"""
Pipeline状态管理模块 - 支持断点续传和状态持久化
"""

import json
import time
from pathlib import Path
from typing import Dict, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class StepCheckpoint:
    """步骤检查点"""
    name: str
    status: str
    start_time: Optional[float]
    end_time: Optional[float]
    success: bool
    message: str
    output_files: list
    metrics: dict


@dataclass
class PipelineCheckpoint:
    """流水线检查点"""
    pipeline_name: str
    status: str
    start_time: Optional[float]
    current_step: Optional[str]
    completed_steps: list
    failed_steps: list
    step_checkpoints: Dict[str, StepCheckpoint]
    created_at: str


class PipelineState:
    """流水线状态管理器"""

    def __init__(self, state_file: str = ".pipeline_state.json"):
        self.state_file = Path(state_file)
        self.current_checkpoint: Optional[PipelineCheckpoint] = None

    def save_checkpoint(self, pipeline_name: str, status: str,
                       current_step: Optional[str] = None,
                       start_time: Optional[float] = None) -> None:
        """保存流水线检查点"""
        if self.current_checkpoint is None:
            self.current_checkpoint = PipelineCheckpoint(
                pipeline_name=pipeline_name,
                status=status,
                start_time=start_time or time.time(),
                current_step=current_step,
                completed_steps=[],
                failed_steps=[],
                step_checkpoints={},
                created_at=datetime.now().isoformat()
            )
        else:
            self.current_checkpoint.status = status
            self.current_checkpoint.current_step = current_step

        self._save_to_file()

    def save_step_checkpoint(self, step_name: str, status: str,
                           start_time: Optional[float] = None,
                           end_time: Optional[float] = None,
                           success: bool = False,
                           message: str = "",
                           output_files: list = None,
                           metrics: dict = None) -> None:
        """保存步骤检查点"""
        if self.current_checkpoint is None:
            raise RuntimeError("必须先保存流水线检查点")

        step_checkpoint = StepCheckpoint(
            name=step_name,
            status=status,
            start_time=start_time,
            end_time=end_time,
            success=success,
            message=message,
            output_files=output_files or [],
            metrics=metrics or {}
        )

        self.current_checkpoint.step_checkpoints[step_name] = step_checkpoint

        # 更新完成/失败步骤列表
        if success and step_name not in self.current_checkpoint.completed_steps:
            self.current_checkpoint.completed_steps.append(step_name)
        elif not success and step_name not in self.current_checkpoint.failed_steps:
            self.current_checkpoint.failed_steps.append(step_name)

        self._save_to_file()

    def load_checkpoint(self) -> Optional[PipelineCheckpoint]:
        """加载最新的检查点"""
        if not self.state_file.exists():
            return None

        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 重建StepCheckpoint对象
            step_checkpoints = {}
            for step_name, step_data in data.get('step_checkpoints', {}).items():
                step_checkpoints[step_name] = StepCheckpoint(**step_data)

            # 重建PipelineCheckpoint对象
            checkpoint_data = data.copy()
            checkpoint_data['step_checkpoints'] = step_checkpoints

            self.current_checkpoint = PipelineCheckpoint(**checkpoint_data)
            return self.current_checkpoint

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"警告：无法加载状态文件 {self.state_file}: {e}")
            return None

    def clear_state(self) -> None:
        """清除状态文件"""
        if self.state_file.exists():
            self.state_file.unlink()
        self.current_checkpoint = None

    def get_last_completed_step(self) -> Optional[str]:
        """获取最后完成的步骤"""
        if not self.current_checkpoint or not self.current_checkpoint.completed_steps:
            return None
        return self.current_checkpoint.completed_steps[-1]

    def is_step_completed(self, step_name: str) -> bool:
        """检查步骤是否已完成"""
        if not self.current_checkpoint:
            return False
        return step_name in self.current_checkpoint.completed_steps

    def get_step_status(self, step_name: str) -> Optional[str]:
        """获取步骤状态"""
        if not self.current_checkpoint:
            return None
        step_checkpoint = self.current_checkpoint.step_checkpoints.get(step_name)
        return step_checkpoint.status if step_checkpoint else None

    def get_resume_point(self) -> Optional[str]:
        """获取恢复执行的起点"""
        if not self.current_checkpoint:
            return None

        # 如果有失败的步骤，从第一个失败步骤开始
        if self.current_checkpoint.failed_steps:
            return self.current_checkpoint.failed_steps[0]

        # 如果当前有正在执行的步骤，从该步骤开始
        if self.current_checkpoint.current_step:
            current_step = self.current_checkpoint.current_step
            if not self.is_step_completed(current_step):
                return current_step

        # 否则返回None，表示从头开始或已全部完成
        return None

    def get_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        if not self.current_checkpoint:
            return {}

        total_steps = len(self.current_checkpoint.step_checkpoints)
        completed_steps = len(self.current_checkpoint.completed_steps)
        failed_steps = len(self.current_checkpoint.failed_steps)

        # 计算总执行时间
        total_duration = 0
        for step_checkpoint in self.current_checkpoint.step_checkpoints.values():
            if step_checkpoint.start_time and step_checkpoint.end_time:
                total_duration += step_checkpoint.end_time - step_checkpoint.start_time

        return {
            "total_steps": total_steps,
            "completed_steps": completed_steps,
            "failed_steps": failed_steps,
            "success_rate": completed_steps / total_steps if total_steps > 0 else 0,
            "total_duration": total_duration,
            "pipeline_status": self.current_checkpoint.status,
            "created_at": self.current_checkpoint.created_at
        }

    def _save_to_file(self) -> None:
        """保存状态到文件"""
        if self.current_checkpoint is None:
            return

        # 转换为可序列化的字典
        data = asdict(self.current_checkpoint)

        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"警告：无法保存状态文件: {e}")