"""
多仓库发现引擎
自动发现、分析和分类代码仓库
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    from .config_all import RepositoryConfig, RepositoryType
except ImportError:
    from config_all import RepositoryConfig, RepositoryType

logger = logging.getLogger(__name__)

@dataclass
class RepositoryMetrics:
    """仓库指标"""
    source_files: int
    header_files: int
    total_lines: int
    avg_file_size: float
    complexity_score: float
    has_tests: bool
    has_docs: bool

class RepositoryDiscoveryEngine:
    """仓库发现引擎"""
    
    def __init__(self, workspace_dir: str = "hammmer-workspace"):
        self.workspace_dir = Path(workspace_dir)
        self.excluded_dirs = {
            "Pods", "__pycache__", "build", "hammer-workspace",
            ".git", "DerivedData", ".xcworkspace", "node_modules"
        }
        self.excluded_patterns = {
            ".DS_Store", "Thumbs.db", "*.tmp", "*.log"
        }
    
    def discover_all_repositories(self) -> List[RepositoryConfig]:
        """发现所有仓库"""
        logger.info(f"🔍 开始扫描 {self.workspace_dir} 目录...")
        
        if not self.workspace_dir.exists():
            logger.error(f"❌ 工作空间目录不存在: {self.workspace_dir}")
            return []
        
        repositories = []
        
        # 并行分析仓库
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = {}
            
            for item in self.workspace_dir.iterdir():
                if self._should_analyze_directory(item):
                    future = executor.submit(self._analyze_repository, item)
                    futures[future] = item.name
            
            for future in as_completed(futures):
                repo_name = futures[future]
                try:
                    repo_config = future.result()
                    if repo_config:
                        repositories.append(repo_config)
                        logger.info(f"✅ 发现仓库: {repo_name}")
                    else:
                        logger.debug(f"⚠️  跳过目录: {repo_name}")
                except Exception as e:
                    logger.error(f"❌ 分析仓库失败 {repo_name}: {e}")
        
        # 按源文件数量排序
        repositories.sort(key=lambda x: x.source_files, reverse=True)
        
        logger.info(f"🎉 发现 {len(repositories)} 个仓库")
        return repositories
    
    def _should_analyze_directory(self, path: Path) -> bool:
        """判断是否应该分析该目录"""
        if not path.is_dir():
            return False
        
        if path.name in self.excluded_dirs:
            return False
        
        if path.name.startswith('.'):
            return False
        
        return True
    
    def _analyze_repository(self, repo_path: Path) -> Optional[RepositoryConfig]:
        """分析单个仓库"""
        repo_name = repo_path.name
        
        try:
            # 收集文件信息
            metrics = self._calculate_repository_metrics(repo_path)
            
            # 只包含有源文件的仓库
            if metrics.source_files == 0:
                return None
            
            # 推断仓库类型
            repo_type = self._infer_repository_type(repo_name, repo_path, metrics)
            
            # 计算优先级
            priority = self._calculate_priority(repo_name, metrics)
            
            # 发现schemes
            schemes = self._discover_schemes(repo_path)
            
            # 分析依赖关系
            dependencies = self._analyze_dependencies(repo_path)
            
            return RepositoryConfig(
                name=repo_name,
                path=str(repo_path),
                type=repo_type,
                source_files=metrics.source_files,
                header_files=metrics.header_files,
                priority=priority,
                enabled=True,
                schemes=schemes,
                dependencies=dependencies
            )
            
        except Exception as e:
            logger.error(f"分析仓库失败 {repo_name}: {e}")
            return None
    
    def _calculate_repository_metrics(self, repo_path: Path) -> RepositoryMetrics:
        """计算仓库指标"""
        source_files = 0
        header_files = 0
        total_lines = 0
        total_size = 0
        file_count = 0
        
        # 递归扫描文件
        for file_path in repo_path.rglob("*"):
            if file_path.is_file() and not self._should_exclude_file(file_path):
                try:
                    file_size = file_path.stat().st_size
                    total_size += file_size
                    file_count += 1
                    
                    if file_path.suffix in ['.m', '.mm']:
                        source_files += 1
                        # 计算行数（仅对源文件）
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                lines = len(f.readlines())
                                total_lines += lines
                        except:
                            pass
                    elif file_path.suffix == '.h':
                        header_files += 1
                        
                except (OSError, PermissionError):
                    continue
        
        # 计算平均文件大小
        avg_file_size = total_size / file_count if file_count > 0 else 0
        
        # 计算复杂度分数（基于文件数量和平均大小）
        complexity_score = (source_files * 0.7 + header_files * 0.3) * (avg_file_size / 1024)
        
        # 检查是否有测试和文档
        has_tests = self._has_tests(repo_path)
        has_docs = self._has_docs(repo_path)
        
        return RepositoryMetrics(
            source_files=source_files,
            header_files=header_files,
            total_lines=total_lines,
            avg_file_size=avg_file_size,
            complexity_score=complexity_score,
            has_tests=has_tests,
            has_docs=has_docs
        )
    
    def _should_exclude_file(self, file_path: Path) -> bool:
        """判断是否应该排除该文件"""
        # 排除隐藏文件
        if file_path.name.startswith('.'):
            return True
        
        # 排除特定模式
        for pattern in self.excluded_patterns:
            if pattern.startswith('*'):
                if file_path.name.endswith(pattern[1:]):
                    return True
            else:
                if file_path.name == pattern:
                    return True
        
        # 排除特定目录中的文件
        for part in file_path.parts:
            if part in self.excluded_dirs:
                return True
        
        return False
    
    def _infer_repository_type(self, repo_name: str, repo_path: Path, metrics: RepositoryMetrics) -> RepositoryType:
        """推断仓库类型"""
        name_lower = repo_name.lower()
        
        # 基于名称的推断
        if "foundation" in name_lower or "baseline" in name_lower:
            return RepositoryType.FOUNDATION
        elif "ui" in name_lower or "kit" in name_lower:
            return RepositoryType.UI_MODULE
        elif "business" in name_lower:
            return RepositoryType.BUSINESS
        elif "dragon" in name_lower or "tencentmap" in name_lower:
            return RepositoryType.CORE_MODULE
        elif "dk" in name_lower or "grag" in name_lower or "protocol" in name_lower:
            return RepositoryType.TOOLS
        
        # 基于内容的推断
        if self._contains_ui_components(repo_path):
            return RepositoryType.UI_MODULE
        elif self._contains_business_logic(repo_path):
            return RepositoryType.BUSINESS
        elif self._contains_foundation_code(repo_path):
            return RepositoryType.FOUNDATION
        
        # 默认为核心模块
        return RepositoryType.CORE_MODULE
    
    def _contains_ui_components(self, repo_path: Path) -> bool:
        """检查是否包含UI组件"""
        ui_indicators = ['View', 'Controller', 'UI', 'Widget', 'Button', 'Label']
        
        for file_path in repo_path.rglob("*.h"):
            if any(indicator in file_path.name for indicator in ui_indicators):
                return True
        
        return False
    
    def _contains_business_logic(self, repo_path: Path) -> bool:
        """检查是否包含业务逻辑"""
        business_indicators = ['Manager', 'Service', 'Business', 'Logic', 'Model']
        
        for file_path in repo_path.rglob("*.h"):
            if any(indicator in file_path.name for indicator in business_indicators):
                return True
        
        return False
    
    def _contains_foundation_code(self, repo_path: Path) -> bool:
        """检查是否包含基础代码"""
        foundation_indicators = ['Foundation', 'Base', 'Common', 'Util', 'Helper']
        
        for file_path in repo_path.rglob("*.h"):
            if any(indicator in file_path.name for indicator in foundation_indicators):
                return True
        
        return False
    
    def _calculate_priority(self, repo_name: str, metrics: RepositoryMetrics) -> int:
        """计算仓库优先级"""
        # 基础模块和核心模块优先级最高
        if any(keyword in repo_name for keyword in ['Foundation', 'Baseline', 'MiddlePlatform']):
            return 1
        
        # 大型仓库优先级较高
        if metrics.source_files > 1000:
            return 1
        elif metrics.source_files > 100:
            return 2
        else:
            return 3
    
    def _discover_schemes(self, repo_path: Path) -> List[str]:
        """发现Xcode schemes"""
        schemes = []
        
        # 查找.xcodeproj文件
        for xcodeproj in repo_path.rglob("*.xcodeproj"):
            schemes_dir = xcodeproj / "xcshareddata" / "xcschemes"
            if schemes_dir.exists():
                for scheme_file in schemes_dir.glob("*.xcscheme"):
                    schemes.append(scheme_file.stem)
        
        # 如果没有找到schemes，使用仓库名作为默认scheme
        if not schemes:
            schemes.append(repo_path.name)
        
        return schemes
    
    def _analyze_dependencies(self, repo_path: Path) -> List[str]:
        """分析依赖关系"""
        dependencies = set()
        
        # 分析import语句
        for source_file in repo_path.rglob("*.m"):
            try:
                with open(source_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 查找import语句
                import re
                imports = re.findall(r'#import\s+<([^/]+)/', content)
                dependencies.update(imports)
                
            except Exception:
                continue
        
        # 过滤掉系统框架
        system_frameworks = {'Foundation', 'UIKit', 'CoreGraphics', 'QuartzCore'}
        dependencies = dependencies - system_frameworks
        
        return list(dependencies)
    
    def _has_tests(self, repo_path: Path) -> bool:
        """检查是否有测试"""
        test_indicators = ['Test', 'Tests', 'Spec', 'UnitTest']
        
        for item in repo_path.rglob("*"):
            if item.is_dir() and any(indicator in item.name for indicator in test_indicators):
                return True
        
        return False
    
    def _has_docs(self, repo_path: Path) -> bool:
        """检查是否有文档"""
        doc_files = ['README.md', 'README.txt', 'CHANGELOG.md', 'docs']
        
        for doc_file in doc_files:
            if (repo_path / doc_file).exists():
                return True
        
        return False
    
    def generate_discovery_report(self, repositories: List[RepositoryConfig]) -> Dict:
        """生成发现报告"""
        total_source_files = sum(repo.source_files for repo in repositories)
        total_header_files = sum(repo.header_files for repo in repositories)
        
        # 按类型分组
        by_type = {}
        for repo in repositories:
            repo_type = repo.type.value
            if repo_type not in by_type:
                by_type[repo_type] = []
            by_type[repo_type].append(repo)
        
        # 按优先级分组
        by_priority = {}
        for repo in repositories:
            priority = repo.priority
            if priority not in by_priority:
                by_priority[priority] = []
            by_priority[priority].append(repo)
        
        return {
            "summary": {
                "total_repositories": len(repositories),
                "total_source_files": total_source_files,
                "total_header_files": total_header_files,
                "discovery_timestamp": "2025-07-09"
            },
            "by_type": {
                repo_type: [{"name": repo.name, "source_files": repo.source_files} for repo in repos]
                for repo_type, repos in by_type.items()
            },
            "by_priority": {
                f"priority_{priority}": [{"name": repo.name, "source_files": repo.source_files} for repo in repos]
                for priority, repos in by_priority.items()
            },
            "largest_repositories": [
                {"name": repo.name, "source_files": repo.source_files, "type": repo.type.value}
                for repo in sorted(repositories, key=lambda x: x.source_files, reverse=True)[:5]
            ]
        }
