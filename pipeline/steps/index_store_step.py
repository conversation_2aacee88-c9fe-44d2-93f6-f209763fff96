"""
IndexStoreStep索引生成步骤实现 - TDD GREEN阶段

实现最小代码使测试通过
"""

import os
import shutil
import subprocess
import logging
from pathlib import Path
from typing import List, Optional

from pipeline.core import PipelineStep, StepResult
from pipeline.config import PipelineConfig

logger = logging.getLogger(__name__)


class IndexStoreStep(PipelineStep):
    """索引生成步骤 - 使用xcodebuild生成Clang索引存储"""

    def __init__(self, config: PipelineConfig,
                 configuration: str = "Debug",
                 sdk: str = "iphonesimulator",
                 destination: str = "platform=iOS Simulator,name=iPhone 15",
                 timeout: int = 3600):
        super().__init__(
            name="index-store",
            description="索引生成步骤 - 使用xcodebuild生成Clang索引存储，为后续USR提取做准备"
        )
        self.config = config
        self.schemes = config.schemes
        self.configuration = configuration
        self.sdk = sdk
        self.destination = destination
        self.timeout = timeout

    def validate_prerequisites(self) -> bool:
        """验证前置条件"""
        # 检查xcodebuild工具
        if not shutil.which("xcodebuild"):
            logger.error("未找到xcodebuild工具")
            return False

        # 检查工作区文件
        workspace_path = Path(self.config.workspace_path)
        if not workspace_path.exists():
            logger.error(f"工作区文件不存在: {workspace_path}")
            return False

        return True

    def execute(self) -> StepResult:
        """执行索引生成"""
        logger.info("开始生成Clang索引存储")

        try:
            schemes_processed = 0
            schemes_failed = 0
            failed_schemes = []

            for scheme in self.schemes:
                logger.info(f"为scheme '{scheme}' 生成索引...")

                try:
                    cmd = self.build_xcodebuild_command(scheme)
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        timeout=self.timeout,
                        check=False
                    )

                    if result.returncode == 0:
                        logger.info(f"Scheme '{scheme}' 索引生成成功")
                        schemes_processed += 1
                    else:
                        logger.error(f"Scheme '{scheme}' 构建失败: {result.stderr}")
                        schemes_failed += 1
                        failed_schemes.append(scheme)

                except subprocess.TimeoutExpired:
                    logger.error(f"Scheme '{scheme}' 构建超时")
                    schemes_failed += 1
                    failed_schemes.append(scheme)

            # 判断整体结果
            if schemes_processed == 0:
                return StepResult(
                    success=False,
                    message=f"所有scheme构建失败: {', '.join(failed_schemes)}",
                    duration=0,
                    error=RuntimeError("构建失败"),
                    metrics={
                        "schemes_processed": schemes_processed,
                        "schemes_failed": schemes_failed,
                        "failed_schemes": failed_schemes
                    }
                )
            elif schemes_failed > 0:
                message = f"索引生成部分完成，成功: {schemes_processed}, 失败: {schemes_failed}"
            else:
                message = f"索引生成完成，处理了 {schemes_processed} 个scheme"

            return StepResult(
                success=True,
                message=message,
                duration=0,
                metrics={
                    "schemes_processed": schemes_processed,
                    "schemes_failed": schemes_failed,
                    "failed_schemes": failed_schemes
                }
            )

        except Exception as e:
            logger.error(f"索引生成过程中发生错误: {e}")
            return StepResult(
                success=False,
                message=f"索引生成失败: {str(e)}",
                duration=0,
                error=e
            )

    def build_xcodebuild_command(self, scheme: str) -> List[str]:
        """构建xcodebuild命令"""
        cmd = [
            "xcodebuild",
            "-workspace", self.config.workspace_path,
            "-scheme", scheme,
            "-configuration", self.configuration,
            "-sdk", self.sdk,
            "-destination", self.destination,
            "-derivedDataPath", self.config.derived_data_path,
            "COMPILER_INDEX_STORE_ENABLE=YES",
            "build"
        ]

        return cmd

    def verify_index_store_output(self) -> bool:
        """验证索引存储输出是否存在"""
        index_store_path = Path(self.config.derived_data_path) / "GraphIndex" / "Index.noindex" / "DataStore"

        if not index_store_path.exists():
            return False

        # 检查关键子目录
        v5_path = index_store_path / "v5"
        if not v5_path.exists():
            return False

        return True