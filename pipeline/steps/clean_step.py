"""
CleanStep清理步骤实现 - TDD GREEN阶段

实现最小代码使测试通过
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List

from pipeline.core import PipelineStep, StepResult
from pipeline.config import PipelineConfig

logger = logging.getLogger(__name__)


class CleanStep(PipelineStep):
    """清理步骤 - 删除之前运行产生的中间文件和目录"""

    def __init__(self, config: PipelineConfig, dry_run: bool = False):
        super().__init__(
            name="clean",
            description="清理流水线历史产物，确保可重复构建"
        )
        self.config = config
        self.dry_run = dry_run

    def validate_prerequisites(self) -> bool:
        """验证前置条件 - 清理步骤通常没有前置条件"""
        return True

    def execute(self) -> StepResult:
        """执行清理操作"""
        logger.info("开始清理流水线历史产物")

        try:
            cleanup_targets = self.get_cleanup_targets()
            deleted_items = 0
            total_size_freed = 0

            for target_path in cleanup_targets:
                path = Path(target_path)

                if path.exists():
                    if self.dry_run:
                        logger.info(f"[干运行] 将删除: {path}")
                        continue

                    # 计算大小（在删除前）
                    if path.is_file():
                        total_size_freed += path.stat().st_size
                        deleted_items += 1
                    elif path.is_dir():
                        for item in path.rglob('*'):
                            if item.is_file():
                                total_size_freed += item.stat().st_size
                                deleted_items += 1

                    # 删除文件或目录
                    if path.is_file():
                        path.unlink()
                        logger.info(f"删除文件: {path}")
                    elif path.is_dir():
                        shutil.rmtree(path)
                        logger.info(f"删除目录: {path}")
                else:
                    logger.debug(f"跳过不存在的路径: {path}")

            if self.dry_run:
                message = f"干运行完成，将清理 {len(cleanup_targets)} 个目标"
            else:
                message = f"清理完成，删除了 {deleted_items} 个项目，释放 {total_size_freed} 字节"

            return StepResult(
                success=True,
                message=message,
                duration=0,
                metrics={
                    "deleted_items": deleted_items,
                    "total_size_freed": total_size_freed,
                    "cleanup_targets": len(cleanup_targets)
                }
            )

        except PermissionError as e:
            logger.error(f"权限错误: {e}")
            return StepResult(
                success=False,
                message=f"权限错误: {str(e)}",
                duration=0,
                error=e
            )
        except Exception as e:
            logger.error(f"清理过程中发生错误: {e}")
            return StepResult(
                success=False,
                message=f"清理失败: {str(e)}",
                duration=0,
                error=e
            )

    def get_cleanup_targets(self) -> List[str]:
        """获取需要清理的目标路径列表"""
        targets = []

        # DerivedData目录
        if self.config.derived_data_path:
            targets.append(self.config.derived_data_path)

        # AST输出目录
        if self.config.output_dir:
            targets.append(self.config.output_dir)

        # 编译命令目录
        if self.config.compile_commands_dir:
            targets.append(self.config.compile_commands_dir)

        # 符号索引文件
        if self.config.index_symbols_file:
            targets.append(self.config.index_symbols_file)

        # 编译命令文件
        if self.config.compile_commands_file:
            targets.append(self.config.compile_commands_file)

        return targets