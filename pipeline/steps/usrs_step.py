"""
UsrsStep USR提取步骤实现 - TDD GREEN阶段
"""

import subprocess
import logging
from pathlib import Path

from pipeline.core import PipelineStep, StepResult
from pipeline.config import PipelineConfig

logger = logging.getLogger(__name__)


class UsrsStep(PipelineStep):
    """USR提取步骤 - 使用indexstore-db工具提取USR到文件路径的映射"""

    def __init__(self, config: PipelineConfig):
        super().__init__(
            name="usrs",
            description="USR提取步骤 - 使用indexstore-db工具提取USR到文件路径的映射"
        )
        self.config = config

    def validate_prerequisites(self) -> bool:
        """验证前置条件"""
        # 检查索引存储目录
        index_store_path = Path(self.config.derived_data_path) / "GraphIndex" / "Index.noindex" / "DataStore"
        if not index_store_path.exists():
            logger.error(f"索引存储目录不存在: {index_store_path}")
            return False

        # 检查indexstore-db工具
        indexstore_db_path = Path(self.config.indexstore_db_path)
        if not indexstore_db_path.exists():
            logger.error(f"indexstore-db工具不存在: {indexstore_db_path}")
            return False

        return True

    def execute(self) -> StepResult:
        """执行USR提取"""
        logger.info("开始提取USR到文件路径映射")

        try:
            index_store_path = Path(self.config.derived_data_path) / "GraphIndex" / "Index.noindex" / "DataStore"
            output_file = Path(self.config.index_symbols_file)

            # 确保输出目录存在
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # 构建命令
            cmd = [
                str(Path(self.config.indexstore_db_path)),
                "lookup",
                "--index-store-path", str(index_store_path),
                "--output-format", "json"
            ]

            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode != 0:
                logger.error(f"indexstore-db工具执行失败: {result.stderr}")
                return StepResult(
                    success=False,
                    message=f"工具执行失败: {result.stderr}",
                    duration=0,
                    error=RuntimeError("USR提取失败")
                )

            # 保存输出到文件
            with open(output_file, 'w') as f:
                f.write(result.stdout)

            # 统计提取的USR数量
            usrs_extracted = len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0

            return StepResult(
                success=True,
                message=f"USR提取完成，提取了 {usrs_extracted} 个USR映射",
                duration=0,
                output_files=[output_file],
                metrics={
                    "usrs_extracted": usrs_extracted,
                    "output_file": str(output_file)
                }
            )

        except Exception as e:
            logger.error(f"USR提取过程中发生错误: {e}")
            return StepResult(
                success=False,
                message=f"USR提取失败: {str(e)}",
                duration=0,
                error=e
            )