"""
Pipeline错误处理和重试机制 - TDD GREEN阶段

实现最小代码使测试通过
"""

import time
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, List, Any, Dict
from enum import Enum

logger = logging.getLogger(__name__)


class PipelineError(Exception):
    """Pipeline基础错误类"""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.original_error = original_error

    @abstractmethod
    def is_retryable(self) -> bool:
        """判断错误是否可重试"""
        pass


class RetryableError(PipelineError):
    """可重试错误"""

    def __init__(self, message: str, original_error: Optional[Exception] = None,
                 suggested_delay: float = 1.0):
        super().__init__(message, original_error)
        self.suggested_delay = suggested_delay

    def is_retryable(self) -> bool:
        return True


class NonRetryableError(PipelineError):
    """不可重试错误"""

    def __init__(self, message: str, original_error: Optional[Exception] = None,
                 suggested_action: str = "请检查配置和环境"):
        super().__init__(message, original_error)
        self.suggested_action = suggested_action

    def is_retryable(self) -> bool:
        return False


class ErrorClassifier:
    """错误分类器"""

    def __init__(self):
        self.network_errors = (TimeoutError, ConnectionError, OSError)
        self.config_errors = (FileNotFoundError, PermissionError, ValueError)
        self.resource_errors = (MemoryError,)

    def classify(self, error: Exception) -> PipelineError:
        """分类错误并返回相应的Pipeline错误"""

        # 首先检查特定的错误类型
        if isinstance(error, FileNotFoundError):
            return NonRetryableError(
                f"文件不存在: {str(error)}",
                original_error=error,
                suggested_action="检查文件路径和权限"
            )

        elif isinstance(error, PermissionError):
            return NonRetryableError(
                f"权限错误: {str(error)}",
                original_error=error,
                suggested_action="检查文件权限或使用管理员权限"
            )

        elif isinstance(error, MemoryError):
            return RetryableError(
                f"内存不足: {str(error)}",
                original_error=error,
                suggested_delay=10.0
            )

        elif isinstance(error, OSError) and "No space left" in str(error):
            return NonRetryableError(
                f"磁盘空间不足: {str(error)}",
                original_error=error,
                suggested_action="清理磁盘空间"
            )

        elif isinstance(error, (TimeoutError, ConnectionError)):
            return RetryableError(
                f"网络错误: {str(error)}",
                original_error=error,
                suggested_delay=2.0
            )

        else:
            # 默认为可重试错误
            return RetryableError(
                f"未知错误: {str(error)}",
                original_error=error,
                suggested_delay=1.0
            )


class RetryStrategy(ABC):
    """重试策略抽象基类"""

    def __init__(self, max_attempts: int = 3):
        self.max_attempts = max_attempts

    @abstractmethod
    def get_delay(self, attempt: int) -> float:
        """获取指定尝试次数的延迟时间"""
        pass

    def should_retry(self, attempt: int) -> bool:
        """判断是否应该重试"""
        return attempt <= self.max_attempts


class ExponentialBackoffStrategy(RetryStrategy):
    """指数退避重试策略"""

    def __init__(self, initial_delay: float = 1.0, max_delay: float = 60.0,
                 backoff_factor: float = 2.0, max_attempts: int = 3):
        super().__init__(max_attempts)
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor

    def get_delay(self, attempt: int) -> float:
        """计算指数退避延迟"""
        delay = self.initial_delay * (self.backoff_factor ** (attempt - 1))
        return min(delay, self.max_delay)


class FixedDelayStrategy(RetryStrategy):
    """固定延迟重试策略"""

    def __init__(self, delay: float = 5.0, max_attempts: int = 3):
        super().__init__(max_attempts)
        self.delay = delay

    def get_delay(self, attempt: int) -> float:
        """返回固定延迟"""
        return self.delay


@dataclass
class RecoveryPlan:
    """恢复计划"""
    can_recover: bool
    recovery_items: List[Any]
    skip_items: List[Any]
    strategy: str
    failure_reason: Optional[str] = None


class ErrorRecovery(ABC):
    """错误恢复抽象基类"""

    @abstractmethod
    def can_recover(self, error: Exception, context: Dict[str, Any]) -> bool:
        """判断是否可以恢复"""
        pass

    @abstractmethod
    def create_recovery_plan(self, error: Exception, context: Dict[str, Any]) -> RecoveryPlan:
        """创建恢复计划"""
        pass


class PartialFailureRecovery(ErrorRecovery):
    """部分失败恢复"""

    def can_recover(self, error: Exception, context: Dict[str, Any]) -> bool:
        """判断是否可以部分恢复"""
        task_type = context.get("task_type", "")
        failed_items = context.get("failed_items", [])
        total_items = context.get("total_items", [])

        # 关键任务不能部分恢复
        if task_type in ["workspace_validation", "config_validation"]:
            return False

        # 如果失败项目少于总数的50%，可以恢复
        if len(failed_items) < len(total_items) * 0.5:
            return True

        return False

    def create_recovery_plan(self, task_type: str, total_items: List[Any],
                           failed_items: List[Any]) -> RecoveryPlan:
        """创建恢复计划"""

        # 关键任务失败
        if task_type in ["workspace_validation", "config_validation"]:
            return RecoveryPlan(
                can_recover=False,
                recovery_items=[],
                skip_items=[],
                strategy="",
                failure_reason="关键组件失败，无法恢复"
            )

        # 计算需要跳过的项目
        skip_items = [item for item in total_items if item not in failed_items]

        return RecoveryPlan(
            can_recover=True,
            recovery_items=failed_items,
            skip_items=skip_items,
            strategy="重新处理失败的文件，跳过已成功的文件"
        )


class ErrorReporter:
    """错误报告器"""

    def generate_report(self, error_info: Dict[str, Any]) -> str:
        """生成详细的错误报告"""

        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("错误摘要")
        report_lines.append("=" * 60)

        # 基本信息
        report_lines.append(f"步骤名称: {error_info.get('step_name', 'Unknown')}")
        report_lines.append(f"错误类型: {error_info.get('error_type', 'Unknown')}")
        report_lines.append(f"错误信息: {error_info.get('error_message', 'No message')}")

        # 重试信息
        if 'attempt_count' in error_info:
            report_lines.append(f"尝试次数: {error_info['attempt_count']}")

        if 'total_duration' in error_info:
            report_lines.append(f"总耗时: {error_info['total_duration']:.2f}秒")

        # 失败文件
        if 'failed_files' in error_info:
            report_lines.append("\n失败文件:")
            for file_path in error_info['failed_files']:
                report_lines.append(f"  - {file_path}")

        # 环境信息
        if 'environment' in error_info:
            report_lines.append("\n环境信息:")
            for key, value in error_info['environment'].items():
                report_lines.append(f"  {key}: {value}")

        # 建议操作
        if error_info.get('is_retryable', True):
            report_lines.append("\n重试建议:")
            report_lines.append("  - 检查网络连接")
            report_lines.append("  - 等待一段时间后重试")
        else:
            report_lines.append("\n不可重试错误")
            if 'suggested_actions' in error_info:
                report_lines.append("建议操作:")
                for action in error_info['suggested_actions']:
                    report_lines.append(f"  - {action}")

        return "\n".join(report_lines)