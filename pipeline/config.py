"""
Pipeline配置管理模块
"""

import os
import json
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import List, Dict, Any, Optional


@dataclass
class PipelineConfig:
    """流水线配置"""
    # 基础路径配置
    workspace_path: str = "hammmer-workspace/TencentMap.xcworkspace"
    derived_data_path: str = "DerivedData/GraphIndex"
    output_dir: str = "ast_out_index"
    compile_commands_dir: str = "compile_commands"

    # 目标scheme配置
    schemes: List[str] = None
    scheme_filter_prefix: str = "QMapBusiness|QMapMiddlePlatform"

    # 执行配置
    parallel_jobs: int = 4
    retry_attempts: int = 3
    timeout_seconds: int = 3600  # 1小时超时

    # 工具路径配置
    clang_library_path: str = "/opt/homebrew/opt/llvm/lib/libclang.dylib"
    indexstore_db_path: str = "tools/indexstore-db"

    # 输出文件配置
    index_symbols_file: str = "index_symbols.jsonl"
    compile_commands_file: str = "compile_commands.json"
    nodes_file: str = "nodes.jsonl"
    edges_file: str = "edges.jsonl"

    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None

    # 验证配置
    min_nodes_count: int = 1000
    min_edges_count: int = 1000
    min_cross_repo_calls: int = 1

    def __post_init__(self):
        if self.schemes is None:
            self.schemes = ["QMapBusiness", "QMapMiddlePlatform"]

    @classmethod
    def from_file(cls, config_path: str) -> 'PipelineConfig':
        """从配置文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        return cls(**config_data)

    def to_file(self, config_path: str):
        """保存配置到文件"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(self), f, indent=2, ensure_ascii=False)

    @classmethod
    def from_env(cls) -> 'PipelineConfig':
        """从环境变量加载配置"""
        config = cls()

        # 从环境变量覆盖配置
        env_mappings = {
            'PIPELINE_WORKSPACE_PATH': 'workspace_path',
            'PIPELINE_SCHEMES': 'schemes',
            'PIPELINE_PARALLEL_JOBS': 'parallel_jobs',
            'PIPELINE_RETRY_ATTEMPTS': 'retry_attempts',
            'CLANG_LIBRARY_FILE': 'clang_library_path',
            'PIPELINE_LOG_LEVEL': 'log_level',
        }

        for env_var, attr_name in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                if attr_name == 'schemes':
                    setattr(config, attr_name, env_value.split(','))
                elif attr_name in ['parallel_jobs', 'retry_attempts']:
                    setattr(config, attr_name, int(env_value))
                else:
                    setattr(config, attr_name, env_value)

        return config

    def validate(self) -> List[str]:
        """验证配置有效性，返回错误列表"""
        errors = []

        # 检查必需的路径
        workspace_path = Path(self.workspace_path)
        if not workspace_path.exists():
            errors.append(f"工作区路径不存在: {self.workspace_path}")

        indexstore_path = Path(self.indexstore_db_path)
        if not indexstore_path.exists():
            errors.append(f"indexstore-db工具路径不存在: {self.indexstore_db_path}")

        # 检查clang库文件
        clang_lib = Path(self.clang_library_path)
        if not clang_lib.exists():
            errors.append(f"Clang库文件不存在: {self.clang_library_path}")

        # 检查数值配置
        if self.parallel_jobs <= 0:
            errors.append("parallel_jobs必须大于0")

        if self.retry_attempts < 0:
            errors.append("retry_attempts不能小于0")

        if self.timeout_seconds <= 0:
            errors.append("timeout_seconds必须大于0")

        # 检查schemes配置
        if not self.schemes:
            errors.append("schemes不能为空")

        return errors

    def get_absolute_paths(self, base_dir: str = ".") -> Dict[str, str]:
        """获取所有路径的绝对路径"""
        base_path = Path(base_dir).resolve()

        return {
            'workspace_path': str(base_path / self.workspace_path),
            'derived_data_path': str(base_path / self.derived_data_path),
            'output_dir': str(base_path / self.output_dir),
            'compile_commands_dir': str(base_path / self.compile_commands_dir),
            'indexstore_db_path': str(base_path / self.indexstore_db_path),
            'index_symbols_file': str(base_path / self.index_symbols_file),
            'compile_commands_file': str(base_path / self.compile_commands_file),
            'nodes_file': str(base_path / self.output_dir / self.nodes_file),
            'edges_file': str(base_path / self.output_dir / self.edges_file),
        }


def load_default_config() -> PipelineConfig:
    """加载默认配置"""
    # 优先级：配置文件 > 环境变量 > 默认值
    config_file = "pipeline_config.json"

    if Path(config_file).exists():
        return PipelineConfig.from_file(config_file)
    else:
        return PipelineConfig.from_env()