"""
Pipeline核心模块 - 定义流水线步骤和管理器的抽象接口
"""

import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class StepStatus(Enum):
    """步骤状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class PipelineStatus(Enum):
    """流水线状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class StepResult:
    """步骤执行结果"""
    success: bool
    message: str
    duration: float
    output_files: List[Path] = None
    metrics: Dict[str, Any] = None
    error: Optional[Exception] = None

    def __post_init__(self):
        if self.output_files is None:
            self.output_files = []
        if self.metrics is None:
            self.metrics = {}


class PipelineStep(ABC):
    """流水线步骤抽象基类"""

    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.status = StepStatus.NOT_STARTED
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.result: Optional[StepResult] = None
        self.dependencies: List[str] = []
        self.retry_count = 0
        self.max_retries = 3

    @abstractmethod
    def validate_prerequisites(self) -> bool:
        """验证步骤执行的前置条件"""
        pass

    @abstractmethod
    def execute(self) -> StepResult:
        """执行步骤的核心逻辑"""
        pass

    def cleanup_on_failure(self) -> None:
        """失败时的清理操作（可选实现）"""
        pass

    def get_progress(self) -> float:
        """获取步骤执行进度 (0.0-1.0)"""
        if self.status == StepStatus.NOT_STARTED:
            return 0.0
        elif self.status == StepStatus.RUNNING:
            return 0.5  # 子类可以重写提供更精确的进度
        elif self.status in [StepStatus.COMPLETED, StepStatus.SKIPPED]:
            return 1.0
        else:  # FAILED
            return 0.0

    def run(self) -> StepResult:
        """运行步骤，包含错误处理和重试逻辑"""
        logger.info(f"开始执行步骤: {self.name}")
        self.status = StepStatus.RUNNING
        self.start_time = time.time()

        try:
            # 验证前置条件
            if not self.validate_prerequisites():
                raise RuntimeError(f"步骤 {self.name} 前置条件验证失败")

            # 执行步骤
            result = self.execute()

            if result.success:
                self.status = StepStatus.COMPLETED
                logger.info(f"步骤 {self.name} 执行成功: {result.message}")
            else:
                self.status = StepStatus.FAILED
                logger.error(f"步骤 {self.name} 执行失败: {result.message}")

        except Exception as e:
            logger.exception(f"步骤 {self.name} 执行异常")
            result = StepResult(
                success=False,
                message=f"执行异常: {str(e)}",
                duration=0,
                error=e
            )
            self.status = StepStatus.FAILED

        finally:
            self.end_time = time.time()
            if self.start_time:
                result.duration = self.end_time - self.start_time
            self.result = result

        return result

    def should_retry(self, error: Exception) -> bool:
        """判断是否应该重试"""
        return self.retry_count < self.max_retries

    def add_dependency(self, step_name: str):
        """添加依赖步骤"""
        if step_name not in self.dependencies:
            self.dependencies.append(step_name)

    @property
    def duration(self) -> Optional[float]:
        """获取步骤执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class Pipeline:
    """流水线管理器"""

    def __init__(self, name: str = "QMap Pipeline"):
        self.name = name
        self.steps: Dict[str, PipelineStep] = {}
        self.step_order: List[str] = []
        self.status = PipelineStatus.NOT_STARTED
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.current_step: Optional[str] = None

    def add_step(self, step: PipelineStep) -> 'Pipeline':
        """添加步骤到流水线"""
        if step.name in self.steps:
            raise ValueError(f"步骤 {step.name} 已存在")

        self.steps[step.name] = step
        self.step_order.append(step.name)
        logger.info(f"添加步骤: {step.name}")
        return self

    def validate_dependencies(self) -> bool:
        """验证步骤依赖关系"""
        for step_name, step in self.steps.items():
            for dep in step.dependencies:
                if dep not in self.steps:
                    logger.error(f"步骤 {step_name} 依赖的步骤 {dep} 不存在")
                    return False
        return True

    def get_execution_order(self) -> List[str]:
        """根据依赖关系计算执行顺序（拓扑排序）"""
        # 简化版本：按添加顺序执行，后续可实现完整的拓扑排序
        return self.step_order.copy()

    def run(self, start_from: Optional[str] = None, stop_at: Optional[str] = None) -> bool:
        """运行流水线"""
        logger.info(f"开始运行流水线: {self.name}")

        if not self.validate_dependencies():
            logger.error("依赖关系验证失败")
            return False

        self.status = PipelineStatus.RUNNING
        self.start_time = time.time()

        execution_order = self.get_execution_order()

        # 确定开始位置
        start_index = 0
        if start_from:
            try:
                start_index = execution_order.index(start_from)
            except ValueError:
                logger.error(f"起始步骤 {start_from} 不存在")
                return False

        # 确定结束位置
        end_index = len(execution_order)
        if stop_at:
            try:
                end_index = execution_order.index(stop_at) + 1
            except ValueError:
                logger.error(f"结束步骤 {stop_at} 不存在")
                return False

        try:
            for i in range(start_index, end_index):
                step_name = execution_order[i]
                step = self.steps[step_name]
                self.current_step = step_name

                logger.info(f"执行步骤 {i+1}/{len(execution_order)}: {step_name}")

                # 检查依赖步骤是否完成
                for dep in step.dependencies:
                    dep_step = self.steps[dep]
                    if dep_step.status != StepStatus.COMPLETED:
                        logger.error(f"依赖步骤 {dep} 未完成，跳过 {step_name}")
                        step.status = StepStatus.SKIPPED
                        continue

                # 执行步骤
                result = step.run()

                if not result.success:
                    logger.error(f"步骤 {step_name} 执行失败，停止流水线")
                    self.status = PipelineStatus.FAILED
                    return False

            self.status = PipelineStatus.COMPLETED
            logger.info("流水线执行完成")
            return True

        except KeyboardInterrupt:
            logger.warning("流水线被用户中断")
            self.status = PipelineStatus.CANCELLED
            return False
        except Exception as e:
            logger.exception("流水线执行异常")
            self.status = PipelineStatus.FAILED
            return False
        finally:
            self.end_time = time.time()
            self.current_step = None

    def get_status(self) -> Dict[str, Any]:
        """获取流水线状态"""
        total_steps = len(self.steps)
        completed_steps = sum(1 for step in self.steps.values()
                            if step.status == StepStatus.COMPLETED)

        return {
            "name": self.name,
            "status": self.status.value,
            "current_step": self.current_step,
            "progress": completed_steps / total_steps if total_steps > 0 else 0,
            "completed_steps": completed_steps,
            "total_steps": total_steps,
            "duration": self.duration,
            "steps": {
                name: {
                    "status": step.status.value,
                    "duration": step.duration,
                    "progress": step.get_progress()
                }
                for name, step in self.steps.items()
            }
        }

    @property
    def duration(self) -> Optional[float]:
        """获取流水线执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None