"""
Pipeline-All 配置系统
支持多仓库的动态配置和管理
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

class RepositoryType(Enum):
    """仓库类型枚举"""
    CORE_MODULE = "core_module"
    UI_MODULE = "ui_module"
    FOUNDATION = "foundation"
    BUSINESS = "business"
    THIRD_PARTY = "third_party"
    TOOLS = "tools"

class ProcessingStrategy(Enum):
    """处理策略枚举"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    HYBRID = "hybrid"

@dataclass
class RepositoryConfig:
    """单个仓库配置"""
    name: str
    path: str
    type: RepositoryType
    source_files: int
    header_files: int
    priority: int = 1  # 1=高优先级, 2=中优先级, 3=低优先级
    enabled: bool = True
    schemes: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RepositoryConfig':
        """从字典创建仓库配置"""
        repo_type = RepositoryType(data.get('type', 'core_module'))
        return cls(
            name=data['name'],
            path=data['path'],
            type=repo_type,
            source_files=data['source_files'],
            header_files=data['header_files'],
            priority=data.get('priority', 1),
            enabled=data.get('enabled', True),
            schemes=data.get('schemes', []),
            dependencies=data.get('dependencies', [])
        )

@dataclass
class PipelineAllConfig:
    """Pipeline-All 主配置"""
    
    # 基础配置
    workspace_dir: str = "hammmer-workspace"
    output_dir: str = "ast_out_index_all"
    
    # 仓库配置
    repositories: List[RepositoryConfig] = field(default_factory=list)
    repository_config_file: str = "pipeline_all_repositories.json"
    
    # 处理策略
    processing_strategy: ProcessingStrategy = ProcessingStrategy.PARALLEL
    max_workers: int = 8
    chunk_size: int = 100  # 每个处理块的文件数
    
    # 跨仓库分析配置
    cross_repo_analysis: bool = True
    max_cross_repo_depth: int = 3  # 最大跨仓库依赖深度
    
    # 性能配置
    memory_limit_gb: int = 4
    timeout_seconds: int = 3600  # 1小时超时
    enable_caching: bool = True
    cache_dir: str = "cache_all"
    
    # 输出配置
    nodes_file: str = "nodes_all.jsonl"
    edges_file: str = "edges_all.jsonl"
    stats_file: str = "stats_all.json"
    
    # 验证配置
    min_nodes_count: int = 35000
    min_edges_count: int = 30000
    min_cross_repo_calls: int = 5000
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "pipeline_all.log"
    
    def __post_init__(self):
        """初始化后处理"""
        self.load_repositories()
    
    def load_repositories(self):
        """加载仓库配置"""
        config_file = Path(self.repository_config_file)
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.repositories = []
            for repo_data in data.get('repositories', []):
                # 自动推断仓库类型
                repo_type = self._infer_repository_type(repo_data['name'])
                repo_data['type'] = repo_type.value
                
                # 设置优先级
                repo_data['priority'] = self._calculate_priority(repo_data)
                
                repo_config = RepositoryConfig.from_dict(repo_data)
                self.repositories.append(repo_config)
            
            # 按优先级排序
            self.repositories.sort(key=lambda x: (x.priority, -x.source_files))
    
    def _infer_repository_type(self, repo_name: str) -> RepositoryType:
        """推断仓库类型"""
        name_lower = repo_name.lower()
        
        if "foundation" in name_lower or "baseline" in name_lower:
            return RepositoryType.FOUNDATION
        elif "ui" in name_lower or "kit" in name_lower:
            return RepositoryType.UI_MODULE
        elif "business" in name_lower:
            return RepositoryType.BUSINESS
        elif "dragon" in name_lower or "tencentmap" in name_lower:
            return RepositoryType.CORE_MODULE
        elif "dk" in name_lower or "grag" in name_lower:
            return RepositoryType.TOOLS
        else:
            return RepositoryType.CORE_MODULE
    
    def _calculate_priority(self, repo_data: Dict[str, Any]) -> int:
        """计算仓库优先级"""
        source_files = repo_data.get('source_files', 0)
        repo_name = repo_data.get('name', '')
        
        # 基础模块和核心模块优先级最高
        if any(keyword in repo_name for keyword in ['Foundation', 'Baseline', 'MiddlePlatform']):
            return 1
        
        # 大型仓库优先级较高
        if source_files > 1000:
            return 1
        elif source_files > 100:
            return 2
        else:
            return 3
    
    def get_enabled_repositories(self) -> List[RepositoryConfig]:
        """获取启用的仓库"""
        return [repo for repo in self.repositories if repo.enabled]
    
    def get_repositories_by_type(self, repo_type: RepositoryType) -> List[RepositoryConfig]:
        """按类型获取仓库"""
        return [repo for repo in self.repositories if repo.type == repo_type]
    
    def get_repository_by_name(self, name: str) -> Optional[RepositoryConfig]:
        """按名称获取仓库"""
        for repo in self.repositories:
            if repo.name == name:
                return repo
        return None
    
    def add_repository(self, repo_config: RepositoryConfig):
        """添加仓库"""
        # 检查是否已存在
        existing = self.get_repository_by_name(repo_config.name)
        if existing:
            # 更新现有仓库
            idx = self.repositories.index(existing)
            self.repositories[idx] = repo_config
        else:
            # 添加新仓库
            self.repositories.append(repo_config)
        
        # 重新排序
        self.repositories.sort(key=lambda x: (x.priority, -x.source_files))
    
    def remove_repository(self, name: str) -> bool:
        """移除仓库"""
        repo = self.get_repository_by_name(name)
        if repo:
            self.repositories.remove(repo)
            return True
        return False
    
    def save_repositories(self):
        """保存仓库配置"""
        data = {
            "repositories": [
                {
                    "name": repo.name,
                    "path": repo.path,
                    "type": repo.type.value,
                    "source_files": repo.source_files,
                    "header_files": repo.header_files,
                    "priority": repo.priority,
                    "enabled": repo.enabled,
                    "schemes": repo.schemes,
                    "dependencies": repo.dependencies
                }
                for repo in self.repositories
            ],
            "total_repositories": len(self.repositories),
            "total_source_files": sum(repo.source_files for repo in self.repositories),
            "last_updated": "2025-07-09"
        }
        
        with open(self.repository_config_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def get_processing_groups(self) -> List[List[RepositoryConfig]]:
        """获取处理分组（用于并行处理）"""
        enabled_repos = self.get_enabled_repositories()
        
        if self.processing_strategy == ProcessingStrategy.SEQUENTIAL:
            # 顺序处理：每个仓库一组
            return [[repo] for repo in enabled_repos]
        
        elif self.processing_strategy == ProcessingStrategy.PARALLEL:
            # 并行处理：按优先级分组
            groups = []
            current_group = []
            current_priority = None
            
            for repo in enabled_repos:
                if current_priority is None or repo.priority == current_priority:
                    current_group.append(repo)
                    current_priority = repo.priority
                else:
                    if current_group:
                        groups.append(current_group)
                    current_group = [repo]
                    current_priority = repo.priority
            
            if current_group:
                groups.append(current_group)
            
            return groups
        
        else:  # HYBRID
            # 混合处理：大型仓库单独处理，小型仓库分组处理
            large_repos = [repo for repo in enabled_repos if repo.source_files > 500]
            small_repos = [repo for repo in enabled_repos if repo.source_files <= 500]
            
            groups = [[repo] for repo in large_repos]  # 大型仓库单独处理
            
            # 小型仓库按worker数量分组
            if small_repos:
                chunk_size = max(1, len(small_repos) // self.max_workers)
                for i in range(0, len(small_repos), chunk_size):
                    groups.append(small_repos[i:i + chunk_size])
            
            return groups
    
    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        enabled_repos = self.get_enabled_repositories()
        
        return {
            "total_repositories": len(self.repositories),
            "enabled_repositories": len(enabled_repos),
            "total_source_files": sum(repo.source_files for repo in enabled_repos),
            "total_header_files": sum(repo.header_files for repo in enabled_repos),
            "processing_strategy": self.processing_strategy.value,
            "max_workers": self.max_workers,
            "cross_repo_analysis": self.cross_repo_analysis,
            "repository_types": {
                repo_type.value: len(self.get_repositories_by_type(repo_type))
                for repo_type in RepositoryType
            }
        }
