{"timestamp": "2025-07-11T12:00:59.436820", "overall_status": "PASSED", "validation_results": {"node_requirements": {"total_nodes_minimum": [true, "总节点数: 23,707 (要求: ≥10,000)"], "method_nodes": [true, "Method节点: 20,556 (要求: ≥5,000)"], "class_nodes": [true, "Class节点: 742 (要求: ≥100)"], "file_nodes": [true, "File节点: 516 (要求: ≥100)"], "property_nodes": [true, "Property节点: 1,888 (要求: ≥500)"]}, "relationship_requirements": {"total_relationships_minimum": [true, "总关系数: 29,647 (要求: ≥10,000)"], "calls_relationships": [true, "CALLS关系: 14,542 (要求: ≥1,000)"], "defines_relationships": [true, "DEFINES关系: 13,587 (要求: ≥1,000)"], "accesses_relationships": [true, "ACCESSES关系: 1,459 (要求: ≥100)"], "inherits_relationships": [true, "INHERITS关系: 56 (要求: ≥10)"], "implements_relationships": [true, "IMPLEMENTS关系: 3 (要求: ≥1)"]}, "relationship_types": {"required": ["CALLS", "DEFINES", "ACCESSES", "INHERITS", "IMPLEMENTS"], "existing": ["DEFINES", "INHERITS", "CALLS", "ACCESSES", "IMPLEMENTS"], "missing": []}, "node_classification": {"classification_rate": [true, "节点分类率: 100.0% (要求: ≥90%)"], "unclassified_nodes": [true, "未分类节点: 0 (要求: ≤2,370)"]}, "data_quality": {"name_coverage": [true, "节点名称覆盖率: 100.0% (要求: ≥80%)"], "orphaned_nodes": [true, "孤立节点率: 0.0% (要求: ≤10%)"]}, "architectural_analysis": {"方法调用链查询": 14542, "类继承层次查询": 56, "跨文件依赖查询": 0, "属性访问模式查询": 1459}}}