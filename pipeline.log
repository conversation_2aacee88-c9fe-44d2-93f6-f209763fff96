2025-07-08 20:14:46,307 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 20:14:46,308 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:14:46,308 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:14:46,308 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 20:14:46,308 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 20:14:46,308 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 20:14:51,172 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 20:14:51,172 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-08 20:14:51,172 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-08 20:14:51,172 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 54006 个项目，释放 7922888739 字节
2025-07-08 20:14:51,172 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 20:14:51,172 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 20:14:51,173 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 20:14:51,173 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 20:17:22,902 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 20:17:22,902 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 20:17:46,194 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 20:17:46,194 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 20:17:46,194 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 20:17:46,194 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 20:17:46,195 - pipeline.steps.usrs_step - ERROR - 索引存储目录不存在: DerivedData/GraphIndex/GraphIndex/Index.noindex/DataStore
2025-07-08 20:17:46,195 - pipeline.core - ERROR - 步骤 usrs 执行异常
Traceback (most recent call last):
  File "/Users/<USER>/GRAG_iOS/pipeline/core.py", line 103, in run
    raise RuntimeError(f"步骤 {self.name} 前置条件验证失败")
RuntimeError: 步骤 usrs 前置条件验证失败
2025-07-08 20:17:46,196 - pipeline.core - ERROR - 步骤 usrs 执行失败，停止流水线
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 20:27:11,357 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:27:11,357 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:27:11,357 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 20:27:11,357 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 20:27:11,357 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 20:27:16,445 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 20:27:16,446 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 60983 个项目，释放 8954710147 字节
2025-07-08 20:27:16,446 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 20:27:16,446 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 20:27:16,446 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 20:27:16,446 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 20:29:44,547 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 20:29:44,548 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 20:30:06,873 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 20:30:06,874 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 20:30:06,874 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 20:30:06,874 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 20:30:06,874 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 20:30:06,874 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-08 20:30:06,874 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 20:30:06,877 - pipeline.steps.usrs_step - ERROR - USR提取过程中发生错误: [Errno 13] Permission denied: 'tools/indexstore-db'
2025-07-08 20:30:06,877 - pipeline.core - ERROR - 步骤 usrs 执行失败: USR提取失败: [Errno 13] Permission denied: 'tools/indexstore-db'
2025-07-08 20:30:06,877 - pipeline.core - ERROR - 步骤 usrs 执行失败，停止流水线
