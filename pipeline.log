2025-07-08 20:14:46,307 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 20:14:46,308 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 20:14:46,308 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:14:46,308 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:14:46,308 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 20:14:46,308 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 20:14:46,308 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 20:14:51,172 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 20:14:51,172 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-08 20:14:51,172 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-08 20:14:51,172 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 54006 个项目，释放 7922888739 字节
2025-07-08 20:14:51,172 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 20:14:51,172 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 20:14:51,173 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 20:14:51,173 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 20:17:22,902 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 20:17:22,902 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 20:17:46,194 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 20:17:46,194 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 20:17:46,194 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 20:17:46,194 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 20:17:46,195 - pipeline.steps.usrs_step - ERROR - 索引存储目录不存在: DerivedData/GraphIndex/GraphIndex/Index.noindex/DataStore
2025-07-08 20:17:46,195 - pipeline.core - ERROR - 步骤 usrs 执行异常
Traceback (most recent call last):
  File "/Users/<USER>/GRAG_iOS/pipeline/core.py", line 103, in run
    raise RuntimeError(f"步骤 {self.name} 前置条件验证失败")
RuntimeError: 步骤 usrs 前置条件验证失败
2025-07-08 20:17:46,196 - pipeline.core - ERROR - 步骤 usrs 执行失败，停止流水线
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 20:27:11,357 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 20:27:11,357 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:27:11,357 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:27:11,357 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 20:27:11,357 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 20:27:11,357 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 20:27:16,445 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 20:27:16,446 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 60983 个项目，释放 8954710147 字节
2025-07-08 20:27:16,446 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 20:27:16,446 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 20:27:16,446 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 20:27:16,446 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 20:29:44,547 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 20:29:44,548 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 20:30:06,873 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 20:30:06,874 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 20:30:06,874 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 20:30:06,874 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 20:30:06,874 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 20:30:06,874 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-08 20:30:06,874 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 20:30:06,877 - pipeline.steps.usrs_step - ERROR - USR提取过程中发生错误: [Errno 13] Permission denied: 'tools/indexstore-db'
2025-07-08 20:30:06,877 - pipeline.core - ERROR - 步骤 usrs 执行失败: USR提取失败: [Errno 13] Permission denied: 'tools/indexstore-db'
2025-07-08 20:30:06,877 - pipeline.core - ERROR - 步骤 usrs 执行失败，停止流水线
2025-07-08 20:43:22,554 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 20:43:22,554 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 20:43:22,554 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 20:43:22,554 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 20:43:22,554 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 20:43:22,554 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 20:43:22,554 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:43:22,554 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 20:43:22,554 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 20:43:22,554 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 20:43:22,554 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 20:43:27,327 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 20:43:27,327 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 60980 个项目，释放 8954647154 字节
2025-07-08 20:43:27,327 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 20:43:27,327 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 20:43:27,327 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 20:43:27,328 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 20:45:55,306 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 20:45:55,307 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 20:46:17,989 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 20:46:17,991 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 20:46:17,992 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 20:46:17,992 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 20:46:17,992 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 20:46:17,992 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-08 20:46:17,992 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 20:46:17,992 - pipeline.steps.usrs_step - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store DerivedData/GraphIndex/Index.noindex/DataStore --out index_symbols.jsonl
2025-07-08 20:46:33,571 - pipeline.core - INFO - 步骤 usrs 执行成功: USR提取完成，提取了 375540 个USR映射
2025-07-08 20:46:33,571 - pipeline.core - INFO - 执行步骤 4/6: cdb
2025-07-08 20:46:33,571 - pipeline.core - INFO - 开始执行步骤: cdb
2025-07-08 20:46:33,572 - pipeline.steps.cdb_step - INFO - 开始生成编译数据库
2025-07-08 20:46:33,572 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapBusiness' 生成编译数据库...
2025-07-08 20:48:31,515 - pipeline.steps.cdb_step - INFO - Scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-08 20:48:31,515 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-08 20:50:11,760 - pipeline.steps.cdb_step - INFO - Scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-08 20:50:11,761 - pipeline.steps.cdb_step - INFO - 合并编译命令完成，共 0 个唯一文件
2025-07-08 20:50:11,762 - pipeline.core - INFO - 步骤 cdb 执行成功: 编译数据库生成完成，处理了 2 个scheme
2025-07-08 20:50:11,762 - pipeline.core - INFO - 执行步骤 5/6: ast
2025-07-08 20:50:11,762 - pipeline.core - INFO - 开始执行步骤: ast
2025-07-08 20:50:11,762 - pipeline.steps.ast_step - INFO - 开始AST抽取
2025-07-08 20:50:11,767 - pipeline.steps.ast_step - ERROR - AST抽取过程中发生错误: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 20:50:11,767 - pipeline.core - ERROR - 步骤 ast 执行失败: AST抽取失败: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 20:50:11,767 - pipeline.core - ERROR - 步骤 ast 执行失败，停止流水线
2025-07-08 21:04:28,643 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 21:04:28,643 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 21:04:28,643 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 21:04:28,643 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 21:04:28,643 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 21:04:28,643 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 21:04:28,643 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 21:04:28,643 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 21:04:28,643 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 21:04:28,643 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 21:04:28,643 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 21:04:33,164 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 21:04:33,165 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-08 21:04:33,165 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-08 21:04:33,165 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 54929 个项目，释放 7911204494 字节
2025-07-08 21:04:33,165 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 21:04:33,165 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 21:04:33,166 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 21:04:33,166 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 21:07:01,371 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 21:07:01,371 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 21:07:23,839 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 21:07:23,840 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 21:07:23,841 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 21:07:23,841 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 21:07:23,841 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 21:07:23,841 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-08 21:07:23,849 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 21:07:23,849 - pipeline.steps.usrs_step - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store DerivedData/GraphIndex/Index.noindex/DataStore --out index_symbols.jsonl
2025-07-08 21:07:39,248 - pipeline.core - INFO - 步骤 usrs 执行成功: USR提取完成，提取了 375543 个USR映射
2025-07-08 21:07:39,248 - pipeline.core - INFO - 执行步骤 4/6: cdb
2025-07-08 21:07:39,248 - pipeline.core - INFO - 开始执行步骤: cdb
2025-07-08 21:07:39,248 - pipeline.steps.cdb_step - INFO - 开始生成编译数据库
2025-07-08 21:07:39,248 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapBusiness' 生成编译数据库...
2025-07-08 21:09:42,153 - pipeline.steps.cdb_step - INFO - Scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-08 21:09:42,153 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-08 21:11:23,534 - pipeline.steps.cdb_step - INFO - Scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-08 21:11:23,535 - pipeline.steps.cdb_step - INFO - 合并编译命令完成，共 0 个唯一文件
2025-07-08 21:11:23,536 - pipeline.core - INFO - 步骤 cdb 执行成功: 编译数据库生成完成，处理了 2 个scheme
2025-07-08 21:11:23,536 - pipeline.core - INFO - 执行步骤 5/6: ast
2025-07-08 21:11:23,536 - pipeline.core - INFO - 开始执行步骤: ast
2025-07-08 21:11:23,536 - pipeline.steps.ast_step - INFO - 开始AST抽取
2025-07-08 21:11:23,536 - pipeline.steps.ast_step - INFO - 加载了 0 个编译命令
2025-07-08 21:11:23,536 - pipeline.steps.ast_step - WARNING - 编译命令为空，这可能是CdbStep的问题，将生成基本的文件节点
2025-07-08 21:11:23,542 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5128: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 21:11:23,542 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5129: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 21:11:23,542 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5130: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 21:11:23,542 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5131: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 21:11:23,542 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5132: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 21:11:23,933 - pipeline.steps.ast_step - WARNING - 跳过了 47/375543 个无效的符号行
2025-07-08 21:11:23,933 - pipeline.steps.ast_step - INFO - 成功加载 375496 个符号映射
2025-07-08 21:11:23,933 - pipeline.steps.ast_step - INFO - 编译命令为空，基于符号映射生成基本节点
2025-07-08 21:11:24,081 - pipeline.steps.ast_step - INFO - 基于符号映射处理了 8260 个文件
2025-07-08 21:11:24,106 - pipeline.core - INFO - 步骤 ast 执行成功: AST抽取完成，生成了 8260 个节点和 0 条边
2025-07-08 21:11:24,106 - pipeline.core - INFO - 执行步骤 6/6: stats
2025-07-08 21:11:24,106 - pipeline.core - INFO - 开始执行步骤: stats
2025-07-08 21:11:24,106 - pipeline.steps.stats_step - INFO - 开始统计验证
2025-07-08 21:11:24,108 - pipeline.steps.stats_step - INFO - 总节点数: 8260
2025-07-08 21:11:24,108 - pipeline.steps.stats_step - INFO - 总边数: 0
2025-07-08 21:11:24,108 - pipeline.steps.stats_step - INFO - 跨仓库调用数: 0
2025-07-08 21:11:24,108 - pipeline.core - ERROR - 步骤 stats 执行失败: 验证失败: 边数量 0 低于最小要求 1000; 跨仓库调用数 0 低于最小要求 1
2025-07-08 21:11:24,108 - pipeline.core - ERROR - 步骤 stats 执行失败，停止流水线
2025-07-08 23:42:24,439 - pipeline.core - INFO - 添加步骤: clean
2025-07-08 23:42:24,439 - pipeline.core - INFO - 添加步骤: index-store
2025-07-08 23:42:24,439 - pipeline.core - INFO - 添加步骤: usrs
2025-07-08 23:42:24,439 - pipeline.core - INFO - 添加步骤: cdb
2025-07-08 23:42:24,439 - pipeline.core - INFO - 添加步骤: ast
2025-07-08 23:42:24,439 - pipeline.core - INFO - 添加步骤: stats
2025-07-08 23:42:24,439 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 23:42:24,439 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-08 23:42:24,439 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-08 23:42:24,439 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-08 23:42:24,439 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-08 23:42:29,070 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-08 23:42:29,071 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-08 23:42:29,071 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-08 23:42:29,071 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 54932 个项目，释放 7913220478 字节
2025-07-08 23:42:29,071 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-08 23:42:29,071 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-08 23:42:29,072 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-08 23:42:29,072 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-08 23:44:58,861 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-08 23:44:58,861 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-08 23:45:21,898 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-08 23:45:21,898 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-08 23:45:21,898 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-08 23:45:21,898 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-08 23:45:21,898 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 23:45:21,898 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-08 23:45:21,898 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-08 23:45:21,898 - pipeline.steps.usrs_step - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store DerivedData/GraphIndex/Index.noindex/DataStore --out index_symbols.jsonl
2025-07-08 23:45:37,332 - pipeline.core - INFO - 步骤 usrs 执行成功: USR提取完成，提取了 375547 个USR映射
2025-07-08 23:45:37,394 - pipeline.core - INFO - 执行步骤 4/6: cdb
2025-07-08 23:45:37,394 - pipeline.core - INFO - 开始执行步骤: cdb
2025-07-08 23:45:37,394 - pipeline.steps.cdb_step - INFO - 开始生成编译数据库
2025-07-08 23:45:37,394 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapBusiness' 生成编译数据库...
2025-07-08 23:45:37,394 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapBusiness -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-08 23:45:47,746 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 1189414 字符
2025-07-08 23:45:47,750 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 28 个CompileC行, 0 个clang命令
2025-07-08 23:45:47,751 - pipeline.steps.cdb_step - WARNING - 找到CompileC行但没有找到对应的clang命令，可能需要调整解析逻辑
2025-07-08 23:45:47,751 - pipeline.steps.cdb_step - WARNING - CompileC示例 1: CompileC /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMapVectorLayout-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/QMapVectorLayout/QMapVectorLayout-dummy.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler (in target 'QMapVectorLayout' from project 'QMapVectorLayout')
2025-07-08 23:45:47,751 - pipeline.steps.cdb_step - WARNING - CompileC示例 2: CompileC /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicDSLPrebuildOperation.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicDSLPrebuildOperation.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler (in target 'QMapVectorLayout' from project 'QMapVectorLayout')
2025-07-08 23:45:47,751 - pipeline.steps.cdb_step - WARNING - CompileC示例 3: CompileC /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLService.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLService.m normal x86_64 objective-c com.apple.compilers.llvm.clang.1_0.compiler (in target 'QMapVectorLayout' from project 'QMapVectorLayout')
2025-07-08 23:45:47,752 - pipeline.steps.cdb_step - INFO - Scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-08 23:45:47,752 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-08 23:45:47,752 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapMiddlePlatform -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-08 23:45:54,306 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 98915 字符
2025-07-08 23:45:54,306 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 0 个CompileC行, 0 个clang命令
2025-07-08 23:45:54,307 - pipeline.steps.cdb_step - INFO - Scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-08 23:45:54,307 - pipeline.steps.cdb_step - INFO - 合并编译命令完成，共 0 个唯一文件
2025-07-08 23:45:54,307 - pipeline.core - INFO - 步骤 cdb 执行成功: 编译数据库生成完成，处理了 2 个scheme
2025-07-08 23:45:54,307 - pipeline.core - INFO - 执行步骤 5/6: ast
2025-07-08 23:45:54,307 - pipeline.core - INFO - 开始执行步骤: ast
2025-07-08 23:45:54,307 - pipeline.steps.ast_step - INFO - 开始AST抽取
2025-07-08 23:45:54,307 - pipeline.steps.ast_step - INFO - 加载了 0 个编译命令
2025-07-08 23:45:54,307 - pipeline.steps.ast_step - WARNING - 编译命令为空，这可能是CdbStep的问题，将生成基本的文件节点
2025-07-08 23:45:54,312 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5128: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 23:45:54,312 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5129: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 23:45:54,312 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5130: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 23:45:54,312 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5131: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 23:45:54,312 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5132: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-08 23:45:54,695 - pipeline.steps.ast_step - WARNING - 跳过了 47/375547 个无效的符号行
2025-07-08 23:45:54,695 - pipeline.steps.ast_step - INFO - 成功加载 375500 个符号映射
2025-07-08 23:45:54,695 - pipeline.steps.ast_step - INFO - 编译命令为空，基于符号映射生成基本节点
2025-07-08 23:45:54,845 - pipeline.steps.ast_step - INFO - 基于符号映射处理了 8260 个文件
2025-07-08 23:45:54,871 - pipeline.core - INFO - 步骤 ast 执行成功: AST抽取完成，生成了 8260 个节点和 0 条边
2025-07-08 23:45:54,871 - pipeline.core - INFO - 执行步骤 6/6: stats
2025-07-08 23:45:54,871 - pipeline.core - INFO - 开始执行步骤: stats
2025-07-08 23:45:54,871 - pipeline.steps.stats_step - INFO - 开始统计验证
2025-07-08 23:45:54,872 - pipeline.steps.stats_step - INFO - 总节点数: 8260
2025-07-08 23:45:54,872 - pipeline.steps.stats_step - INFO - 总边数: 0
2025-07-08 23:45:54,872 - pipeline.steps.stats_step - INFO - 跨仓库调用数: 0
2025-07-08 23:45:54,873 - pipeline.core - INFO - 步骤 stats 执行成功: 统计验证完成，节点: 8260, 边: 0, 跨仓库调用: 0
2025-07-08 23:45:54,873 - pipeline.core - INFO - 流水线执行完成
2025-07-09 00:03:22,320 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 00:03:22,320 - pipeline.core - INFO - 添加步骤: index-store
2025-07-09 00:03:22,320 - pipeline.core - INFO - 添加步骤: usrs
2025-07-09 00:03:22,320 - pipeline.core - INFO - 添加步骤: cdb
2025-07-09 00:03:22,320 - pipeline.core - INFO - 添加步骤: ast
2025-07-09 00:03:22,321 - pipeline.core - INFO - 添加步骤: stats
2025-07-09 00:03:22,321 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-09 00:03:22,321 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-09 00:03:22,321 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 00:03:22,321 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 00:03:22,321 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 00:03:28,071 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-09 00:03:28,072 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-09 00:03:28,073 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-09 00:03:28,073 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 61751 个项目，释放 9033033095 字节
2025-07-09 00:03:28,073 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-09 00:03:28,073 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-09 00:03:28,075 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-09 00:03:28,075 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-09 00:05:58,702 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-09 00:05:58,702 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 00:06:21,545 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-09 00:06:21,545 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-09 00:06:21,545 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-09 00:06:21,545 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-09 00:06:21,546 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-09 00:06:21,546 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-09 00:06:21,546 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-09 00:06:21,546 - pipeline.steps.usrs_step - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store DerivedData/GraphIndex/Index.noindex/DataStore --out index_symbols.jsonl
2025-07-09 00:06:37,310 - pipeline.core - INFO - 步骤 usrs 执行成功: USR提取完成，提取了 375543 个USR映射
2025-07-09 00:06:37,310 - pipeline.core - INFO - 执行步骤 4/6: cdb
2025-07-09 00:06:37,310 - pipeline.core - INFO - 开始执行步骤: cdb
2025-07-09 00:06:37,310 - pipeline.steps.cdb_step - INFO - 开始生成编译数据库
2025-07-09 00:06:37,311 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapBusiness' 生成编译数据库...
2025-07-09 00:06:37,311 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapBusiness -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-09 00:06:47,711 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 1189414 字符
2025-07-09 00:06:47,713 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 28 个CompileC行, 28 个clang命令
2025-07-09 00:06:47,713 - pipeline.steps.cdb_step - INFO - Scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 00:06:47,713 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-09 00:06:47,713 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapMiddlePlatform -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-09 00:06:54,343 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 98915 字符
2025-07-09 00:06:54,343 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 0 个CompileC行, 0 个clang命令
2025-07-09 00:06:54,343 - pipeline.steps.cdb_step - INFO - Scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 00:06:54,344 - pipeline.steps.cdb_step - INFO - 合并编译命令完成，共 0 个唯一文件
2025-07-09 00:06:54,344 - pipeline.core - INFO - 步骤 cdb 执行成功: 编译数据库生成完成，处理了 2 个scheme
2025-07-09 00:06:54,344 - pipeline.core - INFO - 执行步骤 5/6: ast
2025-07-09 00:06:54,344 - pipeline.core - INFO - 开始执行步骤: ast
2025-07-09 00:06:54,344 - pipeline.steps.ast_step - INFO - 开始AST抽取
2025-07-09 00:06:54,344 - pipeline.steps.ast_step - INFO - 加载了 0 个编译命令
2025-07-09 00:06:54,344 - pipeline.steps.ast_step - WARNING - 编译命令为空，这可能是CdbStep的问题，将生成基本的文件节点
2025-07-09 00:06:54,348 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5128: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:06:54,348 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5129: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:06:54,348 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5130: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:06:54,348 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5131: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:06:54,348 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5132: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:06:54,729 - pipeline.steps.ast_step - WARNING - 跳过了 47/375543 个无效的符号行
2025-07-09 00:06:54,729 - pipeline.steps.ast_step - INFO - 成功加载 375496 个符号映射
2025-07-09 00:06:54,729 - pipeline.steps.ast_step - INFO - 编译命令为空，基于符号映射生成基本节点
2025-07-09 00:06:54,883 - pipeline.steps.ast_step - INFO - 基于符号映射处理了 8260 个文件
2025-07-09 00:06:54,909 - pipeline.core - INFO - 步骤 ast 执行成功: AST抽取完成，生成了 8260 个节点和 0 条边
2025-07-09 00:06:54,910 - pipeline.core - INFO - 执行步骤 6/6: stats
2025-07-09 00:06:54,910 - pipeline.core - INFO - 开始执行步骤: stats
2025-07-09 00:06:54,910 - pipeline.steps.stats_step - INFO - 开始统计验证
2025-07-09 00:06:54,911 - pipeline.steps.stats_step - INFO - 总节点数: 8260
2025-07-09 00:06:54,911 - pipeline.steps.stats_step - INFO - 总边数: 0
2025-07-09 00:06:54,911 - pipeline.steps.stats_step - INFO - 跨仓库调用数: 0
2025-07-09 00:06:54,911 - pipeline.core - INFO - 步骤 stats 执行成功: 统计验证完成，节点: 8260, 边: 0, 跨仓库调用: 0
2025-07-09 00:06:54,911 - pipeline.core - INFO - 流水线执行完成
2025-07-09 00:19:35,988 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 00:19:35,989 - pipeline.core - INFO - 添加步骤: index-store
2025-07-09 00:19:35,989 - pipeline.core - INFO - 添加步骤: usrs
2025-07-09 00:19:35,989 - pipeline.core - INFO - 添加步骤: cdb
2025-07-09 00:19:35,989 - pipeline.core - INFO - 添加步骤: ast
2025-07-09 00:19:35,989 - pipeline.core - INFO - 添加步骤: stats
2025-07-09 00:19:35,989 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-09 00:19:35,989 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-09 00:19:35,990 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 00:19:35,990 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 00:19:35,990 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 00:19:41,451 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-09 00:19:41,452 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-09 00:19:41,452 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-09 00:19:41,452 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 61761 个项目，释放 9033026117 字节
2025-07-09 00:19:41,452 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-09 00:19:41,452 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-09 00:19:41,453 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-09 00:19:41,453 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-09 00:22:11,452 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-09 00:22:11,452 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 00:22:33,356 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-09 00:22:33,357 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-09 00:22:33,357 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-09 00:22:33,357 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-09 00:22:33,357 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-09 00:22:33,357 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-09 00:22:33,357 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-09 00:22:33,357 - pipeline.steps.usrs_step - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store DerivedData/GraphIndex/Index.noindex/DataStore --out index_symbols.jsonl
2025-07-09 00:22:49,259 - pipeline.core - INFO - 步骤 usrs 执行成功: USR提取完成，提取了 375544 个USR映射
2025-07-09 00:22:49,260 - pipeline.core - INFO - 执行步骤 4/6: cdb
2025-07-09 00:22:49,260 - pipeline.core - INFO - 开始执行步骤: cdb
2025-07-09 00:22:49,260 - pipeline.steps.cdb_step - INFO - 开始生成编译数据库
2025-07-09 00:22:49,260 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapBusiness' 生成编译数据库...
2025-07-09 00:22:49,260 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapBusiness -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-09 00:22:59,676 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 1189415 字符
2025-07-09 00:22:59,678 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 28 个CompileC行, 28 个clang命令
2025-07-09 00:22:59,678 - pipeline.steps.cdb_step - INFO - 过滤完成: 28/28 个文件通过过滤
2025-07-09 00:22:59,679 - pipeline.steps.cdb_step - INFO - Scheme 'QMapBusiness' 编译数据库生成成功，包含 28 个编译命令
2025-07-09 00:22:59,679 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-09 00:22:59,679 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapMiddlePlatform -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-09 00:23:05,805 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 35876 字符
2025-07-09 00:23:05,805 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 0 个CompileC行, 0 个clang命令
2025-07-09 00:23:05,805 - pipeline.steps.cdb_step - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 00:23:05,805 - pipeline.steps.cdb_step - INFO - Scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 00:23:05,806 - pipeline.steps.cdb_step - INFO - 合并编译命令完成，共 28 个唯一文件
2025-07-09 00:23:05,806 - pipeline.core - INFO - 步骤 cdb 执行成功: 编译数据库生成完成，处理了 2 个scheme
2025-07-09 00:23:05,806 - pipeline.core - INFO - 执行步骤 5/6: ast
2025-07-09 00:23:05,806 - pipeline.core - INFO - 开始执行步骤: ast
2025-07-09 00:23:05,806 - pipeline.steps.ast_step - INFO - 开始AST抽取
2025-07-09 00:23:05,806 - pipeline.steps.ast_step - INFO - 加载了 28 个编译命令
2025-07-09 00:23:05,811 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5128: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:23:05,811 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5129: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:23:05,811 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5130: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:23:05,811 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5131: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:23:05,811 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5132: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - WARNING - 跳过了 47/375544 个无效的符号行
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 成功加载 375497 个符号映射
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 1/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapVectorLayout/QMapVectorLayout-dummy.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 2/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicDSLPrebuildOperation.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 3/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLService.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 4/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayerHostingView.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 5/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayer.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 6/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Report/QMVLReportManager.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 7/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceManager.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 8/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceReporter.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 9/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapViewWidget.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 10/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapView.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 11/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLMediaInfo.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 12/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 13/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+OmnipotentMap.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 14/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+RouteExplain.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 15/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+Navi.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 16/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+BestL4Camera.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 17/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapViewWidget.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 18/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieWidget.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 19/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieView.m
2025-07-09 00:23:06,198 - pipeline.steps.ast_step - INFO - 处理文件 20/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Log/QMVLLogger.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 21/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Injector/QMVLInjector.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 22/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageView.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 23/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageTransformer.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 24/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageFetchManager.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 25/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLFileItem.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 26/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLCardViewCache.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 27/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/QMDynamicMarkerService.m
2025-07-09 00:23:06,200 - pipeline.steps.ast_step - INFO - 处理文件 28/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicMarkerGenerator.m
2025-07-09 00:23:06,209 - pipeline.core - INFO - 步骤 ast 执行成功: AST抽取完成，生成了 56 个节点和 28 条边
2025-07-09 00:23:06,210 - pipeline.core - INFO - 执行步骤 6/6: stats
2025-07-09 00:23:06,210 - pipeline.core - INFO - 开始执行步骤: stats
2025-07-09 00:23:06,210 - pipeline.steps.stats_step - INFO - 开始统计验证
2025-07-09 00:23:06,210 - pipeline.steps.stats_step - INFO - 总节点数: 56
2025-07-09 00:23:06,210 - pipeline.steps.stats_step - INFO - 总边数: 28
2025-07-09 00:23:06,210 - pipeline.steps.stats_step - INFO - 跨仓库调用数: 0
2025-07-09 00:23:06,210 - pipeline.core - ERROR - 步骤 stats 执行失败: 验证失败: 节点数量 56 低于最小要求 100
2025-07-09 00:23:06,210 - pipeline.core - ERROR - 步骤 stats 执行失败，停止流水线
2025-07-09 00:34:04,360 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 00:34:04,361 - pipeline.core - INFO - 添加步骤: index-store
2025-07-09 00:34:04,361 - pipeline.core - INFO - 添加步骤: usrs
2025-07-09 00:34:04,361 - pipeline.core - INFO - 添加步骤: cdb
2025-07-09 00:34:04,361 - pipeline.core - INFO - 添加步骤: ast
2025-07-09 00:34:04,361 - pipeline.core - INFO - 添加步骤: stats
2025-07-09 00:34:04,361 - __main__ - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-09 00:34:04,361 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线
2025-07-09 00:34:04,361 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 00:34:04,361 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 00:34:04,361 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 00:34:09,623 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-09 00:34:09,624 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index
2025-07-09 00:34:09,624 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-09 00:34:09,624 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 61751 个项目，释放 9034999006 字节
2025-07-09 00:34:09,624 - pipeline.core - INFO - 执行步骤 2/6: index-store
2025-07-09 00:34:09,624 - pipeline.core - INFO - 开始执行步骤: index-store
2025-07-09 00:34:09,625 - pipeline.steps.index_store_step - INFO - 开始生成Clang索引存储
2025-07-09 00:34:09,625 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapBusiness' 生成索引...
2025-07-09 00:36:39,772 - pipeline.steps.index_store_step - INFO - Scheme 'QMapBusiness' 索引生成成功
2025-07-09 00:36:39,773 - pipeline.steps.index_store_step - INFO - 为scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 00:37:02,603 - pipeline.steps.index_store_step - INFO - Scheme 'QMapMiddlePlatform' 索引生成成功
2025-07-09 00:37:02,603 - pipeline.core - INFO - 步骤 index-store 执行成功: 索引生成完成，处理了 2 个scheme
2025-07-09 00:37:02,603 - pipeline.core - INFO - 执行步骤 3/6: usrs
2025-07-09 00:37:02,603 - pipeline.core - INFO - 开始执行步骤: usrs
2025-07-09 00:37:02,603 - pipeline.steps.usrs_step - INFO - 检查索引存储目录: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-09 00:37:02,604 - pipeline.steps.usrs_step - INFO - 开始提取USR到文件路径映射
2025-07-09 00:37:02,604 - pipeline.steps.usrs_step - INFO - 使用索引存储路径: DerivedData/GraphIndex/Index.noindex/DataStore
2025-07-09 00:37:02,604 - pipeline.steps.usrs_step - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store DerivedData/GraphIndex/Index.noindex/DataStore --out index_symbols.jsonl
2025-07-09 00:37:19,017 - pipeline.core - INFO - 步骤 usrs 执行成功: USR提取完成，提取了 375549 个USR映射
2025-07-09 00:37:19,017 - pipeline.core - INFO - 执行步骤 4/6: cdb
2025-07-09 00:37:19,017 - pipeline.core - INFO - 开始执行步骤: cdb
2025-07-09 00:37:19,018 - pipeline.steps.cdb_step - INFO - 开始生成编译数据库
2025-07-09 00:37:19,018 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapBusiness' 生成编译数据库...
2025-07-09 00:37:19,018 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapBusiness -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-09 00:37:29,229 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 1189414 字符
2025-07-09 00:37:29,231 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 28 个CompileC行, 28 个clang命令
2025-07-09 00:37:29,231 - pipeline.steps.cdb_step - INFO - 过滤完成: 28/28 个文件通过过滤
2025-07-09 00:37:29,232 - pipeline.steps.cdb_step - INFO - Scheme 'QMapBusiness' 编译数据库生成成功，包含 28 个编译命令
2025-07-09 00:37:29,232 - pipeline.steps.cdb_step - INFO - 为scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-09 00:37:29,232 - pipeline.steps.cdb_step - INFO - 执行命令: xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapMiddlePlatform -configuration Debug -sdk iphonesimulator -destination platform=iOS Simulator,name=iPhone 15 -derivedDataPath DerivedData/GraphIndex build -verbose
2025-07-09 00:37:35,477 - pipeline.steps.cdb_step - INFO - 开始解析构建日志，长度: 98915 字符
2025-07-09 00:37:35,477 - pipeline.steps.cdb_step - INFO - 解析完成: 找到 0 个CompileC行, 0 个clang命令
2025-07-09 00:37:35,477 - pipeline.steps.cdb_step - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 00:37:35,477 - pipeline.steps.cdb_step - INFO - Scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 00:37:35,478 - pipeline.steps.cdb_step - INFO - 合并编译命令完成，共 28 个唯一文件
2025-07-09 00:37:35,478 - pipeline.core - INFO - 步骤 cdb 执行成功: 编译数据库生成完成，处理了 2 个scheme
2025-07-09 00:37:35,478 - pipeline.core - INFO - 执行步骤 5/6: ast
2025-07-09 00:37:35,479 - pipeline.core - INFO - 开始执行步骤: ast
2025-07-09 00:37:35,479 - pipeline.steps.ast_step - INFO - 开始AST抽取
2025-07-09 00:37:35,479 - pipeline.steps.ast_step - INFO - 加载了 28 个编译命令
2025-07-09 00:37:35,484 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5128: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:37:35,484 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5129: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:37:35,484 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5130: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:37:35,484 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5131: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:37:35,484 - pipeline.steps.ast_step - WARNING - 跳过无效JSON行 5132: Expecting ',' delimiter: line 1 column 41 (char 40)
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - WARNING - 跳过了 47/375549 个无效的符号行
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 成功加载 375502 个符号映射
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理 28 个编译命令
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 1/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapVectorLayout/QMapVectorLayout-dummy.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 2/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicDSLPrebuildOperation.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 3/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLService.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 4/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Report/QMVLReportManager.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 5/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayerHostingView.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 6/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceReporter.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 7/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapView.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 8/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapViewWidget.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 9/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayer.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 10/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLMediaInfo.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 11/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceManager.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 12/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+RouteExplain.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 13/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+OmnipotentMap.m
2025-07-09 00:37:35,870 - pipeline.steps.ast_step - INFO - 处理编译文件 14/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView.m
2025-07-09 00:37:35,871 - pipeline.steps.ast_step - INFO - 处理编译文件 15/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+Navi.m
2025-07-09 00:37:35,871 - pipeline.steps.ast_step - INFO - 处理编译文件 16/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapViewWidget.m
2025-07-09 00:37:35,871 - pipeline.steps.ast_step - INFO - 处理编译文件 17/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+BestL4Camera.m
2025-07-09 00:37:35,871 - pipeline.steps.ast_step - INFO - 处理编译文件 18/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieWidget.m
2025-07-09 00:37:35,871 - pipeline.steps.ast_step - INFO - 处理编译文件 19/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieView.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 20/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Log/QMVLLogger.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 21/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Injector/QMVLInjector.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 22/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageView.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 23/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageTransformer.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 24/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageFetchManager.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 25/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLFileItem.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 26/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLCardViewCache.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 27/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/QMDynamicMarkerService.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 处理编译文件 28/28: /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicMarkerGenerator.m
2025-07-09 00:37:35,872 - pipeline.steps.ast_step - INFO - 补充处理符号映射中的项目文件
2025-07-09 00:37:35,988 - pipeline.steps.ast_step - INFO - 处理完成: 编译命令文件 28 个, 符号映射文件 2890 个, 总计 2918 个文件
2025-07-09 00:37:36,003 - pipeline.core - INFO - 步骤 ast 执行成功: AST抽取完成，生成了 2946 个节点和 28 条边
2025-07-09 00:37:36,003 - pipeline.core - INFO - 执行步骤 6/6: stats
2025-07-09 00:37:36,003 - pipeline.core - INFO - 开始执行步骤: stats
2025-07-09 00:37:36,003 - pipeline.steps.stats_step - INFO - 开始统计验证
2025-07-09 00:37:36,004 - pipeline.steps.stats_step - INFO - 总节点数: 2946
2025-07-09 00:37:36,004 - pipeline.steps.stats_step - INFO - 总边数: 28
2025-07-09 00:37:36,004 - pipeline.steps.stats_step - INFO - 跨仓库调用数: 0
2025-07-09 00:37:36,004 - pipeline.core - INFO - 步骤 stats 执行成功: 统计验证完成，节点: 2946, 边: 28, 跨仓库调用: 0
2025-07-09 00:37:36,004 - pipeline.core - INFO - 流水线执行完成
