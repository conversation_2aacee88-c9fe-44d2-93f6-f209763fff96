#!/usr/bin/env python3
"""
QMap Pipeline运行器 - 完整的代码图谱生成流水线

使用方法:
    python run_pipeline.py                    # 运行完整流水线
    python run_pipeline.py --from clean       # 从指定步骤开始
    python run_pipeline.py --to usrs          # 运行到指定步骤
    python run_pipeline.py --dry-run          # 干运行模式
    python run_pipeline.py --config custom.json  # 使用自定义配置
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pipeline import Pipeline, PipelineConfig, PipelineState
from pipeline.steps import CleanStep, IndexStoreStep, UsrsStep
from pipeline.error_handling import ExponentialBackoffStrategy, ErrorClassifier


def setup_logging(level: str = "INFO"):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('pipeline.log')
        ]
    )


def create_pipeline(config: PipelineConfig, dry_run: bool = False) -> Pipeline:
    """创建完整的流水线"""
    pipeline = Pipeline("QMap代码图谱生成流水线")

    # 创建步骤
    clean_step = CleanStep(config, dry_run=dry_run)
    index_store_step = IndexStoreStep(config)
    usrs_step = UsrsStep(config)

    # 配置错误处理和重试
    retry_strategy = ExponentialBackoffStrategy(
        initial_delay=1.0,
        max_delay=60.0,
        max_attempts=3
    )
    error_classifier = ErrorClassifier()

    for step in [clean_step, index_store_step, usrs_step]:
        step.retry_strategy = retry_strategy
        step.error_classifier = error_classifier

    # 设置依赖关系
    index_store_step.add_dependency("clean")
    usrs_step.add_dependency("index-store")

    # 添加步骤到流水线
    pipeline.add_step(clean_step)
    pipeline.add_step(index_store_step)
    pipeline.add_step(usrs_step)

    return pipeline