#!/usr/bin/env python3
"""
Analyze Property Nodes Design

This script analyzes the Property nodes in the Neo4j database to understand their design rationale.
"""

from neo4j import GraphDatabase
import json

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 深入分析Property节点设计')
        print('=' * 60)
        
        # 1. Property节点总体统计
        result = session.run('MATCH (p:Property) RETURN count(p) as total_properties')
        total_properties = result.single()['total_properties']
        print(f'📊 Property节点总数: {total_properties:,}')
        
        # 2. 按type属性分类
        result = session.run('''
            MATCH (p:Property)
            RETURN p.type as property_type, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 Property节点类型分布:')
        virtual_count = 0
        real_count = 0
        
        for record in result:
            prop_type = record['property_type']
            count = record['count']
            print(f'  {prop_type}: {count:,}')
            
            if prop_type == 'virtual_property':
                virtual_count = count
            else:
                real_count += count
        
        print(f'\n🎯 Property节点分类:')
        print(f'  虚拟属性 (virtual_property): {virtual_count:,} ({virtual_count/total_properties*100:.1f}%)')
        print(f'  真实属性 (其他类型): {real_count:,} ({real_count/total_properties*100:.1f}%)')
        
        # 3. 分析virtual_property的特征
        result = session.run('''
            MATCH (p:Property {type: 'virtual_property'})
            RETURN p.name as name, p.file as file, p.class as class, p.virtual as virtual
            LIMIT 10
        ''')
        
        print('\n🔍 virtual_property节点示例:')
        for record in result:
            name = record['name']
            file = record['file']
            class_name = record['class']
            virtual = record['virtual']
            print(f'  名称: {name}')
            print(f'  文件: {file}')
            print(f'  类: {class_name}')
            print(f'  虚拟: {virtual}')
            print()
        
        # 4. 分析真实Property的特征
        result = session.run('''
            MATCH (p:Property)
            WHERE p.type <> 'virtual_property' OR p.type IS NULL
            RETURN p.name as name, p.file as file, p.class as class, p.type as type
            LIMIT 10
        ''')
        
        print('🔍 真实Property节点示例:')
        for record in result:
            name = record['name']
            file = record['file']
            class_name = record['class']
            prop_type = record['type']
            print(f'  名称: {name}')
            print(f'  文件: {file}')
            print(f'  类: {class_name}')
            print(f'  类型: {prop_type}')
            print()
        
        # 5. 分析Property节点的关系
        result = session.run('''
            MATCH (p:Property)-[r]->(target)
            RETURN type(r) as rel_type, labels(target) as target_labels, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('📊 Property节点的出度关系:')
        for record in result:
            rel_type = record['rel_type']
            target_labels = ', '.join(record['target_labels'])
            count = record['count']
            print(f'  {rel_type} -> {target_labels}: {count:,}')
        
        # 6. 分析指向Property节点的关系
        result = session.run('''
            MATCH (source)-[r]->(p:Property)
            RETURN type(r) as rel_type, labels(source) as source_labels, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 指向Property节点的关系:')
        for record in result:
            rel_type = record['rel_type']
            source_labels = ', '.join(record['source_labels'])
            count = record['count']
            print(f'  {source_labels} -> {rel_type} -> Property: {count:,}')
        
        # 7. 分析ACCESSES关系的模式
        result = session.run('''
            MATCH (accessor)-[:ACCESSES]->(p:Property)
            RETURN labels(accessor) as accessor_labels, p.type as property_type, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('\n📊 ACCESSES关系模式分析:')
        for record in result:
            accessor_labels = ', '.join(record['accessor_labels'])
            property_type = record['property_type']
            count = record['count']
            print(f'  {accessor_labels} -> {property_type}: {count:,}')
        
        # 8. 分析Property节点在架构中的作用
        result = session.run('''
            MATCH (f:File)-[:DEFINES]->(c:Class)-[:DEFINES]->(p:Property)
            RETURN f.name as file, c.name as class, count(p) as property_count
            ORDER BY property_count DESC
            LIMIT 10
        ''')
        
        print('\n🏗️ 类的Property定义统计 (Top 10):')
        for record in result:
            file = record['file']
            class_name = record['class']
            property_count = record['property_count']
            print(f'  {class_name} ({file}): {property_count} 个属性')

    driver.close()

if __name__ == "__main__":
    main()
