#!/usr/bin/env python3
"""
Fix Enhanced AST Relationships

This script fixes the relationship data to ensure all referenced nodes exist.
"""

import json
from pathlib import Path
from hashlib import sha1
from collections import defaultdict

def sha1_hash(text: str) -> str:
    """Generate SHA1 hash for given text"""
    return sha1(text.encode('utf-8')).hexdigest()

def node_line(label: str, node_id: str, attrs: dict) -> str:
    """Generate a node line in JSONL format"""
    return json.dumps({
        "id": node_id,
        "label": label,
        "attrs": attrs
    })

def edge_line(edge_type: str, src_id: str, dst_id: str, attrs: dict) -> str:
    """Generate an edge line in JSONL format"""
    return json.dumps({
        "type": edge_type,
        "src": src_id,
        "dst": dst_id,
        "attrs": attrs
    })

def main():
    print("🔧 修复增强型AST关系数据")
    print("=" * 50)
    
    # 读取现有节点
    nodes_file = Path("test_enhanced_ast_fixed/nodes.jsonl")
    edges_file = Path("test_enhanced_ast_fixed/edges.jsonl")
    
    existing_nodes = set()
    nodes_data = []
    
    print("📖 读取现有节点...")
    with open(nodes_file, 'r') as f:
        for line in f:
            if line.strip():
                node = json.loads(line.strip())
                existing_nodes.add(node['id'])
                nodes_data.append(line.strip())
    
    print(f"✅ 读取了 {len(existing_nodes)} 个现有节点")
    
    # 读取关系并修复
    print("🔗 处理关系...")
    fixed_edges = []
    edge_stats = defaultdict(int)
    skipped_stats = defaultdict(int)
    new_nodes = []
    
    with open(edges_file, 'r') as f:
        for line in f:
            if line.strip():
                edge = json.loads(line.strip())
                edge_type = edge['type']
                src_id = edge['src']
                dst_id = edge['dst']
                attrs = edge.get('attrs', {})
                
                # 检查源节点和目标节点是否存在
                src_exists = src_id in existing_nodes
                dst_exists = dst_id in existing_nodes
                
                if src_exists and dst_exists:
                    # 两个节点都存在，保留关系
                    fixed_edges.append(line.strip())
                    edge_stats[edge_type] += 1
                elif edge_type == "DEFINES":
                    # DEFINES关系必须保留，创建缺失的节点
                    if not src_exists:
                        # 创建源节点（通常是文件）
                        file_path = attrs.get('file', 'unknown')
                        new_node = node_line("File", src_id, {
                            "name": file_path,
                            "path": file_path
                        })
                        new_nodes.append(new_node)
                        existing_nodes.add(src_id)
                    
                    if not dst_exists:
                        # 根据属性推断目标节点类型
                        if 'class' in attrs:
                            new_node = node_line("Class", dst_id, {
                                "name": attrs.get('class', 'Unknown'),
                                "file": attrs.get('file', 'unknown')
                            })
                        elif 'method' in attrs:
                            new_node = node_line("Method", dst_id, {
                                "name": attrs.get('method', 'Unknown'),
                                "file": attrs.get('file', 'unknown')
                            })
                        elif 'property' in attrs:
                            new_node = node_line("Property", dst_id, {
                                "name": attrs.get('property', 'Unknown'),
                                "file": attrs.get('file', 'unknown')
                            })
                        else:
                            new_node = node_line("CodeNode", dst_id, {
                                "name": "Unknown",
                                "file": attrs.get('file', 'unknown')
                            })
                        new_nodes.append(new_node)
                        existing_nodes.add(dst_id)
                    
                    fixed_edges.append(line.strip())
                    edge_stats[edge_type] += 1
                elif edge_type in ["CALLS", "ACCESSES", "INHERITS", "IMPLEMENTS"]:
                    # 为重要的关系类型创建虚拟节点
                    if not src_exists:
                        # 创建源节点
                        if edge_type == "CALLS":
                            file_path = attrs.get('file', 'unknown')
                            file_id = sha1_hash(file_path)
                            if file_id not in existing_nodes:
                                new_node = node_line("File", file_id, {
                                    "name": file_path,
                                    "path": file_path
                                })
                                new_nodes.append(new_node)
                                existing_nodes.add(file_id)
                            src_id = file_id  # 使用文件作为调用源
                        else:
                            new_node = node_line("CodeNode", src_id, {
                                "name": "Unknown",
                                "file": attrs.get('file', 'unknown'),
                                "virtual": True
                            })
                            new_nodes.append(new_node)
                            existing_nodes.add(src_id)

                    if not dst_exists:
                        # 创建目标节点
                        if edge_type == "CALLS":
                            method_name = attrs.get('method', 'Unknown')
                            receiver = attrs.get('receiver', 'Unknown')
                            new_node = node_line("Method", dst_id, {
                                "name": method_name,
                                "class": receiver,
                                "file": "external",
                                "virtual": True
                            })
                        elif edge_type == "ACCESSES":
                            property_name = attrs.get('property', 'Unknown')
                            new_node = node_line("Property", dst_id, {
                                "name": property_name,
                                "file": "external",
                                "virtual": True
                            })
                        elif edge_type == "INHERITS":
                            superclass = attrs.get('superclass', 'Unknown')
                            new_node = node_line("Class", dst_id, {
                                "name": superclass,
                                "file": "external",
                                "virtual": True
                            })
                        elif edge_type == "IMPLEMENTS":
                            protocol = attrs.get('protocol', 'Unknown')
                            new_node = node_line("Protocol", dst_id, {
                                "name": protocol,
                                "file": "external",
                                "virtual": True
                            })
                        else:
                            new_node = node_line("CodeNode", dst_id, {
                                "name": "Unknown",
                                "file": "external",
                                "virtual": True
                            })
                        new_nodes.append(new_node)
                        existing_nodes.add(dst_id)

                    # 更新关系的src_id如果需要
                    if edge_type == "CALLS" and not src_exists:
                        edge['src'] = src_id
                        line = json.dumps(edge)

                    fixed_edges.append(line.strip())
                    edge_stats[edge_type] += 1
                else:
                    # 其他关系类型，如果节点不存在就跳过
                    skipped_stats[edge_type] += 1
    
    print(f"✅ 处理完成")
    print(f"📊 保留的关系统计:")
    for edge_type, count in sorted(edge_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {edge_type}: {count:,}")
    
    print(f"📊 跳过的关系统计:")
    for edge_type, count in sorted(skipped_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {edge_type}: {count:,}")
    
    print(f"🆕 创建了 {len(new_nodes)} 个新节点")
    
    # 写入修复后的数据
    print("💾 保存修复后的数据...")
    
    # 保存节点（原有 + 新增）
    with open(nodes_file, 'w') as f:
        for node_line in nodes_data:
            f.write(node_line + '\n')
        for new_node in new_nodes:
            f.write(new_node + '\n')
    
    # 保存关系
    with open(edges_file, 'w') as f:
        for edge_line in fixed_edges:
            f.write(edge_line + '\n')
    
    print(f"✅ 修复完成!")
    print(f"📊 最终统计:")
    print(f"  节点数量: {len(nodes_data) + len(new_nodes):,}")
    print(f"  关系数量: {len(fixed_edges):,}")

if __name__ == "__main__":
    main()
