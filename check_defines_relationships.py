#!/usr/bin/env python3
"""
Check DEFINES Relationships

This script checks the DEFINES relationships in Neo4j database.
"""

from neo4j import GraphDatabase

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 检查DEFINES关系')
        print('=' * 40)
        
        # 直接检查DEFINES关系
        result = session.run('MATCH ()-[r:DEFINES]->() RETURN count(r) as defines_count')
        defines_count = result.single()['defines_count']
        print(f'📊 DEFINES关系总数: {defines_count:,}')
        
        # 检查File->Method的DEFINES关系
        result = session.run('MATCH (f:File)-[r:DEFINES]->(m:Method) RETURN count(r) as file_to_method')
        file_to_method = result.single()['file_to_method']
        print(f'📊 File->Method DEFINES关系: {file_to_method:,}')
        
        # 检查Class->Method的DEFINES关系
        result = session.run('MATCH (c:Class)-[r:DEFINES]->(m:Method) RETURN count(r) as class_to_method')
        class_to_method = result.single()['class_to_method']
        print(f'📊 Class->Method DEFINES关系: {class_to_method:,}')
        
        # 检查DEFINES关系的类型分布
        result = session.run('''
            MATCH (src)-[r:DEFINES]->(dst)
            RETURN labels(src) as src_labels, labels(dst) as dst_labels, count(r) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 DEFINES关系类型分布:')
        for record in result:
            src_labels = ', '.join(record['src_labels'])
            dst_labels = ', '.join(record['dst_labels'])
            count = record['count']
            print(f'  {src_labels} -> {dst_labels}: {count:,}')
        
        # 检查具体的File->Class->Method层次结构
        result = session.run('''
            MATCH (f:File)-[:DEFINES]->(c:Class)-[:DEFINES]->(m:Method)
            RETURN f.name as file_name, c.name as class_name, count(m) as method_count
            ORDER BY method_count DESC
            LIMIT 10
        ''')
        
        print('\n🏗️ File->Class->Method层次结构 (Top 10):')
        for record in result:
            file_name = record['file_name']
            class_name = record['class_name']
            method_count = record['method_count']
            from pathlib import Path
            print(f'  {Path(file_name).name} -> {class_name}: {method_count} 个方法')

    driver.close()

if __name__ == "__main__":
    main()
