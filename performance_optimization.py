#!/usr/bin/env python3
"""
Performance Optimization for Neo4j Code Knowledge Graph

This script optimizes the Neo4j database for better query performance.
"""

import time
from neo4j import GraphDatabase

def create_indexes(session):
    """Create indexes for better query performance"""
    print('🔧 创建性能索引...')
    
    indexes = [
        # Node ID indexes (most important)
        "CREATE INDEX node_id_index IF NOT EXISTS FOR (n) ON (n.id)",
        
        # Name indexes for different node types
        "CREATE INDEX method_name_index IF NOT EXISTS FOR (m:Method) ON (m.name)",
        "CREATE INDEX class_name_index IF NOT EXISTS FOR (c:Class) ON (c.name)",
        "CREATE INDEX file_name_index IF NOT EXISTS FOR (f:File) ON (f.name)",
        "CREATE INDEX property_name_index IF NOT EXISTS FOR (p:Property) ON (p.name)",
        
        # File path indexes
        "CREATE INDEX file_path_index IF NOT EXISTS FOR (f:File) ON (f.path)",
        
        # Virtual node indexes
        "CREATE INDEX virtual_index IF NOT EXISTS FOR (n) ON (n.virtual)",
        
        # Repository indexes
        "CREATE INDEX repository_index IF NOT EXISTS FOR (n) ON (n.repository)",
    ]
    
    for index_query in indexes:
        try:
            session.run(index_query)
            print(f'  ✅ 创建索引: {index_query.split("FOR")[0].split("IF NOT EXISTS")[0].strip()}')
        except Exception as e:
            print(f'  ⚠️ 索引创建失败: {e}')

def optimize_import_script():
    """Create an optimized import script for better performance"""
    print('🚀 创建优化的导入脚本...')
    
    optimized_script = '''#!/usr/bin/env python3
"""
Optimized Neo4j Import Script

This script uses batch processing and transactions for better performance.
"""

import json
from pathlib import Path
from neo4j import GraphDatabase

def batch_import_nodes(session, nodes_file, batch_size=1000):
    """Import nodes in batches for better performance"""
    print(f'📦 批量导入节点 (批次大小: {batch_size})...')
    
    nodes_count = 0
    batch = []
    
    with open(nodes_file, 'r') as f:
        for line in f:
            if line.strip():
                node = json.loads(line.strip())
                batch.append(node)
                
                if len(batch) >= batch_size:
                    # Process batch
                    with session.begin_transaction() as tx:
                        for node in batch:
                            node_id = node['id']
                            label = node['label']
                            attrs = node.get('attrs', {})
                            
                            query = f"""
                            MERGE (n:{label} {{id: $id}})
                            SET n += $attrs
                            """
                            tx.run(query, id=node_id, attrs=attrs)
                    
                    nodes_count += len(batch)
                    batch = []
                    
                    if nodes_count % 10000 == 0:
                        print(f'  已导入 {nodes_count} 个节点...')
        
        # Process remaining nodes
        if batch:
            with session.begin_transaction() as tx:
                for node in batch:
                    node_id = node['id']
                    label = node['label']
                    attrs = node.get('attrs', {})
                    
                    query = f"""
                    MERGE (n:{label} {{id: $id}})
                    SET n += $attrs
                    """
                    tx.run(query, id=node_id, attrs=attrs)
            
            nodes_count += len(batch)
    
    return nodes_count

def batch_import_edges(session, edges_file, batch_size=1000):
    """Import edges in batches for better performance"""
    print(f'🔗 批量导入关系 (批次大小: {batch_size})...')
    
    edges_count = 0
    edge_type_counts = {}
    batch = []
    
    with open(edges_file, 'r') as f:
        for line in f:
            if line.strip():
                edge = json.loads(line.strip())
                batch.append(edge)
                
                if len(batch) >= batch_size:
                    # Process batch
                    with session.begin_transaction() as tx:
                        for edge in batch:
                            edge_type = edge['type']
                            src_id = edge['src']
                            dst_id = edge['dst']
                            attrs = edge.get('attrs', {})
                            
                            query = f"""
                            MATCH (src {{id: $src_id}})
                            MATCH (dst {{id: $dst_id}})
                            MERGE (src)-[r:{edge_type}]->(dst)
                            SET r += $attrs
                            """
                            tx.run(query, src_id=src_id, dst_id=dst_id, attrs=attrs)
                            edge_type_counts[edge_type] = edge_type_counts.get(edge_type, 0) + 1
                    
                    edges_count += len(batch)
                    batch = []
                    
                    if edges_count % 10000 == 0:
                        print(f'  已导入 {edges_count} 条关系...')
        
        # Process remaining edges
        if batch:
            with session.begin_transaction() as tx:
                for edge in batch:
                    edge_type = edge['type']
                    src_id = edge['src']
                    dst_id = edge['dst']
                    attrs = edge.get('attrs', {})
                    
                    query = f"""
                    MATCH (src {{id: $src_id}})
                    MATCH (dst {{id: $dst_id}})
                    MERGE (src)-[r:{edge_type}]->(dst)
                    SET r += $attrs
                    """
                    tx.run(query, src_id=src_id, dst_id=dst_id, attrs=attrs)
                    edge_type_counts[edge_type] = edge_type_counts.get(edge_type, 0) + 1
            
            edges_count += len(batch)
    
    return edges_count, edge_type_counts

def main():
    print('🚀 优化的Neo4j导入脚本')
    print('=' * 50)
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    with driver.session() as session:
        # Clear database
        print('🧹 清理数据库...')
        session.run('MATCH (n) DETACH DELETE n')
        
        # Import with optimized batching
        nodes_count = batch_import_nodes(session, 'ast_out_index_all_fixed/nodes.jsonl')
        edges_count, edge_type_counts = batch_import_edges(session, 'ast_out_index_all_fixed/edges.jsonl')
        
        print(f'✅ 优化导入完成: {nodes_count} 节点, {edges_count} 关系')
        
        # Create indexes
        create_indexes(session)
    
    driver.close()

if __name__ == "__main__":
    main()
'''
    
    with open('optimized_import.py', 'w') as f:
        f.write(optimized_script)
    
    print('✅ 优化导入脚本已创建: optimized_import.py')

def benchmark_queries(session):
    """Benchmark common queries"""
    print('📊 查询性能基准测试...')
    
    queries = [
        ("节点计数", "MATCH (n) RETURN count(n) as count"),
        ("关系计数", "MATCH ()-[r]->() RETURN count(r) as count"),
        ("CALLS关系计数", "MATCH ()-[r:CALLS]->() RETURN count(r) as count"),
        ("方法节点查询", "MATCH (m:Method) RETURN count(m) as count"),
        ("文件到方法调用", "MATCH (f:File)-[:CALLS]->(m:Method) RETURN count(*) as count"),
        ("类继承关系", "MATCH (c1:Class)-[:INHERITS]->(c2:Class) RETURN count(*) as count"),
    ]
    
    for name, query in queries:
        start_time = time.time()
        try:
            result = session.run(query)
            count = result.single()['count']
            elapsed = time.time() - start_time
            print(f'  {name}: {elapsed:.3f}s ({count:,} 结果)')
        except Exception as e:
            print(f'  {name}: 查询失败 - {e}')

def main():
    print('🔧 Neo4j性能优化工具')
    print('=' * 50)
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    with driver.session() as session:
        # Create performance indexes
        create_indexes(session)
        
        # Benchmark queries
        benchmark_queries(session)
        
        # Generate optimized import script
        optimize_import_script()
        
        print()
        print('🎯 性能优化建议:')
        print('  1. ✅ 已创建关键索引')
        print('  2. ✅ 已生成优化导入脚本')
        print('  3. 💡 使用批量事务处理')
        print('  4. 💡 定期维护数据库统计信息')
        print('  5. 💡 考虑使用Neo4j Enterprise版本的并行导入')
    
    driver.close()

if __name__ == "__main__":
    main()
