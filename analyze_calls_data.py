#!/usr/bin/env python3
"""
Analyze CALLS Data

This script analyzes the CALLS relationship data to understand why caller detection fails.
"""

import json
from pathlib import Path

def main():
    print('📊 分析CALLS关系数据')
    print('=' * 50)
    
    edges_file = Path('test_calls_fix/edges.jsonl')
    calls_with_caller = 0
    calls_without_caller = 0
    total_calls = 0
    call_types = {}
    
    with open(edges_file, 'r') as f:
        for line in f:
            if line.strip():
                edge = json.loads(line.strip())
                if edge['type'] == 'CALLS':
                    total_calls += 1
                    attrs = edge.get('attrs', {})
                    caller_method = attrs.get('caller_method')
                    call_type = attrs.get('call_type', 'unknown')
                    
                    call_types[call_type] = call_types.get(call_type, 0) + 1
                    
                    if caller_method:
                        calls_with_caller += 1
                    else:
                        calls_without_caller += 1
    
    print(f'📊 调用者检测分析:')
    print(f'  总CALLS关系: {total_calls:,}')
    print(f'  有调用者信息: {calls_with_caller:,} ({calls_with_caller/total_calls*100:.1f}%)')
    print(f'  无调用者信息: {calls_without_caller:,} ({calls_without_caller/total_calls*100:.1f}%)')
    
    print()
    print(f'📊 调用类型分布:')
    for call_type, count in sorted(call_types.items(), key=lambda x: x[1], reverse=True):
        print(f'  {call_type}: {count:,}')
    
    # 分析无调用者信息的调用示例
    print()
    print('🔍 无调用者信息的调用示例:')
    count = 0
    with open(edges_file, 'r') as f:
        for line in f:
            if line.strip() and count < 5:
                edge = json.loads(line.strip())
                if edge['type'] == 'CALLS':
                    attrs = edge.get('attrs', {})
                    caller_method = attrs.get('caller_method')
                    if not caller_method:
                        print(f'  文件: {attrs.get("file", "unknown")}')
                        print(f'  行号: {attrs.get("line", "unknown")}')
                        print(f'  调用类型: {attrs.get("call_type", "unknown")}')
                        print(f'  接收者: {attrs.get("receiver", "unknown")}')
                        print(f'  方法: {attrs.get("method", "unknown")}')
                        print()
                        count += 1

if __name__ == "__main__":
    main()
