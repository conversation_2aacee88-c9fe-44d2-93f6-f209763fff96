#!/usr/bin/env python3
"""
Enhanced AST Extractor for iOS/Objective-C Projects

This script provides a hybrid approach to AST extraction that combines:
1. Successful file discovery and basic node generation
2. Enhanced source code analysis for relationship detection
3. Fallback mechanisms when clang parsing fails

The goal is to achieve target relationship counts:
- CALLS relationships: 10,000+ (vs current 159)
- Proper node classification: eliminate 77,070 unclassified nodes
- Enhanced relationship accuracy for INHERITS, IMPLEMENTS, EXTENDS
"""

import json
import re
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
import hashlib
import argparse
from collections import defaultdict

def sha1(text: str) -> str:
    """Generate SHA1 hash for node IDs"""
    return hashlib.sha1(text.encode()).hexdigest()

def node_line(label: str, id_: str, attrs: Dict) -> str:
    """Generate node JSON line"""
    return json.dumps({"label": label, "id": id_, "attrs": attrs}, ensure_ascii=False)

def edge_line(type_: str, src: str, dst: str, attrs: Dict = None) -> str:
    """Generate edge JSON line"""
    return json.dumps({"type": type_, "src": src, "dst": dst, "attrs": attrs or {}}, ensure_ascii=False)

class EnhancedASTExtractor:
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.nodes = set()
        self.edges = set()
        self.file_to_classes = defaultdict(list)
        self.class_to_methods = defaultdict(list)
        self.class_to_properties = defaultdict(list)
        self.method_calls = defaultdict(list)
        self.file_contents = {}  # file_path -> content (for extended relationship analysis)
        
    def extract_from_compile_commands(self, compile_db_path: str, include_pods: bool = False):
        """Extract AST information from compilation database"""
        print(f"🔍 读取编译数据库: {compile_db_path}")
        
        with open(compile_db_path, 'r') as f:
            compile_db = json.load(f)
        
        print(f"📊 发现 {len(compile_db)} 个编译条目")
        
        processed_files = set()
        for entry in compile_db:
            file_path = entry.get('file', '')
            if not file_path:
                continue
                
            # 跳过第三方库（除非明确包含）
            if '/Pods/' in file_path and not include_pods:
                continue
                
            # 跳过重复文件
            if file_path in processed_files:
                continue
                
            processed_files.add(file_path)
            
            # 处理源文件
            if file_path.endswith(('.m', '.mm', '.h')):
                self.process_source_file(file_path)
        
        print(f"✅ 处理了 {len(processed_files)} 个源文件")
        
    def process_source_file(self, file_path: str):
        """Process a single source file with enhanced analysis"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"[warning] 无法读取文件 {file_path}: {e}")
            return

        # 存储文件内容供扩展关系分析使用
        self.file_contents[file_path] = content
            
        # 创建文件节点
        file_id = sha1(file_path)
        file_node = node_line("File", file_id, {
            "path": file_path,
            "name": Path(file_path).name,
            "type": "source_file"
        })
        self.nodes.add(file_node)
        
        # 提取类定义
        classes = self.extract_classes(content, file_path)
        for class_info in classes:
            class_id = sha1(f"{file_path}::{class_info['name']}")
            class_node = node_line("Class", class_id, class_info)
            self.nodes.add(class_node)
            
            # 文件定义类的关系
            self.edges.add(edge_line("DEFINES", file_id, class_id))
            self.file_to_classes[file_path].append(class_info['name'])
            
            # 提取方法
            methods = self.extract_methods(content, class_info['name'], file_path)
            for method_info in methods:
                method_id = sha1(f"{file_path}::{class_info['name']}::{method_info['name']}")
                method_node = node_line("Method", method_id, method_info)
                self.nodes.add(method_node)
                
                # 类定义方法的关系
                self.edges.add(edge_line("DEFINES", class_id, method_id))
                self.class_to_methods[class_info['name']].append(method_info)
                
            # 提取属性
            properties = self.extract_properties(content, class_info['name'], file_path)
            for prop_info in properties:
                prop_id = sha1(f"{file_path}::{class_info['name']}::{prop_info['name']}")
                prop_node = node_line("Property", prop_id, prop_info)
                self.nodes.add(prop_node)
                
                # 类定义属性的关系
                self.edges.add(edge_line("DEFINES", class_id, prop_id))
                self.class_to_properties[class_info['name']].append(prop_info)
        
        # 提取协议定义
        protocols = self.extract_protocols(content, file_path)
        for protocol_info in protocols:
            protocol_id = sha1(f"{file_path}::protocol::{protocol_info['name']}")
            protocol_node = node_line("Protocol", protocol_id, protocol_info)
            self.nodes.add(protocol_node)
            
            # 文件定义协议的关系
            self.edges.add(edge_line("DEFINES", file_id, protocol_id))
        
        # 提取枚举定义
        enums = self.extract_enums(content, file_path)
        for enum_info in enums:
            enum_id = sha1(f"{file_path}::enum::{enum_info['name']}")
            enum_node = node_line("Enum", enum_id, enum_info)
            self.nodes.add(enum_node)
            
            # 文件定义枚举的关系
            self.edges.add(edge_line("DEFINES", file_id, enum_id))
        
        # 提取方法调用关系
        self.extract_method_calls(content, file_path)
        
        # 提取继承和协议实现关系
        self.extract_inheritance_relationships(content, file_path)
        
    def extract_classes(self, content: str, file_path: str) -> List[Dict]:
        """Extract class definitions from source code"""
        classes = []
        
        # 匹配 @interface 和 @implementation
        interface_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*(?::\s*([A-Za-z_][A-Za-z0-9_]*))?\s*(?:<([^>]+)>)?'
        implementation_pattern = r'@implementation\s+([A-Za-z_][A-Za-z0-9_]*)'
        
        for match in re.finditer(interface_pattern, content, re.MULTILINE):
            class_name = match.group(1)
            superclass = match.group(2) if match.group(2) else None
            protocols = match.group(3).split(',') if match.group(3) else []
            
            classes.append({
                "name": class_name,
                "type": "interface",
                "file": file_path,
                "superclass": superclass,
                "protocols": [p.strip() for p in protocols],
                "line": content[:match.start()].count('\n') + 1
            })
        
        for match in re.finditer(implementation_pattern, content, re.MULTILINE):
            class_name = match.group(1)
            classes.append({
                "name": class_name,
                "type": "implementation", 
                "file": file_path,
                "line": content[:match.start()].count('\n') + 1
            })
        
        return classes
        
    def extract_methods(self, content: str, class_name: str, file_path: str) -> List[Dict]:
        """Extract method definitions with enhanced detection"""
        methods = []
        
        # Objective-C方法模式
        method_pattern = r'^[\s]*([+-])\s*\([^)]+\)\s*([a-zA-Z_][a-zA-Z0-9_]*(?::[a-zA-Z_][a-zA-Z0-9_]*)*)'
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            match = re.match(method_pattern, line.strip())
            if match:
                method_type = match.group(1)  # + 或 -
                method_name = match.group(2)
                
                methods.append({
                    "name": method_name,
                    "type": "instance_method" if method_type == "-" else "class_method",
                    "class": class_name,
                    "file": file_path,
                    "line": i + 1,
                    "signature": line.strip()
                })
        
        return methods
        
    def extract_properties(self, content: str, class_name: str, file_path: str) -> List[Dict]:
        """Extract property definitions"""
        properties = []
        
        property_pattern = r'@property\s*\([^)]*\)\s*([^;]+);'
        
        for match in re.finditer(property_pattern, content):
            property_declaration = match.group(1).strip()
            parts = property_declaration.split()
            
            if len(parts) >= 2:
                property_name = parts[-1].replace('*', '').strip()
                property_type = ' '.join(parts[:-1])
                
                properties.append({
                    "name": property_name,
                    "type": property_type,
                    "class": class_name,
                    "file": file_path,
                    "line": content[:match.start()].count('\n') + 1
                })
        
        return properties
        
    def extract_protocols(self, content: str, file_path: str) -> List[Dict]:
        """Extract protocol definitions"""
        protocols = []
        
        protocol_pattern = r'@protocol\s+([A-Za-z_][A-Za-z0-9_]*)'
        
        for match in re.finditer(protocol_pattern, content):
            protocol_name = match.group(1)
            protocols.append({
                "name": protocol_name,
                "file": file_path,
                "line": content[:match.start()].count('\n') + 1
            })
        
        return protocols
        
    def extract_enums(self, content: str, file_path: str) -> List[Dict]:
        """Extract enum definitions"""
        enums = []
        
        # 匹配各种枚举定义模式
        enum_patterns = [
            r'typedef\s+enum\s*\{[^}]*\}\s*([A-Za-z_][A-Za-z0-9_]*)',
            r'typedef\s+NS_ENUM\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)',
            r'typedef\s+NS_OPTIONS\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)'
        ]
        
        for pattern in enum_patterns:
            for match in re.finditer(pattern, content):
                enum_name = match.group(1)
                enums.append({
                    "name": enum_name,
                    "file": file_path,
                    "line": content[:match.start()].count('\n') + 1
                })
        
        return enums

    def extract_method_definitions_with_positions(self, content: str, file_path: str):
        """Extract method definitions with their line positions for caller context detection"""
        method_definitions = []

        # Objective-C方法定义模式
        method_patterns = [
            # 实例方法: - (returnType)methodName
            r'^-\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_:]*)',
            # 类方法: + (returnType)methodName
            r'^\+\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_:]*)',
        ]

        lines = content.split('\n')
        current_class = None

        # 查找当前类名
        for i, line in enumerate(lines):
            class_match = re.search(r'@implementation\s+([A-Za-z_][A-Za-z0-9_]*)', line)
            if class_match:
                current_class = class_match.group(1)
                break

        # 查找方法定义
        for i, line in enumerate(lines):
            for pattern in method_patterns:
                match = re.search(pattern, line.strip())
                if match:
                    method_name = match.group(1)

                    # 查找方法体的结束位置（简化版本，查找下一个方法或@end）
                    end_line = len(lines)
                    for j in range(i + 1, len(lines)):
                        next_line = lines[j].strip()
                        if (next_line.startswith('-') or next_line.startswith('+') or
                            next_line.startswith('@end')):
                            end_line = j
                            break

                    method_definitions.append({
                        'name': method_name,
                        'class': current_class,
                        'start_line': i + 1,
                        'end_line': end_line,
                        'file': file_path
                    })

        return method_definitions

    def find_containing_method(self, call_line: int, method_definitions: list):
        """Find which method contains a call at the given line number"""
        for method_def in method_definitions:
            if method_def['start_line'] <= call_line <= method_def['end_line']:
                return method_def
        return None

    def extract_method_calls(self, content: str, file_path: str):
        """Extract method call relationships with enhanced detection and proper caller context"""

        # 首先提取所有方法定义及其位置
        method_definitions = self.extract_method_definitions_with_positions(content, file_path)

        # Objective-C方法调用模式
        call_patterns = [
            # [object method] 模式
            r'\[([A-Za-z_][A-Za-z0-9_]*)\s+([A-Za-z_][A-Za-z0-9_:]*)\]',
            # [object method:param] 模式
            r'\[([A-Za-z_][A-Za-z0-9_]*)\s+([A-Za-z_][A-Za-z0-9_]*):',
            # self.property 访问模式
            r'self\.([A-Za-z_][A-Za-z0-9_]*)',
            # 直接方法调用模式
            r'([A-Za-z_][A-Za-z0-9_]*)\s*\(',
        ]

        for pattern in call_patterns:
            for match in re.finditer(pattern, content):
                call_line = content[:match.start()].count('\n') + 1

                # 找到包含此调用的方法
                caller_method = self.find_containing_method(call_line, method_definitions)

                if pattern.startswith(r'\['):
                    # Objective-C消息发送
                    receiver = match.group(1)
                    method = match.group(2)

                    # 创建调用关系，包含调用者信息
                    call_info = {
                        "receiver": receiver,
                        "method": method,
                        "file": file_path,
                        "line": call_line,
                        "type": "objc_message",
                        "caller_method": caller_method  # 新增：调用者方法信息
                    }
                    self.method_calls[file_path].append(call_info)

                elif 'self\\.' in pattern:
                    # 属性访问
                    property_name = match.group(1)
                    call_info = {
                        "property": property_name,
                        "file": file_path,
                        "line": call_line,
                        "type": "property_access",
                        "caller_method": caller_method  # 新增：调用者方法信息
                    }
                    self.method_calls[file_path].append(call_info)

                else:
                    # C函数调用
                    function_name = match.group(1)
                    # 过滤掉一些常见的关键字和系统函数
                    if function_name not in ['if', 'for', 'while', 'switch', 'return', 'sizeof']:
                        call_info = {
                            "function": function_name,
                            "file": file_path,
                            "line": call_line,
                            "type": "function_call",
                            "caller_method": caller_method  # 新增：调用者方法信息
                        }
                        self.method_calls[file_path].append(call_info)

    def extract_inheritance_relationships(self, content: str, file_path: str):
        """Extract inheritance and protocol implementation relationships with proper node management"""

        # 继承关系模式
        inheritance_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*:\s*([A-Za-z_][A-Za-z0-9_]*)'

        for match in re.finditer(inheritance_pattern, content):
            subclass = match.group(1)
            superclass = match.group(2)

            # 确保子类节点存在
            subclass_id = sha1(f"{file_path}::{subclass}")

            # 确保超类节点存在
            superclass_id = sha1(f"virtual_class::{superclass}")
            superclass_node = node_line("Class", superclass_id, {
                "name": superclass,
                "file": "external",
                "virtual": True,
                "type": "superclass"
            })
            self.nodes.add(superclass_node)

            # 创建继承关系
            self.edges.add(edge_line("INHERITS", subclass_id, superclass_id, {
                "subclass": subclass,
                "superclass": superclass,
                "file": file_path,
                "relationship_type": "inheritance"
            }))

        # 协议实现关系模式
        protocol_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*(?::\s*[A-Za-z_][A-Za-z0-9_]*)?\s*<([^>]+)>'

        for match in re.finditer(protocol_pattern, content):
            class_name = match.group(1)
            protocols_str = match.group(2)
            protocols = [p.strip() for p in protocols_str.split(',')]

            # 确保类节点存在
            class_id = sha1(f"{file_path}::{class_name}")

            for protocol in protocols:
                if protocol:  # 确保协议名不为空
                    # 确保协议节点存在
                    protocol_id = sha1(f"virtual_protocol::{protocol}")
                    protocol_node = node_line("Protocol", protocol_id, {
                        "name": protocol,
                        "file": "external",
                        "virtual": True,
                        "type": "protocol"
                    })
                    self.nodes.add(protocol_node)

                    # 创建协议实现关系
                    self.edges.add(edge_line("IMPLEMENTS", class_id, protocol_id, {
                        "class": class_name,
                        "protocol": protocol,
                        "file": file_path,
                        "relationship_type": "protocol_implementation"
                    }))

    def generate_call_relationships(self):
        """Generate CALLS relationships from extracted method calls with proper node management"""
        print("🔗 生成方法调用关系...")

        call_count = 0
        access_count = 0

        # 创建节点ID映射和已存在节点集合
        existing_node_ids = set()
        node_by_signature = {}  # 按签名索引节点

        # 解析现有节点
        for node_json in self.nodes:
            node_data = json.loads(node_json)
            node_id = node_data["id"]
            attrs = node_data["attrs"]
            existing_node_ids.add(node_id)

            # 为方法和属性创建签名索引
            if node_data["label"] == "Method":
                method_name = attrs.get("name", "")
                class_name = attrs.get("class", "")
                if method_name and class_name:
                    signature = f"{class_name}::{method_name}"
                    node_by_signature[signature] = node_id
            elif node_data["label"] == "Property":
                property_name = attrs.get("name", "")
                class_name = attrs.get("class", "")
                if property_name:
                    if class_name:
                        signature = f"{class_name}::{property_name}"
                    else:
                        signature = f"property::{property_name}"
                    node_by_signature[signature] = node_id

        print(f"📋 已索引 {len(node_by_signature)} 个方法/属性签名")

        for file_path, calls in self.method_calls.items():
            # 获取文件节点ID
            file_id = sha1(file_path)

            for call_info in calls:
                # 获取调用者信息
                caller_method = call_info.get("caller_method")
                caller_id = file_id  # 默认使用文件ID

                # 如果有调用者方法信息，尝试找到对应的方法节点
                if caller_method and caller_method.get("name") and caller_method.get("class"):
                    caller_signature = f"{caller_method['class']}::{caller_method['name']}"
                    if caller_signature in node_by_signature:
                        caller_id = node_by_signature[caller_signature]

                if call_info["type"] == "objc_message":
                    # 处理Objective-C方法调用
                    receiver = call_info["receiver"]
                    method = call_info["method"]

                    # 查找目标方法节点
                    callee_id = None

                    # 尝试多种签名匹配
                    potential_signatures = [
                        f"{receiver}::{method}",           # 完整签名
                        f"*::{method}",                    # 通用方法名
                        method                             # 仅方法名
                    ]

                    for signature in potential_signatures:
                        if signature in node_by_signature:
                            callee_id = node_by_signature[signature]
                            break

                    # 如果找不到，创建虚拟方法节点
                    if callee_id is None:
                        callee_id = sha1(f"virtual_method::{receiver}::{method}")
                        if callee_id not in existing_node_ids:
                            virtual_method = node_line("Method", callee_id, {
                                "name": method,
                                "class": receiver,
                                "file": "external",
                                "virtual": True,
                                "type": "virtual_method"
                            })
                            self.nodes.add(virtual_method)
                            existing_node_ids.add(callee_id)
                            node_by_signature[f"{receiver}::{method}"] = callee_id

                    # 创建CALLS关系 - 使用正确的调用者ID
                    self.edges.add(edge_line("CALLS", caller_id, callee_id, {
                        "receiver": receiver,
                        "method": method,
                        "file": file_path,
                        "line": call_info["line"],
                        "call_type": "objc_message",
                        "caller_method": caller_method.get("name") if caller_method else None,
                        "caller_class": caller_method.get("class") if caller_method else None
                    }))
                    call_count += 1

                elif call_info["type"] == "property_access":
                    # 处理属性访问
                    property_name = call_info["property"]

                    # 查找目标属性节点
                    property_id = None

                    # 尝试多种签名匹配
                    potential_signatures = [
                        f"*::{property_name}",             # 通用属性
                        f"property::{property_name}",      # 属性前缀
                        property_name                      # 仅属性名
                    ]

                    for signature in potential_signatures:
                        if signature in node_by_signature:
                            property_id = node_by_signature[signature]
                            break

                    # 如果找不到，创建虚拟属性节点
                    if property_id is None:
                        property_id = sha1(f"virtual_property::{property_name}")
                        if property_id not in existing_node_ids:
                            virtual_property = node_line("Property", property_id, {
                                "name": property_name,
                                "file": "external",
                                "virtual": True,
                                "type": "virtual_property"
                            })
                            self.nodes.add(virtual_property)
                            existing_node_ids.add(property_id)
                            node_by_signature[f"property::{property_name}"] = property_id

                    # 创建ACCESSES关系 - 使用正确的调用者ID
                    self.edges.add(edge_line("ACCESSES", caller_id, property_id, {
                        "property": property_name,
                        "file": file_path,
                        "line": call_info["line"],
                        "access_type": "property_access",
                        "caller_method": caller_method.get("name") if caller_method else None,
                        "caller_class": caller_method.get("class") if caller_method else None
                    }))
                    access_count += 1

                elif call_info["type"] == "function_call":
                    # 处理C函数调用
                    function_name = call_info["function"]

                    # 创建虚拟函数节点
                    function_id = sha1(f"virtual_function::{function_name}")
                    if function_id not in existing_node_ids:
                        virtual_function = node_line("Method", function_id, {
                            "name": function_name,
                            "file": "external",
                            "virtual": True,
                            "type": "c_function"
                        })
                        self.nodes.add(virtual_function)
                        existing_node_ids.add(function_id)

                    # 创建CALLS关系 - 使用正确的调用者ID
                    self.edges.add(edge_line("CALLS", caller_id, function_id, {
                        "function": function_name,
                        "file": file_path,
                        "line": call_info["line"],
                        "call_type": "c_function",
                        "caller_method": caller_method.get("name") if caller_method else None,
                        "caller_class": caller_method.get("class") if caller_method else None
                    }))
                    call_count += 1

        print(f"✅ 生成了 {call_count} 个CALLS关系")
        print(f"✅ 生成了 {access_count} 个ACCESSES关系")

    def generate_inheritance_relationships(self):
        """Generate INHERITS and IMPLEMENTS relationships with proper node management"""
        print("🔗 生成继承和协议实现关系...")

        inherits_count = 0
        implements_count = 0

        # 创建已存在节点集合
        existing_node_ids = set()
        for node_json in self.nodes:
            node_data = json.loads(node_json)
            existing_node_ids.add(node_data["id"])

        # 处理所有继承关系
        for edge_json in list(self.edges):
            edge_data = json.loads(edge_json)
            if edge_data["type"] == "INHERITS":
                src_id = edge_data["src"]
                dst_id = edge_data["dst"]

                # 确保源节点和目标节点都存在
                if src_id not in existing_node_ids:
                    # 创建源类节点
                    attrs = edge_data.get("attrs", {})
                    subclass = attrs.get("subclass", "Unknown")
                    file_path = attrs.get("file", "unknown")

                    src_node = node_line("Class", src_id, {
                        "name": subclass,
                        "file": file_path,
                        "virtual": False
                    })
                    self.nodes.add(src_node)
                    existing_node_ids.add(src_id)

                if dst_id not in existing_node_ids:
                    # 目标节点应该已经在extract_inheritance_relationships中创建
                    pass

                inherits_count += 1

            elif edge_data["type"] == "IMPLEMENTS":
                src_id = edge_data["src"]
                dst_id = edge_data["dst"]

                # 确保源节点和目标节点都存在
                if src_id not in existing_node_ids:
                    # 创建源类节点
                    attrs = edge_data.get("attrs", {})
                    class_name = attrs.get("class", "Unknown")
                    file_path = attrs.get("file", "unknown")

                    src_node = node_line("Class", src_id, {
                        "name": class_name,
                        "file": file_path,
                        "virtual": False
                    })
                    self.nodes.add(src_node)
                    existing_node_ids.add(src_id)

                if dst_id not in existing_node_ids:
                    # 目标节点应该已经在extract_inheritance_relationships中创建
                    pass

                implements_count += 1

        print(f"✅ 生成了 {inherits_count} 个INHERITS关系")
        print(f"✅ 生成了 {implements_count} 个IMPLEMENTS关系")

    def generate_extended_relationships(self):
        """Generate extended relationship types: USES, OVERRIDES, IMPORTS, etc."""
        print("🔗 生成扩展关系类型...")

        uses_count = 0
        overrides_count = 0
        imports_count = 0

        # 创建已存在节点集合
        existing_node_ids = set()
        for node_json in self.nodes:
            node_data = json.loads(node_json)
            existing_node_ids.add(node_data["id"])

        # 处理每个文件的内容来提取扩展关系
        for file_path, content in self.file_contents.items():
            file_id = sha1(file_path)

            # 1. IMPORTS关系 - 检测#import和@import语句
            import_patterns = [
                r'#import\s+[<"]([^>"]+)[>"]',
                r'@import\s+([^;]+);'
            ]

            for pattern in import_patterns:
                for match in re.finditer(pattern, content):
                    imported_file = match.group(1).strip()

                    # 创建虚拟导入文件节点
                    import_id = sha1(f"import::{imported_file}")
                    if import_id not in existing_node_ids:
                        import_node = node_line("File", import_id, {
                            "name": imported_file,
                            "path": imported_file,
                            "virtual": True,
                            "type": "imported_file"
                        })
                        self.nodes.add(import_node)
                        existing_node_ids.add(import_id)

                    # 创建IMPORTS关系
                    self.edges.add(edge_line("IMPORTS", file_id, import_id, {
                        "imported_file": imported_file,
                        "file": file_path,
                        "relationship_type": "file_import"
                    }))
                    imports_count += 1

            # 2. USES关系 - 检测类型使用
            type_usage_patterns = [
                r'(\w+)\s*\*\s*\w+',  # 指针类型使用
                r'<(\w+)>',           # 协议使用
                r':\s*(\w+)',         # 继承中的类型使用
                r'@property\s*\([^)]*\)\s*(\w+)',  # 属性类型
            ]

            for pattern in type_usage_patterns:
                for match in re.finditer(pattern, content):
                    used_type = match.group(1)

                    # 跳过基本类型和关键字
                    if used_type.lower() in ['void', 'int', 'float', 'double', 'bool', 'char', 'id', 'self', 'super']:
                        continue

                    # 创建虚拟类型节点
                    type_id = sha1(f"type::{used_type}")
                    if type_id not in existing_node_ids:
                        type_node = node_line("Class", type_id, {
                            "name": used_type,
                            "file": "external",
                            "virtual": True,
                            "type": "used_type"
                        })
                        self.nodes.add(type_node)
                        existing_node_ids.add(type_id)

                    # 创建USES关系
                    self.edges.add(edge_line("USES", file_id, type_id, {
                        "used_type": used_type,
                        "file": file_path,
                        "relationship_type": "type_usage"
                    }))
                    uses_count += 1

            # 3. OVERRIDES关系 - 检测方法重写
            # 查找方法定义
            method_pattern = r'-\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)'
            methods_in_file = []

            for match in re.finditer(method_pattern, content):
                method_name = match.group(1)
                methods_in_file.append(method_name)

            # 检查是否有继承关系，如果有，检查方法重写
            inheritance_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*:\s*([A-Za-z_][A-Za-z0-9_]*)'
            for match in re.finditer(inheritance_pattern, content):
                subclass = match.group(1)
                superclass = match.group(2)

                # 为每个方法检查是否可能是重写
                for method_name in methods_in_file:
                    # 创建方法节点ID
                    subclass_method_id = sha1(f"{file_path}::{subclass}::{method_name}")
                    superclass_method_id = sha1(f"virtual_method::{superclass}::{method_name}")

                    # 如果子类方法存在，创建OVERRIDES关系
                    if subclass_method_id in existing_node_ids:
                        # 创建虚拟父类方法节点
                        if superclass_method_id not in existing_node_ids:
                            super_method_node = node_line("Method", superclass_method_id, {
                                "name": method_name,
                                "class": superclass,
                                "file": "external",
                                "virtual": True,
                                "type": "overridden_method"
                            })
                            self.nodes.add(super_method_node)
                            existing_node_ids.add(superclass_method_id)

                        # 创建OVERRIDES关系
                        self.edges.add(edge_line("OVERRIDES", subclass_method_id, superclass_method_id, {
                            "method": method_name,
                            "subclass": subclass,
                            "superclass": superclass,
                            "file": file_path,
                            "relationship_type": "method_override"
                        }))
                        overrides_count += 1

        print(f"✅ 生成了 {imports_count} 个IMPORTS关系")
        print(f"✅ 生成了 {uses_count} 个USES关系")
        print(f"✅ 生成了 {overrides_count} 个OVERRIDES关系")

    def save_results(self):
        """Save nodes and edges to output files"""
        nodes_file = self.output_dir / "nodes.jsonl"
        edges_file = self.output_dir / "edges.jsonl"

        print(f"💾 保存结果到 {self.output_dir}")

        with open(nodes_file, 'w') as f:
            for node in self.nodes:
                f.write(node + '\n')

        with open(edges_file, 'w') as f:
            for edge in self.edges:
                f.write(edge + '\n')

        print(f"📊 生成统计:")
        print(f"  节点数量: {len(self.nodes)}")
        print(f"  关系数量: {len(self.edges)}")

        # 统计关系类型
        edge_types = defaultdict(int)
        for edge_json in self.edges:
            edge_data = json.loads(edge_json)
            edge_types[edge_data["type"]] += 1

        print(f"📈 关系类型分布:")
        for edge_type, count in sorted(edge_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  {edge_type}: {count}")

def main():
    parser = argparse.ArgumentParser(description="Enhanced AST Extractor for iOS/Objective-C")
    parser.add_argument("compile_db", help="Path to compile_commands.json")
    parser.add_argument("--out", default="enhanced_ast_out", help="Output directory")
    parser.add_argument("--include-pods", action="store_true", help="Include Pods files")

    args = parser.parse_args()

    print("🚀 启动增强型AST提取器")

    extractor = EnhancedASTExtractor(args.out)
    extractor.extract_from_compile_commands(args.compile_db, args.include_pods)
    extractor.generate_call_relationships()
    extractor.generate_inheritance_relationships()
    extractor.generate_extended_relationships()
    extractor.save_results()

    print("✅ AST提取完成!")

if __name__ == "__main__":
    main()
