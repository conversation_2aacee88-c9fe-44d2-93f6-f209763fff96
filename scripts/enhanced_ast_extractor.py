#!/usr/bin/env python3
"""
Enhanced AST Extractor for iOS/Objective-C Projects

This script provides a hybrid approach to AST extraction that combines:
1. Successful file discovery and basic node generation
2. Enhanced source code analysis for relationship detection
3. Fallback mechanisms when clang parsing fails

The goal is to achieve target relationship counts:
- CALLS relationships: 10,000+ (vs current 159)
- Proper node classification: eliminate 77,070 unclassified nodes
- Enhanced relationship accuracy for INHERITS, IMPLEMENTS, EXTENDS
"""

import json
import re
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
import hashlib
import argparse
from collections import defaultdict

def sha1(text: str) -> str:
    """Generate SHA1 hash for node IDs"""
    return hashlib.sha1(text.encode()).hexdigest()

def node_line(label: str, id_: str, attrs: Dict) -> str:
    """Generate node JSON line"""
    return json.dumps({"label": label, "id": id_, "attrs": attrs}, ensure_ascii=False)

def edge_line(type_: str, src: str, dst: str, attrs: Dict = None) -> str:
    """Generate edge JSON line"""
    return json.dumps({"type": type_, "src": src, "dst": dst, "attrs": attrs or {}}, ensure_ascii=False)

class EnhancedASTExtractor:
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.nodes = set()
        self.edges = set()
        self.file_to_classes = defaultdict(list)
        self.class_to_methods = defaultdict(list)
        self.class_to_properties = defaultdict(list)
        self.method_calls = defaultdict(list)
        
    def extract_from_compile_commands(self, compile_db_path: str, include_pods: bool = False):
        """Extract AST information from compilation database"""
        print(f"🔍 读取编译数据库: {compile_db_path}")
        
        with open(compile_db_path, 'r') as f:
            compile_db = json.load(f)
        
        print(f"📊 发现 {len(compile_db)} 个编译条目")
        
        processed_files = set()
        for entry in compile_db:
            file_path = entry.get('file', '')
            if not file_path:
                continue
                
            # 跳过第三方库（除非明确包含）
            if '/Pods/' in file_path and not include_pods:
                continue
                
            # 跳过重复文件
            if file_path in processed_files:
                continue
                
            processed_files.add(file_path)
            
            # 处理源文件
            if file_path.endswith(('.m', '.mm', '.h')):
                self.process_source_file(file_path)
        
        print(f"✅ 处理了 {len(processed_files)} 个源文件")
        
    def process_source_file(self, file_path: str):
        """Process a single source file with enhanced analysis"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"[warning] 无法读取文件 {file_path}: {e}")
            return
            
        # 创建文件节点
        file_id = sha1(file_path)
        file_node = node_line("File", file_id, {
            "path": file_path,
            "name": Path(file_path).name,
            "type": "source_file"
        })
        self.nodes.add(file_node)
        
        # 提取类定义
        classes = self.extract_classes(content, file_path)
        for class_info in classes:
            class_id = sha1(f"{file_path}::{class_info['name']}")
            class_node = node_line("Class", class_id, class_info)
            self.nodes.add(class_node)
            
            # 文件定义类的关系
            self.edges.add(edge_line("DEFINES", file_id, class_id))
            self.file_to_classes[file_path].append(class_info['name'])
            
            # 提取方法
            methods = self.extract_methods(content, class_info['name'], file_path)
            for method_info in methods:
                method_id = sha1(f"{file_path}::{class_info['name']}::{method_info['name']}")
                method_node = node_line("Method", method_id, method_info)
                self.nodes.add(method_node)
                
                # 类定义方法的关系
                self.edges.add(edge_line("DEFINES", class_id, method_id))
                self.class_to_methods[class_info['name']].append(method_info)
                
            # 提取属性
            properties = self.extract_properties(content, class_info['name'], file_path)
            for prop_info in properties:
                prop_id = sha1(f"{file_path}::{class_info['name']}::{prop_info['name']}")
                prop_node = node_line("Property", prop_id, prop_info)
                self.nodes.add(prop_node)
                
                # 类定义属性的关系
                self.edges.add(edge_line("DEFINES", class_id, prop_id))
                self.class_to_properties[class_info['name']].append(prop_info)
        
        # 提取协议定义
        protocols = self.extract_protocols(content, file_path)
        for protocol_info in protocols:
            protocol_id = sha1(f"{file_path}::protocol::{protocol_info['name']}")
            protocol_node = node_line("Protocol", protocol_id, protocol_info)
            self.nodes.add(protocol_node)
            
            # 文件定义协议的关系
            self.edges.add(edge_line("DEFINES", file_id, protocol_id))
        
        # 提取枚举定义
        enums = self.extract_enums(content, file_path)
        for enum_info in enums:
            enum_id = sha1(f"{file_path}::enum::{enum_info['name']}")
            enum_node = node_line("Enum", enum_id, enum_info)
            self.nodes.add(enum_node)
            
            # 文件定义枚举的关系
            self.edges.add(edge_line("DEFINES", file_id, enum_id))
        
        # 提取方法调用关系
        self.extract_method_calls(content, file_path)
        
        # 提取继承和协议实现关系
        self.extract_inheritance_relationships(content, file_path)
        
    def extract_classes(self, content: str, file_path: str) -> List[Dict]:
        """Extract class definitions from source code"""
        classes = []
        
        # 匹配 @interface 和 @implementation
        interface_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*(?::\s*([A-Za-z_][A-Za-z0-9_]*))?\s*(?:<([^>]+)>)?'
        implementation_pattern = r'@implementation\s+([A-Za-z_][A-Za-z0-9_]*)'
        
        for match in re.finditer(interface_pattern, content, re.MULTILINE):
            class_name = match.group(1)
            superclass = match.group(2) if match.group(2) else None
            protocols = match.group(3).split(',') if match.group(3) else []
            
            classes.append({
                "name": class_name,
                "type": "interface",
                "file": file_path,
                "superclass": superclass,
                "protocols": [p.strip() for p in protocols],
                "line": content[:match.start()].count('\n') + 1
            })
        
        for match in re.finditer(implementation_pattern, content, re.MULTILINE):
            class_name = match.group(1)
            classes.append({
                "name": class_name,
                "type": "implementation", 
                "file": file_path,
                "line": content[:match.start()].count('\n') + 1
            })
        
        return classes
        
    def extract_methods(self, content: str, class_name: str, file_path: str) -> List[Dict]:
        """Extract method definitions with enhanced detection"""
        methods = []
        
        # Objective-C方法模式
        method_pattern = r'^[\s]*([+-])\s*\([^)]+\)\s*([a-zA-Z_][a-zA-Z0-9_]*(?::[a-zA-Z_][a-zA-Z0-9_]*)*)'
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            match = re.match(method_pattern, line.strip())
            if match:
                method_type = match.group(1)  # + 或 -
                method_name = match.group(2)
                
                methods.append({
                    "name": method_name,
                    "type": "instance_method" if method_type == "-" else "class_method",
                    "class": class_name,
                    "file": file_path,
                    "line": i + 1,
                    "signature": line.strip()
                })
        
        return methods
        
    def extract_properties(self, content: str, class_name: str, file_path: str) -> List[Dict]:
        """Extract property definitions"""
        properties = []
        
        property_pattern = r'@property\s*\([^)]*\)\s*([^;]+);'
        
        for match in re.finditer(property_pattern, content):
            property_declaration = match.group(1).strip()
            parts = property_declaration.split()
            
            if len(parts) >= 2:
                property_name = parts[-1].replace('*', '').strip()
                property_type = ' '.join(parts[:-1])
                
                properties.append({
                    "name": property_name,
                    "type": property_type,
                    "class": class_name,
                    "file": file_path,
                    "line": content[:match.start()].count('\n') + 1
                })
        
        return properties
        
    def extract_protocols(self, content: str, file_path: str) -> List[Dict]:
        """Extract protocol definitions"""
        protocols = []
        
        protocol_pattern = r'@protocol\s+([A-Za-z_][A-Za-z0-9_]*)'
        
        for match in re.finditer(protocol_pattern, content):
            protocol_name = match.group(1)
            protocols.append({
                "name": protocol_name,
                "file": file_path,
                "line": content[:match.start()].count('\n') + 1
            })
        
        return protocols
        
    def extract_enums(self, content: str, file_path: str) -> List[Dict]:
        """Extract enum definitions"""
        enums = []
        
        # 匹配各种枚举定义模式
        enum_patterns = [
            r'typedef\s+enum\s*\{[^}]*\}\s*([A-Za-z_][A-Za-z0-9_]*)',
            r'typedef\s+NS_ENUM\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)',
            r'typedef\s+NS_OPTIONS\s*\([^)]*\)\s*([A-Za-z_][A-Za-z0-9_]*)'
        ]
        
        for pattern in enum_patterns:
            for match in re.finditer(pattern, content):
                enum_name = match.group(1)
                enums.append({
                    "name": enum_name,
                    "file": file_path,
                    "line": content[:match.start()].count('\n') + 1
                })
        
        return enums

    def extract_method_calls(self, content: str, file_path: str):
        """Extract method call relationships with enhanced detection"""

        # Objective-C方法调用模式
        call_patterns = [
            # [object method] 模式
            r'\[([A-Za-z_][A-Za-z0-9_]*)\s+([A-Za-z_][A-Za-z0-9_:]*)\]',
            # [object method:param] 模式
            r'\[([A-Za-z_][A-Za-z0-9_]*)\s+([A-Za-z_][A-Za-z0-9_]*):',
            # self.property 访问模式
            r'self\.([A-Za-z_][A-Za-z0-9_]*)',
            # 直接方法调用模式
            r'([A-Za-z_][A-Za-z0-9_]*)\s*\(',
        ]

        for pattern in call_patterns:
            for match in re.finditer(pattern, content):
                if pattern.startswith(r'\['):
                    # Objective-C消息发送
                    receiver = match.group(1)
                    method = match.group(2)

                    # 创建调用关系
                    call_info = {
                        "receiver": receiver,
                        "method": method,
                        "file": file_path,
                        "line": content[:match.start()].count('\n') + 1,
                        "type": "objc_message"
                    }
                    self.method_calls[file_path].append(call_info)

                elif 'self\\.' in pattern:
                    # 属性访问
                    property_name = match.group(1)
                    call_info = {
                        "property": property_name,
                        "file": file_path,
                        "line": content[:match.start()].count('\n') + 1,
                        "type": "property_access"
                    }
                    self.method_calls[file_path].append(call_info)

                else:
                    # C函数调用
                    function_name = match.group(1)
                    # 过滤掉一些常见的关键字和系统函数
                    if function_name not in ['if', 'for', 'while', 'switch', 'return', 'sizeof']:
                        call_info = {
                            "function": function_name,
                            "file": file_path,
                            "line": content[:match.start()].count('\n') + 1,
                            "type": "function_call"
                        }
                        self.method_calls[file_path].append(call_info)

    def extract_inheritance_relationships(self, content: str, file_path: str):
        """Extract inheritance and protocol implementation relationships"""

        # 继承关系模式
        inheritance_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*:\s*([A-Za-z_][A-Za-z0-9_]*)'

        for match in re.finditer(inheritance_pattern, content):
            subclass = match.group(1)
            superclass = match.group(2)

            subclass_id = sha1(f"{file_path}::{subclass}")

            # 创建超类节点（如果不存在）
            superclass_id = sha1(f"class::{superclass}")
            self.nodes.add(node_line("Class", superclass_id, {
                "name": superclass,
                "file": "external",
                "virtual": True
            }))

            self.edges.add(edge_line("INHERITS", subclass_id, superclass_id, {
                "subclass": subclass,
                "superclass": superclass,
                "file": file_path
            }))

        # 协议实现关系模式
        protocol_pattern = r'@interface\s+([A-Za-z_][A-Za-z0-9_]*)\s*(?::\s*[A-Za-z_][A-Za-z0-9_]*)?\s*<([^>]+)>'

        for match in re.finditer(protocol_pattern, content):
            class_name = match.group(1)
            protocols_str = match.group(2)
            protocols = [p.strip() for p in protocols_str.split(',')]

            class_id = sha1(f"{file_path}::{class_name}")

            for protocol in protocols:
                # 创建协议节点（如果不存在）
                protocol_id = sha1(f"protocol::{protocol}")
                self.nodes.add(node_line("Protocol", protocol_id, {
                    "name": protocol,
                    "file": "external",
                    "virtual": True
                }))

                self.edges.add(edge_line("IMPLEMENTS", class_id, protocol_id, {
                    "class": class_name,
                    "protocol": protocol,
                    "file": file_path
                }))

    def generate_call_relationships(self):
        """Generate CALLS relationships from extracted method calls"""
        print("🔗 生成方法调用关系...")

        call_count = 0
        # 创建一个已存在节点ID的集合
        existing_node_ids = set()
        for node_json in self.nodes:
            node_data = json.loads(node_json)
            existing_node_ids.add(node_data["id"])

        for file_path, calls in self.method_calls.items():
            # 获取文件节点ID
            file_id = sha1(file_path)

            for call_info in calls:
                if call_info["type"] == "objc_message":
                    # 查找调用者和被调用者
                    receiver = call_info["receiver"]
                    method = call_info["method"]

                    # 尝试找到真实的方法节点
                    potential_method_ids = [
                        sha1(f"{file_path}::{receiver}::{method}"),  # 同文件中的方法
                        sha1(f"method::{receiver}::{method}"),      # 通用方法ID
                        sha1(f"{method}")                           # 简化方法ID
                    ]

                    callee_id = None
                    for method_id in potential_method_ids:
                        if method_id in existing_node_ids:
                            callee_id = method_id
                            break

                    # 如果找不到目标方法，创建一个虚拟方法节点
                    if callee_id is None:
                        callee_id = sha1(f"virtual_method::{receiver}::{method}")
                        # 添加虚拟方法节点
                        self.nodes.add(node_line("Method", callee_id, {
                            "name": method,
                            "class": receiver,
                            "file": "external",
                            "virtual": True
                        }))
                        existing_node_ids.add(callee_id)

                    # 使用文件节点作为调用者
                    self.edges.add(edge_line("CALLS", file_id, callee_id, {
                        "receiver": receiver,
                        "method": method,
                        "file": file_path,
                        "line": call_info["line"]
                    }))
                    call_count += 1

                elif call_info["type"] == "property_access":
                    # 属性访问关系
                    property_name = call_info["property"]

                    # 尝试找到真实的属性节点
                    potential_property_ids = [
                        sha1(f"{file_path}::{property_name}"),  # 同文件中的属性
                        sha1(f"property::{property_name}")     # 通用属性ID
                    ]

                    property_id = None
                    for prop_id in potential_property_ids:
                        if prop_id in existing_node_ids:
                            property_id = prop_id
                            break

                    # 如果找不到目标属性，创建一个虚拟属性节点
                    if property_id is None:
                        property_id = sha1(f"virtual_property::{property_name}")
                        # 添加虚拟属性节点
                        self.nodes.add(node_line("Property", property_id, {
                            "name": property_name,
                            "file": "external",
                            "virtual": True
                        }))
                        existing_node_ids.add(property_id)

                    # 使用文件节点作为访问者
                    self.edges.add(edge_line("ACCESSES", file_id, property_id, {
                        "property": property_name,
                        "file": file_path,
                        "line": call_info["line"]
                    }))
                    call_count += 1

        print(f"✅ 生成了 {call_count} 个调用关系")

    def save_results(self):
        """Save nodes and edges to output files"""
        nodes_file = self.output_dir / "nodes.jsonl"
        edges_file = self.output_dir / "edges.jsonl"

        print(f"💾 保存结果到 {self.output_dir}")

        with open(nodes_file, 'w') as f:
            for node in self.nodes:
                f.write(node + '\n')

        with open(edges_file, 'w') as f:
            for edge in self.edges:
                f.write(edge + '\n')

        print(f"📊 生成统计:")
        print(f"  节点数量: {len(self.nodes)}")
        print(f"  关系数量: {len(self.edges)}")

        # 统计关系类型
        edge_types = defaultdict(int)
        for edge_json in self.edges:
            edge_data = json.loads(edge_json)
            edge_types[edge_data["type"]] += 1

        print(f"📈 关系类型分布:")
        for edge_type, count in sorted(edge_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  {edge_type}: {count}")

def main():
    parser = argparse.ArgumentParser(description="Enhanced AST Extractor for iOS/Objective-C")
    parser.add_argument("compile_db", help="Path to compile_commands.json")
    parser.add_argument("--out", default="enhanced_ast_out", help="Output directory")
    parser.add_argument("--include-pods", action="store_true", help="Include Pods files")

    args = parser.parse_args()

    print("🚀 启动增强型AST提取器")

    extractor = EnhancedASTExtractor(args.out)
    extractor.extract_from_compile_commands(args.compile_db, args.include_pods)
    extractor.generate_call_relationships()
    extractor.save_results()

    print("✅ AST提取完成!")

if __name__ == "__main__":
    main()
