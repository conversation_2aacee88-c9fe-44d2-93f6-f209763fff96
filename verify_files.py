#!/usr/bin/env python3
"""
验证Neo4j集成所需文件的存在性
"""

import os
from pathlib import Path

def verify_files():
    """验证所有必需文件"""
    print("🔍 验证Neo4j集成文件...")
    print("=" * 50)
    
    # 检查当前工作目录
    cwd = Path.cwd()
    print(f"📁 当前工作目录: {cwd}")
    
    # 必需的文件列表
    required_files = [
        "test_neo4j_connection.py",
        "neo4j_integration/__init__.py",
        "neo4j_integration/main.py",
        "neo4j_integration/config.py",
        "neo4j_integration/connection.py",
        "neo4j_integration/schema.py",
        "neo4j_integration/importer.py",
        "neo4j_integration/validator.py",
        "neo4j_integration/cross_repo_analyzer.py",
        "neo4j_integration/queries.py",
        "neo4j_integration/demo.py",
        "ast_out_index_all/nodes_all.jsonl",
        "ast_out_index_all/edges_all.jsonl",
        "ast_out_index_all/stats_all.json"
    ]
    
    print("\n📋 检查必需文件:")
    all_exist = True
    
    for file_path in required_files:
        full_path = cwd / file_path
        if full_path.exists():
            if full_path.is_file():
                size = full_path.stat().st_size
                size_mb = size / 1024 / 1024
                print(f"  ✅ {file_path} ({size_mb:.1f} MB)")
            else:
                print(f"  ✅ {file_path} (目录)")
        else:
            print(f"  ❌ {file_path} (缺失)")
            all_exist = False
    
    # 检查数据文件大小
    print("\n📊 数据文件详情:")
    data_files = [
        "ast_out_index_all/nodes_all.jsonl",
        "ast_out_index_all/edges_all.jsonl"
    ]
    
    for file_path in data_files:
        full_path = cwd / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            size_mb = size / 1024 / 1024
            
            # 估算行数
            try:
                with open(full_path, 'r') as f:
                    line_count = sum(1 for _ in f)
                print(f"  📄 {file_path}: {size_mb:.1f} MB, ~{line_count:,} 行")
            except Exception as e:
                print(f"  📄 {file_path}: {size_mb:.1f} MB (无法读取行数)")
    
    # 检查Python依赖
    print("\n🐍 检查Python依赖:")
    try:
        import neo4j
        print(f"  ✅ neo4j: {neo4j.__version__}")
    except ImportError:
        print("  ❌ neo4j: 未安装")
        print("     安装命令: pip install neo4j")
        all_exist = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_exist:
        print("✅ 所有文件和依赖都已就绪!")
        print("🚀 可以运行数据导入:")
        print("   python neo4j_integration/main.py")
    else:
        print("❌ 存在缺失的文件或依赖")
        print("请先解决上述问题")
    
    return all_exist

if __name__ == "__main__":
    verify_files()
