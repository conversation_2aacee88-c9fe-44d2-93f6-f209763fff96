//
//  QMWalkCycleTool.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/11/6.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/RouteReqParam.h>
#import <QMapMiddlePlatform/RouteSearcherConstant.h>
#import <QMapNaviKit/QMWalkCycleNaviModuleContext.h>
#import <QMapNaviKit/ExternalDefine.h>
#import "QMWalkNaviModeContext.h"

NS_ASSUME_NONNULL_BEGIN

@class WalkRouteReqParam, CycleRouteReqParam;
@interface QMWalkCycleTool : NSObject

/// 步骑行算路失败后本地生成请求url
/// @param param 算路请求参数
/// @param hostUrl 服务器host地址
+ (NSString *)getReqParamStrUrl:(RouteReqParam *)param hostUrl:(NSString *)hostUrl;

/// 是否是步骑行算路结果类型
/// @param routeResult 算路结果（会有多条方案）
+ (BOOL)isKindOfWalkCycleRouteResultClass:(QRouteResult *)routeResult;

/// 是否是步骑行方案类型
/// @param route 算路方案
+ (BOOL)isKindOfWalkCycleRouteClass:(QRoute *)route;

/// 是否是步骑行请求类型
/// @param param 请求参数
+ (BOOL)isKindOfWalkCycleReqParamClass:(RouteReqParam *)param;

/// 校验请求参数
/// @param param 请求参数
+ (void)sosomapArmedWalkParam:(WalkRouteReqParam *)param;

/// 校验请求参数
/// @param param 请求参数
+ (void)sosomapArmedCycleParam:(CycleRouteReqParam *)param;

/// 生成图片并写入文件
/// @param param 文字
/// @param param 背景图片
/// @param param 文件路径
+ (UIImage*)generateImageFromText:(NSString*) txt bgImage:(UIImage*)bgImage filePath:(NSString*)filePath;

+ (QMCycleSubmode)getLocalCycleSubmode;
+ (void)storeCycleSubmode:(QMCycleSubmode)mode;

+ (kPageScene)pageSceneMultiRouteWithCycleSubmode:(QMCycleSubmode)mode;

+ (kPageScene)pageSceneNaviWithCycleSubmode:(QMCycleSubmode)mode;

+ (QMWalkCycleNaviType)transferRunModeIntoNaviType:(TravelType)travelType arType:(QMWalkNaviModeContextSource)arType;

@end
NS_ASSUME_NONNULL_END
