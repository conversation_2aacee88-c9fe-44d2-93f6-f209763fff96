//
//  hz2py.h
//  ChiToPinyin
//
//  Created by <PERSON> on 12-3-14.
//  Copyright 2012年 Tencent. All rights reserved.
//




#ifdef __cplusplus__
extern "C"
{
#endif

//将utf8编码的字符串中的汉字解成拼音
// in 输入
// out 输出
// first_letter_only 是否只输出拼音首字母
// polyphone_support 是否输出多音字
// add_blank 是否在拼音之间追加空格
// convert_double_char 是否转换全角字符为半角字符
// overage_buff 末尾如果有多余的不能组成完整utf8字符的字节，将写到overage_buff，传NULL将输出到out    
void utf8_to_pinyin(
               char *in, 
               char *out,
               int first_letter_only, 
               int polyphone_support,
               int add_blank,
               int convert_double_char,
               char *overage_buff
               );

int is_utf8_string(char *utf);

    
    
    
#ifdef __cplusplus__
}
#endif