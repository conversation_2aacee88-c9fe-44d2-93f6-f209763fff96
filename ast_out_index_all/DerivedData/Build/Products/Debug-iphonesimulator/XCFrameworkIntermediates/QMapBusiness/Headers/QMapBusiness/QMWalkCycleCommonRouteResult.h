//
//  QMWalkCycleCommonRouteResult.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/13.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <QMapRouteSearchKit/QMapRouteSearchKit.h> 
#import <QMapMiddlePlatform/QMPointInfo.h>
#import <QMapNaviKit/QMWalkCycleBase.h>
#import <QMapMiddlePlatform/TaxiFee.h>
#import "QMWalkRouteProtocol.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString * const kQMNotificationNameWalkCycleRouteResult;
extern NSString * const kQMNotificationUserInfoKeyResult;

@interface QMWalkCycleCommonRouteResult : QRouteResultForTravel <NSCopying> {
    NSArray<QMWalkCycleBase *> *_routes;
}

/**路线，支持多路线，成员类型为QRoute子类**/
@property (nonatomic, strong) NSArray<QMWalkCycleBase *> *routes;

@property (nonatomic, copy) NSArray<TaxiFee *> *taxiFees;
@property (nonatomic, copy) NSString *taxiFeeString;

@property (nonatomic, assign) int error;
@property (nonatomic, assign) int type;
@property (nonatomic, assign) int iErrNo;

@property (nonatomic, copy) NSString *traceId;
@property (nonatomic, copy) NSString *reqRouteUrl;//保存在线路线请求返回的url

@property (nonatomic, assign) DMMapRect dm_routesRect;

@property (nonatomic, assign) DMMapRect dm_indoorRoutesRect;

@property (nonatomic, assign) int routeSubtype;

@property (nonatomic, assign) int elecBicycleSwitchStatus;

/// 是否在骑行路线结果透出建议切换到步行路线
@property (nonatomic, assign) BOOL isToWalk;

/// 骑行切换步行方案索引，默认-1表示没有步行方案透出，端上注意判断数组下标有无溢出
@property (nonatomic, assign) int cycleWalkRouteNo;

@property (nonatomic, copy) NSString *elecBicycleToastText;

@property (nonatomic, strong) NSData *responseData;

@property (nonatomic, strong) WalkRouteReqParam *requestParam;

@property (nonatomic) CLLocationCoordinate2D destCoordinate;

/**
 画路的时候, 需要多画的点
 目前用于驾车导航小结页, 绘制室内外步行路线时, 额外绘制一条从上一个终点到步行起点的衔接线
 */
@property (nonatomic, strong) DMRoutePoint *externPoint;

- (BOOL)hasRoutes;
- (BOOL)hasMultiRoutes;
- (QMWalkCycleBase *)selectedRoute;

- (QMPointInfo *)startPoint;
- (QMPointInfo *)destPoint;

- (DMMapRect)dm_getRoutesRect;

- (DMMapRect)dm_getIndoorRoutesRect;

// 埋点上报各个方案的标签
- (NSDictionary *)tagsStringDic;


// 是否存在需要跳转到步行方案的数据
- (BOOL)needSwitchToWalk;
// 跳转到步行方案的具体index
- (NSInteger)indexOfSwitchToWalk;

// 骑行的当前所选路线是否是步行替代方案
- (BOOL)isToWalkRoute;

@end

NS_ASSUME_NONNULL_END
