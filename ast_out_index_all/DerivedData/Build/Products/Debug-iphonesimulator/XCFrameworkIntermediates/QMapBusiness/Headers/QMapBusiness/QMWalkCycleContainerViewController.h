//
//  QMWalkCycleContainerViewController.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/1/4.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapNaviKit/QMWalkCycleBase.h>
#import <QMapMiddlePlatform/RouteReqParam.h>
#import "QMWalkCycleCommonRouteResult.h"
#import <QMapNaviKit/ExternalDefine.h>
#import <MapBaseOCModel/RGModelInterface.h>
#import <QMapNaviKit/NaviAutoDismissConfirmView.h>
#import <QMapMiddlePlatform/RouteSearcherConstant.h>

typedef NS_ENUM(NSInteger, QMWalkCycleMode) {
    QMWalkCycleModeNormalWalkCycle,  // 直接发起 普通步骑行导航
    QMWalkCycleModeARWalk,           // 直接发起 AR 步导
};


NS_ASSUME_NONNULL_BEGIN

/// 普通步骑VC/AR步导VC 容器
/// 负责普通步导/AR步导切换逻辑
@interface QMWalkCycleContainerViewController : QMViewController


/// 区域面数据，由外部传入
@property (nonatomic, strong) QDestAreaInfoRsp *destAreaInfo;

@property (nonatomic, weak) QRouteResult *walkCycleCommonRouteResult;
/// 动态底图数据
@property (nonatomic, strong) NSDictionary* roundPOIData;

@property (nonatomic, assign) BOOL hasQuery;

@property (nonatomic, assign) BOOL jumpFromSummary;
/// 来源
@property (nonatomic, assign) NavigationSourceFrom sourceFrom;

///  骑行方式
@property (nonatomic, assign) QMCycleSubmode currentCycleMode;

///是否是公交详情页跳转至步骑行导航
@property (nonatomic) BOOL isJumpFromBusDetail;

@property (nonatomic, assign) TravelType travelType;

// 记录是否有出现 驾车强切 步骑行的弹框
@property (nonatomic) NaviAutoDismissConfirmView *autoDissmisConfirmView;


- (instancetype)initWithSearchParameter:(RouteReqParam *)searchParameter
                                   mode:(QMWalkCycleMode)mode;

- (instancetype)initWithRoute:(nullable QMWalkCycleBase *)naviRoute
              searchParameter:(RouteReqParam *)searchParameter
                         mode:(QMWalkCycleMode)mode;

- (instancetype)initWithRoute:(nullable QMWalkCycleBase *)naviRoute
              searchParameter:(RouteReqParam *)searchParameter
                         mode:(QMWalkCycleMode)mode
               dialogInfoType:(RGDialogInfoType)infoType;

@end

NS_ASSUME_NONNULL_END
