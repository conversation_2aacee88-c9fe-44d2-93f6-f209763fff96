//
//  QMUserActivityManager.h
//  SOSOMap
//
//  Created by lxy on 2020/3/27.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@class HistoryKeyword;

NS_ASSUME_NONNULL_BEGIN

UIKIT_EXTERN NSString *const QMUserActivitySpotlightSearch;  //sug、历史记录、搜索注册 用于spotlight搜索


UIKIT_EXTERN NSString *const QMPOISearchVCClickSearchType;  //产生一次主搜，及启动，注册activity
UIKIT_EXTERN NSString *const QMPlanCarRoutesType;         //进行过一次驾车路线规划，注册activity
UIKIT_EXTERN NSString *const QMPlanBusRoutesType;        //进行过一次公交路线规划，注册activity
UIKIT_EXTERN NSString *const QMPlanWalkingRoutesType;   //进行过一次步行路线规划，注册activity
UIKIT_EXTERN NSString *const QMPlanCyclingRoutesType;  //进行过一次骑行路线规划，注册activity


UIKIT_EXTERN NSString *const QMUserActivityEventRegister;  //注册Activity 埋点 onUserAction key
UIKIT_EXTERN NSString *const QMUserActivityEventClick;  //点击activity 埋点 onUserAction key


@interface QMUserActivitySpotlightModel : NSObject

@property (nonatomic, assign) BOOL enabled; //热词是否启用
@property (nonatomic, assign) NSInteger version; //更新比对版本号
@property (nonatomic, copy) NSString *url; //下载热词URL

@end

@interface QMUserActivityModel : NSObject

@property (nonatomic, copy) NSString *title;  //标题
@property (nonatomic, copy) NSString *subTitle; //副标题
@property (nonatomic, copy) NSString *url;   //跳转URL
@property (nonatomic, assign) BOOL enabled; //某个activityenable
@property (nonatomic, strong) NSArray *keywords; //搜索关键词

@end

/**
UserActivity siri shortcuts 逻辑
app实例化activity 并注册becomeCurrent -> shortcuts点击回调appdelegate continueUserActivity ->处理跳转

.最低版本需要iOS12以上
.iPhone7以下设备不支持shortcuts
.开发状态下需要打开setting -> delvelop(开发者) -> 打开Display Recent Shortcuts及Display Donations on Lock Screen开关
*/

@interface QMUserActivityManager : NSObject

+ (void)performTaskWhenAppDidBecomeActive;
+ (void)performTaskWhenMapDidDrawOrTimeOut;

/**
 
  配置 userActivity状态用于显示siri shortcuts 云控配置下发  userActivityEnabled   1 开启  0 关闭
  @param activityType QMMWAConfigDataManager+UserActivity 中UIKIT_EXTERN 字符串
  @param responder UIResponder子类，需要进行activity的注册
*/
+ (void)configurationUserActivity:(NSString *)activityType responder:(__kindof UIResponder *)responder;


/**
  添加本地搜索记录搜索词,二期优化需求,将本地搜索词进行注册，启动注册一页，搜索加载更多同时进行注册
  后续优化可通过接口调用批量注册
 */
+ (void)registerSearchHistoryWithSearchType:(NSString *)type;
+ (void)registerSearchHistoryWithSearchType:(NSString *)type historyArray:(NSArray <HistoryKeyword *> *)historyArray;

/**
 
 配置spotlight 搜索
 @param title 标题
 @param subTitle 副标题
 */
+ (void)registerUserActivityForSpotlightSearchType:(NSString *)type title:(NSString *)title subTitle:(nullable NSString *)subTitle;

+ (void)registerUserActivityForSpotlight;

/**
 处理appdelegate continueUserActivity点击代理回调
 */
+ (BOOL)handleContinueUserActivity:(NSUserActivity *)userActivity;



@end


NS_ASSUME_NONNULL_END
