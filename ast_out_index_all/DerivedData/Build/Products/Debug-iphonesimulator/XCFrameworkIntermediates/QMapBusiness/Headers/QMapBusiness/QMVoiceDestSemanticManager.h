//
//  QMVoiceDestSemanticManager.h
//  SOSOMap
//
//  Created by sarah on 2020/8/6.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/QMBaseSemanticManager.h>

@interface QMVoiceDestSemanticManager : QMBaseSemanticManager

@property (nonatomic, weak)id voiceDestProxy;

@property (nonatomic, copy)NSString *curTravelWay;

//NO，没有冲突，继续下面的处理，YES，有冲突
- (BOOL)isInVoiceDestConflict:(QMSemanticData *)semantic;
//是否是发起切换目的地语义
- (BOOL)isVoiceDestIntent:(QMSemanticData *)semantic;
//是否是目的地二次确认语义
- (BOOL)isVoiceDestConfirmIntent:(QMSemanticData *)semantic;
//是否是目的地二次选择语义
- (BOOL)isVoiceDestSelectIntent:(QMSemanticData *)semantic;
//处理切换目的地的语义
- (QMSemanticState)handleVoiceDestIntent:(QMSemanticData *)semantic;
//处理目的地二次确认语义
- (QMSemanticState)handleVoiceDestConfirmIntent:(QMSemanticData *)semantic;
//处理目的地二次选择语义
- (QMSemanticState)handleVoiceDestSelectIntent:(QMSemanticData *)semantic;
@end

