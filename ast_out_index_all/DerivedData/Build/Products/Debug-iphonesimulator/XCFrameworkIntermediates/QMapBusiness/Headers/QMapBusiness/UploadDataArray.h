//
//  UploadReqWUP.m
//  SoSoMap
//
//  Created by we<PERSON><PERSON><PERSON> on 2015/08/25.
//  Copyright (c) 2015 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UploadReqWUP.h"
#import "UploadImage.h"


@interface DataPair : NSObject
@property (nonatomic,strong) UIImage* image; //上传
@property (nonatomic,strong) QMJCE_mapfilestore_SCUploadPicRsp* sCUploadPicRsp;
@end

@protocol UploadDataArrayDelegate;


@interface UploadDataArray : NSObject<UploadImageDelegate>

@property (nonatomic, weak) id<UploadDataArrayDelegate> delegate;

//in
@property (nonatomic,strong) NSArray* dataArray; //上传  UIImage* 的数组

//out
@property (nonatomic,strong) NSMutableArray* outArray; //DataPair 的数组


- (void)startUploadArray;


//取消当前的请求
-(void)cancelReq;

@end

@protocol UploadDataArrayDelegate<NSObject>
@optional
- (void)UploadDataArrayDelegateDidFinish:(UploadDataArray *)item;
- (void)UploadDataArrayDelegate:(UploadDataArray *)item didFailWithError:(NSError *)error;
@end


