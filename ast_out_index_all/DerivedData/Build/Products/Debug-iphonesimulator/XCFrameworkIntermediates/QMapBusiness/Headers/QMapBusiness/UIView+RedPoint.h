//
//  UIView+RedPoint.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/24.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSUInteger, QMViewRedPointPosition){
    QMViewRedPointPositionTopRight,
    QMViewRedPointPositionTopLeft,
    QMViewRedPointPositionBottomRight,
    QMViewRedPointPositionBottomLeft,
    QMViewRedPointPositionCenterLeft,
    QMViewRedPointPositionCenterRight,
};

@interface UIView (RedPoint)

/*
 * 添加红点，默认位置QMViewRedPointPositionTopRight，默认size QMRedPointDefaultSize
 * 可指定位置类型QMViewRedPointPosition，size，或者自己指定中心点
 */
-(void)addRedpoint;
-(void)addRedpoint:(QMViewRedPointPosition)positionType;
-(void)addRedpoint:(QMViewRedPointPosition)positionType withSize:(CGFloat)size;
-(void)addRedpointInPositon:(CGPoint)centerPoint;
-(void)addRedpointInPositon:(CGPoint)centerPoint withSize:(CGFloat)size;

/*
 * 移除红点，如果移除成功返回YES，没有找到红点返回NO
 */
-(BOOL)removeRedPoint;
@end
