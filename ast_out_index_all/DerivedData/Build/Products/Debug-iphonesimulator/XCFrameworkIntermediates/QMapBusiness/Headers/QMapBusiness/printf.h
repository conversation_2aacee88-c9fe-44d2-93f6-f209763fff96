#ifndef __PRINTF_H__
#define __PRINTF_H__

#include <stdlib.h>
#include <string.h>

#if defined(_MSC_VER) && _MSC_VER <= 1900
#define snprintf _snprintf // snprintf 找不到标识符
#endif
extern char s_logbuff[];
int RGSwprintf(unsigned short* s, const unsigned short *fmt, ...);
size_t RGWcslen(const unsigned short *str);
int RGWcscmp(const unsigned short *src, const unsigned short *dst);
unsigned short *RGWcstrstr(const unsigned short*s1, const unsigned short *s2);
/*
 * Copy src to string dst of size siz.  At most siz-1 characters
 * will be copied.  Always NUL terminates (unless siz == 0).
 * Returns strlen(src); if retval >= siz, truncation occurred.
 */
size_t RGWcslcpy(unsigned short *dst, const unsigned short *src, size_t siz);

/*
 * Appends src to string dst of size siz (unlike strncat, siz is the
 * full size of dst, not space left).  At most siz-1 characters
 * will be copied.  Always NUL terminates (unless siz <= strlen(dst)).
 * Returns strlen(src) + MIN(siz, strlen(initial dst)).
 * If retval >= siz, truncation occurred.
 */
size_t RGWcslcat(unsigned short *dst, const unsigned short *src, size_t siz);
void RGWreplace(const unsigned short *src, const unsigned short* sub, const unsigned short *dst, unsigned short* result);

void removeSubstring(unsigned short *s,const unsigned short *toremove);

#define RGVECTOR_DECLARE(TXVectorType,TXElementType)	struct TXVectorType {\
	int capacity;\
	int size;\
	int elementSize;\
	TXElementType *data;\
}

#define RGVECTOR_INIT(v)	do {\
	(v).capacity = 0;\
	(v).size = 0;\
	(v).elementSize = RGVECTOR_ELEMENT_SIZE(v);\
	(v).data = 0;\
} while(0)

#define RGVECTOR_DESTROY(v) do {\
	if ((v).data != 0) {\
		free((v).data);\
		(v).data = 0;\
	}\
} while(0)

#define RGVECTOR_SIZE(v)			((v).size)
#define RGVECTOR_AT(v, index)	((v).data[(index)])
#define RGVECTOR_ELEMENT_SIZE(v)	(sizeof((v).data[0]))

#define RGVECTOR_PUSH_BACK(v, element) do {\
	RGVECTOR_ENSURE((v), (v).size+1);\
	(v).data[(v).size++] = (element);\
} while(0)

#define RGVECTOR_ERASE(v, index) do {\
	memmove((v).data+(index), (v).data+(index)+1, ((v).size-(index)-1) * RGVECTOR_ELEMENT_SIZE(v));\
    -- (v).size;\
} while(0)\

#define RGVECTOR_INSERT(v, index, element) do {\
	RGVECTOR_ENSURE((v), (v).size+1);\
	memmove((v).data+(index)+1, (v).data+(index), ((v).size-(index)) * RGVECTOR_ELEMENT_SIZE(v));\
	(v).data[(index)] = (element);\
	++ (v).size;\
} while(0)

#define RGVECTOR_CLEAR(v) do {\
	(v).size = 0;\
} while(0)
    
#define RGVECTOR_RESIZE(v, aSize) do {\
	RGVECTOR_ENSURE((v), (aSize));\
	(v).size = (aSize);\
} while(0)

#define RGVECTOR_ASSIGN(v, otherV) do {\
    RGVECTOR_RESIZE((v), (otherV).size);\
    memcpy((v).data, (otherV).data, (otherV).size * RGVECTOR_ELEMENT_SIZE(v));\
} while(0)

#define RGVECTOR_ASSIGN_CARRAY(v, a, count) do {\
    RGVECTOR_RESIZE((v), (count));\
    memmove((v).data, (a), (count) * RGVECTOR_ELEMENT_SIZE(v));\
} while(0)

#define RGVECTOR_ENSURE(v, aSize) do {\
	if (aSize >= v.capacity) {\
		int aCapacity = aSize * 2;\
		RGVECTOR_RESERVE(&(v), aCapacity);\
	}\
} while(0)

void RGVECTOR_RESERVE(void *vec, int aCapacity);

// split for c
RGVECTOR_DECLARE(VECSTRING, char*);
int strsplit(const char *str, struct VECSTRING* vec_string, const char *delimiter);

int utf82ucs2(unsigned short* uni, const char* utf, int n);
int ucs22utf8(char *dst, const unsigned short *src, int dstlen);
char *ucs22utf82(char *dst, const unsigned short *src, int dstlen);
unsigned short* utf82ucs22(unsigned short* uni, char* utf, int n);

//防止src是NULL的拷贝
char * SafeStrncpy(char* dest, const char* src, int size);

char *sncatprintf(char *x, int n, char *fmt, ...);
char *strncat2(char *dest, const char *src, int destcnt);
inline char* PrintUcs22Utf8(const unsigned short *ucs2)
{
  memset(s_logbuff, 0, 512);
  return ucs22utf82(s_logbuff, ucs2, 511);
}

#endif // !__PRINTF_H__
