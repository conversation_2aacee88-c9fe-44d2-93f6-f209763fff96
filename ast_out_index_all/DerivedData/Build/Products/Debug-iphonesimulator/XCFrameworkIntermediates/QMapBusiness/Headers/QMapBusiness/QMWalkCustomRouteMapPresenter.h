//
//  QMWalkCustomRouteMapPresenter.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/27.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapNaviKit/QMWalkCycleBase.h>
#import <QMapMiddlePlatform/DMPOIMarker.h>

NS_ASSUME_NONNULL_BEGIN

@class QMWalkCustomRouteMapPresenter;

@protocol QMWalkCustomRouteMapPresenterDelegate <NSObject>
- (void)customRouteMapPresenter:(QMWalkCustomRouteMapPresenter *)presenter didFindPOI:(nullable JsonPOIInfo *)poi image:(nullable UIImage *)image;
@end

@interface QMWalkCustomRouteMapPresenter : NSObject
@property (nonatomic, weak) id<QMWalkCustomRouteMapPresenterDelegate> delegate;
@property (nonatomic, assign) NSInteger draggableIndex;
@property (nonatomic, assign, readonly) BOOL displayEndPointImage;
@property (nonatomic, assign, readonly) BOOL canAddPassPoint;
@property (nonatomic, strong, readonly) DMMarkerElement *endPointmarker;
@property (nonatomic, strong, readonly) DMMarkerElement *startPointmarker;

- (instancetype)initWithMapView:(DMSharedMapView *)mapView;
// 刷新路线：快速算路，简易路线
- (void)refreshMapWithCoors:(NSArray<QRouteCoordinate *> *)coors;
// 刷新路线：完整算路，详细路况
- (void)refreshMapWithRoute:(QMWalkCycleBase *)route;
// 刷新拖拽点
- (void)refreshPassPoints:(NSArray <QMPointInfo *>*)points;
- (void)addPassPointWithCoor:(CLLocationCoordinate2D)coor uid:(nullable NSString *)uid name:(nullable NSString *)name;
- (void)replaceMarker:(DMMarkerElement *)marker withPointInfo:(QMPointInfo *)pointInfo;
- (void)removePassPointWithMarker:(DMMarkerElement *)marker;
- (void)removeStartPointOrEndPoint:(DMMarkerElement *)marker;
- (void)removeCurrentPoint;
- (BOOL)isSelectedMarker:(DMMarkerElement *)marker;
- (NSInteger)indexOfMarker:(DMMarkerElement *)marker;
- (void)updateSelectedMarkerWithCoor:(CLLocationCoordinate2D)coor;
- (NSArray <QMPointInfo *> *)passPoints;
- (void)setDestPoi:(JsonPOIInfo *)destPOI;
- (DMPOIMarker *)passPointMarkerByCoordinate:(CLLocationCoordinate2D)coordinate;
- (NSString *)nameFromMarker:(DMMarkerElement *)marker;
- (void)startGeoCoderWithCoor:(CLLocationCoordinate2D)coordinate;
//  全览
- (void)overViewRoute;
- (void)reverseDMGeo:(DMMapPOI *)mapPOI;
- (void)addStartMarker:(CLLocationCoordinate2D)coor name:(NSString *)name;
- (void)addEndMarkers:(CLLocationCoordinate2D)coor uid:(nullable NSString *)uid name:(nullable NSString *)name image:(nullable UIImage *)image;
- (void)resetMarkerName:(NSString *)name targetMarker:(DMMarkerElement *)marker;
- (void)resetMarkerNormalStatus;
- (void)setDisplayEndPointImage:(BOOL)displayEndPointImage needRefresh:(BOOL)needRefresh;
@end

NS_ASSUME_NONNULL_END
