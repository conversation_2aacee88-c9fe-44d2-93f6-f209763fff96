//
//  QMWalkCustomRouteMapProvider.h
//  QMapBusiness
//
//  Created by zhangjiyang on 2025/5/22.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapNaviKit/QMWalkCycleBase.h>
#import <QMapMiddlePlatform/DMPOIMarker.h>

NS_ASSUME_NONNULL_BEGIN

@class QTextIconStyle;

@interface QMWalkCustomRouteMapProvider : NSObject
@property (nonatomic, strong, readonly) DMMarkerElement *endPointmarker;
@property (nonatomic, strong, readonly) DMMarkerElement *endTextMarker;
@property (nonatomic, strong, readonly) DMMarkerElement *endLocationMarker;
@property (nonatomic, strong, readonly) DMMarkerElement *startPointmarker;
@property (nonatomic, strong, readonly) DMMarkerElement *startTextMarker;
@property (nonatomic, strong, readonly) DMMarkerElement *startLocationMarker;
@property (nonatomic, strong, readonly) DMPolygon *destPolygon;
- (instancetype)initWithMapView:(DMSharedMapView *)mapView;

- (NSArray <DMMarkerElement *>*)startPointsMarkersWithCoor:(CLLocationCoordinate2D)coor name:(NSString *)name;
- (DMTextureRoute *)carDMRouteElementWithResult:(QMWalkCycleBase *)result;
- (DMMarkerElement *)endPointMarkersWithCoor:(CLLocationCoordinate2D)coor uid:(NSString *)uid;
- (NSArray <DMMarkerElement *>*)endPointMarkersWithCoor:(CLLocationCoordinate2D)coor name:(NSString *)name image:(UIImage *)image;
- (NSArray<DMRoutePoint *> *)generateQRoutePoint:(NSArray<QRouteCoordinate *> *)coors;
- (DMPOIMarker *)createMarkerWithCoor:(CLLocationCoordinate2D)coor;
- (DMGroupMarker *)textMarkerWithCoordinate:(CLLocationCoordinate2D)coor size:(CGSize)size text:(NSString *)text;
- (QTextIconStyle *)getSubTitleStyle:(BOOL)nightMode;
- (DMTextureRoute *)textureRouteByPoints:(NSArray <DMRoutePoint *>*)points;
- (void)setDestPoi:(JsonPOIInfo *)destPOI;
@end

NS_ASSUME_NONNULL_END
