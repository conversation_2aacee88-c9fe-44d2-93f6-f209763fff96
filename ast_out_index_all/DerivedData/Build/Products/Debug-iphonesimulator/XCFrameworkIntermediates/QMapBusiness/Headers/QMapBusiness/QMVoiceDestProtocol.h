//
//  QMVoiceDestProtocol.h
//  SOSOMap
//
//  Created by sarah on 2020/8/12.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMSemanticData.h>
#import <QMapNaviKit/QMNaviVoiceSearchType.h>

@protocol QMVoiceDestProtocol <NSObject>

- (BOOL)isInVoiceDestIntentProcess;

- (void)voiceDestRetry;

- (void)searchDestPOIByVoice:(NSString *)keyWord semantic:(QMSemanticData *)semantic type:(NaviVoiceSearchType)type;
- (void)searchNaviDestNearByPOIByVoice:(NSString *)keyWord withCoor:(CLLocationCoordinate2D)coor semantic:(QMSemanticData *)semantic;
//-1,无效;0,家;1,公司
- (void)goHomeCompanyByVoice:(int)isHome;

- (void)searchRouteByVoiceForDestAtCurSelectPOI;

- (void)searchRouteByVoiceForDestAtIndex:(NSInteger)index;

- (void)cancelSelectPOIByVoiceDest;

- (void)voiceDestDingdingSpeak:(NSString *)speak force:(BOOL)force;

@end
