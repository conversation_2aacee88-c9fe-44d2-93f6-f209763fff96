//
//  TrafficRadioRouteSearch+Reserve.h
//  SOSOMap--DEBUG
//
//  Created by Bruce<PERSON><PERSON> on 2017/6/14.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <QMapNaviKit/TrafficRadioRouteSearch.h>
#include "RouteSearch.h"
@class QMJCE_routesearch_WalkRoute;
@class QMJCE_routesearch_SimpleXPInfo;
@class QMJCE_routesearch_Info;
@class QMJCE_routesearch_Taxi;
@class RouteSet, QMWalkCycleCommonRouteResult, BusRouteResult;

//APP路线协议解析car
@interface TrafficRadioRouteSearch (Reserve)

+ (NSMutableArray *)parseAppCarResponse:(routesearch::CarRouteRsp)caresponse;

+ (void)parseAppCarRoute:(routesearch::CarRoute)caroute resultArr:(NSMutableArray *)resultArr infoDic:(NSDictionary *)infoDic;

+ (void)parseAppTaxiFee:(routesearch::Taxi)taxiresp saveWalkCycleResult:(QMWalkCycleCommonRouteResult *)result;
+ (void)parseAppTaxiFee:(routesearch::Taxi)taxiresp saveBusResult:(BusRouteResult *)result;
+ (NSMutableDictionary *)parseAppInfo:(routesearch::Info) info;
+ (NSMutableDictionary *)parseAppStreetInfo:(routesearch::SimpleXPInfo)xpinfo;

+ (NSMutableDictionary *)parseAppOneWalkRoute:(routesearch::WalkRoute)walkroute;
+ (NSMutableDictionary *)parseAppOneCycleRoute:(routesearch::WalkRoute)cycleroute;
+ (NSDictionary *)parseAppLayer:(routesearch::CarSegmentLayer)layer;
+ (NSDictionary *)parseAppSegment:(routesearch::CarRouteSegment)segment;
+ (NSDictionary *)parseAppTraffic:(routesearch::Traffic)traffic;
+ (NSDictionary *)parseAppReasons:(routesearch::DerouteReason)reason;
+ (NSMutableDictionary *)parseAppWalkRoute:(routesearch::Walk )walk;
+ (NSArray *)parseAppCycleMarker1:(QMJCE_routesearch_WalkRoute *)cycleRoute;

///  解析 xpinfo 为字典
/// @param xpinfo xpinfo 对象
+(NSMutableDictionary *)parseAppStreetInfo1:(QMJCE_routesearch_SimpleXPInfo *)xpinfo;

//oc 步行解析
/// 步行数据解析
/// @param walkroute 步行数据
+ (NSMutableDictionary *)parseAppOneWalkRoute1:(QMJCE_routesearch_WalkRoute *)walkroute;


+ (NSMutableDictionary *)parseAppInfo1:(QMJCE_routesearch_Info *) info;
/// 骑行导航，TODO
/// @param cycleroute 骑行导航数据
//+ (NSMutableDictionary *)parseAppOneCycleRoute1:(QMJCE_routesearch_WalkRoute *)cycleroute;

+ (void)parseAppTaxiFee1:(QMJCE_routesearch_Taxi *)taxiresp saveWalkCycleResult:(QMWalkCycleCommonRouteResult *)result;

+ (NSMutableDictionary *)parseAppOneCycleRoute1:(QMJCE_routesearch_WalkRoute *)cycleroute;

@end

