//
//  QMWalkCycleBaseRouteViewController.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/13.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import "QMRouteDetailViewController.h"
#import "QMRouteSearchTool.h"
#import "QMRouteCMContext.h"
#import "QMRouteCMManager.h"
#import "QMMultiRoutesView.h"
#import <QMapMiddlePlatform/QMLocationButtonManager.h>
#import "QMNMapElementPresenter.h"
#import <QMapMiddlePlatform/QMRouteExplainContext.h>
#import "QMNDynMapAroundPOI.h"
#import "QMCycleRouteOptionView.h"
#import <QMapMiddlePlatform/QMUibutton.h>

NS_ASSUME_NONNULL_BEGIN

static const float kVisibleMapRectEdgeVerticalMargin = 36;

@class AroundPOIParam, QRouteReqParam, RouteReqParam, QMWalkCycleCommonRouteResult, QMNDynMapAroundPOI, 
       QMWalkCycleBaseRoutePresenter,QMRouteSearchRequestParam, QMRoutePlanOption, QMWalkCycleBase, QMWalkCycleRouteDetailView;

@class QMNReverseGeoCoder, QMMidwayMarkerPresenter, QMAlongwayChargingStationView;

// 步骑多方案页父类
@interface QMWalkCycleBaseRouteViewController : QMRouteDetailViewController

@property (nonatomic, strong) QMNDynMapAroundPOI *roundPOI;
@property (nonatomic, strong, nullable) QMWalkCycleCommonRouteResult *searchResult;

// 底图改造后，路线绘制工具
@property (nonatomic, strong) QMWalkCycleBaseRoutePresenter *routePresenter;

@property (nonatomic, strong) QMWalkCycleRouteDetailView *detailView;
@property (nonatomic, strong, nullable) RouteReqParam *searchParam;

// 底图改造后，路线绘制工具
@property (nonatomic, strong) QMNMapElementPresenter *qmnMapPresenter;

@property (nonatomic, strong) QMTipViewDirector *tipsDirector;
/// 偏好设置view
@property (nonatomic, strong, nullable) QMCycleRouteOptionView *preferOptionLayer;
/// 顶部关闭偏好设置按钮
@property (nonatomic, strong) QMUibutton *topCancelBtn;

@property (nonatomic, strong) QMMultiRoutesView *mapControlView;

@property (nonatomic, assign) NSInteger requestCount;//请求计数，只有步行里用到
@property (nonatomic, assign) NSInteger succeedCount;//请求成功计数，只有步行里用到
@property (nonatomic, assign) NSInteger onlineSucCount;//在线请求成功计数
@property (nonatomic, strong) NSDictionary *query;
@property (nonatomic, strong) NSDate *enterTime;
@property (nonatomic, assign) BOOL handleRefresh;
@property (nonatomic, assign) NSTimeInterval firstRouteFinishTime;  //第一次算路请求结束时记录时间

@property (nonatomic, strong) QMRouteCMManager *componentManager;
@property (nonatomic, strong) QMRouteCMContext *componentContext;

//标记是否挪过底图，挪过底图在解释性tips关闭时不调整视野
@property (nonatomic, assign) BOOL hasTappedMapView;

@property (nonatomic, strong, nullable) QMNReverseGeoCoder *qmnGeoCoder;
@property (nonatomic, strong, nullable) QMMidwayMarkerPresenter *deletePassMarkerPresenter;
@property (nonatomic, strong, nullable) QMMidwayMarkerPresenter *pressMarkerPresenter;
@property (nonatomic, strong, nullable) QMAlongwayChargingStationView *alongWayConfirmView;

@property (nonatomic, assign, readonly) UIEdgeInsets mapViewVisibleEdgeInsets;

// 开放给子类调用
- (void)startNavigation;

- (void)reloadContent;

- (UIEdgeInsets)visibleMapRectEdge;

- (void)playTTSAfterRoutePlanFailedIfNeed;

// 供子类重写方法，最好先调用下父类
/// 应用即将进入前台
- (void)applicationWillEnterForeground;

/// 应用已经进入后台
- (void)applicationDidEnterBackground;

- (void)freshScaleViewPos;

/// 开放点击AR导航接口给子类直接调用
- (void)detailViewDidClickARButton;
///在这个时机增加蒙层
- (void)showDetailView;

/// 子类可继承
- (void)didDisplayDetailViewFirstTime;

- (void)trackDetailViewDisplayEventWithResult:(nullable QMWalkCycleCommonRouteResult *)routeResult
                                 keyGenerator:(NSString * _Nonnull (^ _Nonnull)(void))keyGenerator
                                 paramBuilder:(NSDictionary * _Nonnull (^ _Nonnull)(QMWalkCycleBase * _Nonnull route,
                                                                                    NSInteger routeIndex))paramBuilder;

/// 增加掉帧监控
- (void)showLoadingView;

- (void)hideLoadingView;

/// 解释性开放接口

- (QMRouteNaviScene)routeNaviScene;
- (BOOL)showTip:(QMTipView *)tipView;
- (void)hideTip:(QMTipView *)tipView;
- (BOOL)showTip:(QMTipView *)tip   dismissAfterDelay:(NSTimeInterval)delay dismissBlock:(void(^)(void))dismissBlock;

// 计算并更新地图显示区域
- (void)updateMapRectWith2DRecover:(BOOL)turnTo2D;

// 根据路线起终点调整底图视野
- (void)updateMapRectForStartEndPoint;

- (void)hideDetailView;

/// 显示所有按钮
- (void)showAllButtons;

/// 隐藏所有按钮
- (void)hideAllButtons;

//当展示偏好面板时隐藏部分按钮
- (void)hideButtonsWhenShowPrefer;

- (void)startRouteRequest:(QMRoutePlanOption *)planOption;
//展示偏好设置面板时重新布局mapControlView的按钮
- (void)layoutWhenPreferShow;

/// 更新MapControl
- (void)updateMapControlLayout;

/// 点击刷新按钮
/// @param sender 可以不传
- (void)handleFreshButton:(id __nullable)sender;

/// 是否可以开启导航
- (BOOL)shouldStartNavigation;

/// 更新路线解释性消息盒子
/// @param count 更新消息盒子数据
/// @param isShow 消息盒子是否展示
- (void)updateMessageBoxBtnCount:(NSInteger)count isShow:(BOOL)isShow;

- (NSDictionary *)reportGetDestInfo;

- (void)setTabState:(NSString *)type;

- (NSString *)generateQQMapJumpUrl;

- (void)showIndoorExceptionView;

- (void)goToIndoorStartPointSearchPage;

- (QMNewLocationButton *)locationButton;

- (void)handleRouteRequestExternalTrigger:(RouteReqParam *)param;

- (void)refreshMapControlViewLayoutWithNormalStatus:(BOOL)normalStatus;

- (void)layoutTopCancelButton;

- (void)routeRequestInternal:(QMRoutePlanOption *)planOption;

- (void)mapControlDidClickCustomRouteButton;

/// 自定义页曝光统计
/// - Parameter source: 拖拽 -- 1多方案页金刚位 -- 2方案详情卡片金刚位 -- 3查路线页金刚位-- 4
- (void)onCustomRouteUserActionWithSource:(NSInteger)source;

@end

NS_ASSUME_NONNULL_END
