//
//  UploadReqWUP.m
//  SoSoMap
//
//  Created by we<PERSON><PERSON><PERSON> on 2015/08/25.
//  Copyright (c) 2015 Tencent. All rights reserved.
//
#import <Foundation/Foundation.h>

@protocol UploadReqWUPDelegate;

@interface UploadReqWUP : NSObject

@property (nonatomic, weak) id<UploadReqWUPDelegate> delegate;

//in
@property (nonatomic,strong) QMJCE_mapfilestore_CSUploadPic *cSUploadPic; //上传
@property (assign)int sequenceIndex;

//out
@property (nonatomic,strong) QMJCE_mapfilestore_SCUploadPicRsp *sCUploadPicRsp; //上传 的 rsp

-(void)start;

//取消当前的请求
-(void)cancelReq;

@end

@protocol UploadReqWUPDelegate<NSObject>
@optional
- (void)UploadReqWUPDidFinish:(UploadReqWUP *)item;
- (void)UploadReqWUPItem:(UploadReqWUP *)item didFailWithError:(NSError *)error;
@end


