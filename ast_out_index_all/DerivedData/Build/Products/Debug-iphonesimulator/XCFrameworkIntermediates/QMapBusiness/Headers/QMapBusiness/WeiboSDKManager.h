//
//  WeiboSDKManager.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/1/11.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface WeiboSDKManager : NSObject

QMDefaultManager_H(WeiboSDKManager)

+ (void)initWhenApplicationDidFinishLaunch;
+ (void)performTaskWhenMapDidDrawOrTimeOut;

- (BOOL)registerAPI;

- (BOOL)isSinaWeiboAvailable;

- (BOOL)handleURL:(NSURL *)url;

- (BOOL)handleOpenUniversalLink:(NSUserActivity *)userActivity;

- (void)sendImageWithTitle:(NSString*)title image:(UIImage *)image url:(NSString *)url;

- (void)sendVideoWithTitle:(NSString*)title video:(NSURL *)video;

@end

NS_ASSUME_NONNULL_END
