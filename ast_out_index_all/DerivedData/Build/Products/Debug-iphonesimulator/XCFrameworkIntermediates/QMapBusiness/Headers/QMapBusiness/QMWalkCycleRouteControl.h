//
//  QMWalkCycleRouteControl.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/4/14.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/QMMultiBtnMenuView.h>


typedef NS_ENUM(NSUInteger, QMMultiBtnMenuBtnType) {
    /// 消息盒子
    QMMultiBtnMenuBtnTypeMsgBox,
    //反馈按钮
    QMMultiBtnMenuBtnTypeFeedback,
    //停车点
    QMMultiBtnMenuBtnTypeParking,
    //自定义路线
    QMMultiBtnMenuBtnTypeCustomRoute
};

@protocol QMWalkCycleRouteControlHelperProtocol <NSObject>

- (void)mapControlDidClickMsgBoxButton;

- (void)mapControlDidClickFeedbackButton;

- (void)mapControlDidClickParkingButton;

- (void)mapControlDidClickCustomRouteButton;

@end

NS_ASSUME_NONNULL_BEGIN

@interface QMWalkCycleRouteControl : QMMultiBtnMenuView


@property (nonatomic, weak) id<QMWalkCycleRouteControlHelperProtocol> delegate;


/// 按钮事件点击
/// @param btn 按钮
- (void)btnDidClick:(UIButton *)btn;

- (void)hideAllBtn;

- (void)showAllBtn;

- (void)updateMessageBoxBtnCount:(NSInteger)count isShow:(BOOL)isShow;

- (void)updateParkingImageWithClick:(BOOL)isClicked isDarkMode:(BOOL)isDarkMode;

@end

NS_ASSUME_NONNULL_END
