/*******************************************************************************
cx_typedef.h

	Date time:	2009-12-09

	Copyright (C) Tencent
	All rights reserved.

*******************************************************************************/
#ifndef _CX_TYPEDEF_H_
#define _CX_TYPEDEF_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef COMMONX_BASETYPES
#define COMMONX_BASETYPES


#ifndef QNULL
#ifdef __cplusplus
#define QNULL    0
#else
#define QNULL    ((QVOID *)0)
#endif
#endif

#undef ASSERT
#ifndef ASSERT
#define ASSERT(exp)
#endif

#ifndef QFALSE
#define QFALSE			0
#endif

#ifndef QTRUE
#define QTRUE			1
#endif

#ifndef IN
#define IN
#endif

#ifndef OUT
#define OUT
#endif

#ifndef OPTIONAL
#define OPTIONAL
#endif

#ifndef CALLBACK
#define CALLBACK
#endif

#ifndef CONST
#define CONST               const
#endif

#ifndef INLINE
#ifdef __cplusplus
#define INLINE				inline
#else
#define INLINE				__inline
#endif
#endif //INLINE

#ifndef	QBYTE
#define	QBYTE	unsigned char
#endif

#ifndef QWORD
#define QWORD	unsigned short
#endif

#ifndef QDWORD
#define QDWORD	unsigned long
#endif

#ifndef QVOID
#define QVOID	void
#endif

#ifndef QBOOL
#define QBOOL unsigned int
#endif

#ifndef QINT
#define QINT int
#endif

#ifndef QUINT
#define QUINT	unsigned int
#endif

#ifndef QUSHORT
#define QUSHORT unsigned short
#endif

#ifndef QSHORT
#define QSHORT short
#endif

#ifndef QLONG
#define QLONG	long
#endif

#ifndef QFLOAT
#define QFLOAT float
#endif

#ifndef QDOUBLE
#define QDOUBLE double
#endif

#ifndef QCHAR
#define QCHAR	char
#endif

#ifndef QWCHAR
#define QWCHAR	unsigned short
#endif

#ifndef	QPBYTE
#define	QPBYTE	unsigned char *
#endif

#define LPCSTR		const QCHAR *
#define LPSTR		QCHAR *

#define LPCWSTR		const QWCHAR *
#define LPWSTR		QWCHAR *

typedef char			        QINT8;
typedef short			        QINT16;
typedef int                     QINT32;
typedef unsigned char	        QUINT8;
typedef unsigned short	        QUINT16;
typedef unsigned int            QUINT32;

typedef unsigned char	        BYTE;
typedef unsigned long	        DWORD;
typedef unsigned short	        WORD;
typedef unsigned char	        UINT8;
typedef signed   char	        INT8;
typedef short			        INT16;
typedef int                     INT32;
typedef unsigned int            UINT32;
typedef unsigned short	        UINT16;


#if defined(_MSC_VER)
typedef __int64 				QINT64;
#else
typedef long long				QINT64;
#endif

#define MEMCPY	cx_memcpy
#define MEMSET	cx_memset
#define SPRINTF	sprintf
#define cx_sprintf	sprintf


#ifndef NULL
#define NULL 0
#endif

#endif //COMMONX_BASETYPES


#ifdef __cplusplus
}
#endif //extern "C"

#endif//_CX_TYPEDEF_H_
