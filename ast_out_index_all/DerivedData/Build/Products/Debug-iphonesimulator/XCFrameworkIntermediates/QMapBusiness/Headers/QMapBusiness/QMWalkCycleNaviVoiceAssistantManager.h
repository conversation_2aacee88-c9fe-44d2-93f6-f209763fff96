//
//  QMWalkCycleNaviVoiceAssistantManager.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "NavigationLogicController.h"
#import <QMapNaviKit/ExternalDefine.h>
#import <QMapMiddlePlatform/QMSemanticDelegate.h>
#import <QMapMiddlePlatform/QMSemanticData.h>
#import <QMapNaviKit/QMNaviVoiceSearchType.h>

typedef NS_ENUM(NSUInteger, kFinishNavLandPage) {
    kFinishNavLandPageDefault, //不弹驾驶得分，回到上一页
    kFinishNavLandPageSummary, //弹驾驶得分
    kFinishNavLandPageDrive,   //不弹驾驶得分，退出进入驾车导航
    kFinishNavLandPageAR,       //不弹驾驶得分，退出进入AR导航
};

typedef NS_ENUM(NSUInteger, kNaviViewType) {
    kNaviViewTypeTopPane,
    kNaviViewTypeSpeedView
};

@protocol QMWalkCycleNaviVoiceAssistantManagerDelegate <NSObject>

- (void)dingdangFeedback;
/// 结束导航
/// @param landPage 下一个页面类型
/// @param exitType 退出类型
- (void)finishWalkCycleNavigation:(kFinishNavLandPage)landPage exitType:(QMNavigationLogicExitType)exitType ;

/// 展示 2D 模式
- (void)show2DOverviewMode;

/// 展示 3D 模式
- (void)show3DBestViewMode;

/// 展示 车头向上/道路朝前 模式
- (void)showRouteFrontMode;

/// 展示 正北向上 模式
- (void)showNorthFrontMode;

/// 继续导航
- (void)continueNavigation;


/// 叮当询问终点名称
- (void)handleAskDestinationName;


/// 强制播报
/// @param completionBlock 播报后回调
- (void)forcePlayTTSWithCompletion:(QMSmartVoiceForcePlayTTSBlock)completionBlock;


/// 获取剩余时间
- (int)getLeftTime;


/// 获取剩余距离
- (int)getLeftDistance;


/// 获取结束导航后的下一页面类型
- (kFinishNavLandPage)getFinishNavLandPage;

/// 调整播报模式，是否静音
/// @param ttsMode 是否静音
- (void)updateTTSMode:(QMapNaviTTSMode)ttsMode;


/// 进入全览状态
- (void)revokeOverViewState;

/// 取消全览状态
- (void)cancelOverViewState;

/// 进入AR导航
- (void)dingdangEnterARNavigation;

/* 此处有些瑕疵
   我这边结合AR业务，对步骑VC重构，打算采用MVVM方案，使用QMWalkCycleNaviVoiceAssistantManager承接原先由步骑VC直接处理的叮当业务逻辑
   但慧杰那边叮当修改目的地需求依然采用MVC的方案，提出BaseNaviViewController基类的方案，且与驾车导航业务有耦合，不好轻易改动
   快速方案是迂回多绕几层，最终还是由QMWalkCycleNaviVoiceAssistantManager经代理迂回调用BaseNaviViewController
 */

#pragma mark - 叮当修改目的地逻辑
- (void)voiceDestRetry;

- (void)searchDestPOIByVoice:(NSString *)keyWord semantic:(QMSemanticData *)semantic type:(NaviVoiceSearchType)type;
//-1,无效;0,家;1,公司
- (void)goHomeCompanyByVoice:(int)isHome;

- (void)searchRouteByVoiceForDestAtCurSelectPOI;

- (void)searchRouteByVoiceForDestAtIndex:(NSInteger)index;

- (void)cancelSelectPOIByVoiceDest;

- (void)voiceDestDingdingSpeak:(NSString *)speak force:(BOOL)force;



@end




/// 步骑导航语音助手管理器
@interface QMWalkCycleNaviVoiceAssistantManager : NSObject  <QMSemanticDelegate>

@property (nonatomic, weak) id<QMWalkCycleNaviVoiceAssistantManagerDelegate> delegate;

@end

