//TrafficRadioProtocolEngine.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 04/16/2014.
//  Copyright (c) 2014年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMCarRouteContext.h"

#define ROUTE_EVENTS @"routeEvents"
#define ALL_EVENTS @"allEvents"
#define TRAFFIC_TIMES @"trafficTimes"
#define TRAFFIC_SEGTIMES @"trafficSegTimes"
#define DYNAMIC_ROUTE_RESULT @"dynamicRouteResult"
#define TrafficRefreshInterval @"trafficRefreshInterval"

#define kRouteID @"routeID"
#define kSegmentIndex @"segmentIndex"
#define kCurX @"kCurX"
#define kCurY @"kCurY"
#define kCoorIndex @"kCoorIndex"
#define TrafficRadioProtocolEngineLatsTrafficSuccessTime @"TrafficRadioProtocolEngineLatsTrafficSuccessTime"

@class GPSUploadDataManager;

@interface TrafficRadioProtocolEngine : NSObject

// RouteEventInfo 的数组,保存路线上的事件
@property (nonatomic, strong) NSMutableArray *routeEventsArray;

//轻导航路况
+ (QMJCE_traffic_AllOnRouteReqBatch *)getBatchTrafficRequestData:(NSArray *)requestArray
                         originRouteId:(NSString *)routeid
                             gpsupload:(GPSUploadDataManager *)gpsUpload
                            naviStatus:(NSInteger)naviStatus
                              lastTime:(NSTimeInterval)lastTime
                               context:(QMCarRouteContext *)context;
    
+ (NSArray *)parseBatchTrafficResponseData:(QMJCE_traffic_AllOnRouteResBatch *)responseData;

+ (NSDictionary*)parseAllOnRouteRes:(NSData*)respData timestampPtr:(int *)pTimestamp;
+ (NSDictionary*)parseAllOnRouteResBatch:(NSData*)batchData timestampPtr:(int *)pTimestamp;

+ (NSDictionary*)parseAllOnRouteRequest:(NSData*)requestData;

@end



