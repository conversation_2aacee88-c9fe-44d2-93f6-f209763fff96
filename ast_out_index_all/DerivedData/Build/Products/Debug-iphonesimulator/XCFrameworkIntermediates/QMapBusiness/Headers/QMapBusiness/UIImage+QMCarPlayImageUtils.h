//
//  UIImage+QMCarPlayImageUtils.h
//  SOSOMap
//
//  Created by z<PERSON><PERSON><PERSON> on 2020/11/19.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapNaviKit/QMNaviRoadEnlargementView.h>

NS_ASSUME_NONNULL_BEGIN

#define QM_CARPLAY_JUNCTION_IMAGE_SIZE CGSizeMake(140, 110)

@interface UIImage (QMCarPlayImageUtils)

/// 缩放图片到 30 * 30
- (UIImage *)qm_resizeToCarPlayManeuverSymbolImage;

/// 缩放图片到 16 * 16
- (UIImage *)qm_resizeToCarPlayManeuverAttributedInstructionVariantsAttachmentImage;

/// 缩放图片到 放大图的尺寸 140 * 110
- (UIImage *)qm_resizeToCarPlayJunctionImage;

/// 水平合并图片，
/// @param images 图片数组
/// @param imageGap 图片水平间隔
+ (UIImage *)qm_mergeImages:(NSArray <UIImage *>*)images imagesGap:(CGFloat)imageGap;

/// 堆叠图片，以一个图片的大小为基准，把所有图片都放在一起，摞起来，
/// @param images 图片数组
+ (UIImage *)qm_stackImages:(NSArray <UIImage *>*)images;

/// 根据 QMNaviRoadEnlargementView 生成一张图片，只针对于普通放大图，4K 放大图由底图单独处理
/// @param enlargementView 放大图的 view
+ (UIImage *)qm_junctionImageWithEnlargeView:(QMNaviRoadEnlargementView *)enlargementView;

@end

NS_ASSUME_NONNULL_END
