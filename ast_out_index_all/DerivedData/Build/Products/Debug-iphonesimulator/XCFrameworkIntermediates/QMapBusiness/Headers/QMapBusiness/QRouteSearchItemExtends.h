//
//  QRouteSearchItemExtends.h
//  SOSOMap
//
//  Created by sarah on 2017/12/13.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/RoutePoint.h>
#import <QMapMiddlePlatform/QMapRouteSearchExtend.h>
#import <QMapMiddlePlatform/QMPointInfo.h>

#define INVALID_ROUTEDIRECTION   (-1)

#define MAXONLINEPASSPOINTCOUNT (15)//在线途径点最大个数
#define MAXOFFLINEPASSPOINTCOUNT (3) //离线途径点最大个数

@interface QRouteSearchItemExtends : NSObject
@end

@interface QRouteTabResponse : NSObject
@property (nonatomic, copy) NSString *tab_id;
@property (nonatomic, copy) NSString *tab_name;
@end

@interface QRouteDefaultTabResponse : NSObject
/// 回包错误码
@property (nonatomic, assign) NSInteger retCode;
/// 回包错误信息
@property (nonatomic, copy) NSString *retMsg;
@property (nonatomic, strong) QRouteTabResponse *default_tab;

@end


@interface QRouteReqParam (sosomap)
@property (nonatomic, copy)QMPointInfo *startPoint;
@property (nonatomic, copy)QMPointInfo *destPoint;
@property (nonatomic, copy)NSArray<QMPointInfo *> *passPoints;
@property (nonatomic, assign)int sref;
@property (nonatomic) NSString *tabType;
- (BOOL)hasPassPOIs;
@end

@interface QDriveRouteReqParam (sosomap)
@property (nonatomic, copy)QMPointInfo *startPoint;
@property (nonatomic, copy)QMPointInfo *destPoint;
@property (nonatomic, copy)NSArray<QMPointInfo *> *passPoints;
@end

@interface QTaxiRouteReqParam (sosomap)

@property (nonatomic, copy) QMPointInfo *taxiStartPoint;
@property (nonatomic, copy) QMPointInfo *taxiDestPoint;
@end

@interface QChauffeurRouteReqParam (sosomap)

@property (nonatomic, copy) QMPointInfo *chauffeurStartPoint;
@property (nonatomic, copy) QMPointInfo *chauffeurDestPoint;
@end

@interface QRideSharingRouteReqParam (sosomap)

@property (nonatomic, copy) QMPointInfo *rideSharingStartPoint;
@property (nonatomic, copy) QMPointInfo *rideSharingDestPoint;

@end

@interface QRoute (sosomap)
@property (nonatomic)BOOL isLocalRoute;
@property (nonatomic, copy) NSString *storedFileName;
@property (nonatomic, copy) NSString *routeID;

- (NSArray<RoutePoint *> *)points;
- (NSArray *)segmentsTest;
- (QMPointInfo *)startPoint;
- (QMPointInfo *)endPoint;
- (DMMapRect)dm_frame;
@end

@interface QRouteForDrive (sosomap)
//默认是INVALID_ROUTEDIRECTION
@property(nonatomic, assign)NSInteger routeDirection;
//默认是0
@property(nonatomic, assign)int distanceToNextCamera;
//默认-1,表示没有电子眼
@property(nonatomic, assign)int nextCamera;
//默认是0
- (NSArray<QRouteStepForDrive *> *)segmentsTest;
- (DMMapRect)rectWithPOI:(DMMapPoint)pt start:(int)startIndex end:(int)endIndex;
- (BOOL)hasSegHints;
- (NSArray<QRouteTipInfo *> *)totalSpeedCameras;
- (NSArray<QRouteTipInfo *> *)totalSeatAreas;
@end

@interface QRouteResult (sosomap)
@property (nonatomic) NSInteger selectedRouteIndex;

- (BOOL)isLocalResult;
- (BOOL)hasRoutes;
- (QRoute *)selectedRoute;
- (QMPointInfo *)startPoint;
- (QMPointInfo *)destPoint;
@end


@interface QRouteResultForDrive (sosomap)
-(BOOL) isStartHintValid;
@end

