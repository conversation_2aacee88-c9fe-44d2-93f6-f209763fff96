/*
 * C/C++ header file
 *
 * $id: rtree.h,v 1.0.0 2010/07/29 10:29:05 kkwang Exp $
 * Copyright (C) 2010 Tencent, <PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Last Modified: 2010/08/01 19:00:31
 */

#ifndef __RTREE_HEAD__
#define __RTREE_HEAD__

#include <stdint.h>

typedef int Id;

/** 点 */
typedef struct _gis_point_t
{
    double x;   ///<< x坐标
    double y;   ///<< y坐标
} gis_point_t;

/** 外接矩形 */
class gis_mbr_t
{

public:  /** public fields */
    double minx;    //<< 最小x
    double miny;    //<< 最小y
    double maxx;    //<< 最大x
    double maxy;    //<< 最大y

    /**
     * @brief 构造函数
     */
    gis_mbr_t();

    /** 
     * @brief 析构函数
     */
    ~gis_mbr_t();

    /** 
     * @brief 清空
     */
    void clear();

    /** 
     * @brief 扩大MBR使之包含新的MBR
     * 
     * @param other 新的MBR
     */
    void enlarge(const gis_mbr_t& other);

    /** 
     * @brief 求扩大MBR后使得其包含other时的面积与当前面积之差
     *
     * @param other 新的MBR
     * 
     * @return 面积变化量
     */
    double area_changed(const gis_mbr_t& other) const;

    /** 
     * @brief 判断两矩形是否有相交
     * 
     * @param other 矩形
     * 
     * @return true 相交
     * @return false 不相交
     */
    bool intersect(const gis_mbr_t& other) const;
};

/** RTREE 结点 */
class gis_rtree_node_t
{

public:

    gis_mbr_t          mbr;        ///<<<此结点的MBR
    gis_rtree_node_t*  parent;     ///<<<父结点
    gis_rtree_node_t*  fc;         ///<<<第一个儿子结点
    gis_rtree_node_t*  nc;         ///<<<下一个兄弟结点
    int                child_num;  ///<<<儿子数
    int                level;      ///<<<所在树的level(叶为0)
    uint64_t           id;         ///<<<对应的id
    bool               sortx;      ///<<<分裂时的排序方式

    /**
     * @brief 构造函数
     */
    gis_rtree_node_t();

    /** 
     * @brief 析构函数
     */
    ~gis_rtree_node_t();

    /** 
     * @brief 添加一个儿子
     * 
     * @param node 儿子结点
     */
    void add_child(gis_rtree_node_t* node);

    /** 
     * @brief 清空结点
     */
    void clear();
};

/** RTREE */
class gis_rtree_t
{
    /** friend for testing */
    friend class RtreeTest;

public:
    /** 最大儿子个数 */
    const static int GIS_RTREE_MAX_CHILD_NUM = 5;

    /**
     * @brief 构造函数
     */
    gis_rtree_t();

    /** 
     * @brief 析构函数
     */
    ~gis_rtree_t();

    /**
     * @brief 往RTREE中加入一个矩形
     *
     * @param mbr 矩形
     * @param id  对应id
     * 
     * @return 0    成功
     * @return 其它 出错
     */
    int add(const gis_mbr_t& mbr, Id id);

    /** 
     * @brief 往RTREE中加入一个点
     * 
     * @param x  点的横坐标
     * @param y  点的纵坐标
     * @param id 点对应的id
     * 
     * @return 0 成功
     * @return 其它 出错
     */
    int add_point(double x, double y, Id id);

    /** 
     * @brief 往RTREE中加入一个点
     * 
     * @param point 点
     * @param id 对应的id
     * 
     * @return 0 成功
     * @return 其它 出错
     */
    int add_point(const gis_point_t& point, Id id);

    /** 
     * @brief 往RTREE中加入一条线
     * 
     * @param points       线(点集)
     * @param point_num    点的个数
     * @param id           对应的id
     * 
     * @return 0 成功
     * @return 其它 出错
     */
    int add_line(const gis_point_t* line, int point_num, Id id);

    /** 
     * @brief 搜索指点区域的结果
     *
     * @param mbr           区域
     * @param result        结果数组
     * @param max_num       最多返回结果数(缓冲区大小)
     * 
     * @return < 0          出错
     * @return >= 0         实际结果数
     *
     * NOTICE:
     *     如果返回结果(ret) > max_num，result里只保留了max_num个结果.
     *     如果需要所有结果，则可以重新用一个长度>=ret的数组传入.
     */
    int search(const gis_mbr_t& mbr, Id* result, int max_num);

    /** 
     * @brief 获取根结点
     *
     * @return 根结点
     */
    gis_rtree_node_t* get_root() const;

private:
    /** 
     * @brief 分配结点
     * 
     * @return 新结点指针
     */
    gis_rtree_node_t* _alloc_node();

    /** 
     * @brief 搜索某个结点的子树的指定区域的结果数
     * 
     * @param node  某个结点指针
     * @param mbr   区域
     * @param result  结果缓冲区
     * @param max_num  最大结果数(缓冲区大小)
     * 
     * @return >= 0 实际结果数
     * @return <  0 出错
     */
    int _search(const gis_rtree_node_t* node, const gis_mbr_t& mbr, Id* result, int max_num);

    /** 块状申请结点数 */
    const static int GIS_RTREE_NODE_BLOCK_NUM = 1024;

    /** 结点块 */
    typedef struct _gis_node_block_t
    {
        _gis_node_block_t*  next;                           ///<< 下一个块指针
        gis_rtree_node_t    node[GIS_RTREE_NODE_BLOCK_NUM]; ///<< 结点块
    } gis_node_block_t;

    gis_node_block_t*  _block;                  ///<<< 当前结点块
    int                _cur_num;                ///<<< 当前结点块中使用结点个数
    gis_rtree_node_t*  _root;                   ///<<< 根结点
    gis_rtree_node_t** _tmp;                    ///<<< 分裂临时缓冲区
    int                _c;                      ///<<< 用于调试
};

#endif /** __RTREE_HEAD__ */


