//
//  UploadReqWUP.m
//  SoSoMap
//
//  Created by we<PERSON><PERSON><PERSON> on 2015/08/25.
//  Copyright (c) 2015 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UploadReqWUP.h"

@protocol UploadImageDelegate;


@interface UploadImage : NSObject<UploadReqWUPDelegate>

@property (nonatomic, weak) id<UploadImageDelegate> delegate;

//in
@property (nonatomic,strong) UIImage* image; //上传
@property (assign)NSUInteger imageIndex;

//out
@property (nonatomic,strong) QMJCE_mapfilestore_SCUploadPicRsp *sCUploadPicRsp; //上传 的 rsp


- (void)startUploadImage;


//取消当前的请求
-(void)cancelReq;

@end

@protocol UploadImageDelegate<NSObject>
@optional
- (void)UploadImageDidFinish:(UploadImage *)item;
- (void)UploadImage:(UploadImage *)item didFailWithError:(NSError *)error;
@end


