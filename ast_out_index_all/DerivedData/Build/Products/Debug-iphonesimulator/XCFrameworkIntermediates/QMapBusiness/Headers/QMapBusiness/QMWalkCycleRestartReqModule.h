//
//  QMWalkCycleRestartReqModule.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/8/1.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@class QMRoutePlanOption, QRouteReqParam;
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMWalkCycleCurrentLocationMonitorType) {
    QMWalkCycleCurrentLocationMonitorTypeNone,       //不监控
    QMWalkCycleCurrentLocationMonitorTypeStart,     //起点 我的位置监控
    QMWalkCycleCurrentLocationMonitorTypeDest,        //终点 我的位置监控
    QMWalkCycleCurrentLocationMonitorTypeListen        //重新发起算路后，监听网络请求回来
};

@protocol QMWalkCycleRestartReqDelegate <NSObject>

- (void)needRestartRouteSearchSilently:(QMRoutePlanOption *)planOption;

- (void)restartMonitorEndReason:(NSString *)reason endTime:(NSTimeInterval)endTime;

@end

@interface QMWalkCycleRestartReqModule : NSObject

@property (nonatomic, weak) id<QMWalkCycleRestartReqDelegate> delegate;

@property (nonatomic, assign) QMWalkCycleCurrentLocationMonitorType monitorType;

@property (nonatomic, assign) BOOL isRetrying;

- (void)registerWithRoutePlanOption:(QMRoutePlanOption *)planOption;

- (void)registerWithRouteReqParam:(QRouteReqParam *)reqParam;

- (void)startTipsMonitorIfNeccessary;

- (void)receiveNewRouteResponse;

- (void)unregister;
@end

NS_ASSUME_NONNULL_END
