//
//  QMVoiceAlongAnywayProtocol.h
//  SOSOMap
//
//  Created by sarah on 2020/9/23.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMSemanticData.h>

@protocol QMVoiceAlongAnywayProtocol <NSObject>

- (void)alongwaySearchByVoice:(NSString *)keyWord semantic:(QMSemanticData *)semantic;
- (void)anywaySearchByVoice:(NSString *)keyWord semantic:(QMSemanticData *)semantic;

- (void)addPassPointByVoiceAtCurSelectPOI;

- (void)addPassPointByVoiceAtIndex:(NSInteger)index;

- (void)cancelAlongAnywayByVoice;

- (void)alongAnywayDingdingSpeak:(NSString *)speak force:(BOOL)force;

@end
