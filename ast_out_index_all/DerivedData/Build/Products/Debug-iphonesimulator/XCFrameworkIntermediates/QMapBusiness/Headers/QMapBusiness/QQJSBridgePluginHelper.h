//
//  QQJSBridgeEngine.h
//  QQMSFContact
//
//  Created by 张晓龙 on 14-1-19.
//
//

#import <Foundation/Foundation.h>
#import "QQJSWebViewPluginEngine.h"
#import <QMapMiddlePlatform/QQJSWebViewProtocol.h>
#import <QMapMiddlePlatform/QQJSWebViewProtocol.h>

#define KMSFAnswerFlag_NeedAnswer  0x1 // 需要server应答的包
#define KMSFAnswerFlag_NetCheck    0x2 // 需要做网络检查的包
#define KMSFAnswerFlag_CmdCheck    0x4 // 需要用seq+cmd判断回包的包
#define KMSFAnswerFlag_All         KMSFAnswerFlag_NeedAnswer|KMSFAnswerFlag_NetCheck|KMSFAnswerFlag_CmdCheck

/* 即通应用部 pangzhang
 * QQJSBridgePluginHelper本身实现了IQQWebViewPlugin的所有接口 因为jsbridge下功能繁多，所以我们将他们分成多个插件，但是他们在解析调用这里其实都是类似的方法 所以用一个统一的父类实现一份代码 这个父类插件本身是没有实现任何方法调用的
 */

@interface QQJSBridgePluginHelper : NSObject <IQQJSWebViewPlugin>
{
    //环境变量
    NSDictionary *_env;
    
}
@property (nonatomic, retain)NSDictionary *env;

@property (nonatomic, weak) id<QQJSWebViewProtocol> webViewController;

- (void)handleCallbackWithQuery:(NSDictionary *)query param:(id)param;

@end
