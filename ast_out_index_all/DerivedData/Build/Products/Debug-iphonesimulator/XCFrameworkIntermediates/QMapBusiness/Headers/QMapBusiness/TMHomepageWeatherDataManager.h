//
//  TMHomepageWeatherDataManager.h
//  TencentTravelService
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/6.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>
#import "QMHomepageWeatherDataModel.h"

@class TMHomapageWeatherInfo;
@class TMHomepageWeatherDataManager;
NS_ASSUME_NONNULL_BEGIN

@protocol TMHomepageWeatherDelegate <NSObject>

- (NSString *)supportWeatherAnimationType;

@optional
- (void)dataManager:(TMHomepageWeatherDataManager *)dataManager weatherInfoDidReceived:(TMHomapageWeatherInfo *)weatherInfo;
- (void)dataManager:(TMHomepageWeatherDataManager *)dataManager weatherInfoLoadError:(NSError *)error;
/**首页左上角天气数据*/
- (void)dataManager:(TMHomepageWeatherDataManager *)dataManager weatherDataDidReceived:(QMHomepageWeatherDataModel *)weatherModel;

@end

typedef NS_ENUM(NSInteger, TMWeatherTipType) {
    TMWeatherTipTypeHomepage,
    TMWeatherTipTypeAccumWater
};

@interface TMHomepageWeatherDataManager : NSObject
@property (nonatomic, weak) id<TMHomepageWeatherDelegate> delegate;

- (instancetype)initWithType:(TMWeatherTipType)type;

/**
 定位点对应的天气信息

 @param location 定位
 */
- (void)requestWeatherInfo:(CLLocation *)location;

/**
 *首页左上角天气接口
 *@param location 位置
 */
- (void)requestWeatherData:(CLLocation *)location;

/**
 *首页天气 判断是否需要显示全屏PAG
 *param
 */
- (NSString *)homepageShouldShowWeatherPag:(QMHomepageWeatherDataModel *)weatherModel;

/**
  根据当前 天气动效变更 & 云控支持 & 本地有动效文件 判断是否需要显示动效

 @param type 精简的天气类型
 @return 需要显示天气动效文件夹的名字，如果返回字符串长度为0表示不需要显示
 */
- (NSString *)shouldShowWeatherAnimation:(QMJCE_MapSSO_WeatherStateSet)type;

/**
  根据当前 天气动效变更 & 阿波罗配置 判断是否需要显示PAG

 @param type 精简的天气类型
 @return 需要显示PAG地址
 */
- (NSString *)pagPathShouldShowWeatherType:(QMJCE_MapSSO_WeatherStateSet)type;

/**
 记录type的天气动效已展示

 @param animationType 天气动画文件夹名字
 */
- (void)weatherAnimationDidShown:(NSString *)animationName;

/**
 判断当天是否首次显示此描述的预警tips

 @param description 预警描述
 @return 是否需要展示
 */
- (BOOL)shouldShowAlarmWeatherTips:(NSString *)description;

/**
 天气预警tips已展示

 @param description 预警描述
 */
- (void)alarmWeatherTipsDidShown:(NSString *)description;

/**
 判断当天是否首次显示此类型的天气预报

 @param weatherState 天气网返回的天气类型
 @return 是否需要展示
 */
- (BOOL)shouldShowForecastWeatherTips:(QMJCE_MapSSO_WeatherStateSet)weatherState;

/**
 weatherState类型的预报tips已经展示

 @param weatherState 天气网返回的天气类型
 */
- (void)forecastWeatherTipsDidShown:(QMJCE_MapSSO_WeatherStateSet)weatherState;
@end

NS_ASSUME_NONNULL_END
