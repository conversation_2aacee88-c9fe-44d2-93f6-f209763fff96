/*
 * C/C++ header file
 *
 * $id: ncs_cameras_crypt.h,v 1.0.0 2014/07/18 14:51:33 mingxingwen Exp $
 * Copyright (C) 2014 Tencent, mingxingwen (<EMAIL>).
 *
 * Last Modified: 2014/07/18 15:13:07
 */

#ifndef __NCS_CAMERAS_CRYPT_HEAD__
#define __NCS_CAMERAS_CRYPT_HEAD__
#include <stdint.h>

typedef unsigned char u_char;
typedef unsigned char u_char_t;
struct ncs_state_t {
    u_char  perm[256];
    u_char  index1;
    u_char  index2;
};

void s_init(struct ncs_state_t *state, const u_char *key, int keylen);
void s_do(struct ncs_state_t *state,
        const u_char *inbuf, u_char *outbuf, int buflen);

#endif /** __NCS_CAMERAS_CRYPT_HEAD__ */


