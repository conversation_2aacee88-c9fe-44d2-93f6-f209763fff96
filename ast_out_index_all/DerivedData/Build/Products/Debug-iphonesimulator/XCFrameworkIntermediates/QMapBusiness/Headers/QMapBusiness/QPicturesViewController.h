//
//  QPicturesViewController.m
//
//
//  Created by we<PERSON><PERSON><PERSON> on 17/4/4.
//  Copyright (c) 2017年 Tencent. All rights reserved.
//


#import <SDWebImage/SDWebImage.h>

@class QPicturesViewController;

typedef NS_ENUM(NSInteger, QMPictureDataType) {
    QMPictureDataTypeNone = 0,           //未知
    QMPictureDataTypeURL = 1,            //网络地址
    QMPictureDataTypeLocal = 2,          //本地
};



@protocol QPicturesDelegate <NSObject>

@optional
- (void)scrollViewDidEndDeceleratingAndPageChanged;
- (void)scrollViewDidScrollAtPageIndex:(NSUInteger)index;
- (void)onSingleTap:(QPicturesViewController*)item;

- (void)picturesViewControllerInteractivePopStarted;
- (void)picturesViewControllerInteractivePopChangedWithProgress:(CGFloat)progress;
- (void)picturesViewControllerInteractivePopEnded;
- (void)picturesViewControllerInteractivePopCancelled;

@end

@interface QPicturesViewController :QMViewController

@property (nonatomic,weak) id<QPicturesDelegate>delegate;
/**
 当前显示页
 */
@property(assign,readonly) NSInteger curPage;
/**
 是否显示圆点指示器
 */
@property (nonatomic, assign) BOOL showIndicator;
/**
 是否忽略长按手势
 */
@property (nonatomic, assign) BOOL ignoreLongPress;
/**
 跳转到图片浏览器的来源
 算路方案详情页->QMImageFromRouteDetail
 检索站点详情页小页卡->QMImageFromRetrievalDetailSmall
 检索站点详情页中页卡图片区->QMImageFromRetrievalDetailMiddle
 检索站点详情页marker气泡->QMImageFromRetrievalDetailGroupMarker
 算路方案详情页图区气泡->QMImageFromRouteDetailGroupMarker
 步行导航页图区气泡->QMImageFromWalkGroupMarker
 骑行导航页图区气泡->QMImageFromCycleGroupMarker
 */
@property (nonatomic, strong) NSString *type;

-(id)initPicturesArrayUrl:(NSArray*)array CurPage:(NSInteger)curpage;
-(id)initPicturesArrayUrl:(NSArray*)array placeholderDictionary:(NSDictionary *)placeholderDictionary CurPage:(NSInteger)curpage;
-(id)initPicturesArrayLocal:(NSArray*)array CurPage:(NSInteger)curpage;
- (void)addBottomView:(NSArray *)array;

// 3张图复用
- (void)setupBottomView:(NSArray *)array;

// 大于等于 3张图片，走新的复用逻辑
- (BOOL)imageArrayCountOverLimit;

@end
