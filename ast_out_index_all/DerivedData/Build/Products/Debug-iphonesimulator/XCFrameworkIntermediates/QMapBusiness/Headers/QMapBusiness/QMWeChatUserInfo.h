//
//  QMWeChatUserInfo.h
//  SOSOMap
//
//  Created by lv wei on 13-11-14.
//  Copyright (c) 2013年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

static NSString* const KWeChatHeadImageName = @"wechat.png";

typedef enum
{
    QWeChatInfoTypeNoFile = 0,
    QWeChatInfoTypeUpdate,
    QWeChatInfoTypeNoNeedUpdate,
    
}QWeChatInfoType;

@interface QMWeChatUserInfo : NSObject

@property(nonatomic, strong)NSString *authCode;
@property(nonatomic, strong)NSString *accessToken;
@property(nonatomic, strong)NSString *openId;
@property(nonatomic, strong)NSString *refreshToken;
@property(nonatomic, assign)NSInteger expiresIn;
@property(nonatomic, assign)double time;

@property(nonatomic, strong)NSString *nickName;
@property(nonatomic, strong)NSString *sex;
@property(nonatomic, strong)NSString *country;
@property(nonatomic, strong)NSString *province;
@property(nonatomic, strong)NSString *city;
@property(nonatomic, strong)NSString *headImageUrl;
@property(nonatomic, strong)UIImage *headImage;
@property(nonatomic, strong)NSString *headImageFullPath;

@property(nonatomic, strong)NSString *qqNickName;
@property(nonatomic, strong)NSString *qqSessionID;
@property(nonatomic, strong)NSString *qqUserID;
@property(nonatomic, strong)NSString *qqHeadImageUrl;
@property(nonatomic, strong)NSString *qqHeadImage;
@property(nonatomic, assign)double qqSessionIDTime;


- (BOOL)isWeChatLogined;

- (void)loadWeChatInfo;
- (void)saveWeChatInfo;
- (QWeChatInfoType)checkWeChatInfoType;

- (void)loadWeChatHeadImage;
- (void)saveWeChatHeadImage;

- (void)loadQQSessionID;
- (void)saveQQSessionID;
- (BOOL)checkQQSessionIDState;

- (void)resetWeChatInfo;
- (void)resetQQInfo;


@end
