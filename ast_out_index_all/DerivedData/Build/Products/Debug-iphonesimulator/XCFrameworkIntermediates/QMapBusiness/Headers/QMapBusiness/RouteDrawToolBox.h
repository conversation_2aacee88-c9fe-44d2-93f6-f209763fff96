//
//  RouteDrawToolBox.h
//  SOSOMap
//
//  Created by sarah on 2017/6/27.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
 
 
  

typedef NS_ENUM(NSInteger, QMWalkCycleRouteStyle) {
    QMWalkCycleRouteStyleDefault,           // 默认样式，步骑行多方案页
    QMWalkCycleRouteStyleForDrive,          // 驾车样式，驾车多方案页绘制的步行样式
    QMWalkCycleRouteStyleForDrivePassPoint, // 驾车样式，驾车多方案页绘制的途经点步行样式
    QMWalkCycleRouteStyleForDriving,        // 驾车行中样式, 行中只绘制室外部分
    QMWalkCycleRouteStyleForDrivingPassPoint, // 驾车样式，行中途经点步行样式
    QMWalkCycleRouteStyleForSummary,        // 小结页样式, 功能基本等同于多方案页样式, 这里提供一个新的类型用于区分来源
};



@class DMMapView;
@class BusRoute;
@class RoutePoint;
@class RouteTrafficInfo;
@class Route;
@class JsonBuslineInfo;
@class QMSceneElement;
@class RouteBusSegment;
@class QMWalkCycleBase;

@interface RouteDrawToolBox : NSObject

// 底图改造后，绘制步行路线
+ (DMRouteElement *)createDMWalkRouteInMap:(DMMapView *)mapView
                                     route:(QMWalkCycleBase *)route
                               isMainRoute:(BOOL)isMainRoute
                                     style:(QMWalkCycleRouteStyle)style;

// 底图改造后，绘制步行室内路线
+ (NSArray<DMRouteElement *> *)createDMWalkIndoorRoutesInMap:(DMMapView *)mapView
                                                       route:(QMWalkCycleBase *)route
                                                       style:(QMWalkCycleRouteStyle)style;

// 底图改造后，绘制步行室内路线
+ (DMTextureRoute *)createWalkIndoorRoutesInMap:(DMMapView *)mapView points:(NSArray<RoutePoint *> *)points;

// 底图改造后，绘制步行室内路线
+ (DMTextureRoute *)createDMWalkIndoorRoutesInMap:(DMMapView *)mapView points:(NSArray<DMRoutePoint *> *)points;

+ (DMRouteElement *)updateDMRouteWithArrData:(NSArray<DMRoutePoint *> *)mpoints
                                     arrLine:(NSArray<DMRouteSection *> *)sections
                                     segment:(RouteBusSegment *)segment
                                     isCross:(BOOL)isCross;

+ (void) showDynamicRoadNameInDMMap:(DMMapView *)mapView routeOverlay:(DMRouteElement *)routeOverlay nameStyle:(DM_MAP_RouteNameStyle *)style;

// 恢复步骑行导航原来用的方法
+ (void)showDynamicRoadNameInMap:(DMMapView *)mapView result:(QRoute *)route nameStyle:(DM_MAP_RouteNameStyle *)style;


@end
