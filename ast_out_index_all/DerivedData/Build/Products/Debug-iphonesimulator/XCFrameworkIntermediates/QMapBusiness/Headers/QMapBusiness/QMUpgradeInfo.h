//
//  QMUpgradeInfo.h
//  QMapBusiness
//
//  Created by allensun on 2021/4/2.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMUpgradePageInfo : NSObject

/// 标题
@property (nullable, nonatomic, strong) NSString *title;

/// 副标题
@property (nullable, nonatomic, strong) NSString *subtitle;

/// 图片
@property (nullable, nonatomic, strong) NSString *image;

/// 缓存后的图片
@property (nullable, nonatomic, strong) UIImage *cachedImage;

/// Lottie
@property (nullable, nonatomic, strong) NSString *lottie;

/// 缓存后的 lottie 路径
@property (nullable, nonatomic, strong) NSString *cachedLottie;

/// 是否合法
@property (nonatomic, assign, readonly) BOOL isValid;

/// 是否缓存完成
@property (nonatomic, assign, readonly, getter=isCached) BOOL cached;

@end

@interface QMUpgradeInfo : NSObject

@property (class, nonatomic, strong, readonly) NSString *cacheDir;

/// Button 标题
@property (nonatomic, strong) NSString *button;

/// 新版本号
@property (nonatomic, strong) NSString *version;

/// 升级 URL
@property (nonatomic, strong) NSString *url;

/// 最大弹窗次数
@property (nonatomic, assign) NSInteger times;

/// 最小弹窗间隔
@property (nonatomic, assign) NSInteger interval;

@property (nonatomic, strong) QMUpgradePageInfo *page1;
@property (nonatomic, strong) QMUpgradePageInfo *page2;
@property (nonatomic, strong) QMUpgradePageInfo *page3;

@property (nonatomic, strong) QMPagePopupItem *popupItem;

/// 配置页数
@property (nonatomic, assign, readonly) NSUInteger numberOfPages;

/// 是否合法
@property (nonatomic, assign, readonly) BOOL isValid;

- (void)loadPages;

@end

NS_ASSUME_NONNULL_END
