//
//  WalkRouteDetailItem.h
//  SOSOMap
//
//  Created by lv wei on 13-8-3.
//  Copyright (c) 2013年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum {
    EWalkRouteDetailTypeNone,
    EWalkRouteDetailTypeStart,
    EWalkRouteDetailTypeEnd,
    EWalkRouteDetailTypeNormal,

}WalkRouteDetailType;


@interface WalkRouteDetailItem : NSObject
@property(nonatomic,assign) WalkRouteDetailType walkRouteDetailType;
@property(nonatomic,strong) NSString* action;
@property(nonatomic,strong) NSString* walkText;
@property(nonatomic,strong) NSString* walkDistance;
@property(nonatomic,strong) UIImage* actionIcon;

@property (nonatomic,assign) BOOL hasStreetView;
@property (nonatomic, strong) NSString * svid;
@property (nonatomic, strong) NSString * svPointx;
@property (nonatomic, strong) NSString * svPointy;

@property (nonatomic, strong)UIImage *icon;
@property (nonatomic, copy)NSString *name;

@end
