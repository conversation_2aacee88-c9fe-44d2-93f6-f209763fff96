//
//  QMWalkCycleNaviSettingViewController.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/6/10.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapNaviKit/ViewEventInfo.h>
#import <QMapNaviKit/ExternalDefine.h>
#import <QMapNaviKit/QMNaviSettingLocalStore.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString * const QMWalkCycleNaviSettingCloseString;
extern NSString * const QMWalkCycleNaviSettingNavigationVoiceString;
extern NSString * const QMWalkCycleNaviSettingTTSModeString;
extern NSString * const QMWalkCycleNaviSettingFeaturedThemeString;
extern NSString * const QMWalkCycleNaviSettingViusalAngleString;
extern NSString * const QMWalkCycleNaviSettingGuideLineString;
extern NSString * const QMWalkCycleSettingDayNightExchangeString;
extern NSString * const QMWalkCycleNaviSettingMusicControlSetting;
extern NSString * const QMWalkCycleNaviSettingDimensionMode;

@class QMWalkCycleNaviSettingViewController;

@protocol QMWalkCycleNaviSettingChangedProtocol <NSObject>

@optional
/*
 * 开关类的cell被点击
 * @param settingController 设置控制器
 * @param title cell的title
 * @param isOn 当前状态
 */
- (void)switchCellTapped:(QMWalkCycleNaviSettingViewController*)settingController cellTitle:(NSString*)cellTitle isOn:(BOOL) isOn;

/*
 * 选择类的cell里的选择项被点击
 * @param settingController 设置控制器
 * @param title cell的title
 * @param isOn 当前状态
 */
- (void)selectedCellTapped:(QMWalkCycleNaviSettingViewController*)settingController cellTitle:(NSString*)cellTitle
    ttsMode:(nullable id<QMNaviSettingStoreProtocol>)ttsMode;

/*
 * 选择类的cell里的选择项被点击
 * @param settingController 设置控制器
 * @param title cell的title
 */
- (void)selectedCellTapped:(QMWalkCycleNaviSettingViewController*)settingController cellTitle:(NSString*) cellTitle;

/*
 * 普通cell被点击
 * @param settingController 设置控制器
 */
- (void)commonCellTapped:(QMWalkCycleNaviSettingViewController*)settingController cellTitle:(NSString*)cellTitle;

- (void)bluetoothChannelChoiceAlertViewShow:(BOOL)isShow confirmChange:(BOOL)confirm;

@end

typedef NS_ENUM(NSUInteger, NavigationUIStyle) {
    NavigationDaylightStyle = 0,
    NavigationNightStyle = 1,
};

@interface QMWalkCycleNaviSettingViewController : QMViewController

- (instancetype)initWithUiStyle:(NavigationUIStyle)uiStyle isNavigating:(BOOL)isNavigating;
    
@property (nonatomic, weak) id<QMWalkCycleNaviSettingChangedProtocol> delegate;

/**语音播报控制*/
@property (nonatomic, strong) id<QMNaviSettingStoreProtocol> settingStore;

/**导航模式*/
@property (nonatomic, assign) NavigationUIStyle UIStyle;
/**eta信息*/
@property (nonatomic, strong) ViewEventInfo *etaInfo;
/** 步 / 骑行导航方式*/
@property (nonatomic, assign) TravelType runMode;

@property (nonatomic) BOOL needTabBar;

@property (nonatomic) NSString *navigationTitle;

- (void)reloadBTChoiceData;

@end

NS_ASSUME_NONNULL_END
