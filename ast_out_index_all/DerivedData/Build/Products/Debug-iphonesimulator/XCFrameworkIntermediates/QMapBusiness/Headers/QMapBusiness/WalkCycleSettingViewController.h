//
//  WalkCycleSettingViewController.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 2017/8/23.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapNaviKit/QMapNaviCommon.h>

extern NSString * const threeDOptionKey;
extern NSString * const SlientModeOptionKey;
extern NSString * const LanguageOptionKey;
extern NSString * const themeSkinOptionKey;

typedef NS_ENUM(NSUInteger, NaviPopupSettingType) {
    WalkNaviPopupSetting,
    CycleNaviPopupSetting,
};

@interface WalkCycleSettingViewController : QMViewController

@property (nonatomic, assign) NaviPopupSettingType settingType;

@property (nonatomic, copy) void(^settingComplete)();

@property (nonatomic, copy) void (^eventCallback)(WalkCycleSettingViewController* viewController,NSDictionary* info);

- (void)updateTTSMode:(QMapNaviTTSMode)ttsMode;

- (void)setFirstAccess;

@end
