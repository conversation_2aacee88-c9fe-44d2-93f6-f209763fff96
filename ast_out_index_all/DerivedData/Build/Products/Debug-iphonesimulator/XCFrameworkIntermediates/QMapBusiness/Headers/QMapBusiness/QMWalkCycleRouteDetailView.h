//
//  QMWalkCycleRouteDetailView.h
//  SOSOMap
//
//  Created by sarah on 2017/7/10.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import "QMMultiRoutesView.h"

#define QMRoutePlanWalkCycleInfo(fmt, ...)  QMLogInfo(@"步骑行-新手教育", fmt, ##__VA_ARGS__)

@protocol QMWalkRouteDetailViewDataSource;
@class QMWalkCycleCommonRouteResult, QMPassthroughScrollView;

@interface QMWalkCycleRouteDetailView : QMMultiRoutesView


/// 掉帧场景监控字段
@property (nonatomic, copy) NSString *blueMonitorKey;

@property (nonatomic, copy) NSString *actionString; //区分骑行还是步行，目前也用于拼接导航栏标题文字
@property (nonatomic, copy) NSString *navCustomTitle;   // 发起导航按钮文字
@property (nonatomic, copy) NSString *arCustomTitle;   // AR导航按钮文字

@property (nonatomic, assign) BOOL needShowBikeShareButton;
@property (nonatomic, assign) BOOL needShowARButtonLeft;

@property (nonatomic) BOOL isFirstRequest;

@property (nonatomic, copy) void(^setHiddenBlock)(BOOL hidden);

@property (nonatomic, readonly) UIView *bottomBarView;  //吸底的开始导航按钮 视图容器
@property (nonatomic, readonly) UIButton *startNaviButton;   //开始导航按钮
@property (nonatomic, weak) UIView *startNaviTipsView;  //开始导航按钮 tips
@property (nonatomic) BOOL hasRecorderStartNaviTipsTime;    //是否已经记录过开始导航 tips 按钮的疲劳度

/// 初始化方法
/// - Parameters:
///   - frame:
///   - show: 是否展示AR按钮
///   - hideAllbuttons: 是否隐藏所有按钮
- (instancetype)initWithFrame:(CGRect)frame
                 showARButton:(BOOL)show
               hideAllButtons:(BOOL)hideAllbuttons;


- (instancetype)initWithFrame:(CGRect)frame showShareBikeButton:(BOOL)show type:(int)type;//传参，是否展示扫码骑车按钮 type:0-自行车 1-电动车

- (void)setRouteResult:(QMWalkCycleCommonRouteResult *)routeResult reqParam:(QRouteReqParam *)reqParam;

- (void)setExpandedEnable:(BOOL)enble expand:(BOOL)expand;

// 设置开始导航按钮的图片
- (void)setStartNavCustomButtonImage:(UIImage *)buttonImage;

// 点击tab切换
- (void)scrollToExpand:(BOOL)expand animate:(BOOL)animate;

- (BOOL)isExpanded;

- (void)setCanShowBottomView:(BOOL)can;

- (void)changeTitleViewAlpha:(CGFloat)alpha;

- (void)setVerticalOffset:(int)offset;

- (void)modifySelectRoute:(NSInteger)newIndex oldIndex:(NSInteger)oldIndex;

/// AR导航按钮是否保持在左边
- (BOOL)isArButtonNeedStayLeft;

- (void)checkEnergyGuideViewShowIfneed;

// 移除cardlist hippy。步骑行切换时用
- (void)removeCardListHippyBundle;
// 刷新cardlist hippy。算路成功后调用，切换方案不刷新。
- (void)refreshHippyListView;

- (void)viewWillAppear;

- (void)freshRouteCollectIcon:(BOOL)isCollected;
- (void)setRouteCollectedId:(NSString *)favoriteId;

@end
