//
//  TMHomapageWeatherInfo.h
//  TencentTravelService
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/7/9.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TMHomapageWeatherInfo : NSObject

/**
 请求定位的城市
 */
@property (nonatomic, copy) NSString *cityName;

/**
 当前天气类型，为精简后的类型
 */
@property (nonatomic, assign) QMJCE_MapSSO_WeatherStateSet weatherType;

/**
 预警信息
 */
@property (nonatomic, copy) NSString *alarmIconUrl;
@property (nonatomic, copy) NSString *alarmDescription;

/**
 预报信息
 */
@property (nonatomic, copy) NSString *forecastIconUrl;
@property (nonatomic, copy) NSString *forecastDescription;
@property (nonatomic, assign) QMJCE_MapSSO_WeatherStateSet weatherStateSet;
@end

NS_ASSUME_NONNULL_END
