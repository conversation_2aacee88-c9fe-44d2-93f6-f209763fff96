#ifndef _GPS_RECORDAPI_H
#define _GPS_RECORDAPI_H

#import <QMapNaviKit/GpsCommonDefine.h>



//-------gps api------
/**
 * 初始化要保存的文件,失败返回-1
 * 当写文件时，成功返回0，当读文件时，成功返回gps数据的条数
 * 当读文件时，如果不是本格式的文件，返回-2
 * @param file 要保存的文件名
 * @param isRead 是否为读文件
 * @param sysFlag 平台标志，100IOS,101android
 */
int initFile(const char *file,bool isRead,int sysFlag,void** pHandle);
/**
 * 添加一个gps数据
 * 失败返回-1，成功返回0
 */
int appendGpsData(void* gpsRecord,const GpsData* data);
/**
 * 取一个gps数据，成功返回0，失败返回-1,已经到文件结束返回1
 */
int getLineGpsData(void* gpsRecord,GpsData* data);

int setRouteid(void* gpsRecord,int size,const char* routeid);

int getRouteidSize(void* gpsRecord);

int getRouteid(void* gpsRecord,char* routeid);

void closeFile(void* gpsRecord);
//------gps api--------

//------------规划路线数据api------------------
int initRouteFile(const char *file,bool isRead,int sysFlag);

int addRouteData(int selectedRoute,const char* routeData,int routeSize,const char* fromPoi,int fromSize,const char* toPoi,int toSize);

int getDataSize(int*select,int* route,int* from,int* to);

int getRouteData(char* routeData,char* fromPoi,char* toPoi);

void closeRouteFile();
//------------规划路线数据api------------------

//------------打分路线api----------------------
int initLineFile(const char *file,bool isRead,int sysFlag);

int appendLinePoint(double lon,double lat);

int appendEvent(int color,int from,int to);

int getOneLinePoint(ScoreLinePoint* point);

int getOneEvent(ScoreLineEvent* event);

void closeLineFile();

//------------打分路线api----------------------

//---------------事件数据api-------------------
/**
 * 读文件时，成功返回整个事件的次数，格式不对返回-2，失败返回-1
 */
int initEventFile(const char *file,bool isRead);
void closeEventFile();
int appendEventResult(int result,int len,int ridLen,const char* rid,double rTime);
int appendEventData(EventData* data);
int getNextEvent(int* len, int* ridLen,double* requestTime);
int getEventRouteId(int len,char* routeid);
int getEventData(EventData* data);
//---------------事件数据api--------------------

#endif
