//
//  QMViewController+Transition.h
//  SOSOMap
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2017/7/17.
//  Copyright © 2017年 Tencent. All rights reserved.
//



@class QMVCTransitionManager;

@interface UIViewController (Transition)

#pragma mark - Animation

/**
 需要进行动画的top rect

 @return 默认是（0，0，self.view.width，64）
 */
- (CGRect)topAnimationRect;

/**
 需要进行动画的bottom rect

 @return 默认是（0，0，self.view.width，self.view.height - 64）
 */
- (CGRect)bottomAnimationRect;


/**
 需要进行动画的bottom EdgeInsets

 @return 默认是(0,0,0,0）
 */
- (UIEdgeInsets)bottomAnimationEdgeInsets;


/**
  Top是否需要进行 动画

 @return 默认NO
 */
- (BOOL)isNeedTopAnimation;

/**
 Bottom是否需要进行 动画
 
 @return 默认NO
 */
- (BOOL)isNeedBottomAnimation;

/**
 Bottom是否需要指定topView 截屏
 
 @return 默认NO
 */
- (BOOL)isNeedSnapAppointTopView;

/**
 需要做动画的topview  如果返回不为nil,则动画中默认只会对它做hidden 处理，动画结束后会自动设回

 @return 默认返回nil
 */
- (UIView*)topAnimationView;

/**
 需要做动画的bottomview 如果返回不为nil, 则动画中默认只会对它做hidden 处理，动画结束后会自动设回
 
 @return 默认返回nil
 */
- (UIView*)bottomAnimationView;


#pragma mark - CustomAnimation
- (void(^)(BOOL isFrom, UIView * containerView))beforeAnimationBlock;
- (void(^)(BOOL isFrom, UIView * containerView))animationBlock;
- (void(^)(BOOL isFrom, UIView * containerView))afterAnimationBlock;


@end
