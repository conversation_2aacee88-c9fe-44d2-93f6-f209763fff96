//
//  RouteSearchParser.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/6/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "RouteResult.h"
#import "JsonRoute.h"

typedef enum RouteResultErrorType_
{
    ERROR_TYPE_HAS_RESULT = 0,
    ERROR_TYPE_NO_RESULT = -1,
    ERROR_TYPE_FROM_AND_TO_IDENTICAL = 1
    
} RouteResultErrorType;

@interface RouteSearchParser : NSObject {

}

- (RouteResult *)parseRoute:(NSString *)str;
- (NSArray *)parseTaxiFee:(id)dic;

+ (RouteResult *)parseWalkResponseData:(QMJCE_routesearch_TmapWalkRouteRsp *)resp error:(NSInteger *)error;
+ (RouteResult *)parseCycleResponseData:(QMJCE_routesearch_TmapWalkRouteRsp *)resp error:(NSInteger *)error;


@end
