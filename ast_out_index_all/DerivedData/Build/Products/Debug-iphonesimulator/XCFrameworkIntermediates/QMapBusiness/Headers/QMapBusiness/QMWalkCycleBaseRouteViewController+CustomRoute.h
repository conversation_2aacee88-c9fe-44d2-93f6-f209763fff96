//
//  QMWalkCycleBaseRouteViewController+CustomRoute.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/8.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import "QMWalkCycleBaseRouteViewController.h"
#import "QMCustomRouteBasePresenter.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWalkCycleBaseRouteViewController (CustomRoute)
- (void)mapViewLongPressedForCustomRoute:(DMMapView *)mapView atCoordinate:(CLLocationCoordinate2D)coordinate;
- (BOOL)requestPassPointsByShortLinkIfNeed:(QMRoutePlanOption *)option;
- (BOOL)shouldEnterCustomRoutePageWithCoordinate:(CLLocationCoordinate2D)coor;
@end

NS_ASSUME_NONNULL_END
