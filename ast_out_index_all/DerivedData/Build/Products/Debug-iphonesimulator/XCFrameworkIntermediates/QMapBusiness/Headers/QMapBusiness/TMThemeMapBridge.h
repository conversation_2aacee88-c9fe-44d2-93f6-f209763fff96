//
//  TMThemeMapBridge.h
//  TencentTravelService
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/7/9.
//

#import <Foundation/Foundation.h>
#import <QMapHippy/TMCommonDefine.h>

typedef NS_ENUM(NSInteger, TMThemeMapTipsType)
{
    TMThemeMapTipsTypeAlarm = 1,
    TMThemeMapTipsTypeForecast = 2,
};

NS_ASSUME_NONNULL_BEGIN

@interface TMThemeMapBridge : NSObject

QMSharedInstance_H(TMThemeMapBridge)

/**
 拉取天气数据
 
 @param params 请求地点经纬度
 @param result 获取结果
 */
- (void)requestWeather:(NSDictionary *)params block:(void(^)(TMCommonResult * _Nonnull))result;

/**
 记录积水地图的天气动效已展示
 
 @param animationType 天气动画文件夹名字
 */
- (void)weatherAnimationDidShown:(NSString *)animationType;

/**
 积水地图天气预警tips已展示
 
 @param description 预警描述
 */
- (void)alarmWeatherTipsDidShown:(NSString *)description;


/**
 积水地图weatherState类型的预报tips已经展示
 
 @param weatherState 天气网返回的天气类型
 */
- (void)forecastWeatherTipsDidShown:(QMJCE_MapSSO_WeatherStateSet)weatherState;

/**
 积水地图后台服务地址

 @return 积水地图后台服务地址
 */
- (NSString *)themeMapServerUrlNewSoso;

/**
 获取用户定位信息
 
 @return TMCommonResult.data 格式为{ "latLng": { "latitude": latitude, "longitude": latitude }, "cityName": cityName }
 */
- (TMCommonResult *)getUserLocation;
@end

NS_ASSUME_NONNULL_END
