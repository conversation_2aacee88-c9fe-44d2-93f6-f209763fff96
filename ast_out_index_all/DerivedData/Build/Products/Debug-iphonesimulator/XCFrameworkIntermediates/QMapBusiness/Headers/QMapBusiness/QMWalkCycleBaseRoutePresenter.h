//
//  QMWalkCycleBaseRoutePresenter.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/12.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMRouteMarkerPresenter.h"
#import "RouteDrawToolBox.h"


@class DMMapView;
@class DMRouteElement;
@class QRouteResult;
@class RouteReqParam;

NS_ASSUME_NONNULL_BEGIN



@interface QMWalkCycleBaseRoutePresenter : NSObject

//起终点
@property (nonatomic, strong) QMWalkCycleRouteMarkerPresenter *markersPresenter;

@property (nonatomic, strong, readonly) NSMutableArray<DMRouteElement*> *routes;

@property (nonatomic, strong, readonly) NSMutableArray<DMMarkerElement *> *passPointMarkers;

- (instancetype)initWithMapView:(DMMapView *)mapView;

- (void)reload:(QRouteResult *)result parameter:(RouteReqParam *)param style:(QMWalkCycleRouteStyle)style;

- (void)switchSelectRoute:(int)newIndex oldIndex:(int)oldIndex;

- (void)clear;

// for child to rewrite
- (void)initMarkersPresenterWithMapView:(DMMapView *)mapView;

- (void)selectIndoorRouteAtIndex:(NSUInteger)selectedIndex;

- (void)updateIndoorFacilityMarkerShowStatus;

- (void)didSelectMarker:(DMMarkerElement *)marker;

- (BOOL)isIndoorFacility:(DMMarkerElement *)marker;

- (NSInteger)getIndexOfIndoorRoute:(DMRouteElement *)route;

- (DMCommonMarker *)indoorStartPickerMarker;

- (DMCommonMarker *)indoorDestFloorNameMarker;

- (NSUInteger)indexOfMaker:(DMMarkerElement *)marker;

@end

NS_ASSUME_NONNULL_END
