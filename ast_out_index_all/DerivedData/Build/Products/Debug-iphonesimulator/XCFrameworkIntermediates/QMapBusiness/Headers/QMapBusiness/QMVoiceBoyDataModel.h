//
//  QMVoiceBoyDataModel.h
//  QMapBusiness
//
//  Created by 狄弘辉 on 2021/4/3.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMVoiceType) {
    QMVoiceTypeCommonPoi = 1,    //普通POI
    QMVoiceTypeSmallRoutine = 2, //小程序
    QMVoiceTypeLaHei = 3,        //拉黑（省市）
    QMVoiceTypeBusLine = 4,      //公交地铁
    QMVoiceTypeCityPoi = 5,      //城市POI
};

@class QMVoiceBoyCityModel;
@interface QMVoiceBoyDataModel : NSObject

@property (nonatomic, copy) NSArray *commonPoiList;

@property (nonatomic, copy) NSArray *busLineList;

@property (nonatomic, copy) NSArray *cardList;

@property (nonatomic, copy) NSArray *laheiList;

//折叠态list
@property (nonatomic, copy) NSArray *list;

//当前叮当播报关注的原始数据信息
@property (nonatomic, assign) NSInteger iQueryType;

@property (nonatomic, strong) QMVoiceBoyCityModel *tCity;

@property (nonatomic, assign) BOOL isCityChanged;

@property (nonatomic, copy) NSString *cityName;

@end

@interface QMVoiceBoyCityModel : NSObject

@property (nonatomic, copy) NSString *cname;

@property (nonatomic, assign) NSInteger ctype;

@property (nonatomic, assign) CGFloat latitude;

@property (nonatomic, assign) CGFloat longitude;

@end

//当前搜索列表可视数据item
@interface QMVoiceBoyVisibleItemModel : NSObject

@property (nonatomic, assign) NSInteger index;

@property (nonatomic, assign) QMVoiceType type;

@property (nonatomic, copy) NSString *name;

@property (nonatomic, copy) NSString *shortAddr;

@property (nonatomic, assign) NSInteger coType;

@property (nonatomic, copy) NSDictionary *extraInfo;

@end


NS_ASSUME_NONNULL_END
