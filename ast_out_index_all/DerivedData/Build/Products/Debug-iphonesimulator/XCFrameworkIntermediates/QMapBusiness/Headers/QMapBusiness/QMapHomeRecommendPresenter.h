//
//  QMapHomeRecommendPresenter.h
//  SOSOMap
//
//  Created by admin on 2020/6/28.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapBusBusiness/QMapHomeRecommendModel.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, QMapHomeRecommendFrom) {
    QMapHomeRecommendFromHomepage = 0,
    QMapHomeRecommendFromRouteSearch = 1,
};

@interface QMapHomeRecommendPresenter : NSObject

+ (QMapHomeRecommendPresenter *)shareInstance;

- (void)checkHomeRecomendWithFrom:(QMapHomeRecommendFrom)recomendFrom complete:(void(^)(QMapHomeRecommendModel *)) complete;

- (void)closeHomeRecommendReport:(QMapHomeRecommendModel *) recommendModel;

@end

NS_ASSUME_NONNULL_END
