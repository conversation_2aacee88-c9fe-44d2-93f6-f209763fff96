//
//  QMUserFeedBackContainer.h
//  SOSOMap
//
//  Created by 刘宁 on 2017/7/8.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>  
#import <QMapMiddlePlatform/QMUgcReportTypeData.h>
#import <QMapMiddlePlatform/JsonPOIInfo.h>

@interface QMUserFeedBackContainer : QMViewController

@property (nonatomic) kReqSource source;
@property (nonatomic,assign) QMUgcReportFromSource fromSource;
@property (nonatomic,strong) JsonPOIInfo *poi;
@property (nonatomic,copy) NSString* placeName;
@property (nonatomic,assign) QMUgcReportType reportType;
@property (nonatomic) NSInteger entry; // 用户反馈入口类型，由后台定义
@property (nonatomic, copy) NSString *extensionUrl; //拓展 url, 页面路线由客户顿查找, 包含xxx.html 在内的所有路径均由外部拼接, 端内不处理任何逻辑
@property (nonatomic) BOOL isDarkMode; //设置这个值会让ugc的H5背景和导航栏使用深色模式
@property (nonatomic) NSDictionary *stationInfo; //车站信息

@end
