//
//  QMWalkDetailUIUtil.h
//  SOSOMap
//
//  Created by sarah on 2019/10/30.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapNaviKit/RouteWalkSegment.h>
#import <QMapNaviKit/QMWalkCycleBase.h>

@interface QMWalkDetailUIUtil : NSObject

+ (NSArray *)getFacilityTexts:(QMWalkCycleBase *)routeModel;
+ (NSArray<RouteWalkSegment *> *)getRouteSegments:(QMWalkCycleBase *)routeModel;
+ (NSString *)getShowedActionString:(NSString *)action;
+ (QMPointInfo *)getStartPoint:(QMWalkCycleBase *)routeModel;
+ (QMPointInfo *)getEndPoint:(QMWalkCycleBase *)routeModel;
+ (NSString *)getStartActionDesc:(NSArray<RouteWalkSegment *> *)segments;
+ (NSString *)getStartDirDescription:(NSString *)dir actionStr:(NSString *)actionStr;
+ (NSString *)getSegName:(TipsType)type action:(NSString *)action;
+ (NSString *)getSegAction:(NSString *)dir roadLen:(NSInteger)len;

@end

