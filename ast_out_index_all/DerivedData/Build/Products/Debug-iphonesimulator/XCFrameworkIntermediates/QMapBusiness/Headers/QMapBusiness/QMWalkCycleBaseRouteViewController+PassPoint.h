//
//  QMWalkCycleBaseRouteViewController+PassPoint.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/5.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import "QMWalkCycleBaseRouteViewController.h"
#import "QMNReverseGeoCoder.h"
#import "QMAlongwayChargingStationView.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWalkCycleBaseRouteViewController (PassPoint) <QMNReverseGeocoderDelegate, QMAlongwayChargingStationViewDelegate>
- (void)qmp_mapViewDidLongPressed:(DMMapView *)mapView atMarker:(DMMarkerElement *)marker atMapPoint:(DMMapPoint)mapPoint;
- (void)qmp_mapViewLongPressed:(DMMapView *)mapView atCoordinate:(CLLocationCoordinate2D)coordinate;
- (void)qmp_mapViewDidClick:(DMMapView *)mapView atCoordinate:(CLLocationCoordinate2D)coordinate;
- (void)qmp_mapViewDidSelect:(DMMapView *)mapView marker:(DMMarkerElement *)marker;
- (void)qmp_doInterrupt;
- (void)qmp_cleanUpCurrentMarker;
@end

NS_ASSUME_NONNULL_END
