//
//  QMVoiceDestPOISearcher.h
//  SOSOMap
//
//  Created by sarah on 2020/8/11.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMSemanticData.h>

typedef NS_ENUM(NSInteger, QVoiceDestFailType) {
    QVoiceDestFailTypeUnknown   = 0,
    QVoiceDestFailTypeNoResults,
    QVoiceDestFailTypeNetError
};

typedef void(^QVoiceDestFailBlock)(QVoiceDestFailType type);
typedef void(^QVoiceDestSucBlock)(NSArray *result);

@interface QMVoiceDestPOISearcher : NSObject

@property (nonatomic, copy)QVoiceDestFailBlock failBlock;
@property (nonatomic, copy)QVoiceDestSucBlock sucBlock;

- (void)doSearchPOIByKeyword:(NSString *)keyword semantic:(QMSemanticData *)semantic;
- (void)doNearBySearchPOIByKeyword:(NSString *)keyword
                          withCoor:(CLLocationCoordinate2D)coor
                          semantic:(QMSemanticData *)semantic
                             range:(NSInteger)range;

- (void)cancel;

@end

