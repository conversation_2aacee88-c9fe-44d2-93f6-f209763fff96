//
//  QQJSWebViewPluginEngine.h
//  QQMSFContact
//
//  Created by m on 19/1/14.
//
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QQJSWebViewProtocol.h>
#import <QMapMiddlePlatform/QQJSWebViewPluginEngineProtocol.h>



//插件配置字段
#define kPluginConfigClass          @"cls"
#define kPluginConfigName           @"name"
#define kPluginConfigDescription    @"desc"
#define kPluginConfigVersion        @"ver"
#define kPluginFunc                 @"func"

#define kCommonJSBridgePluginName               @"com.tencent.sosomap.jsbridge.Common"
#define kDriveScoreJSBridgePluginName           @"com.tencent.sosomap.jsbridge.DriveScore"
#define kMapJSBridgePluginName                  @"com.tencent.sosomap.jsbridge.Map"
#define kPOIDetailJSBridgePluginName            @"com.tencent.sosomap.jsbridge.POIDetail"
#define kApolloJSBridgerPluginName              @"com.tencent.sosomap.jsbridge.Apollo"
#define QM_OPERTAION_JSBRIDGE_PLUGIN_NAME       @"com.tencent.sosomap.jsbridge.Opertaion"
#define QM_UGCREPORT_JSBRIDGE_PLUGIN_NAME       @"kUGCReportJSPlugin"
#define QM_CREDIT_JSBRIDGE_PLUGIN_NAM           @"com.tencent.sosomap.jsbridge.Credit"
#define kVoiceJSBridgerPluginName               @"com.tencent.sosomap.jsbridge.Voice"
#define QM_PAY_JSBRIDGE_PLUGIN_NAME             @"com.tencent.sosomap.jsbridge.Pay"
#define QM_RUM_MONITOR_PLUGIN_NAME             @"com.tencent.sosomap.jsbridge.RUMMonitorModule"
#define QM_DYNAMIC_JSBRIDGE_PLUGIN_NAME             @"com.tencent.sosomap.jsbridge.Dynamic"
#define QM_DARKMODE_JSBRIDGE_PLUGIN_NAME             @"com.tencent.sosomap.jsbridge.DarkMode"
#define QM_SENSOR_JSBRIDGE_PLUGIN_NAME             @"com.tencent.sosomap.jsbridge.Sensor"
#define QM_WIDGET_JSBRIDGE_PLUGIN_NAME             @"com.tencent.sosomap.jsbridge.Widget"

extern NSString * const kVoiceAssistantJSBridgePluginName;



//环境变量的key
#define kPluginEnvSource                @"source" //webview是谁打开的
#define kPluginEnvWebViewController     @"webview" //webview引用
#define kPluginEnvNavigationController  @"nav"
#define kPluginEnvAppDelegate           @"appDelegate"
#define kPluginEnvDataCenter            @"dataCenter"
#define kPluginEnvAccountService        @"accountService"
#define kPluginEnvUin                   @"uin"


//请求参数字段
#define kPluginQuerySharedCallbackID    @"_sharedCallbackID"


//webview事件
typedef enum {
	QQWebViewEventLoadStart,
    QQWebViewEventLoadFinish,
    QQWebViewEventLoadFail,
    QQWebViewEventWillAppear,
    QQWebViewEventDidAppear,
    QQWebViewEventWillDisappear,
    QQWEbViewEventDidDisappear,
} QQWebViewEvent;


typedef void (^onBeforeLoadNext)(BOOL);


//插件管理器
@interface QQJSWebViewPluginEngine : NSObject <QQJSWebViewPluginEngineProtocol> {
    NSString *_oblPluginName;
    NSString *_platformName;
    NSString *_platformVersion;
}

@property (nonatomic, strong, readonly) NSMutableDictionary *pluginConfigs;
@property (nonatomic, strong, readonly) NSMutableDictionary *pluginInstances;
@property (nonatomic, strong, readonly) NSMutableDictionary *pluginEnvs;


+ (QQJSWebViewPluginEngine *)getInstance;
+ (BOOL)loadPluginIfNeed:(NSString *)pluginName;
// 插件是否已注册
- (BOOL)pluginExist:(NSString *)name;
- (BOOL)registerPlugin:(NSDictionary *)config;
- (BOOL)unregisterPluginNamed:(NSString *)name;

- (id)pluginInstanceClassNamed:(NSString *)name ofWebview:(id<QQJSWebViewProtocol>)webview;

- (BOOL)handleSchemaRequest:(NSURL *)request fromWebview:(id<QQJSWebViewProtocol>)webview;
- (void)handleEvent:(QQWebViewEvent)event userInfo:(NSDictionary *)userInfo fromWebview:(id<QQJSWebViewProtocol>)webview;

- (void)handleWebviewDestory:(id<QQJSWebViewProtocol>)webview;

- (void)registerPlatform:name version:version;
- (NSArray *)getPlatform;

@end


//插件需要实现的接口
@protocol IQQJSWebViewPlugin <NSObject>

@optional
- (BOOL)handleJsRequest:(NSURL *)request module:(NSString *)module method:(NSString *)method query:(NSDictionary *)query fromEnv:(NSDictionary *)env;

- (BOOL)handleSchemaRequest:(NSURL *)request schema:(NSString *)schema fromEnv:(NSDictionary *)env;
- (BOOL)handleEvent:(QQWebViewEvent)event userInfo:(NSDictionary *)userInfo fromEnv:(NSDictionary *)env; //TODO add url to userInfo (source url ?)
- (void)onBeforeLoad:(NSMutableURLRequest *)request fromEnv:(NSDictionary *)env next:(onBeforeLoadNext)next;
- (void)pluginDidInitFromEnv:(NSDictionary *)env;
- (void)pluginWillDestoryByEnv:(NSDictionary *)env;

@end
