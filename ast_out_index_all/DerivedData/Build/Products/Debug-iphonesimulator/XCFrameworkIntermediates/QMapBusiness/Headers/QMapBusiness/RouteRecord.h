#ifndef ROUTE_RECORD_H
#define ROUTE_RECORD_H
#include <stdio.h>

/**
 * 规划路线数据记录，数据格式：
 * HEADER + DATA
* HEADER 24 bytes :
 * 	flag, 4 bytes, fixed:TXLB
 * 	file type, 1 bytes,fixed:r
 * 	sys flag,1 bytes, 1 android,2 ios
 * 	ver, 1 byte
 * 	isencode,1 byte,1 encode,0 none
 * 	encode key,4 bytes
 * 	remain,11 bytes
 *
 * DATA:
 * select:(int)4 byte,
 * route size:(int)4 bytes;
 * from poi size:(int)4 bytes;
 * to poi size:(int)4 bytes;
 * route data: route size bytes;
 * from poi data:from poi size bytes;
 * to poi data:to poi size bytes;
 */

class RouteRecord{

private:
	FILE* file;
	int selectRoute;
	int routeSize;
	int fromSize;
	int toSize;
public:
	int openRouteFile(const char* fileName,bool isRead,int sysFlag);

	int addRouteData(int selectedRoute,const char* routeData,int rSize,const char* fromPoi,int fSize,const char* toPoi,int tSize);

	int getDataSize(int* select,int* route,int* from,int* to);

	int getRouteData(char* routeData,char* fromPoi,char* toPoi);



	int closeRouteFile();

};

#endif
