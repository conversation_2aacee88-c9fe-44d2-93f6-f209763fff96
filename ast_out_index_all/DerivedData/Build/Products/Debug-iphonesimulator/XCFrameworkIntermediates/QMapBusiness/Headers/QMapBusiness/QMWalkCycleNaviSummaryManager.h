//
//  QMWalkCycleNaviSummaryManager.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapNaviKit/ExternalDefine.h>

@class NavigationLogicController;
@class QMWalkCycleBase;
@class QMTravelRecord;

typedef NS_ENUM(NSUInteger, QMWalkCycleNaviSummaryType) {
    QMWalkCycleNaviSummaryTypeNone,                 // 不出小结页
    QMWalkCycleNaviSummaryTypeActivity,             // 步骑运营红包小结页
    QMWalkCycleNaviSummaryTypeNormalSummary,        // 普通小结页
    QMWalkCycleNaviSummaryTypeUnknown,              // 未知
};


NS_ASSUME_NONNULL_BEGIN
 
/// 步骑结束导航小结页业务逻辑管理器
@interface QMWalkCycleNaviSummaryManager : NSObject


/// 初始化方法
/// @param logic 导航逻辑管理器
/// @param naviRoute 路线
/// @param mapView mapView
/// @param activityInfo 红包运营信息
/// @param isActivityValid 红包运营活动是否开启
/// @param runMode 导航模式
- (instancetype)initWithLogic:(NavigationLogicController *)logic
                    naviRoute:(QMWalkCycleBase *)naviRoute
                      mapView:(DMMapView *)mapView
                 activityInfo:(nullable NSDictionary *)activityInfo
              isActivityValid:(BOOL)isActivityValid
                      runMode:(TravelType)runMode;

/// 获取轨迹数据
- (QMTravelRecord *)generateTravelSummary;


/// 获取导航结束后的下一页面
/// @param record 轨迹数据
- (UIViewController *)nextViewControllerAfterNavigation:(QMTravelRecord *)record;

/// 根据下一页面属性，获取下一页面类型
/// @param nextViewControllerAfterNavigation 导航结束后的下一类型
- (QMWalkCycleNaviSummaryType)nextViewControllerType:(nullable UIViewController *)nextViewControllerAfterNavigation;

/// 保存轨迹数据
/// @param record 轨迹数据
/// @param TravelReverseCompleteBlock 保存完毕后的回调
- (void)addTravelRecordReverseTask:(QMTravelRecord *)record finishBlock:(TravelReverseCompleteBlock)blk;


/// 是否显示小结页
/// @param isExit 是否即将退出
- (BOOL)canShowSummaryPageWithExit:(BOOL)isExit;

@end

NS_ASSUME_NONNULL_END
