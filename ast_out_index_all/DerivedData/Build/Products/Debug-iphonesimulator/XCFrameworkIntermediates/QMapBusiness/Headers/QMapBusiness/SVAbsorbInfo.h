//
//  SVAbsorbInfo.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 14-2-21.
//  Copyright (c) 2014年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface SVAbsorbInfo : NSObject
@property (nonatomic, copy, readonly) NSString *uid;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *addr;
@property (nonatomic, copy) NSNumber *pointx; // serverX
@property (nonatomic, copy) NSNumber *pointy; // serverY
@property (nonatomic, copy) NSNumber *x;//POI location x
@property (nonatomic, copy) NSNumber *y;//POI location y
@property (nonatomic, assign) double latitude;
@property (nonatomic, assign) double longitude;

@property (nonatomic, copy) NSString *svid;
@end
