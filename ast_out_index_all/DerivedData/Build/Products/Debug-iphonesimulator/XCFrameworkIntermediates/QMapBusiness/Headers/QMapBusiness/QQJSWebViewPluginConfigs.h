//
//  QQWebViewPluginConfigs.h
//  QQMSFContact
//
//  Created by m on 19/1/14.
//
//

#ifndef QQMSFContact_QQWebViewPluginConfigs_h
#define QQMSFContact_QQWebViewPluginConfigs_h

#import "POIDetailJSBridgePlugin.h"
#import "QMCommonJSBridgePlugin.h"


static NSMutableDictionary *pluginConfigs;

//不想通过QQWebViewPluginEngine的registerPugin:events:方法来注册插件的同鞋，可以把插件的配置写到这里
//这样当插件管理器初始化的时候，就会自动把这些插件注册上

//每个plugin配置里面增加一个func数组，为插件所能负责的js接口，分布在不同插件的特殊功能采用module_method单独声明，其他的直接声明module即可
//没看懂这段话的RTX：qeyewang
NSMutableDictionary *webviewPluginConfigs(){
    if (!pluginConfigs) {
        pluginConfigs = @{ kCommonJSBridgePluginName: @{ kPluginConfigClass: [QMCommonJSBridgePlugin class],
                                                         kPluginConfigDescription: @"JSBridge Common Module Plugin",
                                                         kPluginConfigVersion: @"1.0.0",
                                                         kPluginFunc: @[@"common"] },
                           QM_CREDIT_JSBRIDGE_PLUGIN_NAM: @{ kPluginConfigClass: NSClassFromString(@"QMCreditJSBridgePlugin"),
                                                         kPluginConfigDescription: @"JSBridge Credit Module Plugin",
                                                         kPluginConfigVersion: @"1.0.0",
                                                         kPluginFunc: @[@"credit"] } ,
                           kApolloJSBridgerPluginName:@{
                                   kPluginConfigClass:NSClassFromString(@"QMApolloJSBridgePlugin"),
                                   kPluginConfigDescription:@"",
                                   kPluginConfigVersion:@"1.0.0",
                                   kPluginFunc:@[@"apollo"]
                           },
                           QM_DARKMODE_JSBRIDGE_PLUGIN_NAME: @{
                               kPluginConfigClass:NSClassFromString(@"QMDarkModeBridgePlugin"),
                               kPluginConfigDescription:@"",
                               kPluginConfigVersion:@"1.0.0",
                               kPluginFunc:@[@"darkmode"]
                           },
                           QM_OPERTAION_JSBRIDGE_PLUGIN_NAME:@{
                                   kPluginConfigClass:NSClassFromString(@"QMOperationJSBridgePlugin"),
                                   kPluginConfigDescription:@"JSBridge operation Module Plugin",
                                   kPluginConfigVersion:@"1.0.0",
                                   kPluginFunc:@[@"operation"]
                           },
                           kVoiceJSBridgerPluginName:@{
                               kPluginConfigClass : NSClassFromString(@"QMVoiceJSBridgePlugin"),
                               kPluginConfigDescription:@"JSBridge voiceSquare Module Plugin",
                               kPluginConfigVersion:@"1.0.0",
                               kPluginFunc:@[@"voice"]
                           },
                           QM_PAY_JSBRIDGE_PLUGIN_NAME: @{
                               kPluginConfigClass : NSClassFromString(@"QMPayJSBridgePlugin"),
                               kPluginConfigDescription:@"JSBridge Pay Module Plugin",
                               kPluginConfigVersion:@"1.0.0",
                               kPluginFunc:@[@"pay"]
                           },
                           QM_RUM_MONITOR_PLUGIN_NAME: @{
                               kPluginConfigClass : NSClassFromString(@"QMRumMonitorJSBridgePlugin"),
                               kPluginConfigDescription:@"JSBridge RUM Monitor Module Plugin",
                               kPluginConfigVersion:@"1.0.0",
                               kPluginFunc:@[@"RUMMonitorModule"]
                           },
                           QM_DYNAMIC_JSBRIDGE_PLUGIN_NAME: @{
                               kPluginConfigClass: NSClassFromString(@"QMDynamicJSBridgePlugin"),
                               kPluginConfigDescription: @"JSBridge dynamic Module Plugin",
                               kPluginConfigVersion: @"1.0.0",
                               kPluginFunc: @[@"dynamic"]
                           },
                           QM_SENSOR_JSBRIDGE_PLUGIN_NAME: @{
                               kPluginConfigClass: NSClassFromString(@"QMSensorJSBridgePlugin"),
                               kPluginConfigDescription: @"JSBridge sensor Module Plugin",
                               kPluginConfigVersion: @"1.0.0",
                               kPluginFunc: @[@"sensor"]
                           },
                           QM_WIDGET_JSBRIDGE_PLUGIN_NAME: @{
                               kPluginConfigClass: NSClassFromString(@"QMWidgetJSBridgePlugin"),
                               kPluginConfigDescription: @"JSBridge widget Module Plugin",
                               kPluginConfigVersion: @"1.0.0",
                               kPluginFunc: @[@"appWidgetPlugin"]
                           },
        }.mutableCopy;
    }
    return pluginConfigs;
}

NSDictionary *webviewAllNonResidentPlugins(){
    static dispatch_once_t onceToken;
    static NSDictionary *allNonResidentPlugins;
    dispatch_once(&onceToken, ^{
        
        
        allNonResidentPlugins = @{kDriveScoreJSBridgePluginName: @{
                                          kPluginConfigClass : NSClassFromString(@"QMDriveJSBridgePlugin"),
                                          kPluginConfigDescription : @"JSBridge Drive Score Module Plugin",
                                          kPluginConfigVersion:@"1.0.0",
                                          kPluginFunc:@[@"drivingScore" , @"walkScore", @"drivingPower"]
        },
                                  kMapJSBridgePluginName: @{
                                          kPluginConfigClass : NSClassFromString(@"QMMapJSBridgePlugin"),
                                          kPluginConfigDescription : @"JSBridge Map Module Plugin",
                                          kPluginConfigVersion:@"1.0.0",
                                          kPluginFunc:@[@"map"]
                                  },
                                  kPOIDetailJSBridgePluginName:@{
                                          kPluginConfigClass : NSClassFromString(@"POIDetailJSBridgePlugin"),
                                          kPluginConfigDescription:@"JSBridge POI Module Plugin",
                                          kPluginConfigVersion:@"1.0.0",
                                          kPluginFunc:@[@"poi"]
                                  },
                                  QM_UGCREPORT_JSBRIDGE_PLUGIN_NAME:@{
                                          kPluginConfigClass : NSClassFromString(@"QMUgcReportJSPlugin"),
                                          kPluginConfigDescription:@"JSBridge ugc Module Plugin",
                                          kPluginConfigVersion:@"1.0.0",
                                          kPluginFunc:@[@"ugc"]
                                  },
                                  kVoiceAssistantJSBridgePluginName:@{
                                          kPluginConfigClass : NSClassFromString(@"QMVoiceAssistantJSBridgePlugin"),
                                          kPluginConfigDescription:@"JSBridge voiceAssistant Module Plugin",
                                          kPluginConfigVersion:@"1.0.0",
                                          kPluginFunc:@[@"voiceAssistant"]
                                  }
        }.copy;
        
        
    });
    
    return allNonResidentPlugins;
}

//这是QQWebviewPluginEngine初始化的时候即加载的插件，他们需要用到QQWebviewEventLoadStart、viewWillAppear这类通知，按需加载不合适。
NSArray *initialPluginsClass()
{
    return @[];
    //    return @[[QQJSBridgeDevicePlugin class],
    //             [QQJSBridgeSensorPlugin class]];
}

#endif
