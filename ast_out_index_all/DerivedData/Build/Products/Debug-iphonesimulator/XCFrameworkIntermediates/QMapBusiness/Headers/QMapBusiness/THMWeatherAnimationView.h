//
//  THMWeatherAnimationView.h
//  AISDK
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/7/1.
//


#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class THMWeatherAnimationView;

typedef NS_ENUM(NSInteger, TMWeatherAnimationSource) {
    TMWeatherAnimationSourceHomepage = 0,
    TMWeatherAnimationSourceAccumWater
};


@protocol THMWeatherAnimationViewDelegate <NSObject>

- (void)animationViewWillDismiss:(THMWeatherAnimationView *)view;

@end


@interface THMWeatherAnimationView : UIView

@property (nonatomic, weak) id<THMWeatherAnimationViewDelegate> delegate;
- (void)showWeatherAnimationWithType:(NSString *)type source:(TMWeatherAnimationSource)source;
- (void)stopWeatherAnimation;
@end

NS_ASSUME_NONNULL_END
