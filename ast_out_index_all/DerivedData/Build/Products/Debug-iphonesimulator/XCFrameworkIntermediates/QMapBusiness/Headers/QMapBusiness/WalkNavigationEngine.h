//
//  WalkNavigationEngine.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 2017/4/7.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>
#import "NavigationBaseEngine.h"

typedef void(^QMWalkAccessibilityAngleOffsetTextBlock)(NSString *angleOffsetText);

@interface WalkNavigationEngine : NavigationBaseEngine

@property (nonatomic, copy) QMFusionLocationUpdateCallBack fusionLocationUpdateCallBack;
@property (nonatomic, copy) QMWalkAccessibilityAngleOffsetTextBlock angleOffsetTextBlock;
@property (nonatomic) BOOL  accessibilityWalkAngleEnable;//夹角开关

@end
