//
//  RouteTrafficInfo.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 3/27/14.
//  Copyright (c) 2014 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, TrafficStatus)
{
    TrafficStatusUnknown = 2,
    TrafficStatusSlow = 0, //缓行
    TrafficStatusJam = 1, //拥堵
    TrafficStatusUnCrowded = 12, //畅通
    TrafficStatusStopMoving = 3, // 严重拥堵
};

typedef NS_ENUM(NSUInteger, ColorType)
{
    ColorTypeGreen = 0,
    ColorTypeYellow = 1,
    ColorTypeRed = 2,
    ColorTypeNone = 3,
    ColorTypeDarkRed = 4,
    ColorTypeOther = 5,
};


@interface RouteTrafficInfo2 : NSObject

@property (nonatomic, assign) int startNum;
@property (nonatomic, assign) int endNum;
@property (nonatomic, assign) CGFloat speed;
@property (nonatomic, assign) NSInteger distance;
@property (nonatomic, assign) ColorType color;
@property (nonatomic, strong) NSString *roadName;

+ (ColorType)convertTrafficStatusToColorType:(TrafficStatus)trafficStatus;

//堵点中  实时状态有三种0为畅通,1为缓行,2为拥堵. 现在加一个第四种3为猪肝红
+ (DMRouteTrafficColorIndex)convertJamTypeToColorType:(int)trafficType;

+ (int)convertJamLineColor:(int )trafficType;

- (UIColor *)rgbColor;

@end


