//
//  RouteSearchKitDefine.h
//  SOSOMap
//
//  Created by sarah on 2017/12/14.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@class RouteSearchParameter;
@class QMPointInfo;
@class JsonPOIInfo;
@class JsonRoute;
@class QMCarRouteMapControl;
@interface RouteSearchKitDefine : NSObject

+ (float) getLineAngle:(int) mapPoint1X y1:(int) mapPoint1Y x2:(int) mapPoint2X y2:(int) mapPoint2Y;

/**
 车牌号里面的汉字，使用GBK编码做URL转义
 之前使用的stringByAddingPercentEscapesUsingEncoding废弃了，所以使用这种方式实现
 */
//+ (NSString *)carNumberWithGBKEncodingPercentEscapes;

+ (NSString *)getStatus;

+ (void)updateRoutePlaceInfoWithCurrentLocation:(QRoutePlaceInfo *)poi;
+ (void)updateDriveRouteReqParamWithCurrentLocation:(QDriveRouteReqParam *)param;

+ (NSString *)canTriggerDriveRouteReq:(QDriveRouteReqParam *)param;

+ (void)driveRouteReqParamArmed:(QDriveRouteReqParam *)param;
+ (void)driveRouteReqParamArmWithFilterGPSPoints:(QDriveRouteReqParam *)param;


+ (DMMapRect)calBound:(QRouteForDrive *)route param:(QDriveRouteReqParam *)param;

+ (CGRect)calBoundForMultiPlan:(CGRect)mapControlFrame isHorizonRoute:(BOOL)isHorizon hasTips:(BOOL)hasTips;

+ (DMMapRect)calDMBound:(QRouteForDrive *)route param:(QDriveRouteReqParam *)param;


+ (DMMapRect)calBound:(QRouteForDrive *)route param:(QDriveRouteReqParam *)param passPoints: (NSArray<RoutePoint*> *)passPoints;
+ (NSString *)getDriveReqUrl:(QDriveRouteReqParam *)param;

+ (NSArray<NSString *> *)getAllRouteidsFromResult:(QRouteResult *)result;

+ (QRoutePlaceInfo *)generateRoutePlaceInfoFromQMPointInfo:(QMPointInfo *)poi;
+ (QMPointInfo *)generateQMPointInfoFromQRoutePlaceInfo:(QRoutePlaceInfo *)placeInfo;

+ (RouteSearchParameter *)generateRouteSearchParamterFromQDriveReqParam:(QDriveRouteReqParam *)parameter;

+ (void)syncStartDestName:(QRouteResultForDrive *)result param:(QDriveRouteReqParam *)param;

@end
