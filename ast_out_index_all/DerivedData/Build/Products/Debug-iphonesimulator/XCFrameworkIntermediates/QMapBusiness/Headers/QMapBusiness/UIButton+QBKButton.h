//
//  UIButton+QBKButton.h
//  SOSOMap
//
//  Created by lv wei on 13-7-5.
//  Copyright (c) 2013年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef enum 
{   E<PERSON>uttonTop,
    EButtonMidVer,
    EButtonBottom,
    EButtonSingleVer,
    EButtonLeft,
    EButtonMidHor,
    EButtonRight,
    
}TButtonType;

@interface UIButton (QBKButton)

- (void)makeTopButtonBk:(BOOL)isResponseEvent;
- (void)makeMidVerButtonBk:(BOOL)isResponseEvent;
- (void)makeBottomButtonBk:(BOOL)isResponseEvent;
- (void)makeSingleVerButtonBk:(BOOL)isResponseEvent;

- (void)makeButtonBK:(NSString*)normalImageName
highlightedImageName:(NSString*)highlightedImageName
          buttonType:(TButtonType)buttonType;

- (void)makeImageTitleAdjust;

@end
