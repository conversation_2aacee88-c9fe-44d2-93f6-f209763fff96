//
//  QMWXMiniProgramAlertInfo.h
//  QMapBusiness
//
//  Created by wyh on 2021/5/13.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMWXMiniProgramAlertInfo : NSObject

@property (nonatomic, copy, readonly) NSString *programAppId;

@property (nonatomic, copy, readonly) NSString *programUserName;

@property (nonatomic, copy, readonly) NSString *alertTitle;

@property (nonatomic, copy, readonly) NSString *alertDesc;

@property (nonatomic, copy, readonly) NSString *alertActionCancelTitle;

@property (nonatomic, copy, readonly) NSString *alertActionOKTitle;

@end

@interface QMWXMiniProgramAlertInfoFetcher : NSObject

@property (nonatomic, strong, readonly) NSArray<QMWXMiniProgramAlertInfo *> *alertInfos;

+ (instancetype)fetcher;


@end

NS_ASSUME_NONNULL_END
