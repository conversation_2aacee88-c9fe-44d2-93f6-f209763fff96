//
//  QMWebViewController.h
//  QMapBusiness
//
//  Created by lidazhu on 2021/9/14.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import "QMWebViewDefines.h"
#import "QMWebViewPluginProtocol.h"
#import <QMapMiddlePlatform/QMViewController.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMWebViewController : QMViewController

@property (nonatomic, copy) NSString *placeholderTitle;

- (instancetype)init NS_UNAVAILABLE;


- (instancetype)initWithURL:(NSString *)url options:(QMWebViewOption)options;

/// 注册自定义的插件
/// 对于业务强相关的代码，应通过自定义插件实现。如业务埋点等
/// @param plugin 插件实例
- (void)addPlugin:(id<QMWebViewPluginProtocol>)plugin;

@end

NS_ASSUME_NONNULL_END
