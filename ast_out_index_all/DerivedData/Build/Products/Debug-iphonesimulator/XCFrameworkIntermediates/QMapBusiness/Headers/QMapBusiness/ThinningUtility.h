//
//  ThinningUtility.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 11/15/13.
//  Copyright (c) 2013 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/ConstantDefine.h>
#import <RouteGuidance/guidance_data_def.h>

@interface ThinningUtility : NSObject

+ (NSMutableArray *)rarefyMapPoints:(RouteGuidanceMapPoint *)points pointsCount:(int)count beginIndex:(NSInteger)beginIndex endIndex:(NSInteger)endIndex  tolerance:(double)tolerance;

//对点窜中指定索引的三个点进行抽稀
+ (NSMutableArray *)rarefyPoints:(RouteGuidanceGPSPoint *)points pointsCount:(int)count firstIndex:(int)firstIndex middleIndex:(int)middleIndex lastIndex:(int)lastIndex tolerance:(double)tolerance;

@end
