//
//  WeixinSDKManager.h
//  SOSOMap
//
//  Created by <PERSON> on 12-11-23.
//  Copyright (c) 2012年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMLoginFailureContext.h"
#import "QMWeChatPayParam.h"
#import <QMapMiddlePlatform/QMOpenMiniProgramOption.h>

@class WMPFLaunchMetricsModel;

static NSString *const KQMWeChatIsLoginning = @"正在登录...";

typedef enum{
    QWeChatLoginStateInit,
    QWeChatLoginStateLoginning,
    QWeChatLoginStateAccessToken,
    QWeChatLoginStateNickName,
    QWeChatLoginStateHeadImage,
    QWeChatLoginStateMapServer,
    QWeChatLoginStateNetError,
    QWeChatLoginStateDataError,
}QWeChatLoginState;

typedef enum{
    QWeChatPayStateOk,
    QWeChatPayStateErr,
    QWeChatPayStateUserCancel,
}QWeChatPayState;

typedef NS_ENUM(NSInteger, QWeChatMinProgramType){
    QWeChatMinProgramTypeNone = 0,
    QWeChatMinProgramTypeBusCode = 1,
    QWeChatMinProgramTypeETC,
};

typedef NS_ENUM(NSUInteger, QWeChatPayType) {
    QWeChatPayTypeDefault,
    QWeChatPayTypeCreditScore,
};

FOUNDATION_EXTERN NSString * const QMWeixinDidFinishSendingNotification;
FOUNDATION_EXTERN NSString * const QMWeixinWillOpenMiniprogramNotification;
FOUNDATION_EXTERN NSString * const QMWeixinDidOpenMiniprogramNotification;
FOUNDATION_EXTERN NSString * const QMWeixinMiniProgramIdUserInfoKey;
FOUNDATION_EXTERN NSString * const QMWeixinMiniProgramUserNameUserInfoKey;
FOUNDATION_EXTERN NSString * const QMWeixinMiniProgramRecordActionUserInfoKey;
FOUNDATION_EXTERN NSString * const QMWeixinDidFinishSendingCampaignStepsNotification;

@class QMWeChatUserInfo;
@protocol WeixinSDKManagerPayDelegate;

@interface WeixinSDKManager : NSObject<QMLoginFailureMessager, DKPWeChatManagerService>

@property (nonatomic, readonly, class) BOOL isAvailable;
@property (nonatomic, readonly) QMWeChatUserInfo *weChatUserInfo;
@property (nonatomic, weak) id<WeixinSDKManagerPayDelegate>weiXinSdkManagerPayDelegate;

@property (nonatomic) NSString *accessToken;
@property (nonatomic) NSString *refreshToken;
@property (nonatomic) NSString *openId;
@property (nonatomic) NSString *expiresIn;
@property (nonatomic) NSString *unionId;

@property (class, nonatomic, readonly) NSString *appId;

@property (nonatomic) QWeChatPayType payType;

QMDefaultManager_H(WeixinSDKManager)

+ (BOOL)isWXAppSupportStateAPI;
+ (void)initWhenApplicationDidFinishLaunch;
+ (BOOL)isReady;

- (BOOL)handleOpenUniversalLink:(NSUserActivity *)userActivity;

+ (BOOL)isSessionSendingAvailable;
+ (BOOL)isTimelineSendingAvailable;

- (void)sendToSessionTitle:(NSString *)title description:(NSString *)desc thumbImage:(UIImage *)thumb wapURL:(NSString *)wap;

- (void)sendToTimelineTitle:(NSString *)title description:(NSString *)desc thumbImage:(UIImage *)thumb wapURL:(NSString *)wap;

- (void)sendToTimelineTitle:(NSString *)title thumbImage:(UIImage *)thumb wapURL:(NSString *)wap;
- (void)sendToTimelineImage:(NSString *)title thumbImage:(UIImage *)thumb withRatio:(NSNumber *)ratio;

- (void)handleURL:(NSURL *)url;

- (void)sendToSessionTitle:(NSString *)title description:(NSString *)desc thumbImage:(UIImage *)thumb musicUrl:(NSString *)musicUrl musicDataUrl:(NSString *)musicDataUrl;
- (void)sendToTimelineTitle:(NSString *)title description:(NSString *)desc thumbImage:(UIImage *)thumb musicUrl:(NSString *)musicUrl musicDataUrl:(NSString *)musicDataUrl;
- (void)sendToSessionImage:(NSString *)title thumbImage:(UIImage *)thumb withRatio:(NSNumber *)ratio;

- (void)sendToStateWithTitle:(NSString *)title imgUrl:(id)imgUrl wxStateId:(NSString *)wxStateId jumpType:(NSInteger)jumpType channelName:(NSString *)channelName completion:(void (^)(NSInteger code))completion;

- (BOOL)isWeChatLogined;
- (void)sendPrePayOrder:(QMWeChatPayParam *)param;
- (void)sendOfflinePay;

/**
 打开小程序
 */
- (void)openMiniProgram:(QMOpenMiniProgramOption *)option completion:(void (^)(BOOL success))completion;


/**
 扫码打开小程序
 */
- (void)openMiniProgramByScanQRCode;
- (void)openMiniProgramByScanQRCode:(NSDictionary *)param callback:(void (^)(NSError *error, NSString *appid))callback;

/**
 是否支持小程序 SDK
 */
- (BOOL)supportWeAppSDK;

/**
 退出小程序登录态
 */
- (void)logoutWeAppSDK;

/**
 预加载小程序框架
 */
- (void)preloadMiniProgramIfNeeded;

/** 分享小程序给 QQ 好友
 * @param userName 小程序标识,
 * @param path 小程序页面路径, 也可以传参数, 如: @"?foo=bar"
 * @param title 标题
 * @param image 图片
 * @param wapURL H5页面, 用于适配打不开小程序的版本
 * @param completion completion
 */
- (void)shareToMiniProgramWithUserName:(NSString *)userName
                                  path:(NSString *)path
                                titile:(NSString *)title
                                  text:(NSString *)text
                                 image:(UIImage *)image
                                wapURL:(NSString *)wap
                       withShareTicket:(BOOL)withShareTicket
                       miniprogramType:(NSInteger)miniprogramType
                            completion:(void (^)(BOOL success))completion;

/** 微信一次性消息订阅，微信用户同意授权，获取一次给用户推送一条订阅消息的机会
 * @param templateId 订阅消息模板ID，在微信开放平台提交应用审核通过后获得
 * @param scene  用来标识订阅场值
 * @param sceneId 运营触发平台模版ID
 * @param position 触发场景
 * @param completion completion
 */
- (void)sendSubscribeMsgWithTemplateId:(NSString *)templateId
                                 scene:(UInt32)scene
                               sceneId:(NSInteger)sceneId
                              position:(NSString *)position
                            completion:(void (^) (NSInteger code, NSString *msg))completion;

/** 分享视频到微信视频号
 *  @param identifier   系统相册中视频标识符
 *  @param completion   completion
 */
- (void)sendToVideoWithIdentifier:(NSString *)identifier
                       completion:(void (^) (NSInteger code, NSString *msg))completion;

/// 跳转到微信客服会话
/// @param corpId 企业ID
/// @param url 客服URL
- (void)jumpToCustomerServiceWithCorpid:(NSString *)corpId
                                    url:(NSString *)url
                             completion:(void (^) (NSInteger code, NSString *msg))completion;


/// 小程序登陆授权
/// @param dic url参数
- (void)verifyMiniProgram:(NSDictionary *)dic;

@end

@protocol WeixinSDKManagerPayDelegate <NSObject>

/**
 *  notify wexin login state
 *
 *  @param weixinSDKManager WeixinSDKManager
 *  @param weChatLoginState QWeChatLoginState
 */
- (void)notifyWeChatPayResult:(WeixinSDKManager*)weixinSDKManager state:(QWeChatPayState)weChatPayState message:(NSString *)message;

@end
