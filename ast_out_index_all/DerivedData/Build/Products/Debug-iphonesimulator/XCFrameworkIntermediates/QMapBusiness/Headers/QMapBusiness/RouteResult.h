//
//  RouteResult.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/6/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <RouteGuidance/QWalkGuidanceApi.h>
#import <RouteGuidance/guidance_data_def.h>
#import "MultiRouteInfo.h"
#import "JsonRoute.h"
#import <QMapMiddlePlatform/TaxiFee.h>

@interface RouteResult : QRouteResult {

    NSArray *routes_;
    // added by nancynan 2012-6-14 for taxi fees
    NSArray *taxiFees_;
    NSString *taxiFeeString_;
    
    NSInteger selectedRouteIndex_;
    BOOL isFromAndToIdentical_;
}

@property (nonatomic) int forkIndex;
@property (nonatomic) double forkX;
@property (nonatomic) double forkY;
@property (nonatomic) int saveTime;
@property (nonatomic, strong) NSString *dynReason;

@property (nonatomic, strong) NSArray *routesReason;//方案外的路线
@property (nonatomic) BOOL isFromAndToIdentical;

@property (nonatomic, assign) BOOL isBoundsResult;
@property (nonatomic, assign) WalkGuidanceMapPoint *fencePoints;
@property (nonatomic, assign) RouteGuidanceMapPoint *mapPoints;
@property (nonatomic, assign) int fencePointsCount;

@property (nonatomic, strong) MultiRouteInfo  *multiRouteInfo;

@property (nonatomic, assign) DMMapPoint *anchorPoints;

@property (nonatomic, copy) NSArray<TaxiFee *> *taxiFees;
@property (nonatomic, copy) NSString *taxiFeeString;
@property (nonatomic, assign) int error;
@property (nonatomic, assign) int type;
@property (nonatomic, assign) int iErrNo;
@property (nonatomic, assign) NSInteger selectedRouteIndex;

@property (nonatomic, copy) NSString *reqRouteUrl;//保存在线路线请求返回的url

- (void)parseCoorPoints:(NSString *)coors;
- (void)parseCarCoorPoints:(NSString *)coors;

- (BOOL)isTypeBusByRow:(int)row;
- (BOOL)isTypeCarByRow:(int)row;
- (BOOL)isTypeWalkByRow:(int)row;
- (BOOL)isTypeBus;
- (BOOL)isTypeCar;
- (BOOL)isTypeWalk;
- (BOOL)isTypeCycle;
- (BOOL)isHassub;
- (id)typeRoute;

@end
