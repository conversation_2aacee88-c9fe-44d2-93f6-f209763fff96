//
//  QMVoiceAlongwaySemanticManager.h
//  SOSOMap
//
//  Created by sarah on 2020/9/23.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/QMBaseSemanticManager.h>

@interface QMVoiceAlongwaySemanticManager : QMBaseSemanticManager

@property (nonatomic, weak)id voiceProxy;

//NO，没有冲突，继续下面的处理，YES，有冲突
- (BOOL)isInVoiceAlongwayConflict:(QMSemanticData *)semantic;
//是否是发起沿途搜语义
- (BOOL)isVoiceAlongwayIntent:(QMSemanticData *)semantic;
//是否是发起任意搜语义
- (BOOL)isVoiceAnywayIntent:(QMSemanticData *)semantic;
//是否是确认语义
- (BOOL)isConfirmIntent:(QMSemanticData *)semantic;
//是否是选择语义
- (BOOL)isSelectIntent:(QMSemanticData *)semantic;

//处理发起沿途搜语义
- (QMSemanticState)handleVoiceAlongwayIntent:(QMSemanticData *)semantic;
//处理发起任意搜语义
- (QMSemanticState)handleVoiceAnywayIntent:(QMSemanticData *)semantic;
//处理确认语义
- (QMSemanticState)handleConfirmIntent:(QMSemanticData *)semantic;
//处理选择语义
- (QMSemanticState)handleSelectIntent:(QMSemanticData *)semantic;

@end
