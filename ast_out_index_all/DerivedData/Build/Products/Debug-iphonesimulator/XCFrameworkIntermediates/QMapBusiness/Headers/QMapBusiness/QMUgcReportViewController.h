//
//  QMUgcReportViewController.h
//  SOSOMap
//
//  Created by leon<PERSON><PERSON> on 2018/1/18.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMUgcReportTypeData.h>
#import <QMapMiddlePlatform/JsonPOIInfo.h>
#import <QMapMiddlePlatform/QMWebViewDefine.h>
#import <QMapMiddlePlatform/QMUgcReportBaseViewController.h>
#import <QMapMiddlePlatform/QMUgcReportUIDelegate.h> 
#import <QMapMiddlePlatform/QMCarRouteNaviCommon.h>

@interface QMUgcReportViewController : QMUgcReportBaseViewController

/**
 当只是作为一个ugcplugin的一个实现类调用的时候，而不是push或present到页面上的时候
 */
@property (nonatomic, assign) BOOL actAsUgcPluginImplementation;
@property (nonatomic, strong) QMWebView *webView;
@property (nonatomic,strong) JsonPOIInfo* poi;
@property (nonatomic,assign) QMUgcReportType reportType;//导航中判定选定类型
@property (nonatomic,assign) BOOL isInNav;//是否是导航上报
@property (nonatomic,assign) QMUgcNavType navType;
@property (nonatomic,assign) BOOL isNight;
@property (nonatomic,copy) NSString* placeName;
@property (nonatomic,strong) NSDictionary *naviInfo;
@property (nonatomic,weak) id<QMUgcReportUIDelegate> ugcUIdelegate;
@property (nonatomic,assign) QMUgcReportFromSource fromSource;
@property (nonatomic,assign) BOOL isRecords; //是否是我的上报页面，屏蔽某些回调用
@property (nonatomic) NSInteger entry; // 用户反馈入口类型，由后台定义
@property (nonatomic, strong) NSString *customReportUrl;
@property (nonatomic, assign) NSInteger maxPictureCount;        //最大的图片数量
@property (nonatomic, strong) id reportDataProducer;
@property (nonatomic, copy) NSString *extensionUrl; //拓展 url, 页面路线由客户顿查找, 包含xxx.html 在内的所有路径均由外部拼接, 端内不处理任何逻辑
@property (nonatomic, strong) UIImage *snapshot; //屏幕截图
@property (nonatomic) BOOL isDarkMode;
@property (nonatomic) NSString *lineId;          //公交线路id
@property (nonatomic) NSString *stationId;       //公交站id
@property (nonatomic) NSDictionary *stationInfo; //车站信息
@property (nonatomic) QMCarRouteNaviType routeNaviType;

@property (nonatomic) BOOL isShowTitleLabel; //是否需要在页面里显示标题
@property (nonatomic) BOOL ignoreReport; //是否需要在页面里显示标题
@property (nonatomic) BOOL showScreenShot; //是否带上截图
@property (nonatomic) BOOL forceScreen; //是否强制竖屏

- (void)unregisterCommonPlugin;


@end
