//
//  QMWebViewDefines.h
//  QMapBusiness
//
//  Created by lidazhu on 2021/9/15.
//  Copyright © 2021 Tencent. All rights reserved.
//

#ifndef QMWebViewDefines_h
#define QMWebViewDefines_h

typedef NS_ENUM(NSUInteger, QMWebViewOption) {
    QMWebViewOptionNone                     = 0,
    QMWebViewOptionEnableJSBridge           = 1 << 0,
    QMWebViewOptionEnableBlankPage          = 1 << 1, // 无网时显示重新加载按钮
    QMWebViewOptionUseH5NavigationBar       = 1 << 2, // 不使用iOS Native实现的导航栏，全屏显示webView，导航实现交由Web实现
    QMWebViewOptionAlwaysPopInsteadOfGoBack = 1 << 3, // 如果不设置此项，多层级web默认会随着手势逐页goBack而不是popVC
    QMWebViewOptionEnableCloseButton        = 1 << 4, // 在返回按钮右侧增加关闭按钮，canGoBack时才会显示
    QMWebViewOptionEnableLoadingProgress    = 1 << 5, // 显示顶部加载进度条
    QMWebViewOptionDefault                  = 1 << 30
};

#endif /* QMWebViewDefines_h */
