//
//  QMapWebViewFactory.h
//  QMapBusiness
//
//  Created by lidazhu on 2021/9/26.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import "QMWebViewDefines.h"
#import "QMWebViewPluginProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMapWebViewFactory : NSObject

/// 创建一个WebView
/// @param url 链接字符串
/// @param options 浏览器功能选项
/// @param plugins 若业务自己有特殊需求可通过注入插件实现
///
/// @code
/// Example:
/// WKWebView *webView = [QMapWebViewFactory webViewWithURL:url options:(QMWebViewOptionDefault | QMWebViewOptionUseH5NavigationBar) plugins:nil];
/// @endcode
+ (WKWebView *)webViewWithURL:(NSString *)url options:(QMWebViewOption)options plugins:(nullable NSArray<id<QMWebViewPluginProtocol>> *)plugins;

/// 创建一个WebViewController
/// @param url 链接字符串
/// @param options 浏览器功能选项
/// @param plugins 若业务自己有特殊需求可通过注入插件实现
///
/// @code
/// Example:
/// UIViewController *vc = [QMapWebViewFactory webViewControllerWithURL:url options:(QMWebViewOptionDefault | QMWebViewOptionEnableLoadingProgress) plugins:nil];
/// @endcode
+ (UIViewController *)webViewControllerWithURL:(NSString *)url options:(QMWebViewOption)options plugins:(nullable NSArray<id<QMWebViewPluginProtocol>> *)plugins;

@end

NS_ASSUME_NONNULL_END

/*
QMWebViewOption 解释
默认 QMWebViewOptionDefault 为:
[√] QMWebViewOptionEnableJSBridge           // JSBridge
[√] QMWebViewOptionEnableBlankPage          // 无网时显示重新加载按钮
[ ] QMWebViewOptionUseH5NavigationBar       // 不使用iOS Native实现的导航栏，全屏显示webView，导航实现交由Web实现
[ ] QMWebViewOptionAlwaysPopInsteadOfGoBack // 如果不设置此项，多层级web默认会随着手势逐页goBack而不是popVC
[ ] QMWebViewOptionEnableCloseButton        // 在返回按钮右侧增加关闭按钮，canGoBack时才会显示
[ ] QMWebViewOptionEnableLoadingProgress    // 显示顶部加载进度条

示例:
QMWebViewOption options = QMWebViewOptionDefault | QMWebViewOptionEnableCloseButton | QMWebViewOptionEnableLoadingProgress;
 
Plugin Example:

@interface QMWebViewXXXPlugin : QMWebViewBasePlugin <QMWebViewPluginProtocol, QMWebViewDelegate>
@end

@implementation QMWebViewAlertPlugin
- (void)integrate {
    // do something
}

// implement QMWebViewDelegate as needed

@end

*/
