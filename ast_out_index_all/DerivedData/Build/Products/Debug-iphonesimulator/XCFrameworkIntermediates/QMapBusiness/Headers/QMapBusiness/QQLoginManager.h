//
//  QQLoginManager.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 11-9-27.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <QMapMiddlePlatform/JsonPOIInfo.h>
#import <QMapFoundation/QMapFoundation.h>
#import "QMLoginFailureContext.h"

#define QMQQLoginDidReceiveResultNotification @"QMQQLoginDidReceiveResultNotification"
#define QMQQLoginQuickLoginCancelNotification @"QMQQLoginQuickLoginCancelNotification"

#define QMQQLogin_NOT_LOGIN @"账号登录"
#define kWTLoginSDKIdentifier @"WT711048601"

typedef enum StateNetRequest_ {
    Sta_loginMapServer = 0,
    Sta_deletePOIsOnQQCloud,    // viton: 将本地删除的POI列表同步到QQCloud
    Sta_deletePOIsInLocal,      // viton: 将QQCloud上删除的POI列表和本地POI列表进行同步
    Sta_uploadPOIsToQQCloud,    // viton: 将本地的POIs同步到QQCloud上
    Sta_idList_downPOI,         // viton: 从QQCloud获取POIs ID List
    Sta_downloadPOIsFromQQCloud,// viton: 从QQCloud获取POIs List
    
    Sta_deleteSvPOIsOnQQCloud,  // viton: 将本地删除的svPOIs同步到QQCloud.
    Sta_deleteSvPOIsInLocal,    // viton: 获取QQCloud端svPOIs的简单信息(simpleInfo).
    Sta_downloadSvPOIsByIdList, // viton: 通过Id来获取svPOIs.
    Sta_uploadSvPOIsToQQCloud,  // viton: 将本地的svPOIs同步到QQCloud.
    Sta_updateSVPOIsToQQCloud,  // viton: 将被编辑过的svPOIs信息更新到QQCloud.
    
    Sta_FinishAll
} StateNetRequest;

typedef int (*FLoginCallback)(id pObject,NSData *data,NSError *pError);

@interface QQLoginManager : NSObject {

    unsigned long long uin_;
    
    NSString *sessionID_;
    NSString *userID_;
    
    StateNetRequest staNetRequest_;
    FLoginCallback callBack_;
    id callBackObj_;
}

@property (nonatomic, assign) StateNetRequest staNetRequest;

+ (QQLoginManager *)defaultLoginManager;

- (NSDictionary *)idListFromQQCloud;

- (BOOL)batchDeleteFromQQCloud:(NSArray *)array;
- (NSArray *)batchGetPOIFromQQCloud:(NSArray *)idList;

- (void)deleteStreetViewPOIFromQQCloud:(NSArray *)idList;
- (void)getStreetViewPOISimpleListFromQQCloud;

- (void)getStreetViewPOIAllListFromQQCloudByIds:(NSArray *)idList;
- (void)uploadNewStreetViewPOIWithAllList:(NSArray *)list;
- (void)updateStreetViewPOIWithUpdateReqInfoList:(NSArray *)reqInfoList;

- (void)cancelRequest;
- (void)setCallBack:(FLoginCallback)aCallBack aObject:(id)aObject;

@end
