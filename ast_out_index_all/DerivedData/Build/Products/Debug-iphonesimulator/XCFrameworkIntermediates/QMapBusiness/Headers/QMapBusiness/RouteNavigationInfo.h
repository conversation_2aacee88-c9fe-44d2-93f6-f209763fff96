//
//  RouteNavigationInfo.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/22/15.
//  Copyright (c) 2015 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface RouteNavigationInfo : NSObject

//是否动态换路
@property (nonatomic, assign) BOOL isBetterRoute;

//是否接受动态换路
@property (nonatomic, assign) BOOL isAcceptBetterRoute;

//路线ETA
@property (nonatomic, assign) int routeEta;

//是否到达终点
@property (nonatomic, assign) BOOL isArriveEnd;

//偏航次数
@property (nonatomic, assign) int outWayTime;

//路线原始距离
@property (nonatomic, assign) int routeDistance;

//结束导航位置是否在终点附近
@property (nonatomic, assign) BOOL isNear;

//导航路线最后一个routeid
@property (nonatomic, copy) NSString* routeid;

//导航终点的uid，可以为空
@property (nonatomic, copy) NSString* end_poi_id;

//设置终点的uid，可以为空
@property (nonatomic, copy) NSString* dest_poi_id;

//设置终点的lat，可以为空
@property (nonatomic, assign) double dest_location_lat;

//设置终点的lon，可以为空
@property (nonatomic, assign) double dest_location_lon;

//设置终点的name，可以为空
@property (nonatomic, copy) NSString* dest_name;

//设置终点的city，可以为空
@property (nonatomic, copy) NSString* dest_city;

//设置终点的address，可以为空
@property (nonatomic, copy) NSString* dest_address;

//导航session id
@property (nonatomic, copy) NSString* session_id;

@end
