//
//  QMWalkTrackAchievementManager.h
//  QMapBusiness
//
//  Created by z<PERSON><PERSON><PERSON><PERSON> on 2022/8/6.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import "QMTrackAchievementManager.h"
#import "QMCSTTrackDataService.h"
#import "QMTrackAchievementManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWalkTrackAchievementManager : QMTrackAchievementManager

QMDefaultManager_H(QMWalkTrackAchievementManager);

// 生成步行轨迹成就,将成就写入轨迹，如果用户同意上云，同时把成就上传后台
- (void)generateWalkTrackAchievements;

// 处理新生成的步行轨迹；
- (QMTravelRecord *)handleNewWalkTrack:(QMTravelRecord *)trackData;

- (void)deleteWalkTrackAchievementsAndDataUUID;

- (NSDictionary *)getWalkTrackAchievementsDataUUID;

// 获取步行轨迹成就
- (NSDictionary *)getWalkTrackAchievements;

@end

NS_ASSUME_NONNULL_END
