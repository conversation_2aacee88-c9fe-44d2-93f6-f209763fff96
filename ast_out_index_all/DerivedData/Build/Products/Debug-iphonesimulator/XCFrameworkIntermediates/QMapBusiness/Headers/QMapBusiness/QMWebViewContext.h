//
//  QMWebViewContext.h
//  QMapBusiness
//
//  Created by lidazhu on 2021/9/15.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMWebViewDelegateDispatcher.h"
#import <WebKit/WebKit.h>
#import "QMWebViewDefines.h"
#import "QMWebViewLoader.h"
#import <QMapMiddlePlatform/QMNavigationBar.h>
#import "QMWebViewMessageHandler.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWebViewContext : NSObject

@property (nonatomic, copy) NSString *url;
@property (nonatomic, weak) UIViewController *vc;
@property (nonatomic, weak) UIView *containerView;
@property (nonatomic, weak) WKWebView *webView;
@property (nonatomic, weak) QMWebViewLoader *loader;
@property (nonatomic, weak) QMWebViewDelegateDispatcher *delegateDispatcher;
@property (nonatomic, assign) QMNavigationBarStyle barStyle;
@property (nonatomic, assign) QMWebViewOption options;
@property (nonatomic, copy) NSString *placeholderTitle;
@property (nonatomic, weak) QMNavigationBar *navigationBar;
@property (nonatomic, weak) QMWebViewMessageHandler *jsMessageHandler;
@property (nonatomic, weak) id<QQJSWebViewProtocol> jsImp;
@property (nonatomic, strong) NSMutableDictionary *observerDict;

@end

NS_ASSUME_NONNULL_END
