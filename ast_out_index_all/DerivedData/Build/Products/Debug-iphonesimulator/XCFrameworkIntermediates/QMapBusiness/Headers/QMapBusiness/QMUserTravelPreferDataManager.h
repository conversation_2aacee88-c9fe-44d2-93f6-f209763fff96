//
//  QMUserTravelPreferDataManager.h
//  SOSOMap
//
//  Created by wyh on 2020/12/28.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXTERN NSString * const QMHUserTravelPreferChangedNotification;

typedef NS_ENUM(NSUInteger, QMapUserTravelPreference) {
    QMapUserTravelPreferenceUnknow = 0,
    QMapUserTravelPreferenceBus = 1,
    QMapUserTravelPreferenceDriver = 2,
};

@interface QMUserTravelPreferDataManager : NSObject

+ (instancetype)defaultManager;

- (void)setUserTravelPreference:(QMapUserTravelPreference)userPreference;
- (QMapUserTravelPreference)userTravelPreference;
- (NSString *)userTravelPreferenceStr;

@end

NS_ASSUME_NONNULL_END
