//
//  QQSDKManager.h
//  SOSOMap
//
//  Created by nopwang on 1/24/13.
//  Copyright (c) 2013 nopwang. All rights reserved.
//

#import <Foundation/Foundation.h>

#define kQQSDKIdentifier @"100379435"
#define KQQSDKKEY  @"daca303318ac485eb4a973ffd047aa5f";
#define kQQSDKIdentifierTmp @"QQ05FBAB2B"
#define kQQSDKIdentifierNew @"tencent100379435"  // QQ更新了协议，为了兼容老版本QQ，在此增加新的Identifier

#define QMMobileQQDidFinishSendingNotification @"QMMobileQQDidFinishSendingNotification"

typedef enum : NSInteger {
    QQSuccess           = 0,
    QQErrCodeUserCancel = -4,
} QQErrCode;

@interface QQSDKManager : NSObject

@property(nonatomic, readonly) BOOL isAvailable;

QMDefaultManager_H(QQSDKManager)

+ (void)initWhenApplicationDidFinishLaunch;
+ (void)performTaskWhenMapDidDrawOrTimeOut;

- (BOOL)registerAPI;

- (BOOL)handleOpenUniversalLink:(NSUserActivity *)userActivity;

- (void)sendNewsWithTitle:(NSString *)title description:(NSString *)desc thumbImage:(UIImage *)thumb wapURL:(NSString *)wap;
- (void)sendImageWithTitle:(NSString*)title Image:(UIImage*)image withRatio:(NSNumber*)ratio;

/** 分享小程序给 QQ 好友
 * @param appID 小程序标识,
 * @param path 小程序页面路径, 也可以传参数, 如: @"?foo=bar"
 * @param title 标题
 * @param text 文案
 * @param image 图片
 * @param wapURL H5页面, 用于适配打不开小程序的版本
 * @param miniprogramType  开发版 0      测试版 1     正式版 3    预览版 4
 */
- (void)sendMiniProgramWithAppID:(NSString *)appID
                            path:(NSString *)path
                           title:(NSString *)title
                            text:(NSString *)text
                           image:(UIImage *)image
                          wapURL:(NSString *)wap
                 miniprogramType:(NSInteger)miniprogramType;

- (void)handleBackFromQQWithURL:(NSURL *)url;
- (void)loinQQWithOAuth;
- (void)logoutQQSdk;
- (void)bindQQ;
@end
