//
//  QMWeakSignalGPSHandler.h
//  SOSOMap
//
//  Created by sarah on 2017/4/17.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "NavigationCommonProtocol.h"

typedef enum : NSUInteger {
    QMWeakSignalGPSTypeNone,   //恢复正常播报
    QMWeakSignalGPSTypeLevel1, //120秒及以内的播报
    QMWeakSignalGPSTypeLevel2, //180s及以后的播报
} QMWeakSignalGPSType;

//弱信号的具体细分场景
typedef enum : NSUInteger {
    QMWeakSignalSceneNone,                //恢复正常播报
    QMWeakSignalSceneIndoor,              //在室内
    QMWeakSignalSceneInTunnel,            //在隧道内
    QMWeakSignalSceneInOtherOutdoorCoveredArea,  //在其他室外遮挡区域
    QMWeakSignalSceneInOther,             //其他弱信号场景
} QMWeakSignalScene;

typedef enum : int64_t {
    QMLocationAnomalySceneNone = 0,                  //什么都没有
    QMLocationAnomalySceneGnssErr = 1,               //GNSS异常重启手机
    QMLocationAnomalySceneDirSensorLoss = 1 << 1,    //方向传感器缺失
    QMLocationAnomalySceneDirCalibration = 1 << 2,   //方向8字校准
    QMLocationAnomalySceneGnssWeakCommon = 1 << 3,   //GNSS信号弱（通用）
    QMLocationAnomalySceneGnssWeakIndoor = 1 << 4,   //GNSS信号弱（室内）
    QMLocationAnomalySceneGnssWeakTunnel = 1 << 5,   //GNSS信号弱（隧道）
    QMLocationAnomalySceneGnssWeakBuilding = 1 << 6, //GNSS信号弱（楼宇遮挡）
}LocationAnomalyScene;

typedef enum :int {
    kGPSSignalStateInitial,//初始状态
    kGPSSignalStateValid,//有效
    kGPSSignalStateInValid,//无效
}kGPSSignalState;

//播报+置灰
@protocol QMWeakSignalGPSHandlerDelegate;
@interface QMWeakSignalGPSHandler : NSObject<LocationExcepSceneProtocol>

@property (nonatomic, weak) id<QMWeakSignalGPSHandlerDelegate>delegate;
//普通步导和AR步导跳转的时候不需要重新start，该变量用来控制是否重新start
@property (nonatomic, assign, readonly) BOOL isStarted;

/// 弱信号提醒的时机配置
@property (nonatomic, assign, readonly) NSInteger weakSignalRemindTime;
//持续处于室内或者持续处于隧道里的播报次数
@property (nonatomic, assign, readonly) NSInteger indoorTunnelBroadcastCount;

//策略启动
- (void)start:(int64_t)sceneType;
//策略终止
- (void)stop;
//设置GPS信号状态
- (void)setState:(kGPSSignalState)state;
@end

@protocol QMWeakSignalGPSHandlerDelegate <NSObject>

@optional
//当前定位可用状态
- (void)currentLocationStatus:(int64_t)sceneType;
//param:none代表恢复正常,level1代表120秒及以内的播报,level2代表180s及以后的播报
//return:0:失败，1成功

//param:弱信号下的不同场景，1、室内 2、室外遮挡（i）隧道（ii）其他室外遮挡区域 3、其他弱信号场景
//return:0:失败，1成功
- (int)monitorGpsSignalSceneStatus:(QMWeakSignalScene)weakSceneType;

- (void)restartReminder;
- (int)directionSensorMiss;
@end
