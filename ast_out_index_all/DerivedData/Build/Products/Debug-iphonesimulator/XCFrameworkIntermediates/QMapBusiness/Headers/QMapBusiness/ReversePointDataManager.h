//
//  LongPressDataManager.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/15/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMNReverseGeoCoder.h"


#define QMReversePointChangedNotification @"QMReversePointChangedNotification"

@interface ReversePointDataManager : NSObject<QMNReverseGeocoderDelegate> {
    
    JsonPOIInfo *reversePoint_;
}

@property (nonatomic, strong) JsonPOIInfo *reversePoint;
@property (nonatomic, assign, readonly) BOOL isReversePointValid;

QMDefaultManager_H(ReversePointDataManager)

- (void)search:(DMMapPoint)coordinate animated:(BOOL)animated containerMapview:(DMMapView *)containerMapview;
- (void)clear;
- (void)replaceReversePointWithPOI:(JsonPOIInfo *)poi;
- (void)replaceReversePointWithPOI:(JsonPOIInfo *)poi containerMapView:(DMMapView *)containerMapView;
@end
