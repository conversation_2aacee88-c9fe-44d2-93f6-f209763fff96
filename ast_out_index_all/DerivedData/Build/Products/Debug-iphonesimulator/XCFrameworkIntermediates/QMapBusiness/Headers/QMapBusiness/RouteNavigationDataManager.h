//
//  RouteGuidanceDataManager.h
//  SOSOMap
//
//  Created by nopwang on 11/22/12.
//  Copyright (c) 2012 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>
#import <QMapFoundation/QMapFoundation.h>
#import <QMapNaviKit/QMapDriveNaviManager+TencentMap.h>

#import "RouteNavigationInfo.h"
#import "DriveNaviSessionInfo.h"
#import "QMCTRHandler.h"
#import "QMNWalkCycleNaviBaseViewController.h"

@interface RouteNavigationDataManager : NSObject

@property (nonatomic, readonly) BOOL isDriveNavigating;//正在导航，GPS导航or模拟导航
@property (nonatomic, readonly) BOOL isSimulateNav; //正在模拟导航
@property (nonatomic, readonly) BOOL isINS;//正在隧道导航
@property (nonatomic, assign) BOOL isSmartLocStatus; //是否在智能定位中

@property (nonatomic, readonly) NSString *routeId;//偏航也只用第一次导航的routeId
@property (nonatomic, weak) QMapDriveNaviManager *driveNaviManager;
@property (nonatomic, weak) QMapWalkCycleNaviManager * walkCycleNaviManager;
@property (nonatomic, assign,readonly) int totalDrivedDistance;
@property (nonatomic, assign) NSUInteger matchedPointIndex;//偏航时的点索引,在离线算路里有用到
@property (nonatomic, strong) RouteNavigationInfo *navigationInfo;//记录导航中的一些信息，在行车小节上传中使用
@property (nonatomic, strong) DriveNaviSessionInfo *driveNaviSessionInfo;
@property (nonatomic, strong) QMCTRHandler *CTRHandle;
@property (nonatomic, strong) QMRoute * firstRoute; //导航开始的第一条数据,didStart中返回的
@property (nonatomic) BOOL isAutoDectionExit;
@property (nonatomic) TravelType walkCycleNaviType;


QMDefaultManager_H(RouteNavigationDataManager)
+ (void)initWhenApplicationDidFinishLaunch;
+ (void)performTaskWhenMapDidDrawOrTimeOut;
+ (void)performTaskWhenMainSceneWillEnterForeground;

//强制结束导航
- (void)forceStopNavigation;

- (int)getCurrentMileage;
//是否有异常退出的导航行为
- (BOOL)hasInterruptedNaviSession;
- (void)clearInterruptedNaviSession;
- (BOOL)saveNaviSession;
- (BOOL)loadNaviSession;

@end  //RouteNavigationDataManager

