//
//  StreetViewController.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12-3-18.
//  Copyright 2012 Tencent. All rights reserved.
//
  
#import <QMapMiddlePlatform/QMNavigationBar.h>
#import "JsonStreetviewPOIInfo.h"
#import <QStreetView/QStreetView.h>


#define MAX_ID_COUNT 6

@class QStreetView;
@class JsonPOIInfo;

@interface StreetViewController : QMViewController <QStreetViewDelegate> {
    QStreetView *streetView_;
    QMNavigationBar *navigationBar_;
    UIActivityIndicatorView *waitingView_;	
    UIView *guide_;
    
    UIImageView *logoImgView_;
    UIImageView *holdImgView_;
    
    /**walk navigation**/
//    IBOutlet UIView *_miniMapContainerView;
//    IBOutlet UIImageView *_miniMapMarkerImageView;
    UIButton *_miniMapViewButton;

     //tbd 后续优化，精简以下变量控制
    BOOL isShowWaitingView_;
    BOOL isHidingNavigator_;//正在隐藏状态时，不能退出
    BOOL isClosing_;//正在退出时，不能响应隐藏状态栏操作
    BOOL isOpeningShare_;//
    BOOL isPauseDeviceMotion_;
    NSString *sStreetViewId_;
    UIViewController *parentViewController_;
}
@property(nonatomic, strong)JsonPOIInfo *poi;
@property(nonatomic, strong)JsonPOIInfo *clickablePOI;
@property(nonatomic, assign)BOOL showDetailPageAfterLoad;
@property(nonatomic, assign)BOOL isFromWAP;
@property(nonatomic, strong) UIButton* miniMapViewButton;
@property (nonatomic, strong) UIView *miniMapContainerView;
@property (nonatomic, strong) UIImageView *miniMapMarkerImageView;
@property (nonatomic, assign) BOOL forceDisableQuietBtn;

- (void)start:(UIViewController*)vc streetViewId:(NSString*)streetViewId;
- (void)start:(UIViewController*)vc streetViewId:(NSString*)streetViewId pitchAngle:(float)pitchAngle yawAngle:(float)yawAngle streetPOI:(JsonStreetviewPOIInfo *)streetPOI;
- (void)startWithPOI:(JsonPOIInfo *)poi fromViewController:(UIViewController *)controller;
- (void)startParkViewWithPOI:(JsonPOIInfo *)poi fromViewController:(UIViewController *)controller;
 
- (void)startWithPOIAndAngle:(JsonPOIInfo *)poi fromViewController:(UIViewController *)controller pitchAngle:(float)pitchAngle yawAngle:(float)yawAngle;

- (void)start:(UIViewController*)vc streetViewId:(NSString*)name svid:(NSString *)svid pointx:(NSString *)pointx pointy:(NSString *) pointy streetPOI:(JsonStreetviewPOIInfo *)streetPOI;

- (void)streetView:(QStreetView *)streetview showParkView:(BOOL)show;

@end
