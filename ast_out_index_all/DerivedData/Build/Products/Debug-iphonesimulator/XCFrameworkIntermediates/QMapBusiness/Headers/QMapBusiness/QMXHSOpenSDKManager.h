//
//  QMXHSOpenSDKManager.h
//  QMapBusiness
//
//  Created by alex<PERSON><PERSON> on 2023/4/18.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMXHSOpenSDKManager : NSObject

QMDefaultManager_H(QMXHSOpenSDKManager)

+ (void)initWhenApplicationDidFinishLaunch;
+ (void)performTaskWhenMapDidDrawOrTimeOut;

- (BOOL)registerAPI;

- (BOOL)isXHSAvailable;

- (BOOL)handleURL:(NSURL *)url;

- (BOOL)handleOpenUniversalLink:(NSUserActivity *)userActivity;

/** 分享图片到小红书 图片限制最多9张 无大小限制
 *  @param  title   标题
 *  @param  imageArray图片数组 内容可以是UIImage类型 或者NSString类型 图片在系统相册的标识
 *  @param  completion  completion
 */
- (void)sendImageWithTitle:(NSString *)title
                   content:(NSString *)content
                imageArray:(NSArray *)imageArray
                  complete:(void(^)(BOOL success))completion;

/** 分享视频到小红书 视频时长限制在15min之内
 *  @param  title   标题
 *  @param  identifier 视频在系统相册的标识
 *  @param  completion  completion
 */
- (void)sendVideoWithTitle:(NSString *)title
                   content:(NSString *)content
           videoIdentifier:(NSString *)identifier
                  complete:(void(^)(BOOL success))completion;

@end

@interface QMXHSOpenSDKManager (localIdentifier)

/** 批量保存图片到系统相册
 *  @param  images  要保存到图片UIImage对象数组
 *  @param  completion  completion
 */
- (void)saveImagesToAlbum:(NSArray<UIImage *> *)images
                 complete:(void(^)(NSArray<NSString *> *localIdentifiers))completion;

@end


NS_ASSUME_NONNULL_END
