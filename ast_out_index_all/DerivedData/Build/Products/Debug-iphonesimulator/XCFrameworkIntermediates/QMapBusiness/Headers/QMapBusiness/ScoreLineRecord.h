#ifndef SCORE_LINE_RECORD_H
#define SCORE_LINE_RECORD_H
#import <QMapNaviKit/GpsCommonDefine.h>
#include <stdio.h>

/**
 * 规划路线数据记录，数据格式：
 * HEADER + DATA
 * HEADER 24 bytes :
 * 	flag, 4 bytes, fixed:TXLB
 * 	file type, 1 bytes,fixed:s
 * 	sys flag,1 bytes, 1 android,2 ios
 * 	ver, 1 byte
 * 	isencode,1 byte,1 encode,0 none
 * 	encode key,4 bytes
 * 	remain,11 bytes
 *
 * DATA:
 * 	point size, 4 bytes
 * 	event size, 4 bytes
 * 	point data,point size x 4 x 2 byts
 * 	event data,event size x 4 x 3 bytes
 *
 *
 * 	写文件时，必须先把所有的点数据写完后，再写所有的事件数据
 * 	读文件时，必须先把所有的点数据读完后，再读事件数据
 */

class ScoreLineRecord{

private:
	FILE* file;
	int pointSize;
	int eventSize;
	bool curIsRead;
	int tmpPointSize;
	int tmpEventSize;

public:
	int openLineFile(const char* fileName,bool isRead,int sysFlag);

	int appendPointData(double lon,double lat);

	int appendEventData(int color,int from,int to);

	int closeLineFile();

	int getOnePoint(ScoreLinePoint* point);

	int getOneEvent(ScoreLineEvent* event);


};

#endif
