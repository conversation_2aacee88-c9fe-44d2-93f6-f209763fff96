//
//  RouteSearchDefine.h
//  SOSOMap
//
//  Created by sarah on 2017/5/26.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/RouteSearcherConstant.h>

#import "QMLocalRouteSearcher.h"
#import <QMapMiddlePlatform/RouteReqParam.h>
#import <QMapMiddlePlatform/QMPointInfo.h>

// 用于步骑行公交算路结果
typedef NS_ENUM(int, RouteErrorType) {
    kErrorTypeTooShort = 3,
    kErrorTypeTooLong = 4,
    kErrorTypeCrossCity = 10007,
    kErrorTypeCityNotSupport = 11003,
    kErrorTypeNoResult1 = 11007,
    kErrorTypeNoResult2 = 11008,
};

@interface RouteSearchDefine : NSObject
+ (BOOL)mapPointIsZero:(DMMapPoint)mp;
+ (void)replaceWithCurrentLocation:(QMPointInfo *)poi;

//QM_TRAVEL_WAY_WALK_STR,QM_TRAVEL_WAY_CYCLE_STR,QM_TRAVEL_WAY_BUS_STR,QM_TRAVEL_WAY_CAR_STR
+ (QRouteReqParam*)getSpecificParamInstance:(NSString*)type;

+ (NSUInteger)indexOfRouteFromResult:(QRouteResult *)result routeid:(NSString *)routeid;

@end

@interface RouteSearchDefine (TravelWay)
//QM_TRAVEL_WAY_WALK_STR,QM_TRAVEL_WAY_CYCLE_STR,QM_TRAVEL_WAY_BUS_STR,QM_TRAVEL_WAY_CAR_STR
// 非同城 tab存储

//获取上一次默认选择 tab
+ (NSString *)getCurrentTravelWay;
+ (NSString *)getCustomTravelWay:(NSString *)way;

/// 是否忽略通勤方式的设置，type传什么就是什么
/// - Parameters:
///   - way: 传过来的导航方式
///   - force: 是否忽略通勤配置
+ (NSString *)getCustomTravelWay:(NSString *)way forceType:(BOOL)force;
+ (void)updateCurrentTravelWay:(NSString *)travelWay;

// 非同城（跨城）
+ (void)saveAcrossCityTime;
+ (NSInteger)getAcrossCityTime;

//非同城 tab存储
+ (NSString *)getAcrossCityTab;
+ (void)saveAcrossCityTab:(NSString *)trvaelWay;


//同城 tab存储
+ (void)saveInSameCityTab:(NSString *)travelWay;
+ (NSString *)getInSameCityTab;

+ (void)saveInSameCityTime;
+ (NSInteger)getInSameCityTime;

@end

@interface RouteSearchDefine (SmartVoice)
+ (NSString *)getTravelWayWithNavigationType:(NSString *)navigationType;
@end

@interface RouteSearchDefine (FilterGPS)

+ (NSArray *)getFilterGPSDataForTravelWay:(NSString *)way atScene:(NSString *)reason;

@end
