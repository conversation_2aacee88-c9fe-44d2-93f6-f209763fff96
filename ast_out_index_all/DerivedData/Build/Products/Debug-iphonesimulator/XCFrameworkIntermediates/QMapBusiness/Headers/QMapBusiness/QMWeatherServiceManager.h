//
//  QMWeatherServiceManager.h
//  QMapBusiness
//
//  Created by 张敬民 on 2022/1/6.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMFavoriteSynchronizeToCloudAuthorizeView.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMWeatherServiceAuthorizationStatus;

@interface QMWeatherServiceManager : NSObject

QMDefaultManager_H(QMWeatherServiceManager)

///首页是否播放了全屏pag
@property (nonatomic) BOOL hasPlayPag;

- (void)showWeatherServiceH5View;

- (QMFavoriteSynchronizeToCloudAuthorizeStyleInfo *)getInfoFromApollo;

@end

NS_ASSUME_NONNULL_END
