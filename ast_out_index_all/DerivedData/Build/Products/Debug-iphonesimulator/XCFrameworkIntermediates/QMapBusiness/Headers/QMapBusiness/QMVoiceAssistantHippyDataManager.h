//
//  QMVoiceAssistantHippyDataManager.h
//  QMapBusiness
//
//  Created by 狄弘辉 on 2021/3/31.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class JsonBuslineInfo;
@class JsonPOIInfo;

typedef NSString * const QMVoiceAssistantHippyPageName;
extern QMVoiceAssistantHippyPageName QMHippyPageNameLoading;
extern QMVoiceAssistantHippyPageName QMHippyPageNameList;
extern QMVoiceAssistantHippyPageName QMHippyPageNameDetail;
extern QMVoiceAssistantHippyPageName QMHippyPageNameMultiCity;
extern QMVoiceAssistantHippyPageName QMHippyPageNameDetailBus;
extern QMVoiceAssistantHippyPageName QMHippyPageNameSelectedPoiDetail;

typedef NS_ENUM(NSInteger, QMVoiceAssistantAskType) {
    QMVoiceAssistantAskTypeNone = 0,
    QMVoiceAssistantAskTypeIndex,
    QMVoiceAssistantAskTypeGoThere,
};

@class QMVoiceBoyDataModel,QMVoiceBoyVisibleItemModel,QMHippyPoiSearchSemanticManager;
@interface QMVoiceAssistantHippyDataManager : NSObject

QMDefaultManager_H(QMVoiceAssistantHippyDataManager)
 
@property (nonatomic, strong) JsonPOIInfo *currentHippyPoi;

/** 记录是否是hippy视图*/
@property (nonatomic, assign) BOOL hasEnteredHippy;
/** 语义处理都先走切词逻辑，如果切词成功，则用block透传状态*/
@property (nullable, nonatomic, copy) void(^hippyVoiceSetSecondRoundBlock)(BOOL result);
/** hippy通知叮当当前可视范围内数据的处理block*/
@property (nullable, nonatomic, copy) void(^hippyVoiceVisibleDataCallBack)(NSArray<QMVoiceBoyVisibleItemModel *> *visibleData);
/** hippy通知叮当当前POI数据的处理block*/
@property (nullable, nonatomic, copy) void(^hippyVoiceCurrentPoiCallBack)(JsonPOIInfo *info);

/**TMPoiSearchModule Call*/
- (void)handleUpdateSearchResponse:(NSDictionary *)params;

/**TMRouterModule Call*/
- (void)updatePageName:(NSString *)pageName;

- (NSString *)currentPageName;

/**Hippy VoiceAssistant Case*/
- (void)handleMultiVoiceAssistantSearchResult:(QMVoiceBoyDataModel *)model complete:(void(^)(QMVoiceAssistantAskType type))completeBlock;

- (void)handleSingleVoiceAssistantSearchResult:(QMVoiceBoyDataModel *)model complete:(void(^)(QMVoiceAssistantAskType type))completeBlock;

- (void)handleLaHeiSearchResult:(QMVoiceBoyDataModel *)model;

- (void)handleVoiceAssistantSpecificPoiInfo:(QMVoiceBoyVisibleItemModel *)model
    finishBlock:(void(^)(BOOL finished))finishBlock;

- (void)handleVoiceAssistantUnknown;

- (void)handleVoiceAssistantPoiNoResult;

- (void)handleVoiceAssistantChangeRegion;

- (void)handleVoiceAssistantChangeCity;

/**Data Parse*/
+ (JsonPOIInfo *)convertTPoi2JsonPoiInfo:(NSDictionary *)tPOI;

+ (JsonPOIInfo *)convertTCity2JsonPoiInfo:(NSDictionary *)tCity;

+ (JsonBuslineInfo *)convertLine2JsonBuslineInfo:(NSDictionary *)busline;

/**Hippy View Box*/
- (void)launchHippyViewBoxWithParams:(NSDictionary *)params;

- (void)closeHippyViewBox;

- (void)addSubviewForHippyViewBox;

/**QMHippyPoiSearchSemanticManager register*/
- (void)registerSemanticManagerDelegate;

- (void)unregisterSemanticManagerDelegate;

/**Defense Code*/
- (void)handleNotReceiveHippyResponse;

@end

NS_ASSUME_NONNULL_END
