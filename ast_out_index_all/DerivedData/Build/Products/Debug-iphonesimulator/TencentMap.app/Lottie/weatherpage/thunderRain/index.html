<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="x5-orientation" content="portrait" />
    <meta name="screen-orientation" content="portrait" />
    <meta name="360-fullscreen" content="true" />
    <meta name="full-screen" content="true" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title>积水地图</title>
    <style>
      html {
        font-size: 16px;
      }
      body {
        background-color: rgba(44, 58, 90, 0.35);
      }
      * {
        margin: 0;
        padding: 0;
      }

      /* .slideshow {
            position: relative;
            overflow: hidden;
        }

        .images {
            background: url("yun.png");
            position: absolute;
            right: 0;
            top: 0;
            height: 218px;
            width: 300%;
            animation: slideshow 10s linear infinite;
        }

        @keyframes slideshow {
            0% {
                right: 0;
            }

            100% {
                right: -200%;
            }
        } */

      .tech-slideshow {
        height: 234px;
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        overflow: hidden;
        margin-top: 10px;
      }

      .tech-slideshow > div {
        height: 234px;
        width: 2526px;
        background: url(yun.png);
        background-size: 540px 234px;
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
      }

      .mover-1 {
          animation: moveSlideshow 60s linear infinite;
          -webkit-animation: moveSlideshow 60s linear infinite;
      }

      .tech-slideshow .mover-2 {
        opacity: 0;
        transition: opacity 0.5s ease-out;
        background-position: 0 -200px;
        animation: moveSlideshow 20s linear infinite;
      }

      .tech-slideshow:hover .mover-2 {
        opacity: 1;
      }

      @keyframes moveSlideshow {
        100% {
          transform: translateX(-66.6666%);
          -webkit-transform: translateX(-66.6666%);
        }
      }

      .canvas_container {
        position: absolute;
        width: 100%;
        top: 160px;
        bottom: 0;
        overflow: scroll;
        overflow-y: hidden;
      }

      @keyframes flash {
        0% {background-color: white; opacity: 0;}
        9% {background-color: white; opacity: 0.3;}
        27% {background-color: white; opacity: 0.3;}
        32% {background-color: white; opacity: 0;}
        36% {background-color: white; opacity: 0;}
        41% {background-color: white; opacity: 0.3;}
        91% {background-color: white; opacity: 0.3;}
        100% {background-color: white; opacity: 0;}
      }

      .flash_container {
        animation: flash 0.73s linear 2s 1 normal;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
      }
    </style>
  </head>

  <body>
    <div class="flash_container"></div>
    <div class="canvas_container">
      <canvas id="canvas" style="height:130%; width:100%;"></canvas>
    </div>
    
    <div class="tech-slideshow">
        <div id='tech-slideshow-mover-1' class='mover-1'></div>
    </div>
    <script>
      const isIphonex = () => {
          if (typeof window !== 'undefined' && window) {
              return /iphone/gi.test(window.navigator.userAgent) && window.screen.height >= 812;
          }
          return false;
      }
      if (isIphonex()) {
          var cloud = document.getElementsByClassName('tech-slideshow')[0];
          cloud.style.marginTop = '40px';
      }
      const isIphone = () => {
          if (typeof window !== 'undefined' && window) {
              return /iphone/gi.test(window.navigator.userAgent);
          }
          return false;
      }
      if (isIphone()) {
          // 解决ios10云不动的问题
          document.getElementById('tech-slideshow-mover-1').className = "";
          setTimeout(function(){ 
              document.getElementById('tech-slideshow-mover-1').className = "mover-1";
          }, 100);
      }
  </script>
    <!-- <script src="cloud.js"></script> -->
    <script src="rain.js"></script>
    <script src="lightning.js"></script>
    <script>
      startRain();
      // let lightOptions = '{"xRange":[5,30],"yRange":[5,25],"pathLimit":[10,35],"lightTimeTotal":[30,100]}';
      // startLightning(lightOptions);
      startFlash();
    </script>
  </body>
</html>
