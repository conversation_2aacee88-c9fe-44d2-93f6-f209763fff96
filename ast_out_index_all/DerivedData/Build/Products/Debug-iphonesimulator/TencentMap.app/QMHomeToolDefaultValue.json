{"recent": ["car", "taxi", "bus", "etabus", "cycle", "walk", "offline", "buscode", "subway", "locationShare"], "groups": [{"name": "出行", "tools": [{"isCheckLogin": false, "name": "car", "title": "驾车", "jump": {"url": "qqmap://map/routeplan_without_endpoint?type=drive&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "taxi", "title": "打车", "jump": {"url": "qqmap://map/mippy?moduleName=taxi&appName=OrderIndex&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "bus", "title": "公交地铁", "jump": {"url": "qqmap://map/routeplan_without_endpoint?type=bus&keepui=1&source=1", "type": 1}}, {"isCheckLogin": false, "name": "etabus", "title": "实时公交", "jump": {"url": "qqmap://map/mippy?moduleName=realtimebus&appName=HomePage&statusBar=dark&animationType=right&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "cycle", "title": "骑行", "jump": {"url": "qqmap://map/routeplan_without_endpoint?type=cycle&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "walk", "title": "步行", "jump": {"url": "qqmap://map/routeplan_without_endpoint?type=walk&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "offline", "title": "离线地图", "jump": {"url": "qqmap://map/offlinemap?keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "buscode", "title": "乘车码", "jump": {"url": "qqmap://map/homepage?action=buscode&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "subway", "title": "地铁图", "jump": {"url": "qqmap://map/gotosubway?keepui=1", "type": 1}}, {"isCheckLogin": true, "name": "locationShare", "title": "地铁图", "jump": {"url": "qqmap://map/mippy?moduleName=sharePosition&appName=Index&animationType=right&fromPage=homepage&keepui=1", "type": 1}}]}, {"name": "车主", "tools": [{"isCheckLogin": true, "name": "violation", "title": "违章查询", "jump": {"url": "qqmap://map/peccancy?keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "edog", "title": "电子狗", "jump": {"url": "qqmap://map/edog?keepui=1", "type": 1}}]}, {"name": "腾讯特色", "tools": [{"isCheckLogin": false, "name": "theme", "title": "特色主题", "jump": {"url": "qqmap://map/themeCenter?tab=1&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "gift", "title": "活动专区", "jump": {"url": "qqmap://map/activityarea?keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "voice", "title": "导航语音", "jump": {"url": "qqmap://map/navigationvoice?page_item=1&keepui=1", "type": 1}}, {"isCheckLogin": false, "name": "signin", "title": "签到", "jump": {"url": "qqmap://map/fly?page=QMPersonalPoint", "type": 1}}]}]}