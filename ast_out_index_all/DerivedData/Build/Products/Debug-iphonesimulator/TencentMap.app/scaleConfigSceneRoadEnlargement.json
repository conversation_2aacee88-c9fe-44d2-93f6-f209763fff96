{"autoscales": [{"type": "hd_intersection", "navitype": 1, "priority": 1.3, "scale_enabled": true, "skew_enabled": true, "rotate_enabled": true, "center_move_switch": true, "roadclass_path": [{"roadclass_start": 0, "roadclass_end": 1, "sub_path": [{"trigger_dis": -1, "end_dis": 0, "min_scale": 17.1, "max_scale": 20.1, "min_skew": 55, "max_skew": 55, "scale_speed": 1, "skew_speed": 10}]}, {"roadclass_start": 2, "roadclass_end": 20, "sub_path": [{"trigger_dis": -1, "end_dis": 0, "min_scale": 17.1, "max_scale": 20.3, "min_skew": 55, "max_skew": 55, "scale_speed": 1, "skew_speed": 10}]}], "intersection_switch_rule": {"switch_type": 1, "best_switch_pos_horizontal": 20, "best_switch_pos_vertical": 55}, "center_move_rule": {"restore_time": 3.5, "move_end_offset": [0, 0.1], "car_starting_offset_h": [0, 0.2], "car_starting_offset_v": [0, 0.235]}}, {"type": "hd_intersection", "navitype": 1, "subtype": [60, 61, 62], "priority": 1.3, "scale_enabled": true, "skew_enabled": true, "rotate_enabled": true, "roadclass_path": [{"roadclass_start": 0, "roadclass_end": 20, "sub_path": [{"trigger_dis": 200, "end_dis": 0, "min_scale": 17.1, "max_scale": 20.3, "min_skew": 45, "max_skew": 45, "scale_speed": 2, "skew_speed": 10, "max_diff_car_rotation_angle": 80}]}], "destination_rule": {"min_scale_exec_distance": 200, "effective_distance_road2map": [0, 170]}, "intersection_switch_rule": {"switch_type": 1, "best_switch_pos_horizontal": 20, "best_switch_pos_vertical": 55}}]}