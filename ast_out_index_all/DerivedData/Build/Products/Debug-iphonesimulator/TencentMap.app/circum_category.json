{"version": 1, "categoty": [{"subcategory": [{"subname": "中餐"}, {"subname": "快餐"}, {"subname": "西餐"}, {"subname": "甜点饮品"}], "name": "美食畅饮", "desc": "日本料理, 韩国料理, 东南饭菜", "iconurl": "http://sv0.map.qq.com/street/circum_food_v1.png"}, {"subcategory": [{"subname": "快捷酒店"}, {"subname": "星级酒店"}, {"subname": "青年旅舍"}, {"subname": "宾馆"}], "name": "酒店住宿", "desc": "公寓式酒店， 经济型酒店, 旅游", "iconurl": "http://sv0.map.qq.com/street/circum_hotel_v1.png"}, {"subcategory": [{"subname": "KTV"}, {"subname": "电影院"}, {"subname": "咖啡店"}, {"subname": "洗浴足疗"}], "name": "娱乐休闲", "desc": "健身，游泳，保龄球, 足球", "iconurl": "http://sv0.map.qq.com/street/circum_happy_v1.png"}, {"subcategory": [{"subname": "公交站"}, {"subname": "地铁站"}, {"subname": "加油站"}, {"subname": "停车场"}], "name": "交通出行", "desc": "路线规划， 地点寻找， 路线导航", "iconurl": "http://sv0.map.qq.com/street/circum_travel_v1.png"}, {"subcategory": [{"subname": "超市"}, {"subname": "ATM"}, {"subname": "公厕"}, {"subname": "药店"}], "name": "生活服务", "desc": "洗衣, 美发, 家政服务, 房屋中介", "iconurl": "http://sv0.map.qq.com/street/circum_life_v1.png"}]}