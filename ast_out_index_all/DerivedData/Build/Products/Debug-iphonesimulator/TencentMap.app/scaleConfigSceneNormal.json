{
  "autoscales": [
      {"type":"hd_bridge","navitype":1,
      "priority":1.23,
      "scale_enabled":true,
      "skew_enabled":true,
      "rotate_enabled":true,
      "center_move_switch":true,
      "path_type":1,
      "roadclass_path":[
          {
          "roadclass_start":0,
          "roadclass_end": 20,
          "interval_path":[
            {
              "interval_type":2,
              "start_ratio":0,
              "start_ratio_offset": -10,
              "end_ratio_offset": 10,
              "end_ratio":1,
              "trigger_dis":-1,
              "end_dis":0,
              "min_scale":18.3,
              "max_scale":18.3,
              "min_skew":40,
              "max_skew":65,
              "scale_speed":2,
              "skew_speed":10,
              "scale_time":3,
              "skew_time":3,
              "max_diff_car_rotation_angle":80,
              "priority":1.25,
              "skew_rule":2,
              "scale_rule":{
                "type":3,
                "coefficient":0.9,
              },
              "rotation_rule":{
                "type":1,
                "animation_time":3,
                "angle_offset":[1.2,-2.0],
                "center_offset":[-0.2,0.23],
                "car_starting_offset_h":[0,0.23],
                "car_starting_offset_v":[0,0.23],
                "rotation_condition":[-10,10]
              }
            }
          ]
          }
      ],
      "collision_rule": {
          "collision_type": 1,
          "collision_exec_distance": [ {
          "roadclass_start": 0,
          "roadclass_end": 20,
          "collision_exec_distance": 150,
          "max_exec_distance": 3000,
          "min_distance_2c_scales": 10000,
          }]
      }
      },
      {
            "type": "sd_intersection",
            "navitype": 0,
            "priority": 1.5,
            "scale_enabled": true,
            "skew_enabled": true,
            "rotate_enabled": true,
            "ramp_enabled_distance": 500,
            "roadclass_path": [
              {
                "roadclass_start": 0,
                "roadclass_end": 0,
                "sub_path": [
                  {
                    "trigger_dis": -1,
                    "end_dis": 2000,
                    "min_scale": 14,
                    "max_scale": 18,
                    "min_skew": 45,
                    "max_skew": 45,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 1
                  },
                  {
                    "trigger_dis": 2000,
                    "end_dis": 600,
                    "min_scale": 14,
                    "max_scale": 18,
                    "min_skew": 45,
                    "max_skew": 45,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 2
                  },
                  {
                    "trigger_dis": 600,
                    "end_dis": 0,
                    "min_scale": 14,
                    "max_scale": 18,
                    "min_skew": 35,
                    "max_skew": 35,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 2
                  }
                ]
              },
              {
                "roadclass_start": 1,
                "roadclass_end": 1,
                "sub_path": [
                  {
                    "trigger_dis": -1,
                    "end_dis": 1000,
                    "min_scale": 15,
                    "max_scale": 18,
                    "min_skew": 45,
                    "max_skew": 45,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 1
                  },
                  {
                    "trigger_dis": 1000,
                    "end_dis": 600,
                    "min_scale": 15,
                    "max_scale": 18,
                    "min_skew": 45,
                    "max_skew": 45,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 2
                  },
                  {
                    "trigger_dis": 600,
                    "end_dis": 0,
                    "min_scale": 15,
                    "max_scale": 18,
                    "min_skew": 35,
                    "max_skew": 35,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 2
                  }
                ]
              },
              {
                "roadclass_start": 2,
                "roadclass_end": 12,
                "sub_path": [
                  {
                    "trigger_dis": -1,
                    "end_dis": 600,
                    "min_scale": 16,
                    "max_scale": 18,
                    "min_skew": 45,
                    "max_skew": 45,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 1
                  },
                  {
                    "trigger_dis": 600,
                    "end_dis": 500,
                    "min_scale": 16,
                    "max_scale": 18,
                    "min_skew": 35,
                    "max_skew": 35,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 1
                  },
                  {
                    "trigger_dis": 500,
                    "end_dis": 0,
                    "min_scale": 16,
                    "max_scale": 18,
                    "min_skew": 35,
                    "max_skew": 35,
                    "scale_speed": 3,
                    "skew_speed": 10,
                    "priority": 2
                  }
                ]
              }
            ],
            "intersection_switch_rule": {
              "switch_type": 1,
              "best_switch_pos_horizontal": 30,
              "best_switch_pos_vertical": 50
            }
          },
   {
            "type": "hd_intersection",
            "navitype": 1,
            "priority": 1.3,
            "scale_enabled": true,
            "skew_enabled": true,
            "center_move_switch": true,
            "rotate_enabled": true,
            "roadclass_path": [
                {
                    "roadclass_start": 0,
                    "roadclass_end": 1,
                    "sub_path": [
                        {
                            "trigger_dis": -1,
                            "end_dis": 0,
                            "min_scale": 19.8,
                            "max_scale": 21,
                            "min_skew": 55,
                            "max_skew": 55,
                            "scale_speed": 0.5,
                            "skew_speed": 5
                        }
                    ]
                },
                {
                    "roadclass_start": 2,
                    "roadclass_end": 20,
                    "sub_path": [
                        {
                            "trigger_dis": -1,
                            "end_dis": 0,
                            "min_scale": 19.8,
                            "max_scale": 21,
                            "min_skew": 55,
                            "max_skew": 55,
                            "scale_speed": 0.5,
                            "skew_speed": 5
                        }
                    ]
                }
            ],
            "intersection_switch_rule": {
                "switch_type": 1,
                "best_switch_pos_horizontal": 20,
                "best_switch_pos_vertical": 55
            },
            "center_move_rule": {
                "restore_time": 3.5,
                "move_end_offset": [
                    0,
                    0.1
                ],
                "car_starting_offset_h": [
                    0,
                    0.22
                ],
                "car_starting_offset_v": [
                    0,
                    0.265
                ]
            }
        },
{
        "type":"hd_intersection",
        "navitype":1,
        "subtype":[60,61,62],
        "priority":1.3,
        "scale_enabled":true,
        "skew_enabled":true,
        "rotate_enabled":true,
        "roadclass_path":[
            {
            "roadclass_start":0,
            "roadclass_end": 20,
            "sub_path":[
                {
                "trigger_dis":350,
                "end_dis":0,
                "min_scale":18.5,
                "max_scale":20.3,
                "min_skew":52,
                "max_skew":52,
                "scale_speed":0.5,
                "skew_speed":5,
                "max_diff_car_rotation_angle":80
                }
            ]
            }
        ],
        "destination_rule":{
         "min_scale_exec_distance":350,
         "effective_distance_road2map":[20,150]
        },
        "intersection_switch_rule": {
            "switch_type": 1,
            "best_switch_pos_horizontal": 20,
            "best_switch_pos_vertical": 55
        }
    },
    {
      "type": "hd_tunnel",
      "navitype": 1,
      "priority": 1.5,
      "scale_enabled": true,
      "skew_enabled": true,
      "rotate_enabled": true,
      "roadclass_path": [
        {
          "roadclass_start": 0,
          "roadclass_end": 20,
          "sub_path": [
            {
              "trigger_dis": -1,
              "end_dis": 80,
              "min_scale": 23.5,
              "max_scale": 23.5,
              "min_skew": 75,
              "max_skew": 75,
              "scale_speed": 0.5,
              "skew_speed": 5,
              "max_diff_car_rotation_angle": 5
            }
          ]
        }
      ],
      "collision_rule": {
        "collision_type": 1,
        "collision_exec_distance": [
          {
            "roadclass_start": 0,
            "roadclass_end": 0,
            "collision_exec_distance": 500
          },
          {
            "roadclass_start": 1,
            "roadclass_end": 1,
            "collision_exec_distance": 500
          },
          {
            "roadclass_start": 2,
            "roadclass_end": 12,
            "collision_exec_distance": 400
          }
        ]
      }
    },
    {
      "type": "hd_tollgate",
      "navitype": 1,
      "priority": 1.7,
      "scale_enabled": true,
      "skew_enabled": true,
      "rotate_enabled": true,
      "roadclass_path": [
        {
          "roadclass_start": 0,
          "roadclass_end": 20,
          "sub_path": [
            {
              "trigger_dis": 200,
              "end_dis": -1,
              "min_scale": 19.3,
              "max_scale": 19.3,
              "min_skew": 60,
              "max_skew": 60,
              "scale_speed": 0.5,
              "skew_speed": 5
            }
          ]
        }
      ]
    },
    {
      "type": "hd_roundabout",
      "navitype": 1,
      "priority": 1.4,
      "scale_enabled": true,
      "skew_enabled": true,
      "rotate_enabled": true,
      "roadclass_path": [
        {
          "roadclass_start": 0,
          "roadclass_end": 20,
          "sub_path": [
            {
              "trigger_dis": 100,
              "end_dis": 0,
              "min_scale": 19,
              "max_scale": 19,
              "min_skew": 50,
              "max_skew": 50,
              "scale_speed": 0.5,
              "skew_speed": 5
            }
          ]
        }
      ],
      "intersection_switch_rule": {
        "switch_type": 1,
        "best_switch_pos_horizontal": 20,
        "best_switch_pos_vertical": 20
      }
    },
    {
      "type": "hd_tunnel_intersection",
      "navitype": 1,
      "priority": 1.6,
      "scale_enabled": true,
      "skew_enabled": true,
      "rotate_enabled": true,
      "roadclass_path": [
        {
          "roadclass_start": 0,
          "roadclass_end": 1,
          "sub_path": [
            {
              "trigger_dis": -1,
              "end_dis": 0,
              "min_scale": 19.6,
              "max_scale": 20.3,
              "min_skew": 55,
              "max_skew": 55,
              "scale_speed": 0.5,
              "skew_speed": 5,
              "max_diff_car_rotation_angle": 5
            }
          ]
        },
        {
          "roadclass_start": 2,
          "roadclass_end": 20,
          "sub_path": [
            {
              "trigger_dis": -1,
              "end_dis": 0,
              "min_scale": 19.6,
              "max_scale": 20.3,
              "min_skew": 55,
              "max_skew": 55,
              "scale_speed": 0.5,
              "skew_speed": 5,
              "max_diff_car_rotation_angle": 5
            }
          ]
        }
      ],
      "intersection_switch_rule": {
        "switch_type": 1,
        "best_switch_pos_horizontal": 20,
        "best_switch_pos_vertical": 55
      }
    },
    {
               "type": "hd_sd_hd_switch",
               "navitype": 1,
               "priority": 1.1,
               "scale_enabled": true,
               "skew_enabled": true,
               "rotate_enabled": true,
               "car_scan_enabled":false,
               "road_scan_enabled":true,
               "car_scan_animation":{
                  "scan_light_color":29115246,
                  "scan_light_width":0.02,
                  "flood_light_intensity":0.4,
                  "scan_light_direction":4,
                  "scan_light_delay":0.2,
                  "scan_animation_second":1
               },
               "road_scan_animation":{
                  "scan_line_texture_name":"scan_line_texture.png",
                  "scan_area_texture_name":"scan_area_texture.png",
                  "scan_frame_texture_name":"scan_frame_texture.png",
                  "scan_line_width":10,
                  "scan_area_width":400,
                  "scan_frame_width":4,
                  "scan_animation_second":1000,
                  "scan_under_guide_area":false
               },
               "roadclass_path": [
                   {
                       "roadclass_start": 0,
                       "roadclass_end": 20,
                       "camera_animation_rule": {
                           "duration": 4,
                           "animation_started_priority": 5,
                           "scale":[{"interval":[0,0.2],"value":20.1}],
                           "skew":[{"interval":[0,0.2],"value":65}],
                           "rotation":[],
                           "car_scan":[{"interval":[0.1,0.3],"value":1}],
                           "road_scan":[{"interval":[0.15,1],"value":1}]
                       }
                   }
               ]
           },
    {
      "type": "hd_magic",
      "navitype": 1,
      "priority": 1.1,
      "scale_enabled": true,
      "skew_enabled": false,
      "rotate_enabled": false,
      "roadclass_path": [
        {
          "roadclass_start": 0,
          "roadclass_end": 12,
          "sub_path": [
            {
              "trigger_dis": -1,
              "end_dis": -200,
              "min_scale": 15,
              "max_scale": 18,
              "min_skew": 20,
              "max_skew": 45,
              "scale_speed": 0.5,
              "skew_speed": 5
            }
          ]
        }
      ]
    },
    {
      "type": "hd_default",
      "navitype": 1,
      "priority": 1.0,
      "scale_enabled": true,
      "skew_enabled": true,
      "rotate_enabled": true,
      "roadclass_path": [
        {
          "roadclass_start": 0,
          "roadclass_end": 1,
          "sub_path": [
            {
              "trigger_dis": -1,
              "end_dis": -1,
              "min_scale": 20.8,
              "max_scale": 20.8,
              "min_skew": 65.5,
              "max_skew": 65.5,
              "scale_speed": 0.5,
              "skew_speed": 5
            }
          ]
        },
        {
          "roadclass_start": 2,
          "roadclass_end": 20,
          "sub_path": [
            {
              "trigger_dis": -1,
              "end_dis": -1,
              "min_scale": 20.8,
              "max_scale": 20.8,
              "min_skew": 65.5,
              "max_skew": 65.5,
              "scale_speed": 0.5,
              "skew_speed": 5
            }
          ]
        }
      ]
    }
  ]
}
