{"maplayers": [{"type": "hd_layer_event_accident", "base_config": {"enable": true, "visible": true}, "personal_config": {"distance_show": 1000, "distance_hide": -100, "dis_line_repetition": 10, "dis_line_head_group_gradual": 5, "dis_line_head_lane_gradual": 0, "dis_line_tail_group_gradual": 5, "dis_line_tail_lane_gradual": 0, "dis_area_head_gradual": 25, "dis_area_tail_gradual": 25, "model_show_min_dis": 5, "model_show_min_one_lane_width": 2.5}, "model_resource_default_multi_lane_day": {"json": "rtt_accident_model_default_multi_lane.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_default_multi_lane_day.png"}, "model_resource_default_multi_lane_night": {"json": "rtt_accident_model_default_multi_lane.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_default_multi_lane_night.png"}, "model_resource_default_one_lane_day": {"json": "rtt_accident_model_default_one_lane.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_default_one_lane_day.png"}, "model_resource_default_one_lane_night": {"json": "rtt_accident_model_default_one_lane.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_default_one_lane_night.png"}, "model_resource_one_lane_day": [{"sub_type": 1, "value": {"json": "rtt_accident_model_one_lane_1.json", "dat": "rtt_accident_model_multi_lane_1.dat", "png": "rtt_accident_model_one_lane_1_day.png"}}, {"sub_type": 2, "value": {"json": "rtt_accident_model_one_lane_2.json", "dat": "rtt_accident_model_one_lane_2.dat", "png": "rtt_accident_model_one_lane_2_day.png"}}, {"sub_type": 3, "value": {"json": "rtt_accident_model_one_lane_3.json", "dat": "rtt_accident_model_one_lane_2.dat", "png": "rtt_accident_model_one_lane_3_day.png"}}, {"sub_type": 4, "value": {"json": "rtt_accident_model_one_lane_4.json", "dat": "rtt_accident_model_multi_lane_4.dat", "png": "rtt_accident_model_one_lane_4_day.png"}}, {"sub_type": 5, "value": {"json": "rtt_accident_model_one_lane_5.json", "dat": "rtt_accident_model_one_lane_2.dat", "png": "rtt_accident_model_one_lane_5_day.png"}}, {"sub_type": 6, "value": {"json": "rtt_accident_model_one_lane_6.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_one_lane_6_day.png"}}, {"sub_type": 7, "value": {"json": "rtt_accident_model_one_lane_7.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_one_lane_7_day.png"}}], "model_resource_one_lane_night": [{"sub_type": 1, "value": {"json": "rtt_accident_model_one_lane_1.json", "dat": "rtt_accident_model_multi_lane_1.dat", "png": "rtt_accident_model_one_lane_1_night.png"}}, {"sub_type": 2, "value": {"json": "rtt_accident_model_one_lane_2.json", "dat": "rtt_accident_model_one_lane_2.dat", "png": "rtt_accident_model_one_lane_2_night.png"}}, {"sub_type": 3, "value": {"json": "rtt_accident_model_one_lane_3.json", "dat": "rtt_accident_model_one_lane_2.dat", "png": "rtt_accident_model_one_lane_3_night.png"}}, {"sub_type": 4, "value": {"json": "rtt_accident_model_one_lane_4.json", "dat": "rtt_accident_model_multi_lane_4.dat", "png": "rtt_accident_model_one_lane_4_night.png"}}, {"sub_type": 5, "value": {"json": "rtt_accident_model_one_lane_5.json", "dat": "rtt_accident_model_one_lane_2.dat", "png": "rtt_accident_model_one_lane_5_night.png"}}, {"sub_type": 6, "value": {"json": "rtt_accident_model_one_lane_6.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_one_lane_6_night.png"}}, {"sub_type": 7, "value": {"json": "rtt_accident_model_one_lane_7.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_one_lane_7_night.png"}}], "model_resource_multi_lane_day": [{"sub_type": 1, "value": {"json": "rtt_accident_model_multi_lane_1.json", "dat": "rtt_accident_model_multi_lane_1.dat", "png": "rtt_accident_model_multi_lane_1_day.png"}}, {"sub_type": 2, "value": {"json": "rtt_accident_model_multi_lane_2.json", "dat": "rtt_accident_model_multi_lane_2.dat", "png": "rtt_accident_model_multi_lane_2_day.png"}}, {"sub_type": 3, "value": {"json": "rtt_accident_model_multi_lane_3.json", "dat": "rtt_accident_model_multi_lane_3.dat", "png": "rtt_accident_model_multi_lane_3_day.png"}}, {"sub_type": 4, "value": {"json": "rtt_accident_model_multi_lane_4.json", "dat": "rtt_accident_model_multi_lane_4.dat", "png": "rtt_accident_model_multi_lane_4_day.png"}}, {"sub_type": 5, "value": {"json": "rtt_accident_model_multi_lane_5.json", "dat": "rtt_accident_model_multi_lane_5.dat", "png": "rtt_accident_model_multi_lane_5_day.png"}}, {"sub_type": 6, "value": {"json": "rtt_accident_model_multi_lane_6.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_multi_lane_6_day.png"}}, {"sub_type": 7, "value": {"json": "rtt_accident_model_multi_lane_7.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_multi_lane_7_day.png"}}], "model_resource_multi_lane_night": [{"sub_type": 1, "value": {"json": "rtt_accident_model_multi_lane_1.json", "dat": "rtt_accident_model_multi_lane_1.dat", "png": "rtt_accident_model_multi_lane_1_night.png"}}, {"sub_type": 2, "value": {"json": "rtt_accident_model_multi_lane_2.json", "dat": "rtt_accident_model_multi_lane_2.dat", "png": "rtt_accident_model_multi_lane_2_night.png"}}, {"sub_type": 3, "value": {"json": "rtt_accident_model_multi_lane_3.json", "dat": "rtt_accident_model_multi_lane_3.dat", "png": "rtt_accident_model_multi_lane_3_night.png"}}, {"sub_type": 4, "value": {"json": "rtt_accident_model_multi_lane_4.json", "dat": "rtt_accident_model_multi_lane_4.dat", "png": "rtt_accident_model_multi_lane_4_night.png"}}, {"sub_type": 5, "value": {"json": "rtt_accident_model_multi_lane_5.json", "dat": "rtt_accident_model_multi_lane_5.dat", "png": "rtt_accident_model_multi_lane_5_night.png"}}, {"sub_type": 6, "value": {"json": "rtt_accident_model_multi_lane_6.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_multi_lane_6_night.png"}}, {"sub_type": 7, "value": {"json": "rtt_accident_model_multi_lane_7.json", "dat": "rtt_accident_model_default_one_lane.dat", "png": "rtt_accident_model_multi_lane_7_night.png"}}]}, {"type": "hd_layer_guide_line", "personal_config": {"road_class_configs": [{"road_class_start": "0", "road_class_end": "1", "guide_line_width": 1.95}, {"road_class_start": "2", "road_class_end": "20", "guide_line_width": 1.65}], "line_start_transparent_dis_low_conf": -1, "line_start_transparent_dis": 5.0, "line_end_transparent_dis": 150.0, "animation_duration": 0, "flow_arrow_area_length": 224.0, "flow_arrow_speed_in_change_lane_area": 112.0, "flow_arrow_speed_in_go_straight_area": 75.0, "texture_arrow_day": "mapbiz_guide_line_arrow_day.png", "texture_arrow_night": "mapbiz_guide_line_arrow_night.png", "default_texture_traffic_color_day": "mapbiz_guide_line_default_day.png", "default_texture_traffic_color_night": "mapbiz_guide_line_default_night.png", "callback_lane_to_pos_engine_distance": 100.0}, "base_config": {"enable": true, "visible": true}}, {"type": "hd_layer_locator", "personal_config": {"locator_animation_config": {"locator_blink_config": [{"component_type": 0, "enable": true, "animation_config": [{"pos_condition": [{"cur_lane_confidence": 0, "logo_breath_status": 1}, {"cur_lane_confidence": 1, "logo_breath_status": 1}, {"cur_lane_confidence": 2, "logo_breath_status": 1}, {"cur_lane_confidence": 0, "logo_breath_status": 2}, {"cur_lane_confidence": 1, "logo_breath_status": 2}, {"cur_lane_confidence": 2, "logo_breath_status": 2}], "animation_param": {"animation_duration": 2, "loop_count": -1, "end_type": 1, "anim_node": [{"rate": 0, "scale": 1, "alpha": 1}, {"rate": 0.5, "scale": 1, "alpha": 0.3}, {"rate": 1, "scale": 1, "alpha": 1}]}}]}, {"component_type": 1, "enable": true, "animation_config": [{"pos_condition": [{"cur_lane_confidence": 0, "logo_breath_status": 1}, {"cur_lane_confidence": 1, "logo_breath_status": 1}, {"cur_lane_confidence": 2, "logo_breath_status": 1}, {"cur_lane_confidence": 0, "logo_breath_status": 2}, {"cur_lane_confidence": 1, "logo_breath_status": 2}, {"cur_lane_confidence": 2, "logo_breath_status": 2}], "animation_param": {"animation_duration": 1, "loop_count": 1, "end_type": 2, "anim_node": [{"rate": 0, "scale": 1, "alpha": 1}, {"rate": 1, "scale": 1, "alpha": 0.8}]}}]}, {"component_type": 2, "enable": true, "animation_config": [{"pos_condition": [{"cur_lane_confidence": 1, "logo_breath_status": 0}, {"cur_lane_confidence": 1, "logo_breath_status": 1}, {"cur_lane_confidence": 1, "logo_breath_status": 2}], "animation_param": {"animation_duration": 2, "loop_count": -1, "end_type": 1, "anim_node": [{"rate": 0, "scale": 0, "alpha": 0}, {"rate": 0.15, "scale": 0.15, "alpha": 1}, {"rate": 0.5, "scale": 0.5, "alpha": 1}, {"rate": 0.85, "scale": 0.85, "alpha": 1}, {"rate": 1, "scale": 1, "alpha": 0}]}}]}]}, "accuracy_style_config": {"color": [152, 230, 255, 255], "radius_ratio": 1, "curve_node": [{"rate": 0, "alpha": 0}, {"rate": 0.6, "alpha": 0}, {"rate": 1, "alpha": 0.6}]}}}, {"type": "sd_layer_locator", "personal_config": {"locator_animation_config": {"locator_blink_config": [{"component_type": 0, "enable": true, "animation_config": [{"pos_condition": [{"cur_lane_confidence": 0, "logo_breath_status": 1}, {"cur_lane_confidence": 0, "logo_breath_status": 2}], "animation_param": {"animation_duration": 2, "loop_count": -1, "end_type": 1, "anim_node": [{"rate": 0, "scale": 1, "alpha": 1}, {"rate": 0.5, "scale": 1, "alpha": 0.3}, {"rate": 1, "scale": 1, "alpha": 1}]}}]}, {"component_type": 1, "enable": true, "animation_config": [{"pos_condition": [{"cur_lane_confidence": 0, "logo_breath_status": 1}, {"cur_lane_confidence": 0, "logo_breath_status": 2}], "animation_param": {"animation_duration": 1, "loop_count": 1, "end_type": 2, "anim_node": [{"rate": 0, "scale": 1, "alpha": 1}, {"rate": 1, "scale": 1, "alpha": 0.6}]}}]}, {"component_type": 2, "enable": true, "animation_config": [{"pos_condition": [{"cur_lane_confidence": 0, "logo_breath_status": 2}], "animation_param": {"animation_duration": 2, "loop_count": -1, "end_type": 1, "anim_node": [{"rate": 0, "scale": 0, "alpha": 0}, {"rate": 0.15, "scale": 0.15, "alpha": 1}, {"rate": 0.5, "scale": 0.5, "alpha": 1}, {"rate": 0.85, "scale": 0.85, "alpha": 1}, {"rate": 1, "scale": 1, "alpha": 0}]}}]}]}, "accuracy_style_config": {"color": [152, 230, 255, 255], "radius_ratio": 1, "curve_node": [{"rate": 0, "alpha": 0}, {"rate": 0.6, "alpha": 0}, {"rate": 1, "alpha": 0.6}]}}}, {"type": "hd_layer_lane_sideline", "base_config": {"enable": true, "visible": true, "min_display_level": 18, "max_display_level": 19, "min_display_priority": 36100, "max_display_priority": 36200, "need_avoid_others": false, "min_margin_with_others": 0, "enable_change_by_map_mode": true, "show_name": false, "need_avoid_route": false, "show_name_with_annotation": false, "marker_show_status": 1, "avoid_route_type": 1}, "personal_config": {"lane_edges": [{"lane_edge_type": 1, "lane_edge_resource_day_name": "lane_edge_resource_single_solid_day.png", "lane_edge_resource_night_name": "lane_edge_resource_single_solid_night.png", "resource_repeat_length": 20, "lane_edge_width": 6}, {"lane_edge_type": 2, "lane_edge_resource_day_name": "lane_edge_resource_single_solid_day.png", "lane_edge_resource_night_name": "lane_edge_resource_single_solid_night.png", "resource_repeat_length": 20, "lane_edge_width": 6}, {"lane_edge_type": 3, "lane_edge_resource_day_name": "lane_edge_resource_single_solid_day.png", "lane_edge_resource_night_name": "lane_edge_resource_single_solid_night.png", "resource_repeat_length": 20, "lane_edge_width": 6}]}}, {"type": "hd_layer_end_marker_guide", "base_config": {"enable": true, "visible": true, "min_display_level": 0, "max_display_level": 30, "min_display_priority": 28000, "max_display_priority": 29000, "need_avoid_others": false, "min_margin_with_others": 0, "enable_change_by_map_mode": true, "show_name": false, "need_avoid_route": false, "show_name_with_annotation": true, "marker_show_status": 1, "avoid_route_type": 1}, "personal_config": {"end_area": {"guide_area_end_resource_day_name": "end_guide_area_marker.png", "guide_area_end_resource_night_name": "end_guide_area_marker.png", "min_combine_distance": 5, "guide_area_end_anchor": [0.5, 0.8]}}}, {"type": "hd_layer_end_bubble_map", "base_config": {"enable": true, "visible": true, "min_display_level": 0, "max_display_level": 30, "min_display_priority": 80000, "max_display_priority": 81000, "need_avoid_others": false, "min_margin_with_others": 0, "enable_change_by_map_mode": true, "show_name": false, "need_avoid_route": false, "show_name_with_annotation": true, "marker_show_status": 1, "avoid_route_type": 1}, "personal_config": {"end_area": {"map_end_resource_day_name": "main_map_end_point.png", "map_end_resource_night_name": "main_map_end_point.png", "combine_end_resource_day_name": "end_combine_marker.png", "combine_end_resource_night_name": "end_combine_marker.png", "min_combine_distance": 5, "combine_end_anchor": [0.5, 0.8], "map_end_anchor": [0.5, 0.8]}}}, {"type": "hd_layer_recommend_lane", "base_config": {"enable": true, "visible": true, "min_display_priority": 39000, "max_display_priority": 39100}, "personal_config": {"distance_between_text_and_locator": 15, "arrow_length": 150, "flow_arrow_animation_duration": 2, "text_hidden_distance_to_end": 50, "arrow_hidden_distance_to_end": 80, "hidden_lane_width_single": 2, "lane_text_texture_day": "recommend_lane_text_texture_fov.png", "lane_text_texture_night": "recommend_lane_text_texture_fov.png", "lane_arrow_texture_day": "recommend_lane_arrow_texture.png", "lane_arrow_texture_night": "recommend_lane_arrow_texture.png", "traffic_texture_day": "recommend_lane_traffic_texture.png", "traffic_texture_night": "recommend_lane_traffic_texture.png", "width_config": [{"start_class": 0, "end_class": 1, "lane_width": 3.5}, {"start_class": 2, "end_class": 14, "lane_width": 3}]}}, {"type": "hd_layer_forbidden_road_sign", "base_config": {"enable": true, "visible": true}, "personal_config": {"show_distance": 300, "merge_distance": 20, "model_min_width": 2, "model_height": 3, "model_thickness": 2.64, "model_width_padding": 0.24, "model_anchor": [0.5, 0.5, 0], "x_day_name": "forbid_lane_logo.obj", "x_night_name": "forbid_lane_logo.obj", "background_day_name": "forbid_lane_frame.obj", "background_night_name": "forbid_lane_frame.obj", "x_texture_day_name": "forbid_lane_logo_texture.png", "x_texture_night_name": "forbid_lane_logo_texture.png", "background_texture_day_name": "forbid_lane_frame_texture.png", "background_texture_night_name": "forbid_lane_frame_texture.png"}}, {"type": "hd_layer_security_alert", "base_config": {"enable": true, "visible": true}, "personal_config": {"distance_suggestion": 200, "distance_warning": 100, "distance_hide": 0, "model_arrow_animation_time": 3, "color_text_day": "0xffF5F8FF", "color_text_night": "0xffE8F5FF", "color_area": {"day": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}, "night": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}}, "area_horizontal_gradual": [0, 1], "area_gradual_head": 0.25, "area_gradual_tail": 0.75, "hint_text": ["会", "车", "区"], "texture_locator_radar_wave_day": "security_warning_radar_wave_day.png", "texture_locator_radar_wave_night": "security_warning_radar_wave_night.png", "color_locator_radar_wave": {"day": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}, "night": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}}, "model_resource_day": [{"sub_type": 0, "value": {"width": 1.8, "color": {"model": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}, "arrow": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}}, "arrow": "security_warning_arrow.png", "arrow_animation_step_count": 4, "json": "security_warning_model_1.json", "dat": "security_warning_model_car.obj", "png": "security_warning_model_car_day.png"}}, {"sub_type": 2, "value": {"width": 1.8, "color": {"model": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}, "arrow": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}}, "arrow": "security_warning_arrow.png", "arrow_animation_step_count": 4, "json": "security_warning_model_1.json", "dat": "security_warning_model_car.obj", "png": "security_warning_model_car_day.png"}}, {"sub_type": 1, "value": {"width": 2, "color": {"model": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}, "arrow": {"suggestion": "0xff6fb0ed", "warning": "0xffecea70", "danger": "0xffed6c6b"}}, "arrow": "security_warning_arrow.png", "arrow_animation_step_count": 4, "json": "security_warning_model_2.json", "dat": "security_warning_model_truck.obj", "png": "security_warning_model_truck_day.png"}}], "model_resource_night": [{"sub_type": 0, "value": {"width": 1.8, "color": {"model": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}, "arrow": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}}, "arrow": "security_warning_arrow.png", "arrow_animation_step_count": 4, "json": "security_warning_model_1.json", "dat": "security_warning_model_car.obj", "png": "security_warning_model_car_night.png"}}, {"sub_type": 2, "value": {"width": 1.8, "color": {"model": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}, "arrow": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}}, "arrow": "security_warning_arrow.png", "arrow_animation_step_count": 4, "json": "security_warning_model_1.json", "dat": "security_warning_model_car.obj", "png": "security_warning_model_car_night.png"}}, {"sub_type": 1, "value": {"width": 2, "color": {"model": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}, "arrow": {"suggestion": "0xff568cbf", "warning": "0xffbfbd56", "danger": "0xffbf5756"}}, "arrow": "security_warning_arrow.png", "arrow_animation_step_count": 4, "json": "security_warning_model_2.json", "dat": "security_warning_model_truck.obj", "png": "security_warning_model_truck_night.png"}}]}}]}