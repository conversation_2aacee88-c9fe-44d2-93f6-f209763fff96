//
//  MapBizOption.h
//  MapBiz
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/29.
//
#ifndef _OC_MAP_BIZ_OPTION_H_
#define _OC_MAP_BIZ_OPTION_H_

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface MapBizOption : NSObject

/**
 * 底图引擎具柄
 * @note 在一个MapView中同时包含主图 和 鹰眼图的情况下，这个具柄为主底图的具柄；
 *       在一个MapView中若仅仅存在一个底图，这个具柄就是从MapView中获取的具柄；
 */
@property (nonatomic, assign, nullable) void *mainMapEngineHandler;
/**
 * 鹰眼图引擎具柄(无鹰眼图时为空)
 */
@property (nonatomic, assign, nullable) void *overviewMapEngineHandler;
/**
 * 底图配置路径
 * 备注:为支持跨平台路径，请在平台层保证路径以路径分隔符结束
 */
@property (nonatomic, copy, nullable) NSString *mapConfigPath;
/**
 * 导航图面资源名称
 */
@property (nonatomic, copy, nullable) NSString *mapConfigName;
/**
 * 导航资源是否启用
 * 备注: 在一些业务端完全通过接口定制资源，不用资源配置文件情况的使用
 *      开启状态：会加载资源文件；关闭状态: 内部不会加载资源,这种情况下需要业务端自己通过资源定制接口传入资源;
 *      内部默认值:开启
 */
@property (nonatomic, assign) BOOL mapConfigEnabled;
/**
 * 底图实例名称(多实例区分)
 * 提示:强烈建议客户端设定唯一标示,非功能性依赖，问题调查效率依赖
 */
@property (nonatomic, copy, nullable) NSString *mapInstanceName;
/**
 * 设置语言类型，SDK内部默认简体中文。SDK初始化时设置语言类型
 * 中文简体: zh-cn; 中文繁体: zh-tw; 英文: en;
 */
@property (nonatomic, copy, nullable) NSString *language;
/**
 * 调试过程中中间数据输出路径(空 将无法输出)
 */
@property (nonatomic, copy, nullable) NSString *mapDebugPath;

@end

NS_ASSUME_NONNULL_END

#endif
