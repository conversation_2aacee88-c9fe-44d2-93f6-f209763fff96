// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by 李林洋 on 2023/10/23.
//

#pragma once
#include <memory>
#include "map_biz_common.h"
#include "map_biz_export.h"
#include "map_layer_define.h"
__MAPBIZ_NAMESPACE_BEGIN__
/**
 * 控制 map-biz HD下的一些属性
 */
class MAPBIZ_EXPORT MapBizHDController {
 public:
  virtual void UpdateLaneGradualConfig(std::shared_ptr<MapBizHDLaneGradualConfig> config) = 0;
  virtual std::shared_ptr<MapBizHDLaneGradualConfig> GetLaneGradualConfig() = 0;
  virtual void SetBuildingAvoidGuideAreaEnabled(bool enabled) = 0;
};
__MAPBIZ_NAMESPACE_END__

