// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by 李林洋 on 2023/1/6.
//

#ifndef MAP_BIZ_INCLUDE_MAP_COMM_MAP_BIZ_INIT_RESOURCE_COPY_TASK_H_
#define MAP_BIZ_INCLUDE_MAP_COMM_MAP_BIZ_INIT_RESOURCE_COPY_TASK_H_
#include "map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__
/**
 * map biz 异步资源拷贝任务，mapbiz执行完此任务后才会创建成功
 */
class MapBizInitResourceCopyTask {
 public:
 /**
  * 初始化资源拷贝，一般会把assert的资源拷贝到 option.map_config_path_ 中。一般指map_biz_resource.dat 这个文件
  * @return 结果
  */
  virtual MapBizCreatedResultInfo InitResourceCopy() { return {0, "init"}; }
};

__MAPBIZ_NAMESPACE_END__
#endif  // MAP_BIZ_INCLUDE_MAP_COMM_MAP_BIZ_INIT_RESOURCE_COPY_TASK_H_
