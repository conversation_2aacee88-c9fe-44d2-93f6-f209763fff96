// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/9/21.
//

#pragma once

#include "map_biz_common.h"
#include "map_biz_export.h"

__MAPBIZ_NAMESPACE_BEGIN__

class MAPBIZ_EXPORT MapTileLayer {
 public:
  /**
   * 设置该图层是否可见
   * 备注:内部默认值：true
   * @param enable     true:可见; false;隐藏
   */
  virtual void SetVisible(bool enable) = 0;
  /**
   * 设置该图层的优先级
   * 备注:解决多个业务图层之间谁在上谁在下
   * @param priority
   */
  virtual void SetPriority(int priority) = 0;
  /**
   * 设置该图层中的元素是否可以点击
   * 备注:内部默认值:true
   * @param clickable   true:可点击; false:不可点击
   */
  virtual void SetClickable(bool clickable) = 0;
  /**
   * 设置该图层的元素是否避让
   * @param enable  true:避让，false:不避让
   */
  virtual void SetAvoidOthers(bool enable) = 0;
  /**
   * 设置该图层的元素冲突的底图元素是否避让
   * @param enable  true:底图元素被避让，false:底图元素不被不避让
   */
  virtual void SetAvoidAnnotations(bool enable) = 0;
  /**
   * 设置该图层数据的刷新间隔，单位秒
   * 备注:内部默认 90秒
   * @param fresh_interval
   */
  virtual void SetRefreshInterval(int fresh_interval) = 0;
  /**
   * 设置该图层数据的显示级别范围
   * @param min_level   最小级别：内部默认12； 可设置范围[4,30)
   * @param max_level   最大级别：内部默认30； 可设置范围[4,30)
   */
  virtual void SetShowLevelRange(int min_level, int max_level) = 0;
  /**
   * 设置用户点击该图层元素后，关于Marker请求下载是否有用户自己拉去
   * @param enable  true:用户自己维护; false：Biz内部拉去详情返回
   */
  virtual void SetElementDetailDownloadingByUser(bool enable) = 0;
};  //

__MAPBIZ_NAMESPACE_END__
