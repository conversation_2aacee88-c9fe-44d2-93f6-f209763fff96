//
//  MapBizMapTileLayer.h
//  MapBiz
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/7.
//

#ifndef _OC_MAP_BIZ_MAP_TILE_LAYER_H_
#define _OC_MAP_BIZ_MAP_TILE_LAYER_H_

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol MapBizMapTileLayer <NSObject>
/**
 * 设置该图层是否可见
 * 备注:内部默认值：true
 * @param true:可见; false;隐藏
 */
- (void)setVisible:(BOOL)visible;
/**
 * 设置该图层的优先级
 * 备注:解决多个业务图层之间谁在上谁在下
 */
- (void)setPriority:(int)priority;
/**
 * 设置该图层中的元素是否可以点击
 * 备注:内部默认值:true
 * @param true:可点击; false:不可点击
 */
- (void)setClickable:(bool)clickable;
/**
 * 设置该图层的元素是否避让
 * @param true:避让，false:不避让
 */
- (void)setAvoidOthers:(bool)avoidOthers;
/**
 * 设置该图层的元素冲突的底图元素是否避让
 * @param avoidAnnotations  true:底图元素被避让，false:底图元素不被不避让
 */
- (void)setAvoidAnnotations:(bool)avoidAnnotations;
/**
 * 设置该图层数据的刷新间隔，单位秒
 * 备注:内部默认 90秒
 * @param fresh_interval
 */
- (void)setRefreshInterval:(int)refreshInterval;
/**
 * 设置该图层数据的显示级别范围
 * min_level   最小级别：内部默认12； 可设置范围[4,30)
 * max_level   最大级别：内部默认30； 可设置范围[4,30)
 */
- (void)setShowLevelRange:(int)minLevel maxLevel:(int)maxLevel;
/**
 * 设置用户点击该图层元素后，关于Marker请求下载是否有用户自己拉去
 * true:用户自己维护; false：Biz内部拉去详情返回
 */
- (void)setElementDetailDownloadingByUser:(BOOL)enable;
@end

NS_ASSUME_NONNULL_END

#endif
