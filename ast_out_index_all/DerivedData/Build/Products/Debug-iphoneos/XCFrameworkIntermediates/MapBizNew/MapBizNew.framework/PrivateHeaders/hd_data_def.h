// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/6/18.
//

#ifndef MAP_BIZ_INCLUDE_MAP_LAYER_HD_DATA_DEF_H_
#define MAP_BIZ_INCLUDE_MAP_LAYER_HD_DATA_DEF_H_

#include "common/had_structure.h"
#include "map_comm/map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__

// 车道面类型
enum class RoadAreaLayerType {
  LANE_GROUP_AREA = 0,  // 多车道面
  LANE_AREA,            // 单车道面
};

/**
 * 连续单车道
 */
class SingleLanes {
 public:
  std::vector<mapbase::HAD::LaneID> lanes;  // 车道ID序列
  double start_ratio = 0.0;                 // first lane开始位置
  double end_ratio = 0.0;                   // last lane结束位置
};

// 车道单元
class LaneUnit {
 public:
  mapbase::HAD::LaneID lane_id;  // 车道ID序列
  double start_ratio = 0.0;      // first lane开始位置
  double end_ratio = 0.0;        // last lane结束位置
};

enum class LineType {
  COLOR_LINE = 0,    // 单色线
  TEXTURE_LINE = 1,  // 纹理线
};

__MAPBIZ_NAMESPACE_END__
#endif  // MAP_BIZ_INCLUDE_MAP_LAYER_HD_DATA_DEF_H_
