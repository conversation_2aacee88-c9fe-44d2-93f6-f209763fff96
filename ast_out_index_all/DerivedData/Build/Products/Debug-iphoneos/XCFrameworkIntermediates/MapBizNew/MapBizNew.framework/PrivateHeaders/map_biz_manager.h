// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/5/8.
//
/**
 * 该类为整个MapBiz的总管理者，其他模块统一与它的交互
 */
#pragma once

#include <memory>
#include "map_biz_hd_controller.h"
#include "map_biz_hd_scale_controller.h"
#include <GLMapLib/MapDataType.h>
#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/trajectory_structure.h>
#include "map_biz_config_context.h"
#include "map_biz_common.h"
#include "map_biz_create_callback.h"
#include "map_biz_event_listener.h"
#include "map_biz_export.h"
#include "map_biz_init_resource_copy_task.h"
#include "map_biz_option.h"
#include "map_biz_route_callback.h"
#include "map_biz_resource_callback.h"
#include "map_biz_resource_define.h"
#include "map_tile_layer_define.h"
#include <MapBaseNew/http_interface.h>
#include "plog/logger.h"
#include "plog/severity.h"
#include <MapBaseNew/green_travel_route_plan_visitor.h>
#include <MapBaseNew/guidance_base_structure.h>
#include <MapBaseNew/route_explain.h>
#include <MapBaseNew/route_plan_visitor.h>

namespace MAPAPI {
class OverlayOptions;
}  // namespace MAPAPI

__MAPBIZ_NAMESPACE_BEGIN__

class MAPBIZ_EXPORT MapBizManager {
 public:
  /**
   * 初始化base库线程池，map-biz独立发布时需调用，必须在创建实例前调用。
   * @param name 线程池名称
   * @param init_default_pool 初始化默认线程池数量
   */
  static void InitBaseThreadPool(const std::string& name, uint8_t init_default_pool = 0);

  /**
   * 获取日志打印配置信息
   * @return                日志配置信息
   */
  static std::shared_ptr<plog::LoggerConfig> GetLoggerConfig();

  /**
   * 获取MapBizManager实例对象
   * @param map_biz_option
   * @param callback 创建是否成功回调（异步创建
   * @param copy_task 资源拷贝任务
   * @return MapBizMgr 共享指针
   */
  static std::shared_ptr<MapBizManager> Create(const MapBizOption& map_biz_option,
                                               std::weak_ptr<MapBizCreateCallBack> callback,
                                               std::shared_ptr<MapBizInitResourceCopyTask> copy_task);

  /**
   * 初始化日志文件
   * @param path 日志存放目录
   * @param level 最大级别，高于该级别将不展示
   * @param dest 日志输出方式
   */
  static void InitLogger(const std::string& path, plog::LogLevel level, plog::LogDest dest);

  /**
   * 设置日志句柄，当使用外部日志时，使用该方法注入日志句柄号
   * @param instance_id 日志句柄号
   */
  static void SetLoggerInstanceId(uint32_t instance_id);

  /**
   * 获取日志句柄号
   * @return 日志句柄号，0为未初始化
   */
  static uint32_t GetLoggerInstanceId();

 protected:
  MapBizManager() = default;

 public:
  virtual ~MapBizManager() = default;

 public:
  /**
   * 支持按照分类打开调试开关
   * @param contents    调试内容的组合值，参考DebugContentCatalog
   * @param bEnabled    开关状态,true打开，false关闭
   * @note  仅仅在Debug版本下该接口才会生效; Release模式下不生效
   */
  virtual void DebugContentsSetEnabled(DebugContent contents, bool bEnabled) = 0;
  /**
   * 更新图层配置参数，可以是全量刷新，也支持指定类型部分配置
   * @param json_config 配置
   */
  virtual void UpdateMapLayerConfig(const std::string& json_config) = 0;
  /**
   * 更新运镜比例尺配置，可以是全量刷新，也支持只改部分配置
   * @param json_config 配置
   */
  virtual void UpdateMagicCameraConfig(const std::string& json_config) = 0;
  /**
   * 针对运镜部分类型比例尺增加动态数据
   * @param camera_data 比例尺动态数据
   */
  virtual void AddMagicCameraData(const MagicCameraData& data) = 0;
  /**
   * 针对运镜部分类型比例尺增加动态区间数据
   * @param camera_data 比例尺动态区间数据
   */
  virtual void AddMagicCameraIntervalData(const MagicCameraIntervalData& data) = 0;
  /**
   * 设置动态帧率开启状态和配置
   * 备注: 若关闭动态帧率，则调整为配置中的默认帧率值
   * @param enable             是否开启动态帧率
   * @param frame_rate_config  动态帧率配置参数
   */
  virtual void SetDynamicFrameRateConfig(bool enable, DynamicFrameRateConfig frame_rate_config) = 0;
  /**
   * 添加MapBiz事件监听器
   * @param map_biz_event_listener
   */
  virtual bool AddMapBizEventListener(std::weak_ptr<MapBizEventListener> map_biz_event_listener) = 0;
  /**
   * 移除MapBiz事件监听器
   * @param map_biz_event_listener
   */
  virtual void RemoveMapBizEventListener(std::weak_ptr<MapBizEventListener> map_biz_event_listener) = 0;
  /**
   * 注册气泡绘制和其他资源加载回调函数
   * @param map_biz_resource_callback      回调
   */
  virtual void AddMapBizResourceCallBack(std::weak_ptr<MapBizResourceCallBack> map_biz_resource_callback) = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetLayerConfig(LayerTypeInfo layer_type_info)
   * 根据LayerTypeInfo 获得对应的layerConfig
   * @param layer_type_info 用来区分获得哪一个LayerConfig
   * @return 对应得到的LayerConfig，具体哪个LayerType对应哪个子类，可以看 LayerType注释
   */
  virtual std::shared_ptr<LayerBaseConfig> GetLayerConfig(LayerTypeInfo layer_type_info) = 0;
  /**
   * 更新图层的配置，至于更新哪个图层，主要通过 LayerBaseConfig 中的main_type_以及sub_type;
   * @param layer_base_config 这个应该是对应
   * LayerBaseConfig的子类。比如更新CameraLayer，layer_base_config的类型就应该是CameraLayerConfig 更新 Locator
   * ，就应该是LocatorLayerConfig
   */
  virtual void UpdateLayerConfig(std::shared_ptr<LayerBaseConfig> layer_base_config) = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetMarkerNameStyleOption(MarkerType marker_type)
   * 获取Marker图层名称样式
   * @param marker_type Marker图层类型
   * @return Marker图层名称样式
   */
  virtual MarkerNameStyleOption GetMarkerNameStyleOption(MarkerType marker_type) = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetCustomRouteStyle()
   * 获取用户自定义蚯蚓线样式
   * @return 用户自定义蚯蚓线样式
   */
  virtual ColorRouteStyleOption GetCustomRouteStyle() = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetCustomRouteStyleEx()
   * 获取用户自定义高级蚯蚓线样式
   * @return 用户自定义高级蚯蚓线样式
   */
  virtual ColorRouteStyleOptionEx GetCustomRouteStyleEx() = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetTurnArrowStyle()
   * 获取用户自定义箭头样式
   * @return 用户自定义箭头样式
   */
  virtual TurnArrowStyleOption GetTurnArrowStyle() = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新定位车标图层配置
   * @param layer_config 定位车标图层配置
   */
  virtual void UpdateLocatorLayerConfig(LocatorLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新蚯蚓线图层配置
   * @param layer_config 蚯蚓线图层配置
   */
  virtual void UpdateRouteLayerConfig(RouteLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新电子眼图层配置
   * @param layer_config 电子眼图层配置
   */
  virtual void UpdateCameraLayerConfig(CameraLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新HD电子眼图层配置
   * @param layer_config 电子眼图层配置
   */
  virtual void UpdateHDCameraLayerConfig(CameraLayerConfig layer_config) = 0;
  /**
   * @deprecated 功能已经下掉
   */
  virtual void UpdateRouteNameLayerConfig(RouteNameLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新拥堵气泡图层配置
   * @param layer_config 拥堵气泡图层配置
   */
  virtual void UpdateRouteTrafficLayerConfig(RouteTrafficLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新伴随气泡图层配置
   * @param layer_config 伴随气泡图层配置
   */
  virtual void UpdateRouteCompanionLayerConfig(RouteCompanionLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新伴随气泡图层配置
   * @param layer_config 伴随气泡图层配置
   */
  virtual void UpdateTrajectoryLayerConfig(TrajectoryLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新伴随气泡图层配置
   * @param layer_config 伴随气泡图层配置
   */
  virtual void UpdateGuideAreaLayerConfig(GuideAreaLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新Marker图层配置
   * @param layer_config Marker图层配置
   */
  virtual void UpdateMarkerLayerConfig(MarkerLayerConfig layer_config) = 0;
  /**
   * 更新Marker图层名称样式
   * @param marker_type Marker图层类型
   * @param style_option Marker图层名称样式
   */
  virtual void UpdateMarkerNameStyleOption(MarkerType marker_type, MarkerNameStyleOption style_option) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新巡航图层配置
   * @param layer_config 巡航图层配置
   */
  virtual void UpdateCruiseLayerConfig(CruiseLayerConfig layer_config) = 0;
  /**
   * 更新用户自定义蚯蚓线样式
   * 备注: 1. 该接口仅仅用于设置使用的纹理资源名称，具体资源需要在资源回调(AddMapBizResourceCallBack)中客户端给出资源
   *      2. 若传入的某个属性值无效，则认为使用内部默认资源，如夜间伴随路线资源名称为空，则使用内部默认的资源
   * @param route_style_option 路线样式选项
   */
  virtual void UpdateCustomRouteStyle(ColorRouteStyleOption route_style_option) = 0;
  /**
   * 更新用户自定义高级蚯蚓线样式
   * 备注: 1. 该接口仅仅用于设置使用的纹理资源名称，具体资源需要在资源回调(AddMapBizResourceCallBack)中客户端给出资源
   *      2. 若传入的某个属性值无效，则认为使用内部默认资源，如夜间伴随路线资源名称为空，则使用内部默认的资源
   *      3. 当需要对调整不同级别下蚯蚓线纹理资源或宽度，名称显示样式时使用；
   *      4. 当UpdateCustomRouteStyleEx和UpdateCustomRouteStyle均设置有效值情况下, 高级定制样式优先生效
   * @param route_style_option 路线样式选项
   */
  virtual void UpdateCustomRouteStyleEx(ColorRouteStyleOptionEx route_style_option) = 0;
  /**
   * 更新用户自定义箭头样式
   * @param turn_arrow_style_option 箭头样式集合
   */
  virtual void UpdateTurnArrowStyle(TurnArrowStyleOption turn_arrow_style_option) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新终点名称ETA图层配置
   * @param layer_config 图层配置
   */
  virtual void UpdateDestNameEtaLayerConfig(DestNameEtaLayerConfig layer_config) = 0;
  /**
   * @deprecated 请使用接口 UpdateLayerConfig
   * 更新路线标签图层配置
   * @param layer_config 图层配置
   */
  virtual void UpdateRouteLabelLayerConfig(RouteLabelLayerConfig layer_config) = 0;
  /**
   * 更新鹰眼图相关配置
   * @param overview_map_config 鹰眼图配置
   */
  virtual void UpdateOverviewMapConfig(OverviewMapConfig overview_map_config) = 0;
  /**
   * 更新客户自定义导航资源(PNG整个图片流)
   * @param custom_map_resources      自定义资源内容列表
   * @note  1. 列表为空时，代表移除所有的客户自定义资源
   *        2. 列表不为空时，仅仅更新对应ID的资源
   */
  virtual void UpdateCustomMapResourcesWithFileBuffer(
      std::map<unsigned int, CustomResourceContentDescriptor> custom_map_resources) = 0;
  /**
   * 更新客户自定义导航资源(PNG纯图像像素部分)
   * @param custom_map_resources      自定义资源内容列表
   * @note  1. 列表为空时，代表移除所有的客户自定义资源
   *        2. 列表不为空时，仅仅更新对应ID的资源
   */
  virtual void UpdateCustomMapResourcesWithPixelBuffer(
      std::map<unsigned int, MapResourceContentDescriptor> custom_map_resources) = 0;

 public:
  /**
   * 导航退后台(内部会根据状态停止一些不必要的计算，降低性能消耗)
   */
  virtual void SetMapPause() = 0;
  /**
   * 导航回前台(内部会恢复导航动作)
   */
  virtual void SetMapResume() = 0;
  /**
   * 设置底图的昼夜模式
   * @param map_mode    MapMode::Day:白天; MapMode::Day:夜间;
   * @note 内部默认:MapMode::Day白天
   */
  virtual void SetMapMode(MapMode map_mode) = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetMapMode()
   * 获取底图的昼夜模式
   * @return           MapMode::Day:白天; MapMode::Day:夜间;
   */
  virtual MapMode GetMapMode() = 0;

  /**
   * 设置底图的导航模式
   * 备注: MapBiz内部会当前模式适当对上一个状态的一些脏数据进行清理
   *      同时确认一些策略仅仅在正确的模式下生效;
   * @param nav_mode   NavMode::Navigation:导航模式; NavMode::Cruise:巡航模式; NavMode::Free自由模式
   * @note 内部默认值: NavMode::Free自由模式
   */
  virtual void SetNavMode(NavMode nav_mode) = 0;
  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetNavMode()
   * 获取底图的导航模式
   * @return  返回当前的导航模式
   */
  virtual NavMode GetNavMode() = 0;
  /**
   * 设置语言类型，SDK内部默认简体中文
   * @param language_str 语言, 中文简体: zh-cn; 中文繁体: zh-tw; 英文: en;
   * 备注: 非法语言 按照默认语言中文处理
   */
  virtual void SetLanguage(std::string language_str) = 0;
  /**
   * 设置定位资源切换 是否由用户完全控制
   * 当设置为true时，表示由用户控制定位表资源；
   * 当设置为false时，表示由SDK内部控制，SDK需要根据GPS状态切换定位表资源
   * @param byUser true：User控制，false:SDK控制;
   */
  virtual void SetLocatorResCtrlMode(bool byUser) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetLocatorResCtrlMode()
   * 获取定位资源切换 是否由用户完全控制
   * @return  true:User控制, false:SDK控制
   */
  virtual bool GetLocatorResCtrlMode() = 0;

  /**
   * 即将删除，请使用接口 void SetLocationFollowedConfig(LocationFollowConfig config)
   * 设置底图跟随模式
   * 说明：与操作态进行区分，跟随模式下，整个底图始终以自车为底图中心，车标更新 带动底图中心更新
   * @param follow          是否为跟随模式; {true: 设置为跟随模式, false: 取消跟随模式}
   * @param heading_up      是否车头朝北; {true: 车头朝上, false: 车头不一定朝上}
   * @param animated        是否采用动画形式生效
   * @note  1> 强调该接口仅仅控制跟随和车头朝北，关于底图朝北该接口不负责；
   *        2> 主底图和鹰眼图在跟随模式上互斥状态，只提供主底图接口，鹰眼图内部控制
   */
  virtual void SetLocationFollowed(bool follow, bool heading_up, bool animated) = 0;
  /**
   * 设置底图跟随模式
   * 说明：与操作态进行区分，跟随模式下，整个底图始终以自车为底图中心，车标更新 带动底图中心更新
   * @param config          配置
   * @note  1> 主底图和鹰眼图在跟随模式上互斥状态，只提供主底图接口，鹰眼图内部控制
   */
  virtual void SetLocationFollowedConfig(LocationFollowConfig config) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->IsLocationFollowed()
   * 获取底图跟随模式状态
   * @return      true:跟随模式; false:非跟随
   */
  virtual bool IsLocationFollowed() = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->IsHeadingUp()
   * 获取底图是否车头朝上/朝北(屏幕顶部)
   * @return      true:车头朝上; false:非车头朝上
   */
  virtual bool IsHeadingUp() = 0;

  /**
   * 设置底图为2D模式还是3D模式
   * 备注：该状态仅仅影响自动比例尺策略和自动俯仰角策略
   *      2D模式, 导航/巡航模式下 自动比例尺(若开启)仅仅缩放，不改变俯仰角
   *      2D模式, 自由模式下  俯仰角设置为0；
   *      3D模式, 导航模式下  自动比例尺(若开启)仅仅缩放，同时调整俯仰角
   *      3D模式，自由模式下  自动俯仰角(若开启) 根据当前比例尺，自动调整俯仰角
   * @param b2d          是否2D模式；{true:2D, false:3D}
   * @param animated     是否采用动画形式生效
   */
  virtual void SetMapTo2D(bool b2d, bool animated) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->Is2DMap()
   * 获取底图是否为2D模式状态
   * @return     true:2D状态;false:3D状态
   */
  virtual bool Is2DMap() = 0;

  /**
   * 设置是否开启自动比例尺策略
   * 默认值:关闭
   * @param enabled  true:开启自动比例尺; false: 关闭自动比例尺
   * @warning 非导航模式下不应该开启自动比例尺策略
   */
  virtual void SetAutoScaleEnabled(bool enabled) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->IsAutoScaleEnabled()
   * 获取自动比例尺策略状态
   * @return     true:自动比例尺开启中; false: 自动比例尺关闭中
   */
  virtual bool IsAutoScaleEnabled() = 0;

  /**
   * 淘汰，建议使用运镜比例尺 updateMagicCameraConfig
   * 设置自动比例尺策略参数
   * @param map_scale_config_container      所有自动比例尺策略的执行参数
   * @note    不设置将使用内部默认值，内部默认值 可通过GetAutoScaleConfig 获取查看
   */
  virtual void SetAutoScaleConfig(MapScaleConfigContainer map_scale_config_container) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetAutoScaleConfig()
   * 获取自动比例尺策略参数
   * @return        所有自动比例尺策略的执行参数
   */
  virtual MapScaleConfigContainer GetAutoScaleConfig() = 0;

  /**
   * 设置导航场景下所有气泡图层的扎点策略和显示策略
   * @param bubble_layer_config_container   所有气泡图层策略的执行参数
   * @note   不设置将使用内部默认值, 内部默认值 可通过GetBubbleLayerConfig 获取查看
   */
  virtual void SetBubbleLayerConfig(BubbleLayerConfigContainer bubble_layer_config_container) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->GetBubbleLayerConfig()
   * 获取导航场景下所有气泡图层的扎点和显示策略
   * @return    所有气泡图层的扎点和显示策略
   */
  virtual BubbleLayerConfigContainer GetBubbleLayerConfig() = 0;

  /**
   * 设置是否开启自动俯仰角策略
   * 默认值:关闭
   * 备注: 开启自动俯仰角策略，并不一定执行； 只有非导航状态下设置生效
   * @param enabled true:开启自动俯仰角策略; false: 关闭自动俯仰角策略
   */
  virtual void SetAutoSkewEnabled(bool enabled) = 0;

  /**
   * Deprecated
   * 请使用 CreateMapBizConfigContext()->IsAutoSkewEnabled()
   * 获取自动俯仰角策略状态
   * @return   true: 自动俯仰角策略开启中; false: 自动俯仰角策略关闭
   */
  virtual bool IsAutoSkewEnabled() = 0;

  /**
   * 增加蚯蚓线数据
   * 备注: 首次计算路线或路线完全重算情况下调用；
   *      路况刷新调用 UpdateRouteData, 伴随路线更新调用 AddCompanionRouteData;
   * @param route_plan_visitor          蚯蚓线数据访问器
   * @warning 该接口不要连续调用(内部有路线，再次调用该接口, Debug模式下会直接崩溃)
   */
  virtual void AddRouteData(const mapbase::RoutePlanVisitor& route_plan_visitor) = 0;

  /**
   * 增加蚯蚓线数据
   * 备注: 首次计算路线或路线完全重算情况下调用；
   *      路况刷新调用 UpdateRouteData, 伴随路线更新调用 AddCompanionRouteData;
   * @param route_plan_visitor          蚯蚓线数据访问器
   * @param destination_name            终点名称
   * @warning 该接口不要连续调用(内部有路线，再次调用该接口, Debug模式下会直接崩溃)
   */
  virtual void AddRouteData(const mapbase::RoutePlanVisitor& route_plan_visitor,
                            const std::string& destination_name) = 0;
  /**
   * 增加蚯蚓线数据
   * 备注: 首次计算路线或路线完全重算情况下调用；
   *      路况刷新调用 UpdateRouteData, 伴随路线更新调用 AddCompanionRouteData;
   * @param route_plan_visitor   蚯蚓线数据访问器
   * @param custom_draw_info     自定义绘制信息
   * @warning 该接口不要连续调用(内部有路线，再次调用该接口, Debug模式下会直接崩溃)
   */
  virtual void AddRouteData(const mapbase::RoutePlanVisitor& route_plan_visitor,
                            const RouteCustomDrawInfo& custom_draw_info) = 0;
  /**
   * 补充HD数据，用于车道级数据支持分包分片
   * @param route_plan_visitor HD数据
   */
  virtual void AddRouteHDInfo(const mapbase::RoutePlanVisitor& route_plan_visitor) = 0;

  /**
   * 增加蚯蚓线数据
   * 备注: 首次计算路线或路线完全重算情况下调用；
   *      路况刷新调用 UpdateRouteData, 伴随路线更新调用 AddCompanionRouteData;
   * @param route_plan_visitor   蚯蚓线数据访问器
   * @param custom_draw_info     自定义绘制信息
   * @warning 该接口不要连续调用(内部有路线，再次调用该接口, Debug模式下会直接崩溃)
   */
  virtual void AddRoute(const mapbase::RoutePlanVisitor& route_plan_visitor,
                        const RouteCustomDrawInfo& custom_draw_info) = 0;

  virtual void AddRoute(const mapbase::RoutePlanVisitor& route_plan_visitor) = 0;

  virtual void SetRouteData(const mapbase::RoutePlanVisitor& route_plan_visitor) = 0;
  /**
   * 更新蚯蚓线数据(伴随路线更新和路况刷新）
   * @param route_update_visitor 路线数据访问器
   */
  virtual void UpdateRouteData(const mapbase::RouteUpdateVisitor& route_update_visitor) = 0;
  /**
   * 添加伴随路线集合
   * 备注: 伴随路线变化或路况数据变化后 调用刷新
   * @param route_update_visitor  路线数据访问器
   * @param explain_info          伴随路线解释性信息
   */
  virtual void AddCompanionRouteData(
      const mapbase::RoutePlanVisitor& route_plan_visitor,
      const std::map<std::string, std::vector<mapbase::CompanionExplainInfo>>& explain_info) = 0;

  /**
   * 更新蚯蚓线数据
   * 备注: 目前仅仅用于更新路况
   * @param route_update_visitor         蚯蚓线数据访问器
   */
  virtual void UpdateRouteTrafficData(const mapbase::RouteUpdateVisitor& route_update_visitor) = 0;

  /**
   * 更新路线解释性信息
   * 备注: 1> 在路线偏航重算情况下 用户调用AddRouteData时需要清除之前所有路线的解释性元素;
   *      2> 在用户调用ClearRouteData时需要清除之前对应路线的解释性元素
   *      3> 其余情况，用户调用更新解析性信息，为对应路线刷新解析元素
   * @param route_explain_info          路线解释性信息
   */
  virtual void UpdateRouteExplainData(const mapbase::RouteExplainInfo& route_explain_info) = 0;

  /**
   * 设置Marker关联的Line是否显示
   * @param marker_id        MarkerID
   * @param visible          是否可见:{true:可见,false:不可见}
   * @note  点击Marker时，MapBiz会通过事件回调接口，返回Marker信息
   *        如果需要显示线，需要外部进一步调用； 不需要显示的时候，需要外部设置隐藏掉；
   *        备注:若Marker没有关联线路，设置也不会显示
   */
  virtual void SetRouteExplainMarkerLineVisible(const std::string marker_id, bool visible) = 0;
  /**
   * 设置路线解释性配置
   * @param route_explain_config 配置
   */
  virtual void SetRouteExplainConfig(RouteExplainConfig route_explain_config) = 0;
  /**
   * 设置Marker关联的Icon是否显示
   * @param marker_id    MarkerID
   * @param visible      是否可见:{true:可见,false:不可见}
   * @note  点击Marker时，若期望底图上的Marker隐藏，一种方式创建一个高优先级Marker，另外通过该接口设置隐藏
   */
  virtual void SetRouteExplainMarkerIconVisible(const std::string marker_id, bool visible) = 0;
  /**
   * 即将删除---接口，请使用接口 SetRouteExplainConfig
   * 设置路线解释性单独线和区域面是否显示
   * @param lineVisible        单独线是否可见:{true:可见,false:不可见}
   * @param areaVisible        区域面是否可见:{true:可见,false:不可见}
   */
  virtual void SetRouteExplainLineAndAreaVisible(bool lineVisible, bool areaVisible) = 0;

  /**
   * 清除路线解释性单独线和区域面
   */
  virtual void ClearRouteExplainLineAndArea() = 0;

  /**
   * 清除指定蚯蚓线数据
   * @param clear_all                    是否擦出所有路线, 若为false 则需要填充路线ID列表;
   * @param route_id_vector              要清除的路线ID列表
   */
  virtual void ClearRouteData(const bool clear_all, std::vector<std::string> route_id_vector) = 0;

  /**
   * 设置当前主路线ID
   * @param selected_route_id
   */
  virtual void SetSelectedRouteId(std::string selected_route_id) = 0;
  /**
   * 动态设置非选中路线是否隐藏的能力
   * 备注:内部默认值为false, 会展示；
   * @param hidden 是否隐藏，true: 隐藏非选中路线; false: 不隐藏非选中路线
   * @param callback 非选中路线隐藏状态变更后的回调
   * @apiNote 用户调用该接口后，应该监听回调，在收到状态变更完成前，不应该再次调用或切换页面；
   *                       不设置回调或不监听回调，可能出现UI 与 图面显示状态不完全一致的问题;
   */
  virtual void SetUnSelectedRouteHidden(bool hidden, std::weak_ptr<MapBizRouteCallBack> callback) = 0;
  /**
   * 获取剩余路线的最大外界框
   * @param route_ids   指定路线ID，若列表为空，返回内部所有路线构成的最大外界框，注意路线边界框计算不包含已隐藏路线
   * @param callback    结果回调
   */
  virtual void GetRemainRouteMaxBound(std::vector<std::string> route_ids,
                                      std::weak_ptr<MapBizRouteCallBack> callback) = 0;
  /**
   * 仅用于导航态（NavMode::Navigation）更新诱导段信息
   * 备注: 包含所有路线(含伴随路线)信息
   * @param guidance_segment_info   诱导信息集合
   */
  virtual void UpdateGuidanceSegmentInfo(mapbase::GuidanceUpdateInfo guidance_segment_info) = 0;

  /**
   * 导航态更新车标位置，蚯蚓线置灰索引，转向箭头，路名，伴随气泡，鹰眼图显示区域
   * 备注: 包含所有路线(含伴随路线)信息
   * @param guidance_segment_info  [in] 诱导信息集合 {@link
   * com.tencent.pangu.mapbase.common.guidance.GuidanceUpdateInfo}
   * @param custom_control_param 用户自定义控制信息
   */
  virtual void UpdateGuidanceSegmentInfo(mapbase::GuidanceUpdateInfo guidance_segment_info,
                                         CustomControlParam custom_control_param) = 0;

  /**
   * 进入转向路口通知(MapBiz用于路口增强自动比例尺策略)
   * @param intersection  路口信息
   * @note 不是一定会有回调, 只在100-200m范围内触发
   */
  virtual void EnterTurnIntersection(mapbase::ApproachingTurnInfo intersection) = 0;

  /**
   * 离开转向路口通知(MapBiz用于路口增强自动比例尺策略)
   * @param last_segment_index 上一个转向位置
   */
  virtual void LeaveTurnIntersection(int last_segment_index) = 0;

  /**
   * 更新拥堵气泡信息
   * @param traffic_bubble          拥堵气泡信息
   * @note  1.用于导航场景或巡航场景 2. 最新数据会覆盖上一次数据
   */
  virtual void UpdateTrafficBubble(mapbase::TrafficJamInfo traffic_bubble) = 0;

  /**
   * 清除拥堵气泡信息
   * @note 1.用于导航场景或巡航场景
   */
  virtual void ClearTrafficBubble() = 0;

  /**
   * 更新警示牌信息
   * @param warning_tip_bubble      警示牌信息
   * @note  1.用于导航场景或巡航场景 2. 最新数据会覆盖上一次数据
   */
  virtual void UpdateWarningTipBubble(mapbase::WarningSignInfo warning_tip_bubble) = 0;

  /**
   * 清除安全警示牌信息
   * @note 1.用于导航场景或巡航场景
   */
  virtual void ClearWarningTipBubble() = 0;

  /**
   * 更新电子眼数据, 路线变化后需要重现添加
   * 清理电子眼，也是这个接口，数据为空即可
   * @param safety_camera_data      电子眼数据
   */
  virtual void UpdateSafetyCameraRawData(mapbase::RouteCameraRefreshInfo safety_camera_data) = 0;

  /**
   * 更新巡航主路线上前方电子眼序号距离信息, 目前Biz内部用于放大对应电子眼
   * @param cameras     电子眼序列信息
   */
  virtual void UpdateFrontCamerasOnRouteInCruise(std::vector<mapbase::RouteCameraInRange> cameras) = 0;
  /**
   * 清理已经走的电子眼数据
   * @param clear_cameras           需要擦出的电子眼
   */
  virtual void ClearSafetyCameraRawData(std::vector<int> clear_cameras) = 0;

  /**
   * 更新交通事件信息, 路线变化后需要重现添加
   * @param traffic_event_vector
   */
  virtual void UpdateTrafficEventItems(std::vector<mapbase::TrafficEventInfo> traffic_event_vector) = 0;

  /**
   * 清理已经走过的交通事件信息
   * @param traffic_event_vector
   */
  virtual void ClearTrafficEventItems(std::vector<mapbase::TrafficEventInfo> traffic_event_vector) = 0;

  /**
   * 更新鱼骨路线上的诱导信息(电子眼+交通事件+拥堵气泡)
   * @param guidance_item_vector              诱导信息列表
   * @note  1. 仅仅用于巡航场景
   */
  virtual void UpdateFishBoneGuidanceItems(std::vector<mapbase::CruiseFishBoneDisplayInfo> guidance_item_vector) = 0;

  /**
   * 清理鱼骨路线上已经走过的诱导信息
   * @param guidance_item_index_vector        鱼骨路线索引列表
   * @note  1. 仅仅用于巡航场景
   */
  virtual void ClearFishBoneGuidanceItems(std::vector<mapbase::CruiseFishBoneHideInfo> guidance_item_index_vector) = 0;

  /**
   * 查询点击的元素(图层) 类型
   * 说明:该方法在UI主线成中被调用，用于端上判断是否进入操作态处理
   * @param x   屏幕坐标
   * @param y   屏幕坐标
   * @return    图层类型
   */
  virtual LayerType QueryClickLayerType(float x, float y) = 0;
  /**
   * 切换地图导航类型(传统导航底图/车道级底图)，仅用于控制自动比例尺类型
   * @param map_scene_type
   */
  virtual void SetMapSceneType(mapbase::MapSceneType map_scene_type) = 0;

  /**
   * 计算引导面，当接收到诱导或者盘古相应的回调后调用。
   * 诱导回调：OnUpdateGuideArea
   * @deprecated 请迁移到void UpdateMultiGuideArea(const std::vector<mapbase::DIHDAreaOperation>& operations)使用
   * @param operation 诱导/盘古传过来的引导面operation
   */
  virtual void UpdateGuideArea(mapbase::DIHDAreaOperation operation) {}
  /**
   * 计算引导面，当接收到诱导或者盘古相应的回调后调用。
   * 诱导回调：OnUpdateGuideArea
   * @param operation 诱导/盘古传过来的引导面operation
   */
  virtual void UpdateMultiGuideArea(const std::vector<mapbase::DIHDAreaOperation>& operations) = 0;
  /**
   * 更新车道级车标信息以及障碍物信息
   * @param hd_locator_info 高精定位信息
   */
  virtual void UpdateHighFreqLocInfo(mapbase::HighFreqLocInfo hd_req_loc_info) = 0;

  /**
   * 更新变道状态,此接口已经淘汰
   * @param is_changing_lane true:变道中，false:无变道或变道完成
   */
  virtual void UpdateChangeLaneStatus(bool is_changing_lane) = 0;
  /**
   * 更新车道级转向墙信息
   * @param turn_wall_info 转向墙信息
   */
  virtual void UpdateTurnWallInfo(const mapbase::TurnWallInfo turn_wall_info) = 0;
  /**
   * 更新目标车道信息
   * @param target_lane_info 目标车道信息
   */
  virtual void UpdateTargetLaneInfo(const mapbase::TurnLaneInfo target_lane_info) = 0;

  /**
   * 设置网路能力
   * @param http_interface
   * @note 目前动态瓦片图层会用到
   */
  virtual void SetNetProvider(std::shared_ptr<mapbase::HttpInterface> http_interface) = 0;

  /**
   * 动态创建一个TileLayer，创建结果通过回调形式返回 OnOperateTileLayerResult
   * @param map_tile_layer_option  图层选项，其中data_source为一个图层的唯一标示
   * @warning 创建动态图层前，请显示设置网络能力，没有网路能力前，创建会失败(见监听)
   */
  virtual void AddTileLayer(MapTileLayerOption map_tile_layer_option) = 0;
  /**
   * 修改一个已有的图层TileLayer的属性, 操作结果通过回调形式返回 OnOperateTileLayerResult
   * @param map_tile_layer_option 图层选项，其中data_source为一个图层的唯一标示
   */
  virtual void ModifyTileLayer(MapTileLayerOption map_tile_layer_option) = 0;
  /**
   * 删除一个已有的图层TileLayer 操作结果通过回调形式返回 OnOperateTileLayerResult
   * @param data_source       图层唯一标示
   */
  virtual void RemoveTileLayer(std::string data_source) = 0;

  /**
   * 设置指定的途经点选中，-1取消选中
   * @param index 途经点索引
   */
  virtual void SetViaPointSelected(int index) = 0;

  /**
   * 更新途径点信息，比如是否选中，比如是否抵达
   * @param info 途经点信息
   */
  virtual void UpdateViaPointInfo(ViaPointInfo info) = 0;

  /**
   * 设置地图全览状态
   * @param screen_rect 屏幕区域
   * @param additional_bounds 额外元素的经纬度范围
   * @param route_ids 路线id，若列表为空，返回内部所有路线构成的最大外界框，注意路线边计算界框不包含已隐藏路线
   * @param whole_route 使用完整路线还是剩余路线
   */
  virtual void SetOverViewMode(const MapRectD& screen_rect,
                               const std::vector<mapbase::Rect<mapbase::GeoCoordinate>>& additional_bounds,
                               const std::vector<std::string>& route_ids, bool whole_route) = 0;

  /**
   * 根据传入参数设置地图路线全览
   * @param param 参数
   */
  virtual void OverLook(const OverLookParam& param) = 0;

  /**
   * 根据传入参数设置地图室内路线全览
   * @param param 参数
   */
  virtual void IndoorOverLook(const IndoorOverLookParam& param) = 0;

  /**
   * 设置Overlay需要避让的全量UI控件区域; 当个数为0代表清空UI区域；
   * @param ui_screen_bounds 控件区域列表；像素坐标
   */
  virtual void SetOverlayAvoidingUIAreas(const std::vector<MapRectD>& ui_screen_bounds) = 0;

  /**
   * 设置指定的路线隐藏显示
   * @param route_ids 路线id列表
   * @param hidden 是否隐藏
   */
  virtual void SetRouteHidden(const std::vector<std::string>& route_ids, bool hidden) = 0;

  /**
   * 切换楼层。用户切换楼层控件或者用户行驶过程中切换了楼层，需要调用此接口通知mapbiz
   * @param building_id  建筑物id
   * @param floor_name 楼层名字
   */
  virtual void SetIndoorActiveFloor(const std::string& building_id, const std::string& floor_name) = 0;

  /**
   * 更新音画同步信息
   * @param voice_sync_info
   */
  virtual void UpdateVoiceSyncInfo(const mapbase::RoadFeatureInfo& voice_sync_info) = 0;

  /**
   * 开启/关闭测试环境
   * @param enable 测试环境开关
   * @param url 测网域名url
   */
  virtual void SetTestEnvironment(bool enable, const std::string& url) = 0;

  /**
   * 添加鹰眼图
   * @param map_handle 底图引擎句柄
   */
  virtual void AttachOverviewMap(void* map_handle) = 0;

  /**
   * 移除鹰眼图
   */
  virtual void DetachOverviewMap() = 0;

  /**
   * 即将进入隧道，目前用于控制车道级导航下的隧道自动比例尺启动时机
   */
  virtual void EnterTunnel(mapbase::EnterTunnelInfo info) = 0;
  /**
   * 已经驶离隧道，目前用于控制车道级导航下的隧道自动比例尺结束时机
   */
  virtual void LeaveTunnel() = 0;

  /**
   * @deprecated 请使用接口 void SetSmartVisionConfig(SmartVisionConfig config)
   * 设置智能视角是否开启（默认导航态车头朝北模式开启）
   * @param enable                是否开启
   * @param default_scene_enable  是否开启默认场景智能视角，enable开启时有效
   */
  virtual void SetSmartVisionEnable(bool enable, bool default_scene_enable = true) = 0;

  /**
   * 设置智能视角是否开启（默认导航态车头朝北模式开启）
   * @param config 开关配置
   */
  virtual void SetSmartVisionConfig(SmartVisionConfig config) = 0;

  /**
   * 进入智能视角主辅路场景
   * @param feature_info 智能识别道路特征信息
   */
  virtual void EnterSmartVisionMainSide(const mapbase::RoadFeatureInfo& feature_info) = 0;

  /**
   * 退出智能视角主辅路场景
   */
  virtual void LeaveSmartVisionMainSide() = 0;

  /**
   * 进入智能视角引导面变道场景
   * @param feature_info 智能识别道路特征信息
   */
  virtual void EnterSmartVisionChangeLane(const mapbase::RoadFeatureInfo& feature_info) = 0;

  /**
   * 退出智能视角引导面变道场景
   */
  virtual void LeaveSmartVisionChangeLane() = 0;

  /**
   * HD特殊车道提示
   */
  virtual void UpdateHDSpecialLaneInfo(const mapbase::SpecialLaneStateInfo& lane_info) = 0;

  /**
   * 更新红绿灯状态数据，用于显示隐藏红绿灯倒计时气泡，支持一个气泡展示多个红绿灯
   * @param info   红绿灯状态数据
   */
  virtual void UpdateTrafficMultiLightCountdownTimer(const mapbase::MultiLightCountdownTimerInfo& info) = 0;
  /**
   * 是否进入/退出特殊场景
   * @param scene_type_info    场景信息
   */
  virtual void UpdatePassingEvent(const mapbase::SceneTypeInfo& scene_type_info) = 0;

  /**
   * 更新通用气泡信息
   * @param callback_type  回调类型
   * @param info  通用气泡信息
   */
  virtual void UpdateCommonBubbleData(const mapbase::CallbackType& callback_type,
                                      const mapbase::CommonBubbleInfo& info) = 0;

  /**
   * 更新等灯气泡信息
   * @param info 等灯信息
   * @note 最新数据会覆盖上一次数据
   */
  virtual void UpdateLightWaitBubble(const mapbase::TrafficLightWaitInfo& info) = 0;

  /**
   * 清除等灯气泡信息
   */
  virtual void ClearLightWaitBubble() = 0;

  /**
   * 销毁底图对象，析构前需调用
   */
  virtual void Destroy() = 0;

  /**
   * 进入变道区
   * @param info 变道区信息
   */
  virtual void EnterSwitchLaneArea(const mapbase::SwitchLaneAreaInfo& info) = 0;

  /**
   * 退出变道区
   * @param info 变道区信息
   */
  virtual void LeaveSwitchLaneArea(const mapbase::SwitchLaneAreaInfo& info) = 0;

  /**
   * 计算引导线，计算成功后显示引导线
   * @param mode        引导线计算模式
   * @param max_length  最长计算长度
   */
  virtual void UpdateGuideLine(GuideLineMode mode, double max_length) = 0;

  /**
   * 移除引导线
   */
  virtual void RemoveGuideLine() = 0;

  /**
   * mapbiz控制类事件， 例如 刷新指定overlay
   * 预期后续可以通过json形式控制biz
   * @param info
   */
  virtual void AddBizControlEvent(const MapBizControlEventInfo& info) = 0;
  /**
   * 模拟车道级定位结果，仅用于测试
   * @param coor_idx  SD吸附索引
   * @param pos       SD吸附位置
   * @param is_left   是否获取左侧车道位置，
   * @return 车道级定位结果
   */
  virtual mapbase::HDLaneMatchResult MockLaneMatchPos(int coor_idx, const mapbase::GeoCoordinate& pos, bool is_left,
                                                      const MockLaneEx& ex) = 0;

  virtual void AddDebugInfo(const DebugInfoData& debug_info) = 0;
  /**
   * 模拟生成地库的数据
   */
  virtual void MockGenerateUndergroundParkData() = 0;

  /**
   * 返回同步存储mapbiz 配置结果。
   * 当调用mapbiz的Config发生更改时，context内部存储的config同步更改。通过configContext拿到的配置保证是最新的
   * @return MapBizConfigContext 对象
   */
  virtual std::shared_ptr<MapBizConfigContext> GetMapBizConfigContext() = 0;

  /**
   * 已经淘汰，建议使用运镜比例尺 建议使用 updateMagicCameraConfig 运镜比例尺
   * @return
   */
  virtual std::shared_ptr<MapBizHDScaleController> GetHDScaleController() = 0;

  virtual std::shared_ptr<MapBizHDController> GetMapBizHDController() = 0;
  /*
   * 从C++层查询每条路线上的分歧点索引位置
   */
  virtual DebugRouteXInfo AcquireRouteDebugInfo(const std::string& route_id) = 0;

  /**
   * 更新mapbiz全局配置
   * @param global_config 全局配置
   */
  virtual void UpdateGlobalConfig(GlobalConfig global_config) = 0;

  /**
   * 更新长链接数据，内部是RealTimeCommWrap 包着的结构
   * @param data 数据
   */
  virtual void UpdateRealTimeCommData(std::unique_ptr<std::vector<uint8_t>> data) = 0;

  /**
   * overlay点击结束事件
   * @param info overlay信息合集
   */
  virtual void OverlayClickFinish(std::shared_ptr<mapbase::OverlayInfo> info) = 0;

  /**
   * 更新overlay options，更新落车点等option和车标一起更新
   * @param options 信息合集，落车点等options
   */
  virtual void UpdateOverlayOptionsWithLoc(std::vector<std::shared_ptr<MAPAPI::OverlayOptions>> options) = 0;

};  // MapBizManager

__MAPBIZ_NAMESPACE_END__

