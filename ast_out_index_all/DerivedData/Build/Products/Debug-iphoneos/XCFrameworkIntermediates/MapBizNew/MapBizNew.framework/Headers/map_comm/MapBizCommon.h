//
//  MapBizCommon.h
//  MapBiz
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/11/29.
//

#pragma once

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 定义地图的昼夜模式
 */
typedef NS_ENUM(NSInteger, MapBizMapMode) {
    MapBizMapModeDay = 0,    // 白天模式
    MapBizMapModeNight = 1,  // 夜间模式
};

/**
 * 导航模式
 */
typedef NS_ENUM(NSInteger, MapBizNavMode) {
    MapBizNavModeFree = 0,        // 自由状态(非导航非巡航)
    MapBizNavModeNavigation = 1,  // 导航状态
    MapBizNavModeCruise = 2,      // 巡航状态
};

@interface MapBizDynamicFrameRateConfig : NSObject
/**
 * 每帧变化的最大距离, 单位屏幕像素, 默认值3像素;
 */
@property (nonatomic, assign) float maxDistancePerFrame;
/**
 * 每帧变化的最大角度, 单位角度, 默认值3度;
 */
@property (nonatomic, assign) float maxDegreePerFrame;
/**
 * 关闭动态降帧 或 帧率候选值无效的情况下 使用的帧率值, 默认值 60帧率；
 */
@property (nonatomic, assign) float defaultFrameRate;
/**
 * 车标动画时长扩展系数
 */
@property (nonatomic, assign) float animationDurationRate;
/**
 * 最终帧率选取的候选值，综合计算一个帧率值后 然后在候选值中选取一个最接近的值(不小于计算的帧率,如需要帧率为12，最终会用15)
 * 候选值列表需要有序，且为升序，默认值:[0,5,10,15,20,25,30]；
 */
@property (nonatomic, copy, nullable) NSArray<NSNumber *> *candidateFrameRates;
/**
 * 是否通过native层设置帧率，如果false，通过MapBizEventListener.OnNotifyFPS(unsigned int) 回调帧率给客户端设置帧率
 */
@property (nonatomic, assign) BOOL nativeSetFPS;
/**
 * 核查并修正配置
 */
- (void)checkAndRectifyConfig;
/**
 * 获取一个最佳匹配帧率值
 * @param frameRate 帧率值
 * @return 一个最佳匹配帧率值
 */
- (int)getBestFrameRate:(int)frameRate;

@end

/**
 * 调试内容分类
 */
typedef NS_OPTIONS(NSInteger, MapBizDebugContentCatalog) {
    MapBizDebugContentCatalogMapScale = 1 << 0,  // 调试自动比例尺
    MapBizDebugContentCatalogMapLayer = 1 << 1,  // 调试图层显示
};

FOUNDATION_EXPORT NSErrorDomain const MapBizErrorDomain;

NS_ERROR_ENUM(MapBizErrorDomain) {
    // mapbase::error_code
    MapBizErrorDomainAddTileLayerFailed = 2001, /** 添加TileLayer失败,没有设定数据源 */
    MapBizErrorDomainModifyTileLayerFailed = 2002,
    MapBizErrorDomainRemoveTileLayerFailed = 2003,
};

NS_ASSUME_NONNULL_END
