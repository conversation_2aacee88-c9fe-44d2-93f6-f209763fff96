// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/8/5.
//

#pragma once

#include "map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__

/**
 * 自动比例尺策略类型
 */
enum class MapScaleHandlerType {
  /**
   * 非法类型
   */
  None = -1,
  /**
   * 拥堵自动比例尺策略
   */
  TrafficJam = 1,
  /**
   * 弯道自动比例尺策略
   */
  CurveRoute = 2,
  /**
   * 常规自动比例尺策略
   */
  Normal = 3,
  /**
   * 启动场景自动比例尺策略
   */
  StartUp = 4,
  /**
   * 终点场景自动比例尺策略
   */
  ApproachEnd = 5,
  /**
   * 伴随分歧点场景自动比例尺策略
   */
  CompanionFork = 6,
  /**
   * 室内场景
   */
  Indoor = 7,
  /**
   * 音画同步自动比例尺策略
   */
  VoiceSync = 8,
  /**
   * 车标游离态场景策略配置
   */
  LocatorFree = 9,
  SDScaleNum = 10,
  /**
   * 车道级导航缩放
   */
  HDScale = 80,
  HD_Default = 81,
  HD_Intersection = 82,
  HD_Tunnel = 83,
  HD_Toll_Station = 86,
  HD_Roundabout = 87,
};

/**
 * 自动比例尺策略参数配置基类
 */
class MapScaleBaseConfig {
 public:
  explicit MapScaleBaseConfig(const MapScaleHandlerType& type);

 public:
  bool operator==(const MapScaleBaseConfig& other) const;
  bool operator!=(const MapScaleBaseConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const MapScaleBaseConfig& scale_config);
  MapScaleHandlerType GetType() const;
  std::string GetTypeStr() const;

 private:
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param other
   * @return   true:相同，false:不相同
   */
  bool IsEqualOfOtherParameters(const MapScaleBaseConfig& other) const;

 public:
  /**
   * 策略类型
   */
  MapScaleHandlerType type_;
  /**
   * 策略启用标志(默认值:true)
   */
  bool enabled_;
  /**
   * 该策略执行时是否需要左右移动自车中心(默认值:false)
   */
  bool move_car_pos_enabled_;
  /**
   * 该策略执行时是否需要根据比例尺调整俯仰角(线性调整, 默认值:true)
   */
  bool adjust_skew_enabled_;
  /**
   * 该策略允许显示的最小比例尺(默认值:15, 100米)
   * 参考map_layer_define.h中比例尺映射定义
   */
  float min_scale_level_;
  /**
   * 该策略允许显示的最大比例尺(默认值:18, 10米)
   * 参考map_layer_define.h中比例尺映射定义
   */
  float max_scale_level_;
  /**
   * 该策略允许显示的最小俯仰角(默认值:20.0度)
   */
  float min_skew_;
  /**
   * 该策略允许显示的最大俯仰角(默认值:45.0度)
   */
  float max_skew_;
  /**
   * 自车左右移动策略开启情况下, 允许在有效区域移动的最左预留系数(1/3)
   */
  float valid_area_left_rate_;
  /**
   * 自车左右移动策略开启情况下, 允许在有效区域向左移最右预留系数(1/3)
   */
  float valid_area_right_rate_;
  /**
   * 当该策略执行结束后保持冻结缩放的最大距离
   * 单位：米， 默认值：50
   */
  float max_freeze_distance_ = 50;
};

/**
 * 路况拥堵自动比例尺策略 参数配置
 */
class TrafficJamScaleConfig : public MapScaleBaseConfig {
 public:
  TrafficJamScaleConfig();

 public:
  bool operator==(const TrafficJamScaleConfig& other) const;
  bool operator!=(const TrafficJamScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const TrafficJamScaleConfig& scale_config);

 public:
  /**
   * 进入路况拥堵策略的条件: 路况拥堵最小长度(默认值:50米)
   */
  float min_length_;
  /**
   * 进入路况拥堵策略的条件: 路况拥堵最大长度(默认值:400米)
   */
  float max_length_;
  /**
   * 进入路况拥堵策略的条件: 自车距离拥堵起点的最大距离(默认值:500米)
   */
  float max_distance_to_start_point;

  /**
   * 高速距离下一个机动点多少m，将不在进入此比例尺
   */
  float freeway_distance_threshold_no_enter_ = 2300;
  /**
   * 城快距离下一个机动点多少m，将不在进入此比例尺
   */
  float urban_freeway_distance_threshold_no_enter_ = 1200;

  /**
   * 普通道路距离下一个机动点多少m，将不在进入此比例尺
   */
  float low_class_road_distance_threshold_no_enter_ = 600;
};

/**
 * 弯道识别自动比例尺策略 参数配置
 */
class CurveRouteScaleConfig : public MapScaleBaseConfig {
 public:
  CurveRouteScaleConfig();

 public:
  bool operator==(const CurveRouteScaleConfig& other) const;
  bool operator!=(const CurveRouteScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const CurveRouteScaleConfig& scale_config);

 public:
  /**
   * 弯道定义: 弯道最大半径(默认值:300米）
   */
  float max_radius_;
  /**
   * 弯道定义: 最小累计弧度(默认值:50度)
   */
  float min_accumulated_angle_;
  /**
   * 弯道定义: 最小累计弧长(默认值:50米)
   */
  float min_accumulated_length_;
  /**
   * 进入路况拥堵策略的条件: 自车距离拥入弯点的最大距离(默认值:500米)
   */
  float max_distance_to_start_point_;
  /**
   * 根据弯道半径实际大小 采用不同的比例尺，半径分割线(默认值150米)
   * 弯道半径 >scale_level_separator_radius_ 最大比例尺为: (max_scale_level_ - 1)
   * 弯道半径<=scale_level_separator_radius_ 最大比例尺为: max_scale_level_
   */
  float scale_level_separator_radius_;
  /**
   * 高速距离下一个机动点多少m，将不进入此比例尺
   */
  float freeway_distance_threshold_no_enter_ = 2300;
  /**
   * 城快距离下一个机动点多少m，将不进入此比例尺
   */
  float urban_freeway_distance_threshold_no_enter_ = 1200;
  /**
   * 普通道路距离下一个机动点多少m，将不进入此比例尺
   */
  float low_class_road_distance_threshold_no_enter_ = 600;
  /**
   * 高速离机动点距离远时比例尺最小等级【15级】
   */
  float min_freeway_scale_level_for_far_intersection_ = 15;
  /**
   * 高速离机动点距离远时比例尺最大等级【17级】
   */
  float max_freeway_scale_level_for_far_intersection_ = 17;
  /**
   * 城快离机动点距离远时比例尺最小等级【15级】
   */
  float min_urban_freeway_scale_level_for_far_intersection_ = 15;
  /**
   * 城快离机动点距离远时比例尺最大等级【17级】
   */
  float max_urban_freeway_scale_level_for_far_intersection_ = 17;
  /**
   * 普通道路离机动点距离远时比例尺最小等级【15级】
   */
  float min_low_class_road_scale_level_for_far_intersection_ = 15;
  /**
   * 普通道路离机动点距离远时比例尺最大等级【18级】
   */
  float max_low_class_road_scale_level_for_far_intersection_ = 18;
};

/**
 * 常规自动比例尺策略 参数配置
 */
class NormalScaleConfig : public MapScaleBaseConfig {
 public:
  NormalScaleConfig();

 public:
  bool operator==(const NormalScaleConfig& other) const;
  bool operator!=(const NormalScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const NormalScaleConfig& scale_config);

 public:
  /**
   * 3D 模式 自车距离与路口的距离阀值 用于判断缩放还是保持
   * 俯仰角和比例尺均参考如下规则:
   * 小于等于该值，则使用缩放，需要设定最小/最大范围
   * 大于该值，则使用保持, 比例尺和俯仰角均用最大值
   * 单位:米
   */
  float distance_threshold_to_intersection_3d = 600;

  /**
   * 2D 模式 自车距离与路口的距离阀值 用于判断缩放还是保持
   * 俯仰角保持最小值；比例尺参考如下规则:
   * 小于等于该值，则使用缩放，需要设定最小/最大范围
   * 大于该值，则使用保持, 比例尺使用最大值
   * 单位:米
   */
  float distance_threshold_to_intersection_2d = 2000;

  /**
   * 高速离机动点距离远时比例尺最小等级
   */
  float freeway_scale_level_for_far_intersection = 14;

  /**
   * 高速比例尺限制距离阈值
   */
  float freeway_distance_threshold_to_intersection = 2000;

  /**
   * 城快离机动点距离远时比例尺最小等级【15级】
   */
  float urban_freeway_scale_level_for_far_intersection = 15;

  /**
   * 城快比例尺限制距离阈值
   */
  float urban_freeway_distance_threshold_to_intersection = 1000;

  /**
   * 普通道路离机动点距离远时比例尺最小等级【16级】
   */
  float low_class_road_scale_level_for_far_intersection = 16;

  /**
   * 普通道路比例尺限制距离阈值
   */
  float low_class_road_distance_threshold_to_intersection = 500;

  /**
   * 高速从其他比例尺强制进入机动点比例尺阈值
   */
  float freeway_distance_threshold_scale_to_intersection = 2000;
  /**
   * 城快从其他比例尺强制进入机动点比例尺阈值
   */
  float urban_freeway_distance_threshold_scale_to_intersection = 1000;

  /**
   * 其他道路从其他比例尺强制进入机动点比例尺阈值
   */
  float low_class_road_distance_threshold_scale_to_intersection = 500;
};

/**
 * 启动场景自动比例尺策略 参数配置
 */
class StartUpScaleConfig : public MapScaleBaseConfig {
 public:
  StartUpScaleConfig();

 public:
  bool operator==(const StartUpScaleConfig& other) const;
  bool operator!=(const StartUpScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const StartUpScaleConfig& scale_config);

 public:
  /**
   * 用户发起导航后驾驶时间在max_time秒内 或驾驶距离在max_distance米内 均算起点策略
   * 单位:秒, 默认值: 10秒
   */
  float max_time_ = 10;
  /**
   * 用户发起导航后驾驶时间在max_time秒内 或驾驶距离在max_distance米内 均算起点策略
   * 单位:米, 默认值: 50米
   */
  float max_distance_ = 50;
};

/**
 * 终点场景自动比例尺策略 参数配置
 */
class ApproachEndScaleConfig : public MapScaleBaseConfig {
 public:
  ApproachEndScaleConfig();

 public:
  bool operator==(const ApproachEndScaleConfig& other) const;
  bool operator!=(const ApproachEndScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const ApproachEndScaleConfig& scale_config);
};

/**
 * 伴随路分歧点场景比例尺策略参数配置
 */
class CompanionForkScaleConfig : public MapScaleBaseConfig {
 public:
  CompanionForkScaleConfig();

 public:
  bool operator==(const CompanionForkScaleConfig& other) const;
  bool operator!=(const CompanionForkScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const CompanionForkScaleConfig& scale_config);

  /**
   * 高速距离下一个机动点多少m，将不在进入此比例尺
   */
  float freeway_distance_threshold_no_enter_ = 2300;
  /**
   * 城快距离下一个机动点多少m，将不在进入此比例尺
   */
  float urban_freeway_distance_threshold_no_enter_ = 1200;

  /**
   * 普通道路距离下一个机动点多少m，将不在进入此比例尺
   */
  float low_class_road_distance_threshold_no_enter_ = 600;
};

/**
 * 室内场景自动比例尺策略 参数配置
 */
class IndoorScaleConfig : public MapScaleBaseConfig {
 public:
  IndoorScaleConfig();

 public:
  bool operator==(const IndoorScaleConfig& other) const;
  bool operator!=(const IndoorScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const IndoorScaleConfig& scale_config);
};

/**
 * 音画同步场景比例尺策略 参数配置
 */
class VoiceSyncScaleConfig : public MapScaleBaseConfig {
 public:
  VoiceSyncScaleConfig();

 public:
  bool operator==(const VoiceSyncScaleConfig& other) const;
  bool operator!=(const VoiceSyncScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const VoiceSyncScaleConfig& scale_config);

 public:
  /**
   * 冻结比例尺(策略)的时间, 默认4秒, 含1秒动画时间
   */
  float max_freeze_time_sec_ = 4;
};

/**
 * 车标游离态场景比例尺策略 参数配置
 */
class LocatorFreeScaleConfig : public MapScaleBaseConfig {
 public:
  LocatorFreeScaleConfig();

 public:
  bool operator==(const LocatorFreeScaleConfig& other) const;
  bool operator!=(const LocatorFreeScaleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LocatorFreeScaleConfig& scale_config);
};

/**
 * 自动比例尺策略参数
 */
class MapScaleConfigContainer {
 public:
  bool operator==(const MapScaleConfigContainer& other) const;
  bool operator!=(const MapScaleConfigContainer& other) const;
  friend std::ostream& operator<<(std::ostream& out, const MapScaleConfigContainer& scale_config_container);

 public:
  /**
   * 路况拥堵策略配置
   */
  TrafficJamScaleConfig traffic_jam_scale_config_;
  /**
   * 弯道识别策略配置
   */
  CurveRouteScaleConfig curve_route_scale_config_;
  /**
   * 常规策略配置
   */
  NormalScaleConfig normal_scale_config_;
  /**
   * 起点策略配置
   */
  StartUpScaleConfig start_up_scale_config_;
  /**
   * 终点策略配置
   */
  ApproachEndScaleConfig approach_end_scale_config_;
  /**
   * 伴随分歧点策略配置
   */
  CompanionForkScaleConfig companion_fork_scale_config_;
  /**
   * 室内路段导航策略配置
   */
  IndoorScaleConfig indoor_scale_config_;
  /**
   * 音画同步策略配置
   */
  VoiceSyncScaleConfig voice_sync_scale_config_;
  /**
   * 车标游离态策略配置
   */
  LocatorFreeScaleConfig locator_free_scale_config_;
};

__MAPBIZ_NAMESPACE_END__
