//
//  MapBizManager.h
//  MapBiz
//
//  Created by z<PERSON><PERSON><PERSON><PERSON> on 2021/11/29.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <MapBaseNew/MapBaseRouteStructure.h>
// #import <MapBaseNew/MapBaseLoggerConfig.h>
#import <MapBaseNew/MapBaseGuidanceBaseStructure.h>
#import <MapBaseNew/MapBaseRoutePlanVisitor.h>
#import <MapBizNew/map_comm/MapBizCommon.h>
#import <MapBizNew/map_comm/MapBizGuideArea.h>
#import <MapBizNew/map_comm/MapBizOption.h>
#import <MapBizNew/map_layer/MapBizMapLayerDefine.h>
#import <MapBizNew/map_tilelayer/MapBizMapTileLayer.h>
#import <MapBizNew/map_tilelayer/MapBizMapTileLayerDefine.h>
#import <MapBizNew/map_resource/MapBizResourceDefine.h>
#import <MapBizNew/map_scale/MapBizMapScaleDefine.h>

NS_ASSUME_NONNULL_BEGIN

/// 路线绘制完成的通知, notification 的 object 为 MapBizManager 对象，userinfo 为 MapBizEventDelegate 的方法 -onRouteLineCreated: 的参数
/// routeOverlayIds
extern NSString *const MapBizRouteLineDrawFinishNotification;

@class MapBizManager;
@protocol MapBizEventDelegate <NSObject>
@optional
/**
 * 导航场景中，根据具体场景，适当调整帧率；
 * 非必须实现
 * @param fps
 */
- (void)manager:(MapBizManager *)manager onNotifyFPS:(unsigned int)fps;

/**
 * 响应气泡扎点信息
 * 端上需要扎点位置信息，自己绘制气泡的话需要监听；
 * @param type                        气泡扎点
 * @param container      {路线ID，扎点位置列表} 对象列表
 * @note 只有通过扎点配置启动了回调，才会有回调，参考BubbleLayerBaseConfig配置
 */
- (void)manager:(MapBizManager *)manager
    onNotifyMapBubblePositionsWithType:(MapBizBubbleLayerType)type
                             container:(MapBizBubbleCandidatePositionContainer *)container;
/**
 * 响应路线点击回调
 * @param routeId   点击路线ID
 */
- (void)manager:(MapBizManager *)manager onTapRouteLineWithRouteId:(NSString *)routeId;
/**
 * 响应经点点击回调
 * @param routeResultNode   途经点信息
 */
- (void)manager:(MapBizManager *)manager onTapRouteNode:(TMapBaseRouteResultNode *)routeResultNode;
/**
 * 响应途经点点击回调，此回调在 OnTapRouteNode 之前
 * @param index                           途经点索引
 */
- (void)manager:(MapBizManager *)manager onTapViaPointIndex:(int)index;
/**
 * 响应终点点击回调
 * @param destination_node                终点信息
 */
- (void)manager:(MapBizManager *)manager onTapRouteDestinationNode:(TMapBaseDestinationNode *)destinationNode;
/**
 * 响应点击路线解释性Marker信息
 * @param routeExplainMarker                 路线解释性Marker信息 (uid为Marker唯一, name
 * 为资源名称 客户端依赖这个资源名称扎点)
 * @param maxBound                            Marker关联线路的最大外界框
 * @note  maxBound 用户可用于将目标全览,
 * 若marker没有绑定线路，max_bound为无效值(对角坐标相同)，使用方请先判断再使用;
 */
- (void)manager:(MapBizManager *)manager onTapRouteMarker:(TMapBaseRouteResultNode *)routeExplainMarker maxBound:(TMapBaseRect *)maxBound;
/**
 * 操作一个TileLayer后的结果回调
 * @param ret           操作图层状态
 * @param dataSource   所用的数据源唯一标示，用户连续发起创建多个图层时，用于客户端区分返回值
 * @param tileLayer 图层具柄，用户后续可通过该具柄操作该图层，如设置显示/隐藏；(安卓平台不适用)
 * @note 只有ret返回0状态的情况下，tile_layer才是有效的
 */
- (void)manager:(MapBizManager *)manager
    onOperateTileLayerResult:(TMapBaseRetMessage *)ret
                  dataSource:(NSString *)dataSource
                   tileLayer:(id<MapBizMapTileLayer>)tileLayer;
/**
 * 响应用户点击
 * @param ret            详情获取状态(0:请求中,1请求完成)
 * @param markerDetail  具体详情内容
 * @warning 如果用户设置了自定义拉去详情, 则直接返回状态1;
 */
- (void)manager:(MapBizManager *)manager onTapTileLayerMarker:(TMapBaseRetMessage *)ret markerDetail:(MapBizMapTileMarkerDetail *)markerDetail;
/**
 * 路线创建完成回调
 * @param routeOverlayIds 路线overlay id
 */
- (void)manager:(MapBizManager *)manager onRouteLinesCreated:(NSDictionary<NSString *, NSArray<NSNumber *> *> *)routeOverlayIds;
/**
 * 鹰眼图添加完成回调
 * @param msg  结果状态消息(msg.ret_==0:成功,其他失败)
 */
- (void)manager:(MapBizManager *)manager onOverviewMapAttached:(TMapBaseRetMessage *)msg;
/**
 * 鹰眼图移除完成回调
 * @param msg  结果状态消息(msg.ret_==0:成功,其他失败)
 */
- (void)manager:(MapBizManager *)manager onOverviewMapDetached:(TMapBaseRetMessage *)msg;
/**
 * 中心线计算回调
 * @param centerLineData 引导面中心线数据
 */
- (void)manager:(MapBizManager *)manager onGuidanceAreaCenterLine:(MapBizCenterLineData *)centerLineData;
/**
 * 客户端调用 UpdateGuidanceSegmentInfo 出现异常回调
 * @param msg 错误码定义参见：
 * mapbiz::error_code::MainRouteNoLocation，msg.msg_打印主路线id，
 * mapbiz::error_code::CompanionRouteNoLocation，msg.msg_打印没有匹配的伴随路线id，
 * mapbiz::error_code::GuidanceRouteNotFound，msg.msg_打印没有找到的诱导路线id
 */
- (void)manager:(MapBizManager *)manager onSegmentUpdateError:(TMapBaseRetMessage *)msg;
@end

/**
 * 气泡资源回调
 */
@protocol MapBizResourceDelegate <NSObject>
@optional
/**
 * 气泡资源回调加载
 * @param bubbleDrawDescriptor
 * @return MapBizMapResourceContentDescriptor
 */
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager onLoadBubbleResource:(MapBizBubbleDrawDescriptor *)bubbleDrawDescriptor;

/// MapBiz 加载路名资源回调
/// @param routeNameDrawDescriptor 路名资源描述
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
        onLoadRouteNameResourceWithResourceDesc:(MapBizBubbleDrawDescriptor *)routeNameDrawDescriptor;

/// MapBiz 加载拥堵气泡资源回调
/// @param trafficDrawDescriptor 拥堵气泡描述
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
        onLoadJamBubbleResourceWithResourceDesc:(MapBizTrafficDrawDescriptor *)trafficDrawDescriptor;

/// MapBiz 加载伴随气泡资源回调
/// @param companionBubbleDrawDescriptor 伴随气泡描述
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
    onLoadCompanionBubbleResourceWithResourceDesc:(MapBizCompanionBubbleDrawDescriptor *)companionBubbleDrawDescriptor;

/// MapBiz 加载电子眼气泡资源描述
/// @param cameraBubbleDrawDescriptor 电子眼气泡描述
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
     onLoadCameraBubbleResourceWithResourceDesc:(MapBizCameraDrawDescriptor *)cameraBubbleDrawDescriptor;

/// MapBiz 加载动态资源回调
/// @param dynamicIconbubbleDrawDescriptor 动态资源描述，一般在 dynamicIconbubbleDrawDescriptor.textContent 中
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
      onLoadDynamicIconResourceWithResourceDesc:(MapBizBubbleDrawDescriptor *)dynamicIconbubbleDrawDescriptor;

- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
      onLoadDefaultIconResourceWithResourceDesc:(MapBizBubbleDrawDescriptor *)defaultIconbubbleDrawDescriptor;

/// MapBiz 加载 ETA，多方案页终点到达时间
/// @param etaDrawDescriptor ETA 信息，需要把文字绘制成图片，文字在 arriveTime 中
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
              onLoadETAResourceWithResourceDesc:(MapBizDestNameEtaDrawDescriptor *)etaDrawDescriptor;

/// MapBiz 路线标签资源，换起叮当算路，多方案页要展示标签
/// @param routeLabelDrawDescriptor 标签信息，通常情况下，只有 textContent 有内容，根据 textContent 绘制即可
- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
       onLoadRouteLabelResourceWithResourceDesc:(MapBizBubbleDrawDescriptor *)routeLabelDrawDescriptor;

- (MapBizMapResourceContentDescriptor *)manager:(MapBizManager *)manager
    onLoadExplainBubbleResourceWithResourceDesc:(MapBizExplainBubbleDrawDescriptor *)explainBubbleDrawDescriptor;
@end

@interface MapBizManager : NSObject
@property (nonatomic, strong) NSHashTable<id<MapBizEventDelegate>> *eventDelegates;
@property (nonatomic, weak, nullable) id<MapBizResourceDelegate> resourceDelegate;  // 气泡绘制和其他资源加载回调函数
//@property (nonatomic, class, strong, readonly) TMapBaseLoggerConfig *loggerConfig;   // 日志打印配置信息
@property (nonatomic, strong) MapBizLocatorLayerConfig *locatorLayerConfig;                  // 定位车标图层配置
@property (nonatomic, strong) MapBizRouteLayerConfig *routeLayerConfig;                      // 蚯蚓线图层配置
@property (nonatomic, strong) MapBizCameraLayerConfig *cameraLayerConfig;                    // 电子眼图层配置
@property (nonatomic, strong) MapBizRouteNameLayerConfig *routeNameLayerConfig;              // 转向路名气泡图层配置
@property (nonatomic, strong) MapBizRouteTrafficLayerConfig *routeTrafficLayerConfig;        // 拥堵气泡图层配置
@property (nonatomic, strong) MapBizRouteCompanionLayerConfig *routeCompanionLayerConfig;    // 伴随气泡图层配置
@property (nonatomic, strong) MapBizCruiseLayerConfig *cruiseLayerConfig;                    // 巡航图层配置
@property (nonatomic, strong) MapBizColorRouteStyleOption *colorRouteStyleOption;            // 用户自定义蚯蚓线样式
@property (nonatomic, strong) MapBizColorRouteStyleOptionEx *colorRouteStyleOptionEx;        // 用户自定义高级蚯蚓线样式
@property (nonatomic, strong) MapBizTurnArrowStyleOption *turnArrowStyleOption;              // 用户自定义箭头样式
@property (nonatomic, strong) MapBizDestNameEtaLayerConfig *destNameEtaLayerConfig;          // 终点名称ETA图层配置
@property (nonatomic, strong) MapBizRouteLabelLayerConfig *routeLabelLayerConfig;            // 路线标签图层配置
@property (nonatomic, strong) MapBizMapScaleConfigContainer *mapScaleConfigContainer;        // 自动比例尺策略参数
@property (nonatomic, strong) MapBizBubbleLayerConfigContainer *bubbleLayerConfigContainer;  // 导航场景下所有气泡图层的扎点和显示策略
/**
 * 底图的昼夜模式
 * @param mapMode    MapBizMapModeDay: 白天; MapBizMapModeNight: 夜间;
 * @note 内部默认:MapBizMapModeDay白天
 */
@property (nonatomic, assign) MapBizMapMode mapMode;
/**
 * 底图的导航模式
 * 备注: MapBiz内部会当前模式适当对上一个状态的一些脏数据进行清理
 *      同时确认一些策略仅仅在正确的模式下生效;
 * @param nav_mode   MapBizNavModeNavigation: 导航模式; MapBizNavModeCruise: 巡航模式; MapBizNavModeFree: 自由模式
 * @note 内部默认值: MapBizNavModeFree: 自由模式
 */
@property (nonatomic, assign) MapBizNavMode navMode;
/**
 * 设置语言类型，SDK内部默认简体中文
 * @param 语言, 中文简体: zh-cn; 中文繁体: zh-tw; 英文: en;
 * 备注: 非法语言 按照默认语言中文处理
 */
@property (nonatomic, copy, nullable) NSString *language;
/**
 * 设置定位资源切换 是否由用户完全控制
 * 当设置为true时，表示由用户控制定位表资源；
 * 当设置为false时，表示由SDK内部控制，SDK需要根据GPS状态切换定位表资源
 */
@property (nonatomic, assign) BOOL locatorResCtrlMode;
@property (nonatomic, assign, readonly) BOOL isLocationFollowed;  // 获取底图跟随模式状态 true:跟随模式; false:非跟随
@property (nonatomic, assign, readonly) BOOL isHeadingUp;         // 获取底图是否车头朝上/朝北(屏幕顶部) true:车头朝上; false:非车头朝上
@property (nonatomic, assign, readonly) BOOL is2DMap;             // 获取底图是否为2D模式状态 true:2D状态;false:3D状态
/**
 * 是否开启自动俯仰角策略
 * 默认值:关闭
 * 备注: 开启自动俯仰角策略，并不一定执行； 只有非导航状态下设置生效
 * @param enabled true:开启自动俯仰角策略; false: 关闭自动俯仰角策略
 */
@property (nonatomic, assign) BOOL autoSkewEnabled;
/**
 * 是否开启自动比例尺策略
 * 默认值:关闭
 * @param enabled  true:开启自动比例尺; false: 关闭自动比例尺
 * @warning 非导航模式下不应该开启自动比例尺策略
 */
@property (nonatomic, assign) BOOL autoScaleEnabled;
@property (nonatomic, copy, nullable, readonly) NSDictionary<NSString *, NSArray<NSNumber *> *> *visibleRouteOverlayMap;

+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;
/**
 * 初始化base库线程池，map-biz独立发布时需调用，必须在创建实例前调用。
 * @param name 线程池名称
 */
+ (void)initBaseThreadPoolWithName:(NSString *)name;

/**
 * 获取MapBizManager实例对象
 * @param option    启动参数
 * @return MapBizManager 指针
 */
- (instancetype)initWithOption:(MapBizOption *)option;

/**
 * 支持按照分类打开调试开关
 * @param contents    调试内容的组合值，参考DebugContentCatalog
 * @param enabled    开关状态,true打开，false关闭
 * @note  仅仅在Debug版本下该接口才会生效; Release模式下不生效
 */
- (void)debugContents:(MapBizDebugContentCatalog)contents enabled:(BOOL)enabled;
/**
 * 设置动态帧率开启状态和配置
 * 备注: 若关闭动态帧率，则调整为配置中的默认帧率值
 * @param frameRateConfig  动态帧率配置参数
 * @param enable             是否开启动态帧率
 */
- (void)setDynamicFrameRateConfig:(MapBizDynamicFrameRateConfig *)frameRateConfig enabled:(BOOL)enabled;
/**
 * 添加MapBiz事件监听器
 * @param delegate
 */
- (void)addMapBizEvent:(id<MapBizEventDelegate>)delegate;
/**
 * 移除MapBiz事件监听器
 * @param delegate
 */
- (void)removeMapBizEvent:(id<MapBizEventDelegate>)delegate;
/**
 * 获取Marker图层配置
 * @param markerType Marker图层类型
 * @return Marker图层配置
 */
- (MapBizMarkerLayerConfig *)markerLayerConfigWithMarkerType:(MapBizMarkerType)markerType;
/**
 * 更新Marker图层配置
 * @param layerConfig Marker图层配置
 */
- (void)updateMarkerLayerConfig:(MapBizMarkerLayerConfig *)layerConfig;
/**
 * 获取Marker图层名称样式
 * @param markerType Marker图层类型
 * @return Marker图层名称样式
 */
- (MapBizMarkerNameStyleOption *)markerNameStyleOptionWithMarkerType:(MapBizMarkerType)markerType;
/**
 * 更新Marker图层名称样式
 * @param markerType Marker图层类型
 * @param styleOption Marker图层名称样式
 */
- (void)updateMarkerNameStyleOptionWithMarkerType:(MapBizMarkerType)markerType styleOption:(MapBizMarkerNameStyleOption *)styleOption;
/**
 * 更新鹰眼图相关配置
 * @param overviewMapConfig 鹰眼图配置
 */
- (void)updateOverviewMapConfig:(MapBizOverviewMapConfig *)overviewMapConfig;
/**
 * 更新客户自定义导航资源(PNG整个图片流)
 * @param customMapResources      自定义资源内容列表
 * @note  1. 列表为空时，代表移除所有的客户自定义资源
 *        2. 列表不为空时，仅仅更新对应ID的资源
 */
- (void)updateCustomMapResourcesWithFileBuffer:(NSDictionary<NSNumber *, MapBizCustomResourceContentDescriptor *> *)customMapResources;
/**
 * 更新客户自定义导航资源(PNG纯图像像素部分)
 * @param customMapResources      自定义资源内容列表
 * @note  1. 列表为空时，代表移除所有的客户自定义资源
 *        2. 列表不为空时，仅仅更新对应ID的资源
 */
- (void)updateCustomMapResourcesWithPixelBuffer:(NSDictionary<NSNumber *, MapBizMapResourceContentDescriptor *> *)customMapResources;

- (void)setMapPause;   // 导航退后台(内部会根据状态停止一些不必要的计算，降低性能消耗)
- (void)setMapResume;  // 导航回前台(内部会恢复导航动作)
/**
 * 设置底图跟随模式
 * 说明：与操作态进行区分，跟随模式下，整个底图始终以自车为底图中心，车标更新 带动底图中心更新
 * @param follow          是否为跟随模式; {true: 设置为跟随模式, false: 取消跟随模式}
 * @param headingUp      是否车头朝北; {true: 车头朝上, false: 车头不一定朝上}
 * @param animated        是否采用动画形式生效
 * @note  1> 强调该接口仅仅控制跟随和车头朝北，关于底图朝北该接口不负责；
 *        2> 主底图和鹰眼图在跟随模式上互斥状态，只提供主底图接口，鹰眼图内部控制
 */
- (void)setLocationFollowed:(BOOL)follow headingUp:(BOOL)headingUp animated:(BOOL)animated;
/**
 * 设置底图为2D模式还是3D模式
 * 备注：该状态仅仅影响自动比例尺策略和自动俯仰角策略
 *      2D模式, 导航/巡航模式下 自动比例尺(若开启)仅仅缩放，不改变俯仰角
 *      2D模式, 自由模式下  俯仰角设置为0；
 *      3D模式, 导航模式下  自动比例尺(若开启)仅仅缩放，同时调整俯仰角
 *      3D模式，自由模式下  自动俯仰角(若开启) 根据当前比例尺，自动调整俯仰角
 * @param b2d          是否2D模式；{true:2D, false:3D}
 * @param animated     是否采用动画形式生效
 */
- (void)setMapTo2D:(BOOL)b2d animated:(BOOL)animated;
/**
 * 增加蚯蚓线数据
 * 备注: 首次计算路线或路线完全重算情况下调用；
 *      路况刷新调用 UpdateRouteData, 伴随路线更新调用 AddCompanionRouteData;
 * @param routePlanVisitor          蚯蚓线数据访问器
 * @warning 该接口不要连续调用(内部有路线，再次调用该接口, Debug模式下会直接崩溃)
 */
- (void)addRouteData:(TMapBaseRoutePlanVisitor *)routePlanVisitor;
/**
 * 增加蚯蚓线数据
 * 备注: 首次计算路线或路线完全重算情况下调用；
 *      路况刷新调用 UpdateRouteData, 伴随路线更新调用 AddCompanionRouteData;
 * @param routePlanVisitor          蚯蚓线数据访问器
 * @param destinationName            终点名称
 * @warning 该接口不要连续调用(内部有路线，再次调用该接口, Debug模式下会直接崩溃)
 */
- (void)addRouteData:(TMapBaseRoutePlanVisitor *)routePlanVisitor destinationName:(NSString *)destinationName;
/**
 * 更新蚯蚓线数据(伴随路线更新和路况刷新）
 * @param routeUpdateVisitor 路线数据访问器
 */
- (void)updateRouteData:(TMapBaseRouteUpdateVisitor *)routeUpdateVisitor;
/**
 * 添加伴随路线集合
 * 备注: 伴随路线变化或路况数据变化后 调用刷新
 * @param routePlanVisitor  路线数据访问器
 * @param explainInfo          伴随路线解释性信息
 */
- (void)addCompanionRouteData:(TMapBaseRoutePlanVisitor *)routePlanVisitor
                  explainInfo:(NSDictionary<NSString *, NSArray<TMapBaseCompanionExplainInfo *> *> *)explainInfo;
/**
 * 更新蚯蚓线数据
 * 备注: 目前仅仅用于更新路况
 * @param routeUpdateVisitor         蚯蚓线数据访问器
 */
- (void)updateRouteTrafficData:(TMapBaseRouteUpdateVisitor *)routeUpdateVisitor;
/**
 * 更新路线解释性信息
 * 备注: 1> 在路线偏航重算情况下 用户调用AddRouteData时需要清除之前所有路线的解释性元素;
 *      2> 在用户调用ClearRouteData时需要清除之前对应路线的解释性元素
 *      3> 其余情况，用户调用更新解析性信息，为对应路线刷新解析元素
 * @param routeExplainInfo          路线解释性信息
 */
- (void)updateRouteExplainData:(TMapBaseRouteExplainInfo *)routeExplainInfo;
/**
 * 设置Marker关联的Line是否显示
 * @param markerId        MarkerID
 * @param visible          是否可见:{true:可见,false:不可见}
 * @note  点击Marker时，MapBiz会通过事件回调接口，返回Marker信息
 *        如果需要显示线，需要外部进一步调用； 不需要显示的时候，需要外部设置隐藏掉；
 *        备注:若Marker没有关联线路，设置也不会显示
 */
- (void)setRouteExplainMarkerLine:(NSString *)markerId visible:(BOOL)visible;
/**
 * 设置路线解释性配置
 * @param routeExplainConfig 配置
 */
- (void)setRouteExplainConfig:(MapBizRouteExplainConfig *)routeExplainConfig;
/**
 * 设置Marker关联的Icon是否显示
 * @param markerId    MarkerID
 * @param visible      是否可见:{true:可见,false:不可见}
 * @note  点击Marker时，若期望底图上的Marker隐藏，一种方式创建一个高优先级Marker，另外通过该接口设置隐藏
 */
- (void)setRouteExplainMarkerIcon:(NSString *)markerId visible:(BOOL)visible;
/**
 * 设置路线解释性单独线和区域面是否显示
 * @param lineVisible        单独线是否可见:{true:可见,false:不可见}
 * @param areaVisible        区域面是否可见:{true:可见,false:不可见}
 */
- (void)setRouteExplainLineAndAreaVisible:(BOOL)lineVisible areaVisible:(BOOL)areaVisible;
/**
 * 清除路线解释性单独线和区域面
 */
- (void)clearRouteExplainLineAndArea;
/**
 * 清除指定蚯蚓线数据
 * @param clearAll              是否擦出所有路线, 若为false 则需要填充路线ID列表;
 * @param routeIdArray      要清除的路线ID列表
 */
- (void)clearRouteData:(BOOL)clearAll routeIdArray:(NSArray<NSString *> *)routeIdArray;
/**
 * 设置当前主路线ID
 * @param selectedRouteId 当前主路线ID
 */
- (void)setSelectedRouteId:(NSString *)selectedRouteId;
/**
 * 动态设置非选中路线是否隐藏的能力
 * 备注:内部默认值为false, 会展示；
 * @param hidden 是否隐藏，true: 隐藏非选中路线; false: 不隐藏非选中路线
 * @param callback 非选中路线隐藏状态变更后的回调
 * @apiNote 用户调用该接口后，应该监听回调，在收到状态变更完成前，不应该再次调用或切换页面；
 *                       不设置回调或不监听回调，可能出现UI 与 图面显示状态不完全一致的问题;
 */
- (void)setUnSelectedRouteHidden:(BOOL)hidden completion:(void (^)(TMapBaseRetMessage *ret))completion;
/**
 * 获取剩余路线的最大外界框
 * @param routeIds   指定路线ID，若列表为空，返回内部所有路线构成的最大外界框，注意路线边界框计算不包含已隐藏路线
 * 若请求时传入的路线ID 为空，则会返回所有路线的最大外界框，路线ID为所有路线ID的拼接，拼接字符逗号
 * @param callback    结果回调
 */
- (void)getRemainRouteMaxBound:(NSArray<NSString *> *)routeIds completion:(void (^)(NSDictionary<NSString *, TMapBaseRect *> *))completion;
/**
 * 仅用于导航态（NavMode::Navigation）更新诱导段信息
 * 备注: 包含所有路线(含伴随路线)信息
 * @param guidanceSegmentInfo   诱导信息集合
 */
- (void)updateGuidanceSegmentInfo:(TMapBaseUpdateInfo *)guidanceSegmentInfo;
/**
 * 进入转向路口通知(MapBiz用于路口增强自动比例尺策略)
 * @param intersection  路口信息
 * @note 不是一定会有回调, 只在100-200m范围内触发
 */
- (void)enterTurnIntersection:(TMapBaseApproachingTurnInfo *)intersection;
/**
 * 离开转向路口通知(MapBiz用于路口增强自动比例尺策略)
 * @param lastSegmentIndex 上一个转向位置
 */
- (void)leaveTurnIntersection:(int)lastSegmentIndex;
/**
 * 更新拥堵气泡信息
 * @param trafficBubble          拥堵气泡信息
 * @note  1.用于导航场景或巡航场景 2. 最新数据会覆盖上一次数据
 */
- (void)updateTrafficBubble:(TMapBaseTrafficJamInfo *)trafficBubble;
/**
 * 清除拥堵气泡信息
 * @note 1.用于导航场景或巡航场景
 */
- (void)clearTrafficBubble;
/**
 * 更新警示牌信息
 * @param warningTipBubble      警示牌信息
 * @note  1.用于导航场景或巡航场景 2. 最新数据会覆盖上一次数据
 */
- (void)updateWarningTipBubble:(TMapBaseWarningSignInfo *)warningTipBubble;
/**
 * 清除安全警示牌信息
 * @note 1.用于导航场景或巡航场景
 */
- (void)clearWarningTipBubble;
/**
 * 更新电子眼数据, 路线变化后需要重现添加
 * 清理电子眼，也是这个接口，数据为空即可
 * @param safetyCameraData      电子眼数据
 */
- (void)updateSafetyCameraRawData:(TMapBaseRouteCameraRefreshInfo *)safetyCameraData;
/**
 * 更新巡航主路线上前方电子眼序号距离信息, 目前Biz内部用于放大对应电子眼
 * @param cameras     电子眼序列信息
 */
- (void)updateFrontCamerasOnRouteInCruise:(NSArray<TMapBaseRouteCameraInRange *> *)cameras;
/**
 * 清理已经走的电子眼数据
 * @param clearCameras           需要擦出的电子眼
 */
- (void)clearSafetyCameraRawData:(NSArray<NSNumber *> *)clearCameras;
/**
 * 更新交通事件信息, 路线变化后需要重现添加
 * @param trafficEventItems
 */
- (void)updateTrafficEventItems:(NSArray<TMapBaseTrafficEventInfo *> *)trafficEventItems;
/**
 * 清理已经走过的交通事件信息
 * @param trafficEventItems
 */
- (void)clearTrafficEventItems:(NSArray<TMapBaseTrafficEventInfo *> *)trafficEventItems;
/**
 * 更新鱼骨路线上的诱导信息(电子眼+交通事件+拥堵气泡)
 * @param guidanceItems              诱导信息列表
 * @note  1. 仅仅用于巡航场景
 */
- (void)updateFishBoneGuidanceItems:(NSArray<TMapBaseCruiseFishBoneDisplayInfo *> *)guidanceItems;
/**
 * 清理鱼骨路线上已经走过的诱导信息
 * @param guidanceItems        鱼骨路线索引列表
 * @note  1. 仅仅用于巡航场景
 */
- (void)clearFishBoneGuidanceItems:(NSArray<TMapBaseCruiseFishBoneHideInfo *> *)guidanceItemIndexArray;
/**
 * 查询点击的元素(图层) 类型
 * 说明:该方法在UI主线程中被调用，用于端上判断是否进入操作态处理
 * @param point   屏幕坐标
 * @return    图层类型
 */
- (MapBizLayerType)queryClickLayerTypeWithPoint:(CGPoint)point;
/**
 * 切换地图导航类型(传统导航底图/车道级底图)
 * @param sceneType  地图导航类型
 */
- (void)setMapSceneType:(TMapBaseMapSceneType)sceneType;
/**
 * 更新车道级车标信息
 * @param posPoint 车道级车标信息
 */
- (void)updateHDLocator:(TMapBaseHDPosPoint *)posPoint;
/**
 * 更新变道状态
 * @param isChangingLane true:变道中，false:无变道或变道完成
 */
- (void)updateChangeLaneStatus:(BOOL)isChangingLane;
/**
 * 更新车道级转向墙信息
 * @param turnWallInfo 转向墙信息
 */
- (void)updateTurnWallInfo:(TMapBaseTurnWallInfo *)turnWallInfo;
/**
 * 更新目标车道信息
 * @param targetLaneInfo 目标车道信息
 */
- (void)updateTargetLaneInfo:(TMapBaseTurnLaneInfo *)targetLaneInfo;

/**
 * 动态创建一个TileLayer，创建结果通过回调形式返回 OnOperateTileLayerResult
 * @param mapTileLayerOption  图层选项，其中data_source为一个图层的唯一标示
 * @warning 创建动态图层前，请显示设置网络能力，没有网路能力前，创建会失败(见监听)
 */
- (void)addTileLayer:(MapBizMapTileLayerOption *)mapTileLayerOption;
/**
 * 修改一个已有的图层TileLayer的属性, 操作结果通过回调形式返回 OnOperateTileLayerResult
 * @param mapTileLayerOption 图层选项，其中data_source为一个图层的唯一标示
 */
- (void)modifyTileLayer:(MapBizMapTileLayerOption *)mapTileLayerOption;
/**
 * 删除一个已有的图层TileLayer 操作结果通过回调形式返回 OnOperateTileLayerResult
 * @param dataSource       图层唯一标示
 */
- (void)removeTileLayer:(NSString *)dataSource;
/**
 * 设置指定的途经点选中，-1取消选中
 * @param index 途经点索引
 */
- (void)setViaPointSelected:(int)index;
/**
 * 设置地图全览状态
 * @param screenRect 屏幕区域
 * @param additionalBounds 额外元素的经纬度范围
 * @param routeIds 路线id，若列表为空，返回内部所有路线构成的最大外界框，注意路线边计算界框不包含已隐藏路线
 * @param wholeRoute 使用完整路线还是剩余路线
 */
- (void)setOverViewMode:(CGRect)screenRect
       additionalBounds:(NSArray<TMapBaseRect *> *)additionalBounds
               routeIds:(NSArray<NSString *> *)routeIds
             wholeRoute:(BOOL)wholeRoute;
/**
 * 根据传入参数设置地图预览,一般用于算路后，调整比例尺，功能待完善
 * @param param 参数
 */
- (void)overLook:(MapBizOverLookParam *)param;
/**
 * 设置指定的路线隐藏显示
 * @param routeIds 路线id列表
 * @param hidden 是否隐藏
 */
- (void)setRoute:(NSArray<NSString *> *)routeIds hidden:(BOOL)hidden;
/**
 * 切换楼层。用户切换楼层控件或者用户行驶过程中切换了楼层，需要调用此接口通知mapbiz
 * @param buildingId  建筑物id
 * @param floorName 楼层名字
 */
- (void)setIndoorActiveFloor:(NSString *)buildingId floorName:(NSString *)floorName;
/**
 * 开启/关闭测试环境
 * @param enable 测试环境开关
 * @param url 测网域名url
 */
- (void)setTestEnvironment:(BOOL)enable url:(NSString *)url;
/**
 * 添加鹰眼图
 * @param mapHandle 底图引擎句柄
 */
- (void)attachOverviewMap:(void *)mapHandle;
/**
 * 移除鹰眼图
 */
- (void)detachOverviewMap;
/**
 * 即将进入隧道，目前用于控制车道级导航下的隧道自动比例尺启动时机
 */
- (void)enterTunnel;
/**
 * 已经驶离隧道，目前用于控制车道级导航下的隧道自动比例尺结束时机
 */
- (void)leaveTunnel;
/**
 * 更新HD电子眼图层配置
 * @param layer_config 电子眼图层配置
 */
- (void)updateHDCameraLayerConfig:(MapBizCameraLayerConfig *)layerConfig;

@end
NS_ASSUME_NONNULL_END
