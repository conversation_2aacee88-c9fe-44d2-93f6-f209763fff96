// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/7/15.
//

#pragma once

#include <map>
#include <MapBaseNew/guidance_base_structure.h>

#include "map_biz_common.h"
#include "map_layer_define.h"

__MAPBIZ_NAMESPACE_BEGIN__
/**
 * 定义MapBiz内使用的各类资源ID信息
 * 客户定制资源 仅仅可以定制这里列出的ID信息
 */
namespace R {
/**
 * 主蚯蚓线纹理名称前缀
 */
constexpr char ROUTE_TEXTURE_NAME_PREFIX_FOR_MAIN_ROUTE[] = "route_line_texture_main_";
/**
 * 伴随蚯蚓线纹理名称前缀
 */
constexpr char ROUTE_TEXTURE_NAME_PREFIX_FOR_ASSIST_ROUTE[] = "route_line_texture_assist_";
/**
 * 路线上交通流向箭头纹理名称
 */
constexpr char ROUTE_FLOW_ARROW_TEXTURE_NAME[] = "route_flow_arrow_texture.png";

#define LOCATOR_MASK_DAY 0x00000000    // 0
#define LOCATOR_MASK_NIGHT 0x00000100  // 256

#define GUIDANCE_ROUTE_NODES_MASK_DAY 0x00010000    // 65536
#define GUIDANCE_ROUTE_NODES_MASK_NIGHT 0x00010100  // 65792

#define GUIDANCE_TRAFFIC_EVENT_MASK_DAY 0x00020000    // 131072
#define GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT 0x00020100  // 131328

#define GUIDANCE_WARNING_TIP_MASK_DAY 0x00030000    // 196608
#define GUIDANCE_WARNING_TIP_MASK_NIGHT 0x00030100  // 196864

/**********************************白天资源ID集合********************************************/
// 定位标资源
constexpr unsigned int LOCATOR_COMPASS_OVERVIEW_IMAGE_DAY = LOCATOR_MASK_DAY + 1;    // 鹰眼图罗盘资源
constexpr unsigned int LOCATOR_INDICATOR_OVERVIEW_IMAGE_DAY = LOCATOR_MASK_DAY + 2;  // 鹰眼图罗盘箭头资源
constexpr unsigned int LOCATOR_INDICATOR_DAY = LOCATOR_MASK_DAY + 4;                 // 定位标中心的方向资源
constexpr unsigned int LOCATOR_COMPASS_EAST_DAY = LOCATOR_MASK_DAY + 6;              // 定位标罗盘东字
constexpr unsigned int LOCATOR_COMPASS_SOUTH_DAY = LOCATOR_MASK_DAY + 7;             // 定位标罗盘南字
constexpr unsigned int LOCATOR_COMPASS_WEST_DAY = LOCATOR_MASK_DAY + 8;              // 定位标罗盘西字
constexpr unsigned int LOCATOR_COMPASS_NORTH_DAY = LOCATOR_MASK_DAY + 9;             // 定位标罗盘北字
constexpr unsigned int LOCATOR_COMPASS_RING_DAY = LOCATOR_MASK_DAY + 10;             // 定位标罗盘圆环
constexpr unsigned int LOCATOR_INDICATOR_GPS_DAY = LOCATOR_MASK_DAY + 11;            //
constexpr unsigned int LOCATOR_INDICATOR_BACKGROUND_DAY = LOCATOR_MASK_DAY + 14;     // 定位标背景资源（白天）

// 路线相关资源
constexpr unsigned int OVERVIEW_START_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 1;  // 鹰眼图起点
constexpr unsigned int OVERVIEW_END_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 2;    // 鹰眼图终点
constexpr unsigned int OVERVIEW_VIA_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 3;    // 鹰眼图途径点

constexpr unsigned int ROUTE_START_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 4;                   // 路线起点引导点
constexpr unsigned int ROUTE_END_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 5;                     // 路线终点引导点
constexpr unsigned int ROUTE_START_BUBBLE_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 6;                  // 路线起点气泡
constexpr unsigned int ROUTE_END_BUBBLE_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 7;                    // 路线终点气泡
constexpr unsigned int ROUTE_VIA_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 8;                     // 路线途径点
constexpr unsigned int ROUTE_VIA_POINT_1_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 9;                   // 路线途径点1
constexpr unsigned int ROUTE_VIA_POINT_2_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 10;                  // 路线途径点2
constexpr unsigned int ROUTE_INDOOR_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 45;                 // 室内外切换点
constexpr unsigned int ROUTE_Walk_bad_route_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 46;               // 质量差道路
constexpr unsigned int ROUTE_Walk_ferry_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 47;                   // 轮渡
constexpr unsigned int ROUTE_Walk_footbridge_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 48;              // 天桥
constexpr unsigned int ROUTE_Walk_inner_route_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 49;             // 内部道路
constexpr unsigned int ROUTE_Walk_retrograde_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 50;              // 逆行
constexpr unsigned int ROUTE_Walk_steps_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 51;                   //  台阶
constexpr unsigned int ROUTE_Walk_underground_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 52;             // 地下通道
constexpr unsigned int ROUTE_Walk_cross_walk_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 53;              //  人行横道
constexpr unsigned int ROUTE_Walk_default_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 54;                 // 步行默认资源
constexpr unsigned int ROUTE_INDOOR_SELECT_FLOOR_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 55;    // 室内选中楼层切换点
constexpr unsigned int ROUTE_INDOOR_UNSELECT_FLOOR_POINT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 56;  // 室内非选中楼层切换点

// NOTICE: 64开始为途经点自定义范围，请跳过

// 导航中红绿灯资源
constexpr unsigned int GUIDANCE_TRAFFIC_LIGHT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 11;  // 导航交通灯
// 用户扩展的途经点资源ID(最多支持32个，即+12 到 +44)
constexpr unsigned int ROUTE_VIA_POINT_BASE_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 12;  // 用户扩展途径点资源ID；
// 策略添加的途经点资源ID，预留16个
constexpr unsigned int ROUTE_STRATEGY_VIA_START_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 64;
constexpr unsigned int ROUTE_STRATEGY_VIA_CHARGER_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 64;  // 充电桩
constexpr unsigned int ROUTE_STRATEGY_VIA_END_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 80;
// 可自定义的，策略添加的途经点资源ID，预留16个
constexpr unsigned int ROUTE_STRATEGY_VIA_CUSTOM_START_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 80;
constexpr unsigned int ROUTE_STRATEGY_VIA_CUSTOM_END_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 96;

constexpr unsigned int HD_GUIDANCE_TRAFFIC_LIGHT_DAY = GUIDANCE_ROUTE_NODES_MASK_DAY + 129;  // hd 红绿灯

// 导航中交通事件资源
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_DEFAULT_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 0;        // 默认交通事件资源
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ACCIDENT_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 1;       // 事故
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_BADWEATHER_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 2;     // 恶劣天气
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CHECK_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 3;          // 检查
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CONGESTION_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 4;     // 拥堵
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 5;   // 施工
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CONTROL_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 6;        // 管制
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_DISASTER_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 7;       // 灾害
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 8;    // 障碍
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 9;    // 封路
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 10;    // 山
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 11;  // 窄(小路)
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_POINT_ACTIVITY_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 12;  // 点事件-活动
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_POINT_RAIN_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 13;      // 点事件-积水
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_POINT_BULLETIN_DAY = GUIDANCE_TRAFFIC_EVENT_MASK_DAY + 14;  // 点事件-公告

// 导航中警示牌资源
constexpr unsigned int GUIDANCE_WARNING_DEFAULT_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 0;               // 默认资源
constexpr unsigned int GUIDANCE_WARNING_AlongMountain_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 1;         // 傍山险路 !
constexpr unsigned int GUIDANCE_WARNING_AlongMountainEx_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 2;       // 傍山险路 !
constexpr unsigned int GUIDANCE_WARNING_UpSteepSlope_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 3;          // 上陡坡
constexpr unsigned int GUIDANCE_WARNING_AccidentProneSection_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 4;  // 事故易发路段
constexpr unsigned int GUIDANCE_WARNING_Caution_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 5;               // 注意危险 !
constexpr unsigned int GUIDANCE_WARNING_ContinuousDownSlope_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 6;   // 连续下坡
constexpr unsigned int GUIDANCE_WARNING_Detour_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 7;                // 左右绕行 !
constexpr unsigned int GUIDANCE_WARNING_DetourLeft_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 8;            // 左侧绕行 !
constexpr unsigned int GUIDANCE_WARNING_DetourRight_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 9;           // 右侧绕行 !
constexpr unsigned int GUIDANCE_WARNING_MindCrosswind_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 10;        // 注意横风 !
constexpr unsigned int GUIDANCE_WARNING_MindLeftMerge_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 11;        // 注意左侧合流
constexpr unsigned int GUIDANCE_WARNING_MindRightMerge_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 12;       // 注意右侧合流
constexpr unsigned int GUIDANCE_WARNING_NarrowBridge_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 13;         // 窄桥
constexpr unsigned int GUIDANCE_WARNING_BothNarrow_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 14;           // 两侧变窄
constexpr unsigned int GUIDANCE_WARNING_LeftNarrow_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 15;           // 左侧变窄
constexpr unsigned int GUIDANCE_WARNING_RightNarrow_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 16;          // 右侧变窄
constexpr unsigned int GUIDANCE_WARNING_NoOvertake_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 17;           // 禁止超车
constexpr unsigned int GUIDANCE_WARNING_RailwayCrossingManaged_DAY =
    GUIDANCE_WARNING_TIP_MASK_DAY + 18;                                                          // 有人看守铁路道口
constexpr unsigned int GUIDANCE_WARNING_ReverseRoadLeft_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 19;   // 反向弯路（左）!
constexpr unsigned int GUIDANCE_WARNING_ReverseRoadRight_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 20;  // 反向弯路（右）!
constexpr unsigned int GUIDANCE_WARNING_MindRock_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 21;          // 注意落石
constexpr unsigned int GUIDANCE_WARNING_MindRockEx_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 22;        // 注意落石
constexpr unsigned int GUIDANCE_WARNING_UnevenRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 23;        // 路面不平
constexpr unsigned int GUIDANCE_WARNING_MindChildren_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 24;      // 注意儿童
constexpr unsigned int GUIDANCE_WARNING_SharpLeftRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 25;     // 向左急弯路
constexpr unsigned int GUIDANCE_WARNING_SharpRightRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 26;    // 向右急弯路
constexpr unsigned int GUIDANCE_WARNING_SlipperyRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 27;      // 易滑
constexpr unsigned int GUIDANCE_WARNING_DownSteepSlope_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 28;    // 下陡坡
constexpr unsigned int GUIDANCE_WARNING_TextWarning_DAY =
    GUIDANCE_WARNING_TIP_MASK_DAY + 29;  // 文字性警示标牌，现场为文字提示，且无法归类到国标危险信息标牌中 !
constexpr unsigned int GUIDANCE_WARNING_RailwayCrossingWild_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 30;  // 无人看守铁路道口
constexpr unsigned int GUIDANCE_WARNING_TurnOnLightInTunnel_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 31;  // 隧道开灯
constexpr unsigned int GUIDANCE_WARNING_Village_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 32;              // 村庄
constexpr unsigned int GUIDANCE_WARNING_WaterRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 33;            // 过水路面  !
constexpr unsigned int GUIDANCE_WARNING_ContinuousDetour_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 34;     // 连续弯路
constexpr unsigned int GUIDANCE_WARNING_LaneSlideSlop_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 35;        // 滑坡
constexpr unsigned int GUIDANCE_WARNING_DirtRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 36;             // 泥石路
constexpr unsigned int GUIDANCE_WARNING_GroundSink_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 37;           // 地面塌陷
constexpr unsigned int GUIDANCE_WARNING_Collapse_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 38;             // 塌方
constexpr unsigned int GUIDANCE_WARNING_OppositeRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 39;         // 双向交通 !
constexpr unsigned int GUIDANCE_WARNING_MindLivestock_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 40;        // 注意牲畜 !
constexpr unsigned int GUIDANCE_WARNING_DamRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 41;              // 堤坝路   !
constexpr unsigned int GUIDANCE_WARNING_DamRoadEx_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 42;            // 堤坝路   !
constexpr unsigned int GUIDANCE_WARNING_CamelBackBridge_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 43;      // 驼峰桥
constexpr unsigned int GUIDANCE_WARNING_AllowOvertake_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 44;        // 解除禁止超车
constexpr unsigned int GUIDANCE_WARNING_Honking_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 45;              // 鸣喇叭
constexpr unsigned int GUIDANCE_WARNING_StopGiveway_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 46;          // 停车让行 !
constexpr unsigned int GUIDANCE_WARNING_EncounterGiveway_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 47;     // 会车让行 !
constexpr unsigned int GUIDANCE_WARNING_SlowdownGiveway_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 48;      // 减速让行 !
constexpr unsigned int GUIDANCE_WARNING_TidalLane_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 49;            // 潮汐车道 !
constexpr unsigned int GUIDANCE_WARNING_ProtrudingRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 50;       // 路面高凸 !
constexpr unsigned int GUIDANCE_WARNING_SinkingRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 51;          // 路面低洼 !
constexpr unsigned int GUIDANCE_WARNING_TrafficAccidentBlackSpots_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 52;  // 事故多发
constexpr unsigned int GUIDANCE_WARNING_QuarantineStation_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 53;          // 检疫站
constexpr unsigned int GUIDANCE_WARNING_Zebra_DAY = GUIDANCE_WARNING_TIP_MASK_DAY + 54;                      // 斑马线

/*********************************夜间资源ID集合********************************************/
// 定位标资源
constexpr unsigned int LOCATOR_COMPASS_OVERVIEW_IMAGE_NIGHT = LOCATOR_MASK_NIGHT + 1;    // 鹰眼图罗盘资源
constexpr unsigned int LOCATOR_INDICATOR_OVERVIEW_IMAGE_NIGHT = LOCATOR_MASK_NIGHT + 2;  // 鹰眼图罗盘箭头资源
constexpr unsigned int LOCATOR_INDICATOR_NIGHT = LOCATOR_MASK_NIGHT + 4;                 // 定位标中心的方向资源
constexpr unsigned int LOCATOR_COMPASS_EAST_NIGHT = LOCATOR_MASK_NIGHT + 6;              // 定位标罗盘东字
constexpr unsigned int LOCATOR_COMPASS_SOUTH_NIGHT = LOCATOR_MASK_NIGHT + 7;             // 定位标罗盘南字
constexpr unsigned int LOCATOR_COMPASS_WEST_NIGHT = LOCATOR_MASK_NIGHT + 8;              // 定位标罗盘西字
constexpr unsigned int LOCATOR_COMPASS_NORTH_NIGHT = LOCATOR_MASK_NIGHT + 9;             // 定位标罗盘北字
constexpr unsigned int LOCATOR_COMPASS_RING_NIGHT = LOCATOR_MASK_NIGHT + 10;             // 定位标罗盘圆环
constexpr unsigned int LOCATOR_INDICATOR_GPS_NIGHT = LOCATOR_MASK_NIGHT + 11;            //
constexpr unsigned int LOCATOR_INDICATOR_BACKGROUND_NIGHT = LOCATOR_MASK_NIGHT + 14;     // 定位标背景资源（夜间）

// 路线相关资源
constexpr unsigned int OVERVIEW_START_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 1;              // 鹰眼图起点
constexpr unsigned int OVERVIEW_END_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 2;                // 鹰眼图终点
constexpr unsigned int OVERVIEW_VIA_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 3;                // 鹰眼图途径点
constexpr unsigned int ROUTE_START_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 4;                 // 路线起点引导点
constexpr unsigned int ROUTE_VIA_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 8;                   // 路线途径点
constexpr unsigned int ROUTE_VIA_POINT_1_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 9;                 // 路线途径点1
constexpr unsigned int ROUTE_VIA_POINT_2_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 10;                // 路线途径点2
constexpr unsigned int ROUTE_END_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 5;                   // 路线终点引导点
constexpr unsigned int ROUTE_START_BUBBLE_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 6;                // 路线终点气泡
constexpr unsigned int ROUTE_END_BUBBLE_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 7;                  // 路线终点气泡
constexpr unsigned int ROUTE_INDOOR_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 45;               // 室内外切换点
constexpr unsigned int ROUTE_Walk_bad_route_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 46;             // 质量差道路
constexpr unsigned int ROUTE_Walk_ferry_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 47;                 // 轮渡
constexpr unsigned int ROUTE_Walk_footbridge_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 48;            // 天桥
constexpr unsigned int ROUTE_Walk_inner_route_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 49;           // 内部道路
constexpr unsigned int ROUTE_Walk_retrograde_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 50;            // 逆行
constexpr unsigned int ROUTE_Walk_steps_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 51;                 //  台阶
constexpr unsigned int ROUTE_Walk_underground_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 52;           // 地下通道
constexpr unsigned int ROUTE_Walk_cross_walk_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 53;            //  人行横道
constexpr unsigned int ROUTE_Walk_default_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 54;               // 步行默认资源
constexpr unsigned int ROUTE_INDOOR_SELECT_FLOOR_POINT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 55;  // 室内选中楼层切换点
constexpr unsigned int ROUTE_INDOOR_UNSELECT_FLOOR_POINT_NIGHT =
    GUIDANCE_ROUTE_NODES_MASK_NIGHT + 56;  // 室内非选中楼层切换点

// 导航中红绿灯资源
constexpr unsigned int GUIDANCE_TRAFFIC_LIGHT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 11;  // 导航交通灯
// 用户扩展的途径点资源ID(最多支持32个，即+12 到 +44)
constexpr unsigned int ROUTE_VIA_POINT_BASE_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 12;  // 用户扩展途径点资源ID；
// 策略添加的途经点资源ID，预留16个
constexpr unsigned int ROUTE_STRATEGY_VIA_START_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 64;
constexpr unsigned int ROUTE_STRATEGY_VIA_CHARGER_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 64;  // 充电桩
constexpr unsigned int ROUTE_STRATEGY_VIA_END_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 80;
// 可自定义的，策略添加的途经点资源ID，预留16个
constexpr unsigned int ROUTE_STRATEGY_VIA_CUSTOM_START_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 80;
constexpr unsigned int ROUTE_STRATEGY_VIA_CUSTOM_END_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 96;

constexpr unsigned int HD_GUIDANCE_TRAFFIC_LIGHT_NIGHT = GUIDANCE_ROUTE_NODES_MASK_NIGHT + 129;  // hd 红绿灯

// 导航中交通事件资源
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_DEFAULT_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 0;   // 默认交通事件资源
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ACCIDENT_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 1;  // 事故
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_BADWEATHER_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 2;       // 恶劣天气
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CHECK_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 3;            // 检查
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CONGESTION_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 4;       // 拥堵
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 5;     // 施工
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_CONTROL_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 6;          // 管制
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_DISASTER_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 7;         // 灾害
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 8;      // 障碍
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 9;      // 封路
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 10;      // 山
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 11;    // 窄(小路)
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_POINT_ACTIVITY_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 12;  // 活动
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_POINT_RAIN_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 13;      // 积水
constexpr unsigned int GUIDANCE_TRAFFIC_EVENT_POINT_BULLETIN_NIGHT = GUIDANCE_TRAFFIC_EVENT_MASK_NIGHT + 14;  // 公告

// 导航中警示牌资源
constexpr unsigned int GUIDANCE_WARNING_DEFAULT_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 0;               // 默认资源
constexpr unsigned int GUIDANCE_WARNING_AlongMountain_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 1;         // 傍山险路 !
constexpr unsigned int GUIDANCE_WARNING_AlongMountainEx_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 2;       // 傍山险路 !
constexpr unsigned int GUIDANCE_WARNING_UpSteepSlope_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 3;          // 上陡坡
constexpr unsigned int GUIDANCE_WARNING_AccidentProneSection_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 4;  // 事故易发路段
constexpr unsigned int GUIDANCE_WARNING_Caution_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 5;               // 注意危险 !
constexpr unsigned int GUIDANCE_WARNING_ContinuousDownSlope_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 6;   // 连续下坡
constexpr unsigned int GUIDANCE_WARNING_Detour_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 7;                // 左右绕行 !
constexpr unsigned int GUIDANCE_WARNING_DetourLeft_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 8;            // 左侧绕行 !
constexpr unsigned int GUIDANCE_WARNING_DetourRight_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 9;           // 右侧绕行 !
constexpr unsigned int GUIDANCE_WARNING_MindCrosswind_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 10;        // 注意横风 !
constexpr unsigned int GUIDANCE_WARNING_MindLeftMerge_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 11;        // 注意左侧合流
constexpr unsigned int GUIDANCE_WARNING_MindRightMerge_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 12;       // 注意右侧合流
constexpr unsigned int GUIDANCE_WARNING_NarrowBridge_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 13;         // 窄桥
constexpr unsigned int GUIDANCE_WARNING_BothNarrow_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 14;           // 两侧变窄
constexpr unsigned int GUIDANCE_WARNING_LeftNarrow_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 15;           // 左侧变窄
constexpr unsigned int GUIDANCE_WARNING_RightNarrow_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 16;          // 右侧变窄
constexpr unsigned int GUIDANCE_WARNING_NoOvertake_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 17;           // 禁止超车
constexpr unsigned int GUIDANCE_WARNING_RailwayCrossingManaged_NIGHT =
    GUIDANCE_WARNING_TIP_MASK_NIGHT + 18;                                                            // 有人看守铁路道口
constexpr unsigned int GUIDANCE_WARNING_ReverseRoadLeft_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 19;   // 反向弯路（左）!
constexpr unsigned int GUIDANCE_WARNING_ReverseRoadRight_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 20;  // 反向弯路（右）!
constexpr unsigned int GUIDANCE_WARNING_MindRock_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 21;          // 注意落石
constexpr unsigned int GUIDANCE_WARNING_MindRockEx_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 22;        // 注意落石
constexpr unsigned int GUIDANCE_WARNING_UnevenRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 23;        // 路面不平
constexpr unsigned int GUIDANCE_WARNING_MindChildren_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 24;      // 注意儿童
constexpr unsigned int GUIDANCE_WARNING_SharpLeftRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 25;     // 向左急弯路
constexpr unsigned int GUIDANCE_WARNING_SharpRightRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 26;    // 向右急弯路
constexpr unsigned int GUIDANCE_WARNING_SlipperyRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 27;      // 易滑
constexpr unsigned int GUIDANCE_WARNING_DownSteepSlope_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 28;    // 下陡坡
constexpr unsigned int GUIDANCE_WARNING_TextWarning_NIGHT =
    GUIDANCE_WARNING_TIP_MASK_NIGHT + 29;  // 文字性警示标牌，现场为文字提示，且无法归类到国标危险信息标牌中 !
constexpr unsigned int GUIDANCE_WARNING_RailwayCrossingWild_NIGHT =
    GUIDANCE_WARNING_TIP_MASK_NIGHT + 30;  // 无人看守铁路道口
constexpr unsigned int GUIDANCE_WARNING_TurnOnLightInTunnel_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 31;  // 隧道开灯
constexpr unsigned int GUIDANCE_WARNING_Village_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 32;              // 村庄
constexpr unsigned int GUIDANCE_WARNING_WaterRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 33;            // 过水路面  !
constexpr unsigned int GUIDANCE_WARNING_ContinuousDetour_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 34;     // 连续弯路
constexpr unsigned int GUIDANCE_WARNING_LaneSlideSlop_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 35;        // 滑坡
constexpr unsigned int GUIDANCE_WARNING_DirtRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 36;             // 泥石路
constexpr unsigned int GUIDANCE_WARNING_GroundSink_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 37;           // 地面塌陷
constexpr unsigned int GUIDANCE_WARNING_Collapse_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 38;             // 塌方
constexpr unsigned int GUIDANCE_WARNING_OppositeRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 39;         // 双向交通 !
constexpr unsigned int GUIDANCE_WARNING_MindLivestock_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 40;        // 注意牲畜 !
constexpr unsigned int GUIDANCE_WARNING_DamRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 41;              // 堤坝路   !
constexpr unsigned int GUIDANCE_WARNING_DamRoadEx_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 42;            // 堤坝路   !
constexpr unsigned int GUIDANCE_WARNING_CamelBackBridge_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 43;      // 驼峰桥
constexpr unsigned int GUIDANCE_WARNING_AllowOvertake_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 44;        // 解除禁止超车
constexpr unsigned int GUIDANCE_WARNING_Honking_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 45;              // 鸣喇叭
constexpr unsigned int GUIDANCE_WARNING_StopGiveway_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 46;          // 停车让行 !
constexpr unsigned int GUIDANCE_WARNING_EncounterGiveway_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 47;     // 会车让行 !
constexpr unsigned int GUIDANCE_WARNING_SlowdownGiveway_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 48;      // 减速让行 !
constexpr unsigned int GUIDANCE_WARNING_TidalLane_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 49;            // 潮汐车道 !
constexpr unsigned int GUIDANCE_WARNING_ProtrudingRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 50;       // 路面高凸 !
constexpr unsigned int GUIDANCE_WARNING_SinkingRoad_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 51;          // 路面低洼 !
constexpr unsigned int GUIDANCE_WARNING_TrafficAccidentBlackSpots_NIGHT =
    GUIDANCE_WARNING_TIP_MASK_NIGHT + 52;                                                             // 事故多发
constexpr unsigned int GUIDANCE_WARNING_QuarantineStation_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 53;  // 检疫站
constexpr unsigned int GUIDANCE_WARNING_Zebra_NIGHT = GUIDANCE_WARNING_TIP_MASK_NIGHT + 54;              // 斑马线

/*****************************************************************************************/
/**
 * 每个资源ID，具有昼夜模式映射
 * 只有白天ID的，夜间ID填充白天ID
 */
static std::map<unsigned int, unsigned int> DayNightResourceMap = {
    {LOCATOR_COMPASS_OVERVIEW_IMAGE_DAY, LOCATOR_COMPASS_OVERVIEW_IMAGE_NIGHT},
    {LOCATOR_INDICATOR_OVERVIEW_IMAGE_DAY, LOCATOR_INDICATOR_OVERVIEW_IMAGE_NIGHT},

    {LOCATOR_INDICATOR_DAY, LOCATOR_INDICATOR_NIGHT},
    {LOCATOR_COMPASS_EAST_DAY, LOCATOR_COMPASS_EAST_NIGHT},
    {LOCATOR_COMPASS_SOUTH_DAY, LOCATOR_COMPASS_SOUTH_NIGHT},
    {LOCATOR_COMPASS_WEST_DAY, LOCATOR_COMPASS_WEST_NIGHT},
    {LOCATOR_COMPASS_NORTH_DAY, LOCATOR_COMPASS_NORTH_NIGHT},
    {LOCATOR_COMPASS_RING_DAY, LOCATOR_COMPASS_RING_NIGHT},
    {LOCATOR_INDICATOR_GPS_DAY, LOCATOR_INDICATOR_GPS_NIGHT},
    {LOCATOR_INDICATOR_BACKGROUND_DAY, LOCATOR_INDICATOR_BACKGROUND_NIGHT},

    {OVERVIEW_START_POINT_DAY, OVERVIEW_START_POINT_NIGHT},
    {OVERVIEW_END_POINT_DAY, OVERVIEW_END_POINT_NIGHT},
    {OVERVIEW_VIA_POINT_DAY, OVERVIEW_VIA_POINT_NIGHT},

    {ROUTE_START_POINT_DAY, ROUTE_START_POINT_NIGHT},
    {ROUTE_VIA_POINT_DAY, ROUTE_VIA_POINT_NIGHT},
    {ROUTE_VIA_POINT_1_DAY, ROUTE_VIA_POINT_1_NIGHT},
    {ROUTE_VIA_POINT_2_DAY, ROUTE_VIA_POINT_2_NIGHT},
    {ROUTE_END_POINT_DAY, ROUTE_END_POINT_NIGHT},
    {ROUTE_START_BUBBLE_DAY, ROUTE_START_BUBBLE_NIGHT},
    {ROUTE_END_BUBBLE_DAY, ROUTE_END_BUBBLE_NIGHT},
    {ROUTE_INDOOR_POINT_DAY, ROUTE_INDOOR_POINT_NIGHT},
    {ROUTE_Walk_bad_route_DAY, ROUTE_Walk_bad_route_NIGHT},
    {ROUTE_Walk_ferry_DAY, ROUTE_Walk_ferry_NIGHT},
    {ROUTE_Walk_footbridge_DAY, ROUTE_Walk_footbridge_NIGHT},
    {ROUTE_Walk_inner_route_DAY, ROUTE_Walk_inner_route_NIGHT},
    {ROUTE_Walk_retrograde_DAY, ROUTE_Walk_retrograde_NIGHT},
    {ROUTE_Walk_steps_DAY, ROUTE_Walk_steps_NIGHT},
    {ROUTE_Walk_underground_DAY, ROUTE_Walk_underground_NIGHT},
    {ROUTE_Walk_cross_walk_DAY, ROUTE_Walk_cross_walk_NIGHT},
    {ROUTE_Walk_default_DAY, ROUTE_Walk_default_NIGHT},

    {GUIDANCE_TRAFFIC_LIGHT_DAY, GUIDANCE_TRAFFIC_LIGHT_NIGHT},

    {GUIDANCE_TRAFFIC_EVENT_DEFAULT_DAY, GUIDANCE_TRAFFIC_EVENT_DEFAULT_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_ACCIDENT_DAY, GUIDANCE_TRAFFIC_EVENT_ACCIDENT_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_BADWEATHER_DAY, GUIDANCE_TRAFFIC_EVENT_BADWEATHER_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_CHECK_DAY, GUIDANCE_TRAFFIC_EVENT_CHECK_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_CONGESTION_DAY, GUIDANCE_TRAFFIC_EVENT_CONGESTION_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_DAY, GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_CONTROL_DAY, GUIDANCE_TRAFFIC_EVENT_CONTROL_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_DISASTER_DAY, GUIDANCE_TRAFFIC_EVENT_DISASTER_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_DAY, GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_DAY, GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_DAY, GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_DAY, GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_POINT_ACTIVITY_DAY, GUIDANCE_TRAFFIC_EVENT_POINT_ACTIVITY_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_POINT_RAIN_DAY, GUIDANCE_TRAFFIC_EVENT_POINT_RAIN_NIGHT},
    {GUIDANCE_TRAFFIC_EVENT_POINT_BULLETIN_DAY, GUIDANCE_TRAFFIC_EVENT_POINT_BULLETIN_NIGHT},

    {GUIDANCE_WARNING_DEFAULT_DAY, GUIDANCE_WARNING_DEFAULT_NIGHT},
    {GUIDANCE_WARNING_AlongMountain_DAY, GUIDANCE_WARNING_AlongMountain_NIGHT},
    {GUIDANCE_WARNING_AlongMountainEx_DAY, GUIDANCE_WARNING_AlongMountainEx_NIGHT},
    {GUIDANCE_WARNING_UpSteepSlope_DAY, GUIDANCE_WARNING_UpSteepSlope_NIGHT},
    {GUIDANCE_WARNING_AccidentProneSection_DAY, GUIDANCE_WARNING_AccidentProneSection_NIGHT},
    {GUIDANCE_WARNING_Caution_DAY, GUIDANCE_WARNING_Caution_NIGHT},
    {GUIDANCE_WARNING_ContinuousDownSlope_DAY, GUIDANCE_WARNING_ContinuousDownSlope_NIGHT},
    {GUIDANCE_WARNING_Detour_DAY, GUIDANCE_WARNING_Detour_NIGHT},
    {GUIDANCE_WARNING_DetourLeft_DAY, GUIDANCE_WARNING_DetourLeft_NIGHT},
    {GUIDANCE_WARNING_DetourRight_DAY, GUIDANCE_WARNING_DetourRight_NIGHT},
    {GUIDANCE_WARNING_MindCrosswind_DAY, GUIDANCE_WARNING_MindCrosswind_NIGHT},
    {GUIDANCE_WARNING_MindLeftMerge_DAY, GUIDANCE_WARNING_MindLeftMerge_NIGHT},
    {GUIDANCE_WARNING_MindRightMerge_DAY, GUIDANCE_WARNING_MindRightMerge_NIGHT},
    {GUIDANCE_WARNING_NarrowBridge_DAY, GUIDANCE_WARNING_NarrowBridge_NIGHT},
    {GUIDANCE_WARNING_BothNarrow_DAY, GUIDANCE_WARNING_BothNarrow_NIGHT},
    {GUIDANCE_WARNING_LeftNarrow_DAY, GUIDANCE_WARNING_LeftNarrow_NIGHT},
    {GUIDANCE_WARNING_RightNarrow_DAY, GUIDANCE_WARNING_RightNarrow_NIGHT},
    {GUIDANCE_WARNING_NoOvertake_DAY, GUIDANCE_WARNING_NoOvertake_NIGHT},
    {GUIDANCE_WARNING_RailwayCrossingManaged_DAY, GUIDANCE_WARNING_RailwayCrossingManaged_NIGHT},
    {GUIDANCE_WARNING_ReverseRoadLeft_DAY, GUIDANCE_WARNING_ReverseRoadLeft_NIGHT},
    {GUIDANCE_WARNING_ReverseRoadRight_DAY, GUIDANCE_WARNING_ReverseRoadRight_NIGHT},
    {GUIDANCE_WARNING_MindRock_DAY, GUIDANCE_WARNING_MindRock_NIGHT},
    {GUIDANCE_WARNING_MindRockEx_DAY, GUIDANCE_WARNING_MindRockEx_NIGHT},
    {GUIDANCE_WARNING_UnevenRoad_DAY, GUIDANCE_WARNING_UnevenRoad_NIGHT},
    {GUIDANCE_WARNING_MindChildren_DAY, GUIDANCE_WARNING_MindChildren_NIGHT},
    {GUIDANCE_WARNING_SharpLeftRoad_DAY, GUIDANCE_WARNING_SharpLeftRoad_NIGHT},
    {GUIDANCE_WARNING_SharpRightRoad_DAY, GUIDANCE_WARNING_SharpRightRoad_NIGHT},
    {GUIDANCE_WARNING_SlipperyRoad_DAY, GUIDANCE_WARNING_SlipperyRoad_NIGHT},
    {GUIDANCE_WARNING_DownSteepSlope_DAY, GUIDANCE_WARNING_DownSteepSlope_NIGHT},
    {GUIDANCE_WARNING_TextWarning_DAY, GUIDANCE_WARNING_TextWarning_NIGHT},
    {GUIDANCE_WARNING_RailwayCrossingWild_DAY, GUIDANCE_WARNING_RailwayCrossingWild_NIGHT},
    {GUIDANCE_WARNING_TurnOnLightInTunnel_DAY, GUIDANCE_WARNING_TurnOnLightInTunnel_NIGHT},
    {GUIDANCE_WARNING_Village_DAY, GUIDANCE_WARNING_Village_NIGHT},
    {GUIDANCE_WARNING_WaterRoad_DAY, GUIDANCE_WARNING_WaterRoad_NIGHT},
    {GUIDANCE_WARNING_ContinuousDetour_DAY, GUIDANCE_WARNING_ContinuousDetour_NIGHT},
    {GUIDANCE_WARNING_LaneSlideSlop_DAY, GUIDANCE_WARNING_LaneSlideSlop_NIGHT},
    {GUIDANCE_WARNING_DirtRoad_DAY, GUIDANCE_WARNING_DirtRoad_NIGHT},
    {GUIDANCE_WARNING_GroundSink_DAY, GUIDANCE_WARNING_GroundSink_NIGHT},
    {GUIDANCE_WARNING_Collapse_DAY, GUIDANCE_WARNING_Collapse_NIGHT},
    {GUIDANCE_WARNING_OppositeRoad_DAY, GUIDANCE_WARNING_OppositeRoad_NIGHT},
    {GUIDANCE_WARNING_MindLivestock_DAY, GUIDANCE_WARNING_MindLivestock_NIGHT},
    {GUIDANCE_WARNING_DamRoad_DAY, GUIDANCE_WARNING_DamRoad_NIGHT},
    {GUIDANCE_WARNING_DamRoadEx_DAY, GUIDANCE_WARNING_DamRoadEx_NIGHT},
    {GUIDANCE_WARNING_CamelBackBridge_DAY, GUIDANCE_WARNING_CamelBackBridge_NIGHT},
    {GUIDANCE_WARNING_AllowOvertake_DAY, GUIDANCE_WARNING_AllowOvertake_NIGHT},
    {GUIDANCE_WARNING_Honking_DAY, GUIDANCE_WARNING_Honking_NIGHT},
    {GUIDANCE_WARNING_StopGiveway_DAY, GUIDANCE_WARNING_StopGiveway_NIGHT},
    {GUIDANCE_WARNING_EncounterGiveway_DAY, GUIDANCE_WARNING_EncounterGiveway_NIGHT},
    {GUIDANCE_WARNING_SlowdownGiveway_DAY, GUIDANCE_WARNING_SlowdownGiveway_NIGHT},
    {GUIDANCE_WARNING_TidalLane_DAY, GUIDANCE_WARNING_TidalLane_NIGHT},
    {GUIDANCE_WARNING_ProtrudingRoad_DAY, GUIDANCE_WARNING_ProtrudingRoad_NIGHT},
    {GUIDANCE_WARNING_SinkingRoad_DAY, GUIDANCE_WARNING_SinkingRoad_NIGHT},
    {GUIDANCE_WARNING_TrafficAccidentBlackSpots_DAY, GUIDANCE_WARNING_TrafficAccidentBlackSpots_NIGHT},
    {GUIDANCE_WARNING_QuarantineStation_DAY, GUIDANCE_WARNING_QuarantineStation_NIGHT},
    {GUIDANCE_WARNING_Zebra_DAY, GUIDANCE_WARNING_Zebra_NIGHT}};

static std::map<unsigned int, std::string> ResourceID2DescriptionMap = {
    {LOCATOR_COMPASS_OVERVIEW_IMAGE_DAY, "白天-鹰眼图车标罗盘"},
    {LOCATOR_INDICATOR_OVERVIEW_IMAGE_DAY, "白天-鹰眼图车标箭头"},

    {LOCATOR_INDICATOR_DAY, "白天-车标箭头"},
    {LOCATOR_COMPASS_EAST_DAY, "白天-车标罗盘东"},
    {LOCATOR_COMPASS_SOUTH_DAY, "白天-车标罗盘南"},
    {LOCATOR_COMPASS_WEST_DAY, "白天-车标罗盘西"},
    {LOCATOR_COMPASS_NORTH_DAY, "白天-车标罗盘北"},
    {LOCATOR_COMPASS_RING_DAY, "白天-车标罗盘环"},
    {LOCATOR_INDICATOR_GPS_DAY, "白天-车标罗盘东"},
    {LOCATOR_INDICATOR_BACKGROUND_DAY, "白天-车标箭头背景"},

    {OVERVIEW_START_POINT_DAY, "白天-鹰眼图起点"},
    {OVERVIEW_END_POINT_DAY, "白天-鹰眼图终点"},
    {OVERVIEW_VIA_POINT_DAY, "白天-鹰眼图途径点"},

    {ROUTE_START_POINT_DAY, "白天-路线起点"},
    {ROUTE_VIA_POINT_DAY, "白天-路线途径点"},
    {ROUTE_VIA_POINT_1_DAY, "白天-路线途径点一"},
    {ROUTE_VIA_POINT_2_DAY, "白天-路线途径点二"},
    {ROUTE_END_POINT_DAY, "白天-路线终点"},
    {ROUTE_START_BUBBLE_DAY, "白天-路线起点气泡"},
    {ROUTE_END_BUBBLE_DAY, "白天-路线终点气泡"},
    {ROUTE_INDOOR_POINT_DAY, "白天-楼层切换点"},
    {ROUTE_Walk_bad_route_DAY, "白天-质量差道路"},
    {ROUTE_Walk_ferry_DAY, "白天-轮渡"},
    {ROUTE_Walk_footbridge_DAY, "白天-天桥"},
    {ROUTE_Walk_inner_route_DAY, "白天-内部道路"},
    {ROUTE_Walk_retrograde_DAY, "白天-逆行"},
    {ROUTE_Walk_steps_DAY, "白天-台阶"},
    {ROUTE_Walk_underground_DAY, "白天-地下通道"},
    {ROUTE_Walk_cross_walk_DAY, "白天-人行横道"},
    {ROUTE_Walk_default_DAY, "白天-步行默认资源"},

    {GUIDANCE_TRAFFIC_LIGHT_DAY, "白天-红绿灯"},

    {GUIDANCE_TRAFFIC_EVENT_DEFAULT_DAY, "白天-交通点事件-默认"},
    {GUIDANCE_TRAFFIC_EVENT_ACCIDENT_DAY, "白天-交通点事件-事故"},
    {GUIDANCE_TRAFFIC_EVENT_BADWEATHER_DAY, "白天-交通点事件-恶劣天气"},
    {GUIDANCE_TRAFFIC_EVENT_CHECK_DAY, "白天-交通点事件-检查"},
    {GUIDANCE_TRAFFIC_EVENT_CONGESTION_DAY, "白天-交通点事件-拥堵"},
    {GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_DAY, "白天-交通点事件-施工"},
    {GUIDANCE_TRAFFIC_EVENT_CONTROL_DAY, "白天-交通点事件-管制"},
    {GUIDANCE_TRAFFIC_EVENT_DISASTER_DAY, "白天-交通点事件-灾害"},
    {GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_DAY, "白天-交通点事件-障碍"},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_DAY, "白天-交通点事件-封路"},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_DAY, "白天-交通点事件-山路"},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_DAY, "白天-交通点事件-窄路"},
    {GUIDANCE_TRAFFIC_EVENT_POINT_ACTIVITY_DAY, "白天-交通点事件-活动"},
    {GUIDANCE_TRAFFIC_EVENT_POINT_RAIN_DAY, "白天-交通点事件-积水"},
    {GUIDANCE_TRAFFIC_EVENT_POINT_BULLETIN_DAY, "白天-交通点事件-公告"},

    {GUIDANCE_WARNING_DEFAULT_DAY, "白天-警示牌-默认资源"},
    {GUIDANCE_WARNING_AlongMountain_DAY, "白天-警示牌-傍山险路"},
    {GUIDANCE_WARNING_AlongMountainEx_DAY, "白天-警示牌-傍山险路"},
    {GUIDANCE_WARNING_UpSteepSlope_DAY, "白天-警示牌-上陡坡"},
    {GUIDANCE_WARNING_AccidentProneSection_DAY, "白天-警示牌-事故易发路段"},
    {GUIDANCE_WARNING_Caution_DAY, "白天-警示牌-注意危险"},
    {GUIDANCE_WARNING_ContinuousDownSlope_DAY, "白天-警示牌-连续下坡"},
    {GUIDANCE_WARNING_Detour_DAY, "白天-警示牌-左右绕行"},
    {GUIDANCE_WARNING_DetourLeft_DAY, "白天-警示牌-左侧绕行"},
    {GUIDANCE_WARNING_DetourRight_DAY, "白天-警示牌-右侧绕行"},
    {GUIDANCE_WARNING_MindCrosswind_DAY, "白天-警示牌-注意横风"},
    {GUIDANCE_WARNING_MindLeftMerge_DAY, "白天-警示牌-注意左侧合流"},
    {GUIDANCE_WARNING_MindRightMerge_DAY, "白天-警示牌-注意右侧合流"},
    {GUIDANCE_WARNING_NarrowBridge_DAY, "白天-警示牌-窄桥"},
    {GUIDANCE_WARNING_BothNarrow_DAY, "白天-警示牌-两侧变窄"},
    {GUIDANCE_WARNING_LeftNarrow_DAY, "白天-警示牌-左侧变窄"},
    {GUIDANCE_WARNING_RightNarrow_DAY, "白天-警示牌-右侧变窄"},
    {GUIDANCE_WARNING_NoOvertake_DAY, "白天-警示牌-禁止超车"},
    {GUIDANCE_WARNING_RailwayCrossingManaged_DAY, "白天-警示牌-有人看守铁路道口"},
    {GUIDANCE_WARNING_ReverseRoadLeft_DAY, "白天-警示牌-反向弯路(左)"},
    {GUIDANCE_WARNING_ReverseRoadRight_DAY, "白天-警示牌-反向弯路(右)"},
    {GUIDANCE_WARNING_MindRock_DAY, "白天-警示牌-注意落石"},
    {GUIDANCE_WARNING_MindRockEx_DAY, "白天-警示牌-注意落石"},
    {GUIDANCE_WARNING_UnevenRoad_DAY, "白天-警示牌-路面不平"},
    {GUIDANCE_WARNING_MindChildren_DAY, "白天-警示牌-注意儿童"},
    {GUIDANCE_WARNING_SharpLeftRoad_DAY, "白天-警示牌-向左急弯路"},
    {GUIDANCE_WARNING_SharpRightRoad_DAY, "白天-警示牌-向右急弯路"},
    {GUIDANCE_WARNING_SlipperyRoad_DAY, "白天-警示牌-易滑"},
    {GUIDANCE_WARNING_DownSteepSlope_DAY, "白天-警示牌-下陡坡"},
    {GUIDANCE_WARNING_TextWarning_DAY, "白天-警示牌-文字性警示标牌"},
    {GUIDANCE_WARNING_RailwayCrossingWild_DAY, "白天-警示牌-无人看守铁路道口"},
    {GUIDANCE_WARNING_TurnOnLightInTunnel_DAY, "白天-警示牌-隧道开灯"},
    {GUIDANCE_WARNING_Village_DAY, "白天-警示牌-村庄"},
    {GUIDANCE_WARNING_WaterRoad_DAY, "白天-警示牌-过水路面"},
    {GUIDANCE_WARNING_ContinuousDetour_DAY, "白天-警示牌-连续弯路"},
    {GUIDANCE_WARNING_LaneSlideSlop_DAY, "白天-警示牌-滑坡"},
    {GUIDANCE_WARNING_DirtRoad_DAY, "白天-警示牌-泥石路"},
    {GUIDANCE_WARNING_GroundSink_DAY, "白天-警示牌-地面塌陷"},
    {GUIDANCE_WARNING_Collapse_DAY, "白天-警示牌-塌方"},
    {GUIDANCE_WARNING_OppositeRoad_DAY, "白天-警示牌-双向交通"},
    {GUIDANCE_WARNING_MindLivestock_DAY, "白天-警示牌-注意牲畜"},
    {GUIDANCE_WARNING_DamRoad_DAY, "白天-警示牌-堤坝路"},
    {GUIDANCE_WARNING_DamRoadEx_DAY, "白天-警示牌-堤坝路"},
    {GUIDANCE_WARNING_CamelBackBridge_DAY, "白天-警示牌-驼峰桥"},
    {GUIDANCE_WARNING_AllowOvertake_DAY, "白天-警示牌-解除禁止超车"},
    {GUIDANCE_WARNING_Honking_DAY, "白天-警示牌-鸣喇叭"},
    {GUIDANCE_WARNING_StopGiveway_DAY, "白天-警示牌-停车让行"},
    {GUIDANCE_WARNING_EncounterGiveway_DAY, "白天-警示牌-会车让行"},
    {GUIDANCE_WARNING_SlowdownGiveway_DAY, "白天-警示牌-减速让行"},
    {GUIDANCE_WARNING_TidalLane_DAY, "白天-警示牌-潮汐车道"},
    {GUIDANCE_WARNING_ProtrudingRoad_DAY, "白天-警示牌-路面高凸"},
    {GUIDANCE_WARNING_SinkingRoad_DAY, "白天-警示牌-路面低洼"},
    {GUIDANCE_WARNING_TrafficAccidentBlackSpots_DAY, "白天-警示牌-事故多发"},
    {GUIDANCE_WARNING_QuarantineStation_DAY, "白天-警示牌-检疫站"},
    {GUIDANCE_WARNING_Zebra_DAY, "白天-警示牌-斑马线"},

    {LOCATOR_COMPASS_OVERVIEW_IMAGE_NIGHT, "夜间-鹰眼图车标罗盘"},
    {LOCATOR_INDICATOR_OVERVIEW_IMAGE_NIGHT, "夜间-鹰眼图车标箭头"},

    {LOCATOR_INDICATOR_NIGHT, "夜间-车标箭头"},
    {LOCATOR_COMPASS_EAST_NIGHT, "夜间-车标罗盘东"},
    {LOCATOR_COMPASS_SOUTH_NIGHT, "夜间-车标罗盘南"},
    {LOCATOR_COMPASS_WEST_NIGHT, "夜间-车标罗盘西"},
    {LOCATOR_COMPASS_NORTH_NIGHT, "夜间-车标罗盘北"},
    {LOCATOR_COMPASS_RING_NIGHT, "夜间-车标罗盘环"},
    {LOCATOR_INDICATOR_GPS_NIGHT, "夜间-车标罗盘东"},
    {LOCATOR_INDICATOR_BACKGROUND_NIGHT, "夜间-车标箭头背景"},

    {OVERVIEW_START_POINT_NIGHT, "夜间-鹰眼图起点"},
    {OVERVIEW_END_POINT_NIGHT, "夜间-鹰眼图终点"},
    {OVERVIEW_VIA_POINT_NIGHT, "夜间-鹰眼图途径点"},

    {ROUTE_START_POINT_NIGHT, "夜间-路线起点"},
    {ROUTE_VIA_POINT_NIGHT, "夜间-路线途径点"},
    {ROUTE_VIA_POINT_1_NIGHT, "夜间-路线途径点一"},
    {ROUTE_VIA_POINT_2_NIGHT, "夜间-路线途径点二"},
    {ROUTE_END_POINT_NIGHT, "夜间-路线终点"},
    {ROUTE_START_BUBBLE_NIGHT, "夜间-路线起点气泡"},
    {ROUTE_END_BUBBLE_NIGHT, "夜间-路线终点气泡"},
    {GUIDANCE_TRAFFIC_LIGHT_NIGHT, "夜间-红绿灯"},
    {ROUTE_INDOOR_POINT_NIGHT, "夜间-楼层切换点"},
    {ROUTE_Walk_bad_route_NIGHT, "夜间-质量差道路"},
    {ROUTE_Walk_ferry_NIGHT, "夜间-轮渡"},
    {ROUTE_Walk_footbridge_NIGHT, "夜间-天桥"},
    {ROUTE_Walk_inner_route_NIGHT, "夜间-内部道路"},
    {ROUTE_Walk_retrograde_NIGHT, "夜间-逆行"},
    {ROUTE_Walk_steps_NIGHT, "夜间-台阶"},
    {ROUTE_Walk_underground_NIGHT, "夜间-地下通道"},
    {ROUTE_Walk_cross_walk_NIGHT, "夜间-人行横道"},
    {ROUTE_Walk_default_NIGHT, "夜间-步行默认资源"},

    {GUIDANCE_TRAFFIC_EVENT_DEFAULT_NIGHT, "夜间-交通点事件-默认"},
    {GUIDANCE_TRAFFIC_EVENT_ACCIDENT_NIGHT, "夜间-交通点事件-事故"},
    {GUIDANCE_TRAFFIC_EVENT_BADWEATHER_NIGHT, "夜间-交通点事件-恶劣天气"},
    {GUIDANCE_TRAFFIC_EVENT_CHECK_NIGHT, "夜间-交通点事件-检查"},
    {GUIDANCE_TRAFFIC_EVENT_CONGESTION_NIGHT, "夜间-交通点事件-拥堵"},
    {GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_NIGHT, "夜间-交通点事件-施工"},
    {GUIDANCE_TRAFFIC_EVENT_CONTROL_NIGHT, "夜间-交通点事件-管制"},
    {GUIDANCE_TRAFFIC_EVENT_DISASTER_NIGHT, "夜间-交通点事件-灾害"},
    {GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_NIGHT, "夜间-交通点事件-障碍"},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_NIGHT, "夜间-交通点事件-封路"},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_NIGHT, "夜间-交通点事件-山路"},
    {GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_NIGHT, "夜间-交通点事件-窄路"},
    {GUIDANCE_TRAFFIC_EVENT_POINT_ACTIVITY_NIGHT, "夜间-交通点事件-活动"},
    {GUIDANCE_TRAFFIC_EVENT_POINT_RAIN_NIGHT, "夜间-交通点事件-积水"},
    {GUIDANCE_TRAFFIC_EVENT_POINT_BULLETIN_NIGHT, "夜间-交通点事件-公告"},

    {GUIDANCE_WARNING_DEFAULT_NIGHT, "夜间-警示牌-默认资源"},
    {GUIDANCE_WARNING_AlongMountain_NIGHT, "夜间-警示牌-傍山险路"},
    {GUIDANCE_WARNING_AlongMountainEx_NIGHT, "夜间-警示牌-傍山险路"},
    {GUIDANCE_WARNING_UpSteepSlope_NIGHT, "夜间-警示牌-上陡坡"},
    {GUIDANCE_WARNING_AccidentProneSection_NIGHT, "夜间-警示牌-事故易发路段"},
    {GUIDANCE_WARNING_Caution_NIGHT, "夜间-警示牌-注意危险"},
    {GUIDANCE_WARNING_ContinuousDownSlope_NIGHT, "夜间-警示牌-连续下坡"},
    {GUIDANCE_WARNING_Detour_NIGHT, "夜间-警示牌-左右绕行"},
    {GUIDANCE_WARNING_DetourLeft_NIGHT, "夜间-警示牌-左侧绕行"},
    {GUIDANCE_WARNING_DetourRight_NIGHT, "夜间-警示牌-右侧绕行"},
    {GUIDANCE_WARNING_MindCrosswind_NIGHT, "夜间-警示牌-注意横风"},
    {GUIDANCE_WARNING_MindLeftMerge_NIGHT, "夜间-警示牌-注意左侧合流"},
    {GUIDANCE_WARNING_MindRightMerge_NIGHT, "夜间-警示牌-注意右侧合流"},
    {GUIDANCE_WARNING_NarrowBridge_NIGHT, "夜间-警示牌-窄桥"},
    {GUIDANCE_WARNING_BothNarrow_NIGHT, "夜间-警示牌-两侧变窄"},
    {GUIDANCE_WARNING_LeftNarrow_NIGHT, "夜间-警示牌-左侧变窄"},
    {GUIDANCE_WARNING_RightNarrow_NIGHT, "夜间-警示牌-右侧变窄"},
    {GUIDANCE_WARNING_NoOvertake_NIGHT, "夜间-警示牌-禁止超车"},
    {GUIDANCE_WARNING_RailwayCrossingManaged_NIGHT, "夜间-警示牌-有人看守铁路道口"},
    {GUIDANCE_WARNING_ReverseRoadLeft_NIGHT, "夜间-警示牌-反向弯路(左)"},
    {GUIDANCE_WARNING_ReverseRoadRight_NIGHT, "夜间-警示牌-反向弯路(右)"},
    {GUIDANCE_WARNING_MindRock_NIGHT, "夜间-警示牌-注意落石"},
    {GUIDANCE_WARNING_MindRockEx_NIGHT, "夜间-警示牌-注意落石"},
    {GUIDANCE_WARNING_UnevenRoad_NIGHT, "夜间-警示牌-路面不平"},
    {GUIDANCE_WARNING_MindChildren_NIGHT, "夜间-警示牌-注意儿童"},
    {GUIDANCE_WARNING_SharpLeftRoad_NIGHT, "夜间-警示牌-向左急弯路"},
    {GUIDANCE_WARNING_SharpRightRoad_NIGHT, "夜间-警示牌-向右急弯路"},
    {GUIDANCE_WARNING_SlipperyRoad_NIGHT, "夜间-警示牌-易滑"},
    {GUIDANCE_WARNING_DownSteepSlope_NIGHT, "夜间-警示牌-下陡坡"},
    {GUIDANCE_WARNING_TextWarning_NIGHT, "夜间-警示牌-文字性警示标牌"},
    {GUIDANCE_WARNING_RailwayCrossingWild_NIGHT, "夜间-警示牌-无人看守铁路道口"},
    {GUIDANCE_WARNING_TurnOnLightInTunnel_NIGHT, "夜间-警示牌- 隧道开灯"},
    {GUIDANCE_WARNING_Village_NIGHT, "夜间-警示牌-村庄"},
    {GUIDANCE_WARNING_WaterRoad_NIGHT, "夜间-警示牌-过水路面"},
    {GUIDANCE_WARNING_ContinuousDetour_NIGHT, "夜间-警示牌- 连续弯路"},
    {GUIDANCE_WARNING_LaneSlideSlop_NIGHT, "夜间-警示牌-滑坡"},
    {GUIDANCE_WARNING_DirtRoad_NIGHT, "夜间-警示牌-泥石路"},
    {GUIDANCE_WARNING_GroundSink_NIGHT, "夜间-警示牌-地面塌陷"},
    {GUIDANCE_WARNING_Collapse_NIGHT, "夜间-警示牌-塌方"},
    {GUIDANCE_WARNING_OppositeRoad_NIGHT, "夜间-警示牌-双向交通"},
    {GUIDANCE_WARNING_MindLivestock_NIGHT, "夜间-警示牌-注意牲畜"},
    {GUIDANCE_WARNING_DamRoad_NIGHT, "夜间-警示牌-堤坝路"},
    {GUIDANCE_WARNING_DamRoadEx_NIGHT, "夜间-警示牌-堤坝路"},
    {GUIDANCE_WARNING_CamelBackBridge_NIGHT, "夜间-警示牌-驼峰桥"},
    {GUIDANCE_WARNING_AllowOvertake_NIGHT, "夜间-警示牌-解除禁止超车"},
    {GUIDANCE_WARNING_Honking_NIGHT, "夜间-警示牌-鸣喇叭"},
    {GUIDANCE_WARNING_StopGiveway_NIGHT, "夜间-警示牌-停车让行"},
    {GUIDANCE_WARNING_EncounterGiveway_NIGHT, "夜间-警示牌-会车让行"},
    {GUIDANCE_WARNING_SlowdownGiveway_NIGHT, "夜间-警示牌-减速让行"},
    {GUIDANCE_WARNING_TidalLane_NIGHT, "夜间-警示牌-潮汐车道"},
    {GUIDANCE_WARNING_ProtrudingRoad_NIGHT, "夜间-警示牌-路面高凸"},
    {GUIDANCE_WARNING_SinkingRoad_NIGHT, "夜间-警示牌-路面低洼"},
    {GUIDANCE_WARNING_TrafficAccidentBlackSpots_NIGHT, "夜间-警示牌-事故多发"},
    {GUIDANCE_WARNING_QuarantineStation_NIGHT, "夜间-警示牌-检疫站"},
    {GUIDANCE_WARNING_Zebra_NIGHT, "夜间-警示牌-斑马线"}};
}  // namespace R

/**
 * 资源文件的文件头
 */
struct MapResourceFileHeader {
  unsigned int resource_sign = 0;
  unsigned int resource_count = 0;
};
/**
 * 该结构用于内部加速解析文件
 */
struct MapResourceHeaderInfo {
  /**
   * 资源唯一标示
   */
  unsigned int id_ = 0;
  /**
   * 资源内容在文件中的偏移
   */
  unsigned int offset_ = 0;
  /**
   * 资源文件的二进制流大小
   */
  unsigned int size_ = 0;
  /**
   * 预留字段，该结构用于加速读取文件;
   */
  unsigned int reserve_ = 0;
};
/**
 * 资源头描述符
 */
struct MapResourceHeaderDescriptor {
  /**
   * 资源唯一标示
   */
  unsigned int id_ = 0;
  /**
   * 资源内容在文件中的偏移
   */
  unsigned int offset_ = 0;
  /**
   * 资源文件的二进制流大小
   */
  unsigned int size_ = 0;
  /**
   * 资源文件中的锚点信息
   */
  float anchorX = 0.5f;
  /**
   * 资源文件中的锚点信息
   */
  float anchorY = 0.5f;
  /**
   * 是否来自中文资源文件
   */
  bool is_cn = true;
  /**
   * 保留字段
   */
  unsigned char reserve1;
  unsigned char reserve2;
  unsigned char reserve3;
};

std::ostream& operator<<(std::ostream& out, const MapResourceHeaderDescriptor& other);

/**
 * 资源内容描述符
 */
class MapResourceContentDescriptor {
 public:
  MapResourceContentDescriptor();
  MapResourceContentDescriptor(const MapResourceContentDescriptor& other);
  ~MapResourceContentDescriptor();

 public:
  MapResourceContentDescriptor& operator=(const MapResourceContentDescriptor& other);
  friend std::ostream& operator<<(std::ostream& out, const MapResourceContentDescriptor& other);

 public:
  /**
   * 重置内容
   */
  void Reset();
  /**
   * 判断是否有效
   * @return
   */
  bool IsValid() const;

  /**
   * 深拷贝图像Buffer
   */
  void SetBuffer(const char* buffer, const unsigned size);

  const char* GetBuffer() const { return buffer_; }
  /**
   * 设置资源的锚点信息，uv坐标系
   * @param x
   * @param y
   */
  void SetAnchor(float x, float y) {
    anchorX = x;
    anchorY = y;
  }

  /**
   * 获取字节流大小
   * @return
   */
  const unsigned GetBufferSize() const { return buffer_size_; }

 public:
  /**
   * 资源唯一标示
   */
  unsigned int id_;
  /**
   * 资源图片宽度
   */
  uint16_t width_;
  /**
   * 资源图片高度
   */
  uint16_t height_;
  /**
   * 资源图片深度信息
   */
  unsigned int depth_;
  /**
   * 缩放比例尺
   * 图片一般为像素，为了让图片在不同设备上显示比例尺相同
   * 引擎需要依赖该缩放系数转为dp, 最终显示时 dp * 整体屏幕密度；
   */
  float scale_;
  /**
   * 图片资源的锚点位置；默认中心0.5,0.5位置
   * 范围[0,0] - [1,1];
   * 0,0代表图片左上角，1，1代表图片左下角；参考纹理UV坐标系
   */
  float anchorX = 0.5f;
  /**
   * 图片资源的锚点位置；默认中心0.5,0.5位置
   * 范围[0,0] - [1,1];
   * 0,0代表图片左上角，1，1代表图片左下角；参考纹理UV坐标系
   */
  float anchorY = 0.5f;
  /**
   * 是否为压缩纹理
   */
  bool isKtx = false;

 private:
  /**
   * 图像大小
   */
  unsigned int buffer_size_;

  /**
   * PNG图像
   */
  char* buffer_;
};

/**
 * 帧动画参数
 */
class AnimationImageParam {
 public:
  AnimationImageParam();
  AnimationImageParam(const AnimationImageParam& param);
  ~AnimationImageParam();

 public:
  AnimationImageParam& operator=(const AnimationImageParam& other);
  friend std::ostream& operator<<(std::ostream& out, const AnimationImageParam& other);

 public:
  /**
   * 重置内容
   */
  void Reset();
  /**
   * 判断是否有效
   * @return
   */
  bool IsValid() const;

 public:
  MapResourceContentDescriptor descriptor;
  std::string animation_image_name;  // 帧动画资源名称
  float angle;                       // 旋转角度，默认0
  int split_num_x;                   // 帧动画图片横向的单元个数
  int split_num_y;                   // 帧动画图片纵向的单元个数
  float offset_x;                    // 帧图片相对于背景图片左下角的X轴偏移量，范围[0,1]
  float offset_y;                    // 帧图片相对于背景图片左下角的Y轴偏移量，范围[0,1]
  int animation_time;                // 动画播放时长，单位ms
};

/**
 * 帧动画资源
 */
class AnimationResource {
 public:
  AnimationResource();
  AnimationResource(const AnimationResource& resource);
  ~AnimationResource();

 public:
  AnimationResource& operator=(const AnimationResource& other);
  friend std::ostream& operator<<(std::ostream& out, const AnimationResource& resource);

 public:
  /**
   * 重置内容
   */
  void Reset();
  /**
   * 判断是否有效
   * @return
   */
  bool IsValid() const;

 public:
  MapResourceContentDescriptor background;
  std::vector<AnimationImageParam> params;
  int16_t direction;
};

/**
 * 客户定制资源描述符号
 * 备注:PNG文件字节流即可
 */
class CustomResourceContentDescriptor {
 public:
  CustomResourceContentDescriptor();
  CustomResourceContentDescriptor(const CustomResourceContentDescriptor& other);
  ~CustomResourceContentDescriptor();

 public:
  CustomResourceContentDescriptor& operator=(const CustomResourceContentDescriptor& other);
  friend std::ostream& operator<<(std::ostream& out, const CustomResourceContentDescriptor& other);

 public:
  /**
   * 重置内容
   */
  void Reset();

  /**
   * 判断是否有效
   * @return
   */
  bool IsValid() const;

  /**
   * 深拷贝图像Buffer
   */
  void SetBuffer(const char* buffer, const unsigned size);

  /**
   * 获取字节流地址
   * @return
   */
  const char* GetBuffer() const { return buffer_; }

  /**
   * 获取字节流大小
   * @return
   */
  const unsigned GetBufferSize() const { return buffer_size_; }
  /**
   * 返回缩放比例尺
   * @return
   */
  const float GetScale() const { return scale_; }
  /**
   * 设置资源的锚点信息，uv坐标系
   * @param x
   * @param y
   */
  void SetAnchor(float x, float y) {
    anchorX = x;
    anchorY = y;
  }
  float GetAnchorX() const { return anchorX; }
  float GetAnchorY() const { return anchorY; }

 private:
  /**
   * 图像大小
   */
  unsigned int buffer_size_;

  /**
   * PNG图像
   */
  char* buffer_;
  /**
   * 缩放比例尺
   * 图片一般为像素，为了让图片在不同设备上显示比例尺相同
   * 引擎需要依赖该缩放系数转为dp, 最终显示时 dp * 整体屏幕密度；
   */
  float scale_ = 1.0f;
  /**
   * 图片资源的锚点位置；默认中心0.5,0.5位置
   * 范围[0,0] - [1,1];
   * 0,0代表图片左上角，1，1代表图片左下角；参考纹理UV坐标系
   */
  float anchorX = 0.5f;
  /**
   * 图片资源的锚点位置；默认中心0.5,0.5位置
   * 范围[0,0] - [1,1];
   * 0,0代表图片左上角，1，1代表图片左下角；参考纹理UV坐标系
   */
  float anchorY = 0.5f;
};

/**
 * 气泡绘制参数
 */
class BubbleDrawDescriptor {
 public:
  BubbleDrawDescriptor();

  virtual ~BubbleDrawDescriptor() = default;

  friend std::ostream& operator<<(std::ostream& out, const BubbleDrawDescriptor& other);
  /**
   * 重置内容
   */
  void Reset();
  /**
   * 判断是否有效
   * @return bool
   */
  virtual bool IsValid() const;

  static const int SCENE_SD = 0;
  static const int SCENE_HD = 1;

 public:
  int scene_ = SCENE_SD;                                                  // hd/sd
  std::string text_content_;                                              // 气泡内容
  std::string language_;                                                  // 语言类型
  int16_t bubble_type_;                                                   // 气泡种类
  int16_t optional_type_;                                                 // 可选附加类型
  int16_t bubble_direction_;                                              // 显示方向
  bool day_mode_;                                                         // 昼夜风格
  std::string font_name;                                                  // 字体名称
  std::string icon;                                                       // bubble 第一层 前置icon
  int font_color = Layer::Style::COLOR_DEFAULT;                           // 字体颜色
  int font_size_for_main_title = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 主标题字体大小
  int font_size_for_sub_title = Layer::Style::FONT_SIZE_FOR_SUB_TITLE;    // 主标题字体大小
  float map_screen_density = 2.0f;                                        // 屏幕密度
  bool is_selected = false;                                               // 是否被选中，默认false未选中
  bool is_force_model = false;
  std::string overlay_id;  // 资源所挂接的overlay id
};

class TrafficDrawDescriptor : public BubbleDrawDescriptor {
 public:
  TrafficDrawDescriptor();
  ~TrafficDrawDescriptor() override = default;
  bool IsValid() const override;

  friend std::ostream& operator<<(std::ostream& out, const TrafficDrawDescriptor& other);

 public:
  /**
   * 诱导下发，拥堵气泡附加信息文案；不空时绘制气泡第二层（由上到下）
   */
  std::string light_text_;
  /**
   * 诱导下发，拥堵气泡附加信息类型: 等灯 or 原因 or 趋势 or 原因+趋势<br>
   * 仅 LightWait 才绘制红绿灯
   */
  mapbase::TrafficExtraDIType extra_type_;
  int font_color_for_main_title;        // 主标题/第一行 文字颜色
  int font_color_for_sub_title;         // 副标题/第二行 文字颜色
  int background_color_for_main_title;  // 主标题/第一行 背景颜色
  int background_color_for_sub_title;   // 副标题/第二行 背景颜色
};

class CameraDrawDescriptor : public BubbleDrawDescriptor {
 public:
  class Camera {
   public:
    friend std::ostream& operator<<(std::ostream& out, const CameraDrawDescriptor::Camera& other);
    int type = 0;         // 电子眼类型
    int limit_speed = 0;  // 限速值，仅限速电子眼、区间测速起点和区间测速终点有效
  };

  CameraDrawDescriptor();
  ~CameraDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const CameraDrawDescriptor& other);

 public:
  std::vector<Camera> cameras;                        // 电子眼列表
  int remain_distance;                                // 电子眼距离当前位置的剩余距离，单位米
  std::string snap_text;                              // 电子眼显示文案
  int font_color_for_normal_type = 0xFF333333;        // 非区间限速电子眼字体颜色
  int background_color_for_normal_type = 0xFFFFFFFF;  // 非区间限速电子眼背景颜色
  int title_color_for_speed_test_type = 0xFFFFFFFF;   // 区间限速电子眼标题颜色
  int font_color_for_speed_test_type;                 // 区间限速电子眼起终点文字颜色
};

class TurnArrowBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  TurnArrowBubbleDrawDescriptor();
  ~TurnArrowBubbleDrawDescriptor() override = default;
  bool IsValid() const override;

  friend std::ostream& operator<<(std::ostream& out, const TurnArrowBubbleDrawDescriptor& other);

 public:
  int remain_distance = 0;            // 转向气泡距离当前位置的剩余距离，单位：米
  int background_color = 0xFFFFFFFF;  // 转向气泡背景颜色
  int icon_color = 0xFF0036BF;        // 转向箭头背景颜色
};

// "({"uid":"%s","content":"%s","second_type":%d,"second_content":[%s],"direction":%d,"mode":%d,"scene_type":%d,"bubble_type":%d,"icon":"%s"}.routeExplainBubble)"
class ExplainBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  ExplainBubbleDrawDescriptor();
  ~ExplainBubbleDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const ExplainBubbleDrawDescriptor& other);

 public:
  std::string uid;                          // bubble id
  std::string content;                      // 第一行内容
  std::vector<std::string> second_content;  // 第二行内容
  int32_t second_type;                      // 第二行显示样式，0标签样式  1竖线分割样式
  /**
   * 220即将加剧 221即将缓解 222即将畅通
   * 223当前不限行，未来将开始限行，且可避开 224当前不限行，未来将开始限行，且无法避开
   * 225当前限行，未来将解除限行，且现在出发进入限行区域时限行已解除
   * 226当前限行，未来将解除限行，且现在出发进入限行区域时限行尚未解除，但到达时30分钟内限行会解除
   * 227即将拥堵 228低速
   * 247通常畅通时刻 248通常拥堵时刻 249比平时拥堵多 250比平时拥堵少
   */
  int32_t scene_type;
  int32_t bubble_type;  // buble 类型 1有文本框 2 纯icon
};

class CompanionBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  CompanionBubbleDrawDescriptor();
  ~CompanionBubbleDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const CompanionBubbleDrawDescriptor& other);
  virtual const char* getTag() const;

 public:
  std::string time_diff;        // 时间差描述
  std::string recommend_label;  // 推荐标签
  std::string distance_diff;    // 距离差描述
  std::string fee_diff;         // 费用差描述
  std::string light_diff;       // 红绿灯差描述
  bool is_recommended;          // 是否推荐
  int scene_type;               // 场景类型
};

class DestNameEtaDrawDescriptor : public BubbleDrawDescriptor {
 public:
  DestNameEtaDrawDescriptor();
  ~DestNameEtaDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const DestNameEtaDrawDescriptor& other);

 public:
  std::string arrive_time;                                   // 到达时间描述
  int dest_name_stroke_width = 2;                            // 终点名称描边宽度
  int dest_name_stroke_color = Layer::Style::COLOR_DEFAULT;  // 终点名称描边颜色，0xAARRGGBB
  int arrive_text_color = Layer::Style::COLOR_DEFAULT;       // 到达时间文字颜色，0xAARRGGBB
  int arrive_background_color = 0xB3000000;                  // 到达时间背景颜色，0xAARRGGBB
};

class LightCountdownTimerDrawDescriptor : public BubbleDrawDescriptor {
 public:
  LightCountdownTimerDrawDescriptor();
  ~LightCountdownTimerDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const LightCountdownTimerDrawDescriptor& other);

 public:
  std::string id;
  int style = 0;
  mapbase::LightStatus light_type = mapbase::LightStatus::None;                     // 红绿灯类型
  mapbase::LightTurnArrowType turn_arrow_type = mapbase::LightTurnArrowType::None;  // 转向箭头类型
  int remain_time = 0;                                                              // 剩余时间，单位秒
  std::string text;                                                                 // 展示文案
  int total_time = 0;                                                               // 当前灯态总时间
  bool is_animation = false;                                                        // 是否是帧动画
};

class MultiLightCountdownTimerDrawDescriptor : public BubbleDrawDescriptor {
 public:
  MultiLightCountdownTimerDrawDescriptor();
  ~MultiLightCountdownTimerDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const MultiLightCountdownTimerDrawDescriptor& other);

  class OneLightCountdownTimerDrawDescriptor {
   public:
    OneLightCountdownTimerDrawDescriptor() = default;
    bool Isvalid() const {
      return light_type != mapbase::LightStatus::None && light_type != mapbase::LightStatus::NotWorking;
    }
    friend std::ostream& operator<<(std::ostream& out, const OneLightCountdownTimerDrawDescriptor& other) {
      out << "OneLightCountdownTimerDrawDescriptor{" << ",lightType=" << static_cast<int>(other.light_type)
          << ",turnArrowType=" << static_cast<int>(other.turn_arrow_type) << ", remainTime=" << other.remain_time
          << ",text" << other.text << ",totalTime=" << other.total_time << "}";
      return out;
    }

   public:
    mapbase::LightStatus light_type = mapbase::LightStatus::None;                     // 红绿灯类型
    mapbase::LightTurnArrowType turn_arrow_type = mapbase::LightTurnArrowType::None;  // 转向箭头类型
    int remain_time = 0;                                                              // 剩余时间，单位秒
    std::string text;                                                                 // 展示文案
    int total_time = 0;                                                               // 当前灯态总时间
  };

 public:
  int style = 0;              // 展示样式
  std::string id;             // 气泡id
  bool is_animation = false;  // 是否是帧动画
  std::vector<OneLightCountdownTimerDrawDescriptor> light_descriptors;
};

class HDLongSolidBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  HDLongSolidBubbleDrawDescriptor();
  ~HDLongSolidBubbleDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const HDLongSolidBubbleDrawDescriptor& other);
};

class ViaPointDrawDescriptor : public BubbleDrawDescriptor {
 public:
  ViaPointDrawDescriptor();
  ~ViaPointDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const ViaPointDrawDescriptor& other);

 public:
  std::string route_id;  // 路线id
  int index;             // 途径点在此路线中的序号
  bool is_selected;      // 途径点是否被选中，默认false未选中
  bool is_reached;       // 途径点是否已驶过，默认false未驶过
  bool is_main_map;      // 是否是主图元素
  int resource_id;       // 资源id
};
class LightWaitBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  LightWaitBubbleDrawDescriptor();
  ~LightWaitBubbleDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const LightWaitBubbleDrawDescriptor& other);
};
class IndoorEntriesBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  IndoorEntriesBubbleDrawDescriptor();
  ~IndoorEntriesBubbleDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const IndoorEntriesBubbleDrawDescriptor& other);

 public:
  int16_t indoor_entries_bubble_type = -1;  // 室内外衔接点气泡类型
  std::string floor_name = "";              // 即将驶入的楼层名称
};

class MarkerDrawDescriptor : public BubbleDrawDescriptor {
 public:
  MarkerDrawDescriptor();
  ~MarkerDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const MarkerDrawDescriptor& other);

 public:
  int16_t marker_type = int16_t(MarkerType::None);  // Marker种类
  std::string name;                                 // Marker名称
  int resource_id;                                  // 资源id
  bool is_main_map = true;                          // 是否是主图元素
};

class YawNoticeBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  YawNoticeBubbleDrawDescriptor();
  ~YawNoticeBubbleDrawDescriptor() override = default;
  bool InitWithJson(const std::string& json);
  bool IsValid() const override;

 public:
  int32_t distance = 0;         // 自车点到气泡距离
  int32_t modelId = 0;          // 模型ID
  int32_t displayPosition = 0;  // 显示位置 0：左， 1：右
  std::string displayContent;   // 气泡显示文本
};

class GuideAreaDrawDescriptor : public BubbleDrawDescriptor {
 public:
  GuideAreaDrawDescriptor();
  ~GuideAreaDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const GuideAreaDrawDescriptor& other);

 public:
  bool guide_line_show_;
  int16_t cur_lane_confidence_;
};

class GuideLineDrawDescriptor : public BubbleDrawDescriptor {
 public:
  GuideLineDrawDescriptor();
  ~GuideLineDrawDescriptor() override = default;
  bool IsValid() const override;
  friend std::ostream& operator<<(std::ostream& out, const GuideLineDrawDescriptor& other);

 public:
  int16_t cur_lane_confidence_;
};

class RouteAccidentBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  RouteAccidentBubbleDrawDescriptor();
  ~RouteAccidentBubbleDrawDescriptor() override = default;
  bool InitWithJson(const std::string& json);
  bool IsValid() const override;
//  std::string ToString() const;
 public:
  int32_t eventType;
  int32_t eventSubType;
  int32_t displayPosition;
  std::string eventId;
  std::string displayContentCh;
  std::string displayContentTw;
  std::string displayContentEn;
  std::string thumbnailUrl;
};

class SafeWarningBubbleDrawDescriptor : public BubbleDrawDescriptor {
 public:
  SafeWarningBubbleDrawDescriptor();
  ~SafeWarningBubbleDrawDescriptor() override = default;
  bool InitWithJson(const std::string& json);
  bool IsValid() const override;

  friend std::ostream& operator<<(std::ostream& out, const SafeWarningBubbleDrawDescriptor& other);
  bool is_animation{false};  // 是否是帧动画
};
__MAPBIZ_NAMESPACE_END__
