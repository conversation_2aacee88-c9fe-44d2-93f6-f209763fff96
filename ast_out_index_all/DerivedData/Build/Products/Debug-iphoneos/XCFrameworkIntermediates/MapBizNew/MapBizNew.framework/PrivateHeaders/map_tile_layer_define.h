// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/9/21.
//

#pragma once

#include <iostream>
#include <string>

#include "map_biz_common.h"
#include "map_layer_define.h"

__MAPBIZ_NAMESPACE_BEGIN__

/**
 * 创建图层选项
 */
class MapTileLayerOption {
 public:
  explicit MapTileLayerOption(const std::string dataSource);
  MapTileLayerOption& operator=(const MapTileLayerOption& map_tile_layer_option);

 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  friend std::ostream& operator<<(std::ostream& out, const MapTileLayerOption& option);

 public:
  /**
   * 核查并修正配置
   */
  void CheckAndRectifyConfig();

 public:
  const std::string data_source = "";     // 该图层对应的数据源，也是图层的唯一标示，不可重复
  int tile_size = 256;                    // 该图层瓦片的大小(像素单位)
  int refresh_interval = 90;              // 该图层数据刷新间隔(单位秒，默认值90)
  int layer_priority = 0;                 // 该图层的优先级
  int visible_min_scale_level = 8;        // 该图层显示的级别范围,最小值
  int visible_max_scale_level = 30;       // 该图层显示的级别范围,最大值
  bool visible = true;                    // 该图层是否显示(支持先创建，但不立即显示)
  bool clickable = true;                  // 该图层元素是否可以点击(默认可以点击)
  bool avoid_others = true;               // 是否参与避让(默认值true)
  bool avoid_annotation = false;          // 是否避让底图元素(默认值false)
  bool get_detail_by_user = false;        // 点击获取详情是否完全由客户端拉去（默认false）
  bool draw_name_enable = false;          // 是否展示名称(默认值false)
  MarkerNameStyleOption draw_name_style;  // 该图层名称样式配置
};

/**
 * 动态图层元素描述
 */
class MapTileMarkerDescriptor {
 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  friend std::ostream& operator<<(std::ostream& out, const MapTileMarkerDescriptor& marker_descriptor);

  /**
   * 重载相等运算符号
   * @param other
   * @return
   */
  bool operator==(const MapTileMarkerDescriptor& other) const;
  bool operator!=(const MapTileMarkerDescriptor& other) const;

 public:
  std::string uid;                     // 元素唯一标示
  std::string catalog;                 // 元素分类(匹配样式使用)
  std::string name;                    // 该元素名称(可能为空)
  std::string data_source;             // 数据源
  std::string marker_info;             // 元素信息
  mapbase::GeoCoordinateZ coordinate;  // 坐标扎点使用
  int priority;                        // 优先级

  class Pred_Equal {
   public:
    explicit Pred_Equal(const std::shared_ptr<MapTileMarkerDescriptor>& marker_desc) : marker_desc_(marker_desc) {}
    bool operator()(const std::shared_ptr<MapTileMarkerDescriptor>& marker_desc) const {
      return (*marker_desc_) == (*marker_desc);
    }
    const std::shared_ptr<MapTileMarkerDescriptor> marker_desc_;
  };
};

/**
 * 用户点击动态图层Marker时返回
 */
class MapTileMarkerDetail {
 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  friend std::ostream& operator<<(std::ostream& out, const MapTileMarkerDetail& marker_detail);

 public:
  std::string uid;                     // 元素唯一标示
  std::string catalog;                 // 元素分类(匹配样式使用)
  std::string name;                    // 该元素名称(可能为空)
  mapbase::GeoCoordinateZ coordinate;  // 坐标扎点使用
  std::string data_source;             // 数据源
  std::string detail;                  // 详细数据(json结构，内部不用，仅仅在回调时有值)
};
__MAPBIZ_NAMESPACE_END__

