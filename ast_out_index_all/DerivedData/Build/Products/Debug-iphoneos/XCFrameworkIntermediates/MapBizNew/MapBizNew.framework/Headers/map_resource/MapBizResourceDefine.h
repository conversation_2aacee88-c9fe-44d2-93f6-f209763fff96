//
//  MapBizResourceDefine.h
//  MapBiz
//
//  Created by zheng<PERSON><PERSON> on 2021/12/7.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <MapBizNew/map_layer/MapBizMapLayerDefine.h>
#import <MapBaseNew/MapBaseRouteExplain.h>

NS_ASSUME_NONNULL_BEGIN

/**********************************白天资源ID集合********************************************/
// 定位标资源
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_OVERVIEW_IMAGE_DAY;    // 鹰眼图罗盘资源
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_OVERVIEW_IMAGE_DAY;  // 鹰眼图罗盘箭头资源
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_DAY;                 // 定位标中心的方向资源
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_EAST_DAY;              // 定位标罗盘东字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_SOUTH_DAY;             // 定位标罗盘南字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_WEST_DAY;              // 定位标罗盘西字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_NORTH_DAY;             // 定位标罗盘北字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_RING_DAY;              // 定位标罗盘圆环
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_GPS_DAY;             //
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_BACKGROUND_DAY;      // 定位标背景资源（白天）

// 路线相关资源
extern const unsigned int MAPBIZ_OVERVIEW_START_POINT_DAY;  // 鹰眼图起点
extern const unsigned int MAPBIZ_OVERVIEW_END_POINT_DAY;    // 鹰眼图终点
extern const unsigned int MAPBIZ_OVERVIEW_VIA_POINT_DAY;    // 鹰眼图途径点

extern const unsigned int MAPBIZ_ROUTE_START_POINT_DAY;   // 路线起点引导点
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_DAY;     // 路线途径点
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_1_DAY;   // 路线途径点1
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_2_DAY;   // 路线途径点2
extern const unsigned int MAPBIZ_ROUTE_END_POINT_DAY;     // 路线终点引导点
extern const unsigned int MAPBIZ_ROUTE_START_BUBBLE_DAY;  // 路线起点气泡
extern const unsigned int MAPBIZ_ROUTE_END_BUBBLE_DAY;    // 路线终点气泡
extern const unsigned int MAPBIZ_ROUTE_INDOOR_POINT_DAY;  // 楼层切换点

// 导航中红绿灯资源
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_LIGHT_DAY;  // 导航交通灯
// 用户扩展的途径点资源ID(最多支持32个，即+12 到 +44)
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_BASE_DAY;  // 用户扩展途径点资源ID；

// 导航中交通事件资源
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_DEFAULT_DAY;       // 默认交通事件资源
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ACCIDENT_DAY;      // 事故
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_BADWEATHER_DAY;    // 恶劣天气
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CHECK_DAY;         // 检查
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CONGESTION_DAY;    // 拥堵
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_DAY;  // 施工
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CONTROL_DAY;       // 管制
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_DISASTER_DAY;      // 灾害
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_DAY;   // 障碍
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_DAY;   // 封路
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_DAY;    // 山
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_DAY;  // 窄(小路)

// 导航中警示牌资源
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DEFAULT_DAY;                 // 默认资源
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_AlongMountain_DAY;           // 傍山险路 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_AlongMountainEx_DAY;         // 傍山险路 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_UpSteepSlope_DAY;            // 上陡坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_AccidentProneSection_DAY;    // 事故易发路段
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Caution_DAY;                 // 注意危险 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ContinuousDownSlope_DAY;     // 连续下坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Detour_DAY;                  // 左右绕行 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DetourLeft_DAY;              // 左侧绕行 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DetourRight_DAY;             // 右侧绕行 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindCrosswind_DAY;           // 注意横风 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindLeftMerge_DAY;           // 注意左侧合流
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindRightMerge_DAY;          // 注意右侧合流
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_NarrowBridge_DAY;            // 窄桥
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_BothNarrow_DAY;              // 两侧变窄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_LeftNarrow_DAY;              // 左侧变窄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_RightNarrow_DAY;             // 右侧变窄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_NoOvertake_DAY;              // 禁止超车
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_RailwayCrossingManaged_DAY;  // 有人看守铁路道口
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ReverseRoadLeft_DAY;         // 反向弯路（左）!
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ReverseRoadRight_DAY;        // 反向弯路（右）!
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindRock_DAY;                // 注意落石
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindRockEx_DAY;              // 注意落石
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_UnevenRoad_DAY;              // 路面不平
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindChildren_DAY;            // 注意儿童
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_SharpLeftRoad_DAY;           // 向左急弯路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_SharpRightRoad_DAY;          // 向右急弯路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_SlipperyRoad_DAY;            // 易滑
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DownSteepSlope_DAY;          // 下陡坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_TextWarning_DAY;             // 文字性警示标牌，现场为文字提示，且无法归类到国标危险信息标牌中 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_RailwayCrossingWild_DAY;     // 无人看守铁路道口
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_TurnOnLightInTunnel_DAY;     // 隧道开灯
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Village_DAY;                 // 村庄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_WaterRoad_DAY;               // 过水路面  !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ContinuousDetour_DAY;        // 连续弯路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_LaneSlideSlop_DAY;           // 滑坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DirtRoad_DAY;                // 泥石路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_GroundSink_DAY;              // 地面塌陷
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Collapse_DAY;                // 塌方
// extern const unsigned int MAPBIZ_      GUIDANCE_WARNING_OppositeRoad_DAY                =
// GUIDANCE_WARNING_TIP_MASK_DAY;    // 双向交通 ! static unsigned int
// GUIDANCE_WARNING_MindLivestock_DAY = GUIDANCE_WARNING_TIP_MASK_DAY;    // 注意牲畜 !
// extern const unsigned int MAPBIZ_      GUIDANCE_WARNING_DamRoad_DAY                     =
// GUIDANCE_WARNING_TIP_MASK_DAY; // 堤坝路 ! extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DamRoadEx_DAY
// = GUIDANCE_WARNING_TIP_MASK_DAY;    // 堤坝路   ! static unsigned int
// GUIDANCE_WARNING_CamelBackBridge_DAY             = GUIDANCE_WARNING_TIP_MASK_DAY;    // 驼峰桥
// extern const unsigned int MAPBIZ_      GUIDANCE_WARNING_AllowOvertake_DAY               =
// GUIDANCE_WARNING_TIP_MASK_DAY;    // 解除禁止超车
// extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Honking_DAY
// = GUIDANCE_WARNING_TIP_MASK_DAY;    // 鸣喇叭
// extern const unsigned int MAPBIZ_GUIDANCE_WARNING_StopGiveway_DAY
// = GUIDANCE_WARNING_TIP_MASK_DAY;    // 停车让行 !
// static unsigned int
// GUIDANCE_WARNING_EncounterGiveway_DAY = GUIDANCE_WARNING_TIP_MASK_DAY; // 会车让行 !
// extern const unsigned int MAPBIZ_      GUIDANCE_WARNING_SlowdownGiveway_DAY             =
// GUIDANCE_WARNING_TIP_MASK_DAY;    // 减速让行 !
// extern const unsigned int MAPBIZ_GUIDANCE_WARNING_TidalLane_DAY
// = GUIDANCE_WARNING_TIP_MASK_DAY;    // 潮汐车道 !
// static unsigned int
// GUIDANCE_WARNING_ProtrudingRoad_DAY = GUIDANCE_WARNING_TIP_MASK_DAY;    // 路面高凸 !
// extern const unsigned int MAPBIZ_      GUIDANCE_WARNING_SinkingRoad_DAY                 =
// GUIDANCE_WARNING_TIP_MASK_DAY;    // 路面低洼 !

/*********************************夜间资源ID集合********************************************/
// 定位标资源
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_OVERVIEW_IMAGE_NIGHT;    // 鹰眼图罗盘资源
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_OVERVIEW_IMAGE_NIGHT;  // 鹰眼图罗盘箭头资源
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_NIGHT;                 // 定位标中心的方向资源
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_EAST_NIGHT;              // 定位标罗盘东字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_SOUTH_NIGHT;             // 定位标罗盘南字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_WEST_NIGHT;              // 定位标罗盘西字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_NORTH_NIGHT;             // 定位标罗盘北字
extern const unsigned int MAPBIZ_LOCATOR_COMPASS_RING_NIGHT;              // 定位标罗盘圆环
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_GPS_NIGHT;             //
extern const unsigned int MAPBIZ_LOCATOR_INDICATOR_BACKGROUND_NIGHT;      // 定位标背景资源（夜间）

// 路线相关资源
extern const unsigned int MAPBIZ_OVERVIEW_START_POINT_NIGHT;  // 鹰眼图起点
extern const unsigned int MAPBIZ_OVERVIEW_END_POINT_NIGHT;    // 鹰眼图终点
extern const unsigned int MAPBIZ_OVERVIEW_VIA_POINT_NIGHT;    // 鹰眼图途径点
extern const unsigned int MAPBIZ_ROUTE_START_POINT_NIGHT;     // 路线起点引导点
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_NIGHT;       // 路线途径点
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_1_NIGHT;     // 路线途径点1
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_2_NIGHT;     // 路线途径点2
extern const unsigned int MAPBIZ_ROUTE_END_POINT_NIGHT;       // 路线终点引导点
extern const unsigned int MAPBIZ_ROUTE_START_BUBBLE_NIGHT;    // 路线终点气泡
extern const unsigned int MAPBIZ_ROUTE_END_BUBBLE_NIGHT;      // 路线终点气泡
extern const unsigned int MAPBIZ_ROUTE_INDOOR_POINT_NIGHT;    // 楼层切换点

// 导航中红绿灯资源
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_LIGHT_NIGHT;  // 导航交通灯
// 用户扩展的途径点资源ID(最多支持32个，即+12 到 +44)
extern const unsigned int MAPBIZ_ROUTE_VIA_POINT_BASE_NIGHT;  // 用户扩展途径点资源ID；

// 导航中交通事件资源
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_DEFAULT_NIGHT;       // 默认交通事件资源
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ACCIDENT_NIGHT;      // 事故
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_BADWEATHER_NIGHT;    // 恶劣天气
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CHECK_NIGHT;         // 检查
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CONGESTION_NIGHT;    // 拥堵
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CONSTRUCTION_NIGHT;  // 施工
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_CONTROL_NIGHT;       // 管制
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_DISASTER_NIGHT;      // 灾害
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_OBSTRUCTION_NIGHT;   // 障碍
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ROUTE_CLOSE_NIGHT;   // 封路
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ROUTE_GHAT_NIGHT;    // 山
extern const unsigned int MAPBIZ_GUIDANCE_TRAFFIC_EVENT_ROUTE_NARROW_NIGHT;  // 窄(小路)

// 导航中警示牌资源
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DEFAULT_NIGHT;                 // 默认资源
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_AlongMountain_NIGHT;           // 傍山险路 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_AlongMountainEx_NIGHT;         // 傍山险路 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_UpSteepSlope_NIGHT;            // 上陡坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_AccidentProneSection_NIGHT;    // 事故易发路段
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Caution_NIGHT;                 // 注意危险 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ContinuousDownSlope_NIGHT;     // 连续下坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Detour_NIGHT;                  // 左右绕行 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DetourLeft_NIGHT;              // 左侧绕行 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DetourRight_NIGHT;             // 右侧绕行 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindCrosswind_NIGHT;           // 注意横风 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindLeftMerge_NIGHT;           // 注意左侧合流
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindRightMerge_NIGHT;          // 注意右侧合流
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_NarrowBridge_NIGHT;            // 窄桥
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_BothNarrow_NIGHT;              // 两侧变窄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_LeftNarrow_NIGHT;              // 左侧变窄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_RightNarrow_NIGHT;             // 右侧变窄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_NoOvertake_NIGHT;              // 禁止超车
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_RailwayCrossingManaged_NIGHT;  // 有人看守铁路道口
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ReverseRoadLeft_NIGHT;         // 反向弯路（左）!
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ReverseRoadRight_NIGHT;        // 反向弯路（右）!
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindRock_NIGHT;                // 注意落石
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindRockEx_NIGHT;              // 注意落石
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_UnevenRoad_NIGHT;              // 路面不平
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_MindChildren_NIGHT;            // 注意儿童
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_SharpLeftRoad_NIGHT;           // 向左急弯路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_SharpRightRoad_NIGHT;          // 向右急弯路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_SlipperyRoad_NIGHT;            // 易滑
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DownSteepSlope_NIGHT;          // 下陡坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_TextWarning_NIGHT;             // 文字性警示标牌，现场为文字提示，且无法归类到国标危险信息标牌中 !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_RailwayCrossingWild_NIGHT;     // 无人看守铁路道口
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_TurnOnLightInTunnel_NIGHT;     // 隧道开灯
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Village_NIGHT;                 // 村庄
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_WaterRoad_NIGHT;               // 过水路面  !
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_ContinuousDetour_NIGHT;        // 连续弯路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_LaneSlideSlop_NIGHT;           // 滑坡
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_DirtRoad_NIGHT;                // 泥石路
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_GroundSink_NIGHT;              // 地面塌陷
extern const unsigned int MAPBIZ_GUIDANCE_WARNING_Collapse_NIGHT;                // 塌方

#pragma mark - MapBizMapScaleConfigContainer - 资源内容描述符
/**
 * 资源内容描述符
 */
@interface MapBizMapResourceContentDescriptor : NSObject
@property (nonatomic, assign, readonly) BOOL isValid;   // 判断是否有效
@property (nonatomic, assign) unsigned int resourceId;  // 资源唯一标示
@property (nonatomic, assign) unsigned int depth;       // 资源图片深度信息
/**
 * 缩放比例尺
 * 图片一般为像素，为了让图片在不同设备上显示比例尺相同
 * 引擎需要依赖该缩放系数转为dp, 最终显示时 dp * 整体屏幕密度；
 */
@property (nonatomic, assign) float scale;
@property (nonatomic, strong, nullable) UIImage *image;

- (instancetype)initWithImage:(UIImage *)image resourceId:(unsigned int)resourceId;
- (instancetype)initWithImage:(UIImage *)image resourceId:(unsigned int)resourceId scale:(CGFloat)scale;

@end

#pragma mark - MapBizCustomResourceContentDescriptor - 客户定制资源描述符号
/**
 * 客户定制资源描述符号
 * 备注:PNG文件字节流即可
 */
@interface MapBizCustomResourceContentDescriptor : NSObject
@property (nonatomic, assign, readonly) BOOL isValid;  // 判断是否有效
@property (nonatomic, assign, readonly) float scale;   // 返回缩放比例尺
@property (nonatomic, copy, nullable) NSData *buffer;

/**
 * 重置内容
 */
- (void)reset;
@end

#pragma mark - MapBizBubbleDrawDescriptor - 气泡绘制参数
/**
 * 气泡绘制参数
 */
@interface MapBizBubbleDrawDescriptor : NSObject
@property (nonatomic, assign, readonly) BOOL isValid;                           // 判断是否有效
@property (nonatomic, copy, nullable) NSString *textContent;                    // 气泡内容
@property (nonatomic, copy, nullable) NSString *language;                       // 语言类型
@property (nonatomic, assign) TMapBaseBubbleType bubbleType;                    // 气泡种类
@property (nonatomic, assign) short optionalType;                               // 可选附加类型
@property (nonatomic, assign) MapBizMapBubbleDisplayDirection bubbleDirection;  // 显示方向
@property (nonatomic, assign) BOOL dayMode;                                     // 昼夜风格
@property (nonatomic, copy, nullable) NSString *fontName;                       // 字体名称
@property (nonatomic, assign) int fontColor;                                    // 字体颜色
@property (nonatomic, assign) int fontSizeForMainTitle;                         // 主标题字体大小
@property (nonatomic, assign) int fontSizeForSubTitle;                          // 主标题字体大小
@property (nonatomic, assign) float mapScreenDensity;                           // 屏幕密度

/**
 * 重置内容
 */
- (void)reset;  // 重置内容
@end

#pragma mark - MapBizTrafficDrawDescriptor
@interface MapBizTrafficDrawDescriptor : MapBizBubbleDrawDescriptor
@property (nonatomic, copy, nullable) NSString *lightText;  // 诱导下发红绿灯文字

/**
 * 重置内容
 */
- (void)reset;
@end

#pragma mark - MapBizCamera
@interface MapBizCamera : NSObject
@property (nonatomic, assign) TMapBaseGuidanceCameraType type;  // 电子眼类型
@property (nonatomic, assign) int limitSpeed;                   // 限速值，仅限速电子眼、区间测速起点和区间测速终点有效
@end

#pragma mark - MapBizCameraDrawDescriptor
@interface MapBizCameraDrawDescriptor : MapBizBubbleDrawDescriptor
@property (nonatomic, copy, nullable) NSArray<MapBizCamera *> *cameras;  // 电子眼列表
@property (nonatomic, assign) int remainDistance;                        // 电子眼距离当前位置的剩余距离，单位米
@property (nonatomic, copy, nullable) NSString *snapText;                // 电子眼显示文案
@end

#pragma mark - MapBizExplainBubbleDrawDescriptor
@interface MapBizExplainBubbleDrawDescriptor : MapBizBubbleDrawDescriptor
@property (nonatomic, copy, nullable) NSString *uid;                       // bubble id
@property (nonatomic, copy, nullable) NSString *content;                   // 第一行内容
@property (nonatomic, copy, nullable) NSArray<NSString *> *secondContent;  // 第二行内容
@property (nonatomic, assign) int secondType;                              // 第二行显示样式，0标签样式  1竖线分割样式
@property (nonatomic, assign) int sceneType;                               // 200即将加剧 221 即将缓解 222 即将畅通 227即将拥堵 228低速
@property (nonatomic, assign) TMapBaseBubbleType bubbleType2;              // buble 类型 1有文本框 2 纯icon
@end

#pragma mark - MapBizCompanionBubbleDrawDescriptor
@interface MapBizCompanionBubbleDrawDescriptor : MapBizBubbleDrawDescriptor
@property (nonatomic, copy, nullable) NSString *timeDiff;        // bubble id
@property (nonatomic, copy, nullable) NSString *recommendLabel;  // 第一行内容
@property (nonatomic, copy, nullable) NSString *distanceDiff;    // bubble id
@property (nonatomic, copy, nullable) NSString *feeDiff;         // 第一行内容
@property (nonatomic, copy, nullable) NSString *lightDiff;       // 第一行内容
@property (nonatomic, assign) BOOL isRecommended;                // 第二行显示样式，0标签样式  1竖线分割样式
@property (nonatomic, assign) int sceneType;                     // 第二行显示样式，0标签样式  1竖线分割样式
@end

#pragma mark - MapBizDestNameEtaDrawDescriptor
@interface MapBizDestNameEtaDrawDescriptor : MapBizBubbleDrawDescriptor
@property (nonatomic, copy, nullable) NSString *arriveTime;  // 到达时间描述
@property (nonatomic, assign) int destNameStrokeWidth;       // 终点名称描边宽度
@property (nonatomic, assign) int destNameStrokeColor;       // 终点名称描边颜色，0xAARRGGBB
@property (nonatomic, assign) int arriveTextColor;           // 到达时间文字颜色，0xAARRGGBB
@property (nonatomic, assign) int arriveBackgroundColor;     // 到达时间背景颜色，0xAARRGGBB
@end
NS_ASSUME_NONNULL_END
