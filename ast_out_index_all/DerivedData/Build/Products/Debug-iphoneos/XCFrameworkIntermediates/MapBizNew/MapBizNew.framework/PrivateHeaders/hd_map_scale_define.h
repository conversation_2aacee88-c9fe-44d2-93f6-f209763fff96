// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by 李林洋 on 2023/12/19.
//

#pragma once

#include <MapBaseNew/common_const.h>
#include "map_biz_common.h"
__MAPBIZ_NAMESPACE_BEGIN__

class HDBaseScaleHandleConfig {
 public:
  bool operator==(const HDBaseScaleHandleConfig& other) const;
  bool operator!=(const HDBaseScaleHandleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDBaseScaleHandleConfig& scale_config);

 public:
  // 此配置生效的道路等级[start_class,end_class],双闭区间，end_class应大于start_class
  mapbase::OnlineRoadFuncClass start_class = mapbase::OnlineRoadFuncClass::HighWay;
  mapbase::OnlineRoadFuncClass end_class = mapbase::OnlineRoadFuncClass::OtherWay;
  // 此配置生效的最小俯仰角，以及最大俯仰角，当距离目标点非常远的时候使用最大俯仰角，距离目标点越近，俯仰角越小
  float min_skew = 30;
  float max_skew = 65;
  // 此配置生效的最小scale_level到最大的scale_level
  float min_scale_level = 19;
  float max_scale_level = 22;
  // 此策略驶过关键点后，是否要保持当前比例尺俯仰角不变在行驶一段距离。主要有一些比如收费站，可能刚驶过还需要保持一段时间
  float freeze_distance = 0;
  double skew_max_speed = 10;
  double scale_level_max_speed = 2;
};
class HDIntersectionScaleHandleConfig : public HDBaseScaleHandleConfig {};
class HDTollStationScaleHandleConfig : public HDBaseScaleHandleConfig {
 public:
  /**废弃，即将删除*/
  float toll_station_enable_max_distance = 200;
};
class HDRoundaboutScaleHandleConfig : public HDBaseScaleHandleConfig {
 public:
  float enter_distance = 250;
};
class HDDefaultScaleHandleConfig : public HDBaseScaleHandleConfig {};
class HDTunnelScaleHandleConfig : public HDBaseScaleHandleConfig {};
class HDIntersectionScaleConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const HDIntersectionScaleConfig& container);
  std::vector<HDIntersectionScaleHandleConfig> handles;
};
class HDTollStationScaleConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const HDTollStationScaleConfig& container);
  std::vector<HDTollStationScaleHandleConfig> handles;
};
class HDRoundaboutScaleConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const HDRoundaboutScaleConfig& container);
  std::vector<HDRoundaboutScaleHandleConfig> handles;
  bool enabled = true;
};
class HDDefaultScaleConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const HDDefaultScaleConfig& container);
  std::vector<HDDefaultScaleHandleConfig> handles;
};
class HDTunnelScaleConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const HDTunnelScaleConfig& container);
  bool enabled = false;
  std::vector<HDTunnelScaleHandleConfig> handles;
};
class HDAutoScaleConfigContainer {
 public:
  HDAutoScaleConfigContainer();
  friend std::ostream& operator<<(std::ostream& out, const HDAutoScaleConfigContainer& container);

 public:
  /**机动点比例尺策略*/
  HDIntersectionScaleConfig intersection_config;
  /**收费站比例尺策略*/
  HDTollStationScaleConfig toll_station_config;
  /**
   * 环岛比例尺
   */
  HDRoundaboutScaleConfig roundabout_config;
  /**
   * 默认比例尺,默认比例尺目前不支持在min_skew和max_skew之间调整。也不支持在min_scale和max_scale之间调整。目前仅使用max_skew和min_scale
   */
  HDDefaultScaleConfig default_scale_config;

  /**隧道比例尺策略*/
  HDTunnelScaleConfig tunnel_config;
  /**hd下比例尺是否生效*/
  bool enabled = true;
};

__MAPBIZ_NAMESPACE_END__
