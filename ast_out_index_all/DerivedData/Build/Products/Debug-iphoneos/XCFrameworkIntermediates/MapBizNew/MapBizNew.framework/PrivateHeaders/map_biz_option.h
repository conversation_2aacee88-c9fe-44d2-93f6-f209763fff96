// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/5/12.
//

#pragma once

#include <iostream>
#include <string>

#include "map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__

class GlobalConfig {
 public:
  /**
   * 路口面内的group数据，在计算引导面的时候是否要单独请求。
   * 此选项是流量优化选项，默认应该独立请求。但是由于数据那边不太有信心，担心有问题
   * 所以这是一个功能回退选项
   */
  bool cross_group_independent_req = false;
  /**
   * 初始化设置后，不支持动态更改，底图sd下的时候设置，一旦设置，mapbiz将不处理任何hd相关的接口以及策略
   * 减少cpu消耗，切换hd，hd定位等接口均不处理,不支持动态更改
   */
  bool pure_sd = false;

  /**
   * 是否打开运镜配置比例尺，如果打开以后，默认试用配置文件，之前所有的比例尺策略均不生效。
   * 此配置不支持动态更改
   */
  bool enable_magic_camera = true;

  /**
  * 是否打开超视距运镜效果，enable_magic_camera为true的前提下，此配置才生效
  */
  bool enable_wide_angle_camera = false;

  /**
   * 是否打开力导向 overlay
   */
  bool enable_force_overlay = false;
  /**
   * 是否使用默认鹰眼图样式
   */
  bool overview_style_default = true;

  friend std::ostream& operator<<(std::ostream& out, const GlobalConfig& global_config);
};

/**
 * 创建MapBiz选项
 */
class MapBizOption {
 public:
  MapBizOption();

 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  friend std::ostream& operator<<(std::ostream& out, const MapBizOption& map_biz_option);

 public:
  /**
   * 底图引擎具柄
   * @note 在一个MapView中同时包含主图 和 鹰眼图的情况下，这个具柄为主底图的具柄；
   *       在一个MapView中若仅仅存在一个底图，这个具柄就是从MapView中获取的具柄；
   */
  void* main_map_engine_handler_;
  /**
   * 鹰眼图引擎具柄(无鹰眼图时为空)
   */
  void* overview_map_engine_handler_;
  /**
   * 底图配置路径
   * 备注:为支持跨平台路径，请在平台层保证路径以路径分隔符结束
   */
  std::string map_config_path_;
  /**
   * 导航图面资源名称
   */
  std::string map_config_name_;
  /**
   * 导航资源是否启用
   * 备注: 在一些业务端完全通过接口定制资源，不用资源配置文件情况的使用
   *      开启状态：会加载资源文件；关闭状态: 内部不会加载资源,这种情况下需要业务端自己通过资源定制接口传入资源;
   *      内部默认值:开启
   */
  bool map_config_enabled_;
  /**
   * 底图实例名称(多实例区分)
   * 提示:强烈建议客户端设定唯一标示,非功能性依赖，问题调查效率依赖
   */
  std::string map_instance_name_;
  /**
   * 设置语言类型，SDK内部默认简体中文。SDK初始化时设置语言类型
   * 中文简体: zh-cn; 中文繁体: zh-tw; 英文: en;
   */
  std::string language_;
  /**
   * 调试过程中中间数据输出路径(空 将无法输出)
   */
  std::string map_debug_path_;
  /**
   * 动态图层下载URL
   * 备注: 若输入空, 则表示用内部URL
   */
  std::string map_tile_layer_url_;
  /**
   * 是否使用默认车标样式
   */
  bool use_default_locator_;

  GlobalConfig default_config_;

  /**
   * 是否打开运镜配置比例尺，如果打开以后，默认试用配置文件，之前所有的比例尺策略均不生效。
   * 此配置不支持动态更改
   */
  bool enable_magic_camera = false;

  bool enable_wide_angle_camera = false;

  friend std::ostream& operator<<(std::ostream& out, const GlobalConfig& global_config);
};

__MAPBIZ_NAMESPACE_END__

