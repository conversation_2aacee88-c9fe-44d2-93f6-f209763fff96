//
// Created by <PERSON> on 2020-07-20.
//

module maplbs
{
    // 附近搜索请求
    struct LbsSearchNearbyReq
    {
        0 require string type;         //索引名称， 比如：trafficEvent
        1 require string location;     //中心点坐标，格式：116.343307,39.901309
        2 require int radius;          //区域半径，单位：米
        3 optional string keyword;     //关键词过滤，不同词用逗号分隔
        4 optional string filter;      //过滤表达式
        5 optional string order;       //排序表达式
        6 optional int page_index;     //开始页码
        7 optional int page_size;      //每页条数
        8 optional string traceid;     //全链路日志追查ID,一般用UUID生成
        9 optional string spanid;      //全链路日志追踪次ID,一般用时间戳
        10 optional string source;     //来源
        11 optional int map_level;     //map比例尺
        12 optional string output;     //返回数据字段，模板名称，默认default
        13 optional string format;     //返回数据格式，默认Json
        14 optional string lang;       //语言类型
        15 optional string ext;        //预留字段

    };

    // 矩形框搜索请求
    struct LbsSearchRectangleReq
    {
        0 require string type;         //索引名称， 比如：trafficEvent
        1 require string tl_point;     //矩形左上角坐标，格式：116.343307,39.901309
        2 require string br_point;     //矩形右下角坐标，格式：116.343307,39.901309
        3 optional string keyword;     //关键词过滤，不同词用逗号分隔
        4 optional string filter;      //过滤表达式
        5 optional string order;       //排序表达式
        6 optional int page_index;     //开始页码
        7 optional int page_size;      //每页条数
        8 optional string traceid;     //全链路日志追查ID,一般用UUID生成
        9 optional string spanid;      //全链路日志追踪次ID,一般用时间戳
        10 optional string source;     //来源
        11 optional int map_level;     //map比例尺
        12 optional string output;     //返回数据字段，模板名称，默认default
        13 optional string format;     //返回数据格式，默认Json
        14 optional string lang;       //语言类型
        15 optional string ext;        //预留字段
    };

    // 区域搜索请求
    struct LbsSearchRegionReq
    {
        0 require string type;         //索引名称， 比如：trafficEvent
        1 require string region;       //区域名称，逗号分隔：北京市,海淀区 or 130681
        2 optional string keyword;     //关键词过滤，不同词用逗号分隔
        3 optional string filter;      //过滤表达式
        4 optional string order;       //排序表达式
        5 optional int page_index;     //开始页码
        6 optional int page_size;      //每页条数
        7 optional string traceid;     //全链路日志追查ID,一般用UUID生成
        8 optional string spanid;      //全链路日志追踪次ID,一般用时间戳
        9 optional string source;      //来源
        10 optional int map_level;     //map比例尺
        11 optional string output;     //返回数据字段，模板名称，默认default
        12 optional string format;     //返回数据格式，默认Json
        13 optional string lang;       //语言类型
        14 optional string ext;        //预留字段
    };

    // 多边形搜索请求
    struct LbsSearchPolygonReq
    {
        0 require string type;            //索引名称， 比如：trafficEvent
        1 require vector<string> polygon; //多边形点坐标，格式：116.343307,39.901309
        2 optional string keyword;        //关键词过滤，不同词用逗号分隔
        3 optional string filter;         //过滤表达式
        4 optional string order;          //排序表达式
        5 optional int page_index;        //开始页码
        6 optional int page_size;         //每页条数
        7 optional string traceid;        //全链路日志追查ID,一般用UUID生成
        8 optional string spanid;         //全链路日志追踪次ID,一般用时间戳
        9 optional string source;         //来源
        10 optional int map_level;        //map比例尺
        11 optional string output;        //返回数据字段，模板名称，默认default
        12 optional string format;        //返回数据格式，默认Json
        13 optional string lang;          //语言类型
        14 optional string ext;           //预留字段
    };


    // lbs搜索返回结果
    struct LbsSearchRsp
    {
        0 require int ret_code;              // 结果返回码
        1 require string ret_msg;            // 结果返回信息
        2 require string result;             // 返回结果列表
    };


    // 根据唯一id获取内容请求
    struct LbsGetContentReq
    {
        0 require string type;               // 索引名称， 比如：trafficEvent
        1 require string id;                 // 上报事件唯一ID
        2 optional string traceid;           // 全链路日志追查ID,一般用UUID生成
        3 optional string spanid;            // 全链路日志追踪次ID,一般用时间戳
        4 optional string source;            //来源
        5 optional string output;            //返回数据格式，默认JSON
        6 optional string lang;              //语言类型
        7 optional string ext;               //预留字段
    };

    // lbs获取内容返回结果
    struct LbsGetContentRsp
    {
        0 require int ret_code;              // 结果返回码
        1 require string ret_msg;            // 结果返回信息
        2 require string result;             // 返回结果列表
    };
};