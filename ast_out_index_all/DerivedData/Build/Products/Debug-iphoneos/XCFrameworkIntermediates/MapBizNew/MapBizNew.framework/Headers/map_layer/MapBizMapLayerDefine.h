//
//  MapBizMapLayerDefine.h
//  MapBiz
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/2.
//

#ifndef _OC_MAP_BIZ_MAP_LAYER_DEFINE_H_
#define _OC_MAP_BIZ_MAP_LAYER_DEFINE_H_

#import <Foundation/Foundation.h>
#import <CoreGraphics/CGGeometry.h>
#import <MapBaseNew/MapBaseCommonLocation.h>
#import <MapBaseNew/MapBaseCommonDefine.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 比例尺参映射表参考信息
   盘古    缩放scale     比例尺
   18  1           10m
   17  0.5         20m
   16  0.25        50m
   15  0.125       100m
   14  0.0625      200m
   13  0.0375      500m
   12  0.01875     1km
   11  0.009375    2km
   10  0.0046875   5km
   9   0.00234375  10km
   8   0.00117188  20km
   7   0.00058594  50km
   6   0.00029297  100km
   5   0.00014648  200km
   4   0.00007324  500km
   3   0.00003662  1000km
   2   0.00001831  2000km
   1   0.00000916  5000km
 */

// 地图允许显示的最大级别
extern const int MAPBIZ_LAYER_LEVEL_MAX_LEVEL;
// 定位车标图层
extern const int MAPBIZ_LAYER_LEVEL_LOCATOR_MIN;
extern const int MAPBIZ_LAYER_LEVEL_LOCATOR_MAX;
// 路线图层
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_MIN;
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_MAX;
// 电子眼图层 9级(10公里) 到MAX_LEVEL级
extern const int MAPBIZ_LAYER_LEVEL_CAMERA_MIN;
extern const int MAPBIZ_LAYER_LEVEL_CAMERA_MAX;
// 路名气泡图层 10级(5KM) 到MAX_LEVEL级
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_NAME_MIN;
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_NAME_MAX;
// 拥堵气泡图层 9级(10KM) 到MAX_LEVEL级
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_TRAFFIC_MIN;
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_TRAFFIC_MAX;
// 伴随气泡图层 10级(5KM) 到MAX_LEVEL级
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_COMPANION_MIN;
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_COMPANION_MAX;
// Marker图层---路线引导点起点
extern const int MAPBIZ_LAYER_LEVEL_START_CIRCLE_MIN;
extern const int MAPBIZ_LAYER_LEVEL_START_CIRCLE_MAX;
// Marker图层---途径点
extern const int MAPBIZ_LAYER_LEVEL_VIA_CIRCLE_MIN;
extern const int MAPBIZ_LAYER_LEVEL_VIA_CIRCLE_MAX;
// Marker图层---路线引导点终点
extern const int MAPBIZ_LAYER_LEVEL_END_CIRCLE_MIN;
extern const int MAPBIZ_LAYER_LEVEL_END_CIRCLE_MAX;
// Marker图层---起点气泡
extern const int MAPBIZ_LAYER_LEVEL_START_BUBBLE_MIN;
extern const int MAPBIZ_LAYER_LEVEL_START_BUBBLE_MAX;
// Marker图层---终点气泡
extern const int MAPBIZ_LAYER_LEVEL_END_BUBBLE_MIN;
extern const int MAPBIZ_LAYER_LEVEL_END_BUBBLE_MAX;
// Marker图层---红绿灯 12级(1KM) 到MAX_LEVEL级
extern const int MAPBIZ_LAYER_LEVEL_TRAFFIC_LIGHT_MIN;
extern const int MAPBIZ_LAYER_LEVEL_TRAFFIC_LIGHT_MAX;
// Marker图层---警示牌 12级(1KM) 到MAX_LEVEL级
extern const int MAPBIZ_LAYER_LEVEL_WARNING_TIP_MIN;
extern const int MAPBIZ_LAYER_LEVEL_WARNING_TIP_MAX;
// Marker图层---交通事件
extern const int MAPBIZ_LAYER_LEVEL_TRAFFIC_EVENT_MIN;
extern const int MAPBIZ_LAYER_LEVEL_TRAFFIC_EVENT_MAX;
// 巡航marker图层
extern const int MAPBIZ_LAYER_LEVEL_CRUISE_MARKER_MIN;
extern const int MAPBIZ_LAYER_LEVEL_CRUISE_MARKER_MAX;
// 终点名称ETA图层
extern const int MAPBIZ_LAYER_LEVEL_DEST_NAME_ETA_MIN;
extern const int MAPBIZ_LAYER_LEVEL_DEST_NAME_ETA_MAX;
// 路线标签图层
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_LABEL_MIN;
extern const int MAPBIZ_LAYER_LEVEL_ROUTE_LABEL_MAX;

// 定位车标图层
extern const int MAPBIZ_PRIORITY_LOCATOR_MIN;
extern const int MAPBIZ_PRIORITY_LOCATOR_MAX;
// 鱼骨路线图层
extern const int MAPBIZ_PRIORITY_CRUISE_MARKER_MIN;
extern const int MAPBIZ_PRIORITY_CRUISE_MARKER_MAX;
// Marker图层---红绿灯
extern const int MAPBIZ_PRIORITY_TRAFFIC_LIGHT_MIN;
extern const int MAPBIZ_PRIORITY_TRAFFIC_LIGHT_MAX;
// Marker图层---路线引导点起点
extern const int MAPBIZ_PRIORITY_START_CIRCLE_MIN;
extern const int MAPBIZ_PRIORITY_START_CIRCLE_MAX;
// Marker图层---路线引导点终点
extern const int MAPBIZ_PRIORITY_END_CIRCLE_MIN;
extern const int MAPBIZ_PRIORITY_END_CIRCLE_MAX;
// 路线图层
extern const int MAPBIZ_PRIORITY_ROUTE_MIN;
extern const int MAPBIZ_PRIORITY_ROUTE_MAX;
// Marker图层---警示牌
extern const int MAPBIZ_PRIORITY_WARNING_TIP_MIN;
extern const int MAPBIZ_PRIORITY_WARNING_TIP_MAX;
// 伴随气泡图层
extern const int MAPBIZ_PRIORITY_ROUTE_COMPANION_MIN;
extern const int MAPBIZ_PRIORITY_ROUTE_COMPANION_MAX;
// 路名气泡图层
extern const int MAPBIZ_PRIORITY_ROUTE_NAME_MIN;
extern const int MAPBIZ_PRIORITY_ROUTE_NAME_MAX;
// Marker图层---终点气泡
extern const int MAPBIZ_PRIORITY_END_BUBBLE_MIN;
extern const int MAPBIZ_PRIORITY_END_BUBBLE_MAX;
// Marker图层---途径点
extern const int MAPBIZ_PRIORITY_VIA_CIRCLE_MIN;
extern const int MAPBIZ_PRIORITY_VIA_CIRCLE_MAX;
// Marker图层---起点气泡
extern const int MAPBIZ_PRIORITY_START_BUBBLE_MIN;
extern const int MAPBIZ_PRIORITY_START_BUBBLE_MAX;
// 拥堵气泡图层
extern const int MAPBIZ_PRIORITY_ROUTE_TRAFFIC_MIN;
extern const int MAPBIZ_PRIORITY_ROUTE_TRAFFIC_MAX;
// 电子眼图层
extern const int MAPBIZ_PRIORITY_CAMERA_MIN;
extern const int MAPBIZ_PRIORITY_CAMERA_MAX;
// Marker图层---交通事件
extern const int MAPBIZ_PRIORITY_TRAFFIC_EVENT_MIN;
extern const int MAPBIZ_PRIORITY_TRAFFIC_EVENT_MAX;
// 终点名称ETA图层
extern const int MAPBIZ_PRIORITY_DEST_NAME_ETA_MIN;
extern const int MAPBIZ_PRIORITY_DEST_NAME_ETA_MAX;
// 路线标签图层
extern const int MAPBIZ_PRIORITY_ROUTE_LABEL_MIN;
extern const int MAPBIZ_PRIORITY_ROUTE_LABEL_MAX;

extern const int MAPBIZ_STYLE_COLOR_NORMAL_DAY;
extern const int MAPBIZ_STYLE_COLOR_BETTER_DAY;
extern const int MAPBIZ_STYLE_COLOR_NORMAL_NIGHT;
extern const int MAPBIZ_STYLE_COLOR_BETTER_NIGHT;
extern const int MAPBIZ_STYLE_COLOR_DEFAULT;

extern const int MAPBIZ_STYLE_FONT_SIZE_FOR_MAIN_TITLE;
extern const int MAPBIZ_STYLE_FONT_SIZE_FOR_SUB_TITLE;
extern const int MAPBIZ_STYLE_FONT_SIZE_FOR_COMPANION_MAIN_TITLE;
extern const int MAPBIZ_STYLE_FONT_SIZE_FOR_COMPANION_SUB_TITLE;

extern NSString *const MAPBIZ_STYLE_FONT_NAME;

/**
 * 图层类型
 * 按照业务内容定义
 */
typedef NS_ENUM(NSInteger, MapBizLayerType) {
    MapBizLayerTypeNone = -1,           // 无效类型
    MapBizLayerTypeLocator = 0,         // 自车定位图层
    MapBizLayerTypeRoute = 1,           // 蚯蚓线图层    备注: 该图层包含:蚯蚓线/动态路名/转向箭头/终点区域面
    MapBizLayerTypeCamera = 2,          // 电子眼图层
    MapBizLayerTypeRouteName = 3,       // 转向路名气泡图层
    MapBizLayerTypeRouteTraffic = 4,    // 拥堵路况气泡图层
    MapBizLayerTypeRouteCompanion = 5,  // 伴随气泡图层
    MapBizLayerTypeMarker = 6,          // Marker图层 备注:起点/终点/途径点, 红绿灯
    MapBizLayerTypeCruiseMarker = 7,    // 巡航marker图层
    MapBizLayerTypeDestNameEta = 8,     // 终点名称和ETA
    MapBizLayerTypeRouteLabel = 9,      // 路线标签
    MapBizLayerTypeTrajectory = 101,    // 车道级图层
};

/**
 * Marker类型元素子类型
 */
typedef NS_ENUM(NSInteger, MapBizMarkerType) {
    MapBizMarkerTypeNone = -1,            // 无效类型
    MapBizMarkerTypeStartCircle = 0,      // 路线引导点起点
    MapBizMarkerTypeViaCircle = 1,        // 路线途径点
    MapBizMarkerTypeEndCircle = 2,        // 路线引导点终点
    MapBizMarkerTypeStartBubble = 3,      // 起点气泡 备注: 暂时无该类型需求，定义在这里保持完整性
    MapBizMarkerTypeEndBubble = 4,        // 终点气泡 备注: 仅主底图需要，鹰眼图不需要
    MapBizMarkerTypeTrafficLight = 5,     // 导航路线上红绿灯
    MapBizMarkerTypeWarningTip = 6,       // 导航路线上警示牌信息
    MapBizMarkerTypeTrafficEvent = 7,     // 巡航交通点事件
    MapBizMarkerTypeHDWarningTip = 1006,  // 导航路线上警示牌信息
};

/**
 * Icon绘制类型
 */
typedef NS_ENUM(NSInteger, MapBizIconType) {
    MapBizIconType2DGeoCoordGeoAngle,     // 2D Icon, 地理坐标，地理角度(正交绘制于屏幕上)
    MapBizIconType2DGeoCoordScreenAngle,  // 2D Icon, 地理坐标，屏幕角度
    MapBizIconType2DScreenCoord,          // 2D Icon, 类似于屏幕UI
    MapBizIconType3D                      // 3D Icon, 贴于地面
};

typedef NS_ENUM(NSInteger, MapBizAvoidRouteType) {
    MapBizAvoidRouteTypeAll,
    MapBizAvoidRouteTypeMainRoute,
};

#pragma mark - MapBizLayerBaseConfig - 图层基础配置
@interface MapBizLayerBaseConfig : NSObject

@property (nonatomic, assign) MapBizLayerType mainType;             // 主图层类型
@property (nonatomic, assign) int subType;                          // 该图层中元素类型(有些图层内元素耦合中但元素类型不同)
@property (nonatomic, assign) BOOL enabled;                         // 该图层是否启用(false:该图层不做任何数据处理)
@property (nonatomic, assign) BOOL visible;                         // 该图层是否显示(false:该图层进行数据处理但不展示)
@property (nonatomic, assign) BOOL needAvoidOthers;                 // 该图层元素是否参与避让
@property (nonatomic, assign) BOOL needAvoidRoute;                  // 该图层元素是否避让路线
@property (nonatomic, assign) MapBizAvoidRouteType avoidRouteType;  // 路线避让类型，默认仅避让主路线
@property (nonatomic, assign) BOOL isClickable;                     // 该图层元素是否可点击(默认不可点击)
@property (nonatomic, assign) int minDisplayLevel;                  // 该图层元素允许显示的最小比例尺(默认值:10, 大约2公里)
@property (nonatomic, assign) int maxDisplayLevel;                  // 该图层元素允许显示的最大比例尺(默认值:30, 超过了引擎显示的最大范围)
@property (nonatomic, assign) int minDisplayPriority;               // 该图层元素允许被设置的最小优先级()
@property (nonatomic, assign) int maxDisplayPriority;               // 该图层元素允许被设置的最大优先级()
@property (nonatomic, assign) int minMarginWithOthers;              // 该图层Marker与其他Marker的最小间距(默认为0)
@property (nonatomic, assign) BOOL debugEnable;            // 是否启动debug调试 (默认false, 不支持对外，启动debug一直marker绘制调试框会显示出来)
@property (nonatomic, assign) BOOL enableChangeByMapMode;  // 样式是否随昼夜模式切换而切换

- (instancetype)initWithMainType:(MapBizLayerType)mainType subType:(MapBizMarkerType)subType;
/**
 * 判断部分属性是否相同
 * 备注:降低复杂度增加
 * @param other
 * @return   true:相同，false:不相同
 */
- (BOOL)isEqualToLayerBaseConfig:(MapBizLayerBaseConfig *)other;
/**
 * 核查并修正配置
 */
- (void)checkAndRectifyConfig;

- (BOOL)isSameTypeToLayerBaseConfig:(MapBizLayerBaseConfig *)other;

@end

/**
 * 描述气泡或文字显示的方向
 * 备注: 气泡相对扎点的方向(不是尖尖相对图片的方向);
 */
/*********************************/
/*              Top              */
/*  LeftTop           RightTop   */
/*           -     -             */
/*  Left      Center      Right  */
/*            -    -             */
/*  LeftBottom        RightBottom*/
/*              Bottom           */
/*********************************/
typedef NS_ENUM(NSInteger, MapBizMapBubbleDisplayDirection) {
    MapBizMapBubbleDisplayDirectionLeftTop = 0,      // 左上方
    MapBizMapBubbleDisplayDirectionRightTop = 1,     // 右上方
    MapBizMapBubbleDisplayDirectionLeftBottom = 2,   // 左下方
    MapBizMapBubbleDisplayDirectionRightBottom = 3,  // 右下方
    MapBizMapBubbleDisplayDirectionCenter = 4,       // 中心
    MapBizMapBubbleDisplayDirectionLeft = 5,         // 左侧
    MapBizMapBubbleDisplayDirectionTop = 6,          // 上方
    MapBizMapBubbleDisplayDirectionRight = 7,        // 右方
    MapBizMapBubbleDisplayDirectionBottom = 8,       // 下方
};

/**
 * 描述气泡类型
 */
typedef NS_ENUM(NSInteger, MapBizBubbleLayerType) {
    MapBizBubbleLayerTypeNone = -1,           // 非法类型
    MapBizBubbleLayerTypeRouteName = 0,       // 前方路名气泡
    MapBizBubbleLayerTypeRouteTraffic = 1,    // 前方路况气泡
    MapBizBubbleLayerTypeRouteCompanion = 2,  // 伴随路线气泡
    MapBizBubbleLayerTypeRouteCamera = 3,     // 电子眼气泡
    MapBizBubbleLayerTypeDynamicIcon = 4,     // 动态资源
    MapBizBubbleLayerTypeDefaultIcon = 5,     // 默认资源
    MapBizBubbleLayerTypeDestNameEta = 6,     // 终点名称和ETA
    MapBizBubbleLayerTypeRouteLabel = 7,      // 路线标签
    MapBizBubbleLayerTypeExplainBubble = 8    // 拥堵预测（解释性信息）
};

#pragma mark - MapBizBubbleLayerBaseConfig - 底图气泡基础配置
@interface MapBizBubbleLayerBaseConfig : NSObject
@property (nonatomic, assign) MapBizBubbleLayerType type;
@property (nonatomic, copy, readonly) NSString *typeStr;
/**
 * 气泡显示字体名称
 * 默认值为空，空则表示使用系统的默认字体;
 */
@property (nonatomic, copy, nullable) NSString *fontName;
/**
 * 气泡扎点个数(默认4)
 */
@property (nonatomic, assign) int displayCandidatePositionCount;
/**
 * 气泡方式的候选方向(具有优先级的列表)
 * MapBizMapBubbleDisplayDirection
 */
@property (nonatomic, copy, nullable) NSArray<NSNumber *> *displayCandidateDirectionArray;
/**
 * 是否需要需要将扎点结果回调给客户端
 * 备注: 结果通过回调函数发送
 */
@property (nonatomic, assign) BOOL needCallbackCandidatePositions;
/**
 * Camera 变化情况下 重新计算最小时间间隔
 * 性能考虑
 */
@property (nonatomic, class, assign, readonly) int minIntervalWhenCameraChanged;
/**
 * 扎点复用时判断一个点是否在路线上采用的最大距离阀值；
 * 点与路线距离不超过3米都认为还在路线上;
 */
@property (nonatomic, class, assign, readonly) int maxDistancePointOnPolyline;

- (instancetype)initWithType:(MapBizBubbleLayerType)type priority:(int)priority;
- (void)checkAndRectifyConfig;
@end

#pragma mark - MapBizRouteNameBubbleConfig - 前方路名气泡扎点和显示配置
/**
 * 前方路名气泡扎点和显示配置
 * 昼夜模式样式不同，需要重新设置
 */
@interface MapBizRouteNameBubbleConfig : MapBizBubbleLayerBaseConfig
@property (nonatomic, assign) int fontColor;  // 字体颜色
@property (nonatomic, assign) int fontSize;   // 字体大小
@end

#pragma mark - MapBizRouteTrafficBubbleConfig - 前方路况气泡扎点和显示配置
/**
 * 前方路况气泡扎点和显示配置
 */
@interface MapBizRouteTrafficBubbleConfig : MapBizBubbleLayerBaseConfig
@property (nonatomic, assign) int fontColor;  // 字体颜色
@property (nonatomic, assign) int fontSize;   // 字体大小
@end

#pragma mark - MapBizRouteCompanionBubbleConfig - 伴随气泡扎点和显示配置
/**
 * 前方路况气泡扎点和显示配置
 */
@interface MapBizRouteCompanionBubbleConfig : MapBizBubbleLayerBaseConfig
@property (nonatomic, assign) int fontColorForNormalDay;                        // 普通推荐颜色
@property (nonatomic, assign) int fontColorForBetterDay;                        // 强烈推荐颜色
@property (nonatomic, assign) int fontColorForNormalNight;                      // 普通推荐颜色
@property (nonatomic, assign) int fontColorForBetterNight;                      // 强烈推荐颜色
@property (nonatomic, assign) int fontSizeForMainTitle;                         // 主标题字体大小
@property (nonatomic, assign) int fontSizeForSubTitle;                          // 副标题字体大小
@property (nonatomic, class, assign, readonly) int thinkTimeSameThreshold;      // 差距秒差距内认为 时间相近(默认值 60 * 2)
@property (nonatomic, class, assign, readonly) int thinkDistanceSameThreshold;  // 差距米差距内认为 距离接近(默认值 100)
@end

#pragma mark - MapBizRouteCameraBubbleConfig - 电子眼扎点和显示配置
/**
 * 电子眼扎点和显示配置
 */
@interface MapBizRouteCameraBubbleConfig : MapBizBubbleLayerBaseConfig
/**
 * 相邻的小电子眼之间显示最小间隔，单位:屏幕像素
 * 默认值:20
 */
@property (nonatomic, assign) int smallCameraMargin;
/**
 * 小电子眼的显示scale
 * 默认值:0.65f
 */
@property (nonatomic, assign) float smallCameraScale;
/**
 * 大ICON：最多显示2个位置(如果有区间测速，最多4个)
 * 默认值:2个;
 */
@property (nonatomic, assign) int maxPositionCountForLargeCamera;
/**
 * 大ICON：同一位置最多显示2个电子眼
 * 默认值:2个
 */
@property (nonatomic, assign) int maxCameraCountAtSamePosition;
/**
 * 小ICON最多显示的个数
 * 默认值:2个
 */
@property (nonatomic, assign) int maxNumForSmallCamera;
/**
 * 显示大ICON电子眼的距离，700M
 * 单位:米
 * 默认值:700;
 */
@property (nonatomic, assign) int distanceForShowLargeCamera;
/**
 * 显示大ICON电子眼的距离+buffer=700M+100M,
 * 单位:米
 * 默认值:800;
 */
@property (nonatomic, assign) int distanceForShowLargeCameraWithBuffer;
/**
 * 显示小ICON电子眼的距离，2公里
 * 单位:米
 * 默认值:2000;
 */
@property (nonatomic, assign) int distanceForShowCamera;
/**
 * 电子眼位置距离不超过此值，视为同一位置
 * 单位:米
 * 默认值: 50;
 */
@property (nonatomic, assign) int distanceForSamePositionCamera;
@end

#pragma mark - MapBizDestNameEtaBubbleConfig - 终点名称ETA气泡配置
/**
 * 终点名称ETA气泡配置
 */
@interface MapBizDestNameEtaBubbleConfig : MapBizBubbleLayerBaseConfig
/**
 * 终点名称文字大小
 */
@property (nonatomic, assign) int destNameTextSize;
/**
 * 终点名称文字颜色白天，0xAARRGGBB
 */
@property (nonatomic, assign) int destNameTextColorDay;
/**
 * 终点名称文字颜色黑夜，0xAARRGGBB
 */
@property (nonatomic, assign) int destNameTextColorNight;
/**
 * 终点名称描边宽度
 */
@property (nonatomic, assign) int destNameStrokeWidth;
/**
 * 终点名称描边颜色白天，0xAARRGGBB
 */
@property (nonatomic, assign) int destNameStrokeColorDay;
/**
 * 终点名称描边颜色黑夜，0xAARRGGBB
 */
@property (nonatomic, assign) int destNameStrokeColorNight;
/**
 * 到达时间文字大小
 */
@property (nonatomic, assign) int arriveTextSize;
/**
 * 到达时间文字颜色白天，0xAARRGGBB
 */
@property (nonatomic, assign) int arriveTextColorDay;
/**
 * 到达时间文字颜色黑夜，0xAARRGGBB
 */
@property (nonatomic, assign) int arriveTextColorNight;
/**
 * 到达时间背景颜色白天，0xAARRGGBB
 */
@property (nonatomic, assign) int arriveBackgroundColorDay;
/**
 * 到达时间背景颜色黑夜，0xAARRGGBB
 */
@property (nonatomic, assign) int arriveBackgroundColorNight;
@end

#pragma mark - MapBizRouteLabelBubbleConfig - 路线标签气泡配置
/**
 * 路线标签气泡配置
 */
@interface MapBizRouteLabelBubbleConfig : MapBizBubbleLayerBaseConfig
/**
 * 白天字体颜色
 */
@property (nonatomic, assign) int fontColorDay;
/**
 * 夜间字体颜色
 */
@property (nonatomic, assign) int fontColorNight;
/**
 * 字体大小
 */
@property (nonatomic, assign) int fontSize;

@end

#pragma mark - MapBizBubbleLayerConfigContainer - 图层显示策略参数
/**
 * 图层显示策略参数
 */
@interface MapBizBubbleLayerConfigContainer : NSObject
/**
 * 路名气泡策略配置
 */
@property (nonatomic, strong) MapBizRouteNameBubbleConfig *routeNameBubbleConfig;
/**
 * 拥堵气泡策略配置
 */
@property (nonatomic, strong) MapBizRouteTrafficBubbleConfig *routeTrafficBubbleConfig;
/**
 * 伴随气泡策略配置
 */
@property (nonatomic, strong) MapBizRouteCompanionBubbleConfig *routeCompanionBubbleConfig;
/**
 * 电子眼气泡策略
 */
@property (nonatomic, strong) MapBizRouteCameraBubbleConfig *routeCameraBubbleConfig;
/**
 * 终点名称ETA气泡策略配置
 */
@property (nonatomic, strong) MapBizDestNameEtaBubbleConfig *destNameEtaBubbleConfig;
/**
 * 路线标签气泡配置
 */
@property (nonatomic, strong) MapBizRouteLabelBubbleConfig *routeLabelBubbleConfig;

@end

#pragma mark - MapBizBubbleCandidatePositionDescriptor - 气泡扎点位置信息
/**
 * 图层显示策略参数
 */
@interface MapBizBubbleCandidatePositionDescriptor : NSObject
@property (nonatomic, copy, nullable) NSString *routeId;
@property (nonatomic, copy, nullable) NSArray<TMapBaseGeoCoordinate *> *candidatePositions;

- (void)reset;
@end

#pragma mark - MapBizBubbleCandidatePositionContainer - 候选位置容器列表
/**
 * 候选位置容器列表
 */
@interface MapBizBubbleCandidatePositionContainer : NSObject
/**
 * 所有路线的候选位置信息
 */
@property (nonatomic, copy, nullable) NSArray<MapBizBubbleCandidatePositionDescriptor *> *candidatePositionDescriptor;
@end

#pragma mark - MapBizLocatorLayerConfig - 定位图层配置
/**
 * 定位图层配置
 */
@interface MapBizLocatorLayerConfig : MapBizLayerBaseConfig
@property (nonatomic, assign) int mapScaleLevelForOverviewFollow;  // 鹰眼图在Follow模式下的显示级别(默认18)
@property (nonatomic, assign) BOOL enableOverviewMapAnimation;     // 是否允许鹰眼图动画
@property (nonatomic, assign) BOOL headingUpForOverviewFollow;     // 鹰眼图follow模式是否车头向上
@property (nonatomic, assign) float maxSkew;                       // 车标的最大俯仰角，单位度，默认使用底图俯仰角设置
@end

#pragma mark - MapBizRouteLayerConfig - 路线图层配置
/**
 * 路线图层配置
 */
@interface MapBizRouteLayerConfig : MapBizLayerBaseConfig
@property (nonatomic, assign) BOOL needShowSecondTurnArrow;  // 是否显示连续白箭头
@end

#pragma mark - MapBizCameraLayerConfig - 电子眼图层配置
/**
 * 电子眼图层配置
 */
@interface MapBizCameraLayerConfig : MapBizLayerBaseConfig
@property (nonatomic, assign) BOOL needEnlargeCamera;           // 符合策略放大的电子眼是否需要放大
@property (nonatomic, assign) BOOL needEnlargeAnimation;        // 符合策略放大的电子眼是否需要放大
@property (nonatomic, assign) BOOL showRemainDistanceForLarge;  // 符合策略放大的电子眼是否需要放大
@end

#pragma mark - MapBizRouteNameLayerConfig - 路名气泡图层配置
/**
 * 电子眼图层配置
 */
@interface MapBizRouteNameLayerConfig : MapBizLayerBaseConfig
@end

#pragma mark - MapBizRouteTrafficLayerConfig - 拥堵气泡图层配置
/**
 * 拥堵气泡图层配置
 */
@interface MapBizRouteTrafficLayerConfig : MapBizLayerBaseConfig
@end

#pragma mark - MapBizRouteCompanionLayerConfig - 伴随气泡图层配置
/**
 * 伴随气泡图层配置
 */
@interface MapBizRouteCompanionLayerConfig : MapBizLayerBaseConfig
/**
 * 伴随气泡当前显示的场景类型，默认值为0
 * 该字段主要用于客户端在切换场景后，能再次收到气泡内容回调；
 */
@property (nonatomic, assign) int sceneType;
@end

#pragma mark - MapBizDestNameEtaLayerConfig - 终点名称和ETA气泡图层配置
/**
 * 终点名称和ETA气泡图层配置
 */
@interface MapBizDestNameEtaLayerConfig : MapBizLayerBaseConfig
/**
 * 伴随气泡当前显示的场景类型，默认值为0
 * 该字段主要用于客户端在切换场景后，能再次收到气泡内容回调；
 */
@property (nonatomic, assign) BOOL showArriveTime;
@end

#pragma mark - MapBizRouteLabelLayerConfig - 路线标签气泡图层配置
/**
 * 路线标签气泡图层配置
 */
@interface MapBizRouteLabelLayerConfig : MapBizLayerBaseConfig
@end

#pragma mark - MapBizMarkerLayerConfig - Marker图层配置
/**
 * Marker图层配置
 * 所有Marker图层复用该结构
 */
@interface MapBizMarkerLayerConfig : MapBizLayerBaseConfig
@property (nonatomic, assign) BOOL needShowName;  // 是否显示Marker的名称(如果有名称的话)

- (instancetype)initWithMarkerType:(MapBizMarkerType)markerType;
@end

#pragma mark - MapBizCruiseLayerConfig - 巡航图层配置
/**
 * 巡航图层配置
 */
@interface MapBizCruiseLayerConfig : MapBizLayerBaseConfig
@end

#pragma mark - MapBizTrajectoryLayerConfig
@interface MapBizTrajectoryLayerConfig : MapBizLayerBaseConfig
@end

#pragma mark - MapBizOverviewMapConfig - 鹰眼图的配置信息
/**
 * 鹰眼图的配置信息
 */
@interface MapBizOverviewMapConfig : NSObject
/**
 * 鹰眼图全览状态是否显示路线流向箭头
 * 默认值:false
 */
@property (nonatomic, assign) BOOL routeFlowArrowOverlookVisible;
/**
 * 鹰眼图跟随状态是否显示路线流向箭头
 * 默认值:true
 */
@property (nonatomic, assign) BOOL routeFlowArrowFollowedVisible;
@end

#pragma mark - MapBizMarkerSize - Marker大小
/**
 * Marker大小
 */
@interface MapBizMarkerSize : NSObject
/**
 * maker宽，单位像素
 */
@property (nonatomic, assign) int width;
/**
 * marker高，单位像素
 */
@property (nonatomic, assign) int height;
@end

#pragma mark - MapBizOverLookParam - 全览配置信息
/**
 * 全览配置信息
 */
@interface MapBizOverLookParam : NSObject
/**
 * 路线横向时屏幕矩形
 */
@property (nonatomic, assign) CGRect screenRectForHorizontalRoute;
/**
 * 路线纵向时屏幕矩形
 */
@property (nonatomic, assign) CGRect screenRectForVerticalRoute;
/**
 * 其他路线形态时屏幕矩形
 */
@property (nonatomic, assign) CGRect screenRectForOther;
/**
 * 横向路线宽高比阈值，大于等于为横向
 */
@property (nonatomic, assign) float widthHeightRatioThresholdForHorizontalRoute;
/**
 * 纵向路线宽高比阈值，小于等于为纵向
 */
@property (nonatomic, assign) float widthHeightRatioThresholdForVerticalRoute;
/**
 * 车标宽高
 */
@property (nonatomic, copy, nullable) MapBizMarkerSize *locatorSize;
/**
 * 用户调用地图接口加入的地理显示元素，这些元素想显示在地图中
 */
@property (nonatomic, copy, nullable) NSArray<TMapBaseRect *> *additionalGeoBounds;
/**
 * 路线id，若列表为空，返回内部所有路线构成的最大外界框，注意路线边界框计算不包含已隐藏路线
 */
@property (nonatomic, copy, nullable) NSArray<NSString *> *routeIds;
/**
 * 使用完整路线还是剩余路线
 */
@property (nonatomic, assign) BOOL wholeRoute;
@end

#pragma mark - MapBizColorRouteStyleOption - 蚯蚓线纹理定制样式选项集合
/**
 * 蚯蚓线纹理定制样式选项集合
 */
@interface MapBizColorRouteStyleOption : NSObject
/**
 * 白天模式主路线的纹理资源名称
 */
@property (nonatomic, copy, nullable) NSString *routeLineTextureMainDay;
/**
 * 夜间模式主路线的纹理资源名称
 */

@property (nonatomic, copy, nullable) NSString *routeLineTextureMainNight;
/**
 * 白天模式伴随路线的纹理资源资源名称
 */
@property (nonatomic, copy, nullable) NSString *routeLineTextureAssistDay;
/**
 * 夜间模式伴随路线的纹理资源名称
 */
@property (nonatomic, copy, nullable) NSString *routeLineTextureAssistNight;
/**
 * 白天模式主路线的流向箭头的纹理资源名称； 空则使用默认资源
 */
@property (nonatomic, copy, nullable) NSString *routeArrowTextureMainDay;
/**
 * 夜间模式主路线的流向箭头的纹理资源名称； 空则使用默认资源
 */
@property (nonatomic, copy, nullable) NSString *routeArrowTextureMainNight;
/**
 * 白天模式伴随路线的流向箭头的纹理资源名称； 空则使用默认资源, 伴随路线默认不显示箭头
 */
@property (nonatomic, copy, nullable) NSString *routeArrowTextureAssistDay;
/**
 * 夜间模式伴随路线的流向箭头的纹理资源名称； 空则使用默认资源, 伴随路线默认不显示箭头
 */
@property (nonatomic, copy, nullable) NSString *routeArrowTextureAssistNight;
/**
 * 路线上交通流向箭头是否显示
 * 默认值:true
 */
@property (nonatomic, assign) BOOL routeFlowArrowVisible;
/**
 * 路线上交通流向箭头间隔(单位dp, <=0 使用内部默认值)
 */
@property (nonatomic, assign) float routeFlowArrowSpacing;

- (void)reset;
@end

#pragma mark - MapBizRouteTextureStyleAtScale - 高级蚯蚓线纹理样式定制参数，支持到不同比例尺调整宽度和纹理样式
/**
 * 高级蚯蚓线纹理样式定制参数，支持到不同比例尺调整宽度和纹理样式
 */
@interface MapBizRouteTextureStyleAtScale : NSObject
@property (nonatomic, assign) float startScale;             // 开始比例尺
@property (nonatomic, assign) float endScale;               // 结束比例尺
@property (nonatomic, assign) float width;                  // 路线宽度:(0,128], 单位为dp, 不需要乘density
@property (nonatomic, copy, nullable) NSString *imageName;  // 路线纹理名称
@end

#pragma mark - MapBizRouteNameStyleAtScale - 高级蚯蚓线名称样式定制，支持到不通过比例尺调整文字大小
/**
 * 高级蚯蚓线名称样式定制，支持到不通过比例尺调整文字大小
 */
@interface MapBizRouteNameStyleAtScale : NSObject
@property (nonatomic, assign) float startScale;     // 开始生效的显示级别
@property (nonatomic, assign) float endScale;       // 结束生效的显示级别
@property (nonatomic, assign) int textColor;        // 文字颜色
@property (nonatomic, assign) int textBorderColor;  // 文字描边颜色
@property (nonatomic, assign) int fontSize;         // 字体大小,有效范围8-20,单位为dp
@end

#pragma mark - MapBizColorRouteStyleOptionEx - 高级蚯蚓线纹理定制样式选项集合
/**
 * 高级蚯蚓线纹理定制样式选项集合
 */
@interface MapBizColorRouteStyleOptionEx : NSObject
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesMainDay;       // 主底图白天模式主路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesMainNight;     // 主底图夜间模式主路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesAssistDay;     // 主底图白天模式伴随路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesAssistNight;   // 主底图夜间模式伴随路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesMainDay2;      // 鹰眼图白天模式主路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesMainNight2;    // 鹰眼图夜间模式主路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesAssistDay2;    // 鹰眼图白天模式伴随路线样式信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteTextureStyleAtScale *> *routeLineTexturesAssistNight2;  // 鹰眼图夜间模式伴随路线样式信息

@property (nonatomic, copy, nullable) NSArray<MapBizRouteNameStyleAtScale *> *routeTextStylesMainDay;      // 主底图白天模式主路线文字信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteNameStyleAtScale *> *routeTextStylesMainNight;    // 主底图夜间模式主路线文字信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteNameStyleAtScale *> *routeTextStylesAssistDay;    // 主底图白天模式伴随路线文字信息
@property (nonatomic, copy, nullable) NSArray<MapBizRouteNameStyleAtScale *> *routeTextStylesAssistNight;  // 主底图夜间模式伴随路线文字信息
@end

#pragma mark - MapBizTurnArrowStyleOption - 蚯蚓线转向箭头定制样式选项
/**
 * 蚯蚓线转向箭头定制样式选项
 */
@interface MapBizTurnArrowStyleOption : NSObject
@property (nonatomic, assign) BOOL visible;        // 是否显示白箭头，默认值显示
@property (nonatomic, assign) int roofColorDay;    // 白天模式下顶部颜色, 格式:ARGB
@property (nonatomic, assign) int edgeColorDay;    // 夜间模式下边颜色, 格式:ARGB
@property (nonatomic, assign) int wallColorDay;    // 白天模式下外侧颜色, 格式:ARGB
@property (nonatomic, assign) int roofColorNight;  // 夜间模式下顶部颜色, 格式:ARGB
@property (nonatomic, assign) int edgeColorNight;  // 夜间模式下边颜色, 格式:ARGB
@property (nonatomic, assign) int wallColorNight;  // 夜间模式下外侧颜色, 格式:ARGB
@property (nonatomic, assign) float widthScale;    // 箭头宽度缩放系数，范围【0.0f, 1.0f] 默认1，与蚯蚓线宽度一致
@property (nonatomic, assign) float heightScale;   // 箭头高度缩放系数，范围【0.0f, 1.0f] 默认1
@end

#pragma mark - MapBizRouteExplainConfig - 路线解释性配置
/**
 * 路线解释性配置
 */
@interface MapBizRouteExplainConfig : NSObject
@property (nonatomic, assign) BOOL explainIndependentLineVisible;  // 设置路线解释性单独线是否可见
@property (nonatomic, assign) BOOL explainIndependentAreaVisible;  // 设置路线解释性单独面是否可见
@property (nonatomic, assign) BOOL explainBubbleVisible;           // 设置路线解释性bubble是否可见
@end

#pragma mark - MapBizMarkerNameStyleOption - Marker图层名称样式集合
/**
 * Marker图层名称样式集合
 */
@interface MapBizMarkerNameStyleOption : NSObject
/**
 * 显示样式
 * (bit:0) for underline; (bit:1) for bold; (bit:2) for outline or halo; (bit:3) for background; (bit:4) for textonicon; other bits is unused now;
 */
@property (nonatomic, assign) int effectDay;     // 显示样式
@property (nonatomic, assign) int fontSizeDay;   // 字体大小
@property (nonatomic, assign) int haloSizeDay;   // 描边大小
@property (nonatomic, assign) int textColorDay;  // 文字颜色
@property (nonatomic, assign) int haloColorDay;  // 描边颜色
@property (nonatomic, assign) int textSpaceDay;
@property (nonatomic, assign) int iconSpaceDay;
@property (nonatomic, copy, nullable) NSArray<NSNumber *> *candidateDirectionsDay;  // MapBizMapBubbleDisplayDirection

@property (nonatomic, assign) int effectNight;     // 显示样式
@property (nonatomic, assign) int fontSizeNight;   // 字体大小
@property (nonatomic, assign) int haloSizeNight;   // 描边大小
@property (nonatomic, assign) int textColorNight;  // 文字颜色
@property (nonatomic, assign) int haloColorNight;  // 描边颜色
@property (nonatomic, assign) int textSpaceNight;
@property (nonatomic, assign) int iconSpaceNight;
@property (nonatomic, copy, nullable) NSArray<NSNumber *> *candidateDirectionsNight;  //  MapBizMapBubbleDisplayDirection
@end

NS_ASSUME_NONNULL_END

#endif
