// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/7/23.
//

#pragma once

#include "map_biz_resource_define.h"
#include "map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__

/**
 * 气泡资源回调
 */
class MapBizResourceCallBack {
 public:
  /**
   * 气泡资源回调加载
   * @param bubble_draw_descriptor
   * @return
   */
  virtual MapResourceContentDescriptor OnLoadBubbleResource(
      std::shared_ptr<BubbleDrawDescriptor> bubble_draw_descriptor) = 0;

  virtual std::vector<AnimationResource> OnAnimationResource(
      std::vector<std::shared_ptr<BubbleDrawDescriptor>> bubble_draw_descriptors) = 0;
};

__MAPBIZ_NAMESPACE_END__
