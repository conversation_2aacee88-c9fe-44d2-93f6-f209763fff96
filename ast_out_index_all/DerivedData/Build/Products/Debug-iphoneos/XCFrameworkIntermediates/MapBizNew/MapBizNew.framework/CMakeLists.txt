cmake_minimum_required(VERSION 3.6)
include_directories(${CMAKE_CURRENT_LIST_DIR})
include_directories(${PROJECT_ROOT}/include)
include_directories(${THIRD_PARTY_DIR}/libpng)
include_directories(${VENDOR_DIR}/HDDataProcessor/include)
include_directories(${VENDOR_DIR}/HDDataProcessor/)
include_directories(${VENDOR_DIR}/map-base/include)
include_directories(${VENDOR_DIR}/map-base/include/common)
include_directories(${VENDOR_DIR}/nerd-api/include)
include_directories(${VENDOR_DIR}/plog/include)

include_directories(${THIRD_PARTY_DIR}/jsoncpp/include)
add_subdirectory(${THIRD_PARTY_DIR}/jsoncpp/src/lib_json ${CMAKE_BINARY_DIR}/jsoncpp)

set(MAPBIZ_SRC
        map_biz_context.cpp
        map_biz_hd_data_processor.cc
        map_biz_manager_hd_impl.cc
        map_biz_manager_impl.cc
        map_biz_monitor.cc
        map_biz_monitor.h
        map_biz_version.cc
        config_context_impl.cc

        map_comm/map_biz_internal_structures_define.cc
        map_comm/location_update_listener.cc
        map_comm/map_biz_common.cc
        map_comm/map_biz_compile_define.cc
        map_comm/biz_data/map_biz_guide_area.cc
        map_comm/biz_data/map_biz_recommend_lane.cc
        map_comm/biz_data/map_biz_indoor.cc
        map_comm/biz_data/map_biz_language_dictionary.cc
        map_comm/biz_data/forbidden_road_sign_data_wrapper.cc
        map_comm/biz_data/equal_width_guide_area_data_wrapper.cc
        map_comm/map_biz_map_observer.cc
        map_comm/biz_data/map_biz_map_status.cc
        map_comm/map_biz_option.cc
        map_comm/biz_data/map_biz_route.cc
        map_comm/map_event_listener_mgr.cc
        map_comm/biz_data/map_biz_hd_intersection_point.cc
        map_comm/biz_data/map_biz_hd_security_alert.cc
        map_comm/biz_data/map_biz_rtt_event.cc
        map_comm/sd_route_event_processor.h
        map_comm/sd_route_event_processor.cc

        map_layer/hd/base/base_hd_async_mapping.cc
        map_layer/base/bubble_builder.cc
        map_layer/base/bubble_layer.cc
        map_layer/base/layer.cc
        map_layer/base/layer_default_config.cpp
        map_layer/base/map_layer_config_define.cpp
        map_layer/base/map_layer_config_parse.cpp
        map_layer/hd/base/security_alert_base_layer.cc
        map_layer/map_biz_debug_layer.cc
        map_layer/sd/bubble/bubble_layer_dest_name_eta.cc
        map_layer/sd/bubble/bubble_layer_indoor_entries.cc
        map_layer/sd/bubble/bubble_layer_route_companion.cc
        map_layer/sd/bubble/bubble_layer_route_label.cc
        map_layer/sd/bubble/bubble_layer_route_traffic.cc
        map_layer/sd/bubble/bubble_layer_light_count_donwn_timer.cc
        map_layer/sd/bubble/bubble_layer_light_wait.cc
        map_layer/sd/bubble/camera_layer.cc
        map_layer/sd/bubble/bubble_layer_underground_park.cc
        map_layer/cruise/cruise_fish_bone_layer.cc
        map_layer/cruise/cruise_camera_layer.cc
        map_layer/cruise/cruise_map_layer_manager.cc
        map_layer/cruise/cruise_traffic_light_layer.cc
        map_layer/cruise/cruise_hd_traffic_bubble_layer.cc
        map_layer/sd/locator/locator_free_layer.cc
        map_layer/sd/locator/location_layer.cc
        map_layer/sd/map_layer_define.cc
        map_layer/map_layer_manager_wrap.cc
        map_layer/sd/map_layer_manager.cc
        map_layer/sd/marker/marker_layer.cc
        map_layer/sd/marker/marker_layer_via_circle.cc
        map_layer/sd/route/route_segment_manager.cc
        map_layer/sd/route/route_layer.cc
        map_layer/sd/route/route_explain_layer.cc
        map_layer/sd/route/route_line_layer.cc
        map_layer/sd/start_aoi_layer.cc
        map_layer/sd/bubble/bubble_layer_safe_warning.h
        map_layer/sd/bubble/bubble_layer_safe_warning.cc
        map_layer/via_circle_manager.cc
        map_layer/safety_camera/road_safety_camera_define.cc

        map_layer/hd/bubble/bubble_layer_light_wait_hd.cc
        map_layer/hd/bubble/bubble_layer_light_count_down_timer_hd.cc
        map_layer/hd/bubble/bubble_layer_long_solid_hd.cc
        map_layer/hd/bubble/bubble_layer_route_traffic_hd.cc
        map_layer/hd/bubble/bubble_layer_turn_arrow.cc
        map_layer/hd/route/guide_area_layer.cc
        map_layer/hd/route/equal_width_guide_area_layer.cc
        map_layer/hd/route/center_flow_arrow.cc
        map_layer/hd/hd_map_layer_manager.cc
        map_layer/hd/bubble/hd_camera_layer.cc
        map_layer/hd/bubble/bubble_layer_dest_name_eta_hd.cc
        map_layer/hd/bubble/bubble_layer_dest_name_eta_hd.h
        map_layer/hd/bubble/turn_arrow_interface.cc
        map_layer/hd/bubble/turn_arrow_interface.h
        map_layer/hd/marker/hd_marker_mapping.h
        map_layer/hd/marker/hd_marker_mapping.cc

        map_layer/hd/bubble/force/bubble_layer_light_count_down_timer_hd_force.cc
        map_layer/hd/bubble/force/bubble_layer_route_companion_hd_force.cc
        map_layer/hd/bubble/force/bubble_layer_safe_warning_force.cc
        map_layer/hd/bubble/force/bubble_layer_turn_arrow_force.cc
        map_layer/hd/bubble/force/bubble_layer_route_traffic_hd_force.cc
        map_layer/hd/bubble/force/bubble_layer_light_wait_hd_force.cc
        map_layer/hd/bubble/force/hd_camera_layer_force.cc
        map_layer/hd/bubble/force/bubble_layer_long_solid_hd_force.cc
        map_layer/hd/bubble/force/bubble_layer_yaw_notice_force.cc
        map_layer/hd/bubble/force/bubble_layer_route_accident_force.cc

        map_layer/hd/route/guide_line_layer.cc
        map_layer/hd/route/hd_companion_area_layer.cc
        map_layer/hd/route/hd_route_traffic_helper.cc
        map_layer/hd/marker/hd_end_marker_layer.cpp
        map_layer/hd/marker/hd_marker_layer.cc
        map_layer/hd/marker/hd_marker_layer_via_circle.cc
        map_layer/hd/marker/hd_traffic_light_layer.cc
        map_layer/hd/special_lane/lane_text_layer.cc
        map_layer/hd/special_lane/lane_edge_layer.cpp
        map_layer/hd/special_lane/special_lane_highlight_layer.cc
        map_layer/hd/special_lane/special_lane_layer_mgr.cc
        map_layer/hd/trajectory_layer.cc
        map_layer/hd/hd_obstacle_layer.cc
        map_layer/hd/route/guide_line_animation.cpp
        map_layer/hd/base/base_area_layer.cc
        map_layer/hd/route/route_line_area_layer.cc
        map_layer/hd/bubble/bubble_layer_route_companion_hd.cc
        map_layer/hd/route/hd_tbt_arrow_layer.cc
        map_layer/hd/lane_gradual.cc
        map_layer/hd/locator/hd_location_layer.cc
        map_layer/sd/locator/pedestrian_vehicle_sep_layer.cc
        map_layer/hd/route/hd_recommend_lane_layer.cc
        map_layer/hd/hd_forbidden_road_sign_layer.cc
        map_layer/hd/base/text_display.cc
        map_layer/hd/special_lane/security_alert_layer.cc
        map_layer/hd/special_lane/security_alert_right_layer.cc
        map_layer/hd/special_lane/rtt_event_layer.cc
        map_layer/hd/zebra_warning_area_layer.h
        map_layer/hd/zebra_warning_area_layer.cc
        net/map_biz_http_impl.cc

        map_resource/map_biz_resource_define.cc
        map_resource/map_resource_loader.cc
        map_resource/map_resource_manager.cc
        map_resource/platform_render.cc
        map_resource/map_resource_transform.cpp

        map_scale/sd/approach_end_scale_handler.cc

        map_scale/base/map_scale_handler.cc
        map_scale/base/map_scale_config_parse.cpp
        map_scale/base/map_scale_config_define.cpp
        map_scale/base/scale_event_creator.cpp
        map_scale/base/scale_default_config.cpp
        map_scale/sd/sd_map_scale_event_manager.cpp
        map_scale/sd/sd_start_up_scale_event_creator.cpp
        map_scale/sd/sd_magic_scale_event_creator.cpp
        map_scale/sd/sd_indoor_scale_event_creator.cpp
        map_scale/sd/sd_intersection_scale_event_creator.cpp
        map_scale/sd/sd_traffic_scale_event_creator.cpp
        map_scale/sd/sd_voice_sync_scale_event_creator.cpp
        map_scale/sd/sd_approach_end_scale_event_creator.cpp
        map_scale/sd/sd_companion_fork_scale_event_creator.cpp
        map_scale/sd/sd_curve_route_scale_event_creator.cpp
        map_scale/sd/sd_free_scale_event_creator.cpp
        map_scale/sd/companion_fork_scale_handler.cc
        map_scale/sd/pedestrian_vehicle_sep_creator.cc
        map_scale/sd/curve_route_scale_handler.cc
        map_scale/curve_route/curve_route_processor.cc
        map_scale/indoor_scale_handler.cc
        map_scale/map_scale_define.cc
        map_scale/sd/map_scale_manager.cc
        map_scale/sd/normal_scale_handler.cc
        map_scale/sd/start_up_scale_handler.cc
        map_scale/sd/traffic_jam_scale_handler.cc
        map_scale/sd/voice_sync_scale_handler.cc
        map_scale/sd/locator_free_scale_handler.cc

        map_scale/hd/hd_intersection_scale_handler.cpp
        map_scale/hd/hd_intersection_creator.cc
        map_scale/hd/hd_standard_creator.cc
        map_scale/hd/hd_map_scale_handler.cpp
        map_scale/hd/hd_toll_station_scale_handler.cpp
        map_scale/hd/hd_roundabout_scale_handler.cpp
        map_scale/hd/hd_roundabout_creator.cc
        map_scale/hd/hd_tunnel_scale_handler.cc
        map_scale/hd/hd_tunnel_creator.cc
        map_scale/hd/hd_magic_scale_event_creator.cpp
        map_scale/hd/map_scale_hd_manager.cpp
        map_scale/hd/hd_map_scale_define.cc
        map_scale/hd/hd_default_scale_handler.cc
        map_scale/hd/hd_default_creator.cc
        map_scale/hd/hd_base_creator.cc
        map_scale/hd/hd_creator_manager.cc
        map_scale/hd/hd_tunnel_intersection_creator.cc
        map_scale/hd/hd_tollgate_creator.cc
        map_scale/hd/sd_hd_switch_creator.cc
        map_scale/base/camera_handler.cc
        map_scale/magic_camera_manager.cc

        map_smart_vision/base_map_vision_handler.cc
        map_smart_vision/map_vision_curve_handler.cc
        map_smart_vision/map_vision_default_handler.cc
        map_smart_vision/map_vision_main_side_handler.cc
        map_smart_vision/map_vision_manager.cc

        map_smart_vision/hd/hd_map_vision_handler.cc

        map_tilelayer/map_tile_downloader.cc
        map_tilelayer/map_tile_layer_define.cc
        map_tilelayer/map_tile_layer_impl.cc
        map_tilelayer/map_tile_layer_mgr.cc
        map_tilelayer/map_tile.cc

        map_utils/map_biz_biz_util.cc
        map_utils/map_biz_geometry_algorithm.cc
        map_utils/map_biz_graph_algorithm.cc
        map_utils/map_biz_json_util.cc
        map_utils/map_biz_log_config.cc
        map_utils/map_biz_midmif_util.cc
        map_utils/map_biz_routepos_algorithm.cc
        map_utils/map_biz_transform_util.cc
        map_utils/map_biz_road_area_util.cc
        map_utils/map_biz_camera_util.cc
        map_utils/map_biz_log_format_utils.cc
        map_utils/map_biz_locator_util.cc
        map_layout/map_biz_force_directed.cc
        map_layout/map_biz_force_types.cc
        map_layout/map_biz_layout_util.cc
        map_layout/map_biz_3d_layout.cc
        map_layout/map_biz_screen_marker_layout.cc

        api/hd_scale_controller_impl.cc
        api/map_biz_hd_controller_impl.cc
)
mbs_include_jce()
set(LIBRARY_TYPE SHARED)

if (OS_ANDROID)
    add_subdirectory(${PROJECT_ROOT}/android android)
    include_directories(${VENDOR_DIR}/map-base/android/include)
    include_directories(${VENDOR_DIR}/plog/android/include)
    include_directories(${JNI_DEPEND_DIR})
    list(APPEND MAPBIZ_SRC ${JNI_SRCS})
    #message(FATAL_ERROR "JNI_DEPEND_DIR = ${JNI_DEPEND_DIR}")
elseif (OS_IOS)
    set(LIBRARY_TYPE STATIC)
elseif (OS_OHOS)
    list(APPEND MAPBIZ_SRC map_resource/platform_render_ohos.cc ${NAPI_SRCS})
endif ()
set(MAPBIZ_TARGET ${PROJECT_NAME})
if (NOT COMPONENT_BUILD)
    set(LIBRARY_TYPE STATIC)
    list(APPEND THIRD_DYNAMIC_LIBRARY_LIST "z")
    # set(COMPILE_EXPORT_DEFINES mapbiz_EXPORTS)
    mbs_generate_static_import_cmake(${PROJECT_ROOT} map-biz ${MAPBIZ_TARGET})
endif ()
add_library(${MAPBIZ_TARGET} ${LIBRARY_TYPE} ${MAPBIZ_SRC})
add_subdirectory(${THIRD_PARTY_DIR}/libpng ${CMAKE_BINARY_DIR}/libpng)
target_link_libraries(${MAPBIZ_TARGET} PRIVATE libpng)

if (OS_OHOS)
    target_link_libraries(${MAPBIZ_TARGET} PRIVATE libace_napi.z.so libace_ndk.z.so libnative_window.so libnative_drawing.so)
endif ()

mbs_depend_vendor(${MAPBIZ_TARGET} HDDataProcessor source)

target_link_libraries(${MAPBIZ_TARGET} PRIVATE ${JSONCPP_LIB_NAME})

if (${LIBRARY_TYPE} STREQUAL "SHARED")
    #message(FATAL_ERROR "LIBRARY_TYPE: " ${LIBRARY_TYPE})
    mbs_depend_vendor(${MAPBIZ_TARGET} map-base library)
    target_link_libraries(${MAPBIZ_TARGET} PRIVATE z)

    # 底图引擎库
    if (BUILD_WITH_MAP_ENGINE_BY_TXMAPSDK)
        message("BUILD_WITH_MAP_ENGINE_BY_TXMAPSDK")
        mbs_depend_vendor(${MAPBIZ_TARGET} txmapsdk library)
    else ()
        message("NOT BUILD_WITH_MAP_ENGINE_BY_TXMAPSDK")
        mbs_depend_vendor(${MAPBIZ_TARGET} map-engine library)
    endif ()
    mbs_depend_vendor(${MAPBIZ_TARGET} nerd-api library)
    mbs_depend_vendor(${MAPBIZ_TARGET} plog library)
    if (USE_ZLIB)
        target_link_libraries(${PROJECT_NAME} PRIVATE z)
    endif ()
endif ()
set(FILE_EXPORT ${PROJECT_ROOT}/include)
set(OUTPUT_DIR ${PROJECT_ROOT}/out)
set(LIBRARY_DIR ${OUTPUT_DIR}/${CMAKE_SYSTEM_NAME}-${CMAKE_SYSTEM_PROCESSOR}-${CMAKE_BUILD_TYPE})

install(DIRECTORY ${FILE_EXPORT} DESTINATION ${OUTPUT_DIR})
if (OS_ANDROID)
    install(DIRECTORY ${PROJECT_ROOT}/android/include DESTINATION ${OUTPUT_DIR}/android/)
endif ()

install(TARGETS ${MAPBIZ_TARGET}
        LIBRARY DESTINATION ${LIBRARY_DIR}
        ARCHIVE DESTINATION ${LIBRARY_DIR}
)
