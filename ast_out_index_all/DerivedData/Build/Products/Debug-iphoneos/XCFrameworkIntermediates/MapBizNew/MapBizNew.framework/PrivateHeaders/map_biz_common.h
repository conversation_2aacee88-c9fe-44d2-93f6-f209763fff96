// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/5/12.
//

#pragma once

#define __MAPBIZ_NAMESPACE_BEGIN__ namespace mapbiz {
#define __MAPBIZ_NAMESPACE_END__ }

#include <iostream>
#include <vector>
#include <MapBaseNew/common_define.h>
#include "map_scale_define.h"
#include <MapBaseNew/guidance_base_structure.h>
#include <MapBaseNew/route_structure.h>
__MAPBIZ_NAMESPACE_BEGIN__

typedef void (*LogPrintCallback)(const std::string& line);

/**
 * 定义地图的昼夜模式
 */
enum class MapMode {
  /**
   * 白天模式
   */
  Day = 0,
  /**
   * 夜间模式
   */
  Night = 1,
};
// 重载自定义枚举 运算符 "<<"
std::ostream& operator<<(std::ostream& out, const MapMode& map_mode);

/**
 * 导航模式
 */
enum class NavMode {
  /**
   * 自由状态(非导航非巡航)
   */
  Free = 0,
  /**
   * 导航状态
   */
  Navigation = 1,
  /**
   * 巡航状态
   */
  Cruise = 2,
};
// 重载自定义枚举 运算符 "<<"
std::ostream& operator<<(std::ostream& out, const NavMode& nav_mode);

enum class TargetResult { ERROR_DATA = 0, ERROR_NO_UPDATE = 1, ERROR_PASS = 2, SUCCESS = 3 };

/**
 * 导航场景下自动降帧配置
 */
class DynamicFrameRateConfig {
 public:
  /**
   * 核查并修正配置
   */
  void CheckAndRectifyConfig();
  /**
   * 获取一个最佳匹配帧率值
   * @param frame_rate
   * @return
   */
  int GetBestFrameRate(const int& frame_rate);
  friend std::ostream& operator<<(std::ostream& out, const DynamicFrameRateConfig& frame_rate_config);

 public:
  /**
   * 每帧变化的最大距离, 单位屏幕像素, 默认值3像素;
   */
  float max_distance_per_frame = 3;
  /**
   * 每帧变化的最大角度, 单位角度, 默认值3度;
   */
  float max_degree_per_frame = 3;
  /**
   * 关闭动态降帧 或 帧率候选值无效的情况下 使用的帧率值, 默认值 60帧率；
   */
  float default_frame_rate = 60;
  /**
   * 车标动画时长扩展系数
   */
  float animation_duration_rate = 1.0f;
  /**
   * 最终帧率选取的候选值，综合计算一个帧率值后
   * 然后在候选值中选取一个最接近的值(不小于计算的帧率,如需要帧率为12，最终会用15)
   * 候选值列表需要有序，且为升序，默认值:[0,5,10,15,20,25,30]；
   */
  std::vector<int> candidate_frame_rates{0, 5, 10, 15, 20, 25, 30};
  /**
   * 是否通过native层设置帧率，如果false，通过MapBizEventListener.OnNotifyFPS(unsigned int) 回调帧率给客户端设置帧率
   */
  bool native_set_fps = true;
};

class GuideAreaReq {
 public:
  GuideAreaReq() {}
  std::string req_id;  // 引导面计算请求ID,用来请求和回调做匹配
  mapbase::RoutePos car_pos;
};

class LocationFollowConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const LocationFollowConfig& config);
  bool operator==(const LocationFollowConfig& other) const;
  bool operator!=(const LocationFollowConfig& other) const;

 public:
  /**
   * 是否为跟随模式; {true: 设置为跟随模式, false: 取消跟随模式}
   * 默认true
   */
  bool follow = true;
  /**
   * 是否车头朝上; {true: 车头朝上, false: 车头不一定朝上}
   * 默认为false
   */
  bool heading_up = false;
  /**
   * heading_up为false时，底图旋转角度才有意义
   * 是否设置底图camera的旋转角度
   */
  bool set_map_camera_rotate_angle = false;
  /**
   * set_map_camera_rotate_angle 为true的时候，才生效
   * camera旋转角度
   */
  float map_camera_rotate_angle = 0;
  /**
   * 底图camera旋转是否有动画
   */
  bool rotate_angele_animation = false;
  /*
   * 是否有比例尺动画
   */
  bool scale_animate = true;
};
/**
 * 用户自定义的路线数据内容
 * 这里的内容若有效则会替换掉原始路线中的内容
 */
class RouteCustomDrawInfo {
 public:
  friend std::ostream& operator<<(std::ostream& out, const RouteCustomDrawInfo& custom_draw_info);

 public:
  /**
   * 终点名称信息
   * 备注:若为空则使用路线中的终点名称信息
   */
  std::string destination_name;
  /**
   * 设置默认路线ID
   * 备注: 若选中路线ID为空，则使用路线中第一条路线作为选中路线;
   */
  std::string selected_route_id;
};

class SmartVisionConfig {
 public:
  friend std::ostream& operator<<(std::ostream& out, const SmartVisionConfig& config);
  bool operator==(const SmartVisionConfig& other) const;
  bool operator!=(const SmartVisionConfig& other) const;

 public:
  /**
   * 是否开启智能视角
   */
  bool enable = true;
  /**
   * 是否开启普通导航默认场景智能视角，enable开启时有效
   */
  bool default_scene_enable_for_sd = true;
  /**
   * 是否开启车道级导航默认场景智能视角，enable开启时有效
   */
  bool default_scene_enable_for_hd = false;
};
/**
 * 调试内容分类
 */
enum class DebugContentCatalog {
  DebugContent_MapScale = 1 << 0,  // 调试自动比例尺
  DebugContent_MapLayer = 1 << 1,  // 调试图层显示
};
typedef int DebugContent;

namespace error_code {
const static int AddTileLayerFailed = 2001;    /** 添加TileLayer失败,没有设定数据源 */
const static int ModifyTileLayerFailed = 2002; /** 修改TileLayer失败,没有设定数据源 */
const static int RemoveTileLayerFailed = 2003; /** 删除TileLayer失败,没有设定数据源 */

const static int MainRouteNoLocation = 3001;      /** 主路线无更新信息 */
const static int CompanionRouteNoLocation = 3002; /** 伴随路线无更新信息 */
const static int GuidanceRouteNotFound = 3003;    /** 更新信息中路线在mapbiz中找不到 */
}  // namespace error_code

// enum class TrafficLightType {
//   LightNone = 0,         // 未知类型
//   LightNotWorking = 1,   // 未工作
//   LightRed = 3,          // 红灯
//   LightGreen = 5,        // 绿灯
//   LightYellow = 7        // 黄灯
// };
enum class TurnArrowType {
  None = -1,     // 未知类型
  Straight = 0,  // 直行
  Left = 1,      // 左转
  Right = 2,     // 右转
  LeftTurn = 3,  // 左掉头
  RightTurn = 4  // 右掉头
};
class TrafficLightStatus {
 public:
  friend std::ostream& operator<<(std::ostream& out, const TrafficLightStatus& status);
  bool operator==(const TrafficLightStatus& other) const;
  bool operator!=(const TrafficLightStatus& other) const;

 public:
  /**
   * 红绿灯类型
   */
  mapbase::LightStatus light_type = mapbase::LightStatus::None;
  /**
   * 转向箭头类型
   */
  TurnArrowType turn_arrow_type = TurnArrowType::None;
  /**
   * 当前灯态剩余时间
   */
  int remain_time = 0;
  /**
   * 展示文案
   */
  std::string text = "";
};
/**
 * 通用debug场景类型
 */
enum class CommonDebugSceneType {
  None = 0,       // 未知类型
  GuideArea = 1,  // 引导面
  MagicCamera,    // 运镜比例尺
  MapLayer,
  GeoJson = 100,  // geojson for MapOverlay
  TbtArrow,
  DataVersion,
  MainDataVersion,
};
/**
 * 通用debug信息透出
 * 1：引导面调试信息透出，目前仅透出未引导面相关信息
 */
class CommonDebugInfo {
 public:
  CommonDebugSceneType scene_type{CommonDebugSceneType::None};  // debug信息场景值 1：引导面debug信息
  std::string debug_info;                                       // debug信息
};
enum class MapBizEventType {
  None = 0,                   // 未知类型
  HD_VERSION_UPDATE = 1,      // HD版本更新，收到此事件，端上应该立即退出到sd
  EN_PEDESTRIAN_VEH_SEP = 2,  // 进入人车分离，收到此事件，端上应该立即退出到sd
  ON_PEDESTRIAN_VEH_SEP = 3,  // 已经进入了人车分离模式
  EX_PEDESTRIAN_VEH_SEP = 4,  // 退出人车分离，当状态从ON_PEDESTRIAN_VEH_SEP -> EX_PEDESTRIAN_VEH_SEP 端上收到此回调后才能进入HD，否则有时序问题
  RTT_EVENT_UTL = 5,          // 点事件缩略图url信息
};
class MapBizEventInfo {
 public:
  MapBizEventType event_type{MapBizEventType::None};  // 信息场景值 1：HD版本更新
  std::string message;
};

class MapBizCreatedResultInfo {
 public:
  int code = 0;
  std::string message = "success";
};
enum class MapBizLocationType_ { SD_GPS = 0, GUIDE_AREA_CENTER_LINE, HD_LANE };

enum class ViaPointStatus {
  None = -1,       // 未知类型
  Selected = 0,    // 选中
  UnSelected = 1,  // 非选中
  Reached = 2,     // 已到达
  Unreached = 3    // 未到达
};
class ViaPointInfo {
 public:
  friend std::ostream& operator<<(std::ostream& out, const ViaPointInfo& info);
  bool operator==(const ViaPointInfo& other) const;
  bool operator!=(const ViaPointInfo& other) const;

 public:
  /**
   * 路线id
   */
  std::string route_id;
  /**
   * 途径点在此路线中的序号
   */
  int index;
  /**
   * 途径点状态
   */
  ViaPointStatus status = ViaPointStatus::None;
};

class ViaPointEvent {
 public:
  friend std::ostream& operator<<(std::ostream& out, const ViaPointEvent& event);
  bool operator==(const ViaPointEvent& other) const;
  bool operator!=(const ViaPointEvent& other) const;

 public:
  /**
   * 路线id
   */
  std::string route_id;
  /**
   * 途径点在此路线中的序号
   */
  int index;
  /**
   * 途径点是否被选中，默认false未选中
   */
  bool is_selected = false;
  /**
   * 途径点是否已驶过，默认false未驶过
   */
  bool is_reached = false;
  /**
   * 途径点节点信息
   */
  mapbase::RouteResultNode node;
};
enum class MagicCameraHandlerStatus {
  Start = 0,
  Stop,
  Animation,
};
enum class NaviGuideType {
  GuideType_None = -1,  // 非法类型
  GuideType_SD = 0,     // SD导航
  GuideType_HD = 1,     // HD导航
};
class MagicCameraStatus {
 public:
  virtual ~MagicCameraStatus() {}
  std::string scale_type;
  NaviGuideType guide_type{NaviGuideType::GuideType_None};
  MagicCameraHandlerStatus handler_status{MagicCameraHandlerStatus::Start};
  friend std::ostream& operator<<(std::ostream& out, const MagicCameraStatus& event);
};

class AnimationMagicCameraStatus : public MagicCameraStatus {
 public:
  float animation_ratio{};
  friend std::ostream& operator<<(std::ostream& out, const AnimationMagicCameraStatus& event);
};

class MagicCameraData {
 public:
  std::string route_id;
  std::string scale_type = "";
  mapbase::RoutePos target_pos_on_route;
  mapbase::GeoCoordinateZ target_pos;
  friend std::ostream& operator<<(std::ostream& out, const MagicCameraData& data);
};

class MagicCameraIntervalData {
 public:
  std::string route_id;
  std::string event_name = "";
  mapbase::RoutePos start_pos;
  mapbase::RoutePos end_pos;
  mapbase::RoutePos target_pos;
  std::vector<mapbase::GeoCoordinateZ> scale_target_points;
  friend std::ostream& operator<<(std::ostream& out, const MagicCameraIntervalData& data);
};

enum class GuideLineMode {
  None = -1,         // 无效值
  StopBeforeCross,   // 引导线在路口前停止，调用后计算一次引导线，定位车道偏离后重新计算
  GoThroughCross,    // 引导线可以穿过路口，调用后计算一次引导线，定位车道偏离后重新计算
  ContinuousExplore  // 从自车位置持续计算固定距离的引导线
};

class GuideLineData {
 public:
  const static int STATUS_OK = 0;
  const static int STATUS_FAILED_FORBID_SWITCH =
      7231;  // 错误码已迁移到GuideLineStatus::STATUS_GUIDE_ERROR_FAILED_FORBID_SWITCH
 public:
  int status = 0;
  std::string error_msg = "success";
  std::string route_id;
  double length = 0;
  mapbase::GuideLineInfo to_pos_engine;
};

struct TBTArrowData {
  TBTArrowData(const std::vector<mapbase::GeoCoordinateZ>& center_line, uint32_t turn_range_start,
               uint32_t turn_range_end)
      : center_line(center_line), turn_range_start(turn_range_start), turn_range_end(turn_range_end) {}
  std::vector<mapbase::GeoCoordinateZ> center_line;  // 白箭头中心线
  uint32_t turn_range_start;                         // 路口转向区域开始点
  uint32_t turn_range_end;                           // 路口转向区域结束点
};

enum HDSwitchLaneDirection {
  HD_SWITCH_DIR_NONE = 0,
  HD_SWITCH_DIR_LEFT = 1,
  HD_SWITCH_DIR_RIGHT = 2,
  HD_SWITCH_DIR_FORWARD = 3,
};

/// 引导线变道信息
struct LaneChangeInfo {
  int start_index = -1;
  int end_index = -1;
  HDSwitchLaneDirection direction{HD_SWITCH_DIR_NONE};
};

/// 仅仅用来测试
struct MockLaneEx {
  int lane_index_offset{0};
  bool change_dir{false};         /// 变道方向，向左true，向右false
  double change_time_ratio{0.0};  /// 当前位置变道占据比例
  bool is_used_guide_area_info_mock{false};
  mapbase::PosTpid last_lane_id{};
  int last_lane_index = 0;
};

/// 仅仅用来测试
enum DebugInfoSceneType {
  kRectMarker,
  kAddGroupMarker = 10010,
  kUpdateForceConfig = 20000,
  kEnableDebugInfo = 30000,
  kEnableGuideAreaDebugInfo = 30001,
  kEnableRecommendLaneDebugInfo = 30002,
};
struct DebugInfoData {
  int scene_type{0};
  std::string common_info;
};

enum MapBizControlEventType {
  None = 0,
  UPDATE_OVERLAY_RES = 1,  // 刷新指定overlay资源
};

struct MapBizControlEventInfo {
  MapBizControlEventType event_type{MapBizControlEventType::None};  // 控制事件类型
  std::string control_info;
  friend std::ostream& operator<<(std::ostream& out, const MapBizControlEventInfo& control_info);
};

struct DebugRouteXInfo {
  std::string forkInfo;
  std::vector<mapbase::GeoCoordinate> shapePoints;
};

class RouteLine {
 public:
  friend std::ostream& operator<<(std::ostream& out, const RouteLine& route_line);
  bool operator==(const RouteLine& other) const;
  bool operator!=(const RouteLine& other) const;

 public:
  /**
   * 路线ID
   */
  std::string route_id = "";
  /**
   * 建筑物ID，为空表示室外路段
   */
  std::string building_id = "";
  /**
   * 楼层名称，为空表示室外路段
   */
  std::string building_floor = "";
};

enum class ShowStatus {
  OutdoorsOnly = 0,           // 室外显示
  OutdoorsNCurrentFloor = 1,  // 室外和当前楼层显示
  OutdoorsNIndoors = 2,       // 室外和室内全部楼层均显示
};

enum class IndoorEntriesBubbleType {
  None = -1,     // 无效
  Entrance = 1,  // 入口
  Exit = 2,      // 出口
  FloorOut = 3,  // 层间退出
  FloorIn = 4,   // 层间进入
};

class CustomControlParam {
 public:
  friend std::ostream& operator<<(std::ostream& out, const CustomControlParam& custom_control_param);
  bool operator==(const CustomControlParam& other) const;
  bool operator!=(const CustomControlParam& other) const;

 public:
  mapbase::PosLogoDisplayType pos_logo_display_type_for_cruise = mapbase::PosLogoDisplayType::LogoDisplayType_Route;
};

class UVAnimationParams {
 public:
  friend std::ostream& operator<<(std::ostream& out, const UVAnimationParams& materials_animation);
  bool operator==(const UVAnimationParams& other) const;
  bool operator!=(const UVAnimationParams& other) const;

 public:
  float uv_offset_start_x_{0.0f};  // x轴UV动画起始偏移，默认0
  float uv_offset_start_y_{0.0f};  // y轴UV动画起始偏移，默认0
  float uv_single_step_x_{0.0f};   // x轴UV动画单步步长，0-1
  float uv_single_step_y_{0.0f};   // y轴UV动画单步步长，0-1
  int uv_step_count_{0};           // UV动画的UV步数，默认0
};

class MaterialUVAnimationParams {
 public:
  friend std::ostream& operator<<(std::ostream& out, const MaterialUVAnimationParams& materials_animation);
  bool operator==(const MaterialUVAnimationParams& other) const;
  bool operator!=(const MaterialUVAnimationParams& other) const;

 public:
  int material_id_{0};                        // 材质id
  std::vector<UVAnimationParams> uv_params_;  // 材质UV动画参数
};

class MaterialsAnimation {
 public:
  friend std::ostream& operator<<(std::ostream& out, const MaterialsAnimation& materials_animation);
  bool operator==(const MaterialsAnimation& other) const;
  bool operator!=(const MaterialsAnimation& other) const;

 public:
  bool enable_ = false;
  std::map<int, std::string> materials_;                        // 材质id->材质纹理资源名称
  float animation_duration_{0.0f};                              // 一次动画时间，单位ms
  float delay_duration_{0.0f};                                  // 动画延迟执行时间，单位ms
  int begin_from_current_state_{0};                             // 动画被打断时停在当前状态(1) or 完成状态(0)
  std::vector<MaterialUVAnimationParams> uv_animation_params_;  // 材质UV动画参数
};

enum class LocationLaneConfidence {
  None = 0,
  Low = 1,
  High = 2,
};
std::ostream& operator<<(std::ostream& out, const LocationLaneConfidence& lane_confidence);

__MAPBIZ_NAMESPACE_END__
