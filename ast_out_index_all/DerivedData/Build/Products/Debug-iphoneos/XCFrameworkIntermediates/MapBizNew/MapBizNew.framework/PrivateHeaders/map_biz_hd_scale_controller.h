// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by <PERSON>林洋 on 2023/12/18.
//

#pragma once
#include <memory>
#include <MapBaseNew/common_const.h>
#include "map_biz_common.h"
#include "map_biz_export.h"
#include "map_biz_option.h"
#include "hd_map_scale_define.h"
__MAPBIZ_NAMESPACE_BEGIN__

/**
 *
 * 已经淘汰
 * hd 比例尺控制器
 * 建议使用 updateMagicCameraConfig 运镜比例尺
 */
class MAPBIZ_EXPORT MapBizHDScaleController {
 public:
  virtual std::shared_ptr<HDAutoScaleConfigContainer> GetHDAutoScaleConfigContainer() = 0;
  virtual void SetHDAutoScaleConfigContainer(std::shared_ptr<HDAutoScaleConfigContainer> container) = 0;
};
__MAPBIZ_NAMESPACE_END__

