// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/5/12.
//

#pragma once

#include <map>
#include <string>
#include <MapBaseNew/common_define.h>
#include "map_biz_common.h"
#include "map_tile_layer.h"
#include "map_tile_layer_define.h"
#include <MapBaseNew/guidance_base_structure.h>
#include <MapBaseNew/route_structure.h>

__MAPBIZ_NAMESPACE_BEGIN__

/**
 * 地图事件回调
 */
class MapBizEventListener {
 public:
  /**
   * 导航场景中，根据具体场景，适当调整帧率；
   * 非必须实现
   * @param fps
   */
  virtual void OnNotifyFPS(const unsigned int fps) {}

  /**
   * 响应气泡扎点信息
   * 端上需要扎点位置信息，自己绘制气泡的话需要监听；
   * @param bubble_layer_type                        气泡扎点
   * @param bubble_candidate_position_container      {路线ID，扎点位置列表} 对象列表
   * @note 只有通过扎点配置启动了回调，才会有回调，参考B<PERSON><PERSON><PERSON>ayerBaseConfig配置
   */
  virtual void OnNotifyMapBubblePositions(const BubbleLayerType bubble_layer_type,
                                          const BubbleCandidatePositionContainer bubble_candidate_position_container) {}

  /**
   * 响应路线点击回调
   * @param route_id                              点击路线ID
   */
  virtual void OnTapRouteLine(const std::string& route_id) {}
  /**
   * 响应路线点击回调信息
   * @param route_line                              点击路线信息
   */
  virtual void OnTapRouteLineInfo(const RouteLine& route_line) {}

  /**
   * 响应途经点点击回调
   * @param route_node                           途经点信息
   */
  virtual void OnTapRouteNode(const mapbase::RouteResultNode& route_result_node) {}

  /**
   * 响应途经点点击回调，此回调在 OnTapRouteNode 之前
   * @param index                           途经点索引
   */
  virtual void OnTapViaPointIndex(int index) {}

  /**
   * 响应终点点击回调
   * @param destination_node                终点信息
   */
  virtual void OnTapRouteDestinationNode(const mapbase::DestinationNode& destination_node) {}

  /**
   * 响应点击路线解释性Marker信息
   * @param route_explain_marker                 路线解释性Marker信息 (uid为Marker唯一, name
   * 为资源名称 客户端依赖这个资源名称扎点)
   * @param max_bound                            Marker关联线路的最大外界框
   * @note  max_bound 用户可用于将目标全览,
   * 若marker没有绑定线路，max_bound为无效值(对角坐标相同)，使用方请先判断再使用;
   */
  virtual void OnTapRouteMarker(const mapbase::RouteResultNode& route_explain_marker,
                                const mapbase::Rect<mapbase::GeoCoordinate>& max_bound) {}

  /**
   * 响应交通点事件Marker点击回调
   * @param trafficEventMarker {uid: 事件ID, name: 类型名称, location：坐标}
   */
  virtual void OnTapTrafficEventMarker(const mapbase::RouteResultNode& traffic_event_marker) {}

  /**
   * 操作一个TileLayer后的结果回调
   * @param ret           操作图层状态
   * @param data_source   所用的数据源唯一标示，用户连续发起创建多个图层时，用于客户端区分返回值
   * @param tile_layer 图层具柄，用户后续可通过该具柄操作该图层，如设置显示/隐藏；(安卓平台不适用)
   * @note 只有ret返回0状态的情况下，tile_layer才是有效的
   */
  virtual void OnOperateTileLayerResult(const mapbase::RetMessage& ret, std::string data_source,
                                        std::shared_ptr<MapTileLayer> tile_layer) {}

  /**
   * 响应用户点击
   * @param ret            详情获取状态(0:请求中,1请求完成)
   * @param marker_detail  具体详情内容
   * @warning 如果用户设置了自定义拉去详情, 则直接返回状态1;
   */
  virtual void OnTapTileLayerMarker(const mapbase::RetMessage& ret, const MapTileMarkerDetail& marker_detail) {}

  /**
   * 路线创建完成回调
   * @param route_overlay_ids 路线overlay ids 路线对应的所有蚯蚓线overlay
   */
  virtual void OnRouteLinesCreated(const std::map<std::string, std::vector<int>>& route_overlay_ids) {}
  /**
   * 路线生长动画执行完成
   * @param msg  结果状态消息(msg.ret_==0:成功,其他失败)
   */
  virtual void OnRouteLineGrowFinished(const mapbase::RetMessage& msg) {}
  /**
   * 鹰眼图添加完成回调
   * @param msg  结果状态消息(msg.ret_==0:成功,其他失败)
   */
  virtual void OnOverviewMapAttached(const mapbase::RetMessage& msg) {}

  /**
   * 鹰眼图移除完成回调
   * @param msg  结果状态消息(msg.ret_==0:成功,其他失败)
   */
  virtual void OnOverviewMapDetached(const mapbase::RetMessage& msg) {}

  /**
   * 中心线计算回调
   * @param center_line_data 引导面中心线数据
   */
  virtual void OnGuidanceAreaCenterLine(const mapbase::GuideAreaCenterLineData& center_line_data) {}
  /**
   * 距离已经计算出引导面结尾距离回调
   * @param guide_area_end_distance 距离
   * @deprecated 已废弃，回调新接口OnGuideAreaEndDistanceWithAllRoutes
   */
  virtual void OnGuideAreaEndDistance(const mapbase::GuideAreaEndDistance& guide_area_end_distance) {}

  /**
   * 距离已经计算出引导面结尾距离回调
   * @param guide_area_end_distance 所有路线的剩余距离
   * 虚函数实现了回调主路线的OnGuideAreaEndDistance，子类按需重写
   */
  virtual void OnGuideAreaEndDistanceWithAllRoutes(
      const mapbase::GuideAreaEndDistanceWithAllRoutes& guide_area_end_distance) {
    auto it_find = guide_area_end_distance.end_distances.find(guide_area_end_distance.main_route_id);
    if (it_find != guide_area_end_distance.end_distances.end()) {
      OnGuideAreaEndDistance(it_find->second);
    }
  }

  /**
   * 客户端调用 UpdateGuidanceSegmentInfo 出现异常回调
   * @param msg 错误码定义参见：
   * mapbiz::error_code::MainRouteNoLocation，msg.msg_打印主路线id，
   * mapbiz::error_code::CompanionRouteNoLocation，msg.msg_打印没有匹配的伴随路线id，
   * mapbiz::error_code::GuidanceRouteNotFound，msg.msg_打印没有找到的诱导路线id
   */
  virtual void OnSegmentUpdateError(const mapbase::RetMessage& msg) {}

  /**
   * 通用debug信息透出
   * 引导面失败原因透出
   * @param debug_info
   */
  virtual void OnCommonDebugInfo(const CommonDebugInfo& debug_info) {}

  /**
   * 通用mapBiz事件透出
   * @param info 事件info
   */
  virtual void OnMapBizEvent(const MapBizEventInfo& event) {}

  /**
   * 全览动画完成回调
   */
  virtual void OnOverLookAnimationFinished() {}

  /**
   * 引导线更新回调
   * @param guide_line_data 引导线信息
   */
  virtual void OnGuideLineDataUpdate(const GuideLineData& guide_line_data) {}

  /**
   * 引导线中心线更新回调
   * 默认该回调不会触发，需要在白箭头配置中设置回调开关(GuideLineLayerConfig has_center_line_callback)
   * @param src_loc_inf 高频定位信息
   * @param center_line 引导线中心线行点串
   * @param lane_change_info_vec 变道信息
   */
  virtual void OnGuideLineCenterLineDataUpdate(const mapbase::HighFreqLocInfo& src_loc_inf,
                                               const std::vector<mapbase::GeoCoordinateZ> &center_line,
                                               const std::vector<LaneChangeInfo>& lane_change_info_vec) {}

  /**
   * 白箭头创建回调
   * 默认该回调不会触发，需要在白箭头配置中设置回调开关(HDTBTArrowLayerConfig has_center_line_callback)
   * @param tbt_arrow_data 白箭头信息
   */
  virtual void OnTBTArrowUpdate(const std::vector<TBTArrowData>& tbt_arrow_data) {}

  /**
   * 为了解决JavaDetach多线程问题设计的接口，内部接口
   */
  virtual void _OnDestroy() {}

  /**
   * 途径点相关事件回调
   * @param viaPointEvent 途径点信息
   * @return 如果回调中返回 true，表示客户端处理过，后续不会在触发 onTapViaPointIndex 以及 途径点的 onTapRouteNode
   */
  virtual bool OnViaPointEvent(const ViaPointEvent& viaPointEvent) { return false; }

  /*
   * Overlay点击事件统一回调接口 (废弃，请用Json版本）
   * @param info 被点击的overlay信息合集
   * 对于info.main_type_为LayerType::SD_Camera或LayerType::HD_Camera的回调，透传的是CameraOverlayInfo类的智能指针
   */
  virtual void OnClick(const std::shared_ptr<mapbase::OverlayInfo> info) {}
  /*
   * 自车驶过Overlay事件
   * @param info 的overlay信息合集
   * 对于info.main_type_为LayerType::SD_Camera或LayerType::HD_Camera的回调，透传的是OverlayInfo类的智能指针
   */
  virtual void OnPassed(const std::shared_ptr<mapbase::OverlayInfo> info) {}

  /**
   * 运镜比例尺里面的handler进入退出消息
   * @param magic_camera_status
   */
  virtual void OnMagicCameraStatus(const std::shared_ptr<MagicCameraStatus>& magic_camera_status) {}

  /* Overlay点击事件统一回调接口 json 版
   * 结构
   * {
   *  mainType:x
   *  subType:x
   *  layerId:x
   *  其它字段：
   *  }
   * */
  virtual void OnClick(const std::string& json) {}
};
__MAPBIZ_NAMESPACE_END__

