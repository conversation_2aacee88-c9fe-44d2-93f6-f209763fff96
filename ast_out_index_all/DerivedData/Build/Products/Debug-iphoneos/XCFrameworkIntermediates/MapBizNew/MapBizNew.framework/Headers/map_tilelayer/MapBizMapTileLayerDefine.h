//
//  MapBizMapTileLayerDefine.h
//  MapBiz
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/7.
//

#ifndef _OC_MAP_BIZ_MAP_TILE_LAYER_DEFINE_H_
#define _OC_MAP_BIZ_MAP_TILE_LAYER_DEFINE_H_

#import <Foundation/Foundation.h>
#import <MapBaseNew/MapBaseCommonLocation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - MapBizMapTileLayerOption - 创建图层选项
/**
 * 创建图层选项
 */
@interface MapBizMapTileLayerOption : NSObject
@property (nonatomic, copy, readonly) NSString *dataSource;  // 该图层对应的数据源，也是图层的唯一标示，不可重复
@property (nonatomic, assign) int tileSize;                  // 该图层瓦片的大小(像素单位)
@property (nonatomic, assign) int refreshInterval;           // 该图层数据刷新间隔(单位秒，默认值90)
@property (nonatomic, assign) int layerPriority;             // 该图层的优先级
@property (nonatomic, assign) int visibleMinScaleLevel;      // 该图层显示的级别范围,最小值
@property (nonatomic, assign) int visibleMaxScaleLevel;      // 该图层显示的级别范围,最小值
@property (nonatomic, assign) BOOL visible;                  // 该图层是否显示(支持先创建，但不立即显示)
@property (nonatomic, assign) BOOL clickable;                // 该图层元素是否可以点击(默认可以点击)
@property (nonatomic, assign) BOOL avoidOthers;              // 是否参与避让(默认值true)
@property (nonatomic, assign) BOOL avoidAnnotation;          // 是否避让底图元素(默认值false)
@property (nonatomic, assign) BOOL getDetailByUser;          // 点击获取详情是否完全由客户端拉去（默认false）
- (instancetype)initWithDataSource:(NSString *)dataSource;
@end

#pragma mark - MapBizMapTileMarkerDescriptor - 动态图层元素描述
/**
 * 动态图层元素描述
 */
@interface MapBizMapTileMarkerDescriptor : NSObject
@property (nonatomic, copy, nullable) NSString *uid;
@property (nonatomic, copy, nullable) NSString *catalog;
@property (nonatomic, strong, nullable) TMapBaseGeoCoordinate *coordinate;
@property (nonatomic, assign) int priority;
@end

#pragma mark - MapBizMapTileMarkerDetail - 用户点击动态图层Marker时返回
/**
 * 用户点击动态图层Marker时返回
 */
@interface MapBizMapTileMarkerDetail : NSObject
@property (nonatomic, copy, nullable) NSString *uid;
@property (nonatomic, copy, nullable) NSString *catalog;
@property (nonatomic, strong, nullable) TMapBaseGeoCoordinate *coordinate;
@property (nonatomic, copy, nullable) NSString *dataSource;
@property (nonatomic, copy, nullable) NSString *detail;
@end
NS_ASSUME_NONNULL_END

#endif
