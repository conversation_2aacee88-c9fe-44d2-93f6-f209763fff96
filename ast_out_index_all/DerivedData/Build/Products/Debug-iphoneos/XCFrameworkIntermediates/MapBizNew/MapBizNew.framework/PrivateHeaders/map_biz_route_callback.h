// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/12/4.
//

#pragma once

#include <map>
#include <MapBaseNew/common_define.h>

#include "map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__

/**
 * 蚯蚓线操作回调
 */
class MapBizRouteCallBack {
 public:
  /**
   * 用户设置非选中路线(伴随路线)动态隐藏/显示完成后的回调
   * @param ret     操作状态
   */
  virtual void OnSetUnSelectedRouteHiddenFinished(const mapbase::RetMessage& ret) {}
  /**
   * 用户请求路线剩余轨迹外界框 对应的回调
   * 若请求时传入的路线ID 为空，则会返回所有路线的最大外界框，路线ID为所有路线ID的拼接，拼接字符逗号
   * @param route_bounds
   */
  virtual void OnGetRemainRouteMaxBound(
      const std::map<std::string, mapbase::Rect<mapbase::GeoCoordinate>> route_bounds) {}
};

__MAPBIZ_NAMESPACE_END__

