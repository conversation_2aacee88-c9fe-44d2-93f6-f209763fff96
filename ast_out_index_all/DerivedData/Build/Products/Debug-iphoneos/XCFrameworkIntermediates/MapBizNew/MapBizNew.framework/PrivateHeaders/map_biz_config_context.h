// Copyright (c) 2024 Tencent Inc. All rights reserved.
//
// Created by 李林洋 on 2023/10/23.
//

#pragma once
#include <memory>
#include "map_biz_common.h"
#include "map_biz_export.h"
#include "map_biz_option.h"
#include "map_layer_define.h"
__MAPBIZ_NAMESPACE_BEGIN__
/**
 * 内部保存了mapbiz的一些config
 */
class MAPBIZ_EXPORT MapBizConfigContext {
 public:
  /**
   * 根据LayerTypeInfo 获得对应的layerConfig
   * @param layer_type_info 用来区分获得哪一个LayerConfig
   * @return 对应得到的LayerConfig，具体哪个LayerType对应哪个子类，可以看 LayerType注释
   */
  virtual std::shared_ptr<LayerBaseConfig> GetLayerConfig(LayerTypeInfo layer_type_info) = 0;

  /**
   * 获取Marker图层名称样式
   * @param marker_type Marker图层类型
   * @return Marker图层名称样式
   */
  virtual MarkerNameStyleOption GetMarkerNameStyleOption(MarkerType marker_type) = 0;

  /**
   * 获取用户自定义蚯蚓线样式
   * @return 用户自定义蚯蚓线样式
   */
  virtual ColorRouteStyleOption GetCustomRouteStyle() = 0;

  /**
   * 获取用户自定义高级蚯蚓线样式
   * @return 用户自定义高级蚯蚓线样式
   */
  virtual ColorRouteStyleOptionEx GetCustomRouteStyleEx() = 0;

  /**
   * 获取用户自定义箭头样式
   * @return 用户自定义箭头样式
   */
  virtual TurnArrowStyleOption GetTurnArrowStyle() = 0;

  /**
   * 获取底图的昼夜模式
   * @return           MapMode::Day:白天; MapMode::Day:夜间;
   */
  virtual MapMode GetMapMode() = 0;

  /**
   * 获取底图的导航模式
   * @return  返回当前的导航模式
   */
  virtual NavMode GetNavMode() = 0;

  /**
   * 获取定位资源切换 是否由用户完全控制
   * @return  true:User控制, false:SDK控制
   */
  virtual bool GetLocatorResCtrlMode() = 0;

  /**
   * 获取底图跟随模式状态
   * @return      true:跟随模式; false:非跟随
   */
  virtual bool IsLocationFollowed() = 0;

  /**
   * 获取底图是否车头朝上/朝北(屏幕顶部)
   * @return      true:车头朝上; false:非车头朝上
   */
  virtual bool IsHeadingUp() = 0;
  /**
   * 获取底图是否为2D模式状态
   * @return     true:2D状态;false:3D状态
   */
  virtual bool Is2DMap() = 0;

  /**
   * 获取自动比例尺策略状态
   * @return     true:自动比例尺开启中; false: 自动比例尺关闭中
   */
  virtual bool IsAutoScaleEnabled() = 0;

  /**
   * 获取自动比例尺策略参数
   * @return        所有自动比例尺策略的执行参数
   */
  virtual MapScaleConfigContainer GetAutoScaleConfig() = 0;

  /**
   * 获取导航场景下所有气泡图层的扎点和显示策略
   * @return    所有气泡图层的扎点和显示策略
   */
  virtual BubbleLayerConfigContainer GetBubbleLayerConfig() = 0;

  /**
   * 获取自动俯仰角策略状态
   * @return   true: 自动俯仰角策略开启中; false: 自动俯仰角策略关闭
   */
  virtual bool IsAutoSkewEnabled() = 0;
  virtual GlobalConfig GetGlobalConfig() = 0;
};
__MAPBIZ_NAMESPACE_END__
