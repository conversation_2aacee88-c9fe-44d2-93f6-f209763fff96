// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(赵春亮) on 2020/8/5.
//

#pragma once

#include <map>
#include <GLMapLib/MapDataType.h>
#include "map_biz_common.h"

__MAPBIZ_NAMESPACE_BEGIN__

namespace Layer {

// 默认显示级别
namespace Level {
/**
 * 比例尺参映射表参考信息
盘古	缩放scale     比例尺
18  1           10m
17  0.5         20m
16  0.25        50m
15  0.125       100m
14  0.0625      200m
13  0.0375      500m
12  0.01875     1km
11  0.009375    2km
10  0.0046875   5km
9   0.00234375  10km
8   0.00117188  20km
7   0.00058594  50km
6   0.00029297  100km
5   0.00014648  200km
4   0.00007324  500km
3   0.00003662  1000km
2   0.00001831  2000km
1   0.00000916  5000km
*/


// 地图允许显示的最大级别
constexpr int MAX_LEVEL = 30;
// 定位车标图层
constexpr int LOCATOR_MIN = 0;
constexpr int LOCATOR_MAX = MAX_LEVEL;
// 路线图层
constexpr int ROUTE_MIN = 0;
constexpr int ROUTE_MAX = MAX_LEVEL;
// 电子眼图层 9级(10公里) 到MAX_LEVEL级
constexpr int CAMERA_MIN = 9;
constexpr int CAMERA_MAX = MAX_LEVEL;
// 路名气泡图层 10级(5KM) 到MAX_LEVEL级
constexpr int ROUTE_NAME_MIN = 10;
constexpr int ROUTE_NAME_MAX = MAX_LEVEL;

constexpr int PEDESTRIAN_VEHICLE_SEP_MIN = 3;
constexpr int PEDESTRIAN_VEHICLE_SEP_MAX = MAX_LEVEL;

// 室内图切换点图层
// @Deprecated
constexpr int INDOOR_ENTRIES_MIN = 0;
constexpr int INDOOR_ENTRIES_MAX = MAX_LEVEL;
// 入口气泡
constexpr int ENTRANCE_BUBBLE_MIN = 0;
constexpr int ENTRANCE_BUBBLE_MAX = MAX_LEVEL;
// 出口气泡
constexpr int EXIT_BUBBLE_MIN = 0;
constexpr int EXIT_BUBBLE_MAX = MAX_LEVEL;
// 层间气泡
constexpr int FLOOR_BUBBLE_MIN = 0;
constexpr int FLOOR_BUBBLE_MAX = MAX_LEVEL;
// 拥堵气泡图层 9级(10KM) 到MAX_LEVEL级
constexpr int ROUTE_TRAFFIC_MIN = 9;
constexpr int ROUTE_TRAFFIC_MAX = MAX_LEVEL;
// 伴随气泡图层 10级(5KM) 到MAX_LEVEL级
constexpr int ROUTE_COMPANION_MIN = 10;
constexpr int ROUTE_COMPANION_MAX = MAX_LEVEL;
// Marker图层---路线引导点起点
constexpr int START_CIRCLE_MIN = 0;
constexpr int START_CIRCLE_MAX = MAX_LEVEL;
// Marker图层---途径点
constexpr int VIA_CIRCLE_MIN = 0;
constexpr int VIA_CIRCLE_MAX = MAX_LEVEL;
// Marker图层---路线引导点终点
constexpr int END_CIRCLE_MIN = 0;
constexpr int END_CIRCLE_MAX = MAX_LEVEL;
// Marker图层---起点气泡
constexpr int START_BUBBLE_MIN = 0;
constexpr int START_BUBBLE_MAX = MAX_LEVEL;
// Marker图层---终点气泡
constexpr int END_BUBBLE_MIN = 0;
constexpr int END_BUBBLE_MAX = MAX_LEVEL;
// Marker图层---红绿灯 12级(1KM) 到MAX_LEVEL级
constexpr int TRAFFIC_LIGHT_MIN = 12;
constexpr int TRAFFIC_LIGHT_MAX = MAX_LEVEL;
// Marker图层---HD红绿灯 12级(1KM) 到MAX_LEVEL级
constexpr int HD_TRAFFIC_LIGHT_MIN = 12;
constexpr int HD_TRAFFIC_LIGHT_MAX = MAX_LEVEL;
// Marker图层---警示牌 12级(1KM) 到MAX_LEVEL级
constexpr int WARNING_TIP_MIN = 12;
constexpr int WARNING_TIP_MAX = MAX_LEVEL;
// Marker图层---交通事件
constexpr int TRAFFIC_EVENT_MIN = 10;
constexpr int TRAFFIC_EVENT_MAX = MAX_LEVEL;
// Marker图层---门marker
constexpr int DOOR_MARKER_MIN = 11;
constexpr int DOOR_MARKER_MAX = MAX_LEVEL;
// 巡航marker图层
constexpr int CRUISE_MARKER_MIN = 12;
constexpr int CRUISE_MARKER_MAX = MAX_LEVEL;
// 终点名称ETA图层
constexpr int DEST_NAME_ETA_MIN = 0;
constexpr int DEST_NAME_ETA_MAX = MAX_LEVEL;
// 路线标签图层
constexpr int ROUTE_LABEL_MIN = 0;
constexpr int ROUTE_LABEL_MAX = MAX_LEVEL;
// 等灯气泡 9级 到MAX_LEVEL级
constexpr int LIGHT_WAIT_MIN = 9;
constexpr int LIGHT_WAIT_MAX = MAX_LEVEL;

// TP图层
constexpr int TP_MIN = 10;
constexpr int TP_MAX = MAX_LEVEL;

// 引导面图层
constexpr int GUIDE_AREA_MIN = 10;
constexpr int GUIDE_AREA_MAX = MAX_LEVEL;

// 蚯蚓面图层
constexpr int ROUTE_LINE_AREA_MIN = 10;
constexpr int ROUTE_LINE_AREA_MAX = MAX_LEVEL;

// 引导线图层
constexpr int GUIDE_LINE_MIN = 10;
constexpr int GUIDE_LINE_MAX = MAX_LEVEL;

// HD电子眼图层 19级 到MAX_LEVEL级
constexpr int HD_CAMERA_MIN = 9;
constexpr int HD_CAMERA_MAX = MAX_LEVEL;
// HD路名气泡图层 19级 到MAX_LEVEL级
constexpr int HD_ROUTE_NAME_MIN = 10;
constexpr int HD_ROUTE_NAME_MAX = MAX_LEVEL;
// Marker图层---途径点
constexpr int HD_VIA_CIRCLE_MIN = 0;
constexpr int HD_VIA_CIRCLE_MAX = MAX_LEVEL;
// Marker图层---路线引导点终点
constexpr int HD_END_CIRCLE_MIN = 0;
constexpr int HD_END_CIRCLE_MAX = MAX_LEVEL;
// Marker图层---起点气泡
constexpr int HD_START_BUBBLE_MIN = 0;
constexpr int HD_START_BUBBLE_MAX = MAX_LEVEL;
// Marker图层---终点气泡
constexpr int HD_END_BUBBLE_MIN = 0;
constexpr int HD_END_BUBBLE_MAX = MAX_LEVEL;
// HD警示牌 19级 到MAX_LEVEL级
constexpr int HD_WARNING_TIP_MIN = 12;
constexpr int HD_WARNING_TIP_MAX = MAX_LEVEL;
// HD拥堵气泡 19级 到MAX_LEVEL级
constexpr int HD_ROUTE_TRAFFIC_MIN = 9;
constexpr int HD_ROUTE_TRAFFIC_MAX = MAX_LEVEL;
// HD转向气泡
constexpr int HD_TURN_ARROW_MIN = 9;
constexpr int HD_TURN_ARROW_MAX = MAX_LEVEL;
// HD事件
constexpr int HD_ROUTE_ACCIDENT_MIN = 9;
constexpr int HD_ROUTE_ACCIDENT_MAX = MAX_LEVEL;
// HD车道文字
constexpr int HD_LANE_TEXT_MIN = 10;
constexpr int HD_LANE_TEXT_MAX = MAX_LEVEL;
// HD车道高亮
constexpr int HD_LANE_HIGHLIGHT_MIN = 10;
constexpr int HD_LANE_HIGHLIGHT_MAX = MAX_LEVEL;
// 红绿灯倒计时图层
constexpr int LIGHT_COUNTDOWN_TIMER_MIN = 14;
constexpr int LIGHT_COUNTDOWN_TIMER_MAX = MAX_LEVEL;
// HD红绿灯倒计时图层
constexpr int HD_LIGHT_COUNTDOWN_TIMER_MIN = 14;
constexpr int HD_LIGHT_COUNTDOWN_TIMER_MAX = MAX_LEVEL;
// HD长实线气泡 10级(5KM) 到MAX_LEVEL级
constexpr int HD_LONG_SOLID_MIN = 10;
constexpr int HD_LONG_SOLID_MAX = MAX_LEVEL;
// HD等灯气泡 10级 到MAX_LEVEL级
constexpr int HD_LIGHT_WAIT_MIN = 10;
constexpr int HD_LIGHT_WAIT_MAX = MAX_LEVEL;
// HD伴随气泡图层
constexpr int HD_ROUTE_COMPANION_MIN = 9;
constexpr int HD_ROUTE_COMPANION_MAX = MAX_LEVEL;
// HD白箭头图层
constexpr int HD_TBTARROW_MIN = 19;
constexpr int HD_TBTARROW_MAX = MAX_LEVEL;

constexpr int UNDERGROUND_PARK_MIN = 0;
constexpr int UNDERGROUND_PARK_MAX = MAX_LEVEL;

// 障碍物图层
constexpr int HD_OBSTACLE_MIN = 10;
constexpr int HD_OBSTACLE_MAX = MAX_LEVEL;

// HD易偏航气泡
constexpr int HD_YAW_NOTICE_LEVEL_MIN = 9;
constexpr int HD_YAW_NOTICE_LEVEL_MAX = MAX_LEVEL;

// 安全告警图层
constexpr int HD_SECURITY_ALERT_MIN = 10;
constexpr int HD_SECURITY_ALERT_MAX = MAX_LEVEL;

// 动态事件,HD 线面
constexpr int HD_RTT_EVENT_MIN = 10;
constexpr int HD_RTT_EVENT_MAX = MAX_LEVEL;

constexpr int SAFE_WARNING_MIN = 10;
constexpr int SAFE_WARNING_MAX = MAX_LEVEL;
}  // namespace Level

// 默认优先级，如有改动需同步改MapLayerDefine.java文件
namespace Priority {

// 巡航鱼骨路线图层
constexpr int CRUISE_MARKER_MIN = 10000;
constexpr int CRUISE_MARKER_MAX = 11000;
// Marker图层---路线引导点起点
constexpr int START_CIRCLE_MIN = 22000;
constexpr int START_CIRCLE_MAX = 23000;
// Marker图层---门marker
constexpr int DOOR_MARKER_MIN = 24000;
constexpr int DOOR_MARKER_MAX = 25000;
// Marker图层---路线引导点终点
constexpr int END_CIRCLE_MIN = 28000;
constexpr int END_CIRCLE_MAX = 29000;
// 路线图层
constexpr int ROUTE_MIN = 34000;
constexpr int ROUTE_MAX = 35000;
// 引导面图层
constexpr int GUIDE_AREA_MIN = 34000;
constexpr int GUIDE_AREA_MAX = 35000;
// 蚯蚓面图层
constexpr int ROUTE_LINE_AREA_MIN = 34000;
constexpr int ROUTE_LINE_AREA_MAX = 35000;
// 引导线图层
constexpr int GUIDE_LINE_MIN = 35100;
constexpr int GUIDE_LINE_MAX = 35200;
// 事件图层线，面
constexpr int HD_RTT_EVENT_MIN = 35201;
constexpr int HD_RTT_EVENT_MAX = 35300;
// 车道高亮
constexpr int HD_LANE_HIGHLIGHT_MIN = 37900;
constexpr int HD_LANE_HIGHLIGHT_MAX = 38000;
// HD白箭头图层
constexpr int HD_TBTARROW_MIN = 38100;
constexpr int HD_TBTARROW_MAX = 38100;
// 车道文字，未确定，暂用
constexpr int HD_LANE_TEXT_MIN = 38000;
constexpr int HD_LANE_TEXT_MAX = 39000;
// 安全告警显示优先级
constexpr int HD_SECURITY_WARNING_MIN = 39601;
constexpr int HD_SECURITY_WARNING_MAX = 39700;
// Marker图层---警示牌
constexpr int WARNING_TIP_MIN = 40000;
constexpr int WARNING_TIP_MAX = 41000;
// HD长实线气泡
constexpr int HD_LONG_SOLID_MIN = 43000;
constexpr int HD_LONG_SOLID_MAX = 43000;
// 伴随气泡图层
constexpr int ROUTE_COMPANION_MIN = 46000;
constexpr int ROUTE_COMPANION_MAX = 47000;
// 路线标签图层
constexpr int ROUTE_LABEL_MIN = 46000;
constexpr int ROUTE_LABEL_MAX = 47000;
// 层间气泡
constexpr int FLOOR_BUBBLE_MIN = 47100;
constexpr int FLOOR_BUBBLE_MAX = 48100;
// 出口气泡
constexpr int EXIT_BUBBLE_MIN = 47200;
constexpr int EXIT_BUBBLE_MAX = 48200;
// 入口气泡
constexpr int ENTRANCE_BUBBLE_MIN = 47300;
constexpr int ENTRANCE_BUBBLE_MAX = 48300;
// 拥堵气泡图层
constexpr int ROUTE_TRAFFIC_MIN = 49000;
constexpr int ROUTE_TRAFFIC_MAX = 50000;
// 路名气泡图层
constexpr int ROUTE_NAME_MIN = 52000;
constexpr int ROUTE_NAME_MAX = 53000;
// 路线解释性气泡：57000-58000
constexpr int ROUTE_EXPLAIN_BUBBLE_MIN = 57000;
constexpr int ROUTE_EXPLAIN_BUBBLE_MAX = 58000;
// Marker图层---起点气泡
constexpr int START_BUBBLE_MIN = 58000;
constexpr int START_BUBBLE_MAX = 59000;
// 小电子眼
constexpr int SMALL_CAMERA_MIN = 61000;
constexpr int SMALL_CAMERA_MAX = 62000;
// Marker图层---途径点
constexpr int VIA_CIRCLE_MIN = 64000;
constexpr int VIA_CIRCLE_MAX = 65000;
// 路线解释性marker
constexpr int ROUTE_EXPLAIN_MARKER_MIN = 76000;
constexpr int ROUTE_EXPLAIN_MARKER_MAX = 78000;
// Marker图层---交通事件
constexpr int TRAFFIC_EVENT_MIN = 76000;
constexpr int TRAFFIC_EVENT_MAX = 78000;
// Marker图层---终点气泡
constexpr int END_BUBBLE_MIN = 80000;
constexpr int END_BUBBLE_MAX = 81000;
// 终点名称ETA图层
constexpr int DEST_NAME_ETA_MIN = 80000;
constexpr int DEST_NAME_ETA_MAX = 81000;
// 地库出口气泡
constexpr int UNDERGROUND_PARK_MIN = 80000;
constexpr int UNDERGROUND_PARK_MAX = 81000;
// 红绿灯倒计时图层
constexpr int LIGHT_COUNTDOWN_TIMER_MIN = 81400;
constexpr int LIGHT_COUNTDOWN_TIMER_MAX = 81500;
// HD红绿灯倒计时图层
constexpr int HD_LIGHT_COUNTDOWN_TIMER_MIN = 81400;
constexpr int HD_LIGHT_COUNTDOWN_TIMER_MAX = 81500;
// Marker图层---红绿灯
constexpr int TRAFFIC_LIGHT_MIN = 81000;
constexpr int TRAFFIC_LIGHT_MAX = 81399;
// Marker图层---HD红绿灯
constexpr int HD_TRAFFIC_LIGHT_MIN = 81000;
constexpr int HD_TRAFFIC_LIGHT_MAX = 81399;
// HD伴随气泡图层
constexpr int HD_ROUTE_COMPANION_MIN = 81600;
constexpr int HD_ROUTE_COMPANION_MAX = 81600;
// 电子眼图层
constexpr int CAMERA_MIN = 82000;
constexpr int CAMERA_MAX = 83000;
// HD等灯气泡
constexpr int HD_LIGHT_WAIT_MIN = 84000;
constexpr int HD_LIGHT_WAIT_MAX = 84000;
// 等灯气泡
constexpr int LIGHT_WAIT_MIN = 84000;
constexpr int LIGHT_WAIT_MAX = 84000;
// HD转向气泡
constexpr int HD_TURN_ARROW_MIN = 85000;
constexpr int HD_TURN_ARROW_MAX = 86000;
// 易偏航气泡
constexpr int HD_YAW_NOTICE_PRI_MIN = 85000;
constexpr int HD_YAW_NOTICE_PRI_MAX = 86000;
// 点击状态的电子眼图层
constexpr int CLICKABLE_CAMERA_MIN = 86000;
constexpr int CLICKABLE_CAMERA_MAX = 87000;

// 交通点事件气泡
constexpr int HD_ROUTE_ACCIDENT_MIN = 87000;
constexpr int HD_ROUTE_ACCIDENT_MAX = 88000;
// 预警车辆
constexpr int HD_SECURITY_VEHICLE_MIN = 89000;
constexpr int HD_SECURITY_VEHICLE_MAX = 90000;

// 人车分离
constexpr int PEDESTRIAN_VEHICLE_SEP_MIN = 90000;
constexpr int PEDESTRIAN_VEHICLE_SEP_MAX = 90100;

// 经验安全预警
constexpr int SD_SAFE_WARNING_MIN = 83500;
constexpr int SD_SAFE_WARNING_MAX = 83900;

constexpr int HD_SAFE_WARNING_MIN = 83000;
constexpr int HD_SAFE_WARNING_MAX = 84000;
// 定位车标图层
constexpr int LOCATOR_MIN = 100000;
constexpr int LOCATOR_MAX = 100000;

}  // namespace Priority

namespace Style {

constexpr int COLOR_NORMAL_DAY = 0xFF777777;    // 白天普通推荐颜色
constexpr int COLOR_BETTER_DAY = 0xFF0090FF;    // 白天强烈推荐颜色
constexpr int COLOR_NORMAL_NIGHT = 0xFFFFFFFF;  // 夜间普通推荐颜色
constexpr int COLOR_BETTER_NIGHT = 0xFFFFFFFF;  // 夜间强烈推荐颜色
constexpr int COLOR_DEFAULT = 0xFFFFFFFF;       // 默认颜色
constexpr int COLOR_NORMAL = 0xff333333;        // 普通颜色

constexpr int FONT_SIZE_FOR_MAIN_TITLE = 28;
constexpr int FONT_SIZE_FOR_SUB_TITLE = 24;
constexpr int FONT_SIZE_FOR_COMPANION_MAIN_TITLE = 28;
constexpr int FONT_SIZE_FOR_COMPANION_SUB_TITLE = 24;

const static std::string FONT_NAME = "";
}  // namespace Style

}  // namespace Layer

/**
 * 图层类型
 * 按照业务内容定义
 */
enum class LayerType {
  /**
   * 无效类型
   */
  None = -1,
  /**
   * 自车定位图层 LocatorLayerConfig
   */
  Locator = 0,
  /**
   * 蚯蚓线图层  RouteLayerConfig
   * 备注: 该图层包含:蚯蚓线/动态路名/转向箭头/终点区域面
   */
  Route = 1,
  /**
   * 电子眼图层 CameraLayerConfig
   */
  SD_Camera = 2,
  /**
   * 已经淘汰
   */
  RouteName = 3,
  /**
   * 拥堵路况气泡图层 RouteTrafficLayerConfig
   */
  RouteTraffic = 4,
  /**
   * 伴随气泡图层 RouteCompanionLayerConfig
   */
  RouteCompanion = 5,
  /**
   * Marker图层  MarkerLayerConfig
   * 备注:起点/终点/途径点, 红绿灯
   */
  Marker = 6,
  /**
   * 巡航marker图层  CruiseLayerConfig
   */
  CruiseMarker = 7,
  /**
   * 终点名称和ETA  DestNameEtaLayerConfig
   */
  DestNameEta = 8,
  /**
   * 路线标签   RouteLabelLayerConfig
   */
  RouteLabel = 9,

  /**
   * 室内图切换点
   */
  IndoorEntries = 11,
  /**
   * 红绿灯倒计时 LightCountdownTimerLayerConfig
   */
  LightCountdownTimer = 12,
  /**
   * 等灯气泡图层 LightWaitLayerConfig
   */
  LightWait = 13,
  /**
   * 地库图层气泡
   */
  UndergroundParkBubble = 14,
  /**
   * 车标游离态连线
   */
  LocatorFreeLine = 15,
  /**
   * 起点区域面
   */
  StartAoiPolygon = 16,
  /**
   * 巡航电子眼图层  CruiseLayerConfig
   */
  CruiseCamera = 18,
  /**
   * 巡航鱼骨路
   */
  CruiseFishBone = 19,
  /**
   * 巡航红绿灯marker和倒计时
   */
  CruiseTrafficLight = 20,
  /**
   * 入口气泡
   */
  EntranceBubble = 21,
  /**
   * 出口气泡
   */
  ExitBubble = 22,
  /**
   * 层间气泡
   */
  FloorBubble = 23,

  PedestrianVehicleSep = 24,

  /**
   * SD经验安全预警
   */
  SafeWarning = 25,

  HD_START = 100,
  /**
   * 车道级图层  TrajectoryLayerConfig
   */
  Trajectory = HD_START + 1,
  /**
   * HD电子眼图层  CameraLayerConfig
   */
  HD_Camera = HD_START + 2,
  /**
   * 已经淘汰
   */
  HD_RouteName = HD_START + 3,
  /**
   * 拥堵路况气泡图层  RouteTrafficLayerConfig
   */
  HD_RouteTraffic = HD_START + 4,
  /**
   * 引导面图层  GuideAreaLayerConfig
   */
  GuideArea = HD_START + 5,
  /**
   * 转向气泡图层 TurnArrowLayerConfig
   */
  HD_TurnArrow = HD_START + 6,
  /**
   * 引导线图层  GuideLineLayerConfig
   */
  GuideLine = HD_START + 7,
  /**
   * 蚯蚓面图层  RouteLineAreaLayerConfig
   */
  RouteLineArea = HD_START + 8,
  /**
   * 沿车道中心线显示文字图层 LaneTextLayerConfig
   */
  HD_LaneText = HD_START + 9,

  HD_DestNameEta = HD_START + 10,
  /**
   * 特殊车道高亮提示
   */
  HD_LaneHighlight = HD_START + 11,
  /**
   * HD长实线气泡图层
   */
  HD_LongSolid = HD_START + 12,
  /**
   * HD等灯气泡图层 HDLightWaitLayerConfig
   */
  HD_LightWait = HD_START + 13,
  /**
   * HD红绿灯倒计时 LightCountdownTimerLayerConfig
   */
  HD_LightCountdownTimer = HD_START + 14,

  /**
   * HD伴随路线配置
   */
  HD_CompanionAreaLayer = HD_START + 15,
  /**
   * HD伴随路线气泡
   */
  HD_RouteCompanionBubble = HD_START + 16,
  /**
   * HD白箭头
   */
  HD_TBTArrow = HD_START + 17,
  /**
   * 等宽引导面
   */
  EqualWidthGuideArea = HD_START + 18,
  /**
   * 障碍物
   */
  HD_Obstacle = HD_START + 19,
  /**
   * HD巡航红绿灯marker和倒计时
   */
  HDCruiseTrafficLight = HD_START + 20,
  /**
   * HD车标
   */
  HD_Locator = HD_START + 21,
  /**
   * HD车道线的长白实线
   */
  HDLaneEdge = HD_START + 22,
  /**
   * HD推荐车道
   */
  HD_RecommendLane = HD_START + 23,
  /**
   * 禁行道路标志
   */
  HD_ForbiddenRoadSign = HD_START + 24,
  /**
   *  易偏航气泡
   */
  HDYawNotice = HD_START + 25,
  HD_SecurityAlert = HD_START + 27,
  HD_SecurityAlertRight = HD_START + 31,
  /**
   * HD交通点事件气泡
   */
  HDRouteAccident = HD_START + 28,

  HD_EventAccident = HD_START + 29,

  /**
   * HD经验安全预警
   */
  HD_SafeWarning = HD_START + 30,
  /**
   * 斑马线警示面
   */
  HD_ZebraWarningArea = HD_START + 31,
};

/**
 * Marker类型元素子类型
 */
enum class MarkerType {
  /**
   * 无效类型
   */
  None = -1,
  /**
   * 路线引导点起点
   */
  START_CIRCLE = 0,
  /**
   * 路线途径点
   */
  VIA_CIRCLE = 1,
  /**
   * 路线引导点终点
   */
  END_CIRCLE = 2,
  /**
   * 起点气泡
   * 备注: 暂时无该类型需求，定义在这里保持完整性
   */
  START_BUBBLE = 3,
  /**
   * 终点气泡
   * 备注: 仅主底图需要，鹰眼图不需要
   */
  END_BUBBLE = 4,
  /**
   * 导航路线上红绿灯
   */
  TRAFFIC_LIGHT = 5,
  /**
   * 导航路线上警示牌信息
   */
  WARNING_TIP = 6,
  /**
   * 交通点事件
   */
  TRAFFIC_EVENT = 7,
  /**
   * 算路添加的途经点 -- 被动途经点
   */
  STRATEGY_VIA = 10,
  /**
   * 楼层切换点marker
   */
  FLOOR_MARKER = 11,
  /**
   * 路线解释性marker
   */
  ROUTE_EXPLAIN_MARKER = 12,
  /**
   * 路线点事件marker
   */
  ROUTE_EXPLAIN_EVENT_MARKER = 13,
  /**
   * 门marker
   */
  DOOR_MARKER = 14,
  /**
   * 向下开始是hd
   */
  HD_START = 1000,
  /**
   * 路线引导点起点 hd下引导点形态废弃
   * @deprecated
   */
  HD_START_CIRCLE = HD_START + 0,
  /**
   * 路线途径点
   */
  HD_VIA_CIRCLE = HD_START + 1,
  /**
   * 路线引导点终点
   */
  HD_END_CIRCLE = HD_START + 2,
  /**
   * 起点气泡
   * 备注: 暂时无该类型需求，定义在这里保持完整性
   */
  HD_START_BUBBLE = HD_START + 3,
  /**
   * 终点气泡
   * 备注: 仅主底图需要，鹰眼图不需要
   */
  HD_END_BUBBLE = HD_START + 4,
  /**
   * 导航路线上警示牌信息
   */
  HD_WARNING_TIP = HD_START + 6,
  /**
   * 交通点事件
   */
  HD_TRAFFIC_EVENT = HD_START + 7,
  /**
   * 算路添加的途经点 -- 被动途经点
   */
  HD_STRATEGY_VIA = HD_START + 10,
  /**
   * 导航路线上红绿灯
   */
  HD_TRAFFIC_LIGHT = HD_START + 11
};
// 重载自定义枚举 运算符 "<<"
std::ostream& operator<<(std::ostream& out, const LayerType& layer_type);
// 重载自定义枚举 运算符 "<<"
std::ostream& operator<<(std::ostream& out, const MarkerType& marker_type);

/**
 * Icon绘制类型
 */
enum class IconType {
  IconType_2D_GeoCoord_GeoAngle,     // 2D Icon, 地理坐标，地理角度(正交绘制于屏幕上)
  IconType_2D_GeoCoord_ScreenAngle,  // 2D Icon, 地理坐标，屏幕角度
  IconType_2D_ScreenCoord,           // 2D Icon, 类似于屏幕UI
  IconType_3D                        // 3D Icon, 贴于地面
};

class LayerTypeInfo {
 public:
  explicit LayerTypeInfo(LayerType main_type, int sub_type = static_cast<int>(MarkerType::None));
  LayerType layer_type_ = LayerType::None;
  /**
   * sub layer无要填写这个
   * marker layer用这个区分get的是哪一个layer config
   */
  int sub_type_ = static_cast<int>(MarkerType::None);
  friend std::ostream& operator<<(std::ostream& out, const LayerTypeInfo& config);
};

// 图层基础配置
class LayerBaseConfig {
 public:
  enum class AvoidRouteType { All, MainRoute };

 public:
  LayerBaseConfig(LayerType main_type, int sub_type);

 public:
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param pother
   * @return   true:相同，false:不相同
   */
  virtual bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const { return true; }

  /**
   * 核查并修正配置
   */
  void CheckAndRectifyConfig();

  /**
   * 判断类型是否相同
   * @param other
   * @return
   */
  bool IsSameType(const LayerBaseConfig& other) const;
  bool operator==(const LayerBaseConfig& other) const;
  bool operator!=(const LayerBaseConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LayerBaseConfig& config);

 public:
  LayerType main_type_ = LayerType::None;                        // 主图层类型
  int sub_type_ = -1;                                            // 该图层中元素类型(有些图层内元素耦合中但元素类型不同)
  bool enabled_ = true;                                          // 该图层是否启用(false:该图层不做任何数据处理)
  bool visible_ = true;                                          // 该图层是否显示(false:该图层进行数据处理但不展示)
  bool visible_in_overview_map_ = true;                          // 该图层是否在鹰眼图显示
  bool need_avoid_others_ = true;                                // 该图层元素是否参与避让
  bool need_avoid_route_ = false;                                // 该图层元素是否避让路线
  AvoidRouteType avoid_route_type_ = AvoidRouteType::MainRoute;  // 路线避让类型，默认仅避让主路线
  bool is_clickable_ = false;                                    // 该图层元素是否可点击(默认不可点击)
  char min_display_level_ = 10;                                  // 该图层元素允许显示的最小比例尺(默认值:10, 大约2公里)
  char max_display_level_ =
      Layer::Level::MAX_LEVEL;           // 该图层元素允许显示的最大比例尺(默认值:30, 超过了引擎显示的最大范围)
  unsigned min_display_priority_ = 0;    // 该图层元素允许被设置的最小优先级()
  unsigned max_display_priority_ = 0;    // 该图层元素允许被设置的最大优先级()
  unsigned min_margin_with_others_ = 0;  // 该图层Marker与其他Marker的最小间距(默认为0)
  bool debug_enable = false;  // 是否启动debug调试 (默认false, 不支持对外，启动debug一直marker绘制调试框会显示出来)
  bool enable_change_by_map_mode_ = true;        // 样式是否随昼夜模式切换而切换
  unsigned min_display_priority_clickable_ = 0;  // 该图层元素点击后允许被设置的最小优先级
  unsigned max_display_priority_clickable_ = 0;  // 该图层元素点击后允许被设置的最大优先级
};

class BubbleLayerConfig : public LayerBaseConfig {
 public:
  int32_t left_padding = 0;
  int32_t right_padding = 0;
  int32_t top_padding = 0;
  int32_t bottom_padding = 0;

 public:
  BubbleLayerConfig(LayerType main_type, int sub_type) : LayerBaseConfig(main_type, sub_type) {}

  /**
   * 统一的出现/消失动画时长
   * @param duration_s 单位:秒
   */
  static void SetShowHideAnimationDurationS(float duration_s);

  /**
   * 统一的出现/消失动画时长
   * @return 单位:秒
   */
  static float GetShowHideAnimationDurationS();

  /**
   * 设置杆移动的范围角度
   * @param angle_degree 15~45度
   */
  static void SetPollRangeAngle(float angle_degree);

  /**
   * 获取杆移动的范围角度
   */
  static float GetPollRangeAngle();

 public:
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param pother
   * @return   true:相同，false:不相同
   */
  virtual bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const { return true; }

  bool operator==(const BubbleLayerConfig& other) const;
  bool operator!=(const BubbleLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const BubbleLayerConfig& config);
};
/**
 * 描述气泡或文字显示的方向
 * 备注: 气泡相对扎点的方向(不是尖尖相对图片的方向);
 */
/*********************************/
/*              Top              */
/*  LeftTop           RightTop   */
/*           -     -             */
/*  Left      Center      Right  */
/*            -    -             */
/*  LeftBottom        RightBottom*/
/*              Bottom           */
/*********************************/
enum class MapBubbleDisplayDirection {
  /**
   * 左上方
   */
  LeftTop = 0,
  /**
   * 右上方
   */
  RightTop = 1,
  /**
   * 左下方
   */
  LeftBottom = 2,
  /**
   * 右下方
   */
  RightBottom = 3,
  /**
   * 中心
   */
  Center = 4,
  /**
   * 左侧
   */
  Left = 5,
  /**
   * 上方
   */
  Top = 6,
  /**
   * 右方
   */
  Right = 7,
  /**
   * 下方
   */
  Bottom = 8,
};

/**
 * 描述气泡类型
 */
enum class BubbleLayerType {
  /**
   * 非法类型
   */
  None = -1,
  /**
   * 前方路名气泡
   */
  RouteName = 0,
  /**
   * 前方路况气泡
   */
  RouteTraffic = 1,
  /**
   * 伴随路线气泡
   */
  RouteCompanion = 2,
  /**
   * 电子眼气泡
   */
  RouteCamera = 3,
  /**
   * 动态资源
   */
  DynamicIcon = 4,
  /**
   * 默认资源
   */
  DefaultIcon = 5,
  /**
   * 终点名称和ETA
   */
  DestNameEta = 6,
  /**
   * 路线标签
   */
  RouteLabel = 7,
  /**
   * 拥堵预测（解释性信息）
   */
  ExplainBubble = 8,
  /**
   * 有文字数据的marker气泡
   */
  MarkerBubble = 9,
  /**
   * 转向气泡
   */
  TurnArrowBubble = 10,
  /**
   * 红绿灯倒计时气泡
   */
  LightCountdownTimerBubble = 11,
  /**
   * HD长实线气泡
   */
  HD_LongSolidBubble = 12,
  /**
   * 室内图切换点气泡
   */
  IndoorEntriesBubble = 13,
  /**
   * 等灯气泡
   */
  LightWaitBubble = 14,
  /**
   * 途径点
   */
  ViaCircle = 20,

  /*
   * marker资源
   */
  Marker = 21,

  /**
   * HD的伴随气泡,
   * 废弃，伴随气泡使用 RouteCompanion。sd/hd使用 scene_区分
   */
  HD_RouteCompanion = 22,
  /**
   * 充电桩
   */
  ChargingPoint = 23,
  /**
   * 多红绿灯倒计时气泡
   */
  MultiLightCountdownTimerBubble = 24,
  /**
   * 地库tips气泡类型
   */
  UndergroundPark = 25,

  /**
   * HD的拥堵气泡
   */
  HDRouteTraffic = 26,

  /**
   * 经验安全预警气泡
   */
  SafeWarning = 28,
};
/**
 * 气泡资源回调类型
 */
enum class BubbleDrawType {
  /**
   * 非法类型
   */
  None = -1,
  /**
   * 前方路名气泡
   */
  RouteName = 0,
  /**
   * 前方路况气泡
   */
  RouteTraffic = 1,
  /**
   * 伴随路线气泡
   */
  RouteCompanion = 2,
  /**
   * 电子眼气泡
   */
  RouteCamera = 3,
  /**
   * 动态资源
   */
  DynamicIcon = 4,
  /**
   * 默认资源
   */
  DefaultIcon = 5,
  /**
   * 终点名称和ETA
   */
  DestNameEta = 6,
  /**
   * 路线标签
   */
  RouteLabel = 7,
  /**
   * 拥堵预测（解释性信息）
   */
  ExplainBubble = 8,
  /**
   * 有文字数据的marker气泡
   */
  MarkerBubble = 9,
  /**
   * 转向气泡
   */
  TurnArrowBubble = 10,
  /**
   * 红绿灯倒计时气泡
   */
  LightCountdownTimerBubble = 11,
  /**
   * HD长实线气泡
   */
  HD_LongSolidBubble = 12,
  /**
   * 室内图切换点气泡
   */
  IndoorEntriesBubble = 13,
  /**
   * 等灯气泡
   */
  LightWaitBubble = 14,
  /**
   * 途径点
   */
  ViaCircle = 20,

  /*
   * marker资源
   */
  Marker = 21,

  /**
   * HD的伴随气泡,
   * 废弃，伴随气泡使用 RouteCompanion。sd/hd使用 scene_区分
   */
  HD_RouteCompanion = 22,
  /**
   * 充电桩
   */
  ChargingPoint = 23,
  /**
   * 多红绿灯倒计时气泡
   */
  MultiLightCountdownTimerBubble = 24,
  /**
   * 地库tips气泡类型
   */
  UndergroundPark = 25,

  /**
   * HD的拥堵气泡
   */
  HDRouteTraffic = 26,
  /**
   * 力导向bubble的锚点
   */
  ForceDot = 27,
  /**
   * 力导向bubble的连杆
   */
  ForcePoll = 28,

  /**
   * 易偏航气泡
   */
  HDYawNotice = 30,
  /**
   * 引导面
   */
  GuideArea = 31,
  /**
   * 引导线
   */
  GuideLine = 32,

  /**
   * 交通点事件气泡
   */
  HDRouteAccident = 29,

  /**
   * 经验安全预警气泡
   */
  SafeWarning = 33,
};

enum class GuideOptionalType {
  Invalid = -1,
  /**
   * 路况纹理
   */
  TrafficTexture = 0,
  /**
   * 路况描边纹理
   */
  TrafficStrokeTexture = 1,
  /**
   * 流向箭头纹理
   */
  ArrowTexture = 2,
  /**
   * 流向箭头透明度纹理
   */
  ArrowTransparencyTexture = 3,
};

/**
 * 底图气泡基础配置
 */
class BubbleLayerBaseConfig {
 public:
  BubbleLayerBaseConfig(const BubbleLayerType& type, const int priority);

 public:
  bool operator==(const BubbleLayerBaseConfig& other) const;
  bool operator!=(const BubbleLayerBaseConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const BubbleLayerBaseConfig& bubble_config);
  BubbleLayerType GetType() const;
  const char* GetTypeStr() const;
  void SetType(BubbleLayerType type);

 private:
  /**
   * 计算候选位置允许设置的最大值
   * 备注: 底图引擎有限制
   */
  static const int display_candidate_position_max_count_ = 8;
  /**
   * 气泡在每个候选点允许显示的方向最大值
   * 备注: 底图引擎有限制
   */
  static const int display_candidate_direction_max_count_ = 4;

 public:
  /**
   * 核查并修正配置
   * 1. 修正显示方向，去重复并限制最大数量
   * 2. 调整扎点最大数
   */
  void CheckAndRectifyConfig();

 private:
  /**
   * 气泡类型
   */
  BubbleLayerType type_;

 public:
  /**
   * 气泡显示字体名称
   * 默认值为空，空则表示使用系统的默认字体;
   */
  std::string font_name = Layer::Style::FONT_NAME;
  /**
   * 气泡扎点个数(默认4)
   */
  int display_candidate_position_count_;
  /**
   * 气泡方式的候选方向(具有优先级的列表)
   */
  std::vector<MapBubbleDisplayDirection> display_candidate_direction_vector_;
  /**
   * 是否需要需要将扎点结果回调给客户端
   * 备注: 结果通过回调函数发送
   */
  bool need_callback_candidate_positions_;

  /**
   * Camera 变化情况下 重新计算最小时间间隔
   * 性能考虑
   */
  static const int min_interval_when_camera_changed_ = 3;
  /**
   * 扎点复用时判断一个点是否在路线上采用的最大距离阀值；
   * 点与路线距离不超过3米都认为还在路线上;
   */
  static const int max_distance_point_on_polyline_ = 3;
};

/**
 * 前方路名气泡扎点和显示配置
 * 昼夜模式样式不同，需要重新设置
 */
class RouteNameBubbleConfig : public BubbleLayerBaseConfig {
 public:
  RouteNameBubbleConfig();

 public:
  bool operator==(const RouteNameBubbleConfig& other) const;
  bool operator!=(const RouteNameBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteNameBubbleConfig& bubble_config);

 public:
  int font_color = Layer::Style::COLOR_DEFAULT;            // 字体颜色
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 字体大小
};

/**
 * 拥堵气泡扎点和显示配置
 */
class RouteTrafficBubbleConfig : public BubbleLayerBaseConfig {
 public:
  RouteTrafficBubbleConfig();
  explicit RouteTrafficBubbleConfig(const BubbleLayerType& type);

 public:
  bool operator==(const RouteTrafficBubbleConfig& other) const;
  bool operator!=(const RouteTrafficBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteTrafficBubbleConfig& bubble_config);

 public:
  int font_color = Layer::Style::COLOR_DEFAULT;            // 字体颜色
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 字体大小
  /**
   * 白天模式 主标题/第一行 文字颜色，0xAARRGGBB
   */
  int font_color_for_main_title_day = 0xFFFFFFFF;
  /**
   * 白天模式 副标题/第二行 文字颜色，0xAARRGGBB
   */
  int font_color_for_sub_title_day = 0xFFFF2A00;
  /**
   * 白天模式 主标题/第一行 背景颜色，0xAARRGGBB
   */
  int background_color_for_main_title_day = 0xFFFF2A00;
  /**
   * 白天模式 副标题/第二行 背景颜色，0xAARRGGBB
   */
  int background_color_for_sub_title_day = 0xFFFFFFFF;
  /**
   * 夜间模式 主标题/第一行 文字颜色，0xAARRGGBB
   */
  int font_color_for_main_title_night = 0xFFF67575;
  /**
   * 夜间模式 副标题/第二行 文字颜色，0xAARRGGBB
   */
  int font_color_for_sub_title_night = 0xFF691E1E;
  /**
   * 夜间模式 主标题/第一行 背景颜色，0xAARRGGBB
   */
  int background_color_for_main_title_night = 0xFF691E1E;
  /**
   * 夜间模式 副标题/第二行 背景颜色，0xAARRGGBB
   */
  int background_color_for_sub_title_night = 0xFFC4C4C4;
};
/**
 * 拥堵气泡扎点和显示配置
 */
class HDRouteTrafficBubbleConfig : public RouteTrafficBubbleConfig {
 public:
 public:
  HDRouteTrafficBubbleConfig();

 public:
  bool operator==(const HDRouteTrafficBubbleConfig& other) const;
  bool operator!=(const HDRouteTrafficBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDRouteTrafficBubbleConfig& bubble_config);
  /**
   * 自车安全距离，此范围内气泡无法扎点
   * 单位m
   */
  int car_move_to_lerance = 50;
  /**
   * 气泡被碰撞后扎点移动步长
   * 单位m
   */
  int avoided_move_step = 10;
};
/**
 * 伴随气泡扎点和显示配置
 */
class RouteCompanionBubbleConfig : public BubbleLayerBaseConfig {
 public:
  RouteCompanionBubbleConfig();

 public:
  bool operator==(const RouteCompanionBubbleConfig& other) const;
  bool operator!=(const RouteCompanionBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteCompanionBubbleConfig& bubble_config);

 public:
  int font_color_for_normal_day = Layer::Style::COLOR_DEFAULT;                      // 普通推荐颜色
  int font_color_for_better_day = Layer::Style::COLOR_DEFAULT;                      // 强烈推荐颜色
  int font_color_for_normal_night = Layer::Style::COLOR_DEFAULT;                    // 普通推荐颜色
  int font_color_for_better_night = Layer::Style::COLOR_DEFAULT;                    // 强烈推荐颜色
  int font_size_for_main_title = Layer::Style::FONT_SIZE_FOR_COMPANION_MAIN_TITLE;  // 主标题字体大小
  int font_size_for_sub_title = Layer::Style::FONT_SIZE_FOR_COMPANION_SUB_TITLE;    // 主标题字体大小
  static const int think_time_same_threshold_ = 60 * 2;   // 差距秒差距内认为 时间相近(默认值 60 * 2)
  static const int think_distance_same_threshold_ = 100;  // 差距米差距内认为 距离接近(默认值 100)
};

class HDRouteCompanionBubbleConfig : public BubbleLayerBaseConfig {
 public:
  HDRouteCompanionBubbleConfig();
};

/**
 * 电子眼扎点和显示配置
 */
class RouteCameraBubbleConfig : public BubbleLayerBaseConfig {
 public:
  RouteCameraBubbleConfig();

 public:
  bool operator==(const RouteCameraBubbleConfig& other) const;
  bool operator!=(const RouteCameraBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteCameraBubbleConfig& bubble_config);

 public:
  /**
   * 相邻的小电子眼之间显示最小间隔，单位:屏幕像素
   * 默认值:0，不做同源避让
   */
  int small_camera_margin = 0;
  /**
   * 小电子眼的显示scale
   * 默认值:0.65f
   */
  float small_camera_scale = 0.65f;
  /**
   * 大ICON：最多显示2个位置(如果有区间测速，最多4个)
   * 默认值:2个;
   */
  int max_position_count_for_large_camera = 2;
  /**
   * 大ICON：同一位置最多显示2个电子眼
   * 默认值:2个
   */
  int max_camera_count_at_same_position = 2;
  /**
   * 小ICON最多显示的个数
   * 默认值:2个
   */
  int max_num_for_small_camera = 2;
  /**
   * 显示大ICON电子眼的距离
   * 单位:米
   * 默认值:850;
   */
  int distance_for_show_large_camera = 850;
  /**
   * 显示大ICON电子眼的距离+buffer=850M+100M,
   * 单位:米
   * 默认值:950;
   */
  int distance_for_show_large_camera_with_buffer = 950;
  /**
   * 显示小ICON电子眼的距离，2公里
   * 单位:米
   * 默认值:2000;
   */
  int distance_for_show_camera = 2000;
  /**
   * 电子眼位置距离不超过此值，视为同一位置
   * 单位:米
   * 默认值: 50;
   */
  int distance_for_same_position_camera;
  /**
   * 显示小ICON电子眼的距离，500米
   * 单位:米
   * 默认值:500;
   */
  int distance_for_show_camera_hd;
  /**
   * 电子眼位置距离不超过此值，视为同一位置
   * 单位:米
   * 默认值: 5;
   */
  int distance_for_same_position_camera_hd;
  /**
   * 小电子眼避让白箭头，排除路口a、b点前后距离阈值
   * 单位:米
   * 默认值: 60;
   */
  int distance_avoid_turn_arrow_for_small_camera = 60;
  /**
   * 非区间限速电子眼字体颜色，0xAARRGGBB
   */
  int font_color_for_normal_type = 0xFF333333;
  /**
   * 非区间限速电子眼背景颜色，0xAARRGGBB
   */
  int background_color_for_normal_type = 0xFFFFFFFF;
  /**
   * 区间限速电子眼标题颜色，0xAARRGGBB
   */
  int title_color_for_speed_test_type = 0xFFFFFFFF;
  /**
   * 白天模式 区间限速电子眼起终点文字颜色，0xAARRGGBB
   */
  int font_color_for_speed_test_type_day = 0xE6000000;
  /**
   * 夜间模式 区间限速电子眼起终点文字颜色，0xAARRGGBB
   */
  int font_color_for_speed_test_type_night = 0xFFFFFFFF;
};
/**
 * 终点名称ETA气泡配置
 */
class DestNameEtaBubbleConfig : public BubbleLayerBaseConfig {
 public:
  DestNameEtaBubbleConfig();

 public:
  bool operator==(const DestNameEtaBubbleConfig& other) const;
  bool operator!=(const DestNameEtaBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const DestNameEtaBubbleConfig& bubble_config);

 public:
  /**
   * 终点名称文字大小
   */
  int dest_name_text_size = 32;
  /**
   * 终点名称文字颜色白天，0xAARRGGBB
   */
  int dest_name_text_color_day = 0xFF1C1C1C;
  /**
   * 终点名称文字颜色黑夜，0xAARRGGBB
   */
  int dest_name_text_color_night = 0xFFFFFFFF;
  /**
   * 终点名称描边宽度
   */
  int dest_name_stroke_width = 4;
  /**
   * 终点名称描边颜色白天，0xAARRGGBB
   */
  int dest_name_stroke_color_day = 0xFFFFFFFF;
  /**
   * 终点名称描边颜色黑夜，0xAARRGGBB
   */
  int dest_name_stroke_color_night = 0xFF1A1A1A;
  /**
   * 到达时间文字大小
   */
  int arrive_text_size = 24;
  /**
   * 到达时间文字颜色白天，0xAARRGGBB
   */
  int arrive_text_color_day = 0xFFFFFFFF;
  /**
   * 到达时间文字颜色黑夜，0xAARRGGBB
   */
  int arrive_text_color_night = 0xFFFFFFFF;
  /**
   * 到达时间背景颜色白天，0xAARRGGBB
   */
  int arrive_background_color_day = 0xB3000000;
  /**
   * 到达时间背景颜色黑夜，0xAARRGGBB
   */
  int arrive_background_color_night = 0xB33E4A5E;
};
/**
 * 路线标签气泡配置
 */
class RouteLabelBubbleConfig : public BubbleLayerBaseConfig {
 public:
  RouteLabelBubbleConfig();

 public:
  bool operator==(const RouteLabelBubbleConfig& other) const;
  bool operator!=(const RouteLabelBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteLabelBubbleConfig& bubble_config);

 public:
  /**
   * 白天字体颜色
   */
  int font_color_day = Layer::Style::COLOR_BETTER_DAY;
  /**
   * 夜间字体颜色
   */
  int font_color_night = Layer::Style::COLOR_BETTER_NIGHT;
  /**
   * 字体大小
   */
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;
};
/**
 * 转向气泡配置
 */
class TurnArrowBubbleConfig : public BubbleLayerBaseConfig {
 public:
  TurnArrowBubbleConfig();

 public:
  bool operator==(const TurnArrowBubbleConfig& other) const;
  bool operator!=(const TurnArrowBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const TurnArrowBubbleConfig& bubble_config);
};
/**
 * 红绿灯倒计时气泡配置
 */
class LightCountdownTimerBubbleConfig : public BubbleLayerBaseConfig {
 public:
  LightCountdownTimerBubbleConfig();

 public:
  bool operator==(const LightCountdownTimerBubbleConfig& other) const;
  bool operator!=(const LightCountdownTimerBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LightCountdownTimerBubbleConfig& bubble_config);
};
/**
 * 长实线气泡策略配置
 */
class HDLongSolidBubbleConfig : public BubbleLayerBaseConfig {
 public:
  HDLongSolidBubbleConfig();

 public:
  bool operator==(const HDLongSolidBubbleConfig& other) const;
  bool operator!=(const HDLongSolidBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDLongSolidBubbleConfig& bubble_config);

 public:
  int font_color = Layer::Style::COLOR_NORMAL;             // 字体颜色
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 字体大小
};
/**
 * 等灯气泡配置
 */
class LightWaitBubbleConfig : public BubbleLayerBaseConfig {
 public:
  LightWaitBubbleConfig();

 public:
  bool operator==(const LightWaitBubbleConfig& other) const;
  bool operator!=(const LightWaitBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LightWaitBubbleConfig& bubble_config);

 public:
  int font_color = Layer::Style::COLOR_DEFAULT;            // 字体颜色
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 字体大小
};
/**
 * 室内图切换点气泡扎点和显示配置
 */
class IndoorEntriesBubbleConfig : public BubbleLayerBaseConfig {
 public:
  IndoorEntriesBubbleConfig();

  bool operator==(const IndoorEntriesBubbleConfig& other) const;
  bool operator!=(const IndoorEntriesBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const IndoorEntriesBubbleConfig& bubble_config);

 public:
  int font_color = Layer::Style::COLOR_DEFAULT;            // 字体颜色
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 字体大小
};

class YawNoticeConfig : public BubbleLayerConfig {
 public:
  YawNoticeConfig() : BubbleLayerConfig(LayerType::HDYawNotice, static_cast<int>(MarkerType::None)) {
    enabled_ = false;
    visible_ = false;
    need_avoid_route_ = true;
    need_avoid_others_ = true;
    min_display_level_ = Layer::Level::HD_YAW_NOTICE_LEVEL_MIN;
    max_display_level_ = Layer::Level::HD_YAW_NOTICE_LEVEL_MAX;
    min_display_priority_ = Layer::Priority::HD_YAW_NOTICE_PRI_MIN;
    max_display_priority_ = Layer::Priority::HD_YAW_NOTICE_PRI_MAX;
  }

  bool operator==(const YawNoticeConfig& other) const { return BubbleLayerConfig::operator==(other); }

  bool operator!=(const YawNoticeConfig& other) const { return !(*this == other); }

  std::string to_string() const {
    std::stringstream ss;
    ss << "YawNoticeConfig:{" << static_cast<BubbleLayerConfig>(*this) << "}";
    return ss.str();
  }

 public:
  int32_t showDistance = 2000;  // 提前显示距离
};

/**
 * 交通点事件气泡图层
 */

class RouteAccidentConfig : public BubbleLayerConfig {
 public:
  RouteAccidentConfig() : BubbleLayerConfig(LayerType::HDRouteAccident, static_cast<int>(MarkerType::None)) {
    enabled_ = false;
    visible_ = false;
    min_display_level_ = Layer::Level::HD_ROUTE_ACCIDENT_MIN;
    max_display_level_ = Layer::Level::HD_ROUTE_ACCIDENT_MAX;
    min_display_priority_ = Layer::Priority::HD_ROUTE_ACCIDENT_MIN;
    max_display_priority_ = Layer::Priority::HD_ROUTE_ACCIDENT_MAX;
  }

  bool operator==(const RouteAccidentConfig& other) const { return BubbleLayerConfig::operator==(other); }

  bool operator!=(const RouteAccidentConfig& other) const { return !(*this == other); }

  std::string to_string() const {
    std::stringstream ss;
    ss << "RouteAccidentConfig:{" << static_cast<BubbleLayerConfig>(*this) << "}";
    return ss.str();
  }

 public:
  int32_t showDistance = 1000;
  double eventUrlDistance = 1500;  // 事件url回调距离
};

/**
 * 图层显示策略参数
 */
class BubbleLayerConfigContainer {
 public:
  BubbleLayerConfigContainer();
  bool operator==(const BubbleLayerConfigContainer& other) const;
  bool operator!=(const BubbleLayerConfigContainer& other) const;
  friend std::ostream& operator<<(std::ostream& out, const BubbleLayerConfigContainer& config_container);

 public:
  /**
   * 路名气泡策略配置
   */
  RouteNameBubbleConfig route_name_bubble_config_;
  /**
   * 拥堵气泡策略配置
   */
  RouteTrafficBubbleConfig route_traffic_bubble_config_;
  /**
   * 伴随气泡策略配置
   */
  RouteCompanionBubbleConfig route_companion_bubble_config_;
  /**
   * 电子眼气泡策略
   */
  RouteCameraBubbleConfig route_camera_bubble_config_;
  /**
   * 终点名称ETA气泡策略配置
   */
  DestNameEtaBubbleConfig dest_name_eta_bubble_config_;
  /**
   * HD终点名称ETA气泡策略配置
   */
  DestNameEtaBubbleConfig hd_dest_name_eta_bubble_config_;
  /**
   * 路线标签气泡配置
   */
  RouteLabelBubbleConfig route_label_bubble_config_;
  /**
   * HD电子眼气泡策略
   */
  RouteCameraBubbleConfig hd_route_camera_bubble_config_;
  /**
   * HD拥堵气泡策略配置
   */
  HDRouteTrafficBubbleConfig hd_route_traffic_bubble_config_;
  /**
   * HD路名气泡策略配置
   */
  RouteNameBubbleConfig hd_route_name_bubble_config_;
  /**
   * 转向气泡配置
   */
  TurnArrowBubbleConfig turn_arrow_bubble_config_;
  /**
   * 巡航电子眼气泡策略
   */
  RouteCameraBubbleConfig cruise_route_camera_bubble_config_;
  /**
   * 红绿灯倒计时气泡配置
   */
  LightCountdownTimerBubbleConfig light_countdown_timer_bubble_config_;
  /**
   * HD长实线气泡策略
   */
  HDLongSolidBubbleConfig hd_long_solid_bubble_config_;

  /**
   * HD 伴随气泡策略配置
   */
  HDRouteCompanionBubbleConfig hd_route_companion_bubble_config_;

  /**
   * 室内图切换点气泡策略
   */
  IndoorEntriesBubbleConfig indoor_entries_bubble_config_;
  /**
   * 等灯气泡策略
   */
  LightWaitBubbleConfig light_wait_bubble_config_;
  /**
   * HD红绿灯倒计时气泡配置
   */
  LightCountdownTimerBubbleConfig hd_light_countdown_timer_bubble_config_;
  /**
   * 巡航态下红绿灯倒计时气泡配置
   */
  LightCountdownTimerBubbleConfig cruise_light_countdown_timer_bubble_config_;
  /**
   * 巡航态下红绿灯倒计时气泡配置
   */
  LightCountdownTimerBubbleConfig hd_cruise_light_countdown_timer_bubble_config_;
  /**
   * HD巡航拥堵气泡策略配置
   */
  RouteTrafficBubbleConfig hd_cruise_route_traffic_bubble_config_;
  /**
   * 交通点事件
   */
  RouteAccidentConfig hd_route_accident_bubble_config_;
};

/**
 * 前方路名气泡扎点和显示配置
 * 昼夜模式样式不同，需要重新设置
 */
class MarkerBubbleConfig : public BubbleLayerBaseConfig {
 public:
  MarkerBubbleConfig();

 public:
  bool operator==(const MarkerBubbleConfig& other) const;
  bool operator!=(const MarkerBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const MarkerBubbleConfig& bubble_config);

 public:
  int font_color = Layer::Style::COLOR_DEFAULT;            // 字体颜色
  int font_size = Layer::Style::FONT_SIZE_FOR_MAIN_TITLE;  // 字体大小
};

/**
 * 气泡扎点位置信息
 */
class BubbleCandidatePositionDescriptor {
 public:
  BubbleCandidatePositionDescriptor();

  void Reset();

 public:
  /**
   * 气泡对应路线信息
   */
  std::string route_id_;
  /**
   * 气泡候选位置信息
   */
  std::vector<mapbase::GeoCoordinate> candidate_positions_;
};

/**
 * 候选位置容器列表
 */
struct BubbleCandidatePositionContainer {
  /**
   * 所有路线的候选位置信息
   */
  std::vector<BubbleCandidatePositionDescriptor> candidate_position_descriptor_;
};
struct HDMappingPos;
/**
 * 气泡扎点位置信息
 */
class HDBubbleCandidatePositionDescriptor {
 public:
  HDBubbleCandidatePositionDescriptor();

  void Reset();

 public:
  /**
   * 气泡对应路线信息
   */
  std::string route_id_;
  /**
   * 气泡候选位置信息
   */
  std::vector<mapbase::RoutePos> sd_candidate_positions_;
  std::vector<mapbase::GeoCoordinateZ> hd_candidate_positions_;
  /**
   * 数据朝向
   */
  std::pair<mapbase::GeoCoordinateZ, mapbase::GeoCoordinateZ> line_vec_;

  /// hd坐标对应的laneGroup index，sdmap index， ratio等
  std::vector<std::shared_ptr<HDMappingPos>> hd_candidate_positions_info_vec;

  std::vector<MapBubbleDisplayDirection> bubble_directions_;
};

/**
 * 候选位置容器列表
 */
struct HDBubbleCandidatePositionContainer {
  /**
   * 所有路线的候选位置信息
   */
  std::vector<HDBubbleCandidatePositionDescriptor> candidate_position_descriptor_;
};
/**
 * 定位图层配置
 */
class LocatorLayerConfig : public LayerBaseConfig {
 public:
  LocatorLayerConfig();
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param other
   * @return   true:相同，false:不相同
   */
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;

  bool operator==(const LocatorLayerConfig& other) const;
  bool operator!=(const LocatorLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LocatorLayerConfig& config);

 public:
  int map_scale_level_for_overview_follow_ = 18;        // 鹰眼图在Follow模式下的显示级别(默认18)
  bool enable_overview_map_animation_ = true;           // 是否允许鹰眼图动画
  bool heading_up_for_overview_follow_ = true;          // 鹰眼图follow模式是否车头向上
  float max_skew_ = -1.0f;                              // 车标的最大俯仰角，单位度，默认使用底图俯仰角设置
  bool pause_location_update_ = false;                  // 暂停车标位置刷新
  int locator_update_without_animation_distance_ = 80;  // 导航中连续位置点距离跳点阈值，超过不使用动画更新车标
};
/**
 * 路线图层配置
 */
class RouteLayerConfig : public LayerBaseConfig {
 public:
  explicit RouteLayerConfig(LayerType main_type = LayerType::Route);
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const RouteLayerConfig& other) const;
  bool operator!=(const RouteLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteLayerConfig& config);

 public:
  bool need_show_second_turn_arrow = false;       // 是否显示连续白箭头
  bool draw_route_with_animation_enable = false;  // 是否需要动画
  bool outdoor_route_visible = true;              // 室外路线是否显示
  float draw_route_with_animation_time = 0.3f;    // 每条路线的默认时长;
  bool switch_floor_with_location = false;        // 导航时根据定位结果自动切换显示对应室内楼层蚯蚓线
  int16_t show_status = 1;                        // 蚯蚓线显示状态
};
/**
 * 电子眼图层配置
 */
class CameraLayerConfig : public BubbleLayerConfig {
 public:
  explicit CameraLayerConfig(LayerType main_type = LayerType::SD_Camera);
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param other
   * @return   true:相同，false:不相同
   */
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;

  bool operator==(const CameraLayerConfig& other) const;
  bool operator!=(const CameraLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const CameraLayerConfig& config);

 public:
  /**
   * 独立小电子眼配置，支持独立设置 minDisplayLevel、maxDisplayLevel、minDisplayPriority、
   * maxDisplayPriority 和 needAvoidOthers
   */
  LayerBaseConfig small_camera_config;
  /**
   * 符合策略放大的电子眼是否需要放大
   */
  bool need_enlarge_camera = true;
  /**
   * 当电子眼需要放大时 是否需要采用动画形式放大
   */
  bool need_enlarge_animation = true;
  /**
   * 大电子眼（区间测速电子眼除外）显示剩余距离，默认true
   */
  bool show_remain_distance_for_large = true;
  /**
   * 带文案的电子眼透出距离,默认false
   * 仅当showRemainDistanceForLarge为false该字段生效
   */
  bool show_remain_distance_for_with_text = false;
  /**
   * 不带文案的电子眼透出距离,默认false
   * 仅当showRemainDistanceForLarge为false该字段生效
   */
  bool show_remain_distance_for_without_text = false;
  /**
   * 区间测速电子眼透出距离,默认false
   * 仅当showRemainDistanceForLarge为false该字段生效
   */
  bool show_remain_distance_for_speedtest = false;
};
/**
 * 已经淘汰
 */
class RouteNameLayerConfig : public LayerBaseConfig {
 public:
  explicit RouteNameLayerConfig(LayerType type = LayerType::RouteName);
  bool operator==(const RouteNameLayerConfig& other) const;
  bool operator!=(const RouteNameLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteNameLayerConfig& config);
};
/**
 * 拥堵气泡图层配置
 */
class RouteTrafficLayerConfig : public BubbleLayerConfig {
 public:
  explicit RouteTrafficLayerConfig(LayerType type = LayerType::RouteTraffic);
  bool operator==(const RouteTrafficLayerConfig& other) const;
  bool operator!=(const RouteTrafficLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteTrafficLayerConfig& config);
};
/**
 * 伴随气泡图层配置
 */
class RouteCompanionLayerConfig : public BubbleLayerConfig {
 public:
  RouteCompanionLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const RouteCompanionLayerConfig& other) const;
  bool operator!=(const RouteCompanionLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteCompanionLayerConfig& config);

 public:
  /**
   * 伴随气泡当前显示的场景类型，默认值为0
   * 该字段主要用于客户端在切换场景后，能再次收到气泡内容回调；
   */
  int sceneType_ = 0;
};

/**
 * 伴随气泡图层配置
 */
class HDRouteCompanionLayerConfig : public BubbleLayerConfig {
 public:
  HDRouteCompanionLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const HDRouteCompanionLayerConfig& other) const;
  bool operator!=(const HDRouteCompanionLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteCompanionLayerConfig& config);

 public:
  /**
   * 伴随气泡当前显示的场景类型，默认值为0
   * 该字段主要用于客户端在切换场景后，能再次收到气泡内容回调；
   */
  int sceneType_ = 0;
};

/**
 * 终点名称和ETA气泡图层配置
 */
class DestNameEtaLayerConfig : public BubbleLayerConfig {
 public:
  DestNameEtaLayerConfig();
  explicit DestNameEtaLayerConfig(LayerType layer_type);
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const DestNameEtaLayerConfig& other) const;
  bool operator!=(const DestNameEtaLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const DestNameEtaLayerConfig& config);

 public:
  bool show_arrive_time = true;  // 是否显示预计到达时间
};
/**
 * 路线标签气泡图层配置
 */
class RouteLabelLayerConfig : public BubbleLayerConfig {
 public:
  RouteLabelLayerConfig();
  bool operator==(const RouteLabelLayerConfig& other) const;
  bool operator!=(const RouteLabelLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteLabelLayerConfig& config);
};
#define ShowLightCountNaviOtherDefaultValue -1
/**
 * Marker图层配置
 * 所有Marker图层复用该结构
 */
class MarkerLayerConfig : public LayerBaseConfig {
 public:
  explicit MarkerLayerConfig(MarkerType marker_type);
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param other
   * @return   true:相同，false:不相同
   */
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;

  bool operator==(const MarkerLayerConfig& other) const;
  bool operator!=(const MarkerLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const MarkerLayerConfig& config);

 private:
  void InitLayerConfig(int min_display_level, int max_display_level, int min_display_priority, int max_display_priority,
                       bool need_avoid_others, int min_margin_with_others, bool enable_change_by_map_mode,
                       bool show_name, bool need_avoid_route, bool show_name_with_annotation, int16_t status = 0);

  void InitLayerConfig(int min_display_level, int max_display_level, int min_display_priority, int max_display_priority,
                       bool need_avoid_others, int min_margin_with_others, bool enable_change_by_map_mode,
                       bool show_name, bool need_avoid_route, bool show_name_with_annotation, bool visible,
                       int show_light_count_navi_follow, int show_light_count_navi_other, int16_t marker_show_status);

 public:
  bool need_show_name = true;                                              // 是否显示Marker的名称(如果有名称的话)
  bool need_show_name_with_annotation = true;                              // 是否用标注方式展示名称;
  int show_light_count_navi_follow_ = 3;                                   // 行中跟随态默认显示前方3个电子眼
  int show_light_count_navi_other_ = ShowLightCountNaviOtherDefaultValue;  // 行中非跟随态默认显示全部电子眼
  int16_t marker_show_status_ = 0;                                         // marker显示时的定位状态
};
/**
 * 巡航图层配置
 */
class CruiseLayerConfig : public LayerBaseConfig {
 public:
  explicit CruiseLayerConfig(LayerType type = LayerType::CruiseMarker);
  bool operator==(const CruiseLayerConfig& other) const;
  bool operator!=(const CruiseLayerConfig& other) const;

  bool show_fishbone_markers = true;
  friend std::ostream& operator<<(std::ostream& out, const CruiseLayerConfig& config);
};
class TrajectoryLayerConfig : public LayerBaseConfig {
 public:
  TrajectoryLayerConfig();
  bool operator==(const TrajectoryLayerConfig& other) const;
  bool operator!=(const TrajectoryLayerConfig& other) const;
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  friend std::ostream& operator<<(std::ostream& out, const TrajectoryLayerConfig& config);
  int width = 40;
  std::map<int, long> obstacle_color_map;  // 障碍物颜色
};

class HDObstacleLayerConfig : public LayerBaseConfig {
 public:
  HDObstacleLayerConfig();
  bool operator==(const HDObstacleLayerConfig& other) const;
  bool operator!=(const HDObstacleLayerConfig& other) const;
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  friend std::ostream& operator<<(std::ostream& out, const HDObstacleLayerConfig& config);
  std::map<int, long> obstacle_color_map;  // 障碍物颜色
  float mix_ratio_ = 1.0f;                 // 模型被遮挡时的半透的比例
};
class SpecialHighlightLaneLayerConfig : public LayerBaseConfig {
 public:
  SpecialHighlightLaneLayerConfig();
  bool operator==(const SpecialHighlightLaneLayerConfig& other) const;
  bool operator!=(const SpecialHighlightLaneLayerConfig& other) const;
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  friend std::ostream& operator<<(std::ostream& out, const SpecialHighlightLaneLayerConfig& config);
  std::string texture_highlight_ = "special_lane_highlight_texture.png";
  float lane_erase_start = 0.0f;
  float lane_erase_end = 5.0f;
  float lane_end_gradient_distance = 5.0f;
};

// 描述颜色类型同指定纹理坐标对应
enum class ColorType {
  Color_Blue = 0,  // 蓝色
  Color_Green,     // 绿色
  Color_Yellow,    // 黄色
  Color_Red,       // 红色
  Color_DarkRed,   // 深红
};

// 计算相机位置与姿态时使用的 ScreenCenterOffset 类型
enum class OverlookScreenCenterOffsetType {
  OverlookScreenCenterOffsetType_Target = 0,  // 若当前处于动画中，则使用动画目标值，否则使用当前值
  OverlookScreenCenterOffsetType_Current,     // 无论是否处于动画中，都使用当前值
  OverlookScreenCenterOffsetType_User         // 使用调用接口时指定的值
};

class GuideAreaLayerConfig : public LayerBaseConfig {
 public:
  GuideAreaLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const GuideAreaLayerConfig& other) const;
  bool operator!=(const GuideAreaLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const GuideAreaLayerConfig& config);

 public:
  const static std::map<ColorType, float> COLOR_TEX_COORD_MAP;  // 颜色对应一维纹理坐标结构定义

  float flow_arrow_area_length = 300;   // 流向箭头区间长度，单位：米
  float flow_arrow_animation_time = 3;  // 箭头动画流动一次时长，单位：秒
  std::string texture_arrow_day = "";   // 白天模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_transparency_day =
      "";  // 白天模式流向箭头透明度的纹理资源名称，资源宽度为2px，高度为2的整次幂px，空则使用默认资源
  std::string texture_arrow_night = "";  // 夜间模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_transparency_night =
      "";  // 夜间模式流向箭头透明度的纹理资源名称，资源宽度为2px，高度为2的整次幂px，空则使用默认资源

  std::string default_texture_traffic_color_day = "";        // 白天模式引导面/蚯蚓面的纹理资源名称
  std::string default_texture_traffic_color_night = "";      // 夜间模式引导面/蚯蚓面的纹理资源名称
  std::string non_highlight_area_texture_day = "";           // 白天引导面非高亮纹理，显示引导线时使用
  std::string non_highlight_area_texture_night = "";         // 夜间引导面非高亮纹理，显示引导线时使用
  std::string texture_stroke_traffic_color_day = "";         // 白天模式引导面描边/蚯蚓面的纹理资源名称
  std::string texture_stroke_traffic_color_night = "";       // 夜间模式引导面描边/蚯蚓面的纹理资源名称
  std::string non_highlight_area_stroke_texture_day = "";    // 白天引导面非高亮描边纹理，显示引导线时使用
  std::string non_highlight_area_stroke_texture_night = "";  // 夜间引导面非高亮描边纹理，显示引导线时使用

  float stroke_width = 0.4f;         // 引导面描边宽度,单位m
  float area_erase_start_dis = 0.0;  // 渐变效果起始点（alpha值为1）与自车位置之间的距离，自车后方位正，自车前方为负
  float area_erase_end_dis = 5.0;    // 渐变效果终止点（alpha值为0）与自车位置之间的距离, 自车后方位正，自车前方为负
  float area_end_transparency_dis = 5.0;  // 引导面结束位置透明渐变距离，单位：米

  bool guide_area_semitransparent = true;  // 已经淘汰，即将删除，于2024.8

  float guide_line_show_animation_time = 0.1f;  // 当引导面显示在屏幕范围内的时候，引导线从无到有动画时长，单位s
                                                // 此动画时长必须要小于 flow_arrow_animation_time
  float guide_line_hide_animation_time = 0.1f;  // 当引导面显示在屏幕范围内的时候，引导线从无到有动画时长，单位s
                                                // 此动画时长必须要小于 flow_arrow_animation_time
  bool flow_arrow_visible = true;               // 流向箭头显示/隐藏

  float hidden_distance_to_intersection = 500;  // 有车道级白箭头需隐藏流向箭头时，自车到机动点的距离，单位m，默认值500
};
class RouteLineAreaLayerConfig : public LayerBaseConfig {
 public:
  RouteLineAreaLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const RouteLineAreaLayerConfig& other) const;
  bool operator!=(const RouteLineAreaLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const RouteLineAreaLayerConfig& config);

 public:
  static const int SHOW_MODE_SHOW_ALWAYS = 0;
  /**当界面上引导线显示的时候，自动隐藏蚯蚓面，引导线不显示的时候，自动显示蚯蚓面*/
  static const int SHOW_MODE_HIDE_GUIDE_LINE_SHOW = 1;

  float route_width = 3.75;               // 蚯蚓面宽度，单位：米
  int show_mode = SHOW_MODE_SHOW_ALWAYS;  // 蚯蚓面展示方式，当enable=true&&visible=true 生效
  float flow_arrow_area_length = 300;     // 流向箭头区间长度，单位：米
  float flow_arrow_animation_time = 3;    // 箭头动画流动一次时长，单位：秒
  std::string texture_arrow_day = "";     // 白天模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_transparency_day =
      "";  // 白天模式流向箭头透明度的纹理资源名称，资源宽度为2px，高度为2的整次幂px，空则使用默认资源
  std::string texture_arrow_night = "";  // 夜间模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_transparency_night =
      "";  // 夜间模式流向箭头透明度的纹理资源名称，资源宽度为2px，高度为2的整次幂px，空则使用默认资源
  std::string default_texture_traffic_color_day =
      "";  // 白天模式蚯蚓面的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string default_texture_traffic_color_night =
      "";  // 夜间模式蚯蚓面的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string non_highlight_area_texture_day = "";    // 白天蚯蚓面非高亮纹理，显示引导线时使用
  std::string non_highlight_area_texture_night = "";  // 夜间蚯蚓面非高亮纹理，显示引导线时使用
  float area_erase_start_dis = 0.0;  // 渐变效果起始点（alpha值为1）与自车位置之间的距离，自车后方位正，自车前方为负
  float area_erase_end_dis = 5.0;    // 渐变效果终止点（alpha值为0）与自车位置之间的距离,自车后方位正，自车前方为负
};

class EqualWidthGuideAreaLayerConfig : public LayerBaseConfig {
 public:
  EqualWidthGuideAreaLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const EqualWidthGuideAreaLayerConfig& other) const;
  bool operator!=(const EqualWidthGuideAreaLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const EqualWidthGuideAreaLayerConfig& config);

 public:
  static const int SHOW_MODE_SHOW_ALWAYS = 0;
  /**当界面上引导线显示的时候，自动隐藏蚯蚓面，引导线不显示的时候，自动显示蚯蚓面*/
  static const int SHOW_MODE_HIDE_GUIDE_LINE_SHOW = 1;

  float expand_width = 2.5f;              // 拓展宽度
  int show_mode = SHOW_MODE_SHOW_ALWAYS;  // 蚯蚓面展示方式，当enable=true&&visible=true 生效
  float flow_arrow_area_length = 224;     // 流向箭头区间长度，单位：米
  float flow_arrow_animation_time = 3;    // 箭头动画流动一次时长，单位：秒
  std::string texture_arrow_day = "";     // 白天模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_transparency_day =
      "";  // 白天模式流向箭头透明度的纹理资源名称，资源宽度为2px，高度为2的整次幂px，空则使用默认资源
  std::string texture_arrow_night = "";  // 夜间模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_transparency_night =
      "";  // 夜间模式流向箭头透明度的纹理资源名称，资源宽度为2px，高度为2的整次幂px，空则使用默认资源
  std::string texture_traffic_color_day =
      "";  // 白天模式蚯蚓面的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_traffic_color_night =
      "";  // 夜间模式蚯蚓面的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string non_highlight_area_texture_day = "";    // 白天蚯蚓面非高亮纹理，显示引导线时使用
  std::string non_highlight_area_texture_night = "";  // 夜间蚯蚓面非高亮纹理，显示引导线时使用
  std::string texture_stroke_traffic_color_day;
  std::string texture_stroke_traffic_color_night;
  std::string non_highlight_area_stroke_texture_day;
  std::string non_highlight_area_stroke_texture_night;

  float area_erase_start_dis = 0.0;  // 渐变效果起始点（alpha值为1）与自车位置之间的距离，自车后方位正，自车前方为负
  float area_erase_end_dis = 5.0;    // 渐变效果终止点（alpha值为0）与自车位置之间的距离, 自车后方位正，自车前方为负
  float area_end_transparency_dis = 5.0;        // 引导面结束位置透明渐变距离，单位：米
  float guide_line_show_animation_time = 0.1f;  // 当引导面显示在屏幕范围内的时候，引导线从无到有动画时长，单位s
                                                // 此动画时长必须要小于 flow_arrow_animation_time
  float guide_line_hide_animation_time = 0.1f;  // 当引导面显示在屏幕范围内的时候，引导线从无到有动画时长，单位s
                                                // 此动画时长必须要小于 flow_arrow_animation_time
  float stroke_width = 0.4f;                    // 引导面描边宽度,单位m
  bool flow_arrow_visible = true;               // 流向箭头显示/隐藏
  float hidden_distance_to_intersection = 500;  // 有车道级白箭头需隐藏流向箭头时，自车到机动点的距离，单位m，默认值500
};

class GuideLineLayerConfig : public LayerBaseConfig {
 public:
  GuideLineLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const GuideLineLayerConfig& other) const;
  bool operator!=(const GuideLineLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const GuideLineLayerConfig& config);

 public:
  const static std::map<ColorType, float> COLOR_TEX_COORD_MAP;  // 颜色对应一维纹理坐标结构定义

  float guide_line_width = 1.95;       // 引导线宽度，淘汰,使用 route_class_config 中的 guide_line_width配置
  float flow_arrow_area_length = 224;  // 流向箭头区间长度
  float flow_arrow_speed_in_change_lane_area = 112;  // 变道区箭头流速，单位m/s
  float flow_arrow_speed_in_go_straight_area = 75;   // 顺行区箭头流速，单位m/s
  std::string texture_arrow_day = "";    // 白天模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_arrow_night = "";  // 夜间模式流向箭头的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string default_texture_traffic_color_day =
      "";  // 白天模式引导线的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string default_texture_traffic_color_night =
      "";                                  // 夜间模式引导线的纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  float line_start_transparent_dis = 5.0;  // 引导线擦除开始距离自车中心向前距离
  float line_end_transparent_dis = 5.0;    // 引导面擦除结束距离自车中心向后距离
  bool has_center_line_callback = false;   // 是否有中心线回调 (MapBizEventListener::OnGuideLineCenterLineDataUpdate)
};

class HDCompanionAreaLayerConfig : public LayerBaseConfig {
 public:
  HDCompanionAreaLayerConfig();
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const HDCompanionAreaLayerConfig& other) const;
  bool operator!=(const HDCompanionAreaLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDCompanionAreaLayerConfig& config);

 public:
  const static std::map<ColorType, float> COLOR_TEX_COORD_MAP;  // 颜色对应一维纹理坐标结构定义
  float expand_width = 5.0f;                                    // 拓展宽度
  float stroke_width = 0.0f;                                    // 描边宽度，单位m

  std::string default_texture_traffic_color_day =
      "";  // 白天模式纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string default_texture_traffic_color_night =
      "";  // 夜间模式纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string highlight_dashed_line_texture_traffic_color_day =
      "";  // 白天模式伴随线纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string highlight_dashed_line_texture_traffic_color_night =
      "";  // 夜间模式伴随线纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string mask_dashed_line_texture_traffic_color_day =
      "";  // 白天模式模板纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string mask_dashed_line_texture_traffic_color_night =
      "";  // 夜间模式模板纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_stroke_traffic_color_day =
      "";  // 白天模式描边纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  std::string texture_stroke_traffic_color_night =
      "";                                   // 夜间模式描边纹理资源名称，资源宽和高为2的整次幂px，空则使用默认资源
  float line_start_transparent_dis = 5.0f;  // 渐变设置，距离起始位置距离
  float line_end_transparent_dis = 5.0f;    // 渐变设置，距离终点位置距离
};

/**
 * 转向气泡图层配置
 */
class TurnArrowLayerConfig : public BubbleLayerConfig {
 public:
  TurnArrowLayerConfig();
  bool operator==(const TurnArrowLayerConfig& other) const;
  bool operator!=(const TurnArrowLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const TurnArrowLayerConfig& config);
};
/**
 * 红绿灯倒计时气泡图层配置
 */
class LightCountdownTimerLayerConfig : public BubbleLayerConfig {
 public:
  explicit LightCountdownTimerLayerConfig(LayerType type = LayerType::LightCountdownTimer);
  bool operator==(const LightCountdownTimerLayerConfig& other) const;
  bool operator!=(const LightCountdownTimerLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LightCountdownTimerLayerConfig& config);
};
/**
 * 长实线气泡图层配置
 */
class HDLongSolidLayerConfig : public BubbleLayerConfig {
 public:
  explicit HDLongSolidLayerConfig(LayerType type = LayerType::HD_LongSolid);
  bool operator==(const HDLongSolidLayerConfig& other) const;
  bool operator!=(const HDLongSolidLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDLongSolidLayerConfig& config);
};
/**
 * 入口气泡图层配置
 */
class EntranceBubbleLayerConfig : public BubbleLayerConfig {
 public:
  explicit EntranceBubbleLayerConfig(LayerType type = LayerType::EntranceBubble);
  bool operator==(const EntranceBubbleLayerConfig& other) const;
  bool operator!=(const EntranceBubbleLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const EntranceBubbleLayerConfig& config);
};
/**
 * 出口气泡图层配置
 */
class ExitBubbleLayerConfig : public BubbleLayerConfig {
 public:
  explicit ExitBubbleLayerConfig(LayerType type = LayerType::ExitBubble);
  bool operator==(const ExitBubbleLayerConfig& other) const;
  bool operator!=(const ExitBubbleLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const ExitBubbleLayerConfig& config);
};
/**
 * 层间气泡图层配置
 */
class FloorBubbleLayerConfig : public BubbleLayerConfig {
 public:
  explicit FloorBubbleLayerConfig(LayerType type = LayerType::FloorBubble);
  bool operator==(const FloorBubbleLayerConfig& other) const;
  bool operator!=(const FloorBubbleLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const FloorBubbleLayerConfig& config);
};
/**
 * 等灯气泡图层显示配置
 */
class LightWaitLayerConfig : public BubbleLayerConfig {
 public:
  explicit LightWaitLayerConfig(LayerType type = LayerType::LightWait);
  bool operator==(const LightWaitLayerConfig& other) const;
  bool operator!=(const LightWaitLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LightWaitLayerConfig& config);
};
/**
 * HD等灯气泡图层显示配置
 */
class HDLightWaitLayerConfig : public BubbleLayerConfig {
 public:
  explicit HDLightWaitLayerConfig(LayerType type = LayerType::HD_LightWait);
  bool operator==(const HDLightWaitLayerConfig& other) const;
  bool operator!=(const HDLightWaitLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDLightWaitLayerConfig& config);
};

class UndergroundParkBubbleConfig : public BubbleLayerConfig {
 public:
  explicit UndergroundParkBubbleConfig(LayerType type = LayerType::UndergroundParkBubble);
  bool operator==(const UndergroundParkBubbleConfig& other) const;
  bool operator!=(const UndergroundParkBubbleConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const UndergroundParkBubbleConfig& config);
};

class StartAoiPolygonConfig : public LayerBaseConfig {
 public:
  explicit StartAoiPolygonConfig(LayerType type = LayerType::StartAoiPolygon);
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const StartAoiPolygonConfig& other) const;
  bool operator!=(const StartAoiPolygonConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const StartAoiPolygonConfig& config);

  float border_width = 10.0f;

  std::vector<int> dash_pattern = {6, 6};

  // AARRGGBB
  int aoi_border_color_day = 0xFF3B5074;
  int aoi_fill_color_day = 0x4DC4D8F4;

  // AARRGGBB
  int aoi_border_color_night = 0xFF3B5074;
  int aoi_fill_color_night = 0x4DC4D8F4;
};

class LocatorFreeLineConfig : public LayerBaseConfig {
 public:
  explicit LocatorFreeLineConfig(LayerType type = LayerType::LocatorFreeLine);
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const LocatorFreeLineConfig& other) const;
  bool operator!=(const LocatorFreeLineConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const LocatorFreeLineConfig& config);

  // 路线宽度
  float route_width = 20.0f;
  float foot_print_space = 10.0f;

  std::string route_tex_name_day = "mapbiz_underground_park_line_arrow.png";
  std::string route_tex_name_night = "mapbiz_underground_park_line_arrow.png";
};
/**
 * 车道级白箭头图层配置
 */
class HDTBTArrowLayerConfig : public LayerBaseConfig {
 public:
  explicit HDTBTArrowLayerConfig(LayerType type = LayerType::HD_TBTArrow);
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;
  bool operator==(const HDTBTArrowLayerConfig& other) const;
  bool operator!=(const HDTBTArrowLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDTBTArrowLayerConfig& config);

 public:
  float show_distance_to_intersection = 2000;  // 显示时自车到机动点的距离，单位m，默认值2000
  int roof_fill_color_day = 0xffffffff;        // 日间顶面填充颜色，格式RGBA
  int roof_fill_color_night = 0xffffffff;      // 夜间顶面填充颜色，格式RGBA
  int roof_border_color_day = 0x13c5ffff;      // 日间顶面描边颜色，格式RGBA
  int roof_border_color_night = 0x13c5ffff;    // 夜间顶面描边颜色，格式RGBA
  int wall_color_day = 0x0075e8ff;             // 日间侧面描边颜色，格式RGBA
  int wall_color_night = 0x0075e8ff;           // 夜间侧面描边颜色，格式RGBA
  int inner_border_color_day = 0x2a68ccff;     // 白间内边描边颜色，格式RGBA
  int inner_border_color_night = 0x2a68ccff;   // 夜间内边描边颜色，格式RGBA
  float max_rotate_angle = 60;
  float max_rotate_skew = 60;
  float min_rotate_skew = 30;
  bool gradual_status = false;
  float gradual_distance_to_intersection = 300;  // 已经淘汰
  float gradual_animation_start_dis = 200;
  float gradual_animation_end_dis = 150;
  float roof_gradual_start = -0.1f;
  float roof_gradual_end = 0.2f;
  float other_gradual_start = -1.0f;
  float other_gradual_end = 0.0f;
  float width_ratio = 1.0f;
  float height_to_width_ratio = 0.2f;
  float head_bottom_to_width_ratio = 1.56f;
  bool flow_light_status = false;
  float flow_light_distance_to_intersection = 300;
  float float_light_rate = 30.0;
  std::string float_light_texture_day = "";
  std::string float_light_texture_night = "";
  bool has_center_line_callback = false;  // 是否有中心线回调 (MapBizEventListener::OnTBTArrowUpdate)
};
/**
 * 鹰眼图的配置信息
 */
class OverviewMapConfig {
 public:
  /**
   * 鹰眼图全览状态是否显示路线流向箭头
   * 默认值:false
   */
  bool route_flow_arrow_overlook_visible = false;
  /**
   * 鹰眼图跟随状态是否显示路线流向箭头
   * 默认值:true
   */
  bool route_flow_arrow_followed_visible = true;
  /**
   * 鹰眼图允许显示的最大比例尺等级
   * 默认值：30
   */
  int max_display_level = Layer::Level::MAX_LEVEL;
};
std::ostream& operator<<(std::ostream& out, const OverviewMapConfig& overview_map_config);
class MarkerSize {
 public:
  int width = 0;   // maker宽，单位像素
  int height = 0;  // marker高，单位像素
};
std::ostream& operator<<(std::ostream& out, const MarkerSize& marker_size);
class MarkerDescriptor {
 public:
  /**
   * marker坐标
   */
  mapbase::GeoCoordinate coordinate;
  /**
   * marker大小
   */
  MarkerSize marker_size;
  /**
   * marker锚点
   */
  MapVector2f anchor{0.5f, 0.5f};
  /**
   * X轴方向偏移量，像素坐标
   */
  float screen_offset_x{0};
  /**
   * Y轴方向偏移量，像素坐标
   */
  float screen_offset_y{0};
};
std::ostream& operator<<(std::ostream& out, const MarkerDescriptor& marker);
class OverLookParam {
 public:
  /**
   * 路线横向时屏幕矩形
   */
  MapRectD screen_rect_for_horizontal_route{0, 0, 0, 0};
  /**
   * 路线纵向时屏幕矩形
   */
  MapRectD screen_rect_for_vertical_route{0, 0, 0, 0};
  /**
   * 其他路线形态时屏幕矩形
   */
  MapRectD screen_rect_for_other{0, 0, 0, 0};
  /**
   * 横向路线宽高比阈值，大于等于为横向
   */
  float width_height_ratio_threshold_for_horizontal_route = 5.0F;
  /**
   * 纵向路线宽高比阈值，小于等于为纵向
   */
  float width_height_ratio_threshold_for_vertical_route = 0.2F;
  /**
   * 车标宽高
   */
  MarkerSize locator_size;
  /**
   * 用户调用地图接口加入的地理显示元素，这些元素想显示在地图中
   */
  std::vector<mapbase::Rect<mapbase::GeoCoordinate>> additional_geo_bounds;
  /**
   * 路线id，若列表为空，返回内部所有路线构成的最大外界框，注意路线边界框计算不包含已隐藏路线
   */
  std::vector<std::string> route_ids;
  /**
   * 使用完整路线还是剩余路线
   */
  bool whole_route = false;
  /**
   * 需要纳入全览显示的外部创建marker
   */
  std::vector<MarkerDescriptor> additional_markers;
  /**
   * 全览动画时长，单位秒
   */
  float animation_time{0.4F};
  /**
   * 考虑到 overlook 过程可能处于 ScreenCenterOffset 动画中，ScreenCenterOffset
   * 不稳定导致计算结果不稳定，因此由调用方指定 ScreenCenterOffsetType
   */
  OverlookScreenCenterOffsetType screen_center_offset_type =
      OverlookScreenCenterOffsetType::OverlookScreenCenterOffsetType_Target;
  /**
   * 预览的中心点偏移
   */
  MapVector2f screen_center_offset_value{0, 0.235f};
};
std::ostream& operator<<(std::ostream& out, const OverLookParam& param);
class IndoorOverLookParam : public OverLookParam {
 public:
  /**
   * 室内路线所在的建筑物id
   */
  std::string building_id;
};
std::ostream& operator<<(std::ostream& out, const IndoorOverLookParam& param);
/**
 * 蚯蚓线纹理定制样式选项集合
 */
class ColorRouteStyleOption {
 public:
  void Reset();

 public:
  /**
   * 白天模式主路线的纹理资源名称
   */
  std::string route_line_texture_main_day = "";
  /**
   * 夜间模式主路线的纹理资源名称
   */
  std::string route_line_texture_main_night = "";
  /**
   * 白天模式伴随路线的纹理资源资源名称
   */
  std::string route_line_texture_assist_day = "";
  /**
   * 夜间模式伴随路线的纹理资源名称
   */
  std::string route_line_texture_assist_night = "";
  /**
   * 白天模式主路线的流向箭头的纹理资源名称； 空则使用默认资源
   */
  std::string route_arrow_texture_main_day = "";
  /**
   * 夜间模式主路线的流向箭头的纹理资源名称； 空则使用默认资源
   */
  std::string route_arrow_texture_main_night = "";
  /**
   * 白天模式伴随路线的流向箭头的纹理资源名称； 空则使用默认资源, 伴随路线默认不显示箭头
   */
  std::string route_arrow_texture_assist_day = "";
  /**
   * 夜间模式伴随路线的流向箭头的纹理资源名称； 空则使用默认资源, 伴随路线默认不显示箭头
   */
  std::string route_arrow_texture_assist_night = "";
  /**
   * 路线上交通流向箭头是否显示
   * 默认值:true
   */
  bool route_flow_arrow_visible = true;
  /**
   * 路线上交通流向箭头间隔(单位dp, <=0 使用内部默认值)
   */
  float route_flow_arrow_spacing = 0;
};
// 重载自定义枚举 运算符 "<<"
std::ostream& operator<<(std::ostream& out, const ColorRouteStyleOption& route_style_option);

/**
 * 高级蚯蚓线纹理样式定制参数，支持到不同比例尺调整宽度和纹理样式
 */
class RouteTextureStyleAtScale {
 public:
  float start_scale = 0;                      // 开始比例尺
  float end_scale = Layer::Level::MAX_LEVEL;  // 结束比例尺
  float width = 10;                           // 路线宽度:(0,128], 单位为dp, 不需要乘density
  std::string image_name = "";                // 路线纹理名称
};
std::ostream& operator<<(std::ostream& out, const std::vector<RouteTextureStyleAtScale>& option);
/**
 * 高级蚯蚓线名称样式定制，支持到不通过比例尺调整文字大小
 */
class RouteNameStyleAtScale {
 public:
  float start_scale = 0;                      // 开始生效的显示级别
  float end_scale = Layer::Level::MAX_LEVEL;  // 结束生效的显示级别
  int text_color = 0;                         // 文字颜色
  int text_border_color = 0;                  // 文字描边颜色
  int font_size = 16;                         // 字体大小,有效范围8-20,单位为dp
};
std::ostream& operator<<(std::ostream& out, const std::vector<RouteNameStyleAtScale>& option);
/**
 * 高级蚯蚓线纹理定制样式选项集合
 */
class ColorRouteStyleOptionEx {
 public:
  std::vector<RouteTextureStyleAtScale> route_line_textures_main_day;       // 主底图白天模式主路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_main_night;     // 主底图夜间模式主路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_assist_day;     // 主底图白天模式伴随路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_assist_night;   // 主底图夜间模式伴随路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_main_day2;      // 鹰眼图白天模式主路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_main_night2;    // 鹰眼图夜间模式主路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_assist_day2;    // 鹰眼图白天模式伴随路线样式信息
  std::vector<RouteTextureStyleAtScale> route_line_textures_assist_night2;  // 鹰眼图夜间模式伴随路线样式信息

  std::vector<RouteNameStyleAtScale> route_text_styles_main_day;      // 主底图白天模式主路线文字信息
  std::vector<RouteNameStyleAtScale> route_text_styles_main_night;    // 主底图夜间模式主路线文字信息
  std::vector<RouteNameStyleAtScale> route_text_styles_assist_day;    // 主底图白天模式伴随路线文字信息
  std::vector<RouteNameStyleAtScale> route_text_styles_assist_night;  // 主底图夜间模式伴随路线文字信息
};
std::ostream& operator<<(std::ostream& out, const ColorRouteStyleOptionEx& option);
/**
 * 蚯蚓线转向箭头定制样式选项
 */
class TurnArrowStyleOption {
 public:
  bool operator==(const TurnArrowStyleOption& other) const;
  bool operator!=(const TurnArrowStyleOption& other) const;

 public:
  /**
   * 是否显示白箭头，默认值显示
   */
  bool visible = true;
  /**
   * 白天模式下顶部颜色, 格式:ARGB
   */
  int roof_color_day = 0xFFFFFFFF;
  /**
   * 夜间模式下边颜色, 格式:ARGB
   */
  int edge_color_day = 0xFF325ABB;
  /**
   * 白天模式下外侧颜色, 格式:ARGB
   */
  int wall_color_day = 0xFF6694FF;
  /**
   * 夜间模式下顶部颜色, 格式:ARGB
   */
  int roof_color_night = 0xFFFFFFFF;
  /**
   * 夜间模式下边颜色, 格式:ARGB
   */
  int edge_color_night = 0xFF3B6FE5;
  /**
   * 夜间模式下外侧颜色, 格式:ARGB
   */
  int wall_color_night = 0xFF6694FF;
  /**
   * 白天模式下阴影颜色, 格式:ARGB
   */
  int shadow_color_day = 0xB2696969;
  /**
   * 夜间模式下阴影颜色, 格式:ARGB
   */
  int shadow_color_night = 0xB2696969;
  /**
   * 箭头宽度缩放系数，范围【0.0f, 1.0f] 默认1，与蚯蚓线宽度一致
   */
  float width_scale = 1.0f;
  /**
   * 箭头高度缩放系数，范围【0.0f, 1.0f] 默认1
   */
  float height_scale = 1.0f;
};
std::ostream& operator<<(std::ostream& out, const TurnArrowStyleOption& option);

/**
 * 路线解释性单独线样式
 */
class RouteExplainLineStyle {
 public:
  /**
   * 颜色，格式：ARGB
   */
  int font_color = 0;
  /**
   * 宽度，单位：dp
   */
  float font_width = 0;
  /**
   * 填充类型，1 实线、2 虚线、3 小圆点
   */
  int fill_type = 0;
  /**
   * 描边颜色，格式：ARGB
   */
  int border_color = 0;
  /**
   * 描边宽度，单位：dp
   */
  float border_width = 0;
  /**
   * 虚线长度设置，单位：dp，fill_type==2时必填
   */
  std::vector<int> dash_pattern;
  /**
   * 小圆点纹理图片名称，fill_type==3时必填
   */
  std::string dot_texture;

  bool operator==(const RouteExplainLineStyle& other) const;
  bool operator!=(const RouteExplainLineStyle& other) const;
};
std::ostream& operator<<(std::ostream& out, const RouteExplainLineStyle& style);

/**
 * 路线解释性单独面样式
 */
class RouteExplainAreaStyle {
 public:
  /**
   * 边线颜色，格式：ARGB
   */
  int border_color = 0;
  /**
   * 边线宽度，单位：dp
   */
  float border_width = 0;
  /**
   * 填充颜色，格式：ARGB
   */
  int fill_color = 0;

  bool operator==(const RouteExplainAreaStyle& other) const;
  bool operator!=(const RouteExplainAreaStyle& other) const;
};
std::ostream& operator<<(std::ostream& out, const RouteExplainAreaStyle& style);

/**
 * 路线解释性配置
 */
class RouteExplainConfig {
 public:
  /**
   * 设置路线解释性单独线是否可见
   */
  bool explain_independent_line_visible = true;
  /**
   * 设置路线解释性单独面是否可见
   */
  bool explain_independent_area_visible = true;
  /**
   * 设置路线解释性bubble是否可见
   */
  bool explain_bubble_visible = true;
  /**
   * 设置各个场景路线解释性单独线优先级
   */
  std::map<int, unsigned int> explain_independent_line_priority;
  /**
   * 设置各个场景路线解释性单独面优先级
   */
  std::map<int, unsigned int> explain_independent_area_priority;
  /**
   * 白天路线解释性单独线样式
   */
  std::map<int, RouteExplainLineStyle> route_explain_line_style_day;
  /**
   * 夜间路线解释性单独线样式
   */
  std::map<int, RouteExplainLineStyle> route_explain_line_style_night;
  /**
   * 白天路线解释性单独面样式
   */
  std::map<int, RouteExplainAreaStyle> route_explain_area_style_day;
  /**
   * 夜间路线解释性单独面样式
   */
  std::map<int, RouteExplainAreaStyle> route_explain_area_style_night;

  bool operator==(const RouteExplainConfig& other) const;
  bool operator!=(const RouteExplainConfig& other) const;
};

std::ostream& operator<<(std::ostream& out, const RouteExplainConfig& config);

/**
 * Marker图层名称样式集合
 */
class MarkerNameStyleOption {
 public:
  MarkerNameStyleOption();

 public:
  /**
   * 显示样式
   * (bit:0) for underline; (bit:1) for bold; (bit:2) for outline or halo; (bit:3) for background; (bit:4) for
   * textonicon; other bits is unused now;
   */
  int effect_day = 3;
  int font_size_day = 28;           // 字体大小
  int halo_size_day = 1;            // 描边大小
  int text_color_day = 0xFF000000;  // 文字颜色
  int halo_color_day = 0xFFFFFFFF;  // 描边颜色
  int text_space_day = 5;
  int icon_space_day = 0;
  std::vector<MapBubbleDisplayDirection> candidate_directions_day;

  int effect_night = 3;               // 显示样式
  int font_size_night = 28;           // 字体大小
  int halo_size_night = 1;            // 描边大小
  int text_color_night = 0xFFFFFFFF;  // 文字颜色
  int halo_color_night = 0xFF000000;  // 描边颜色
  int text_space_night = 5;
  int icon_space_night = 0;
  std::vector<MapBubbleDisplayDirection> candidate_directions_night;
};
std::ostream& operator<<(std::ostream& out, const MarkerNameStyleOption& option);

class MapBizHDLaneGradualConfig {
 public:
  bool enabled = false;
  std::string url;                    // 访问半透服务的url
  float tunnel_model_enter_dis = 30;  // 当自车驶入隧道后多少米，半透进入隧道模式
  float tunnel_model_exit_dis = -30;  // 当自车驶出隧道后多少米，恢复正常模式  负数代表还未驶出隧道的时候
 public:
  friend std::ostream& operator<<(std::ostream& out, const MapBizHDLaneGradualConfig& config);
};

class CruiseTrafficLightLayerConfig : public BubbleLayerConfig {
 public:
 public:
  explicit CruiseTrafficLightLayerConfig(LayerType type = LayerType::CruiseTrafficLight);
  bool operator==(const CruiseTrafficLightLayerConfig& other) const;
  bool operator!=(const CruiseTrafficLightLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const CruiseTrafficLightLayerConfig& config);
};

class HDLocatorLayerConfig : public LayerBaseConfig {
 public:
  HDLocatorLayerConfig();
  /**
   * 判断部分属性是否相同
   * 备注:降低复杂度增加
   * @param other
   * @return   true:相同，false:不相同
   */
  bool IsEqualOfOtherParameters(const LayerBaseConfig* pother) const override;

  bool operator==(const HDLocatorLayerConfig& other) const;
  bool operator!=(const HDLocatorLayerConfig& other) const;
  friend std::ostream& operator<<(std::ostream& out, const HDLocatorLayerConfig& config);

 public:
  float show_distance_to_intersection_ = 80;       // 转向灯闪烁开始时到HD白箭头pass段起点的距离，默认80米
  std::vector<int> turn_left_intersection_type_;   // 左转向灯生效机动点类型
  std::vector<int> turn_right_intersection_type_;  // 右转向灯生效机动点类型
  MaterialsAnimation turn_left_params_;            // 左转向灯动画参数
  MaterialsAnimation turn_right_params_;           // 右转向灯动画参数
  MaterialsAnimation brake_light_params_;          // 刹车灯动画参数

 private:
  void InitTurnLightParams();
  void InitBrakeLightParams();
};

__MAPBIZ_NAMESPACE_END__
