//
//  MapBizMapScaleDefine.h
//  MapBiz
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/12/7.
//
#ifndef _OC_MAP_BIZ_SCALE_HEADER_H_
#define _OC_MAP_BIZ_SCALE_HEADER_H_

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 自动比例尺策略类型
 */
typedef NS_ENUM(NSInteger, MapBizMapScaleHandlerType) {
    MapBizMapScaleHandlerTypeNone = -1,          // 非法类型
    MapBizMapScaleHandlerTypeTrafficJam = 1,     // 拥堵自动比例尺策略
    MapBizMapScaleHandlerTypeCurveRoute = 2,     // 弯道自动比例尺策略
    MapBizMapScaleHandlerTypeNormal = 3,         // 常规自动比例尺策略
    MapBizMapScaleHandlerTypeStartUp = 4,        // 启动场景自动比例尺策略
    MapBizMapScaleHandlerTypeApproachEnd = 5,    // 终点场景自动比例尺策略
    MapBizMapScaleHandlerTypeCompanionFork = 6,  // 伴随分歧点场景自动比例尺策略
    MapBizMapScaleHandlerTypeIndoor = 7,         // 室内场景
    MapBizMapScaleHandlerTypeHDScale = 8,        // 车道级导航缩放
    MapBizMapScaleHandlerTypeHD_Default = 9,
    MapBizMapScaleHandlerTypeHD_Intersection = 10,
    MapBizMapScaleHandlerTypeHD_Tunnel = 11,
    MapBizMapScaleHandlerTypeHD_RTK_CP = 12,
    MapBizMapScaleHandlerTypeHD_Tips = 13
};

#pragma mark - MapBizMapScaleBaseConfig - 自动比例尺策略参数配置基类
/**
 * 自动比例尺策略参数配置基类
 */
@interface MapBizMapScaleBaseConfig : NSObject
@property (nonatomic, assign) MapBizMapScaleHandlerType type;  // 策略类型
@property (nonatomic, copy, readonly) NSString *typeStr;
@property (nonatomic, assign) BOOL enabled;            // 策略启用标志(默认值:true)
@property (nonatomic, assign) BOOL moveCarPosEnabled;  // 该策略执行时是否需要左右移动自车中心(默认值:false)
@property (nonatomic, assign) BOOL adjustSkewEnabled;  // 该策略执行时是否需要根据比例尺调整俯仰角(线性调整, 默认值:true)

/**
 * 该策略允许显示的最小比例尺(默认值:15, 100米)
 * 参考map_layer_define.h中比例尺映射定义
 */
@property (nonatomic, assign) float minScaleLevel;
/**
 * 该策略允许显示的最大比例尺(默认值:18, 10米)
 * 参考map_layer_define.h中比例尺映射定义
 */
@property (nonatomic, assign) float maxScaleLevel;
/**
 * 该策略允许显示的最小俯仰角(默认值:20.0度)
 */
@property (nonatomic, assign) float minSkew;
/**
 * 该策略允许显示的最大俯仰角(默认值:60.0度)
 */
@property (nonatomic, assign) float maxSkew;
/**
 * 自车左右移动策略开启情况下, 允许在有效区域移动的最左预留系数(1/3)
 */
@property (nonatomic, assign) float validAreaLeftRate;
/**
 * 自车左右移动策略开启情况下, 允许在有效区域向左移最右预留系数(1/3)
 */
@property (nonatomic, assign) float validAreaRightRate;
/**
 * 当该策略执行结束后保持冻结缩放的最大距离
 * 单位：米， 默认值：50
 */
@property (nonatomic, assign) float maxFreezeDistance;

- (instancetype)initWithType:(MapBizMapScaleHandlerType)type;
@end

#pragma mark - MapBizTrafficJamScaleConfig - 路况拥堵自动比例尺策略 参数配置
/**
 * 路况拥堵自动比例尺策略 参数配置
 */
@interface MapBizTrafficJamScaleConfig : MapBizMapScaleBaseConfig
@property (nonatomic, assign) float minLength;                // 进入路况拥堵策略的条件: 路况拥堵最小长度(默认值:50米)
@property (nonatomic, assign) float maxLength;                // 进入路况拥堵策略的条件: 路况拥堵最大长度(默认值:400米)
@property (nonatomic, assign) float maxDistanceToStartPoint;  // 进入路况拥堵策略的条件: 自车距离拥堵起点的最大距离(默认值:500米)
@end

#pragma mark - MapBizCurveRouteScaleConfig - 弯道识别自动比例尺策略 参数配置
/**
 * 弯道识别自动比例尺策略 参数配置
 */
@interface MapBizCurveRouteScaleConfig : MapBizMapScaleBaseConfig
/**
 * 弯道定义: 弯道最大半径(默认值:300米）
 */
@property (nonatomic, assign) float maxRadius;
/**
 * 弯道定义: 最小累计弧度(默认值:50度)
 */
@property (nonatomic, assign) float minAccumulatedAngle;
/**
 * 弯道定义: 最小累计弧长(默认值:50米)
 */
@property (nonatomic, assign) float minAccumulatedLength;
/**
 * 进入路况拥堵策略的条件: 自车距离拥入弯点的最大距离(默认值:500米)
 */
@property (nonatomic, assign) float maxDistanceToStartPoint;
/**
 * 根据弯道半径实际大小 采用不同的比例尺，半径分割线(默认值150米)
 * 弯道半径 >scale_level_separator_radius_ 最大比例尺为: (max_scale_level_ - 1)
 * 弯道半径<=scale_level_separator_radius_ 最大比例尺为: max_scale_level_
 */
@property (nonatomic, assign) float scaleLevelSeparatorRadius;
@end

#pragma mark - MapBizNormalScaleConfig - 常规自动比例尺策略 参数配置
/**
 * 常规自动比例尺策略 参数配置
 */
@interface MapBizNormalScaleConfig : MapBizMapScaleBaseConfig
/**
 * 3D 模式 自车距离与路口的距离阀值 用于判断缩放还是保持
 * 俯仰角和比例尺均参考如下规则:
 * 小于等于该值，则使用缩放，需要设定最小/最大范围
 * 大于该值，则使用保持, 比例尺和俯仰角均用最大值
 * 单位:米
 */
@property (nonatomic, assign) float distanceThresholdToIntersection3d;
/**
 * 2D 模式 自车距离与路口的距离阀值 用于判断缩放还是保持
 * 俯仰角保持最小值；比例尺参考如下规则:
 * 小于等于该值，则使用缩放，需要设定最小/最大范围
 * 大于该值，则使用保持, 比例尺使用最大值
 * 单位:米
 */
@property (nonatomic, assign) float distanceThresholdToIntersection2d;
@end

#pragma mark - MapBizStartUpScaleConfig - 启动场景自动比例尺策略 参数配置
/**
 * 启动场景自动比例尺策略 参数配置
 */
@interface MapBizStartUpScaleConfig : MapBizMapScaleBaseConfig
/**
 * 用户发起导航后驾驶时间在max_time秒内 或驾驶距离在max_distance米内 均算起点策略
 * 单位:秒, 默认值: 10秒
 */
@property (nonatomic, assign) float maxTime;
/**
 * 用户发起导航后驾驶时间在max_time秒内 或驾驶距离在max_distance米内 均算起点策略
 * 单位:米, 默认值: 50米
 */
@property (nonatomic, assign) float maxDistance;
@end

#pragma mark - MapBizApproachEndScaleConfig - 终点场景自动比例尺策略 参数配置
/**
 * 终点场景自动比例尺策略 参数配置
 */
@interface MapBizApproachEndScaleConfig : MapBizMapScaleBaseConfig
@end

#pragma mark - MapBizCompanionForkScaleConfig - 伴随路分歧点场景比例尺策略参数配置
/**
 * 伴随路分歧点场景比例尺策略参数配置
 */
@interface MapBizCompanionForkScaleConfig : MapBizMapScaleBaseConfig
@end

#pragma mark - MapBizIndoorScaleConfig - 室内场景自动比例尺策略 参数配置
/**
 * 室内场景自动比例尺策略 参数配置
 */
@interface MapBizIndoorScaleConfig : MapBizMapScaleBaseConfig
@end

#pragma mark - MapBizMapScaleConfigContainer - 自动比例尺策略参数
/**
 * 自动比例尺策略参数
 */
@interface MapBizMapScaleConfigContainer : NSObject
/**
 * 路况拥堵策略配置
 */
@property (nonatomic, strong) MapBizTrafficJamScaleConfig *trafficJamScaleConfig;
/**
 * 弯道识别策略配置
 */
@property (nonatomic, strong) MapBizCurveRouteScaleConfig *curveRouteScaleConfig;
/**
 * 常规策略配置
 */
@property (nonatomic, strong) MapBizNormalScaleConfig *normalScaleConfig;
/**
 * 起点策略配置
 */
@property (nonatomic, strong) MapBizStartUpScaleConfig *startUpScaleConfig;
/**
 * 终点策略配置
 */
@property (nonatomic, strong) MapBizApproachEndScaleConfig *approachEndScaleConfig;
/**
 * 伴随分歧点策略配置
 */
@property (nonatomic, strong) MapBizCompanionForkScaleConfig *companionForkScaleConfig;

/**
 * 室内路段导航策略配置
 */
@property (nonatomic, strong) MapBizIndoorScaleConfig *indoorScaleConfig;
@end
NS_ASSUME_NONNULL_END

#endif
