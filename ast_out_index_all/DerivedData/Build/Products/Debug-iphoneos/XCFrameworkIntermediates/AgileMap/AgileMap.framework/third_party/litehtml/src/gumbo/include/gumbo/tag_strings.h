// Generated via `gentags.py src/tag.in`.
// Do not edit; edit src/tag.in instead.
// clang-format off
"html",
"head",
"title",
"base",
"link",
"meta",
"style",
"script",
"noscript",
"template",
"body",
"article",
"section",
"nav",
"aside",
"h1",
"h2",
"h3",
"h4",
"h5",
"h6",
"hgroup",
"header",
"footer",
"address",
"p",
"hr",
"pre",
"blockquote",
"ol",
"ul",
"li",
"dl",
"dt",
"dd",
"figure",
"figcaption",
"main",
"div",
"a",
"em",
"strong",
"small",
"s",
"cite",
"q",
"dfn",
"abbr",
"data",
"time",
"code",
"var",
"samp",
"kbd",
"sub",
"sup",
"i",
"b",
"u",
"mark",
"ruby",
"rt",
"rp",
"bdi",
"bdo",
"span",
"br",
"wbr",
"ins",
"del",
"image",
"img",
"picture",
"iframe",
"embed",
"object",
"param",
"video",
"audio",
"source",
"track",
"canvas",
"map",
"area",
"math",
"mi",
"mo",
"mn",
"ms",
"mtext",
"mglyph",
"malignmark",
"annotation-xml",
"svg",
"foreignobject",
"desc",
"table",
"caption",
"colgroup",
"col",
"tbody",
"thead",
"tfoot",
"tr",
"td",
"th",
"form",
"fieldset",
"legend",
"label",
"input",
"button",
"select",
"datalist",
"optgroup",
"option",
"textarea",
"keygen",
"output",
"progress",
"meter",
"details",
"summary",
"menu",
"menuitem",
"applet",
"acronym",
"bgsound",
"dir",
"frame",
"frameset",
"noframes",
"isindex",
"listing",
"xmp",
"nextid",
"noembed",
"plaintext",
"rb",
"strike",
"basefont",
"big",
"blink",
"center",
"font",
"marquee",
"multicol",
"nobr",
"spacer",
"dialog",
"tt",
"rtc",
