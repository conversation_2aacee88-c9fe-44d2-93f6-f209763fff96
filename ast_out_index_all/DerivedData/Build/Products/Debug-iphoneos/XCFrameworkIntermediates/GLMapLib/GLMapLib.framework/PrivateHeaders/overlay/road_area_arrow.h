#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "overlay/overlay.h"
#include "overlay/road_area_arrow_options.h"

namespace tencentmap {
class RoadAreaArrowImpl;
}

namespace MAPAPI {

/**
 * 车道级导航白箭头
 */
class TXMAPSDK_EXPORT RoadAreaArrow : public Overlay {
  friend class OverlayFactory;

 public:
  /**
   * 设置白箭头的中心线数据
   * @param center_lines 有序的多个白箭头的中心线形点，经纬度坐标
   */
  void SetCenterLineData(const std::vector<RoadAreaArrowGeometry> &center_lines);

  /**
   * 每一个白箭头的头部是否需要旋转
   * @param needs的顺序与center_line顺序一致，默值{false}
   */
  void SetRotateHead(const std::vector<bool> &needs);

  /**
   * 白箭头旋转参数
   * @param max_rotate_angle 最大旋转角度，单位度
   * @param start_pitch 旋转的起始俯仰角，单位度
   * @param end_pitch 旋转的结束俯仰角，单位度
   */
  void SetRotateHeadParams(float max_rotate_angle, float start_pitch, float end_pitch);

  /**
   * 白箭头宽度的缩放比例
   * @param ratio 默认值1.0
   */
  void SetWidthRatio(float ratio);

  /**
   * 白箭头高/宽比
   * @param ratio 默认值0.2
   */
  void SetHeightToWidthRatio(float ratio);

  /**
   * 白箭头三角底边宽度/宽比
   * @param ratio 默认值1.56
   */
  void SetHeadBottomToWidthRatio(float ratio);

  /**
   * 白箭头颜色值，可设置也可不设置，有默认值
   * @param roof_fill_color 顶面填充颜色
   * @param roof_border_color 顶面描边颜色
   * @param wall_color 侧面描边颜色
   * @param inner_border_color 顶面内描边颜色
   */
  void SetArrowColor(const MapColor4ub &roof_fill_color, const MapColor4ub &roof_border_color,
                     const MapColor4ub &wall_color, const MapColor4ub &inner_border_color);

  /**
   * 白箭头的透明渐变参数
   * @param roof_gradual_start 顶面透明渐变起始位置
   * @param roof_gradual_end 顶面透明渐变终止位置
   * @param other_gradual_start 其他部分透明渐变起始位置
   * @param other_gradual_end 其他部分透明渐变起始位置
   */
  void SetGradualParam(float roof_gradual_start, float roof_gradual_end, float other_gradual_start,
                       float other_gradual_end);

  /**
   * 每一个白箭头的是否进行流光
   * @param needs的顺序与center_line顺序一致，默值{false}
   */
  void SetFlowLightStatus(const std::vector<bool> &needs);

  /**
   * 设置流光纹理
   */
  void SetFlowLightTexture(TMBitmap *bitmap);

  /**
   * 设置流光速率，单位米/秒，默认值30m/s
   */
  void SetFlowLightSpeed(float speed);

  /**
   * 渲染内部测试使用
   * 设置流光纹理
   */
  void SetFlowLightTexture(const std::string &texture);

  /**
   * 设置白箭头动画
   */
  void SetRoadAreaArrowAnimation(const std::vector<float> &gradual_base_target_pos, float gradual_animation_time);

  /**
   * 获取渲染生成的中心线, 只在渲染线程中调用有效
   */
  void GetRenderCenterLine(std::vector<std::vector<MapVector3d>>& arrow_geometry,
                           std::vector<MapVector3f>& width_in_out);
 private:
  using Overlay::Overlay;
  std::shared_ptr<tencentmap::RoadAreaArrowImpl> GetImpl();

};

}
#endif
