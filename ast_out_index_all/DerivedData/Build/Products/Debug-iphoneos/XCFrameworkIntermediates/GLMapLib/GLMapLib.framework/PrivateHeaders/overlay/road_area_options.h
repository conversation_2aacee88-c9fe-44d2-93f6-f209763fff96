//
// Created by cuipanpan on 2022/6/24.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <functional>
#include <tuple>
#include "../base/map_define.h"
#include "overlay.h"
#include "had_structure.h"
#include "road_area_animation.h"
namespace MAPAPI {

/**
 * 引导面图片使用方式
 */
enum RoadAreaTextureUseStyle{
    RoadAreaTextureUseStyle_Flat,   //  平铺
    RoadAreaTextureUseStyle_ClampY, //  Y方向截断
};

/**
 * RoadAreaOverlay 使用场景
 */
enum RoadAreaOverlayScene{
    RoadAreaOverlayScene_Polygon,                   //  引导面
    RoadAreaOverlayScene_Arrow,                     //  可变箭头
    RoadAreaOverlayScene_FixedArrow,                //  固定箭头
    RoadAreaOverlayScene_Text,                      //  文字
    RoadAreaOverlayScene_Default
};

class MapImpl;
class TXMAPSDK_EXPORT RoadAreaOptions : public OverlayOptions {
public:
    RoadAreaOptions();
    ~RoadAreaOptions();

    /**
     设置道路数据
     @param data 数据
     */
    void SetRoadAreaData(mapbase::HAD::RoadAreaData& data);

    /**
     获取道路数据
     */
    mapbase::HAD::RoadAreaData& GetRoadAreaData() const;
    
    /**
     获取道路数据是否发生了改变
     */
    bool HasChangedRoadAreaData() const;
    
    /**
     设置动画数据
     @param animation 动画数据
     */
    void UpdateRoadAreaAnimation(RoadAreaAnimation& animation);
    
    /**
     获取动画数据
     */
    RoadAreaAnimation& GetRoadAreaAnimation() const;
    
    /**
     获取动画数据是否发生了改变
     */
    bool HasChangedRoadAreaAnimation() const;
    
    /**
     设置道路数据
     @param bitmap 纹理数据
     @param animationTime 动画时长（秒单位）
     @param animationCallback 动画回调
     */
    void SetRoadAreaTexture(TMBitmap* bitmap, float animationTime = 0.0f, std::function<void(bool)> animationCallback = nullptr);

    /**
     * 设置边线数据信息
     * @param width: 边线宽度,单位:米
     * @param bitmap: 边线的纹理数据
     */
    void SetStrokeTexture(float width, TMBitmap* bitmap);
    
    bool HasChangedStrokeTexture() const;

    TMBitmap* GetStrokeTexture() const;

    float GetStrokeWidth() const;
    
    /**
     * 设置RoadAreaOverlay使用场景
     * @param scene: RoadAreaOverlay 使用场景
     */
    void SetRoadAreaOverlayScene(RoadAreaOverlayScene scene);
    
    bool HasRoadAreaOverlayScene() const;

    RoadAreaOverlayScene GetRoadAreaOverlayScene() const;

    /**
     获取bitmap
     @param bitmap 纹理信息
     */
    void GetRoadAreaTexture(TMBitmap** bitmap) const;

    float GetTextureAnimationTime() const;

    std::function<void(bool)> GetTextureAnimationCallback() const;

    /**
     * 引导面、流向箭头整体透明度
     * @param start 起始透明度
     * @param end 结束透明度
     * @param animationTime 动画时长（秒单位）
     * @param animationCallback 动画回调
     */
    void SetAlpha(float start, float end, float animationTime = 0.0f, std::function<void(bool)> animationCallback = nullptr);
    std::tuple<float, float, float, std::function<void(bool)>> GetAlpha() const;

    bool HasChangedAlpha() const;

    /**
     获取bitmap是否发生了改变
     */
    bool HasChangedRoadAreaTexture() const;

    /**
     坐标转换
     */
    void CoordinateTransform(MapImpl * mapImpl);
    void CoordinateTransform();
    
    /**
     重置数据、动画、纹理是否改变标识符。仅限渲染引擎内部调用
     */
    void ResetChangeSign() const;
    
    /**
     获取OverlayType
     */
    virtual OverlayType GetType() const override { return OverlayType::RoadArea; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;

    /**
     * 设置是否支持点击
     * @param interactive
     */
    void SetInteractive(bool interactive);
    bool GetInteractive() const;
    
    void SetTextureUseStyle(RoadAreaTextureUseStyle style);
    RoadAreaTextureUseStyle GetTextureUseStyle() const;
    
    std::string Description() const override;
    
    std::string ToJson() const;

private:
    struct Impl;
    std::shared_ptr<RoadAreaOptions::Impl> GetImpl();
    std::shared_ptr<RoadAreaOptions::Impl> GetImpl() const;
};

}
#endif //NOT_USE_4K_ELEMENT
