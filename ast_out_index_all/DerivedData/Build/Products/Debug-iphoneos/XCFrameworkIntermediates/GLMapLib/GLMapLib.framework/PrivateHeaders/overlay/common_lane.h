#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "../base/map_define.h"
#include "overlay.h"
#include "common_lane_options.h"

namespace tencentmap{
class Macro4KLane;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT CommonLane : public Overlay{
    friend class OverlayFactory;
public:
    void SetOptions(const CommonLaneOptions & options);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KLane> GetImpl();
};

}
#endif