//
//  map_render.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/26.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_CORE_H__
#define __MAPAPI_BASE_MAP_CORE_H__

#include <stdio.h>
#include <string>
#include <memory>
#include "base/map_define.h"
#ifndef WITHOUT_PLOG
#include "plog/plog.h"
#endif

namespace MAPAPI {

class MapCallback;
class TrafficManager;
class DownloadManager;

class TXMAPSDK_EXPORT LogWritter {
 public:
  virtual ~LogWritter() {}
  virtual bool OnWriteLog(const char* log_content, int length) = 0;
};

class TXMAPSDK_EXPORT NetProvider {
 public:
  virtual ~NetProvider(){}
  virtual std::shared_ptr<MapNetResponse> DoGet(const std::string& url, const std::string& user_agent) = 0;
  virtual std::shared_ptr<MapNetResponse> DoPost(const std::string& url, const std::string& user_agent, unsigned char* post_data, int post_data_len) = 0;
};


class TXMAPSDK_EXPORT MapCore : public MapComponent{
public:
    DownloadManager * GetDownloadManager();
    
    TrafficManager * GetTrafficManager();
    
    void SetMapCallback(MapCallback * callback);
    
    /**
     * 地图回前台
     */
    void Resume();
    
    /**
     * 地图退后台
     */
    void Pause();
    
    /**
     * 休眠通知
     * 地图会在该调用内部销毁部分资源
     *
     * 1. Needs OpenGL context
     * 2. Task thread should be stopped before hibernate.
     */
    void Hibernate();
    
    /**
     * 内存警告
     * 降低cache阈值，并未立刻释放资源
     */
    void MemoryWarning();
    
    /**
     * 渲染一帧
     */
    void DrawFrame();
      
    /**
     * 查询引擎是否需要重绘
     *
     * @return 是否需要重绘
     */
    bool IsNeedDraw();
    
    /**
     * 设置重绘的flag
     *
     * @param bNeedDraw 是否需要重绘
     */
    void SetNeedDraw(bool bNeedDraw);
    
     /**
      * 更新一帧，主要做动画
      *
      * Note:可以更改为当前时间，时间差在引擎内部计算
      * @param duration 两帧之间时间差
      */
    void UpdateDuration(double duration);
    
    /**
     * 获取屏幕密度。
     * 就是MapCreate时，外部传入的density。
     */
    float GetScreenDensity();
    
    bool IsLoadingFinished();
    
    /**
     * 资源线程load resource
     *
     * @return 是否加载成功
     */
    bool LoadResources();

    /**
     * 退出地图前调用, 停止地图、销毁引擎资源
     */
    void Stop();
  
    /**
     * 设置Log日志写入回调
     */
    static void SetLogWritter(LogWritter* log_writter);
    
    /**
     * 处理全量/增量资源dat文件
     * 需要引擎启动前调用或修改poi资源后调用
     * @param icon_incr_full_dat_path 原始全量icon dat资源路径(比如 ./data/cfg/icon_incr.dat)
     * @param icon_incr_add_dat_path 增量icon dat资源路径(比如 ./data/cfg/icon_incr_add.dat)
     * @param icon_dest_path 解压dat生成png和poi 大图的目标文件夹
     * @param cfg_path 配置文件夹路径
     */
    static void ProcessFullIconIncr(const std::string& icon_incr_full_dat_path, const std::string& icon_incr_add_dat_path, const std::string& icon_dest_path, const std::string& cfg_path);

#ifndef WITHOUT_PLOG
    /**
     * 初始化日志. plog
     * @param log_path 日志存放的文件夹路径，必须是已经存在的目录
     * @param log_level 日志级别，见plog::LogLevel
     * @param log_dest 日志输出&记录方式，见plog::LogDest
     */
    static void InitLogger(const std::string& log_path, plog::LogLevel log_level, plog::LogDest log_dest);

    /**
     * 获取plog::LoggerConfig对象. 如果没有初始化，则返回nullptr
     */
    static std::shared_ptr<plog::LoggerConfig> GetLoggerConfig();
#endif
  
protected:
    friend class MapImpl;
    MapCore();
};

class TXMAPSDK_EXPORT DownloadManager : public MapComponent{
public:
    /**
     * 设置底图数据域名
     */
    void SetServerHost(const std::string & serverHost);
    
    /**
     * 根据不同的type设置tag.比如
     *
     * setServerUrlTag(MapUrlTagType_BaseMap, "mvd_map")
     * setServerUrlTag(MapUrlTagType_Landmark, "landmark")
     *
     * @param type 指定的type
     * @param tag tag
     */
    void SetServerUrlTag(MapUrlTagType type, const std::string & tag);
    
    
    /**
     * 下载的数据传给引擎
     *
     * @param urlString 瓦片下载地址
     * @param data 数据
     * @param dataSize 数据大小
     * @param httpcode http状态码
     */
    void WriteDownloadData(const std::string & urlString, const void *data, int dataSize, int httpcode);
    
    /**
     * 写入封路数据
     */
    void WriteBlockRouteData(const char *urlString, const void* data, int iDataLen);
};

class TXMAPSDK_EXPORT MapCallback {
public:
    virtual ~MapCallback() {}
    
    virtual TMBitmap* OnLoadImage(const char *fileName, int code, float *anchorPointX1, float *anchorPointY1) = 0;
    
    //TODO:AAA
    virtual void    OnDownload(const std::string & urlString, MapTileID tileID) = 0;
    
    //TODO:AAA
    virtual void    OnCancelDownload(const std::string & urlString, MapTileID tileID) = 0;
    
    virtual GlyphMetrics  OnCalcTextSize(unsigned short *text, int count, int fontSize, bool bold) = 0;
    
    virtual void    OnDrawText(TMBitmap *textBitmap, unsigned short *text, int count, int fontSize, bool bold, float density, GlyphMetrics metrics) = 0;
    
    virtual void*   OnReadFile(const std::string & fileName, int *data_size) = 0;
    
    virtual void    OnWriteFile(const std::string & fileName, const void *data, int dataSize) = 0;

    virtual TMBitmap* OnLoadGlyph(unsigned short text, int fontSize, bool bold, MapRectF* rect) = 0;
    
    virtual void    OnBlockRoute(const std::string & urlStr) = 0;
    
    virtual void*   OnGetGLContext() = 0;
};

}

#endif /* __MAPAPI_BASE_MAP_CORE_H__ */
