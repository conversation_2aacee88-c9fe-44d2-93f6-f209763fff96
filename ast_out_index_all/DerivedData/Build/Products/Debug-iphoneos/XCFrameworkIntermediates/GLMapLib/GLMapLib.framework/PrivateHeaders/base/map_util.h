//
//  map_util.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/7.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_UTIL_H__
#define __MAPAPI_BASE_MAP_UTIL_H__

#include <stdio.h>
#include <string>
#include "base/map_define.h"

namespace MAPAPI {

typedef std::shared_ptr<TMBitmap>  TMBitmapPtr;

struct TMBitmapDeleter{
    void operator ()( TMBitmap* p){
        if(NULL != p) {
            if (p->buffer) {
                free(p->buffer);
                p->buffer = NULL;
            }
            if (p) {
                free(p);
                p = NULL;
            }
        }
    }
};

class TXMAPSDK_EXPORT MapUtil
{
public:
    /**
     * 绘制文字到已有bitmap上，返回叠加文字后的新bitmap
     */
    //TODO:GLMapAnnotationText，只有手图IOS在用
//    TMBitmap *DrawAnnotationObject(TMBitmap *iconBitmap, GLMapAnnotationText *annotationObject, MapVector2f *anchorPoint);
    
    /**
     显示普通ICON的外接矩形框，可用于压盖调试。
     */
    void SetMarkerIconShowDebugRectangle(bool isShowRect){}
  
    /**
     * 获取引擎状态信息；
     * 上层崩溃时，调用该接口获取引擎的一些核心信息，
     */
    const char * GetMapEngineReportMapParms(){return "";}

    /**
     * 平滑函数(2阶或者3阶贝塞尔曲线)
     * @param input 首尾点代表平滑后曲线上的首尾点，中间的点代表平滑曲线控制点
     * @param output 平滑后曲线点
     * @param count 平滑后的点个数
     * @return 平滑结果
     */
     static bool bezier(const std::vector<MapVector3d> &input, std::vector<MapVector3d>& output, int count);

     /**
      * 射线和曲线是否相交
      * @param start 射线起点
      * @param dir 射线单位方向向量
      * @param segs 曲线
      * @param intersect 交点(曲线上的点)
      * @return 判断结果
      */
      static bool DirSegIntersect(const MapVector3d &start, const MapVector3d &dir, std::vector<MapVector3d>& segs, MapVector3d& intersect);
 
 
    /**
     * 创建非压缩纹理的bitmap类
     * @param buffer  bitmap像素数据
     * @param format bitmap类型
     * @param width 图片宽度
     * @param height 图片高度
     * @param stride 一行的字节数
     * @param scale 图片显示的缩放比例
     * @param ownBuffer 如果buffer存在，是否执行深拷贝
     * @return bitmap智能指针
     */
    static TMBitmapPtr TMBitmapContextCreateSptr(void *buffer, TMBitmapFormat format, int width, int height, int stride, float scale, TM_BOOL ownBuffer = TM_NO);
    
    /**
     * 创建压缩纹理bitmap类
     * @param buffer  压缩纹理二进制数据
     * @param buffer_len  buffer字节长度
     * @param scale 图片显示的缩放比例
     * @return bitmap智能指针
     */
    static TMBitmapPtr TMCompressTextureCreateSptr(void *buffer, int buffer_len, float scale);
};

}

#endif /* __MAPAPI_BASE_MAP_UTIL_H__ */
