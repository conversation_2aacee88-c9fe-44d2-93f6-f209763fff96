//
// Created by felix on 2024/4/18.
//

#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "../base/map_define.h"
#include "overlay.h"
#include "road_area_animation.h"
#include "road_companionArea_options.h"
#include "road_area_texture_options.h"
#include "had_structure.h"


namespace tencentmap{
class RoadCompanionAreaOverlay;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT RoadCompanionArea : public Overlay{
    friend class OverlayFactory;
public:
    /**
     更新渲染单元
     @param road_area_data  道路信息
     */
    void SetRoadAreaData(mapbase::HAD::RoadAreaData & road_area_data);
    /**
     更新纹理信息
     @param bitmap  纹理信息
     */
    void SetRoadAreaTexture(TMBitmap* bitmap, float animationTime = 0.0f, std::function<void(bool)> animationCallback = nullptr);
    void SetRoadLineTexture(TMBitmap* bitmap);
    void SetRoadMaskTexture(TMBitmap* bitmap);

    /*
     * 设置引导线的擦除位置及透明渐变区间
     * eraseGradStartRatio: 擦除透明渐变开始的位置
     * eraseGradEndRatio: 擦除透明渐变结束的位置
     * endGradStartRatio: 尾部透明渐变开始的位置
     * endGradEndRatio: 尾部透明渐变结束的位置
     * required: eraseGradEndRatio <= endGradStartRatio
     * */
    void setGuideLineAlphaRange(float eraseGradStartRatio,
                                float eraseGradEndRatio,
                                float endGradStartRatio,
                                float endGradEndRatio);

    /**
     设置roadarea层级关系类型
     @param type  kCrossLane 在标线下，kInLanez在标线上
     */
    void SetRoadAreaRelationshipType(mapbase::HAD::RelationShipType type);

    /*
     * 设置伴随引导面类型
     * type: 伴随引导面类型
     * */
    void SetCompanionAreaType(mapbase::HAD::CompanionType type);

    /*
     * 设置引导线的线宽透明渐变区间，线宽从0到1.假设左边总是0.0，右边为1.0
     * widthEraseGradStartUV: 透明度为1.0时的UV值
     * widthEraseGradEndUV: 透明度为0.0时的UV值
     * */
    void SetGuideLineWidthAlphaRange(float widthEraseGradStartUV,
                                     float widthEraseGradEndUV);
    /**
   * 设置伴随引导面颜色
   * @param color探测波的颜色
   * @param duration 动画播放时间，单位：秒，设置为0.0时直接变为最终位置
   */
    void SetGuideLineColorWithDuration(const MapColor4ub &color, float duration);

private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::RoadCompanionAreaOverlay> GetImpl();
};

}
#endif
