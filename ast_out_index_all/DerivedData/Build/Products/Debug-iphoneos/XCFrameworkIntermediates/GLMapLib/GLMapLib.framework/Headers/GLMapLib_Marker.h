//
//  GLMapLib_Marker.h
//  GLMapLib2.0
//
//  Created by lihuafeng on 2018/11/12.
//  Copyright © 2018 nopwang. All rights reserved.
//

#ifndef GLMapLib_Marker_h
#define GLMapLib_Marker_h

#include "GLMapLib_Base.h"
#include "MapDataType.h"
#ifdef __cplusplus
extern "C" {
#endif
    // Marker related.
    
    /**
     创建MapIcon时需要Type类型
     */
    typedef MAP_ENUM(int, MapIconType)
    {
        MapIconType_2D_GeoCoord_GeoAngle,           // 2D Icon, 地理坐标，地理角度，正交绘制于屏幕上
        MapIconType_2D_GeoCoord_ScreenAngle,        // 2D Icon, 地理坐标，屏幕角度
        MapIconType_2D_OnScreen,                    // 2D Icon, 类似于屏幕UI
        MapIconType_3D,                              // 3D Icon, 贴于地面
        MapIconType_Anim                            //动画Icon，做uv动画
    };
    
    /**
     * Overlay内部是按点，线，面三个图层的顺序从上到下绘制，如果需要把某个overlay移到
     * 其它图层显示，可以修改其OverlayGeoType
     */
    typedef MAP_ENUM(int, OverlayGeoType)
    {
        Overlay_Geo_Point   = 0,
        Overlay_Geo_Line    = 1,
        Overlay_Geo_Polygon = 2,
        Overlay_Geo_3DModel = 3,
        Overlay_Geo_Num     = 4,
    };

    /**
     * 描述Overlay元素的数据源, 默认Common；
     */
    typedef MAP_ENUM(int, OverlayDataSource)
    {
        OverlayDataSource_Common         = 0,
        OverlayDataSource_CameraBig      = 1,
        OverlayDataSource_CameraSmall    = 2,
        OverlayDataSource_TrafficLight   = 3,
        OverlayDataSource_BlockRoad      = 4
    };
    
    /**
     创建Marker时，指定避让路线规则
     */
    typedef MAP_ENUM(int, AvoidRouteType)
    {
        AvoidRouteType_None      = 0,                   // Marker 不避让任何路线
        AvoidRouteType_All       = 1,                   // Marker 避让所有蚯蚓线；引擎计算
        AvoidRouteType_appointed = 2,                   // Marker 避让指定路线，上层设置
        AvoidRouteType_MainRoad  = 3                    // Marker 避让主路（内部默认最上层一条蚯蚓线就是主路）
    };

    /**
     避让类型，
     */
    typedef MAP_ENUM(int, AvoidType)
    {
        AvoidType_UI      = 0,
        AvoidType_Route   = 1,
        AvoidType_Locator   = 2,
    };
    
    typedef struct MapMarkerAvoidRouteRule
    {
        AvoidRouteType  avoidRouteType;                //避让蚯蚓线规则
        int             avoidRoutesID[MapRouteMaxNum]; //需要避让的蚯蚓线ID
        int             avoidRouetsIDCount;            //需要避让的蚯蚓线总数
    }MapMarkerAvoidRouteRule;
    
    /**
     描述Marker避让的详细规则
     */
    typedef struct MapMarkerAvoidDetailedRule
    {
        int                      sourceType;                                       // 当前Marker的数据源类型(0-99 保留为引擎内部使用, 100以上端上自行设定维护)
        int                      minMarginWithSameSourceType;                      // 与相同数据源之间避让的最小预留距离(负数 代表同类之间不进行避让, 其他代表 设定元素避让情况下 同类元素之间的最小间隔)
    }MapMarkerAvoidDetailedRule;
    
    /**
     MapMarkerIconInfo 结构体，用来通过引擎创建marker 对应于MapIconInfo
     */
    typedef struct MapMarkerIconInfo
    {
        MapIconType type;
        char        imageName[MapImageNameMaxLength];
        MapVector3d coordinate;
        MapVector2f anchorPoint;
        float       angle;
        float       alpha;
        MapVector3f mixColor;
        float       colorAnimDuration;
        MapVector2f scale;
        MapVector2ub flip;
        bool        avoidAnnotation;
        bool        interactive;
        int         markerID; // Output markerID
        bool        isNeedAvoidCallback;
        char        rockerDotName[MapImageNameMaxLength];
        char        rockerLineName[MapImageNameMaxLength];
        int         cullType;
    }MapMarkerIconInfo;


    
    /**
     subPoi结构体
     */
    typedef struct MapMarkerSubPoiInfo
    {
        char        imageName[MapImageNameMaxLength];
        char        secondImageName[MapImageNameMaxLength];
        MapVector2d coordinate;
        MapVector2f anchorPoint;
        MapVector2f secondAnchorPoint;
        bool        interactive;
        bool        avoidAnnotation;
        int         markerID; // Output markerID
    }MapMarkerSubPoiInfo;
    
    /**
     QPathViewOverlay使用类型
     */
    typedef MAP_ENUM(int, MapPrimitiveType)
    {
        MapPrimitiveType_Polygon            = 1 << 0,
        MapPrimitiveType_LineLoop           = 1 << 1,
        MapPrimitiveType_Line               = 1 << 2,
    };
    
    typedef struct MapHoleInfo
    {
        MapVector2d*      points;
        int               pointCount;
    } MapHoleInfo;
    
    typedef struct MapPatternStyle
    {
        float    startScale;
        float    endScale;
        float    width;
        int      patternCount;   // 虚线属性个数
        int*     pattern;        // 虚线属性
    }MapPatternStyle;

    /**
     QPathViewOverlay 使用
     */
    typedef struct MapPrimitive
    {
        MapPrimitiveType    primitiveType;
        MapColor4ub         color;          // 填充颜色
        float               lineWidth;      // 对Line, LineLoop代表线的宽度，对Polygon代表描边宽度
        
        int                 pointsCount;
        MapVector2d         origin;         // Origin is the center point for primitive scaling.
        MapVector2d*        points;         // Points are relative to origin.
        MapVector3d*        points3D;       // 可能不在一个平面上的点
        
        MapColor4ub         borderColor;    // 仅对Polygon类型有效，代表描边颜色
        MapHoleInfo*        holes;          // 仅对Polygon类型有效，每个洞的形状点信息
        int                 holesCount;     // 仅对Polygon类型有效，洞的个数
        double              shrink_dist_;   //单位：米
        float               height;         // 仅对Polygon类型有效，代表高度
        
        int                 primitiveID;    // Output primitiveID
        
        int                 patternCount;   // 虚线属性个数
        int*                pattern;        // 虚线属性
        MapPatternStyle*    externPattern;  // 不同比例尺配置不同宽度和pattern
        int                 externCount;    // 不同比例尺配置个数
        bool                onTop;          // 是否优先显示，优先显示时优先显示，否则优先显示底图
        bool                interactive;    //是否可点击交互
    } MapPrimitive;
    
    typedef enum  _TEXT_POS
    {
        TXT_INVALID = 0,
        TXT_BOTTOM = 2,
        TXT_LEFT = 4,
        TXT_RIGHT = 6,
        TXT_TOP = 8,
        TXT_MIDDLE = 10
    }TEXT_POS;
    #define MarkerAnnoMaxNameLen 256
    /**
     MapMarkerAnnotationInfo 结构体，用来通过引擎创建marker 对应于MapMarkerAnnotationInfoInfo
     */
    typedef struct MapMarkerAnnotationInfo
    {
        unsigned char subtype[5]; // text's relative position to icon. use enum  TEXT_POS 最多可指定五个位置，引擎按顺序选择最优位置进行显示
        unsigned char effect; // (bit:0) for underline; (bit:1) for bold; (bit:2) for outline or halo; (bit:3) for background; (bit:4) for textonicon; other bits is unused now;
        
        float fontSize;           // 单位 dp
        float haloSize;           // text halo size 单位 dp;
        float textSpace;          // the min space between text and text 单位 dp.
        float iconSpace;          // the min space between icon and text 单位 dp.
        float rowSpace;         //the space between text rows.
        
        TMColor color;
        TMColor backgroundColor;
        
        MapVector3d coordinate;
//        double altitude;      // Marker高度

        float       angle;
        float       alpha;
        MapVector2f scale;
        MapVector2f iconAnchorPt;  //  default(0.5,0.5)
        
        char iconName[MapImageNameMaxLength];
        bool interactive;
        bool avoidAnnotation; //是否避让底图标注
        bool isHideTextWhenAvoidFailed; //当自身文字无法避让时，是否隐藏自身文字
        bool isShowDebugRect;          //是否显示碰撞检测矩形框，调试用
        int  markerID; // Output markerID
        int nameCharCount; //如需换行，可以设定换行分隔符'\t',目前最多只支持两行
        unsigned short name[MarkerAnnoMaxNameLen]; //最多支持32个字符
    }MapMarkerAnnotationInfo;

	typedef struct MapMarkerClusterData{
		int item_id; //点选时返回，与底图创建的markerid不同
		MapVector2d point; //点的位置
		unsigned short name[MarkerAnnoMaxNameLen]; //poi名称
	}MapMarkerClusterData;
 
    /**
     * 生成一个聚类实例，设置聚类的点数据，多实例用于支持多个aoi面内部点聚类
     * @param data 参加聚类的点信息
     * @param dataCount 点个数
	 * @param clusterStyle 聚类时的显示样式。iconName、markerID、nameCharCount、name字段不需要设置
	 * @param singleStyle 显示单独点时的样式。iconName、markerID、nameCharCount、name字段不需要设置
	 * @param radiusDp 以Dp为单位的聚类范围，方形范围内的点会聚类显示
	 * @param maxShowNum 聚类点最大显示的数字，数量超过时显示99+效果
	 * @param minMaxDispLevel 大于x，小于y时聚类点显示，其他范围时隐藏，必须：x<y
	 * @param minMaxClusterLevel 聚类发生的级别范围，低于x时始终显示聚类形态但不重新聚类，高于y时显示单独的点，[x,y]以内正常聚类显示，必须：x<y
	 * @return 生成的新聚类实例的ID
     */
    int MapCreateClusterGroup(void *pWorld, MapMarkerClusterData *data, int dataCount, MapMarkerAnnotationInfo *clusterStyle, MapMarkerAnnotationInfo *singleStyle, float radiusDp, int maxShowNum, MapVector2i minMaxDispLevel ,MapVector2i minMaxClusterLevel);

	/**
	 * 移除掉一个聚类实例
	 * @param group_id 聚类的实例ID
	 */
	void MapRemoveClusterGroup(void *pWorld, int group_id);

    /**
     * 增加一个聚类点数据
	 * @param item 一个参与聚类的点数据
     * @param group_id 要加入的聚类实例ID
     */
    void MapAddClusterItemToGroup(void *pWorld, MapMarkerClusterData *item, int group_id);

    /**
     * 删除一个聚类点数据
     * @param item_id 要删除的点的id，由MapMarkerClusterData中创建时提供
	 * @param group_id 要删除的点的id所属的聚类ID
     */
	void MapRemoveClusterItemFromGroup(void *pWorld, int item_id, int group_id);

	/**
	 * 设置聚类点的显示/隐藏
	 * @param group_id 聚合组id
	 * @param visible true显示，false隐藏
	 */
	void MapSetClusterVisible(void *pWorld, int group_id, bool visible);

    /**
     * 设置item_id对应的聚合结果，如果是单点状态，是否显示文字
     * @param pWorld 场景句柄
     * @param group_id 组ID
     * @param item_id itemID（端上生成）
     * @param visible 文字是否可见
     */
    void MapSetClusterItemTextVisible(void *pWorld, int group_id, int item_id, bool visible);

    #pragma mark 普通marker相关接口
    /**
     创建Marker类型Overlay
     传入数组并不能优化性能，建议修改为创建单个overlay，返回ID
     @param pWorld 场景句柄
     @param markerInfos marker属性数组
     @param size 数组大小
     */
    void MapMarkerIconCreate(void *pWorld, MapMarkerIconInfo *markerInfos, int size);
    
    /**
     创建SubPoi类型Overlay，demo中调用保留空实现
     传入数组并不能优化性能，建议修改为创建单个overlay，返回ID
     【注意】此接口已经废弃，内部已是空实现。
     @param pWorld 场景句柄
     @param markerInfos marker属性数组
     @param size 数组大小
     */
    void MapMarkerSubPoiCreate(void *pWorld, MapMarkerSubPoiInfo *markerInfos, int size);
    
    /**
     创建Primtive类型Overlay，用于绘制线，面
     如果批量创建简单多边形，会进行合批优化（简单多边形：无洞无描边）
     @param pWorld 场景句柄
     @param markerInfos primitive属性数组
     @param size 数组大小
     */
    void MapMarkerPrimitiveCreate(void *pWorld, MapPrimitive *markerInfos, int size);
    
    /**
     获取marker类型overlay的相关属性
     
     @param pWorld 场景句柄
     @param markerInfo 属性描述
     @return 是否成功获取
     */
    bool MapMarkerIconGetInfo(void *pWorld, MapMarkerIconInfo *markerInfo);
    
    /**
     显示普通ICON的外接矩形框，可用于压盖调试。
     */
    void MapMarkerIconShowDebugRectangle(void *pWorld, bool isShowRect);
    
    
    /**
     获取primitive类型overlay的相关属性
     
     @param pWorld 场景句柄
     @param markerInfo 属性描述
     @return 是否成功获取
     */
    bool MapMarkerPrimitiveGetInfo(void *pWorld, MapPrimitive *markerInfo);
    
    /**
     更新marker类型overlay的属性
     
     @param pWorld 场景句柄
     @param markerInfos 属性描述
     @param size 数组大小
     */
    void MapMarkerIconModifyInfo(void *pWorld, MapMarkerIconInfo *markerInfos, int size);
    
    
    /**
     更新primitive类型overlay的属性
     
     @param pWorld 场景句柄
     @param markerInfos 属性描述
     @param size 数组大小
     */
    bool MapMarkerPrimitiveModify(void *pWorld, MapPrimitive *markerInfos, int size);
    
    /**
     按ID删除overlay数组
     
     @param pWorld 场景句柄
     @param markerIDs ID数组
     @param size 数组大小
     */
    void MapMarkerDelete(void *pWorld, int *markerIDs, int size);
    
    /**
     设置Marker需要避让的全量UI控件区域; 当个数为0代表清空UI区域, 主线程调用；
     @param pWorld 场景句柄
     @param uiAreas 控件区域列表；像素坐标
     @param size    控件区域个数
     @param isDebugUiAreas 调试使用，将框画到屏幕上
     */
    void MapMarkerSetAvoidingUIAreas(void *pWorld, TMRect* uiAreas, int size, bool isDebugUiAreas);
    
    /**
     设置Overlay是否可点
     @param pWorld 场景句柄
     @param overlay_id  overlayid
     @param interactive  true可点，false不可点
     */
    void MapOverlaySetInteractive(void *pWorld, int overlay_id, bool interactive);

    /**
     设置marker避让路线的规则
     
     目前只支持MapMarkerIconInfo, MapMarkerGroupIconInfo， MapMarkerAnnotation,其它类型如MapMarkerPrimitive会返回false
     */
    bool MapMarkerSetAvoidRouteRule(void *pWorld, int markerID, MapMarkerAvoidRouteRule * rule);
    
    /**
     设置marker在遇到更高优先级的marker时，是否避让其它marker，默认是false，不避让，直接被高优先级marker压盖。
     
     目前只支持MapMarkerIconInfo, MapMarkerGroupIconInfo, MapMarkerAnnotation, 其它类型如MapMarkerPrimitive会返回false
     */
    bool MapMarkerSetAllowAvoidOtherMarker(void *pWorld, int markerID, bool isAllowAvoidOtherMarker);
    
    /**
     为当前marker(子marker)设置一个MainMarker(主marker)，添加一层虚拟的关联关系。mainMarkerID=0，清除关系。
     如果MainMarker被避让掉，当前marker也一同被避让掉；
     如果MainMarker没有被避让掉，当前marker按照自身情况避让。
     如果用户主动对MainMarker调用SetHidden操作，当前marker不受影响。
     注意：MapMarkerBindSubMainMarker为此接口的升级版本，此接口最好废弃掉
     */
    void MapMarkerSetMainMarker(void * pWorld, int subMarkerID, int mainMarkerID);
    /**
     为两个marker绑定一个虚拟的主子关系。设置mainMarkerID=0时解除绑定。
     注意：
     - 一个主marker可以绑定多个子marker，但一个子marker仅能有一个主maker;
     - 无法绑定多于1层的主子关系
     - 如果主marker被避让掉，子marker也一同被避让掉；
     - 若syncVisibility设置为false，子marker避让隐藏不会影响主marker；若syncVisibility设置为true，子marker避让隐藏时主marker也会隐藏。
     - 若innerCollision设置为false，则主子之间不会相互避让；若设置为true，则主子之间按照“子避让主、子子间看优先级”的逻辑进行避让
     - 若同步显隐和内部碰撞都为true，会实现主子自己把自己碰掉的效果
     
     @param pWorld          场景句柄
     @param subMarkerID     子makrer的ID
     @param mainMarkerID    主marker的ID，设置0时清除绑定
     @param syncVisibility  是否同步显隐：false-主避让隐藏子也隐藏，子避让隐藏不影响主；true-主或子避让隐藏，全部主子隐藏
     @param innerCollision  是否进行内部碰撞：默认为true，false——主子之间不避让；true——子避让主
     */
    void MapMarkerBindSubMainMarker(void * pWorld, int subMarkerID, int mainMarkerID, bool syncVisibility, bool innerCollision);

    /**
     * 获取以给定ID的marker作为主marker的子marker的ID列表
     */
    void MapMarkerGetSubMarkerIDs(void * pWorld, int mainMarkerID, int *subMarkerIDs, int *size);
    /**
     为某个Marker设置更详细的避让规则
     @param pWorld       场景句柄
     @param markerID     MarkerID
     @param pRule        具体规则内容
    */
    void MapMarkerSetAvoidDetailedRule(void *pWorld, int markerID, MapMarkerAvoidDetailedRule* pRule);
    
    /**
     * Overlay内部是按点，线，面三个图层的顺序从上到下绘制，如果需要把某个overlay移到
     * 其它图层显示，可以修改其OverlayGeoType
     */
    void MapMarkerSetGeometryType(void* pWorld, int markerID, OverlayGeoType geoType);
    
    /**
     MapMarkerIconInfo设置备选图片及anchor。
     备选图片会在主图片与其它ICON压盖时显示。
     
     @param image , image = NULL, 表示清除备选图片，清除成功，return true
     @return 无效的markerID，或者marker类型不是MapMarkerIconInfo，会返回false；其它返回true。
     */
    bool MapMarkerIconSetAlternativeImage(void *pWorld, int markerID, const char * image, MapVector2f anchor);
    
    /**
     更新Overlay坐标
     功能：按地理坐标对marker进行移动。
     如相应overlay不存在该属性，更新无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param coordinate 地理坐标（墨卡托坐标）
     */
    void MapMarkerModifyCoordinate(void *pWorld, int markerID, MapVector2d coordinate);

    /**
     * 当调用在渲染线程时不抛消息队列，立即执行
     * @param pWorld 场景句柄
     * @param markerID overlay ID
     * @param coordinate 地理坐标（墨卡托坐标）
     */
    void MapMarkerModifyCoordinateSync(void *pWorld, int markerID, MapVector2d coordinate);
    
    /**
     更新Overlay坐标
     功能：按地理坐标对marker进行移动。
     如相应overlay不存在该属性，更新无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param coordinate 地理坐标（墨卡托坐标）
     */
    void MapMarkerModifyCoordinate3D(void *pWorld, int markerID, MapVector3d coordinate);

    /**
     * 当调用在渲染线程时不抛消息队列，立即执行
     * @param pWorld 场景句柄
     * @param markerID overlay ID
     * @param coordinate 地理坐标（墨卡托坐标）
     */
    void MapMarkerModifyCoordinateSync3D(void *pWorld, int markerID, MapVector3d coordinate);

    /**
     更新Overlay在屏幕坐标系下，相对于当前坐标的一个偏移量。默认为（0,0）。
     功能：按像素坐标对marker进行移动。支持动画。
     如相应overlay不存在该属性，更新无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param screenOffset 屏幕坐标偏移量，像素坐标，x轴向右，y轴向下。
     */
    void MapMarkerModifyScreenOffset(void *pWorld, int markerID, MapVector2f screenOffset);
    
    /**
     更新Overlay图片
     如相应overlay不存在该属性，该操作无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param imageName 图片名
     @param anchorPoint 锚点
     */
    void MapMarkerModifyImage(void *pWorld, int markerID, const char *imageName, MapVector2f anchorPoint);
    
    /**
     更新Overlay角度
     如相应overlay不存在该属性，该操作无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param angle 角度
     */
    void MapMarkerModifyAngle(void *pWorld, int markerID, float angle);
    
    /**
     更新overlay透明度
     如相应overlay不存在该属性，更新无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param alpha 透明度（0.0f ~ 1.0f）
     */
    void MapMarkerModifyAlpha(void *pWorld, int markerID, float alpha);
    
    /**
     更新overlay缩放比例
     如相应overlay不存在该属性，更新无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param scale 缩放比例
     */
    void MapMarkerModifyScale(void *pWorld, int markerID, MapVector2f scale);
    
    /**
     更新overlay颜色值
     如相应overlay不存在该属性，更新无任何影响
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param color 颜色值
     */
    void MapMarkerModifyColor(void *pWorld, int markerID, TMColor color);
    
    /**
     强制overlay同步加载资源，不延迟展示
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param bForceLoad 是否强制加载
     */
    void MapMarkerSetForceLoad(void *pWorld, int markerID, bool bForceLoad);
    
    /**
     获取overlay地理坐标
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 地理坐标，如查找失败，返回(0.0，0.0)
     */
    MapVector2d MapMarkerGetCoordinate(void *pWorld, int markerID);
    
    /**
     获取overlay角度
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 角度，如查找失败，返回0.0f
     */
    float MapMarkerGetAngle(void *pWorld, int markerID);
    
    /**
     获取overlay透明度
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 透明度，如查找失败，返回1.0f
     */
    float MapMarkerGetAlpha(void *pWorld, int markerID);
    
    /**
     获取overlay缩放比例
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 缩放比例，如查找失败，返回(1.0f, 1.0f)
     */
    MapVector2f MapMarkerGetScale(void *pWorld, int markerID);
    
    /**
     获取overlay对应的屏幕区域
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 屏幕区域，如查找失败，返回(0.0f, 0.0f, 0.0f, 0.0f)
     */
    MapVector4f MapMarkerGetScreenArea(void *pWorld, int markerID);
    
    /**
     设置overlay优先级，默认优先级为0
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param priority 优先级，支持负数
     */
    void MapMarkerSetPriority(void *pWorld, int markerID, int priority);
    
    /**
     获取overlay优先级，默认优先级为0
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 优先级，可能为负数
     */
    int  MapMarkerGetPriority(void *pWorld, int markerID);
    
    /**
     设置overlay显示的比例尺范围
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param nMinScaleLevel 最小比例尺Level，最小取值为0
     @param nMaxScaleLevel 最大比例尺Level, 最大取值为30
     */
    void MapMarkerSetScaleLevelRange(void *pWorld, int markerID, int nMinScaleLevel, int nMaxScaleLevel);
    
    /**
     将srcMarkerID优先级抬高到desMarkerID之上
     
     【注意】此接口不再支持，多个线程同时调用时，结果存在不确定性。
     
     @param pWorld 场景句柄
     @param srcMarkerID 需要被修改的overlay ID
     @param desMarkerID 用于比较的overlay ID
     @return 修改后的srcMarkerID 的priority。
     */
    int  MapMarkerBringAbove(void *pWorld, int srcMarkerID, int desMarkerID);
    
    /**
     将srcMarkerID优先级降低到desMarkerID之下
     
     【注意】此接口不再支持，多个线程同时调用时，结果存在不确定性。
     
     @param pWorld 场景句柄
     @param srcMarkerID 需要被修改的overlay ID
     @param desMarkerID 用于比较的overlay ID
     @return 修改后的srcMarkerID 的priority。
     */
    int  MapMarkerBringBelow(void *pWorld, int srcMarkerID, int desMarkerID);
    
    /**
     设置临时优先级为最高，不影响priority
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @param bOnTop true->临时最高优先级；false->取消临时最高优先级
     */
    void MapMarkerSetOnTop(void *pWorld, int markerID, bool bOnTop);                // With priority not changed.
    
    /**
     记录hidden状态，并临时设置所有overlay状态为hidden值
     建议删除该类接口，由上层自己实现
     @param pWorld 场景句柄
     @param hidden hidden值
     */
    void MapMarkerSetAllHidden(void *pWorld, bool hidden);
    
    /**
     恢复hidden状态，配合MapMarkerSetAllHidden使用
     建议删除该类接口，由上层自己实现
     @param pWorld 场景句柄
     */
    void MapMarkerRestoreAllHiddenStatus(void *pWorld);
    
    /**
     设置overlay隐藏状态
     
     @param pWorld 场景句柄
     @param markerIDs overlay ID数组
     @param size 数组大小
     @param hidden 是/否
     */
    void MapMarkerSetHidden(void *pWorld, int *markerIDs, int size, bool hidden);
    
    /**
     获取overlay隐藏状态
     
     @param pWorld 场景句柄
     @param markerID overlay ID
     @return 是否隐藏
     */
    bool MapMarkerIsHidden(void *pWorld, int markerID);
    
    /**
     下落动画
     
     @param pWorld 场景句柄
     @param markerIDs overlay ID数组
     @param size 数组大小
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     */
    void MapMarkerStartDropDownAnimation(void *pWorld, int *markerIDs, int size, bool animated,
                                         MapAnimationDidStopCallback didStopCallback, void *context);


    typedef MAP_ENUM(int, MarkerCullType) {
        MarkerCullType_None = 0,				// 无裁剪逻辑
        MarkerCullType_Annotation = 1,			// 和底图文字一致
        MarkerCullType_RoadClosureMarker = 2,    // 和封路marker一致
        MarkerCullType_Num = 3,
    };
    /**
     Marker被裁剪的类型，默认无裁剪。
     目前只支持Icon, GroupIcon, Annotation

     @param pWorld 场景句柄
     @param markerID overlay ID
     @param cullType 裁剪类型
     */
    void MapMarkerSetCullType(void *pWorld, int markerID, MarkerCullType cullType);
    
    /************************************************************************/
    /*                              GroupIcon相关接口                        */
    /************************************************************************/
    #pragma mark GroupIcon相关接口

    typedef struct AnimationParam{
        char image_name[MapImageNameMaxLength];//图片名称
        void* anim_bitmap;//TMBimap oc2c无法识别,故使用裸指针
        float angle;//逆时针旋转角度，默认为0
        MapVector2i split_number;//多帧图片横向和纵向的单元个数
        MapVector2f anchor;//相对与父图片的百分比
        int anim_duration;//动画在指定的时间内播完，可能跳帧。单位1/1000秒
    }AnimationParam;

    typedef struct _MarkerGroupIconAnchor{
        MapVector2f     anchor;
        char            imageName[MapImageNameMaxLength];
        void*      bgBitmap;//TMBimap oc2c无法识别,故使用裸指针
        MapEdgeInsets   edge; //dp单位
        AnimationParam animations[AnimMaxLength];//最多支持五个
        int            anim_size;
    }MarkerGroupIconAnchor;
    
    /**
     组合气泡可视范围避让类型
     */
    typedef MAP_ENUM(int, VisualRectShowType)
    {
        ShowInVisualRect_None  = 0 ,                // 不需要显示在某个指定可视范围内（如屏幕内）
        ShowInVisualRect_First = 1 ,                // 当组合气泡与其它Overlay相压盖，又与屏幕范围冲突时，优先显示在屏幕内，可以压盖其它Overlay
        ShowInVisualRect_Last  = 2 ,                // 当组合气泡与其它Overlay相压盖，又与屏幕范围冲突时，优先显示在屏幕外，不能压盖其它Overlay
    };

    /**
     组合气泡：有多个可用的候选位置，多个方向，最终选择一个位置，一个方向进行展示。
     */
    typedef struct _MapMarkerGroupIconInfo{
        MapVector3d         points[8];                //气泡的候选坐标列表
        int                 pointsCount;              //气泡可供候选的坐标总数
        
        MarkerGroupIconAnchor anchors[8];             //气泡的候选的锚点列表
        int                 anchorsCount;             //气泡可供候选的锚点总数
        
        VisualRectShowType  visualRectShowType;       //是否需要优先显示在指定可视范围内
        TMRect              visualRect;               //指定的可视的屏幕像素范围(如屏幕内), visualRectShowType = ShowInVisualRect_None时不需要设置
        
        bool                interactive;              //是否可以点击响应
        bool                isAvoidAnnotation;        //底图文字是否对其进行避让
        
        bool                isShowDebugRect;          //是否显示碰撞检测矩形框，调试用
        int                 markerID;                 //Output markerID
        
        bool                isShowExtendRect;          //是否显示点击扩展区域
        float               clickRegionExtend;         //扩展点击区域，单位：dp
        
        bool                isNeedAvoidCallback;       //是否需要在显隐变化时是否通过回调传递避让信息

        bool                isRealTimeUpdate;          //=true，实时更新：每帧都重新计算显示位置, =false，惰性更新：只有当前位置无法显示的时候，才重新计算显示位置，默认false
    }MapMarkerGroupIconInfo;
    
    /**
     组合气泡当前摆放的位置信息：坐标index和方向index
     */
    typedef struct _MapMarkerGroupIconPosInfo{
        int pointIndex;
        int anchorIndex;
    }MapMarkerGroupIconPosInfo;
    
    /**
     创建组合Marker
     @return 成功，返回Group MarkerID，失败，返回0，并打印日志.
     */
    int MapMarkerGroupIconCreate(void* pWorld, MapMarkerGroupIconInfo * info);
    
    bool MapMarkerGroupIconGetInfo(void *pWorld, MapMarkerGroupIconInfo *markerInfo);
    
    /**
     获取组合气泡在进行内部避让计算之后，最终摆放的位置和方向。
     
     @param markerID ，input param
     @param pos    ，output param
     
     @return true，获取成功； false， 获取失败，可能是markerID无效
     */
    bool MapMarkerGroupIconGetPos(void *pWorld, int markerID, MapMarkerGroupIconPosInfo *pos);
    
    void MapMarkerGroupIconModifyInfo(void *pWorld, MapMarkerGroupIconInfo *markerInfo);
    
    /**
     * Marker吸附功能开关
     * @param enabled 是否开启（默认开启）
     */
    void MapMarkerSetAbsorbEnabled(void *pWorld, bool enabled);
    
    /************************************************************************/
    /*                              文字marker相关接口                        */
    /************************************************************************/
    #pragma mark 文字marker相关接口
    
    /**
     创建 Annotation Marker
     @param pWorld 场景句柄
     @param markerInfos marker属性数组
     @param size 数组大小
     */
    void MapMarkerAnnotationCreate(void *pWorld, MapMarkerAnnotationInfo *markerInfos, int size);
    
    void MapMarkerAnnotationModifyInfo(void *pWorld, MapMarkerAnnotationInfo *markerInfo);
    
    bool MapMarkerAnnotationGetInfo(void *pWorld, MapMarkerAnnotationInfo *markerInfo);

    /*
     view_ 转换为MTKView
     MTKView *metalView = (__bridge MTKView *)view_;
     render_command_encoder_ 转换为 id<MTLRenderCommandEncoder>
     id<MTLRenderCommandEncoder> renderEncoder = (__bridge id<MTLRenderCommandEncoder>)render_command_encoder_;
     */
    typedef struct _EngineRenderContent {
        void* view_;
        void* render_command_encoder_;
        bool is_opengl_;
    }EngineRenderContent;

    /*
     创建Custom Marker
     **/
    typedef struct _MapMarkerCustomIconInfo {
        
        // 初始化的时候调用
        void (*pOnInit)(void*, EngineRenderContent*);
        
        // 绘制的时候调用
        bool (*pOnDraw)(void*, EngineRenderContent*);
        
        // 点击的时候调用，客户端返回true或者false，告知引擎是否点中
        bool (*pOnTap)(int, int,int, void*);
        
        // 碰撞检测的结果，参数代表当前选中的位置，如果所有的位置都被碰撞掉，参数为-1
        void (*pOnCollisionResult)(int, void*);
        
        // 3D碰撞检测的结果，参数代表当前选中的位置，如果所有的位置都被碰撞掉，参数为-1
        void (*pOnCollision3DResult)(int, void*);
        
        // 客户端返回true或者false，告知引擎是否需要继续绘制
        bool (*pOnUpdateFrame)(double, void*);
        
        // 返回marker占用的屏幕位置，参数代表几种布局，每个布局有几个子布局框, 返回值是布局 * 子布局框的二维数组
        MapVector4f** (*pGetScreenAreas)(int*, int*, void*);
        int markerId;
        bool clickable;
        void* pContext;
        bool avoidAnnotation; //是否避让底图标注
        bool clickable_forced; //避让情况下是否强制响应点击事件
        bool avoid_3D;    //是否参与3D避让
        int avoid_3D_type; //3D避让类型
        MapVector3d coordinate; //经纬度+高度（单位m）坐标
        char attached_poi_id[32];  //和当前的marker关联的建筑id（POIID）
    } MapMarkerCustomIconInfo;


    /**
    * marker 3d避让结果
    */
    enum Avoid_3D_Reult{
        Avoided_By_None,
        Avoided_By_Build
    };

    /**
    * marker 3D避让结果回调结果结构体
    */
    typedef struct _Collision3DResult{
        int marker_id;       //marker id
        int avoid_result;    //避让结果
    }Collision3DResult;

    /**
    * marker 批量3D避让结果回调
    */
    typedef void (*MapCustomMarkerColliosn3DResultCallback)(Collision3DResult* result, int size);
    /**
     * 设置custom_marker 3D避让回调
     */
    void MapSetCustomMarkerCollision3DResult(void* pWorld, MapCustomMarkerColliosn3DResultCallback callback, void* context);

    /**
     * 创建custom marker
     * @param pWorld 引擎句柄
     * @param info custom marker设置属性
     */
    int MapMarkerCustomIconCreate(void* pWorld, MapMarkerCustomIconInfo * info);

    /**
    Marker 元素避让无处可放时，布局中有被type类型避让的，如果revive_if_avoid为true恢复这个布局为可用（多用于groupmarker）
    @param pWorld 场景句柄
    @param marker_id  overlay的ID
    @param type  marker被避让的元素类型，AvoidType_UI或者AvoidType_Route
    @param revive_if_avoid  true：被type类型避让后继续进行下一阶段显示逻辑  false：被type避让后不显示，内部默认为false
    */
    void MapMarkerReviveWhenAvoided(void *pWorld, int marker_id, int type, bool revive_if_avoid);

    /**
    Marker 是否能被UI避让，仅在allowdoverlay的时候生效，不设置默认为true
    @param pWorld 场景句柄
    @param marker_id  overlay的ID
    @param need_avoid  是否需要避让UI
    */
    void MapMarkerSetAvoidUI(void *pWorld, int marker_id, bool need_avoid);

    /**
    Marker 是否能被Locator避让，仅在allowdoverlay的时候生效，不设置默认为false
    @param pWorld 场景句柄
    @param marker_id  overlay的ID
    @param need_avoid  是否需要避让UI
    */
    void MapMarkerSetAvoidLocator(void *pWorld, int marker_id, bool need_avoid);

    /**
     * 设置Overlay响应随级别变化的scale，具体参数通过接口MapSetAnnoMarkerScaleByLevel设置
     * @param marker_id overlayid
     * @param need_react  是否需要响应设置，默认为false
     */
    void MapMarkerSetReactToScaleByLevel(void *pWorld, int marker_id, bool need_react);
#ifndef NOT_USE_IN_OPENSDK
    /************************************************************************/
    /*                              ImageLabel相关接口                        */
    /************************************************************************/
    #pragma mark ImageLabel相关接口

//    typedef struct _MarkerImageLabelAnchor{
//        //    MapVector2f     anchor;
//        //    char            imageName[MapImageNameMaxLength];
//        //    MapEdgeInsets   edge; //dp单位
//    }MarkerImageLabelAnchor;

    /**
     组合气泡：有多个可用的候选位置，多个方向，最终选择一个位置，一个方向进行展示。
     */
    typedef struct _MapMarkerImageLabelInfo{
        MapIconType iconType;
        
        MapVector3d coordinate;
        char markerImageName[MapImageNameMaxLength];
        char labelImageName[MapImageNameMaxLength];
        char closeImageName[MapImageNameMaxLength];

        MapVector2f markerAnchor;
        MapVector2f labelAnchor;
        MapVector2f closeAnchor;

        //    VisualRectShowType  visualRectShowType;       //是否需要优先显示在指定可视范围内
        //    TMRect              visualRect;               //指定的可视的屏幕像素范围(如屏幕内), visualRectShowType = ShowInVisualRect_None时不需要设置
        //
        bool                interactive;              //是否可以点击响应
        bool                avoidAnnotation;        //底图文字是否对其进行避让
        //
        bool                isShowDebugRect;          //是否显示碰撞检测矩形框，调试用
        int                 markerID;                 //Output markerID
        //
        bool                isShowExtendRect;          //是否显示点击扩展区域
        //    float               clickRegionExtend;         //扩展点击区域，单位：dp
        float       angle;
        float       alpha;
        MapVector2f scale;
        MapVector2f iconAnchorPt;  //  default(0.5,0.5)
        
        //---------text
        unsigned char effect; // (bit:0) for underline; (bit:1) for bold; (bit:2) for outline or halo; (bit:3) for background; (bit:4) for textonicon; other bits is unused now;
        float fontSize;           // 单位 dp
        float haloSize;           // text halo size 单位 dp;
        float textSpace;          // the min space between text and text 单位 dp.
        float iconSpace;          // the min space between icon and text 单位 dp.
        float rowSpace;         //the space between text rows.
        
        TMColor color;
        TMColor backgroundColor;
        
        int nameCharCount; //如需换行，可以设定换行分隔符'\t',目前最多只支持两行
        unsigned short name[MarkerAnnoMaxNameLen]; //最多支持32个字符
        
    }MapMarkerImageLabelInfo;

    
    /**
     * 创建imageLabel类型的marker
     * @param pWorld            场景句柄
     * @param imageLabelInfo    构造info
     * @return  marker的ID
     */
    int MapMarkerImageLabelCreate(void* pWorld, MapMarkerImageLabelInfo *imageLabelInfo);
    /**
     * 修改制定ID的imageLabel类型的marker
     * @param pWorld            场景句柄
     * @param imageLabelInfo    构造info
     */
    void MapMarkerImageLabelModifyInfo(void* pWorld, MapMarkerImageLabelInfo *imageLabelInfo);
    /**
     * 获取imageLabel类型的marker的label的显隐状态
     * @param pWorld            场景句柄
     * @param markerID          marker的ID
     * @return  label的显隐状态
     */
    bool MapMarkerImageLabelGetLabelHidden(void *pWorld, int markerID);
    /**
     * 批量设置指定ID的imageLabel类型的marker的label的显隐状态
     * @param pWorld            场景句柄
     * @param markerIDs         marker的ID数组
     * @param size              marker的ID数组的大小
     * @param hidden            label的显隐状态
     */
    void MapMarkerImageLabelSetLabelHidden(void *pWorld, int *markerIDs, int size, bool hidden);
    /**
     *修改imageLabel类型的marker的图片
     * @param pWorld            场景句柄
     * @param markerID          marker的ID
     * @param markerPicName     marker的图片
     * @param markerPicAnchor   marker图片的anchor
     * @param labelPicName      label的图片
     * @param labelPicAnchor    label图片的anchor
     * @param closePicName      close的图片
     * @param closePicAnchor    close图片的anchor
     */
    void MapMarkerImageLabelModifyImages(void *pWorld, int markerID,
                                         const char* markerPicName, MapVector2f markerPicAnchor,
                                         const char* labelPicName, MapVector2f labelPicAnchor,
                                         const char* closePicName, MapVector2f closePicAnchor);
    /**
     * 设置imageLabel类型的marker的marker与label、marker与text的间距
     * @param pWorld                场景句柄
     * @param markerID              marker的id
     * @param markerLabelSpacing    marker与label的间距
     * @param markerTextSpacing     marker与text的间距
     */
    void MapMarkerImageLabelSetSpacing(void *pWorld, int markerID, float markerLabelSpacing, float markerTextSpacing);
#endif
    /************************************************************************/
    /*                               画圆相关接口                             */
    /************************************************************************/
    #pragma mark 画圆相关接口
    
    typedef struct MapCircleInfo
    {
        MapVector2d center;
        float       radius;      //单位：米
        float       shrink_dist_;//单位：米
        MapColor4ub fillColor;
        MapColor4ub borderColor;
        float       borderWidth; //单位 dp
        bool        bDrawFill;
        bool        bDrawBorder;
 
        bool        bDrawFanShaped; //是否为扇形
 
        char        border_dash_; //0实线，1虚线为方形，2虚线为圆点
        MapVector2f dash_param_; //描边为dash时，表示实部虚部的宽度，描边为圆点时，只用第一个参数作为圆的间隔。单位dp
 
        int         circleID;    //return markerID
        float       startAngle;     //扇形的起始角度，当bDrawFanShaped==true时，起作用，正北为0度，顺时针增加，单位:度
        float       intervalAngle;  //扇形张角,当bDrawFanShaped==true时，起作用，正北为0度，顺时针增加，单位:度
        
        bool        interactive;    //是否可点击交互
    }MapCircleInfo;
    
    /**
     画圆接口
     */
    void MapMarkerCircleCreate(void*pWorld, MapCircleInfo *markerInfos, int size);
    
    /**
     修改圆的的属性
     */
    void MapMarkerCircleModifyInfo(void *pWorld, MapCircleInfo *markerInfos, int size);
    
    /************************************************************************/
    /*                              路口放大图相关接口                             */
    /************************************************************************/
    #pragma mark 路口放大图相关接口
    typedef struct Marker4KInfo
    {
        TMRect rect;            // 放大图显示区域
        void* data;             // 数据可能为空，为空时表示 SDK正在下载数据，下载完后会通过modify更新；
        int len;                // 数据长度
        int visble;             // 创建完直接展示
        int overlayID;          // 放大图overlayid;
        int day_night_mode;          // 0:白天  1:黑夜
        char* filePath;           // 数据缓存路径
        int abDistance;         // AB点距离
        int junctionType;       // 路口类型
        int junctionStyle;      // 路口样式
        MapVector2d* junctionRectPoints; // 路口大图显示区域点串
        int junctionRectPointsCount;    // 点串数量
    }Marker4KInfo;

    /**
    创建Marker类型Overlay
    @param pWorld 场景句柄
    @param  info 显示范围、矢量数据、可见性、ovelayID
    @return 大于0，代表创建成功，返回的是overlayID
                小于0，代表创建失败。-1:数据解析失败，-2：版本不匹配 -3:crc校验失败
    */
    int MapMarker4kCreate(void *pWorld, Marker4KInfo* info);

    bool Parse4kData(void* pWorld, Marker4KInfo* infos);
    
    MapVector2d GLMapGet4kCenterMapPoint(void* pWorld, int overlayID);

    void Map4kGetViewport(long pWorld, int overlayID, int *x,int* y, int* width, int* height); //获取放大图viewport
    /**
     更新放大图
     
     @param pWorld 场景句柄
     @param infos  放大图信息
     @return 大于0，代表创建成功，返回的是overlayID
                 小于0，代表创建失败。-1:数据解析失败，-2：版本不匹配 -3:crc校验失败
     */
    int MapMarker4kModify(void *pWorld, Marker4KInfo* infos);

    /**
     设置放大图车标
     
     @param pWorld 场景句柄
     @param overlayID  overlay ID
     @param lon 经度
     @param lat 纬度
     @param carAngle 角度
     */
    void Set4kOverlayCar(void *pWorld, int overlayID, double lon, double lat, float carAngle);

    /**
     核查该 4K⽮矢量量放⼤大图 可绘制性
     
     @param pWorld 场景句柄
     @param overlayID overlay ID
     @return 是否可以绘制，1可以正常绘制
     备注:创建失败。-1:数据解析失败，-2:版本不匹配 -3:crc校验失，-4:头信息错误 -5:解
     压失败 -6:⽹网络超时 -7:rect范围错误 -8:world或overlay为NULL
     */
    int Check4kMarkerStatus(void *pWorld, int overlayID);

    /**
     放大图渲染状态
     @param overlayID 大图id
     @param status 放大图状态
     备注:创建失败。-1:数据解析失败，-2:版本不匹配 -3:crc校验失，-4:头信息错误 -5:解
     压失败 -6:⽹网络超时 -7:rect范围错误 -8:world或overlay为NULL
     */
    typedef void (*MapJunctionMapStatusCallback)(int overlayID, int status);

    /**
    核查该 4K⽮矢量量放⼤大图 可绘制性
    @param pWorld 场景句柄
    @param overlayID overlay ID
    @param renderStatusCallback  创建结果回调
     */
    void CheckJunctionMapStatus(void *pWorld, int overlayID, MapJunctionMapStatusCallback renderStatusCallback);


    /**
     获取放大图Ifnfo
     @param pWorld 场景句柄
     @param overlayID overlay ID
     @return Marker4KInfo对象
    */
    Marker4KInfo MapMarker4KGetInfo(void *pWorld, int overlayID);
    
    /**
     设置放大图状态
     
     @param pWorld 场景句柄
     @param overlayID overlay ID
     @param status 状态码
     */
    void Map4kMarkerSetStatus(void *pWorld, int overlayID, int status);

    void LonLat4kOverlay(void *pWorld, int overlayID, double* lon, double* lat);

    /**
     * 设置诱导事件信息
     */
    typedef struct GuidanceEventInfo
    {
        int guidanceEvent;      // 诱导事件
        int distance;           // 距离第一路口多少米
    }GuidanceEventInfo;
    
    /**
     * 设置诱导事件信息
     * @param pWorld 场景句柄
     * @param overlayID  overlayID
     * @param pGuidanceInfo 诱导事件信息
     */
    void MapMarker4kGuidanceEvent(void *pWorld, int overlayID, GuidanceEventInfo* pGuidanceInfo);

    /**
     * 放大图图片信息
     */
    typedef struct JuncImageInfo
    {
        void * pBitMap;       // 图片数据
        int dataLen;          // 数据长度
        int width;            // 图片宽度
        int height;           // 图片高度
        int format;           // 像素格式
    }JuncImageInfo;

    /**
     放大图渲染完成回调

     @param bCompleted 放大图是否渲染完成
     @param context context
     */
    typedef void (*MapMarker4kRenderCompletedCallback)(int overlayID, bool bCompleted, void *context, void * pJuncImageInfo);

    /**
     放大图渲染状态回调
     
     @param pWorld 场景句柄
     @param overlayID  overlayID
     @param bExportImage  是否导出image
     @param renderCompletedCallback 渲染完成回调
     @param context 回调对象，默认为空
     */
    void MapMarker4kRenderCompleted(void *pWorld, int overlayID, bool bExportImage, MapMarker4kRenderCompletedCallback renderCompletedCallback, void *context);
    
    /**
     设置路口大图样式
     @param pWorld 场景句柄
     @param day_style_file 白天样式文件路径
     @param night_style_file 夜间样式文件路径
    */
    void SetJunctionStyle(void *pWorld,const char* day_style_file,const char* night_style_file);
/*=========================================================================*/
/*                              感知底图相关接口                              */
/*=========================================================================*/
#pragma mark  感知底图相关接口
#ifndef NOT_USE_4K_ELEMENT
/**
 * 地图场景类型。不同场景类型，地图显示内容不同。
 */
enum MapSceneType{
    //普通地图场景：显示地图全要素，包括（感知障碍物、感知车道线、基础底图上的所有内容）
    MapSceneType_Normal = 0,
    
    //感知地图--仪表盘场景：只显示感知障碍物、感知车道线。
    MapSceneType_Dashboard,
};

/**
 * 创建3D模型
 */
int MapModel3DCreate(void * pWorld, void * options);

/**
 * 初始化内置3D模型
 */
int MapInitModel3D(void * pWorld, void * materialOptions, int count);

/**
 *  创建引导面marker
 */
int MapGuideAreaCreate(void * pWorld, void * areaoption);

int MapGuideAreaModifyTrafficInfo(void * pWorld, int overlayID, void * trafficoption);

int MapGuideArrowCreate(void* pWorld, void* arrowOption);

int MapGuideLineCreate(void* pWorld, void* guideLineOption);

int MapGuideLineModifyInfo(void * pWorld, int overlayID, void * guideLineOption);
#endif
/**
   * 设置2dmarker是否可以被3d物体压盖， 默认为false。
   * @param markerId 2d元素对应的overlayID
   * @param is_coverby_3d  true 2d被3d模型压盖，false 2d元素压盖3d模型。
   */
void MapMarkerSetOver3D(void * pWorld, int markerId, bool is_coverby_3d);

/**
 * 设置自定义marker是否可以被locator压盖， 默认为false。
 * @param markerId 自定义marker对应的overlayID
 * @param is_above_locator_render  true 自定义marker压盖locator， 默认值false
 *
 */
void MapCustomMarkerSetAboveLocator(void * pWorld, int markerId, bool is_above_locator_render);

/**
 * 设置Point类型Overlay按照屏幕距离降序绘制,即远的先绘制近的后绘制,近的压盖远的;
 * 说明: 1> 该接口开启后所有Point类型普通Overlay一起处理;
 *      2> 该接口一般用于导航场景,进入导航场景开启退出导航场景关闭;
 * @param enable true开启, false:关闭;
 */
void MapMarkerSetRenderOrderByDepth(void * pWorld, const bool enable);

#ifdef __cplusplus
}
#endif

#endif /* GLMapLib_Marker_h */
