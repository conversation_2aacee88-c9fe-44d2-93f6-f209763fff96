//
//  locator.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/3.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_LOCATOR_INFO_H__
#define __MAPAPI_BASE_LOCATOR_INFO_H__

#include <stdio.h>
#include <string>
#include <memory>
#include <vector>
#include "base/map_define.h"
#include "animation/animation_enable_object.h"
#include "overlay/model_3d_options.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT LocationInfo : public OverlayOptions{
public:
    LocationInfo();
    LocationInfo(const LocationInfo & other);
    LocationInfo& operator=(const LocationInfo & other);
    ~LocationInfo();
    
    // 设置位置
    LocationInfo & SetPoint(const MapVector3d point);
    const MapVector3d & GetPoint() const;
    
    // 设置定位精度
    LocationInfo & SetAccuracy(float accuracy);
    float GetAccuracy() const;
    
    // 设置方位角
    LocationInfo & SetRoll(float roll);
    float GetRoll() const;
    
    // 设置俯视角
    LocationInfo & SetPitch(float pitch);
    float GetPitch() const;
    
    // 设置侧倾角
    LocationInfo & SetYaw(float yaw);
    float GetYaw() const;

    // 设置lane id
    LocationInfo& SetLaneID(const MapLaneID& id);
    const MapLaneID& GetLaneID() const;

    //设置修改坐标/角度 是否带动画
    LocationInfo& SetUseAnimation(bool animation);
    bool GetUseAnimation() const;

    virtual OverlayType GetType() const override { return OverlayType::Locator; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
private:
    struct Impl;
    std::shared_ptr<LocationInfo::Impl> GetImpl();
    std::shared_ptr<LocationInfo::Impl> GetImpl() const;
};

}

#endif /* locator_info_h */
