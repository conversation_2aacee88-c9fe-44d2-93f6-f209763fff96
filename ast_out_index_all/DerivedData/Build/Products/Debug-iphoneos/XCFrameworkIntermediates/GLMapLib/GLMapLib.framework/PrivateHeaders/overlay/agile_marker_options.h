//
//  agile_marker_options.h
//  GLMapLib2.0
//
//  Created by ji<PERSON><PERSON><PERSON> on 2025/02/04.
//  Copyright © 2025 TencentMap. All rights reserved.
//

#pragma once

#include <vector>
#include "overlay/overlay.h"
#include "agilemap/agilemap_controller.h"
namespace MAPAPI {
 
class TXMAPSDK_EXPORT AgileMarkerOption : public OverlayOptions
{
public:
    AgileMarkerOption();
    AgileMarkerOption(const AgileMarkerOption & other);
    virtual ~AgileMarkerOption() override;
    
    virtual OverlayType GetType() const override;

    AgileMarkerOption & SetCoordinate(const MapVector3d & coordinate);
    const MapVector3d & GetCoordinate() const;
    
    AgileMarkerOption &  SetCanvasLayoutParam(const CanvasLayoutParam & param);
    const CanvasLayoutParam & GetCanvasLayoutParam() const;

private:
    struct Impl;
    std::shared_ptr<AgileMarkerOption::Impl> GetImpl();
    std::shared_ptr<AgileMarkerOption::Impl> GetImpl() const;
};

}
 
