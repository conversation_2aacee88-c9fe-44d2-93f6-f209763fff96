//
//  GLMapBusiness.h
//  GLMapLib
//
//  Created by morgansun on 2022/4/29.
//  Copyright © 2022 TencentMap. All rights reserved.
//

#ifndef GLMapBusiness_h
#define GLMapBusiness_h

#include "base/map_define.h"
#include <memory>

namespace tencentmap {
namespace business {

class TXMAPSDK_EXPORT MapBusinessApi {
public:
    virtual ~MapBusinessApi() = default;

    //////////// 路况图层开关 //////////////
    /**
     路况图层开关
     */
    virtual void EnableTraffic(bool enable) = 0;
    /**
     暂停路况刷新
     */
    virtual void PauseTrafficUpdate() = 0;
    /**
     恢复路况刷新
     */
    virtual void ResumeTrafficUpdate() = 0;


    //////////// 手绘图开关 //////////////
    /**
     * 启用或关闭手绘图
     * @param enable true-启用 false-关闭
     */
    virtual void EnableHandDraw(bool enable) = 0;
    
    /**
     * 查询当前视野的屏幕内是否有手绘图
     * @return true 存在，false不存在
     */
    virtual bool HasHanddrawInScreen() = 0;
        
};

/**
 创建底图业务图层管理器
 */
TXMAPSDK_EXPORT std::shared_ptr<MapBusinessApi> Create(void *world);


}
}

#endif /* GLMapBusiness_h */
