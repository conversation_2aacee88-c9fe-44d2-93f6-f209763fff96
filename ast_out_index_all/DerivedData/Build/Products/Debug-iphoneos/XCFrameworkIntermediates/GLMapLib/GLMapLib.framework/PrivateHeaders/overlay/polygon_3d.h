//
// Created by THY on 2025/4/15.
// Copyright © 2025 TencentMap. All rights reserved.
//

#ifndef _MAPAPI_POLYGON_3D_H_
#define _MAPAPI_POLYGON_3D_H_

#include "polygon.h"
#include "polygon_3d_options.h"

namespace tencentmap{
class Polygon3DImpl;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT Polygon3D : public Polygon{
    friend class OverlayFactory;
public:
    void SetPoints(const std::vector<MapVector3d> & points);
    void SetPoints(const std::vector<MapVector2d> & points) override;
    const std::vector<MapVector3d>& Get3DPoints() const;

    void SetFillColor(const MapColor4ub & fillColor) override;
    const MapColor4ub & GetFillColor() const override;

  /**
   * 设置Polygon3D的透明度循环动画，在初始和目标alpha值之间渐变
   * @param fromAlpha 初始alpha值
   * @param targetAlpha 目标alpha值
   * @param cycle 一次渐变的周期 单位：秒
   * @param times 动画执行次数
   */
  void SetLoopingAlpha(float fromAlpha, float targetAlpha, float cycle, uint32_t times);

  /**
   * 设置Polygon的透明度循环动画结束
   * 结束后直接回到初始alpha值
   */
  void SetLoopingAlphaFinish();

private:
    using Polygon::Polygon;
    std::shared_ptr<tencentmap::Polygon3DImpl> GetImpl();

    std::shared_ptr<Polygon3DOptions> GetOptions() const{
        return std::dynamic_pointer_cast<Polygon3DOptions>(options_);
    }
};

}
#endif  // _MAPAPI_POLYGON_3D_H_
