//
//  compass.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/6.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_COMAPASS_H__
#define __MAPAPI_BASE_COMAPASS_H__

#include <stdio.h>
#include <string>
#include "base/map_define.h"

namespace tencentmap{
class MarkerIcon;
};


namespace MAPAPI {

class TXMAPSDK_EXPORT Compass{
public:
    /**
     * 设置指南针的坐标(屏幕坐标)信息
     */
    void SetPosition(MapVector2d coord);
    
    /**
     * 获取指南针的坐标(屏幕坐标)信息
     */
    MapVector2d GetPosition();

    /**
     * 设置是否显示指南针
     */
    void SetHidden(bool bHidden);

    /**
     * 获取指南针显示状态
     */
    bool GetHidden();
    
    /**
     * 修改指南针的资源文件
     */
    void SetImage(const std::string & imageName);
    
private:
    friend class MapImpl;
    
    Compass();
    
    std::shared_ptr<tencentmap::MarkerIcon> GetCompass();
    
    class Impl;
    std::unique_ptr<Impl> impl_;
};

}

#endif /* __MAPAPI_BASE_COMAPASS_H__ */
