//
//  animation_interface.h
//  GLMapLib2.0
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_ANIMATION_ENABLE_OBJECT_INCLUDE__
#define __MAPAPI_ANIMATION_ENABLE_OBJECT_INCLUDE__

#include <memory>
#include "base/map_define.h"

namespace MAPAPI {

enum class TXMAPSDK_EXPORT AnimationOwnerEnum{
    No_Anim = -1,
    BaseMap_Anim   = 0,
    Marker_Anim    = 1,
    Locator_Anim   = 2,
};

class Animation;
class TXMAPSDK_EXPORT AnimationEnableObject : public std::enable_shared_from_this<AnimationEnableObject>{
public:
    /**
     * 获取动画的对象类型
     *
     * @return 获取动画的对象类型枚举
     */
    virtual AnimationOwnerEnum GetAnimationOwnerType() const { return AnimationOwnerEnum::No_Anim;}
    
    /**
     * 获取动画的对象ID
     *
     * @return 获取动画的对象ID，底图为0，Overlay是自己的ID
     */
    virtual int GetID() const { return 0;}
    
    virtual ~AnimationEnableObject() = default;
protected:
    friend class AnimationControllerImpl;
    virtual void OnAnimationFinish(std::shared_ptr<Animation> anim, const std::string& anim_value) {}
    virtual void OnAnimationStart(std::shared_ptr<Animation> anim, const std::string& anim_value){}
};

}

#endif /* __MAPAPI_ANIMATION_ENABLE_OBJECT_INCLUDE__ */
