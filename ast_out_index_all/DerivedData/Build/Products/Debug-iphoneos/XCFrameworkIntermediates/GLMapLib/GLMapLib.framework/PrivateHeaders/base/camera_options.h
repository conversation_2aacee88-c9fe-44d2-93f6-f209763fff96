//
//  camera_options.hpp
//  GLMapLib
//
//  Created by lihuafeng on 2024/1/12.
//

#pragma once

#include <stdio.h>
#include <memory>
#include <assert.h>
#include "MapDataType.h"

namespace MAPAPI{

/**
 * 地图相机的姿态。
 *
 * 如果只想修改地图的中心，只调用WithCenter(目标点)，其它属性不用设置。
 * 如果想同时修改地图的中心点，比例尺，则调用WithCenter(目标点).WithScaleLevel(目标比例尺)。
 * 以此类推。。。
 *
 * 不设置的属性，地图内部不会修改。
 */
struct TXMAPSDK_EXPORT CameraOptions{
    CameraOptions();
    CameraOptions(const CameraOptions &);
    virtual ~CameraOptions();
    
    //地图中心，注意y>0，单位：底图墨卡托坐标
    CameraOptions & WithCenter(const MapVector3d &);
    
    //地图中心，注意y>0，单位：底图墨卡托坐标
    CameraOptions & WithCenter(const MapVector2d & );
    
    //地图缩放比例
    CameraOptions & WithScale(double _scale);
    
    //传入scale_level。注意与上面的WithScale()不同的。
    CameraOptions & WithScaleLevel(float);
    
    //地图在屏幕上移动距离，单位：像素
    CameraOptions & WithMoveBy(const MapVector2f& );
    
    //地图缩放中心
    CameraOptions & WithScalePivot(const MapVector2f&);
    
    //俯仰角，单位：度
    CameraOptions & WithSkew(float);
    
    //是否开启自动俯仰角策略。未来此功能会下掉。
    CameraOptions & WithAutoSkew(bool);
    
    CameraOptions & WithToleranceSkew(float);
    
    //旋转角，单位：度
    CameraOptions & WithRotate(float);
    
    //屏幕中心偏移
    CameraOptions & WithScreenCenterOffset(const MapVector2f &);
  
    //Fov，单位：度
    CameraOptions & WithFov(float);
    
    std::string ToString() const;
    
    struct Impl;
    std::unique_ptr<Impl> impl;
};

/**
 * 动画相关属性设置
 */
struct TXMAPSDK_EXPORT AnimationOptions{
    AnimationOptions();
    AnimationOptions(const AnimationOptions &);
    virtual ~AnimationOptions();
    
    //是否开启动画，默认false
    AnimationOptions & WithAnimated(bool _animated);
    
    //动画时长，单位：秒，默认0.4秒
    AnimationOptions & WithDuration(double _duration);
    
    //动画是否延迟执行，延迟时间，单位：秒，默认0秒
    AnimationOptions & WithDelay(double _delay);
    
    //动画插值曲线类型，默认MapAnimationCurveType_Linear
    AnimationOptions & WithCurveType(MapAnimationCurveType _curve);
    
    //是否打断当前动画？？？默认true
    AnimationOptions & WithBeginFromCurrentState(bool _begin_from_current_state);
    
    //动画完成回调，默认NULL
    AnimationOptions & WithCallback(MapAnimationDidStopCallback _callback);
    
    //回调相关context，默认NULL
    AnimationOptions & WithContext(void* _context);
    
    std::string ToString() const;
    
    struct Impl;
    std::unique_ptr<Impl> impl;
};


/**
 * 相机移动方式
 */
enum TXMAPSDK_EXPORT CameraMoveWay {
    kMoveTo = 0, //直线方式移动到目标点
    kFlyTo  = 1  //相机曲线飞行的方式移到目标点。目前还不支持。
};

}
