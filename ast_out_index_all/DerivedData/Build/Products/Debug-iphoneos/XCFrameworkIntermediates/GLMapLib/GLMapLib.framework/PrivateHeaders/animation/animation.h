//
//  map_animation.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/27.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_ANIMATION_H__
#define __MAPAPI_ANIMATION_H__

#include <stdio.h>
#include <string>
#include "base/map_define.h"
#include "animation/animation_value.h"
#include <memory>

namespace MAPAPI {

class AnimationEnableObject;
class AnimationObserver;
class AnimationController;
class AnimationControllerImpl;

enum class TXMAPSDK_EXPORT AnimationType
{
    /**
     *底图中心动画，暂未实现
     */
    AnimationType_Map_Center = 0,
    /**
     *底图缩放动画，暂未实现
     */
    AnimationType_Map_Scale,
    /**
     *底图俯仰动画，暂未实现
     */
    AnimationType_Map_Skew,
    /**
     *底图旋转动画，暂未实现
     */
    AnimationType_Map_Rotate,
    /**
     *底图样式动画，暂未实现
     */
    AnimationType_Map_Style,
    /**
     *底图概览动画，暂未实现
     */
    AnimationType_Map_Overlook,
    /**
     *底图滑动动画，暂未实现
     */
    AnimationType_Map_MoveBy,
    
    /**
     *Marker透明度动画
     */
    AnimationType_Marker_Alpha = 100,
    /**
     *Marker位置动画，暂未实现
     */
    AnimationType_Marker_Position,
    /**
     *Marker缩放
     */
    AnimationType_Marker_Scale,
    /**
     *Marker旋转
     */
    AnimationType_Marker_Rotate,
    /**
     *暂未实现
     */
    AnimationType_Marker_ScreenOffset
};

class TXMAPSDK_EXPORT Animation{
    friend class Map;
public:
    
    virtual ~Animation();
    
    /**
     * 设置动画时间
     *
     * @param duration 时间间隔（单位：秒）
     */
    void SetAnimationDuration(double duration);
    
    /**
     * 设置动画延迟执行的时间
     *
     * @param delay 时间间隔（单位：秒）
     */
    void SetAnimationDelay(double delay);
    
    /**
     * 设置动画curve类型
     *
     * @param curve curve类型
     */
    void SetAnimationCurve(MapAnimationCurveType curve);
    
    /**
     * 设置动画是否从当前状态接着执行
     *
     * @param fromCurrentState 是否从当前状态接着执行
     */
    void SetAnimationBeginFromCurrentState(bool fromCurrentState);
    
    /**
     * 设置动画回调
     *
     * @param observer 回调观察者
     */
    void SetAnimationObserver(std::shared_ptr<AnimationObserver> observer);
    
    /**
     * 设置动画的目标值
     *
     * @param animationValue 动画的目标值
     */
    void SetAnimationTargetValue(AnimationValue &animationValue);
    
    /**
     * 获取动画的目标对象
     *
     * @return 返回执行动画的对象
     */
    std::shared_ptr<AnimationEnableObject> GetAnimationObject() const;
    
    /**
     * 获取动画的属性
     *
     * @return 返回动画类型
     */
    AnimationType GetAnimationType() const;
    
protected:
    Animation(std::shared_ptr<AnimationEnableObject> animObj, AnimationType animType);
    
    friend class AnimationControllerImpl;
    class Impl;
    std::unique_ptr<Impl> _impl;
};

}

#endif /* __MAPAPI_ANIMATION_H__ */
