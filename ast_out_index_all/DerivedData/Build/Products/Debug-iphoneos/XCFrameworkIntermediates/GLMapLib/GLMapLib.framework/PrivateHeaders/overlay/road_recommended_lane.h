//
// Created by neilxiang on 2024/09/25.
//

#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "../base/map_define.h"
#include "overlay.h"
#include "road_recommended_lane_options.h"
#include "had_structure.h"

namespace tencentmap{
class RoadRecommendedLaneOverlay;
}


namespace MAPAPI {

class TXMAPSDK_EXPORT RoadRecommendLane : public Overlay {
    friend class OverlayFactory;
public:
    /**
     设置推荐车道数据
     @param data 数据
     */
    void SetRecommendedLaneData(RecommendedLaneData& data);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::RoadRecommendedLaneOverlay> GetImpl();
    void LoadLog(const RecommendedLaneData& data);
};

}
#endif
