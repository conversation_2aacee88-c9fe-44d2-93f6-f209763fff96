#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/guide_line_options.h"

namespace tencentmap{
class Macro4KGuideLine;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT GuideLine : public Overlay{
    friend class OverlayFactory;
public:
    using Overlay::SetHidden; // 引入基类函数，规避编译报错

    void SetOptions(const GuideLineOptions & options);
    /**
     * 设置数据更新时间间隔，即调用SetOptionsg接口的频率。单位秒
     */
    void SetUpdateDuration(double duration);

    // 设置动画的最大形变方差，形变过大的不做动画
    void SetMaxStdd(double maxstdd);
    void SetMaxMean(double maxmean);
    
    // 设置最大动画长度，远处不做动画
    void SetMaxAnimationLength(double max_animation_length);

    /**
     * 设置显隐状态，支持显隐动画
     * @param duration 显隐动画时间，单位为s，0表示不执行动画
     */
    void SetHidden(bool hidden, float duration);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KGuideLine> GetImpl();
};

}

#endif