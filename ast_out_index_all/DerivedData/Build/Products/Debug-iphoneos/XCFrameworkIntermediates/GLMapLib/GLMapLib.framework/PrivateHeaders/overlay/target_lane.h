#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/target_lane_options.h"

namespace tencentmap{
class Macro4KTargetLane;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT TargetLane : public Overlay{
    friend class OverlayFactory;
public:
    void SetOptions(const TargetLaneOptions & options);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KTargetLane> GetImpl();
};

}
#endif
