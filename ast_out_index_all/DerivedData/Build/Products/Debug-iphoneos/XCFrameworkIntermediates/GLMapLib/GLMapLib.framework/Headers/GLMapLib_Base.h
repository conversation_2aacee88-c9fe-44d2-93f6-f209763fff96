/*
 *  GLMapLib2.0.h
 *  GLMapLib
 *
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12-2-13.
 *  Copyright 2012 Tencent. All rights reserved.
 *
 */

#ifndef GLMapLib_Base_h
#define GLMapLib_Base_h

#include "MapDefine.h"
#include "MapDataType.h"
#include "TMBitmapContext.h"

//#define UPLOAD_LEAK_TRACE_LOG
#ifdef PERFORMANCE_TEST
#include "map_data_engine_marco.h"
#endif
#define MAP_INTERACTOR              // 游戏项目关闭此项，交互交给游戏层

//#define MAP_LANDMARK                // 4K地标
// #define MAP_STREETVIEW           // 街景数据, 开平分支暂时关闭该功能
// #define MAP_BLOCKROUTE           // 封路数据, 开平分支暂时关闭该功能
// #define MAP_ROUTEDESCBUBBLE      // 伴随气泡, 开平分支暂时关闭该功能
// #define MAP_DYNAMICROUTENAME     // 动态路名
// #define Map_SDFCACHE             // SDF字模型Cache，持久化有字模错乱 无法自修复的权限，开平暂时关闭
// #define MAP_CITY_3D              // 用于开启渲染3D城市的功能
// #define MAP_BUILDING_PICKING     // 用于开启3D楼块拾取的功能
// #define MAP_PLUGIN_TRAFFIC
// #define MAP_PLUGIN_INDOORMAP
// #define MAP_PLUGIN_BLOCKROUTE

#ifndef MAP_QT
#ifndef MAP_ANDROID
#ifndef MAP_LINUX
#ifndef MAP_OHOS
#ifndef MAP_IOS
#define MAP_IOS
#endif
#endif
#endif
#endif
#endif

#ifdef MAP_IOS
  #if (__cplusplus && __cplusplus >= 201103L && (__has_extension(cxx_strong_enums) || \
    __has_feature(objc_fixed_enum))) || (!__cplusplus && __has_feature(objc_fixed_enum))
    #define MAP_ENUM(_type, _name) enum _name : _type _name; enum _name : _type
  #else
    #define MAP_ENUM(_type, _name) _type _name##_type; enum _name
  #endif
#else
  #define MAP_ENUM(_type, _name) _type _name; enum
#endif

#define OverlaySouceTypeMaxNum          (6)
#define MapRouteMaxNum                  (4)
#define AnimMaxLength                   (5)
#define MapImageNameMaxLength           (512)
#define MapPathMaxLength                (256)
#define MaxCustomPointExtInfoLen 1024
#define kMaxImageNameLength MapImageNameMaxLength
#define TIMAGEID_COLOR_ARROW_TEXTURE            "color_arrow_texture.png"
#define TIMAGEID_ROUTE_ARROW_TEXTURE_CUSTOM     "route_arrow_texture.png"
#define TIMAGEID_COMPASS                        "compass.png"
#define SKY_DAY                                 "skyboxDay.png"
#define SKY_NIGHT                               "skybox_night.png"

#define COMPOSITE_ROUTE

#define MAP_ENUM_WITH_NAMES(enumName, ...) \
    typedef enum _##enumName { __VA_ARGS__ }enumName; \
    static char enumName##_Names[] = #__VA_ARGS__;

#ifdef __cplusplus
extern "C" {
#endif


typedef enum _MapTappedType
{
    kGLMapTappedNone,
    kGLMapTappedTextAnnotation,
    kGLMapTappedClosureAnnotation,
    kGLMapTappedCompass,
    kGLMapTappedMarkerItem,
    kGLMapTappedLineOverlayItem,
    kGLMapTappedLocator,
    kGLMapTappedBlockRouteAnnotation,
    kGLMapTappedIndoorBuilding,
    kGLMapTapped4KModel,
	kGLMapTappedCluster,
    kGLMapTappedImageLabel,
    kGLMapTappedCircle,
    kGLMapTappedPolygon,
    kTappedElementTypeMax
} MapTappedType;

typedef enum _MapTapGestureType{
    kGestureNone,
    kGestureSingleTap,
    kGestureDoubleTap,
    kGestureTripleTap,
    kGestureTapNum
}MapTapGestureType;

/**
 * 相机变化原因
 */
typedef enum _cameraChangeReasonType {
  REASON_API_ANIMATION = 0, ///< 程序API调用
  REASON_GESTURE,           ///< 用户手势操作
} CameraChangeReason;

/**
 地图点击后返回的信息
 */
typedef enum _MapTappedTextAnnotationType
{
    kMapTappedTextAnnotationBaseMap,      // 基础底图标注
    kMapTappedTextAnnotationIndoorMap,    // 室内图标注
    kMapTappedTextAnnotation3DAoi,        // 3daoi标注
    kMapTappedTextAnnotationMax
} MapTappedTextAnnotationType;


/**
 聚类点击后返回的信息，点选返回后拷贝使用，指针由引擎内部管理不需要释放
 */
typedef struct _ClusterTappedInfo{
    int overlay_id;  //所点选的overlay的ID
    MapVector2d overlay_coord; //overlay的扎点位置

    int item_num; //item_ids中元素的个数
	int *item_ids;	//聚类点包含的itemid(由端上生成)

    int scatter_coord_num; //scatter_coord_buf的个数，如果不会则扩散为0
	MapVector2d *scatter_coord; //扩散后各个元素的坐标，如果不会则扩散为空

	int target_level; //点击聚类点，需要放大到的级别，不需要放大时是当前级别
	int group_id; //聚类组id
}ClusterTappedInfo;

/**
 * 室内停车位需求:
 * 室内相关元素(停车位区域)点击后返回的信息，点选返回后拷贝使用，指针由引擎内部管理不需要释放
 */
typedef struct _IndoorTappedInfo{
  double height;        //高度
  int points_num; 		//points的个数, 没有点则为0
  MapVector2d *points;  //元素(目前是停车位)的坐标数组。
  int nameLen; //name的实际长度。注意：不能超过32
  unsigned short name[32]; //poi点的名称，utf16编码。目前是停车位关联的poi的名称。
}IndoorTappedInfo;

/**
 地图点击后返回的信息
 */
typedef struct _MapTappedInfo
{
    MapTappedType type; //点选类型，poi、overlay点、线、自车标、室内图等
    int x; //点的墨卡托坐标
    int y;
    int altitude; //点的高度
    int itemType; //区分室内图、基础底图点、3DAoi，见MapTappedTextAnnotationType
    int drawPriority; //点的渲染顺序，见MapDrawCallBackType
    int nameLen; //name的实际长度
    unsigned short name[256]; //poi点的名称，utf16编码
    void *itemId;   //室内图点选信息
    int idArrayLen;     //overlay idArray的真实长度
    int idArray[5];     //点击选中的id数组，目前只有叠加的蚯蚓线返回多个id
    int extInfoLen;     //extInfo的长度
    char *extInfo;      //返回的扩展信息，目前用于百变地图和积水地图，外部不需要释放指针
    int classCode;      // poi的classCode，用于判断poi点分类
    bool ignoreCallback; //是否触发MapCallBackType_MapOnTapInfo回调
    char poiType;       //只对底图poi点有效，poi类型
    unsigned long long poiId; //poi的ID
	int layerId; //开放图层的poi点 >0,普通底图=0
	ClusterTappedInfo cluster_tap_info;	//聚类点选信息
    IndoorTappedInfo indoor_tap_info;   //室内图停车位点选信息
//    bool isSingleTap;  //是否是单击选中
    int tap_type;  //0:未点击 1：点击 2：双击 3：三击
    char agilemap_id[256];  //新百变地图的layerid
} MapTappedInfo;

typedef enum _Object3DSubType{
    Object3DSubType_None,
    UniversalModelType_Tree,                //树
    UniversalModelType_ChangingStation,     //换电站
    UniversalModelType_SkyWheel,            //摩天轮
    UniversalModelType_BigScreen,           //大屏幕
    UniversalModelType_BillBoard,           //广告牌
    UniversalModelType_ScareCrow,           //稻草人
    UniversalModelType_HayStack,            //草垛
    UniversalModelType_UnderStore,          //底商
    UniversalModelType_SubwayGate,          //地铁出入口
    UniversalModelType_GasStation,          //加油站
    UniversalModelType_BuildingDoor,        //门
    UniversalMapModelType_ChargingPile,     //充电桩
    UniversalMapModelType_RepairStation,    //维修站
    Object3DSubType_Num,
}Object3DSubType;

typedef enum _Object3DType{
    OBJ3D_NONE = 0,							// None
    OBJ3D_BUILDING = 1,					// L1
    OBJ3D_LANDMARK = 2,					// L4
    OBJ3D_MODEL_OVERLAY = 3,		// 3DOverlay
    OBJ3D_UNIVERSAL_MODEL = 4,	// 通模，在具体的通模中细分类型
    OBJ3D_COMPONENT_MODEL = 5,          // 桥梁
	// 对内使用
    OBJ3D_ROAD_AREA_OVERLAY,
    OBJ3D_ROAD_COMPANION_AREA_OVERLAY,
}Object3DType;

typedef struct _UniversalModelTapInfo{
    int type;           //通模类型（具体类型参考UniversalModelType）
    int admin_code;     //行政区域编码
    int style_id;       //通模样式id
    MapVector3d geo;    //通模坐标（经纬度+高度）
    char ext_info[256];
}UniversalModelTapInfo;

typedef struct _Object3DTapInfo{
    int type; //0= null, 1=L1,2=L4,3=modelOverlay,4=通模,见Object3DType
//#ifdef MAP_STYLE_PLATFORM
//    int style_id = -1;
//#endif
    bool isVisitable;
    MapVector2d tap_screen_coord;
    char object3DID[32]; //3d元素的ID
    UniversalModelTapInfo universal_model_info;
}Object3DTapInfo;

typedef struct _BuildingLightAttribute{
    TMColor spot_color;                  //光柱颜色
    TMColor spot_edge_outline_color;     //光柱描边颜色
    float   spot_height;                 //光柱高度
    int     spot_angle;                  //光柱角度
    float   spot_edge_outline_width;     //光柱描边宽度
    TMColor blur_color;                 //建筑蒙层颜色列表
    TMColor roof_highlight_color;
    TMColor wall_highlight_color;
    int     mix_mode;
}BuildingLightAttribute;

typedef struct _BuildingLightIdAttributeIndex{
    char poi_id[32];                      //绑定poi id
    int  attribute_index;                 //光柱对应的样式索引
}BuildingLightIdAttributeIndex;

typedef struct _BuildingLightInfo{
    BuildingLightIdAttributeIndex *id_list;          //光柱显示poi id及其对应样式索引的列表
    int id_size;
    BuildingLightAttribute *attributes;       //光柱样式列表
    int attribute_size;
}BuildingLightInfo;

typedef enum _BuildingUnitType{
    BuildingUnit_NONE = 0,
    BuildingUnit_PARAPET_ROOFSTRUCT = 1,        // 建筑物女儿墙
    BuildingUnit_ROOFLINE = 2,                  // 建筑物屋顶边线
    BuildingUnit_BILLBOARD = 3,                 // 通用建筑物广告牌
    BuildingUnit_WATERWAVE = 4,                 // 打卡水波纹
    BuildingUnit_VIP_BILLBOARD = 5,             // 客户招商广告牌
    BuildingUnit_BillBoard_SliderShow = 6,      // 轮播大屏幕
    BuildingUnit_NUM = 7                        // 建筑物构件个数
}BuildingUnitType;

typedef enum _BillBoardImageCallbackType{
    AD_VIP = 0,
    Screen_SliderShow = 1,
}BillBoardImageCallbackType;

typedef enum _MapBuildingAnimationType
{
    MapBuildingAnimation_Grow = 0,              // 建筑物生长动画
    MapBuildingAnimation_Reduce = 1,            // 建筑物降干扰动画
    MapBuildingAnimation_Fade = 2,              // 建筑物虚实动画
    MapBuildingAnimation_GrowUpDown = 3,        // 建筑物生长收缩动画
}MapBuildingAnimationType;

typedef enum Macro4KContentType
{
    Macro4KPedestrianOverpass = 0,              // 天桥
    Macro4KHighwayGantry = 1,                   // 龙门架
    Macro4KBridgeComponent = 2,                 // 组件化斜拉桥/悬索桥梁
    Macro4KRefinedBridgeModel = 3,              // 精细化桥梁
    Macro4KGreenbeltPlants = 4,                 // 绿化带绿植
    Macro4KGirderBridgeComponent = 5,           // 组件化拱式/桁架桥
    Macro4KMedianBarrier = 6,                   // 水泥墩
    Macro4KPedestrianGuardrail = 7,             // 组件化隔离带护栏
    MacroGableRoofComponent = 8,                // 组件化硬山顶
    Macro4KContent_NUM,
} Macro4KContentType;

typedef enum _MapFrustumScopeType
{
    MapFrustumScope_Low  = 0,                  // 建筑物近处显示
    MapFrustumScope_High = 1,                  // 建筑物远处显示
}MapFrustumScopeType;

#define Map_Failed                        (false)
#define Map_Success                       (true)
    
    
#define GLMAP_ANNOTATION_POINT_SUBTYPE_LEFT		(0)
#define GLMAP_ANNOTATION_POINT_SUBTYPE_TOP		(1)
#define GLMAP_ANNOTATION_POINT_SUBTYPE_RIGHT	(2)
#define GLMAP_ANNOTATION_POINT_SUBTYPE_BOTTOM	(3)

typedef enum MapLocatorModel3DType
{
   Model3D_3DS = 0,
   Model3D_Skeleton,
   Model3D_Obj, // 已废弃
   Model3D_StdObj,
} MapLocatorModel3DType;
    
typedef struct _GLMapAnnotationIcon
{
    char icon_file[64];
    
    int x;
    int y;
    int width;
    int height;

} GLMapAnnotationIcon;

/**
 文字标注，poi搜索时用到
 */
typedef struct _GLMapAnnotationText
{
    volatile int retainCount;           // reference count for smart pointer;
    
    unsigned char subtype;              // text's relative position to icon.
    unsigned char effect;               // (bit:0) for underline; (bit:1) for bold; (bit:2) for outline or halo; (bit:3) for background; (bit:4) for textonicon; other bits is unused now;
    unsigned char textHidden;
    unsigned char iconHidden;

    float fontSize;
    float haloSize;           // text halo size;
    float textSpace;          // the min space between text and text.
    int    rowSpace;        //the space between text rows.
    float iconSpace;          // the min space between icon and text.
    
    TMColor color;
    TMColor backgroundColor;
    
    MapVector2d point;
    
    GLMapAnnotationIcon icon;   // the detailed icon information
    
    int nameCharCount;
    unsigned short name[1];
} GLMapAnnotationText;

/**
 *底图加载模式
 */
typedef enum _MapTileLoadMode {
  MapTileLoadModeOfflinePriority = 0,  //离线优先加载模式
  MapTileLoadModeOnlinePriority  = 1,  //在线优先加载模式
  MapTileLoadModeOnlyOffline     = 2,  //仅加载离线数据
  MapTileLoadModeOnlyOnline      = 3,  //仅加载在线数据
} MapTileLoadMode;


typedef struct _ModelID{
    unsigned long long model_poi_id;
    int model_class_code;
}ModelID;

/**
 * 自定义广告牌信息
 */
typedef struct _BillboardInfo{
    ModelID *model_ids;               // 广告牌通模ID
    int model_count;                  // 广告牌通模数量
    char *image_data_path;            // 广告牌数据路径
    int image_type;                   // 广告牌图片类型 jpg=0；png=1；webp=2
    unsigned long long crc_code;      // 广告牌crc校验码
    int billboard_type;               // 广告牌的类型
    int sprite_row;                   // 轮播图（大图）中小图的行数
    int sprite_column;                // 轮播图（大图）中小图的列数
    float slider_show_offset;         // 轮播图的播放间隔（单位秒）
    int slider_sprite_width;         //子图的宽
    int slider_sprite_height;        //子图的高
    int slider_image_width;          //大图的宽
    int slider_image_height;         //大图高
    int slider_sprite_count;         //子图的个数
}BillboardInfo;

/**
 根据经纬度和时间返回当天的日夜时间信息
 都已经转换成对应时区时间，非UTC时间
 */
typedef struct _DayInfo
{
    float sunRise_time;
    float sunFall_time;
    float dayStart_time; ///白天起始时间。dayStart_time = sunRise_time - 1.0;
    float dayEnd_time; ///白天结束时间。dayEnd_time = sunFall_time + 1
}DayInfo;

GLMapAnnotationText *GLMapAnnotationTextCreate(int nameCharCount);

void GLMapAnnotationTextRelease(GLMapAnnotationText *annotation);

/**
 地图动画完成回调

 @param finished 动画是否被打断
 @param context context
 */
typedef void    (*MapAnimationDidStopCallback)(bool finished, void *context);

/**
 * 单指双击点击回调，左上角原点
 @param screen_x  屏幕x坐标
 @param screen_y 屏幕x坐标
 @param context context
 */
typedef void (*MapOnDoubleTapCallback)(float screen_x, float screen_y, void* context);

/**
 蚯蚓线置灰动画状态
 */
typedef enum _RouteAnimationStatusType
{
    kRouteAnimationStatusRunning = 0,     //动画执行
    kRouteAnimationStatusFinished,     //动画完成
    kRouteAnimationStatusInterrupted, //动画中断
    kRouteAnimationStatusCanceled,    //动画取消
} RouteAnimationStatusType;

/*
  MapRouteSetPassedPointAnimation 路线置灰动画回调
  @param routeID 执行动画的路线ID
  @param pointIndex 动画置灰点所在点索引
  @param currentMapPoint 置灰点所在坐标
  @param isAnimationFinished 上次MapRouteSetPassedPointAnimation调用设置的动画是否完成
 */
typedef void    (*MapRoutePassedPointCallback)(int routeID, int pointIndex, MapVector2d currentMapPoint, float angle, RouteAnimationStatusType animationStatus, void* pCallbackContext);


/**
 MapBitmapTileID动作。要数据；取消要数据
 */
typedef enum _MapBitmapTileIDType
{
  kMapBitmapTileIDTypeRequest = 0,
  kMapBitmapTileIDTypeCancel = 1,
} MapBitmapTileIDType;

/**
 贴图时对应的 x, y, z, 使用时可能需要返回urlString
 */
typedef struct MapBitmapTileID
{
    int x;
    int y;
    int z;
    char urlString[512];
}MapBitmapTileID;


/**
 tileOverlay请求瓦片的回调

 @param parameter 瓦片参数
 @param context context
 */
typedef TMBitmap *(*MapCallback_LoadTile)(MapBitmapTileID *parameter, void *context);
    
/**
 写入数据的回调,tileOverlay里面可以通过此回调叫下载的tile 数据保存到本地
 
 @param data 数据
 @param data_size 数据大小
 @param context context
 */
typedef void (*MapCallback_WriteTile)(MapBitmapTileID *parameter, const void *data, int data_size, void *context);

typedef enum _NetRequestErrorHandle {
    kNetRequestErrorHandleNextRunloop    = 0,    // 固定的时间间隔后重试（默认2秒）
    kNetRequestErrorHandleNoMore         = 1,    // 不再尝试请求
    kNetRequestErrorHandleRetryNow       = 2,    // 立即发起重试请求
} NetRequestErrorHandle;

/**
 网络错误回调,tileOverlay里面可以通过此回调问客户端遇到网络错误的处理方式
 
 @param parameter 瓦片参数
 @param http_code http状态码
 @param context context
 */
typedef NetRequestErrorHandle (*MapCallback_NetRequestError)(MapBitmapTileID parameter, int http_code, void *context);
    
//纹理索引值是从上往下，0是顶部彩虹蚯蚓第一个颜色
enum COLOR_TRAFFIC_NEW {
    CTN_THROUGHED   = 0,        //走过的颜色，区别与其它颜色
    CTN_LIGHT_BLUE1 = 1,        //无路况，
    CTN_RED         = 2,        //拥堵
    CTN_YELLOW      = 3,        //缓行
    CTN_GREEN       = 4,        //畅通，公交方案里的公交线路
    CTN_LIGHT_BLUE5 = 5,        //无数据用淡蓝
    CTN_DARK_BLUE   = 6,        //5
    CTN_TRANSPARENT = 7,
    CTN_NO_SELECTED = 8,        //CT_LIGHT_BLUE, //走过的颜色，区别与其
    CTN_BROWN       = 9,        //褐色，猪肝红
    CTN_BLACK       = 10,
    CTN_DARK_BLACK  = 11,
    CTN_WALK_IN_BUS = 30,       //公交方案里的步行线路需要单独绘制点纹理，单独设置
    CTN_BUS_IN_BUS = 31,        //公交方案里的公交线路
    CTN_SUB_IN_BUS = 32,        //公交方案里的地铁线路
    //    CTN_WALK_ROUTE = 33,      //步行点纹理全部通过KDottedLine类型来处理，color不再生效
    CTN_BUS_LINE = CTN_DARK_BLUE,   //公交线的整条线路用深蓝色画
    CTN_SUB_LINE = CTN_DARK_BLUE,   //地铁线的整条线路用深蓝色画
    CTN_NONE = CTN_DARK_BLUE,
};
    
typedef MAP_ENUM(int, MapContent)
{
    MapContent_BaseMap          = 1 << 0,
    MapContent_Traffic          = 1 << 1,
    MapContent_StreetViewRoad   = 1 << 2,
    MapContent_Annotation       = 1 << 3,
    MapContent_Building         = 1 << 4,
    MapContent_IndoorBuilding   = 1 << 5,
    MapContent_Tree             = 1 << 6,
    MapContent_SkyBox           = 1 << 7,
    MapContent_LandMark         = 1 << 8,
    MapContent_FarmLand         = 1 << 9,
    MapContent_Region         = 1 << 10,
    MapContent_ALL              = 0XFFFF,
};

typedef MAP_ENUM(int, Landmark_Lod_level)
{
    Landmark_Lod_level_0          = 0, // 代表精模型 举例：10000170.obj
    Landmark_Lod_level_1          = 1, // 代表简模型0 举例：10000170_simply.obj
    Landmark_Lod_level_count      = 2, //  代表个数
};

typedef MAP_ENUM(int, MapLanguageType)
{
    LANGUAGE_NONE = 0,
    LANGUAGE_CN,                           //简体中文
    LANGUAGE_TW,                           //繁体中文
    LANGUAGE_EN,                           //英文
    LANGUAGE_PT,                           //葡萄牙文
};

typedef MAP_ENUM(uint64_t, DataFeatureTypeBuilding)
{
    BUILDING_TYPE_MAIN_HQ_ROUND_ANGLE = 0x01+0x04,
    BUILDING_TYPE_ANCILLARY_HQ_ROUND_ANGLE_ANCIENT = 0x01+0x04+0x1000,
    BUILDING_TYPE_MAIN_LQ_ROUND_ANGLE = 0x02+0x04,
    BUILDING_TYPE_ANCILLARY_LQ_ROUND_ANGLE_ANCIENT = 0x02+0x04+0x1000,
    BUILDING_TYPE_MAIN_HQ_RECT_ANGLE =  0x01+0x08,
    BUILDING_TYPE_ANCILLARY_HQ_RECT_ANGLE_ANCIENT = 0x01+0x08+0x1000,
    BUILDING_TYPE_MAIN_LQ_RECT_ANGLE =  0x02+0x08,
    BUILDING_TYPE_ANCILLARY_LQ_RECT_ANGLE_ANCIENT = 0x02+0x08+0x1000,
    ROOF = 0x100,
    BILL_BOARD_SCREEN = 0x200,
    DOOR = 0x400,
    GROUND_STORE = 0x800,
};

typedef MAP_ENUM(uint64_t, DataFeatureTypeLandUse)
{
    LANDUSE_TYPE_FARMLAND_HQ_PRODUCED = 0x01,
    LANDUSE_TYPE_FARMLAND_LQ_PRODUCED = 0x02,
    LANDUSE_TYPE_GREENAREA_HQ_PRODUCED  =  0x04,
    LANDUSE_TYPE_GREENAREA_LQ_PRODUCED  =  0x08,
    HAYSTACK_SCARECROW_HQ = 0x100,
    HAYSTACK_SCARECROW_LQ = 0x200,
};

typedef MAP_ENUM(uint64_t, SurfaceObjectLane)
{
    SURFACEOBJECT_NAVIGATION_GANTRY = 0x01 + 0x0200,
    SURFACEOBJECT_NAVIGATION_GUIDE_BRIDGE = 0x01 + 0x0400,
    SURFACEOBJECT_NAVIGATION_GUIDE_REFINED_BRIDGE_MODEL = 0x01 + 0x0800,
    SURFACEOBJECT_NAVIGATION_GREEN_BELT_PLANTS_MODEL = 0x01 + 0x2000,
    SURFACEOBJECT_NAVIGATION_PEDESTRIAN_GUARDRAIL_MODEL = 0x1 + 0x4000,
};

typedef MAP_ENUM(uint64_t, DataFeatureTypeLane)
{
    LANE_FEATURE_NAVIGATION_GUIDE = 0x01,
    LANE_FEATURE_NONE_NAVIGATION = 0x02,
};

typedef MAP_ENUM(int, DataFeatureTypeMainType)
{
    DataFeatureTypeMainType_Lane = 0,
    DataFeatureTypeMainType_TemplateModel = 1,
    DataFeatureTypeMainType_Building = 2,
    DataFeatureTypeMainType_Landuse = 3,
    DataFeatureTypeMainType_SurfaceObject = 4,
};

typedef MAP_ENUM(uint64_t, DataFeatureTypeTemplate)
{
    TREE_TYPE_STREET_TREE_LOW_DENSITY = 0x01,
    TREE_TYPE_STREET_TREE_MIDDLE_DENSITY = 0x02,
    TREE_TYPE_STREET_TREE_HIGH_DENSITY =  0x04,
    TREE_TYPE_REGIONALTREE_LOW_DENSITY =  0x08,
    TREE_TYPE_REGIONALTREE_MIDDLE_DENSITY = 0x10,
    TREE_TYPE_REGIONALTREE_HIGH_DENSITY = 0x20,
    FERRIS_WHEEL_LIGHT_HOUSE = 0x10000,
    SUBWAY_EXIT_ENTRANCE = 0x20000,
    GAS_STATION = 0x40000,
  	CHARGING_PILE = 0x100000,
  	OTHER = 0x200000,
    REPAIR_STATION = 0x400000,
};

typedef struct _GLMapFloorName
{
    char name[30];
} GLMapFloorName;
 
typedef struct _GLBuildingInfo
{
    unsigned long long        guid;
    unsigned short            name[33];
    GLMapFloorName            curFloorName;
} GLBuildingInfo;

#ifndef POINameMaxLength
#define POINameMaxLength                (32)
#endif

typedef struct _POIInfo
{
    MapLocation        coordinate;
    unsigned short     name[POINameMaxLength];
} POIInfo;
    
    /************************************************************************/
    /*                              MapContents                             */
    /************************************************************************/
    #pragma mark MapContents
    
    enum MapLogLevel{
        MapLogLevel_Debug = 1,  //调试信息
        MapLogLevel_Info  = 2,  //普通信息
        MapLogLevel_Error = 3,  //错误信息
    };


typedef MAP_ENUM(int, TXAnimationOwner)
{
    BaseMap_Anim   = 0,
    Marker_Anim    = 1,
    Locator_Anim   = 2,
};

typedef MAP_ENUM(int, TXAnimationTypeEnum)  {
    BaseMap_Center_Anim = 0,
    BaseMap_Scale_Anim,
    BaseMap_Skew_Anim,
    BaseMap_Rotate_Anim,
    BaseMap_Style_Anim,
    BaseMap_Overlook_Anim,//底图过场动画
    BaseMap_Moveby_Anim,//底图屏幕像素变化的动画
    BaseMap_Gesture_Zoom_And_Skew_Anim,//手势操作引起的底图（缩放+倒伏）动画

    Marker_Alpha_Anim= 100,
    Marker_Position_Anim,
    Marker_Scale_Anim,
    Marker_Rotate_Anim,
    Marker_ScreenOffset_Anim,
};
 
#ifndef AnimationContentValueLength
#define AnimationContentValueLength                (100)
#endif
typedef struct _AnimationContent
{
    TXAnimationTypeEnum animType; //动画类型
    char targetValue[AnimationContentValueLength];   //动画的目标值，100 byte是针对最大的过场动画使用
}AnimationContent;

typedef struct _TXAnimationParam
{
    MapAnimationCurveType curvetype;   //动画曲线
    int animDuration;        //动画的持续时间，单位ms
    int animationDelay;       //动画延迟执行的时间，单位ms
    bool beginFromCurrentState;    //为true时有动画会先停止，然后动画从当前状态开始执行新的动画。false时会直接设置到目标状态，然后开始新的动画
    AnimationContent *animContent;    //数组，包含动画类型和参数数值
    int contentNum;             //同时执行的动画的个数
    int overlayID;           //执行对象的ID，Animation_BaseMap和Animation_Overlay_Locator动画不需要传入
}TXAnimationParam;

typedef MAP_ENUM(int, TXCustomRasterPriorityEnum)  {
    BelowRegion,
    AboveRegionBelowLine,
    AboveLine,
    AboveBuilding,
    CustomRasterPriorityNum,
};

typedef struct _BuildingLoadedParam{
    char modelid[30]; //模型的id
    char poiid[30]; //模型对应的poiid
    MapVector2d*  contour; //经纬度的轮廓点
    int  contour_num;//轮廓点个数
    double height; //高度，单位米
    float  fov; //相机张角，固定不变
}BuildingLoadedParam;

typedef struct _MapModelReportFlag {
    bool has_report_model;// 标记是否上报
    char poiid[30];       // 模型poiid
    int style_id;         // 模型style
    int model_main_type;  // 主类型
    int model_sub_type;   // 子类型
    MapVector2f position; // 经纬度
}MapModelReportFlag;

typedef void (*LogHandler)(const char* str, int length, void* user_data);

    void MapSetMarsXLogInfo(int level, const char *info);


    typedef struct TXUploadLogArgs_ {
          const char * submodule;
          const char * event;
          const char * extra;
    } TXUploadLogArgs;

    //日志回流云端
    void MapUploadLog(int level, TXUploadLogArgs loginfo);

    void MapSetMarsXLogHandler(LogHandler handler, void* user_data);

    /**
     开启或关闭部分渲染内容

     @param pWorld 场景句柄
     @param bEnabled 开关
     @param contents 场景内容类型
     */
    void MapContentsSetEnabled(void *pWorld, bool bEnabled, MapContent contents);

    /**
     设置地图中特定内容的显示级别

     @param pWorld 场景句柄
     @param scaleLevel 显示级别
     @param contents 场景内容类型
     */
    void MapContentsSetVisibleScaleLevel(void *pWorld, int scaleLevel, MapContent contents);
    
    /**
     建议废弃的函数，使用MapContentsSetEnabled替代
     
     @param pWorld XXX
     @param aSwitch XXX
     */
    void GLMapSetSwitchOfStreetView(void *pWorld, bool aSwitch);
    
    /**
     检查特定城市是否存在街景路网
     
     @param pWorld 场景句柄
     @param aCityName 城市ID
     @return 是/否
     */
    bool GLMapIsCityHasStreetviewRoad(void *pWorld, const unsigned short* aCityName);
    
    /**
     街景是否显示
     
     @param pWorld XX
     @return XXX
     */
    bool GLMapIsStreetRoadShown(void *pWorld);
    
    void MapSetSatelliteEnabled(void *pWorld, bool enabled);

    void MapSetDemEnabled(void *pWorld, bool enabled);

    void MapSetDTMEnabled(void *pWorld, bool enabled);

    /**
     开关默认的树图层
     @param pWorld 引擎实例
     @param enable 是否开关底图默认的树图层
    */
    void MapSetDefaultTreeEnable(void *pWorld, bool enable);
    
    /************************************************************************/
    /*                               显示相关                                */
    /************************************************************************/
    #pragma mark 显示相关
    /**
     动态msaa，只有在camera停止发生变化时开启多重采样
     
     @param pWorld 场景句柄
     @param bDynamicMSAA 开关状态
     */
    void MapSetDynamicMSAA(void *pWorld, bool bDynamicMSAA);
    
    /**
     设置底图文字大小
     @param pWorld 场景句柄
     @param fontSizeFlag  MF_INVALID = -1, MF_NORMAL = 0, MF_LARGE = 1, MF_HUGE = 2, MF_SMALL = 3, MF_CARPLAY = 4
     */
    bool GLMapSetFontSize(void *pWorld, int fontSizeFlag);

    //废弃，统一使用GLMapSetFontSize
    bool GLMapSetSceneFontSize(void *pWorld, int fontSizeFlag);
    
    int GLMapGetFontSize(void *pWorld);

    
    /**
     通过屏幕坐标(pixel)获取地图上的元素
     
     @param pWorld pWorld
     @param screenCoordinate 屏幕坐标
     @param element 返回element
     */
    void MapOnTap(void *pWorld, MapVector2f screenCoordinate, MapTappedInfo *element);

    /**
     通过双击屏幕坐标(pixel)获取地图上的元素
 
     @param pWorld pWorld
     @param screenCoordinate 屏幕坐标
     @param element 返回element
     */
    void MapOnDoubleTap(void *pWorld, MapVector2f screenCoordinate, MapOnDoubleTapCallback p_double_callback, void * context, MapTappedInfo *element);

    /**
     * 三击底图响应
     * @param pWorld
     * @param screenCoordinate
     * @param element
     */
    void MapOnTripleTap(void *pWorld, MapVector2f screenCoordinate, MapTappedInfo *element);

    /**
     测试指南针是否选中（只有ios用到）
     
     @param pWorld pWorld
     @param screenCoordinate 屏幕坐标
     @return 是否点选中了指南针
     */
    bool MapTapCompass(void *pWorld, MapVector2f screenCoordinate);
    
    /**
     重新设置路径
     
     @param pWorld pWorld
     @param dataDir 数据目录
     @param configDir 配置文件目录
     @param sateDir 卫星图目录
     */
    void MapResetPath(void *pWorld, const char *dataDir, const char *configDir, const char *sateDir, const char *demDir, const char *offlineDir);
    
    /*
     重新设置路径；更轻量； 仅仅改变路径，不需要重新创建数据引擎
     @param pWorld pWorld
     @param configDir 配置文件目录
     */
    void MapResetConfPath(void *pWorld, const char *configDir);
    
    /**
     获取地图城市名称
     
     @param pWorld pWorld
     @param x x 坐标
     @param y y 坐标
     @param buf 返回城市名称
     @param bufSize bufSize
     */
    void GLMapGetCityName(void *pWorld, int x, int y, char *buf, int bufSize);
    
    /**
     是否需要重绘
     
     @param pWorld pWorld
     @return 是否需要重绘
     */
    UNITY_EXPORT bool GLMapNeedsDisplay(void *pWorld);
    
    /**
     设置重绘的flag
     
     @param pWorld pWorld
     @param bNeedsDisplay 是否需要重绘
     */
    UNITY_EXPORT void GLMapSetNeedsDisplay(void *pWorld, bool bNeedsDisplay);
    
    /**
     清除缓存
     Thread safe, usage: GLMapLockEngine(); GLMapClearCache(); GLMapUnlockEngine();
     
     @param pWorld pWorld
     */
    void GLMapClearCache(void *pWorld);
    
    /**
     lock engine
     清除缓存，离线地图现在完成需要lock engine，消息队列以后不需要，待删除
     @param pWorld pWorld
     */
    void GLMapLockEngine(void *pWorld);
    
    /**
     unlock engine，消息队列以后不需要，待删除
     
     @param pWorld pWorld
     */
    void GLMapUnlockEngine(void *pWorld);


    /**
    * 是否将兜底样式颜色，作为地面颜色的第一选择，
    */
    void GLMapSetForceUseDefaultGroundColor(void *pWorld, bool force_use);

    /**
     *设置背景颜色,目前仅用于再样式解析失败后，作为兜底颜色的
     */
    void GLMapSetGroundColorForDefault(void *pWorld, TMColor tmColor);

    /**
     获取背景颜色
     
     @param pWorld pWorld
     @return 背景颜色
     */
    TMColor GLMapGetBackgroundColor(void *pWorld);
    
    /**
     设置viewPort，坐标pixel

     @param pWorld pWorld
     @param x x
     @param y y
     @param width width
     @param height height
     */
    void MapSetViewport(void *pWorld, int x, int y, int width, int height);
    void MapGetViewport(void *pWorld, int* x, int* y, int* width, int* height);

    void MapSetDefaultViewport(void *pWorld, int x, int y, int width, int height);
    void MapSetFps(void *pWorld, int fps);
    void MapGetFps(void *pWorld, int* fps);
    void MapGetRealFps(void *pWorld, int* fps);
    
    void MapSetOverview(void *pWorld, bool bEnalbed, int cornerLength, float opacity);
    void MapSetOverviewFrame(void *pWorld, bool bEnalbed, MapColor4ub color, float width);
    
    void MapSetSkyRatioOnScreen(void *pWorld, float ratio);
    void MapSetSkyMaxRatioAndMinSkew(void *pWorld, float max_ratio, float min_skew);
    void MapSetScreenBottomToSkyBottomDistance(void *pWorld, int distance);
    int  MapGetScreenBottomToSkyBottomDistance(void *pWorld);
    void MapSetMaxTileCountInScreenBottomToSkyBottom(void *pWorld,int count);
    int  MapGetMaxTileCountInScreenBottomToSkyBottom(void *pWorld);
    
    void GLMapFetchMapVersions(void *pWorld);
    int GLMapGetDataVersion(void* pWorld);
    int GLMapGetDataVersionByCityName(void* pWorld, const unsigned short *cityName);

    /**
    * 设置天空盒的纹理
    * @param pWorld 场景句柄
    * @param texture_name 纹理名称
    */
    void MapSetSkyBoxTexture(void *pWorld, const char* texture_name);
    
    /**
     获取引擎SVN版本；线程安全
     @param pWorld 场景句柄
     @param buf        版本Buffer，外部申请
     @param bufSize    版本Buffer大小，外部指定
     */
    void GLMapGetRevision(void *pWorld, char *buf, int bufSize);
    
    /**
     获取当前库采用的渲染引擎版本号
     @param pWorld 场景句柄
     */
    const char * MapGetMapEngineVersion(void *pWorld);
    
    /**
     获取当前库采用的数据引擎版本号
     @param pWorld 场景句柄
     */
    const char * MapGetDataEngineVersion(void *pWorld);
    
    /**
     屏幕截图
     Needs OpenGL context
     @param pWorld 场景句柄
     @param area 截图区域（像素单位）
     @param pBuffer 截图数据存储位置（仅支持RGBA格式）
     */
    void MapSnapshot(void *pWorld, MapRectD area, unsigned char *pBuffer);
    
    /************************************************************************/
    /*                               Camera                                 */
    /************************************************************************/
#pragma mark Camera
    
#ifdef MAP_INTERACTOR
    
    /**
     屏幕坐标转换为地理坐标

     @param pWorld 场景句柄
     @param screenCoordinate 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     @return 地理坐标，单位：墨卡托
     */
    MapVector2d MapGetWorldCoordinate(void *pWorld, MapVector2f screenCoordinate);
    /**
     将屏幕坐标转换为地理坐标，并且如果转换后的地理坐标在天空盒范围内，则将其强制设置为天空盒下方的坐标
     @param pWorld 场景句柄
     @param screenCoordinate 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     @return 地理坐标，单位：墨卡托
     */
    MapVector2d MapGetWorldCoordinateWithoutSky(void *pWorld, MapVector2f screenCoordinate);
    /**
    屏幕坐标转换为地理坐标

    @param pWorld 场景句柄
    @param screenCoordinate 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
    @param outputWorldCoordinate 地理坐标，单位：墨卡托
    @return 是否有效
    */
    bool MapGetWorldCoordinateWithStatus(void *pWorld, MapVector2f
    screenCoordinate,
                               MapVector2d *outputWorldCoordinate);

    /**
     地理坐标转换为屏幕坐标
     
     @param pWorld 场景句柄
     @param worldCoordinate 地理坐标，单位：墨卡托
     @return 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     */
    MapVector2f MapGetScreenCoordinate(void *pWorld, MapVector2d worldCoordinate);


    /**
     3D地理坐标转换为屏幕坐标
     
     @param pWorld 场景句柄
     @param worldCoordinate 3D地理坐标，单位：墨卡托
     @return 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     */
    MapVector2f MapGetScreenCoordinate3D(void *pWorld, MapVector3d* worldCoordinate);

    /**
     获取缩放比例

     @param pWorld 场景句柄
     @return 缩放比例 20级->1.0;19级->0.5;18级->0.25...
     */
    double  GLMapGetScale(void *pWorld);

    /**
     获取相机的张角
     @param pWorld 场景句柄
     @return 张角 fovy
     */
    float  GLMapGetCameraFovy(void *pWorld);

    /**
     * 重设底图Fov角度为默认值
     * @param pWorld 场景句柄
     * @param animated  是否包含动画
     * @param duration 动画时长 单位 s
     * @param didStopCallback 动画结束回调
     * @param context 回调相关Context
     * @param curveType 插值方式
     */
    void GLMapResetFov(void *pWorld, bool animated, float duration, MapAnimationDidStopCallback didStopCallback, void *context, MapAnimationCurveType curveType);

    /**
     * 开启默认自动俯仰角策略
     * @param pWorld 场景句柄
     * @param use 是否开启
     */
    void GLMapEnableDefaultAutoSkew(void *pWorld, bool use);
    
    /**
     设置缩放比例

     @param pWorld 场景句柄
     @param scale 缩放比例
     @param animated 动画渐变
     */
    void    GLMapSetScale(void *pWorld, double scale, bool animated);

    /**
     设置缩放比例

     @param pWorld 场景句柄
     @param scale 缩放比例
     @param durationInSeconds 动画时间
     */
    void    GLMapSetScaleWithDuration(void *pWorld, double scale, float durationInSeconds);
    
    /**
     获取缩放级别

     @param pWorld 场景句柄
     @return 缩放级别
     */
    UNITY_EXPORT int     GLMapGetScaleLevel(void *pWorld);
    
    /**
     设置缩放级别

     @param pWorld 场景句柄
     @param scaleLevel 缩放级别
     @param animated 动画渐变
     */
    UNITY_EXPORT void    GLMapSetScaleLevel(void *pWorld, int scaleLevel, bool animated);

    /**
     设置缩放级别

     @param pWorld 场景句柄
     @param scaleLevel 缩放级别
     @param durationInSeconds 动画时间
     */
    void    GLMapSetScaleLevelWithDuration(void *pWorld, int scaleLevel, float durationInSeconds);
    
    /**
     缩放比例到缩放级别的转换，线程无关

     @param scale 缩放比例
     @return 缩放级别
     */
    int     MapScaleToScaleLevel(double scale);
    float   MapScaleToScaleLevelF(double scale);
    double  MapScaleLevelToScale(float scalelevel);
    
    /**
     获取地图中心点

     @param pWorld 场景句柄
     @return 地图中心点坐标，单位：墨卡托
     */
    UNITY_EXPORT MapVector2d GLMapGetCenterMapPoint(void *pWorld);
    
    /**
     设置地图中心点

     @param pWorld 场景句柄
     @param mapPoint 中心点坐标
     @param animated 动画渐变
     @param didStopCallback 动画结束回调
     @param pCallBackObject 回调句柄
     */
    UNITY_EXPORT void    GLMapSetCenterMapPoint(void *pWorld, MapVector2d mapPoint, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     设置地图天空占比

     @param pWorld 场景句柄
     @param percent 百分比 0~100 对应0%~100%
     */
    void GLMapSetSkyPercent(void *pWorld, int percent);

    /**
     获取像素点代表的距离；
     备注：开平业务侧 单独实现了手势和动画，需要该接口，仅仅为开平；
     */
    double  MapGetPerPixelDistance(void *pWorld);

    /**
     获取相机到中心点距离 单位 p20
     */
    float   GLMapGetEyeCenterDistance(void *pWorld);
    
    /**
     手势平移操作

     @param pWorld 场景句柄
     @param dx 屏幕x方向像素偏移
     @param dy 屏幕y方向像素偏移
     */
    UNITY_EXPORT void    GLMapMoveBy(void *pWorld, float dx, float dy, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     检查是否能够继续放大地图

     @param pWorld 场景句柄
     @return true->能 false->不能
     */
    bool    GLMapCanZoomIn(void *pWorld);
    
    /**
     检查是否能够继续缩小地图

     @param pWorld 场景句柄
     @return true->能 false->不能
     */
    bool    GLMapCanZoomOut(void *pWorld);
    
    /**
     放大地图

     @param pWorld 场景句柄
     @param x 缩放中心点x像素坐标
     @param y 缩放中心点y像素坐标
     @param animated 动画
     @param didStopCallback 动画回调
     @param pCallBackObject 动画回调上下文
     @return -1 缩放失败  0 以（x，y）为中心点缩放，1 以屏幕中心点进行缩放
     */
    int     GLMapZoomIn(void *pWorld, float x, float y, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    /**
     放大地图

     @param pWorld 场景句柄
     @param x 缩放中心点x像素坐标
     @param y 缩放中心点y像素坐标
     @param animated 动画
     @param autoSkew 自动比例尺
     @param didStopCallback 动画回调
     @param pCallBackObject 动画回调上下文
     @return -1 缩放失败  0 以（x，y）为中心点缩放，1 以屏幕中心点进行缩放
    */
    int     GLMapZoomInSkew(void *pWorld, float x, float y, bool animated, bool autoSkew, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    /**
     缩小地图

     @param pWorld 场景句柄
     @param animated 动画渐变
     @return -1 缩放失败 1 缩放成功
     */
    int     GLMapZoomOut(void *pWorld, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    /**
     * 缩小地图
     * @param pWorld 场景句柄
     * @param animated 动画渐变
     * @param autoSkew 自动俯仰角
     * @param didStopCallback 动画回调
     * @param pCallBackObject 动画回调上下文
     * @return -1 缩放失败 1 缩放成功
     */
    int     GLMapZoomOutSkew(void *pWorld, bool animated, bool autoSkew, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    /**
     缩小地图

     @param pWorld 场景句柄
     @param x 缩放中心点x像素坐标
     @param y 缩放中心点y像素坐标
     @param animated 动画渐变
     @return -1 缩放失败 1 缩放成功
     */
    int     GLMapZoomOutWithCenter(void *pWorld, float x, float y, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    /**
     * 缩小地图
     @param pWorld 场景句柄
     @param x 缩放中心点x像素坐标
     @param y 缩放中心点y像素坐标
     @param animated 动画渐变
     @param autoSkew 自动俯仰角
     @return -1 缩放失败 1 缩放成功
     */
    int     GLMapZoomOutWithCenterSkew(void *pWorld, float x, float y, bool animated, bool autoSkew, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    /**
     获取倾斜角
     倾斜角：视野方向中心轴与地面垂线的夹角
     @param pWorld 场景句柄
     @return 倾斜角
     */
    UNITY_EXPORT float   GLMapGetSkew(void *pWorld);

    /**
     获取天空在屏幕上的占比
     @param pWorld 场景句柄
     @return 天空占比
     */
    float   GLMapGetSkyRatio(void *pWorld);

    /**
     设置倾斜角
     倾斜角：视野方向中心轴与地面垂线的夹角
     @param pWorld 场景句柄
     @param skew 倾斜角
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     @param curveType 插值方式
     */
    UNITY_EXPORT void    GLMapSetSkew(void *pWorld, float skew, bool animated, MapAnimationDidStopCallback didStopCallback, void *context, MapAnimationCurveType curveType);

    // @param tolerance_angle_diff 俯仰角摆动旷量
    void    GLMapSetSkewWithReason(void *pWorld, float skew, bool animated, MapAnimationDidStopCallback didStopCallback, void *context,float tolerance_angle_diff, MapAnimationCurveType curveType);

    /**
     设置倾斜角
     倾斜角：视野方向中心轴与地面垂线的夹角
     @param pWorld 场景句柄
     @param skew 倾斜角
     @param durationInSeconds 动画时间,单位秒
     @param didStopCallback XXX
     @param context XXX
     @param curveType 插值方式
     */
    void    GLMapSetSkewWithDuration(void *pWorld, float skew, float durationInSeconds, MapAnimationDidStopCallback didStopCallback, void *context, MapAnimationCurveType curveType);

    /**
     设置倾斜角
     倾斜角：视野方向中心轴与地面垂线的夹角
     @param pWorld 场景句柄
     @param skew 倾斜角
     @param animated 是否开启动画
     @param durationInSeconds 动画时间,单位秒
     @param tolerance_angle_diff 俯仰角摆动旷量
     @param auto_skew 是否开启自动俯仰角调节
     @param didStopCallback 动画结束回调
     @param context 动画执行上下文（一般是当前实例）
     */
    void GLMapSetSkewImpl(void *pWorld, float skew, bool animated, float durationInSeconds,float tolerance_angle_diff, bool auto_skew,
                          MapAnimationDidStopCallback didStopCallback, void *context, MapAnimationCurveType curveType);
    
    /**
     获取旋转角
     旋转角：假设墨卡托地图南极方向为V0，视野方向中心轴映射到地面的矢量为V1，该角度为V0逆时针旋转到V1所经历的角度。

     @param pWorld 场景句柄
     @return 旋转角
     */
    UNITY_EXPORT float   GLMapGetRotate(void *pWorld);
    
    /**
     设置旋转角
     旋转角：假设墨卡托地图南极方向为V0，视野方向中心轴映射到地面的矢量为V1，该角度为V0逆时针旋转到V1所经历的角度。
     @param pWorld 场景句柄
     @param rotate 旋转角
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     */
    UNITY_EXPORT void    GLMapSetRotate(void *pWorld, float rotate, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);

    /**
     设置缓存大小比率，基础大小为8M
     @param pWorld 场景句柄
     @param ratio 比率
     @param isFast 是否高速加载
     */
    void    GLMapSetMemoryRatioAndLoadMode(void *pWorld, float ratio, bool isFast);

    /**
     旋转操作

     @param pWorld 场景句柄
     @param centerX 旋转中心点像素坐标x值
     @param centerY 旋转中心点像素坐标y值
     @param radian 旋转弧度
     @return 旋转中心点是否吸附到屏幕中心点
     */
    bool    GLMapRotate(void *pWorld, float centerX, float centerY, float radian);
    
    // speedX/speedY:
    
    /**
     滑动手势结束时惯性动画

     @param pWorld 场景句柄
     @param speedX 滑动速度X值(pixels per second)
     @param speedY 滑动速度Y值(pixels per second)
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     */
    void    GLMapSwipe(void *pWorld, float speedX, float speedY, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     捏合手势，控制缩放

     @param pWorld 场景句柄
     @param centerX 中心点像素坐标X值
     @param centerY 中心点像素坐标Y值
     @param scale 缩放变化比例
     @return 是否吸附到屏幕中心点
     */
    bool    GLMapPinch(void *pWorld, float centerX, float centerY, float scale);

    /**
     * 捏合手势，控制缩放
     * @param pWorld 场景句柄
     * @param centerX 中心点像素坐标X值
     * @param centerY 中心点像素坐标Y值
     * @param scale 缩放变化比例e
     * @param autoSkew 开启自动俯仰角
     * @retur 是否吸附到屏幕中心点n
     */
    bool    GLMapPinchSkew(void *pWorld, float centerX, float centerY, float scale, bool autoSkew);

    /**
     *
     * @param pWorld        场景句柄
     * @param prePoints     双指离开前一刻位置(x,y,x,y, ...)
     * @param prePointsNum  pre points数量
     * @param curPoints     双指离开时的位置(x,y,x,y, ...)
     * @param curPointsNum  cur points数量
     * @param time          两个位置测量时间差
     */
    void    GLMapInertialMove(void *pWorld, float* prePoints, int prePointsNum, float* curPoints, int curPointsNum, long time);

    /**
    *
    * @param pWorld        场景句柄
    * @param centerX 指定的中心坐标
    * @param prePoints     双指离开前一刻位置(x,y,x,y, ...)
    * @param prePointsNum  pre points数量
    * @param curPoints     双指离开时的位置(x,y,x,y, ...)
    * @param curPointsNum  cur points数量
    * @param time          两个位置测量时间差
    */
    void    GLMapInertialMoveWithCenter(
        void *pWorld,float centerX,float centerY,float*prePoints,
        int prePointsNum, float* curPoints, int curPointsNum, long time);
    
    /**
     建议废弃的函数，与GLMapPinch功能重复
     
     @param pWorld XXX
     @param scale XXX
     @param isPercent XXX
     @return XXX
     */
    bool    GLMapPinchInCenter(void *pWorld, float scale, bool isPercent);
    
    /**
     Fix screen points dev0, dev1 to geography points geo0, geo1.
     This operation may change map display parameters like scale, center, rotation except for skew.
     @param pWorld 场景句柄
     @param dev0 屏幕坐标
     @param dev1 屏幕坐标
     @param geo0 地理坐标
     @param geo1 地理坐标
     */
    //void    MapPinchWithPointsFixed(void *pWorld, MapVector2f dev0, MapVector2f dev1, MapVector2d geo0, MapVector2d geo1);
    
    /**
     全览
     在特定倾斜角和旋转角下，通过自动调整缩放比例和地图中心点，将特定地理区域限制在特定屏幕区域内。
     @param pWorld 场景句柄
     @param geoRect 地理区域
     @param devRect 屏幕区域
     @param skewAngle 目标倾斜角
     @param rotateAngle 目标旋转角
     @param screenCenterOffsetType 考虑到执行 Overlook 时可能处于 ScreenOffset 动画中，ScreenOffset 不稳定导致计算结果不稳定，因此由调用方指定 ScreenOffsetType
     @param userScreenCenterOffset 调用方指定的 ScreenOffset 值，只在 screenOffsetType == User 时生效，参数规格同 MapSetScreenCenterOffset 接口（Y大于0视角向上移，取值范围[-0.5, 0.5]）
     @param animating XXX
     @param callback XXX
     @param context XXX
     @param durationInSeconds 动画时间（以秒为单位）
     */
    void    MapOverLook(void *pWorld, MapRectD geoRect, MapRectD devRect, float skewAngle, float rotateAngle, bool animating, MapAnimationDidStopCallback callback, void *context);
    void    MapOverLookWithTime(void *pWorld, MapRectD geoRect, MapRectD devRect, float skewAngle, float rotateAngle, bool animating, MapAnimationDidStopCallback callback, void *context, float durationInSeconds);
    /**
     * @note 默认使用Fov=45度
     */
    void MapOverLookEx(void *pWorld, OverlookParam* overlookParam);
    /**
     全览目标显示参数预获取

     @param pWorld 场景句柄
     @param geoRect 地理区域
     @param devRect 屏幕区域
     @param skewAngle 目标倾斜角
     @param rotateAngle 目标旋转角
     @param screenCenterOffsetType 考虑到计算过程可能处于 ScreenOffset 动画中，ScreenOffset 不稳定导致计算结果不稳定，因此由调用方指定 ScreenOffsetType
     @param userScreenCenterOffset 调用方指定的 ScreenOffset 值，只在 screenOffsetType == User 时生效，参数规格同 MapSetScreenCenterOffset 接口（Y大于0视角向上移，取值范围[-0.5, 0.5]）
     @return 目标显示参数
     */
    MapDisplayParam MapOverLookGetDisplayParam(void *pWorld, MapRectD geoRect, MapRectD devRect, float skewAngle, float rotateAngle);
    MapDisplayParam MapOverLookGetDisplayParamEx(void *pWorld, CameraOverlookParam* cameraParam);
    
    //void    MapSetScreenCenterOffset(void *pWorld, MapVector2f offset, bool animated);
    // Navigation animation, pNextPoint is pointer to next turn-arrow coordinate.
    
    /**
     导航缩放动画
     配合GLMapSetPaddingToZoomForNavigation使用，将下一个关键点显示在屏幕特定高度。
     
     @param pWorld 场景句柄
     @param nextPoint 下一个关键点
     @param minScaleLevel 最小缩放级别
     @param maxScaleLevel 最大缩放级别
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     */
    void    MapZoomForNavigation(void *pWorld, MapVector2d nextPoint, int minScaleLevel, int maxScaleLevel, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);

    /**
     导航缩放动画
     配合GLMapSetPaddingToZoomForNavigation使用，将下一个关键点显示在屏幕特定高度。不考虑白箭头的范围
     
     @param pWorld 场景句柄
     @param nextPoint 下一个关键点
     @param minScaleLevel 最小缩放级别
     @param maxScaleLevel 最大缩放级别
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     */
    void    MapZoomForPureNavigation(void *pWorld, MapVector2d nextPoint, int minScaleLevel, int maxScaleLevel, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     导航缩放动画
     配合GLMapSetPaddingToZoomForNavigation使用，将下一个关键点显示在屏幕特定高度。
     
     @param pWorld 场景句柄
     @param zoomParameter 缩放动画参数
     @param animated XXX
     @param didStopCallback XXX
     @param context XXX
     */
    void    MapZoomForNavigationEx(void *pWorld, ZoomForNaviParameter zoomParameter, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     * 通过给定的目标俯仰角计算最佳比例尺，提供一系列地理坐标点和屏幕范围，根据比例尺进行缩放能够让所有的点都位于屏幕内，并且距离中心最远的点恰好位于屏幕边缘
     * 单位：像素
     *
     * @param geoPoints 给定的地理坐标点
     * @param screenRect 给定的屏幕范围
     */
    float MapGetBestScaleLevel(const void *pWorld, const MapVector2d* geoPoints, const int numGeoPoints, const MapRectD screenRect);
    /**
     * 通过给定的目标俯仰角计算最佳比例尺，提供一系列地理坐标点和屏幕范围，根据比例尺进行缩放能够让所有的点都位于屏幕内，并且距离中心最远的点恰好位于屏幕边缘
     * 单位：像素
     *
     * @param geoPoints 给定的地理坐标点
     * @param screenRect 给定的屏幕范围
     * @param skewAngle 给定的目标俯仰角(0-65)
     */
    float MapGetBestScaleLevelWithSkew(const void * pWorld, const MapVector2d* geoPoints, const int numGeoPoints, const MapRectD screenRect, const float skewAngle);
    /**
    * 通过给定的目标俯仰角计算最佳比例尺，提供一系列地理坐标点和屏幕范围，根据比例尺进行缩放能够让所有的点都位于屏幕内，并且距离中心最远的点恰好位于屏幕边缘
    * 单位：像素
    *
    * @param geoPoints 给定的地理坐标点
    * @param screenRect 给定的屏幕范围
    * @param skewAngle 给定的目标俯仰角(0-65)
    * @param rotateAngle 给定的目标旋转角
    */
    float MapGetBestScaleLevelWithSkewAndRotate(const void *pWorld, const MapVector2d* geoPoints, const int numGeoPoints, const MapRectD screenRect, const float skewAngle, const float rotateAngle);


    /**
     导航缩放动画，padding限制
     单位：像素
     @param pWorld 场景句柄
     @param topPadding 限制：屏幕上侧
     @param leftPadding 限制：屏幕左侧
     @param bottomPadding 限制：屏幕下侧
     @param rightPadding 限制：屏幕右侧
     */
    void    GLMapSetPaddingToZoomForNavigation(void *pWorld, float topPadding, float leftPadding, float bottomPadding, float rightPadding);

    /**
     获取导航缩放动画，padding限制
     单位：像素
     @param pWorld 场景句柄
     @param topPadding 限制：屏幕上侧
     @param leftPadding 限制：屏幕左侧
     @param bottomPadding 限制：屏幕下侧
     @param rightPadding 限制：屏幕右侧
     */
    void    GLMapGetPaddingToZoomForNavigation(void *pWorld, float* topPadding, float* leftPadding, float* bottomPadding, float* rightPadding);

    /**
     导航缩放动画，padding限制区域缩进比例
     padding限制的区域往屏幕中心缩进 (限制区域宽*widthScale、限制区域高*heightScale)
     @param pWorld 场景句柄
     @param widthScale 横向缩进比例(0-1.0)
     @param heightScale 纵向缩进比例(0-1.0)
     */
    void GLMapSetIndentScaleOfPaddedRegion(void *pWorld, float widthScale, float heightScale);

    /**
     设定底图显示的最小比例尺和地理范围
     @param pWorld           场景句柄
     @param minScaleLevel    显示的开始比例尺
     @param restrictBounds   显示的地理范围；地理像素坐标
     @note  1>当限制范围值均为0, 则代表取消之前设置的限制范围;
            2>取消比例尺限制 请用其他现有接口设置;
     */
    void    GLMapSetRestrictBounds(void *pWorld, float minScaleLevel, MapRectD restrictBounds);

    /**
     设置地图最小缩放级别

     @param pWorld 场景句柄
     @param minScaleLevel 最小缩放级别
     */
    UNITY_EXPORT void    GLMapSetMinScaleLevel(void *pWorld, int minScaleLevel);
    
    /**
     设置地图最大缩放级别
     
     @param pWorld 场景句柄
     @param maxScaleLevel 最大缩放级别
     */
    UNITY_EXPORT void    GLMapSetMaxScaleLevel(void *pWorld, int maxScaleLevel);
    
#else
    
    /**
     设置frustum参数，用于游戏场景，实现camera绑定

     @param pWorld 场景句柄
     @param aspect perspective参数aspect
     @param fovy perspective参数fovy
     @param zNear perspective参数zNear
     @param zFar perspective参数zFar
     @param screenOffset 屏幕中心点下移
     */
    void MapSetFrustum(void *pWorld, float aspect, float fovy, float zNear, float zFar, MapVector2f screenOffset);
#endif  // MAP_INTERACTOR end
    
    /**
     设置屏幕中心点偏移
     offset = (offsetX / screenWidth, offsetY / screenHeight)
     @param pWorld 场景句柄
     @param offset 中心点下移占屏幕比例(0.0f, 0.0f)无下移;(0.2f, 0.0f)屏幕下侧，距离底部(0.5 - 0.2) = 3/10处.
     */
    void MapSetScreenCenterOffset(void *pWorld, MapVector2f offset, bool animated);
    
    /**
     获取屏幕中心点偏移（动画过程中，获取的是目标偏移值，而非当前值）
     
     @param pWorld 场景句柄
     @return 中心点偏移（-0.5 ~ 0.5, -0.5 ~ 0.5）
     */
    MapVector2f MapGetScreenCenterOffset(void *pWorld);
    
    /**
     * 设置false，只是影响了camera的center值，camera的center不再等于地图center
     * 目前手图设置为true, 车机为flase, 内部默认true.
     */
    void MapSetCenterOffsetByFrustum(void *pWorld, bool bCenterOffset);

    /**
     * 设置camera lod 参数
     */
    void MapSetCameraLodParams(void *pWorld, int* level_arr, float* factor_arr, int size);
    
    /************************************************************************/
    /*                           底图样式相关接口                              */
    /************************************************************************/
    #pragma mark 底图样式相关接口
    
    /**
     设置地图样式索引

     @param pWorld 场景句柄
     @param mapStyleID 样式索引
     @param reuseOnSwitch 切换的过程中是否复用当前样式
     */
    void MapSetMapStyle(void *pWorld, int mapStyleID, bool reuseOnSwitch);
    
    /**
     设置地图样式索引
     
     @param pWorld 场景句柄
     @param mapStyleID 样式索引
     @param reuseOnSwitch 切换的过程中是否复用当前样式
     @param isAnimation 切换样式时，是否有动画过度
     @param animationDuration 样式过度动画的执行时长,isAnimation=true时才生效
     */
    void MapSetMapStyleWithAnimation(void *pWorld, int mapStyleID, bool reuseOnSwitch,
                                     bool isAnimation, double animationDuration);
    
    /**
     获取当前地图样式索引

     @param pWorld 场景句柄
     @return 样式索引
     */
    int  MapGetMapStyle(void *pWorld);
    
    /**
     设置皮肤对应动态样式索引
     
     @param pWorld 场景句柄
     @param mapSkinUID 动态样式索引
     @param reuseOnSwitch 切换的过程中是否复用当前样式
     */
    void MapSetMapSkin(void *pWorld, int mapSkinUID, bool reuseOnSwitch);
    
    /**
     设置皮肤对应动态样式索引，带动画
     
     @param pWorld 场景句柄
     @param mapSkinUID 样式索引
     @param reuseOnSwitch 切换的过程中是否复用当前样式
     @param isAnimation 切换样式时，是否有动画过度
     @param animationDuration 样式过度动画的执行时长,isAnimation=true时才生效
     */
    void MapSetMapSkinWithAnimation(void *pWorld, int mapSkinUID, bool reuseOnSwitch,
                                     bool isAnimation, double animationDuration);
    
    /**
     获取当前地图动态样式索引
     
     @param pWorld 场景句柄
     @return 动态样式索引
     */
    int  MapGetMapSkin(void *pWorld);

    /**
     * 设置样式对应icon资源文件名称的映射规则
     * @param pWorld         实例句柄
     * @param res_name_list  icon资源文件名称集合
     * @param list_size      每个icon资源文件名称包含的样式数据
     * @param index_list     样式集合
     * @param size           icon资源文件的数量
     */
    void MapSetStyleMappingRule(void *pWorld, const char* res_name_list, int* list_size, int* index_list, int size);
    
    /**
     获取当前是否是HD场景
     @param pWorld 场景句柄
     @return true->是hd ; false->不是hd
     */
    bool MapIsHdScene(void *pWorld);

    /**
     获取当前是否是HD场景
     @param pWorld 场景句柄
     @param use_external_config true: 外部设置生效  false：外部设置不生效
     @param scene 1: HD优先  2: sd优先
     @return
     */
    void MapUseHdSdExternalConfig(void *pWorld, bool use_external_config, int scene);
    /************************************************************************/
    /*                              动画相关接口                              */
    /************************************************************************/
    #pragma mark 动画相关接口
    
    /**
     设置动画精度，控制动态降帧。动画精度最高时，动态降帧可能被关闭
     
     @param pWorld pWorld
     @param type 精度
     */
    void MapSetAnimationQuality(void *pWorld, MapAnimationQualityType type);
    
    /**
     开始动画状态设置的标志（状态机）

     @param pWorld 场景句柄
     */
    void GLMapBeginAnimations(void *pWorld);
    
    /**
     结束动画状态设置，并提交（状态机）

     @param pWorld 场景句柄
     */
    void GLMapCommitAnimations(void *pWorld);
    
    /**
     设置动画时间

     @param pWorld 场景句柄
     @param duration 时间间隔（单位：秒）
     */
    void GLMapSetAnimationDuration(void *pWorld, double duration);
    
    /**
     设置动画延迟执行的时间

     @param pWorld 场景句柄
     @param delay 时间间隔（单位：秒）
     */
    void GLMapSetAnimationDelay(void *pWorld, double delay);
    
    /**
     设置动画curve类型

     @param pWorld 场景句柄
     @param curve curve类型
     */
    void GLMapSetAnimationCurve(void *pWorld, MapAnimationCurveType curve);
    
    /**
     设置动画是否从当前状态接着执行，最终解释权归nopwang

     @param pWorld 场景句柄
     @param fromCurrentState 是否从当前状态接着执行
     */
    void GLMapSetAnimationBeginsFromCurrentState(void *pWorld, bool fromCurrentState);
    
    /**
     设置动画结束回调

     @param pWorld 场景句柄
     @param didStopCallback 回调函数
     @param context 回调句柄
     */
    void GLMapSetAnimationDidStopCallback(void *pWorld, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     检查是否有移动动画，但不表示正在执行移动动画
     
     hasMoving , not isMoving!
     
     @param pWorld 场景句柄
     @return 是/否
     */
    bool    GLMapHasMovingAnimation(void *pWorld);
    
    /**
     停止当前的移动动画
     
     @param pWorld 场景句柄
     */
    void    GLMapStopMovingAnimation(void *pWorld);
    
    /************************************************************************/
    /*                            Tile Overlay                              */
    /************************************************************************/
    #pragma mark Tile Overlay相关接口
    
    /**
     创建一个新TileOverlay图层
     @param pWorld 场景句柄
     @param loadTileCallback        加载瓦片回调函数
     @param writeTileCallback       回写瓦片回调函数
     @param context                 调用上下文
     @param betterQuality           高清/普清显示：true for HD, false for SD
     @return 返回图层ID
     */
    int GLMapAddTileOverlay(void *pWorld, MapCallback_LoadTile loadTileCallback, MapCallback_WriteTile writeTileCallback, void *context, bool betterQuality);
    
    /**
     根据图层ID，移除指定TileOverlay图层
     @param pWorld 场景句柄
     @param tileOverlayId 图层ID
     */
    void GLMapRemoveTileOverlay(void *pWorld, int tileOverlayId);
    
    /**
     根据图层ID，重新加载TileOverlay图层(内部会重新创建图层，特殊属性需要外部重新设置，如数据级别范围)
     @param pWorld 场景句柄
     @param tileOverlayId 图层ID
     */
    void GLMapReloadTileOverlay(void *pWorld, int tileOverlayId);

    /**
     根据图层ID，清除已经注册的回调,以免产生野指针问题. 外部注意: 不可在回调中调用此方法,以免造成内部死锁
     @param pWorld 场景句柄
     @param tileOverlayId 图层ID,若传递 -1 则所有的TileOverlay图层的回调全部清除
     */
    void GLMapTileOverlayRemoveCallback(void *pWorld, int tileOverlayId);

    
    /**
     查询Tile Overlay所有图层的启用状态
     @param pWorld 场景句柄
     @return TileOverlay图层开启状态
     */
    bool GLMapIsTileOverlayEnabled(void *pWorld);
    
    /**
     设置TileOverlay所有图层的启用状态
     @param pWorld 场景句柄
     @param tileRenderEnabled TileOverlay图层状态，true 启用，false 禁用
     */
    void GLMapSetTileOverlayEnabled(void *pWorld, bool tileRenderEnabled);
    
    /**
     设置TileOverlay对应业务可用的数据级别；(不设置引擎默认4到18级)
     @param pWorld 场景句柄
     @param tileOverlayId 图层ID
     @param minDataLevel 可用数据最小级别，一般为4；
     @param maxDataLevel 可用数据最大级别
     */
    void GLMapSetTileOverlayDataLevelRange(void *pWorld, int tileOverlayId, int minDataLevel, int maxDataLevel);

    /**
     获取TileOverlay对应业务可用的数据级别；(不设置引擎默认4到18级)
     @param pWorld 场景句柄
     @param tileOverlayId 图层ID
     @return 最小和最大显示级别
     */
    MapVector2i GLMapGetTileOverlayDataLevelRange(void *pWorld, int tileOverlayId);

    /**
     设置TileOverlay的显示级别范围
     @param pWorld        场景句柄
     @param tileOverlayId 图层ID
     @param minScaleLevel 最小显示级别
     @param maxScaleLevel 最大显示级别
     */
    void GLMapSetTileOverlayVisibleScaleLevelRange(void *pWorld, int tileOverlayId, int minScaleLevel, int maxScaleLevel);

    /**
     设置TileOverlay网络请求错误的时候回调
     @param pWorld 场景句柄
     @param tileOverlayId 图层ID
     @param netRequestCallBack 最小和最大显示级别
     */
    void GLMapTileOverlaySetNetErrorCallBack(void *pWorld, int tileOverlayId, MapCallback_NetRequestError netRequestCallBack);
    
  /**
   设置TileOverlay瓦片的bitmap
   @param pWorld 场景句柄
   @param tileOverlayId 图层ID
   @param tileId 瓦片信息
   @param txbmp 瓦片栅格图
   */
    void GLMapTileOverlaySetTileContent(void *pWorld, int tileOverlayId, const MapBitmapTileID *tileId, TMBitmap *txbmp);

#define kTileOverlayDefaultPriority (0x8000000)
    int GLMapTileOverlayGetPriority(void *pWorld, int tileOverlayId);
    void GLMapTileOverlaySetPriority(void *pWorld, int tileOverlayId, int priority); // kTileOverlayDefaultPriority +1, +2, +3 ...
    
    /**
     设置TileOverlay的渲染层级
     目前只支持两个层级（100和200），默认为200
     @param pWorld          场景句柄
     @param tileOverlayId   图层ID
     @param renderLayer     渲染层级：100-楼块之后、L4之前；200-楼块和L4之后
     */
    void GLMapTileOverlaySetRenderLayer(void *pWorld, int tileOverlayId, int renderLayer);
    /**
     获取TileOverlay的渲染层级
     @param pWorld          场景句柄
     @param tileOverlayId   图层ID
     @return    渲染层级
     */
    int GLMapTileOverlayGetRenderLayer(void *pWorld, int tileOverlayId);

    /**
     设置TileOverlay的跨级复用级数和级数偏移
     @param pWorld  场景句柄
     @param tileOverlayId   图层ID
     @param searchDepth 跨级复用级数
     @param levelOffset 级数偏移
     */
    void GLMapTileOverlaySetSearchDepthAndLevelOffset(void *pWorld, int tileOverlayId, int searchDepth, int levelOffset);
    void GLMapTileOverlaySetNetErrorCallBack(void *pWorld, int tileOverlayId, MapCallback_NetRequestError netRequestCallBack);

    /**
     设置TileOverlay网络请求失败回调
     @param pWorld  场景句柄
     @param tileOverlayId   图层ID
     @param netRequestCallBack 网络请求失败回调
     */
    void GLMapTileOverlaySetNetErrorCallBack(void *pWorld, int tileOverlayId, MapCallback_NetRequestError netRequestCallBack);

    /************************************************************************/
    /*                            Annotation                                */
    /************************************************************************/
    #pragma mark Annotation相关接口

    TMBitmap *GLMapDrawAnnotationObject(void *pWorld, TMBitmap *iconBitmap, GLMapAnnotationText *annotationObject, MapVector2f *anchorPoint);
    
    void GLMapAnnotationNeedAvoidColorRoutes(void *pWorld,bool needAvoid);
    
    /**
     Annotation 文字能否点击，默认false
     */
    void GLMapAnnotationClickTextEnable(void *pWorld,bool clickTextEnable);
    
    /**
     重新加载底图标注资源
     @param pWorld 场景句柄
     */
    void GLMapReloadAnnotations(void* pWorld);
    
    void GLMapRefineVipText(void *pWorld, GLMapAnnotationText **texts, int textsCount, bool shouldKeepIcon,bool selectFirst);
    
    int GLMapQueryCityList(void *pWorld, MapRectD geoRect, int scaleLevel, int* cityList, int count);
    
    /************************************************************************/
    /*                           动态底图相关接口                              */
    /************************************************************************/
    #pragma mark 动态底图相关接口
    
    /**
     动态底图 Annotation
     */
    typedef struct _DynamicMapAnnotationObject{
        MapVector2d point;
        
        int priority;
        int classCode;
        int from_scale;
        int dynamic_flag;
        
        unsigned int    nameCharCount;
        unsigned int    firstNameCount;
        unsigned short  name[1];
        
    }DynamicMapAnnotationObject;
    
    DynamicMapAnnotationObject *DynamicMapAnnotationObjectCreate(int nameCharCount);
    
    void DynamicMapAnnotationObjectRelease(DynamicMapAnnotationObject *annotation);
    
    /*
     Function: In walk or cycling mode, add some import pois data along route to display on base map
     */
    bool GLDynamicMapWritePOI(void *pWorld, unsigned short index, DynamicMapAnnotationObject ** pois, int poiCount);
    
    /*
     Function: When there are more than zero route on map, to use this interface switch active pois along the route.
     */
    bool GLDynamicMapShowPOI(void *pWorld, unsigned short index, bool bShow);
    
    /*
     Function: When User give up walk or cycling map, clear dynamic poi data;
     
     index == -1, clear all pois;
     index == 0, 1, 2 ... , clear some bat poi;
     */
    bool GLDynamicMapClearPOI(void *pWorld, int index);
    
    //////////////////////////////////////////////////////////////////////////
    
    /************************************************************************/
    /*                              车机相关接口                              */
    /************************************************************************/
    #pragma mark 车机相关接口
    
    void MapSetOfflineEnabled(void *pWorld, bool enabled);
    
    int GLMapCheckAndClearMapCache(void *pWorld, int nSize);
    
    /************************************************************************/
    /*                               工具类接口                               */
    /************************************************************************/
    
    /**
     重新加载路况数据
     
     @param pWorld 场景句柄
     */
    void GLMapInvalidateAllTileTextures(void* pWorld);
    
    /**
     工具类函数：生成贝塞尔曲线
     
     @param start 起点
     @param end 终点
     @param controlPoints 控制点数组
     @param controlCount 控制点数
     @param output 输出点串数组
     @param outputCount 输出点串数组大小
     @return 是否成功生成
     */
    bool MapBezierGeneratePoints(MapVector2d *start, MapVector2d *end, MapVector2d *controlPoints, int controlCount, MapVector2d *output, int outputCount);
    
    /**
     废弃的函数
     
     @param pWorld XXX
     @return XXX
     */
    float MapSightGetOnScreenHeight(void *pWorld);
    
    /**
     启用临时任务，任务执行后立即回调(MapCallBack_MapEvent);
     @param pWorld          场景句柄
     @param taskType        任务类型
            1> MapTaskType_CheckMapLoadingFinished 类型时回调类型为 MapCallBackType_MapLoadingFinished
     @return 是:启动成功; 否:启动不成功
     备注：首次创建引擎实例，内部默认启动MapTaskType_CheckMapLoadingFinished任务，执行一次回调；其他时机 需要外部触发；
     */
    bool GLMapStartTaskAndDidStopCallBack(void *pWorld, MapTaskType taskType);
    
    /**
     获取引擎状态信息；
     上层奔溃时，调用该接口获取引擎的一些核心信息，
     */
    const char * GLMapGetMapEngineReportMapParms(void *pWorld);
    
    /**
     获取当前打印的数据引擎log，按照时间顺序排列，log间使用\n分割
     与GLMapSetDataEngineReportBufSize成对使用
     @param pWorld   场景句柄
     @return log对应的缓存指针，log以\0结束
     */
    const char * GLMapGetDataEngineReportLog(void *pWorld);
    
    /**
     设置内部数据引擎日志buffer的大小，环形存储，当缓存区满时覆盖旧的日志。为0时关闭log，清空日志。重复调用会清空上一次的日志。
     与GLMapGetDataEngineReportLog成对使用
     @param pWorld        场景句柄
     @param logBufSize    日志buffer的大小，以字节为单位
     */
    void GLMapSetDataEngineReportBufSize(void *pWorld,int logBufSize);
    
    /**
     获取指定室内图的显示起始级别
     若guid对应的室内图数据存在，则可返回有效的起始级别, 否则返回一个默认级别.
     @param pWorld   场景句柄
     @param guid     室内图guid
     @param showFromLevel     显示起始级别
     @return true 有数据，有效起始级别，false 缺少数据，用默认级别
     */
    bool MapIndoorBuildingGetIndoorBuildingShowFromScaleLevel(void *pWorld, const unsigned long long* guid, int* showFromLevel);
    
    /**
     返回当前屏幕范围内的兴趣点列表
     @param pWorld 场景句柄
     @param pPoiInfoBuffer    信息存储缓冲区,外部申请
     @param poiInfoBufferSize 信息存储缓冲区大小；
     @param bExtendMargin     是否返回屏幕外边缘的兴趣点；
     @return                  实际返回的POI个数；
     */
    int GLMapGetPoisOnScreen(void *pWorld, POIInfo* pPoiInfoBuffer, int poiInfoBufferSize, bool bExtendMargin);
    
    /**
     设置俯视看时，是否使用正投影 (效果: 正2D下 楼块是否3D效果带阴影)
     备注: 引擎默认true, 开平:需要单独设置
     @param pWorld 场景句柄
     @param enabled    使用正投影开关，true:打开， false:关闭；
     */
    void GLMapSetUsingOrthoWhenLookingDown(void *pWorld, bool enabled);

    void MapFastLoad(void *pWorld);

    void GLMapSetPipe(void* pWorld, TXMapDataType type, TXMapEventType eventType, void* pValue);

    /**
     * 设置建筑物窗户点亮的阈值，范围为0～1，数值越大，越难点亮。0即为全部点亮
     * 调用该接口控制点亮前，要确保已经设置了窗户点亮纹理，否则不会响应
     @param pWorld  场景句柄
     @param threshold 阈值(0~1)
     */
    void GLMapSetBuildingLightThreshold(void *pWorld, float threshold);

    /**
     * 设置建筑物参与碰撞的高度门槛，单位为米，底图默认值为15
     * @param pWorld 场景句柄
     * @param height 碰撞高度门槛
     */
    void GLMapSetBuildingCollisionHeightThreshold(void *pWorld, float height);

    /**
     * 设置建筑物相机碰撞的模式
     * @param  pWorld  场景句柄
     * @param  on      true:完整碰撞模式 false:单体碰撞模式
     */
    void GLMapSetWholeBuildingCollisionMode(void *pWorld, bool on);

    /**
     * 设置建筑物窗户点亮的纹理（纹理的下半部分为不点亮时窗户的颜色，上半部分为点亮的颜色，点亮颜色可以为多种，根据纹理中的分布比例决定点亮的比例）
     * @param pWorld  场景句柄
     * @param name 纹理，纹理的宽和高可以不同，但是必须为2^n
     */
    void GLMapSetBuildingWindowTexture(void *pWorld, const char* name);

    /**
     * 设置建筑物是否避让屏幕
     * @param pWorld   场景句柄
     * @param switchOn 开启相机楼块碰撞
     */
    void GLMapSwitchCameraBuildingCollision(void *pWorld, bool switchOn);
    
    /**
    组合动画原子接口
    @param pWorld    场景句柄
    @param owner    执行动画的目标种类
    @param animParam     动画参数，包含动画目标的ID、动画种类、时长、曲线等参数
    @param didStopCallback 动画结束回调
    @param pCallBackObject 回调句柄
    @return  是否设置成功，失败包括参数解析、动画种类不支持等
    */
    bool MapSetAnimationObject(void *pWorld, TXAnimationOwner owner, const TXAnimationParam *animParam, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);

    bool MapSetAnimationObjectEx(void *pWorld, TXAnimationOwner owner, const TXAnimationParam *param, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject, bool isMapbiz);
    
    void MapSetWaterWaveEnable(void *pWorld, bool enabled);

    /**
     设置Icon动画开关
     @param pWorld    场景句柄
     @param enable    Icon动画开关
     */
    void MapSetIconAnnotationEnable(void *pWorld, bool enable);

    /**
     设置Icon动画的方向
     @param pWorld    场景句柄
     @param direct    Icon动画方向
     */
    void MapSetIconAnnotationDirect(void *pWorld, bool direct);

    /**
     设置Icon动画的classcode
     @param pWorld    场景句柄
     @param classcode    动画icon的classcode
     @param freq    动画批次
     */
    void MapSetIconAnnotationClassCode(void *pWorld, int classcode, int freq);
    
    /**
     获取封路icon优先级
     @param pWorld    场景句柄
     */
    int GLMapGetBlockRouteIconPriority(void *pWorld);

    /**
     设置封路icon优先级
     @param pWorld    场景句柄
     @param priority 优先级
     */
    void GLMapSetBlockRouteIconPriority(void *pWorld, int priority);


	//自定义矢量图层查询返回的点信息
    typedef struct _CustomTileQueryInfo{
      unsigned long long poi_id; //poiID
      int layer_id;              //所属的图层id
      MapVector2d position;      //位置点，墨卡托坐标
      double altitude;           //高度
      int class_code;            //样式ID
      unsigned short name[POINameMaxLength];   //poi名称
      int name_len;          //名称的字数
      unsigned char ext_info[MaxCustomPointExtInfoLen]; //扩展信息buffer，最大1024字节
      int ext_info_len;          //扩展信息的实际字节长度
    }CustomTileQueryInfo;

    /**
    rich信息图片生成的回调
    @param poi_info  poi点的详细信息
    @param context 回调上下文
    */
    typedef TMBitmap *(*MapCallback_GenRichImage)(const CustomTileQueryInfo *poi_info, void *context);

    //rich信息图片或者颜色回调返回信息，目前只有中远打卡的颜色信息，可扩充
    typedef struct _RichCallbackInfo{
        TMColor* color;
        int color_count;
    }RichCallbackInfo;

    /**
    rich信息图片或者颜色批量生成回调
    @param poi_info  poi点的详细信息
    @param info_count  poi点的个数
    @param context 回调上下文
    @return 回调信息，信息中color的个数必须和info_count相等
    */
    typedef RichCallbackInfo (*MapCallback_GenRichInfo_Batch)(const CustomTileQueryInfo *poi_info, int info_count, void *context);

    /**
     自定义图层点扩展信息透出的回调函数定义
     @param layer_id 图层id
     @param tile_id 点所在的块ID，x-y-z的形式
     @param point_info 包含扩展信息的点数据，见CustomTilePointExtInfo定义
     @param point_num 点的个数
     @param context 回调上下文，引擎内部使用
     */
    typedef void (*MapCallback_CustomTilePointLoaded)(int layer_id, const char *tile_id,
        const CustomTileQueryInfo *point_info, int point_num, void *context);

    /**
     自定义图层中的瓦块析构
     @param layer_id 图层id
     @param tile_id 点所在的块ID，x-y-z的形式
     @param context 回调上下文，引擎内部使用
     */
    typedef void (*MapCallback_CustomTileReleased)(int layer_id, const char *tile_id, void *context);

	//自定义矢量图层线样式
	typedef struct _CustomTileLineStyle{
		int style_id;	//样式编号，需要和数据中的classcode对应
		MapVector2i level_range; //显示的最大最小级别
		TMColor stroke_color; //线的填充颜色
		TMColor border_color; //线的描边颜色
		float stroke_width; //线的填充宽度，单位Dp
		float border_width; //线的描边宽度，单位Dp
		MapVector2f dash_solide_spcae; //dash线实心和空白部分的长度，单位Dp，非dash线时都为0
	}CustomTileLineStyle;

	//自定义矢量图层点样式
	typedef struct _CustomTilePointStyle{
		int style_id;	//样式编号，需要和数据中的classcode对应
		MapVector2i level_range; //style生效的最大最小级别

		float font_size; //字体大小，单位dp
		float border_width; //描边宽度，单位dp
		TMColor color;  //文字颜色
		TMColor border_color; //描边颜色
		bool bold; //是否粗体
		bool avoid_other;//是否避让其他点
		bool can_be_avoided;//是否能够被避让（false时按照点的优先级压盖显示）

		unsigned char sub_type; //文字icon的相对位置, 0-4 左上右下中
		float text_icon_space; //文字icon的间距

		char icon_name[MapImageNameMaxLength]; //icon资源
		MapVector2f icon_anchor; //icon的锚点，左上角为0,0点，x向右，y向下，与overlay锚点坐标系一致

		float rich_text_space; //rich和文字的间距，单位Dp
	}CustomTilePointStyle;

	//自定义矢量图层面样式
	typedef struct _CustomTileRegionStyle{
		int style_id;	//样式编号，需要和数据中的classcode对应
		MapVector2i level_range; //显示的最大最小级别
		TMColor color; //面的填充颜色
	}CustomTileRegionStyle;

    //自定义图层3DObj样式
    typedef struct _CustomTileModel3DStyle{
        int style_id;    //样式编号，需要和数据中的classcode对应
        char mesh_name[MapImageNameMaxLength]; //mesh文件名称，引擎通过回调读取
        char image_name[MapImageNameMaxLength]; //纹理名称，引擎通过回调读取
    }CustomTileModel3DStyle;

    /**
     * 添加自定义矢量图层
     * @param pWorld 引擎实例
     * @param url_pattern 自定义图层的url,
     * @param rect 限制区域列表，左上角和右下角的墨卡托坐标
     * @param rect_count 限制区域的个数
     * @param level_range 显示的级别范围
     * @param line_styles 线样式内容列表
     * @param line_style_count 线样式个数
     * @param region_styles 面样式内容列表
     * @param region_style_count 面样式个
     * @param point_styles 点样式内容列表
     * @param point_style_count 点样式个数
	 * @return 图层的ID
	 */
	int MapAddCustomTileLayer(void *pWorld, const char *url_pattern, MapRectD *rect, int rect_count,  MapVector2i level_range, const CustomTileLineStyle *line_styles, int line_style_count, const CustomTileRegionStyle *region_styles, int region_style_count, const CustomTilePointStyle *point_styles, int point_style_count);

    /**
     * 设置自定义3D物图层样式
     * @param pWorld 引擎实例
     * @param url_pattern 自定义图层下载的url, 瓦片和版本编号部分使用"%s"填充
     * @param rect 限制区域列表，左上角和右下角的墨卡托坐标
     * @param rect_count 限制区域的个数
     * @param level_range 显示的级别范围
     * @param style_list 样式内容列表
     * @param style_num 样式个数
     * @return 图层的ID
     */
    int MapAddCustomTileModel3DLayer(void *pWorld, const char *url_pattern, MapRectD *rect, int rect_count, MapVector2i level_range, CustomTileModel3DStyle *style_list, int style_num);

	/**
	 * 清空自定义图层的内存缓存
	 * @param pWorld 引擎实例
	 * @param layer_id 图层ID
	 */
	void MapDeleteCustomTileLayer(void *pWorld, int layer_id);

	/**
	 * 清空自定义图层的内存缓存
	 * @param pWorld 引擎实例
	 * @param layer_id 图层ID
	 */
	void MapRefreshCustomTileLayer(void *pWorld, int layer_id);

    /**
     * 刷新已经生成layer的样式（目前仅仅支持纹理）
     * @param pWorld 引擎实例
     * @param layer_id 图层ID
     * @param style_list 样式内容列表
     * @param style_num 样式个数
     */
    void MapUpdateCustomTileLayer(void *pWorld, int layer_id, CustomTileModel3DStyle *style_list, int style_num);

	/**
	 * 清空自定义图层的内存缓存
	 * @param pWorld 引擎实例
	 * @param layer_id 图层ID
	 */
	void MapSetCustomTileLayerVisible(void *pWorld, int layer_id, bool visible);

	/**
	 * 查询指定图层屏幕区域内的点数据
	 * @param pWorld 引擎实例
	 * @param layer_ids 图层ID列表
	 * @param screen_area 屏幕像素范围，格式为左上角x,y右下角x，y
	 * @param query_num 返回的查询的个数
	 * @return 查询结果，内部生成的buffer不需要释放
	 */
	CustomTileQueryInfo * MapQueryCustomTileByScreenArea(void *pWorld, const int *layer_ids, int layer_num, MapVector4i screen_area, int *query_num);

    /**
     * 查询屏幕内已加载的块中包含的点信息，当瓦块没有全部加载完毕时点信息为空（块内的点可能不在屏幕内）
     * @param pWorld 引擎实例
     * @param layer_id 图层ID
     * @param point_info  查询到的点列表，外部拷贝使用，无需释放
     * @param point_num 查询到的点个数
     * @return true：屏幕内的块已加载完毕，false：未加载完
     */
    bool MapQueryCustomTilePointLoadedInScreen(void *pWorld, int layer_id, CustomTileQueryInfo **point_info, int *point_num);

	/**
	 * 设置Rich信息生成图片的回调
	 * @param pWorld 引擎实例
	 * @param genFun 生成图片的回调
	 * @param context 生成图片的context
	 */
	void MapSetRichGenCallback(void *pWorld, MapCallback_GenRichImage genFun, void* context);

    /**
     * 设置Rich信息生成图片的回调
     * @param pWorld 引擎实例
     * @param gen_fun 生成图片和color的回调
     * @param context 生成图片和color的context
     */
    void MapSetRichGenCallbackBatch(void *pWorld, MapCallback_GenRichInfo_Batch gen_fun, void* context);

    /**
     * 获取和设置底图矢量缓存策略
     * @param pWorld 引擎实例
     * @param strategyDays_m 缓存策略天数
     * @param togetherDays_n 缓存AB共存天数
     */
    void MapGetFileCacheDays(void *pWorld, int *strategyDays_m, int *togetherDays_n);
    bool MapSetFileCacheDays(void *pWorld, const int strategyDays_m, const int togetherDays_n);

    /**
     * 创建栅格图层，如果有相同url的会替换掉之前的，创建新的
     * @param pWorld 引擎实例
     * @param url 下载数据的url，引擎会填充goolge标准瓦片的xyz
     * @param priority 优先级，同样优先级的图层会按照添加先后顺序，后边的优先显示
     * @param file_tag 文件缓存存储的目录名称（统一在mapcreate中dataDir参数下的子目录）
     * @param min_max_level  最小级别x和最大级别y，级别内的瓦片会被请求
     * @param leftup 左上角墨卡托坐标，与此范围有交集的瓦片会被请求
     * @param rightbottom 右下角墨卡托坐标，与此范围有交集的瓦片会被请求
     * @return 图层id（>0 为有效）
     *
     */
    int MapAddCustomTileRasterLayer(void *pWorld, const char* url, TXCustomRasterPriorityEnum priority, const char* file_tag, MapVector2i min_max_level, MapVector2d leftup, MapVector2d rightbottom);

    /**
     * 删除栅格文件缓存
     * @param pWorld 引擎实例
     * @param file_tag 数据库的文件名
     *
     */
    void MapRemoveCustomTileRasterLayerCache(void *pWorld, const char* file_tag);

    /**
     * 设置自定义图层点扩展信息透出回调
     * @param pWorld 引擎实例
     * @param point_fun  数据块中的点加载完成的回调函数
     * @param context  回调的context
     */
    void MapSetCustomTilePointLoadedCallback(void *pWorld, MapCallback_CustomTilePointLoaded point_fun, void *context);

    /**
     * 设置自定义图层中的瓦块析构回调
     * @param pWorld 引擎实例
     * @param release_fun  数据块中的点加载完成的回调函数
     * @param context  回调的context
     */
    void MapSetCustomTileReleasedCallback(void *pWorld, MapCallback_CustomTileReleased release_fun, void *context);

    /**
     * 根据点的poiid查询详细信息
     * @param pWorld 引擎实例
     * @param layer_id 图层ID
     * @param poi_id  poiID
     * @param info poiid对应的扩展信息
     * @return 是否查询到
     */
    bool MapQueryCustomTilePointByID(void *pWorld, int layer_id, unsigned long long poi_id, CustomTileQueryInfo* info);

	/**
	 * 注入网络接口
	 * @param httpHandler 网络能力句柄，对象为tencentmap::HttpInterface实现类
	 * @param external true-注入后，生命周期由引擎内部控制，外层切勿主动释放资源
	 *                 false-外部托管生命周期，危险！如果生命周期错误会导致引擎崩溃
	 * @return 0-成功 1-参数非法
	 */
	int MapSetNetHttpDelegate(void* httpHandler, bool external);

    /**
     * 注入设备信息状态监听，注入后由引擎内部托管，外部无需释放内存
     * @param provider 监听器
     */
    void MapSetDeviceInfoProvider(void* provider);

  /**
   * 注意：此接口set之后，立即get。获取的值不是最新的。
   */
  void GLMapGetPipe(void* pWorld,TXMapEventType eventType, void* pValue);

  /**
   * 设置是否打开调试图层（默认关闭）
   * @param pWorld  场景句柄
   * @param show 是否显示
   */
  void GLMapSetShowDebugTile(void* pWorld, bool show);

#ifndef IS_SLIM
    /**设置和获取地图瓦片的加载模式
    * @param pWorld 场景句柄
    * @param mode 底图加载模式,默认模式为离线优先加载模式
    */
    bool GLMapSetMapTileLoadMode(void *pWorld, MapTileLoadMode mode);
    MapTileLoadMode GLMapGetMapTileLoadMode(void *pWorld);
#endif

    /**
    * 世界图开关参数
    * @param pWorld    场景句柄
    * @param isCountry 是否设定全国级别(全国级别指数据级别3/5/7, 更大为城市级别) true:全国级别,false:城市级别
    * @param enable    是否开启, true:开启状态, false:关闭状态
    */
    bool MapSetSupportWorldMapEnable(void *pWorld, bool isCountry, bool enable);
    /**
     * 返回是否支持世界图开关状态
     * @param pWorld    场景句柄
     * @param isCountry 是否是全国级别, true:全国级别, false:城市界别
     * @return true:开启状态, false:关闭状态
     */
    bool MapIsSupportWorldMap(void *pWorld, bool isCountry);

    /**
     * 设置sdk版本号，用于路况jce数据
     * @param version sdk版本号
     */
    void MapSetSDKVersion(const char* version);

    /**
     * 设置3D球开关
     * @param pWorld  引擎句柄
     * @param enable  3D球开关
     */
    void MapSetGlobeEnable(void* pWorld, bool enable);
    /**
     * 设置球体偏移
     * @param pWorld              引擎句柄
     * @param xoffset              x轴上偏移，值为1时，偏移一个球体模型原始大小的宽度，大于0往右偏、小于0往左偏
     * @param yoffset              y轴上偏移，值为1时，偏移一个球体模型原始大小的高度，大于0往上偏、小于0往下偏
     * @param durationInMS   过渡时间，单位是毫秒
     * @param eraseType         过渡曲线
     */
    void MapSetGlobeShift(void* pWorld, float xoffset, float yoffset, int durationInMS, int eraseType);
    /**
     * 设置3D球相关纹理
     * @param pWorld           引擎句柄
     * @param sphere_name      球的纹理名称
     * @param background_name  背景纹理名称
     * @param cloud_name       云层纹理名称
     */
    void MapModifyGlobeImage(void* pWorld, const char* sphere_name, const char* background_name,const char* cloud_name);

    /**
     开关Landmark图层，不设置时默认开启，但是显隐还受级别（16.4级及以下）、倾角（不为0）、样式配置影响
     @param pWorld 引擎实例
     @param enable_by_user 根据业务场景开关
    */
    void MapSetLandmarkEnable(void *pWorld, bool enable_by_user);

    /**
     开关3D POI
     @param pWorld 引擎实例
     @param enable_3d_poi 根据业务场景开关3D POI功能
    */
    void MapSet3DPOIEnable(void *pWorld, bool enable_3d_poi);

    /**
    开关3D POI
    @param pWorld 引擎实例
    @param enable_3d_poi_avoid_building 根据业务场景开关3D POI是否避让楼块
    */
    void MapSet3DPOIAvoidBuildingEnable(void *pWorld, bool enable_3d_poi_avoid_building);

    /**
    开关POI 3D避让(3D避让新接口，上面旧接口暂时保留，后面让上层改为新接口调用)
    @param pWorld 引擎实例
    @param enable_3d_poi_avoid_building 根据业务场景开关3D POI是否避让楼块
    @param alpha 遮挡poi透明度（被poi关联建筑遮挡的poi透明度， 0.0:遮挡剔除, 范围[0.0, 1.0], 大于1.0认为全显示，被非poi关联建筑遮挡时剔除不显示）
    @param alpha_start_scale POI 3D遮挡半透生效起始比例尺（底图默认17级），poi3D避让目前暂时不支持设置起始比例尺
    */
    void MapSetPOI3DOcclusionEnable(void *pWorld, bool enable_3d_poi_avoid_building, float alpha, int alpha_start_scale);

    /**
    开关3D AOI
    @param pWorld 引擎实例
    @param enable_3d_aoi 根据业务场景开关3D AOI功能
    */
    void MapSet3DAOIEnable(void *pWorld, bool enable_3d_aoi);

    /**
    设置3D AOI的显示级别范围，默认0-30
    @param pWorld 引擎实例
    @param minLevel 显示最小级别
    @param maxLevel 显示最大级别
    */
    void MapSet3DAOIDisplayLevel(void *pWorld, float minLevel, float maxLevel);

    /**
     设置Landmark图层的内存释放参数，使用的总内存（memory_limit）超限且非当前显示的缓存对象个数（cache_num）超限时会释放
     @param pWorld 引擎实例
     @param memory_limit 总内存大小限制（字节）
     @param cache_num  非当前显示的缓存对象个数限制
    */
    void MapSetLandmarkMemoryLimitParam(void *pWorld, int memory_limit, int cache_num);

    /**设置天空盒子建模参数，因为纹理包围时会repeat，heightRation * 宽高比/4 必须能被1整除，否则会造成接缝的地方不连续
     * @param pWorld 场景句柄
     * @param height_ration  高度所占半圆的比例, 范围是[0,1]， 默认值0.5，超出范围设置不生效
     * @param gradient_ration  渐变部分占图片的高度比例，范围是[0,1]，默认值0.08，超出范围设置不生效
     */
    void MapSetSkyBoxModelParam(void *pWorld, float height_ration, float gradient_ration);

    typedef void (*MapCallback_StyleLoad)(int skin_id, int style_id, const char* file_name, int error, void *context);
    void MapSetMapSkinWithCallback(void *pWorld, int skin_id, bool reuse, MapCallback_StyleLoad callback, void* context);


    struct ParseStyleResult{
        bool success;
        const char* result;
        const char* sID;
        const char* md5;
    };
    typedef void (*MapCallback_StyleParse)(struct ParseStyleResult parseRst);

    /**将p20为单位的三维坐标转化为经度-纬度-米为单位的坐标
     * @param p20  x-y-z均为p20的三维坐标
     * @return 经度-纬度-米三维坐标
     */
    MapVector3d MapCoordFromP20ToLngLatMeter(const MapVector3d *p20);

    /*
    * 获取当前屏幕内scaleLevel和视野Rect
    */
    void MapGetCurrentScaleAndRect(void *pWorld, float *scaleLevel, MapRectD *geoRect);

    /**
    * 获取文字的可显示区域
    * @param pWorld
    * @param scaleLevel
    * @param geoRect
    */
    void MapGetAnnotationRect(void *pWorld, float *scaleLevel, MapRectD *geoRect);


/**
     * 设置地图加载4K地图模式
     * @param pWorld 场景句柄
     * @param enable 0-默认，按照样式配置走 1-强制打开(有4K数据区域) 2-强制关闭
     */
    void MapSet4KMapLoadMode(void *pWorld, int enable);

 
    /**添加3D粒子动效
     * @param pWorld 场景句柄
     * @param geo   粒子目标的中心点，经纬度
     * @param repeatTime 重复次数
     * @param autoRemove 播放完自动移除
     * @param frameNum 动画帧数
     */
    int MapAddParticleEffect(void *pWorld, const char *pathBuf, MapVector3d geo, int repeatTime,bool autoRemove, int frameNum);

    /**添加底图2D粒子动画效果
     * @param pWorld 场景句柄
     * @param pathBuf 动画文件路径
     * @param yOffset 相对屏幕中心的偏移量(0为屏幕中心，范围[-1,1],-1粒子原点屏幕最下方, +1粒子原点屏幕最上方)
     * @param repeatTime 重复次数
     * @param autoRemove 动画重复次数播放完之后是否会自动删除，true:自动移除，false: 不自动移除
     * @param frameNum 动画帧数,由制作的动画文件决定
    */
    int MapAdd2DParticleEffect(void *pWorld, const char *pathBuf, float yOffset, int repeatTime,bool autoRemove, int frameNum);

    /**实时设置粒子效果的位置和旋转角
     * @param pWorld 场景句柄
     * @param geo   粒子目标的中心点，格式：经纬度
     * @param rotate 粒子的旋转角，单位：角度
     */
    void MapSetParticlePosAndRotate(void *pWorld, int particleID, MapVector3d geo, float rotate);

    void MapPlayParticleEffect(void *pWorld, int id);

    void MapRemoveParticleEffect(void *pWorld, int id);

    /**使用一个虚拟的相机计算geo地理坐标对应的屏幕坐标，使用pWorld现有的screenoffset和viewport
     * @param pWorld 场景句柄
     * @param center 目标相机的中心点
     * @param scale 目标相机缩放级别
     * @param rotate 目标相机的旋转角
     * @param skew 目标相机的倾斜角
     * @param geo 要转化的地理坐标，类型p20
     * @return 屏幕坐标（像素），落在屏幕外时返回（-1，-1）
     */
    MapVector2f MapGetScreenPointWithVirtualCamera(void *pWorld,  MapVector2d center, double scale, float rotate, float skew, MapVector3d geo);

    /**使用一个虚拟的相机计算geo地理坐标对应的屏幕坐标，使用传入的screenoffset
     * @param pWorld 场景句柄
     * @param center 目标相机的中心点
     * @param scale 目标相机缩放级别
     * @param rotate 目标相机的旋转角
     * @param skew 目标相机的倾斜角
     * @param screenOffset 目标相机的偏移量
     * @param geo 要转化的地理坐标，类型p20
     * @return 屏幕坐标（像素），落在屏幕外时返回（-1，-1）
     */
    MapVector2f MapGetScreenPointWithScreenOffsetVirtualCamera(void *pWorld, MapVector2d center, double scale, float rotate, float skew, MapVector2f screenOffset, MapVector3d geo);

    /**使用一个虚拟的相机计算屏幕坐标对应的geo地理坐标
     * @param center 目标相机的中心点
     * @param scale 目标相机缩放比例
     * @param rotate 目标相机的旋转角
     * @param skew 目标相机的倾斜角
     * @param screenOffset 目标相机的偏移量
     * @param screenPos 要转化的屏幕坐标
     * @return 地理坐标
     */
    MapVector2d MapGetGeographPointWithVirtualCamera(void *pWorld, MapVector2d center, double scale, float rotate, float skew,
                                                MapVector2f screenOffset, MapVector2f screenPos);
 
 
    /**楼块墙体法线窗格效果的开关，默认开启，必须在引擎启动时调用，无法更新已存在的缓存
     * @param enable  true开启，false关闭
     */
    void MapSetBuildingExtEffectEnable(void *pWorld, bool enable);

 
    /** 建筑物交互是否静态显示
     * @param bStatic  true开启，false关闭
     */
    void MapSetBuildingLightStatic(void *pWorld, bool bStatic);

    /**设置自定义图层为依附于底图Poi的rich信息图层，必须是通过MapAddCustomTileLayer已创建的图层，同时只能有一个生效，重复设置会覆盖之前的rich图层
     * @param pWorld 场景句柄
     * @param layerID 图层ID
     */
    void MapSetCustomTileLayerAttachPoi(void *pWorld, int layerID);

    /**获取相机的位置和角度
     * @param pWorld 场景句柄
     * @param eye 相机位置
     * @param center 相机看向的位置，等同于地图中心点
     * @param up 相机头向上的矢量
     */
    void MapGetCameraPosture(void *pWorld, MapVector3d *eye, MapVector3d *center, MapVector3d *up);
 
    /**设置相机的位置姿态参数
     * @param pWorld 场景句柄
     * @param eye 相机三维位置，P20坐标
     * @param center 相机看向的三维位置，等同于地图中心点，P20坐标
     * @param up 相机头向上的矢量(在skew不为0时设置0,0,1即可，为0时需要设置真实值)
     */
    void MapSetCameraPosture(void *pWorld, MapVector3d eye, MapVector3d center, MapVector3d up);
    
    /**获取文字的显示时的整体显示框的大小，以及锚点在框中的位置
     * @param pWorld 场景句柄
     * @param poiid     相机位置
     * @param box_size  poi的整体显示框的大小
     * @param anchor_offset     锚点在box中的坐标，左上角为原点
     */
    void MapGetPoiDisplayBox(void *pWorld, const char* poiid, MapVector2f *box_size, MapVector2f *anchor_offset);
    /**
     * 全局阴影开关(旧)
     * enale true:开启 false:关闭
     * @deprecated
     */
    void MapEnableShadow(void *pWorld, bool enable);

    /**
     * 全局阴影显示形态设置
     * @param pWorld 实例句柄
     * @param setting 阴影的显示设置，参看ShadowSetting枚举
     */
    void MapSetShadowSetting(void *pWorld, ShadowSetting setting);

    /**
     * 全局阴影显示起始比例尺
     */
    void MapSetShadowStartScaleLevel(void *pWorld, int scale_level);


    /**
     * 建筑物构件显示控制开关
     * @param pWorld 场景句柄
     * @param building_unit_type  建筑物构件类型
     * @param bVisible  是否显示建筑物构件
     */
    void MapSetBuildingUnitVisible(void *pWorld, BuildingUnitType building_unit_type, bool bVisible);

    /**
     * 调整相机远裁剪的范围，可以裁剪更少的瓦块，默认关闭，手动关闭需要设置小于0的数值
     * @param pWorld 场景句柄
     * @param ratio  机远裁剪调整的比例（0-1），建议使用1.f，更小的值可以加载更少的瓦片，但是远处天空附近可能空白
     */
    void MapSetOptimizedZfarRatio(void *pWorld, float ratio);

    /**
     * 设置百变图层是否裁剪远处的瓦片，优化性能
     * @param pWorld 场景句柄
     * @param layer_id  图层ID
     * @param lod_cull_type  远处的瓦块lod裁剪类型，-1不使用，0使用默认裁剪参数，其他使用SetLodParam自定义的lod类型
     */
    void MapSetCustomTileUseLodCull(void *pWorld, int layer_id, int lod_cull_type);
 
 
    /**
    * 设置自定义矢量图层的线、面样式属性由info_layer_id代表的图层中的扩展数据获取
    * @param pWorld 引擎实例
    * @param layer_id 显示的图层，从此图层中获取形点属性
    * @param info_layer_id 绑定的图层，从此图层中获取样式属性
    */
    void MapSetCustomTileLayerAttachInfo(void *pWorld, int layer_id, int info_layer_id);
 
    /**设置广告牌id list
     * @param pWorld 场景句柄
     * @param info 广告牌IDlist
     * @param count 广告牌个数
     */
    void MapSetBillBoardInfoList(void *pWorld, const BillboardInfo *info, int count);
 
    /**
    * 引擎允许使用压缩纹理。引擎内部会判断是否支持压缩纹理，也可以通过此开关外部云控。开关默认关闭。
    * @param pWorld 引擎实例
    * @param enable 允许使用，引擎内部也会判断硬件是否支持。只有外部和内部同时打开，才会启用压缩纹理功能。
    */
    void MapEnableCompressedTexture(void *pWorld, bool enable);

    /**
    * 机型是否支持压缩纹理。根据opengl判断是否支持压缩纹理。必须在引擎启动且绘制完成一次之后调用
    * @param pWorld 引擎实例
    */
    bool MapGetIsSupportCompressedTexture(void *pWorld);

    /**
    * 引擎3D元素恒大的方式。3d元素恒大效果的放大系数两种计算方式：与地图中心点距离和相机自身高度
    * @param pWorld 引擎实例
    * @param relative_center  true ：系数与地图中心点距离成反比;  false ：系数与相机高度成反比
    */
    void MapSetModelConstantDisplayMode(void *pWorld, bool relative_center);

    /**
     * 动态的重绘控制，动画时不进行底图重绘, 默认为false
     * @param pWorld 引擎实例
     * @param use_dynamic  true:使用动态重绘优化  false:不使用，刷帧时全部重绘
     */
    void MapUseDynamicRedrawControl(void *pWorld, bool use_dynamic);
#pragma mark 动态天空盒
    /**
    配置动态天空盒素材路径
    @param pWorld 引擎实例
    @param assetPath 天空盒素材路径
    */
    void MapSetSkyboxProAssetPath(void *pWorld, const char* assetPath);

    /**
    配置动态天空盒参数
    @param pWorld 引擎实例
    @param key runtime可修改天空盒/天气参数名
    @param value 对应参数值
    */
    void MapSetSkyboxProParam(void *pWorld, const char* key, float value);

    /**
    配置天空盒AB参数。0 - 动态天空盒mSkyboxPro; 1 - 老天空盒mSkyBox
    @param pWorld 引擎实例
    @param ab 天空盒AB参数. true - old skybox; false - new skybox. 引擎内部默认为false
    */
    void MapSetSkyboxAB(void *pWorld, bool ab);

    /**
    配置风格化素材路径
    @param pWorld 引擎实例
    @param assetPath 风格化素材路径。设置空字符串表示卸载当前风格素材
    */
    void MapSetStylizationAssetPath(void *pWorld, const char* assetPath);

    /**
    配置风格化参数
    @param pWorld 引擎实例
    @param key runtime可修改风格化参数名
    @param value 对应参数值
    */
    void MapSetStylizationParam(void *pWorld, const char* key, float value);
#ifndef NOT_USE_SKY_BOX_PROCEDURAL
    /**
    夜晚出现/消失的回调。我们认为月亮出现代表动态天空盒到了晚上，月亮消失代表天空盒来到了白天
    @param nightShow  true: 切换到夜晚；false: 切换到白天
    @param context 回调上下文
    */
    typedef void (*MapCallback_NightShow)(bool nightShow, void *context);

    /**
     设置月亮出现/消失的回调
     @param pWorld 场景句柄
     @param moonShowCallback 回调函数
     @param context 回调句柄
     */
    void GLMapSetNightShowCallback(void *pWorld, MapCallback_NightShow moonShowCallback, void *context);
#endif
    /**
    * 设置当前视角跟踪动态天空盒的太阳/月亮. 目前仅展示效果用到, 只在debug编译时生效
    @param pWorld 引擎实例
    @param bFollow true 跟踪，false 不跟踪
    */
    void MapSetFollowSkyMode(void *pWorld, bool bFollow);

    /** 根据经纬度和时间返回当天的日夜时间信息。
    切换场景/城市时调用，同一场景预期调用一次，禁止高频调用。
    注意，此接口与动态天空盒解耦，完全独立的一个功能（因为业务方会根据此作为切换动态/静态天空盒的一个变量）。
    @param pWorld 场景句柄
    @param lat 纬度
    @param lon 经度
    @param year 年
    @param month 月
    @param day 日
    @param timeZone 时区。例如北京时间传入+8, 格林尼治标准时间传入0。为世界地图准备，如下文字有详细说明。
    @return 日夜时间。所有时间都是timeZone时区。我们当前一般传入+8即可
    */
    DayInfo MapGetDayInfo(void *pWorld, float lat, float lon, int year, int month, int day, int timeZone);
    /**
    * 设置SD路面箭头的显示方式。默认屏幕朝向
    * @param pWorld 引擎实例
    * @param type 0:屏幕朝向；1:随地面倒伏。
    */
    void MapSetSdRoadArrowDisplayType(void *pWorld, int type);
    /**
     * 路名被3D物体遮挡时的半透设置
     * @param pWorld 引擎实例句柄
     * @param enable 路名半透开关
     * @param alpha 遮挡路名的透明度
     */
    void MapEnableRoadNameTranslucent(void *pWorld, bool enable, float alpha);

    /**
     * 路名半透的alpha值
     * @param pWorld 引擎实例句柄
     * @param alpha 路名透明度
     */
    void MapSetRoadNameAlpha(void* pWorld, float alpha);

    /** 设置引导信息透出楼块
    * @param pWorld 场景句柄
    * @param bEnable 开启/关闭
    */
    void MapSetGuideAroundModelAlphaEnable(void *pWorld, bool bEnable);

    /**
     * 设置开启/关闭标注子类型功能
     * @param pWorld 场景句柄
     * @param sub_content_type   标注类型 BaseMap-0, 基础底图标注 IndoorMap-1室内图标注 Aoi3D-2 3D Aoi RoadName-3 /道路名称
     * @param bEnable  开启/关闭标注子类型功能
     */
    void MapSetAnnotationSubContentEnable(void *pWorld, int sub_content_type, bool bEnable);

    /**
     * 组件类型控制开关
     * @param pWorld world对象
     * @param content_type 组件类型
     * @param enable 是否开启
     */
    void MapSetMacro4KContentEnable(void *pWorld, Macro4KContentType content_type, bool enable);

    /**
     * 上报开关
     * @param pWorld world对象
     * @param model_main_type 模型主类型
     * @param enable 是否开启
     */
    void MapSetModelComponentReportEnable(void *pWorld, int model_main_type, bool enable);

    /**
     * 设置导航道路需要的道路相关半透值
     * @param pWorld 引擎实例
     * @param gradual_road_elem_alpha : 渐变道路元素的透明度
     * @param gradual_road_fence_alpha: 渐变道路护栏的透明度
     * @param high_transparent_road_alpha : 高透道路透明度
     * @param high_transparent_road_elem_alpha : 高透道路元素透明度
     * @param high_transparent_road_fence_alpha : 高透道路护栏的的透明度
     */
    void MapSetLaneTransparentParam(void *pWorld, float gradual_road_elem_alpha, float gradual_road_fence_alpha, float high_transparent_road_alpha, float high_transparent_road_elem_alpha, float high_transparent_road_fence_alpha);

    /**
     * 设置鱼骨数据
     * 备注:该ID体系为LaneGroup的TpID
     * @param lane_group_infos : 鱼骨包含的lanegroupid数组
     * @param lane_group_size : lanegroupid数组size
     * @param lane_group_fish_bone : 鱼骨包含的graduate数组，与lane_group_infos数组对应
     * @param lane_group_fish_bone_size: lane_group_fish_bone数组size
     * @param fish_bone_ext : 鱼骨包含的zlevel、animation等数组，与lane_group_infos数组对应
     * @param fish_bone_ext_size： fish_bone_ext数组size
     * @param cross_ids： 路口id数组
     * @param cross_size： 路口id数组size
     * @param cross_fish_bone: 路口包含的graduate数组，与cross_ids数组对应
     * @param cross_fish_bone_size: cross_fish_bone数组size
     * @param cross_fish_bone_ext: 路口包含的zlevel、animation等数组，与cross_ids数组对应
     * @param cross_fish_bone_ext_size：cross_fish_bone_ext数组size
     * 
     */
     void MapSetLaneFishBoneData(void *pWorld, int64_t *lane_group_infos, int lane_group_size, float* lane_group_fish_bone, int lane_group_fish_bone_size,
                            int* fish_bone_ext, int fish_bone_ext_size, int64_t *cross_ids, int cross_size, float* cross_fish_bone, int cross_fish_bone_size,
                            int* cross_fish_bone_ext, int cross_fish_bone_ext_size);

    /**
   设置引导面/伴随线是否避让poi
   @param pWorld 引擎实例
   @param enable 业务控制引导面/伴随线是否避让Poi
   */
   void MapSetGuideAvoidPoiEnable(void *pWorld, bool enable);

   /**
   设置引导面是否避让楼块
   @param pWorld 引擎实例
   @param enable 业务控制引导面是否避让楼块
    */
    void MapSetGuideAvoidBuildingEnable(void *pWorld, bool enable);

    /**
     * poi文字朝向自动适配
     * @param pWorld 引擎实例句柄
     * @param adapt_normal  poi文字是否沿法线方向排布
     */
    void MapSetPoiDirectionAdaptNormal(void *pWorld, bool adapt_normal);

    /**
     * 设置请求数据规则
     * @param pWorld 引擎实例
     * @param maskType : 设置数据类型
     *     0:DataFeatureType_kLane：道路数据
     *     1:DataFeatureType_kTemplateModel：通模（数）
     *     2:DataFeatureType_kBuilding：建筑
     *     3:DataFeatureType_kLanduse： 农田
     * @param featureVec 数组 : 具体规则，由产品定义：https://git.woa.com/ide/data-engine/nds-scheme/merge_requests/304?ADTAG=mr
     * @param count : featureVec的长度
     */
    void MapSetDataFeatureSet(void *pWorld, int maskType, uint64_t* featureVec, int count);


    /**
     * 设置场景
     * @param pWorld  引擎实例
     * @param scene  场景枚举，见mapbase::HAD::TransparentScene
     */
    void MapSetLaneTransparentScene(void *pWorld, int scene);


     /**
      * 设置当前车道上的隧道场景
      * @param pWorld  引擎实例
      * @param scene  场景枚举，见mapbase::HAD::GuideLaneScene
      * @param animationDuration  动画效果时长，单位为秒
      */
     void MapSetGuideLaneTunnelScene(void *pWorld, int scene, float animationDuration);

    /**
     * 更新引导面伴随面经过的lanegroupid
     * 备注:该ID体系为LaneGroup的TpID
     */
     void MapUpdatePassedLaneGroupID(void *pWorld, const char *route_key, int64_t *ids, int size);
     
    /**
    * 切换路线
    * @param pWorld
    * @param route_key
    */
    void MapSwitchRoute(void *pWorld, const char *route_key);

#ifndef NOT_USE_MAP_MODEL
    /**
     * 设置树的运营区域，校验失败时树模型保持现状
     * @param pWorld  引擎实例
     * @param operation_json  运营树配置，内部相同ID的会覆盖上次的设置。天气树id固定为0，无需传模型ID
     */
    void MapSetTreeOperationRegion(void *pWorld, const char *operation_json);
    /**
     * 根据id删除部分树的运营区域
     * @param pWorld  引擎实例
     * @param region_id  运营区域id列表
     * @param region_id_count  运营区域id的个数
     */
    void MapRemoveTreeOperationRegion(void *pWorld, int* region_id, int region_id_count);
#endif
MAP_ENUM_WITH_NAMES(MapEnableFeature,
    //是否开启全局阴影，默认关闭
    kMapEnableShadow,

    //是否开启手绘图，默认关闭
    kMapEnableHandDrawMap,

    kMapEnablePoiRich, // 地铁POI rich

    //是否开启poi 3D遮蔽
    kMapEnablePoi3dOcclusion,
    kMapEnableBuildingPoiRich, // 楼层POI rich
    kMapEnableAppPOIRich, // 小程序的rich
    kMapEnablePoiCorner, // poi角标
    //路况开关,默认关闭
    kMapEnableTraffic,
    //封路开关，默认关闭
    kMapEnableRoadClosure,
    //卫星图开关，默认关闭
    kMapEnableSatellite,
    //三维真地形图开关， 默认关闭
    kMapEnableDTME,
    //室内图开关, 默认开启，总开关
    kMapEnableIndoorBuilding,
    //室内图绘制开关，默认开启，只控制绘制，不控制裁剪和加载
    kMapEnableIndoorBuildingDraw,
    //地形图开关， 默认关闭
    kMapEnableDEM,
    //是否开启debug瓦片信息
    kShowDebugTileOnMap,
    /**文字能否点击*/
    AnnotationClickTextEnabled,
     /**街景图层开关*/
    StreetViewEnable,
    //3D球
    kGlobe,
    //默认树
    kDefaultTree,
    //L4
    kLandMark,
    //3D poi
    kMapAnnotationPoi3D,
    //AOI立牌
    kMapAnnotationAoi3D,
   /**
    * 强制使用外部设置的默认地面颜色
    * 默认值false
    * */
    kForceUseExternalGroundColor,
    kMapEnableOverlayCollisionDetailLog,
    //启用以密度作为计算体系的新相机
    kMapEnableDensityCamera,
                    
    //启用新的立牌样式
    kMapEnableNewStandplate,
               
                    
    /**
        风格化配置相关
     */
    kMapStylizationHslOst,
    kMapStylizationColor,
                    
    //私有枚举，放在最后定义
    //是否显示MapView，此枚举值为私有值，只被Android SDK调用
    kMapEnableMapViewShow_Private,
    kMapEnableCount
)

MAP_ENUM_WITH_NAMES(MapIntProperty,
    //全局阴影开始的比例尺级别，默认16级
    kMapIntPropertyShadowStartScaleLevel,
    //继续添加。。。
    kMapIntPropertyXXX,
    kMapIntPropertyRoadNameAnimDuration,//蚯蚓线路名动画时长，默认为150ms
    kMapIntPropertyPerformanceLevel,
    kMapIntPropertyPoi3dOcclusionAlphaStartScaleLevel, //poi 3d遮蔽半透起始比例尺
    kMapIntPropertyLandmarkLodLevel, // L4LOD级别
    kMapIntGeoShapeType,//选择几何形体，包括圆台等
    kGroundDefaultColor,
    
    //私有枚举，放在最后定义
    //私有方法，SurfaceChange
    kMapPrivate_SurfaceChange,
    kMapIntPropertyCount
)

MAP_ENUM_WITH_NAMES(MapFloatProperty,
    kMapFloatPropertyXXX,
    kMapFloatPropertyPickL1Transparency, // 设置 L1建筑物 点选被遮蔽后的透明度值
    kMapFloatPropertyPickL4Transparency,  // 设置 L4 点选被遮蔽后的透明度值
    kMapFloatPropertyPoi3dOcclusionAlpha, // poi 3d遮蔽半透透明度
    kMapFloatPropertyCompassGroupIconsMinDepthScale,//在进行根据屏幕深度近大远小设置时，车标东南西北 需要设置 最小的sclae比例（相对于原始大小）
    kMapFloatPropertyCount
)

MAP_ENUM_WITH_NAMES(MapDoubleProperty,
    kMapDoublePropertyXXX,
    //继续添加。。。
    
    kMapDoublePropertyCount
)

MAP_ENUM_WITH_NAMES(MapStringProperty,
    kMapStringPropertyXXX,
    kMapStringPropertyWorldStatus,
    
    kMapStringPropertyCount
)

MAP_ENUM_WITH_NAMES(MapIntArrayProperty,
    kMapIntArrayPropertyXXX,
    //继续添加。。。
    
    kMapIntArrayPropertyCount
)

MAP_ENUM_WITH_NAMES(MapFloatArrayProperty,
    kMapFloatArrayPropertyXXX,
    //继续添加。。。
    
    kMapFloatArrayPropertyCount
)

MAP_ENUM_WITH_NAMES(MapDoubleArrayProperty,
    kMapDoubleArrayPropertyXXX,
    //继续添加。。。
    
    kMapDoubleArrayPropertyCount
)

MAP_ENUM_WITH_NAMES(MapStringArrayProperty,
    kMapStringArrayPropertyXXX,
    //继续添加。。。
    
    kMapStringArrayPropertyCount
)

void MapSetEnable(void* pWorld, MapEnableFeature feature, bool enable);
bool MapGetEnable(void* pWorld, MapEnableFeature feature);

void MapSetIntValue(void* pWorld, MapIntProperty property, int value);
void MapSetFloatValue(void* pWorld, MapFloatProperty property, float value);
void MapSetDoubleValue(void* pWorld, MapDoubleProperty property, double value);
void MapSetStringValue(void* pWorld, MapStringProperty property, const char * value);

int  MapGetIntValue(void* pWorld, MapIntProperty property);
float MapGetFloatValue(void* pWorld, MapFloatProperty property);
double MapGetDoubleValue(void* pWorld, MapDoubleProperty property);
const char * MapGetStringValue(void* pWorld, MapStringProperty property);


/**
 * 如果values 为NULL，或者count <= 0，表示清空此属性内部array值。
 */
void MapSetIntArrayValue(void* pWorld, MapIntArrayProperty property, int *values, int count);
void MapSetFloatArrayValue(void* pWorld, MapFloatArrayProperty property, float *values, int count);
void MapSetDoubleArrayValue(void* pWorld, MapDoubleArrayProperty property, double *values, int count);
void MapSetStringArrayValue(void* pWorld, MapStringArrayProperty property, const char ** values, int count);

typedef void (*MapCallback_ResourceParse)(bool success, const char *result);

/**
 * 主动解压icon图标文件
 * @param cfg_dir       解压到的目标路径
 * @param resource_dir  incr.dat文件路径
 * @param incr_file     incr.dat文件的当前文件名（服务请求下来的名称应该带有后缀）
 */
void MapParseIconRes(const char* cfg_dir, const char* resource_dir, const char* incr_file);

/**
 * 主动解压icon图标文件
 * @param cfg_dir       解压到的目标路径
 * @param resource_dir  incr.dat文件路径
 * @param incr_file     incr.dat文件的当前文件名（服务请求下来的名称应该带有后缀）
 * @param check_result_exist  检查解压产物是否存在，如果存在，则不解压
 * @param async  是否异步解压
 * @param callback 解压完成后回调, 入参为解压是否成功及原因
 */
void MapParseIconPkg(const char* cfg_dir, const char* resource_dir, const char* incr_file, bool check_result_exist, bool async, MapCallback_ResourceParse callback);

/**
 * 主动解压xz样式配置文件
 * @param cfg_dir       解压到的目标路径
 * @param resource_dir  .xz文件路径
 * @param xz_file      .xz文件的当前文件名
 * @param check_result_exist  检查解压产物是否存在，如果存在，则不解压
 * @param async  是否异步解压
 * @param callback 解压完成后回调, 入参为解压是否成功及原因
 */
void MapParseXZPkg(const char* cfg_dir, const char* resource_dir, const char* xz_file, bool check_result_exist, bool async, MapCallback_ResourceParse callback);

/**
 * 配置后处理效果参数
 * @param pWorld 引擎实例
 * @param key runtime可修改后处理参数名
 * @param value 对应参数值
 * 放大镜后效参数：
 * zoomer:enable  打开放大镜开关。1.0打开，0.0关闭
 * zoomer:circleScale 放大镜半径大小系数。与原始半径大小作乘法，0.5 - 5.0
 * zoomer:zoomScale  放大镜放大倍数大小，1.0 - 3.0
 * zoomer:borderScale  放大镜边框白边宽度缩放系数。与原始白边宽度作乘法，1.0 - 10.0
 * zoomer:posOffsetScale 放大镜沿两指连线的法向量方向的偏移程度，-2.0 - 2.0。0.0代表两指中心点即无偏移
 * zoomer:position.x	 放大镜x轴位置。左下角为（0，0），画布宽高为1的坐标系下位置。手图不需要设置
 * zoomer:position.y	 放大镜y轴位置。同上。同上
 */
void MapSetPostProcessParam(void* pWorld, const char* key, float value);

/**
 * 文字滤镜设置
 * @param pWorld 引擎实例，可以重复设置，同类型后设置的覆盖前设置的
 * @param text_type  文字的类型，类型字串要和样式配置对齐，目前有三种PointTextSymbolizer、LineTextSymbolizer、LableIconSymbolizer
 * @param type_num 类型的个数
 * @param s_percent hsv空间中s值的调整大小（范围-1 - 1，内部会clamp到0-1），数值越大越鲜艳，最小时为灰色
 * @param v_percent hsv空间中v值的调整大小（范围-1 - 1，内部会clamp到0-1），数值越大越亮，最小时为黑色
 */
void MapSetPoiLightness(void* pWorld, char** text_type, int type_num, float s_percent, float v_percent);
/**
 * 获取Metal上下文
 */
void MapGetMetalContext(void *pWorld, void** commandEncoder);

/**设置引擎中文字、marker随级别大小变化参数, level数组的长度和scale的长度必须一致
* @param level   级别数组，level为null时清空当前的参数设置
* @param scale   scale参数数组，level级别范围之内的级别插值计算，级别范围之外的都是1（范围边界处会有过渡）
* @param param_num   数组个数
*/
void MapSetAnnoMarkerScaleByLevel(void *pWorld, int *level, float *scale, int param_num);

/**
 * 获取某点的地理距离（米）到屏幕dp
 * @param scale 相机缩放比例
 * @param meter 地理距离 单位 米
 * @param location 地理经纬度
 * @return 某点的地理距离（米）到屏幕dp
 * @note 返回0.0代表传入非法值，计算无效
 */
double MapMetersToPoints(void *pWorld, double scale, double meters, MapVector2d location);

#ifdef __cplusplus
}
#endif

#endif // GLMapLib_Base_h
