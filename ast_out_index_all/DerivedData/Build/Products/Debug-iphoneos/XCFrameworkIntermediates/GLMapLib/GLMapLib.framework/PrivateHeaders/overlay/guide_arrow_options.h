//
// Created by cuipanpan on 2021/11/23.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "../base/map_define.h"
#include "overlay.h"

namespace MAPAPI {

class MapImpl;

enum ARROW_TYPE {
    ARROW_LEFT,
    ARROW_MID,
    ARROW_RIGHT,
};

class TXMAPSDK_EXPORT GuideArrowOptions : public OverlayOptions {
public:
     GuideArrowOptions();
     GuideArrowOptions(const GuideArrowOptions& options);
     GuideArrowOptions& operator=(const GuideArrowOptions & other);
     virtual ~GuideArrowOptions();

     virtual OverlayType GetType() const override;
     /**
      * 设置引导面中线数据（不回影响引导面绘制，影响的是道路中心引导箭头的位置）
      * mid_points
      */
     void SetMidPoints(const std::vector<MapVector3d>& mid_points);

     std::vector<MapVector3d>& GetMidPoints();
     const std::vector<MapVector3d>& GetMidPoints() const;

     /**
      * 设置引导箭头宽度
      * @param width 20级像素单位，1像素约等于0.15米
      */
     void SetArrowWidth(float width);
     float GetArrowWidth() const;

     /**
      * 设置箭头流动的速度,每秒移动多少个完整箭头
      * @param speed 速度
      */
     void SetSpeed(float speed);
     float GetSpeed() const;

     void SetArrowType(ARROW_TYPE type);
     ARROW_TYPE GetArrowType() const;

     /**
      * 设置箭头纹理名称
      * @param arrow_name 纹理名
      */
     void SetArrowName(const std::string& arrow_name);
     const std::string& GetArrowName() const;
     /**
      * 设置透明度纹理名称
      * @param alpha_name 纹理名
      */
     void SetAlphaName(const std::string& alpha_name);
     const std::string& GetAlphaName() const;

public:
     void CoordinateTransform(MapImpl * mapImpl);
    
private:
    struct Impl;
    std::shared_ptr<GuideArrowOptions::Impl> GetImpl();
    std::shared_ptr<GuideArrowOptions::Impl> GetImpl() const;
};

}
#endif