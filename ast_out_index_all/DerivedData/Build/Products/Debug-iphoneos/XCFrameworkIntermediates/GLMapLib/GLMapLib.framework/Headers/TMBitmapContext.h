//
//  TMBitmapContext.hpp
//  GLMapLib2.0
//
//  Created by nopwang on 17/3/29.
//  Copyright © 2017年 nopwang. All rights reserved.
//

#ifndef TMBitmapContext_hpp
#define TMBitmapContext_hpp

#include "MapDefine.h"

#if !defined(TM_INLINE)
#  if defined(__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
#    define TM_INLINE static inline
#  elif defined(__cplusplus)
#    define TM_INLINE static inline
#  elif defined(__GNUC__)
#    define TM_INLINE static __inline__
#  elif defined(_MSC_VER)
#    define TM_INLINE static __inline
#  else
#    define TM_INLINE static
#  endif
#endif

#define TM_MIN(a, b) ((a) < (b) ? (a) : (b))
#define TM_MAX(a, b) ((a) > (b) ? (a) : (b))

typedef unsigned char TMColorAlpha;
typedef unsigned short TMColor565;
typedef unsigned int TMColor;

TM_INLINE TMColor565 TMColor565Make(unsigned char r, unsigned char g, unsigned char b) {
  return (TMColor565)(((r << 8) & 0xF800) | ((g << 3) & 0x7E0) | ((b >> 3) & 0x1F));
}

TM_INLINE TMColor565 TMColor565ForColorRGBA(TMColor color) {
  return (TMColor565)(((color << 8) & 0xF800) | ((color >> 5) & 0x7E0) | ((color >> 19) & 0x1F));
}

TM_INLINE TMColor TMColorRGBAForColor565(TMColor565 color565) {
  TMColor color = color565;
  return ((color & 0xF800) >> 8) | ((color & 0x7E0) << 5) | ((color & 0x1F) << 19) | 0xFF000000;
}

TM_INLINE TMColor TMColorMake(unsigned char r, unsigned char g, unsigned char b, unsigned char a) {
  return (((r) << 0) | ((g) << 8) | ((b) << 16) | ((unsigned int)(a) << 24));
}
TM_INLINE TMColor TMColorFromTXRGBColor(TMColor color,float alpha) {
    unsigned char b = ((unsigned char)(((color) >> 0) & 0xFF));
    unsigned char g = ((unsigned char)(((color) >> 8) & 0xFF));
    unsigned char r = ((unsigned char)(((color) >> 16) & 0xFF));
    return TMColorMake(r, g, b, alpha);
}
TM_INLINE unsigned char TMColorGetR(TMColor color) {
  return ((unsigned char)(((color) >> 0) & 0xFF));
}

TM_INLINE unsigned char TMColorGetG(TMColor color) {
  return ((unsigned char)(((color) >> 8) & 0xFF));
}

TM_INLINE unsigned char TMColorGetB(TMColor color) {
  return ((unsigned char)(((color) >> 16) & 0xFF));
}

TM_INLINE unsigned char TMColorGetA(TMColor color) {
  return ((unsigned char)(((color) >> 24) & 0xFF));
}

typedef int TM_BOOL;
#define TM_YES (1)
#define TM_NO (0)

typedef unsigned char TM_UINT8;

typedef struct TXMAPSDK_EXPORT TMPoint {
  int x;
  int y;
} TMPoint;

typedef struct TXMAPSDK_EXPORT TMSize {
  int cx;
  int cy;
} TMSize;

typedef struct TXMAPSDK_EXPORT GlyphMetrics {
    int width;
    int height;
    int above;
    int under;
} GlyphMetrics;

TM_INLINE GlyphMetrics GlyphMetricsMake(int width, int height, int above, int under){
    GlyphMetrics metrics = {width, height, above, under};
    return metrics;
}

typedef struct TXMAPSDK_EXPORT TMRect {
  int left;
  int top;
  int right;
  int bottom;
} TMRect;

TM_INLINE TMPoint TMPointMake(int x, int y) {
  TMPoint point = {x, y};
  return point;
}

TM_INLINE TMSize TMSizeMake(int cx, int cy) {
  TMSize size = {cx, cy};
  return size;
}

TM_INLINE TMRect TMRectMake(int left, int top, int right, int bottom) {
  TMRect rect = {left, top, right, bottom};
  return rect;
}

TM_INLINE int TMRectIntersectsRect(TMRect rect1, TMRect rect2) {
  return !(rect1.left > rect2.right || rect1.right < rect2.left || rect1.top > rect2.bottom ||
           rect1.bottom < rect2.top);
}

TM_INLINE int TMRectContainsRect(TMRect rect1, TMRect rect2) {
  return (rect1.left <= rect2.left && rect1.right >= rect2.right && rect1.top <= rect2.top &&
          rect1.bottom >= rect2.bottom);
}

typedef enum TXMAPSDK_EXPORT TMBitmapFormat {
  TMBITMAP_FORMAT_RGBA = 0,
  TMBITMAP_FORMAT_RGB565,
  TMBITMAP_FORMAT_ALPHA,
  TMBITMAP_FORMAT_RGB888,
  TMBITMAP_FORMAT_KTX, //压缩纹理的格式
  TMBITMAP_FORMAT_MAX = 0xFFFFFFFF
} TMBitmapFormat;

typedef struct TXMAPSDK_EXPORT TMBitmapContext {
    volatile int nRetainCount;
    TMBitmapFormat format;

    int width;
    int height;

    int stride;
    float scale;

 
    TM_BOOL ownBuffer;
    void *buffer;

    int buffer_size; //本期用来存储压缩纹理的大小，非压缩纹理是0。以后可以扩展到普通格式的图片
    TM_BOOL src_blend; //使用BlendMode_Src_OneMinusSrc(非预乘alpha)，压缩纹理为true，目前使用的大部分是预乘
    TM_UINT8 *rows[1];
 
} TMBitmapContext;

typedef struct TMBitmapContext TMBitmap;

#ifdef __cplusplus
extern "C" {
#endif

TXMAPSDK_EXPORT TMBitmapContext *TMBitmapContextCreate(void *buffer, TMBitmapFormat format, int width, int height,
                                       int stride, float scale, TM_BOOL ownBuffer);

TXMAPSDK_EXPORT TMBitmapContext *TMCompressTextureCreate(void *buffer, int buffer_len, float scale);

TXMAPSDK_EXPORT TMBitmapContext *TMBitmapContextRetain(TMBitmapContext *pBitmapContext);

TXMAPSDK_EXPORT void TMBitmapContextRelease(TMBitmapContext **pBitmapContext);

TXMAPSDK_EXPORT TMColor TMBitmapContextGetPixel(TMBitmapContext *pBitmapContext, int x, int y);

TXMAPSDK_EXPORT void TMBitmapContextSetPixel(TMBitmapContext *pBitmapContext, int x, int y, TMColor color);

TXMAPSDK_EXPORT void TMBitmapContextDrawBitmap(TMBitmapContext *pBitmapContext, TMBitmap *pBitmap, TMPoint point,
                               const TMRect *rect);

TXMAPSDK_EXPORT void TMBitmapContextDrawA8Bitmap(TMBitmapContext *pBitmapContext, TMPoint posInContext,
                                                 unsigned char* bmpBuffer, TMBitmapFormat format, TMPoint bmpSize);

TXMAPSDK_EXPORT void TMBitmapContextDrawBitmapSub(TMBitmapContext *pBitmapContext, TMBitmap *pBitmap, TMPoint point,
                                  unsigned int index);

TXMAPSDK_EXPORT void TMBitmapContextDrawBitmapSubp(TMBitmapContext *pBitmapContext, TMBitmap *pBitmap,
                                   TMPoint point, unsigned int index, TMRect *rect);

TXMAPSDK_EXPORT void TMBitmapContextStrentchBitmap(TMBitmapContext *pGraphicsContext, TMBitmap *pBitmap,
                                   const TMRect *dstRect, const TMRect *rect);

TXMAPSDK_EXPORT void TMBitmapContextDrawAlphaBitmap(TMBitmapContext *pBitmapContext, TMBitmap *pBitmap,
                                    TMColor color, int outline, TMPoint point, const TMRect *rect);

#define TMBitmapCreate TMBitmapContextCreate

#define TMBitmapRetain TMBitmapContextRetain

#define TMBitmapRelease TMBitmapContextRelease

#define TMBitmapGetPixel TMBitmapContextGetPixel

#define TMBitmapSetPixel TMBitmapContextSetPixel

TXMAPSDK_EXPORT void TMBitmapWriteToBMP(TMBitmap *pBitmap, const char *filePath);

bool TMBitmapWriteToPPM(TMBitmap *pBitmap, const char *filePath);

TMBitmap * TMBitmapContextCopyNoBlankRegion(TMBitmap *pBitmap, int extend_x, int extend_y);

#ifdef __cplusplus
}
#endif

#endif /* TMBitmapContext_hpp */
