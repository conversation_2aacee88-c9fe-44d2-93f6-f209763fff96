//
//  group_icon_marker_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2021/10/25.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef CUSTOM_ICON_MARKER_OPTIONS_H
#define CUSTOM_ICON_MARKER_OPTIONS_H

#include <vector>
#include "overlay/overlay.h"

class MapMarkerCustomIconListenerImpl;

namespace MAPAPI {

/*
 view_ 转换为MTKView
 MTKView *metalView = (__bridge MTKView *)view_;
 render_command_encoder_ 转换为 id<MTLRenderCommandEncoder>
 id<MTLRenderCommandEncoder> renderEncoder = (__bridge id<MTLRenderCommandEncoder>)render_command_encoder_;
 */
class TXMAPSDK_EXPORT RenderContent {
public:
    void* view_;
    void* render_command_encoder_;
    bool is_opengl_;
};

class TXMAPSDK_EXPORT CustomOverlayListener
{
public:
    virtual void OnInit(const RenderContent& content) = 0;
    virtual bool OnDraw(const RenderContent& content) = 0;
    virtual bool OnTap(int screen_x, int screen_y, int tap_type) = 0;
    virtual void OnCollisionResult(int index) = 0;
    virtual void OnCollision3DResult(int index) = 0;
    virtual std::vector<std::vector<MapVector4f>> OnGetScreenArea() = 0;
    virtual bool updateFrame(double duration) = 0;
    virtual void OnDestroy(const RenderContent& content) = 0;
};

class TXMAPSDK_EXPORT CustomIconMarkerOptions : public OverlayOptions
{
public:
    CustomIconMarkerOptions();
    virtual ~CustomIconMarkerOptions() override;
    virtual OverlayType GetType() const override;
    
    // 是否可以响应点击
    CustomIconMarkerOptions& SetClickable(bool clickable);
    bool GetClickable() const;
    std::shared_ptr<CustomOverlayListener> GetListener() const;
    CustomIconMarkerOptions& SetListener(const std::shared_ptr<CustomOverlayListener>);
    
    CustomIconMarkerOptions& SetAvoidAnnotation(bool avoidAnnotation);
    bool GetAvoidAnnotation() const;

    CustomIconMarkerOptions& SetClickableForced(bool clickable);
    bool GetClickableForced()const;
    
    CustomIconMarkerOptions& SetCollision3D(bool avoid_3d);
    bool GetCollision3D()const;
    
    CustomIconMarkerOptions& SetCollision3DType(OverlayAvoid3dType type);
    OverlayAvoid3dType GetCollision3DType()const;
    
    CustomIconMarkerOptions& SetCoordinate(MapVector3d coord);
    MapVector3d GetCoordinate() const;
    
    CustomIconMarkerOptions& SetAttachPoiid(std::string poi_id);
    std::string GetAttachPoiid() const;

    // 传递给底层的listener实现
    std::shared_ptr<MapMarkerCustomIconListenerImpl> listenerImpl;
    
private:
    struct Impl;
    std::shared_ptr<CustomIconMarkerOptions::Impl> GetImpl();
    std::shared_ptr<CustomIconMarkerOptions::Impl> GetImpl() const;
};

}

#endif
