//
// Created by cuipanpan on 2021/7/6.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "overlay/overlay.h"
#include "common_lane_options.h"

namespace MAPAPI {
class MapImpl;

class TXMAPSDK_EXPORT SpecialLaneOptions : public CommonLaneOptions{
public:
    SpecialLaneOptions();
    SpecialLaneOptions(const SpecialLaneOptions & other);
    SpecialLaneOptions& operator=(const SpecialLaneOptions & other);
    ~SpecialLaneOptions();

    /**
     * 特殊车道显示文本颜色，格式rgba
     * @param text_color 颜色
     */
    SpecialLaneOptions & SetTextColor(long text_color);
    long GetTextColor() const;

    /**
     * 特殊车道文本宽度
     * @param text_width 宽度
     */
    SpecialLaneOptions & SetTextWidth(int text_width);
    int GetTextWidth() const;

    /**
     * 特殊车道文本高度
     * @param text_height 高度
     */
    SpecialLaneOptions & SetTextHeight(int text_height);
    int GetTextHeight() const;

    /**
     * 文本显示间隔
     * @param text_gap 间隔
     */
    SpecialLaneOptions & SetTextGap(double text_gap);
    double GetTextGap() const;

    /**
     * 文字显示位置（自车前方指定距离）
     * @param to_car_dis 距离
     */
    SpecialLaneOptions & SetToCarDistance(double to_car_dis);
    double GetToCarDistance() const;

    /**
     * 设置自车位置,复用LaneInfo，LaneInfo的两个打断点一样
     * @param car_pos 车标位置
     */
    SpecialLaneOptions & SetCarPosition(MapVector3d car_pos);
    LaneInfo GetCarPosition() const;

    virtual OverlayType GetType() const override { return OverlayType::SpecialLane; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
private:
    struct Impl;
    std::shared_ptr<SpecialLaneOptions::Impl> GetImpl();
    std::shared_ptr<SpecialLaneOptions::Impl> GetImpl() const;
};

}
#endif