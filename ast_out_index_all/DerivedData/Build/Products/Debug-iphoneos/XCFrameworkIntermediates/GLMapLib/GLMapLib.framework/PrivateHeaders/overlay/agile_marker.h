//
//  agile_marker.h
//  GLMapLib2.0
//
//  Created by ji<PERSON><PERSON><PERSON> on 2025/02/04.
//  Copyright © 2025 TencentMap. All rights reserved.
//

#pragma once

#include "overlay/marker.h"
#include "overlay/agile_marker_options.h"

namespace tencentmap{
class OVLAgileMarker;
}

namespace MAPAPI {
class TXMAPSDK_EXPORT AgileMarker : public Marker{
    friend class OverlayFactory;
public:
    
    const AgileMarkerOption & GetOption();
    void SetOption(const AgileMarkerOption & options);
    
    void RemoveFromMap() override;
 
    void SetAvoid3DEnable(bool enable);
 
   void SetAnimParam(const CanvasAnimParam &param);

private:
    using Marker::Marker;
    std::shared_ptr<tencentmap::OVLAgileMarker> GetImpl();
    AgileMarkerOption options_;
};

}
 
