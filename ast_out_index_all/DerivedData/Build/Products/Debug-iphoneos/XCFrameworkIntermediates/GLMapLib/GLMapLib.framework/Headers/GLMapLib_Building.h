//
//  GLMapLib_Indoor.h
//  GLMapLib2.0
//
//  Created by lihuafeng on 2018/11/12.
//  Copyright © 2018 nopwang. All rights reserved.
//

#ifndef GLMapLib_Building_h
#define GLMapLib_Building_h

#include "GLMapLib_Base.h"

#ifdef __cplusplus
extern "C" {
#endif

    /**
     设置黑白名单控制规则
     说明：1.目前引擎同一时刻仅支持一种模式，非黑即白；不设置内部为黑名单方式，黑名单类型为空，即按照配置显示；
          2.该接口可以重复调用, 新规则会覆盖之前设置的规则
     @param pWorld 场景句柄
     @param pRule  显示控制规则
     */
    void MapSetBlackWhiteListRule(void *pWorld, BlackWhiteListRule* pRule);

    
    // 函数名需更正为 MapBuildingSet3DEnabled；因为在俯视正交投影时，3DEnabled为true时3DEffect仍为false。
    /**
     设置楼快是否允许3D效果。设置了也不一定表示当前就是显示3D效果，内部还受到比例尺的控制。
     
     @param pWorld 场景句柄
     @param bEnabled false->2D, true->3D;
     */
    void MapBuildingSetEffect3D(void *pWorld, bool bEnabled);
    
    /**
     获取楼快是否允许3D效果。
     
     @param pWorld 场景句柄
     @return false->2D, true->3D;
     */
    bool MapBuildingIsEffect3D(void *pWorld);
    // 函数名需更正为 MapBuildingIs3DEnabled；原因同上。
    
    /**
     * 查询当前是否显示3D楼块效果
     */
    bool MapBuildingIsShowing3D(void *pWorld);
 
    /**
     * 设置楼块需要避让的rect列表。
     * 当rectList=NULL，或者rectCount=0，表示清空之前设置的rect列表。
     * MapRectD中origin为矩形框的左上角的坐标，size中width, height都大于0。
     */
    void MapBuildingSetAvoidRectList(void * pWorld, MapRectD * rectList, int rectCount);

    /**
     * 楼块重新加载。
     */
    void MapBuildingReload(void *pWorld);

    typedef enum _BuildingLightType{
        Building_Light_Spot,  //打卡点选光柱
        Building_Highlight,   //终点高亮
        Building_Pick_Now     //用户实时选中
    }BuildingLightType;

    /**
     * 设置建筑光柱和蒙层
     * @param pWorld  场景句柄
     * @param info    光柱和蒙层参数（包括id级样式列表）
     * @param type    高亮类型（打卡:Building_Light_Spot, 高亮:Building_Highlight）
     */
    void MapSetBuildingLightColorInfo(void *pWorld, BuildingLightInfo *info, int type);
    
    /**设置建筑的可打卡效果的区域，全部为0时，认为是无效的rect清空所有水波纹效果
     * @param pWorld 场景句柄
     * @param leftup 查询的范围左上角（墨卡托坐标）
     * @param rightdown 查询的范围右下角（墨卡托坐标）
     */
    void MapSetBuildingVisitRect(void *pWorld, MapVector2d leftup, MapVector2d rightdown);
    
    /**
     设置室内图显示隐藏
     
     @param pWorld 场景句柄
     @param hidden 隐藏状态
     */
    void MapIndoorBuildingSetHidden(void *pWorld, bool hidden);

    /**
     设置室内图是否绘制。
     绘制状态默认为true。
     
     当室内状态：
     hidden = false, draw = true ：触发室内回调，室内绘制。
     hidden = false, draw = false ：触发室内回调，室内不绘制。
     hidden = true ：不触发室内回调，室内不绘制。
     
     hidden来源于MapIndoorBuildingSetHidden。
 
     @param pWorld 场景句柄
     @param bDraw 是否绘制
    */
    void MapIndoorBuildingSetDrawEnable(void *pWorld, bool bDraw);

    /**
     * 查询室内图是否绘制。
     * @param pWorld 场景句柄
     * @return 室内图是否绘制。
     */
    bool MapIndoorBuildingGetDrawEnable(void *pWorld);

    /**
     停车场面属性结构体
     */
    typedef struct IndoorParkSpaceInfo{
        char  area_id[64];
        TMColor  color;
    }IndoorParkSpaceInfo;

    typedef struct IndoorParkSpaceInfoBatchs
    {
        TMColor color;
        char**       area_ids;
        int          id_counts;
    }IndoorParkSpaceInfoBatchs;
    
    /**
     设置室内图激活（当前显示）的楼层
     
     @param pWorld 场景句柄
     @param floorID 楼层ID
     @return 是否设置成功
     */
    bool MapIndoorBuildingSetActiveFloorID(void *pWorld, int floorID);

    /**
     批量设置室内停车位颜色
     @param pWorld                                场景句柄
     @param infos                                 停车场面属性结构体
     @param size                                    数目
     @return                    是否设置成功
     */
    bool MapIndoorBuildingSetActiveParkSpaceColor(void *pWorld, IndoorParkSpaceInfo* infos, int size);


    /*
     * 按颜色批量设置室内停车位颜色
     * @param pWorld                     场景句柄
     * @param IndoorParkSpaceInfoBatchs   停车场面属性结构体
     * @param size                       数目
     * @return                           是否设置成功
     */
    bool MapIndoorBuildingSetActiveParkSpaceColorBatch(void *pWorld, IndoorParkSpaceInfoBatchs* infos, int size);

    /**
    * 室内数据重置
    */
    void MapIndoorBuildingReset(void *pWorld);
    
    /**
     获取激活的室内图（当前高亮）ID
     
     @param pWorld 场景句柄
     @param guid 室内图ID，通过参数返回
     @return 是否成功获取
     */
    bool MapIndoorBuildingGetActiveBuildingGUID(void *pWorld, unsigned long long *guid);
    
    /**
     获取室内图楼层总数
     
     @param pWorld 场景句柄
     @param floorNum 楼层总数，通过参数返回
     @return 是否成功获取
     */
    bool MapIndoorBuildingGetActiveFloorNum(void *pWorld, int *floorNum);
    
    /**
     获取室内图所有的楼层名称
     
     @param pWorld 场景句柄
     @param floorNum 楼层总数
     @param floorNames 楼层名称，通过参数返回
     @return 是否成功获取
     */
    bool MapIndoorBuildingGetActiveFloorNames(void *pWorld, int floorNum, GLMapFloorName* floorNames);
    
    /**
     获取室内图激活（当前显示）的楼层
     
     @param pWorld 场景句柄
     @param floorID 楼层ID，通过参数返回
     @return 是否成功获取
     */
    bool MapIndoorBuildingGetActiveCurrentFloor(void *pWorld, int *floorID);
    
    /**
     获取室内图当前激活的楼层名称
     
     @param pWorld 场景句柄
     @param name 楼层名称，通过参数返回
     @param size 名称字符数组长度
     @param coordinate 楼层坐标中心，通过参数返回
     @return 是否成功获取
     */
    bool MapIndoorBuildingGetActiveName(void *pWorld, unsigned short *name, int size, MapVector2d *coordinate);
    
    /**
     获取当前激活的室内图闭包矩形
     
     @param pWorld 场景句柄
     @param bounds 闭包矩形，通过参数返回
     @return 是否成功获取
     */
    bool MapIndoorBuildingGetActiveBounds(void *pWorld, MapRectD *bounds);
    
    /**
     设置室内图屏幕激活区域，在该区域内的室内图可能被激活高亮显示
     
     @param pWorld 场景句柄
     @param x 屏幕区域左上角x (0.0f ~ 1.0f)
     @param y 屏幕区域左上角y (0.0f ~ 1.0f)
     @param width 屏幕区域宽度 (0.0f ~ 1.0f)
     @param height 屏幕区域高度 (0.0f ~ 1.0f)
     */
    void MapIndoorBuildingSetActiveScreenArea(void *pWorld, float x, float y, float width, float height);
    
    /**
     室内图高亮时，高亮室内图以外的地方覆盖了蒙版，设置该蒙版的颜色值
     
     @param pWorld 场景句柄
     @param color 颜色值
     */
    void MapIndoorBuildingSetGrayMaskColor(void *pWorld, TMColor color);
    
    /**
     设置室内图 楼层小面是否可选中
     
     @param pWorld 场景句柄
     @param enabled  可选状态
     */
    void GLMapSetIndoorBuildingPickEnabled(void *pWorld, bool enabled);
    
    /*
     设置在点击室内图标注时，是否返回室内图基础信息；
     @param pWorld 场景句柄
     @param enabled    true自动返回，false不返回；
     */
    void GLMapSetIndoorTextAttachBuildingInfoOnTapEnable(void *pWorld, bool enabled);
    
    /**
     设置指定室内图的指定楼层
     @param pWorld    场景句柄
     @param guid      指定室内图的唯一ID
     @param floorName 楼层名称，如"F1"
     */
    void MapIndoorBuildingSetSelectedIndoorBuildingGuidAndFloorName(void *pWorld, const char *guid, const char *floorName);

    /**
     废弃：可以用MapIndoorBuildingSetShowIndoorBuildingControlRule接口代替
     设置室内图显示的白名单
     若白名单不为空时，按照白名单显示室内图；若白名单为空，所有室内图均可显示.
     @param pWorld 场景句柄
     @param guidlist 室内图guid白名单
     @param size     室内图白名单大小(个数)
     */
    void MapIndoorBuildingSetShowIndoorBuildingWhiteList(void *pWorld, const char **guidlist, int size);

    typedef MAP_ENUM(int, GLMapIndoorStyleIndex)
    {
        GLMapIndoorStyle_Day = 0,
        GLMapIndoorStyle_Night = 1
    };

    /**
     * 设置室内图的显示样式
     * @param pWorld 场景句柄
     * @param index 样式id
     */
    void MapIndoorBuildingSetStyleIndex(void* pWorld, GLMapIndoorStyleIndex index);

    /**
     * 此接口已废弃，无任何意义。2023.5.18
     */
    void MapIndoorBuildingSetCfgDownloadTag(void* pWorld, bool isAll);

    /**
     * 查询poiid对应的poi的位置
     * @param pWorld 场景句柄
     * @param poiid  poi的id
     * @param box_size  poi整体的大小
     * @param anchor_offset  poi锚点在box中的位置，
     * @return 三维位置信息，P20坐标
     */
    MapVector3d MapQueryPoiPositionAndBox(void *pWorld, const char* poiid, MapVector2f *box_size, MapVector2f *anchor_offset);

    /**
     * 修改poiid的显示优先级，以不会被避让
     * @param pWorld 场景句柄
     * @param poiid poi的id
     * @param toMax true设置为无法被避让，false还原为原来的避让状态
     */
    void MapModifyPoiPriority(void* pWorld, const char* poiid, bool toMax);

    /**
     * 刷新某个POI的rich信息，设置后会从ICustomTileRichImageGenerator回调中重新获取rich图片
     * @param pWorld 场景句柄
     * @param poiid poi的id
     */
    void MapRefreshPoiRich(void* pWorld, const char* poiid);
    
    /**
     设置建筑动画开关
     @param animation_type 建筑物动画类型：虚实动画，生长动画，楼块压低动画
     @param enable  是否开启，默认关闭
     */
    void MapBuildingSetAnimationEnable(void *pWorld, MapBuildingAnimationType animation_type, bool enable);

    /**
     设置模型缩放比例
     @param model_type 模型类型：0。L1建筑物只对高度压缩（不在xy方向压缩），通模（树等）对整体压缩
     @param sub_type 模型子类型，详见Object3DSubType，目前只用来细分通模子类型，可扩展
     @param scale_ratio  压缩百分比，默认100%
     */
    void MapSetModelInitalScaleForScene(void *pWorld, Object3DType model_type, Object3DSubType sub_type, float scale_ratio);

    /**
     设置建筑加载范围
     @param scope_type 建筑物加载数据范围：Low：近处加载（与线上相同），High：远处加载
     */
    void MapSetFrustumScopeOption(void *pWorld, MapFrustumScopeType scope_type);

    /**
     * 设置室外蒙层是否生效
     * @param enable enable设置为true则生效；设置为false则不生效。
     * @param guid 室外蒙层生效的建筑物的guid
     * @param floor_name 生效的建筑物的楼层名称
     */
    void MapIndoorBuildingSetEnableIndoorMask(void* pWorld, bool enable, const char* guid, const char* floor_name);

    /**
     * 设置室外蒙层的颜色
     * @param rgba 室外蒙层的颜色值。颜色为RGBA格式。其中RGB分量代表蒙层颜色，A分量代表蒙层透明度。A值越大，蒙层越不透明。
     */
    void MapIndoorBuildingSetIndoorMaskColor(void* pWorld, TMColor rgba);

    /**
     * L4素模
     * */
    typedef struct _SimpleLandMarkMode {
        bool use_simple_mode;  // 是否使用素模,默认false
        float brightness;     // 亮度（0～2）
        float saturation;     // 饱和度（0～2）
        float contrast;      // 对比度 （0～2）
        float addition_color_intensity;  //附加颜色强度
        TMColor addition_color;          //附加颜色
    }SimpleLandMarkMode;

    /**
     * 开启/关闭L4 素模功能
     * */
    void MapLandMarkerSimpleMode(void* pWorld, SimpleLandMarkMode* mode);
 
    /**
     * 设置底商高亮的颜色
     * @param storeid  底商的poiid，为null时清空所有高亮
     * @param color  底商高亮颜色，为0时清空当前id对应的高亮效果
     */
    void MapSetUnderStoreHighLight(void *pWorld, const char *storeid, TMColor color);

    /**
     * 指定L1&L4 特定建筑的透明度
     * @param guidlist 建筑的guid列表
     * @param size 数组的长度
     * @param alpha 指定透明度 取值范围（0～1）
     *              其中 0表全透，1 表全不透
     */
     void MapBuildingSetTransparent(void *pWorld, const char **guidlist, int size, float alpha);

    /**
     * 渐近半透开关
     * @param enable  设置为true则生效，设置为false则不生效。
     */
     void MapSetTranslucentBuildingEnable(void *pWorld, bool enable);

    /**
     * 渐近半透时L1建筑的最透半透值
     * @param alpha  楼块的最小半透值，区间为 [0.0-1.0]
     */
     void MapSetTranslucentBuildingMinAlpha(void *pWorld, float alpha);

    /**
     * 移图半透时L4精模的最透半透值
     * @param alpha  精模的最小半透值，区间为 [0.0-1.0]
     */
     void MapSetTranslucentLandmarkMinAlpha(void *pWorld, float alpha);

#ifdef __cplusplus
}
#endif

#endif /* GLMapLib_Indoor_h */
