//
//  model_group.h
//  GLMapLib
//
//  Created by al<PERSON><PERSON><PERSON> on 2024/10/21.
//

#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {
class MapImpl;
class TXMAPSDK_EXPORT ModelGroupOptions : public OverlayOptions {
public:
    ModelGroupOptions();
    ModelGroupOptions(const ModelGroupOptions& other);
    ModelGroupOptions& operator=(const ModelGroupOptions& other);
    
    
    virtual OverlayType GetType() const override { return OverlayType::ModelGroup; }
    
    /**
     * 设置Obj模型文件路径
     * @param obj_paths 模型路径。第一项为背景模型，可能会被拉伸、缩放。后续项为前景模型，不会被拉伸，可能会被缩放。
     */
    void SetObjPaths(const std::vector<std::string>& obj_paths);
    const std::vector<std::string>& GetObjPaths() const;
    
    /**
     * 设置Obj模型文件路径
     * @param texture_paths 模型贴图路径。需要和模型路径一一对应
     */
    void SetTexturePaths(const std::vector<std::string>& texture_paths);
    const std::vector<std::string>& GetTexturePaths() const;
    
    /**
     * 设置overlay的坐标
     * @param coordinate 经纬度坐标+相对高程（单位：米）
     */
    void SetCoordinate(const MapVector3d& coordinate);
    const MapVector3d& GetCoordinate3D() const;
    
    /**
     * 设置模型朝向
     * @param heading_angle 正北方向顺时针夹角 [0,360)
     */
    void SetHeadingAngle(const float& heading_angle);
    const float& GetHeadingAngle() const;
    
    /**
     * 设置模型尺寸
     * @param dimensions 沿X、Y、Z轴的尺寸（单位：米）。
     */
    void SetDimensions(const MapVector3f& dimensions);
    const MapVector3f& GetDimensions() const;
    
    /**
     * 设置模型Anchor
     * @param anchor 沿X、Y、Z轴的锚点位置[0, 1]
     */
    void SetAnchor(const MapVector3f& anchor);
    const MapVector3f& GetAnchor() const;
    
    void CoordinateTransform(MapImpl * mapImpl);
    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
private:
    struct Impl;
    std::shared_ptr<ModelGroupOptions::Impl> GetImpl() ;
    std::shared_ptr<ModelGroupOptions::Impl> GetImpl() const;
};

}
#endif
