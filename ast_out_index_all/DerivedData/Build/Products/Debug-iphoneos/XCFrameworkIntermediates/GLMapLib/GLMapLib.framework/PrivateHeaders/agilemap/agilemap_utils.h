//
//  agilemap_utils.h
//  GLMapLib
//
//  Created by ji<PERSON><PERSON><PERSON> on 2024/6/26.
//

#pragma once
#include <stdio.h>
#include "agilemap/agilemap_controller.h"

namespace MAPAPI {
class AgilemapUtils{
public:
    //option转为二进制buffer
    static std::vector<char> CanvasOptionToBuffer(const std::vector<MAPAPI::CanvasOption>& options, const std::string& tapinfo);
    //二进制buffer转为option
    static std::vector<MAPAPI::CanvasOption> CanvasOptionFromBuffer(const char* buffer, int buffer_count);
    
    //快速读取size和anchor，返回二进制的offset
    static bool CanvasSizeInfoFromSingleBuffer(const char*& buffer, size_t& buffer_count, MapVector2i& size, MapVector2f& anchor);
    //base64编码转为option
    static std::vector<MAPAPI::CanvasOption> CanvasOptionFromBase64Buffer(const std::string& base64_buf);
    //option转为base64编码
    static std::string CanvasOptionToBase64Buffer(const std::vector<MAPAPI::CanvasOption>& options);
    //二进制buffer转为一个option
    static MAPAPI::CanvasOption BufferToSingleOption(const char*& buffer, size_t& buffer_count);

    /**
     * 将一个完整的百变marker画布信息序列base64字符串
     * @param canvasLayoutParam 百变marker画布信息
     * @return 序列化的base64字符串
     */
    static std::string CanvasLayoutParamToBase64Buffer(const MAPAPI::CanvasLayoutParam& canvasLayoutParam);
    /**
     * 将一个序列化的base64字符串转换为百变marker画布信息
     * @param canvasLayoutParamBuffer
     * @return 百变marker画布信息
     */
    static MAPAPI::CanvasLayoutParam CanvasLayoutParamFromBase64Buffer(const std::string& canvasLayoutParamBuffer);
private:
    
    static std::vector<char> SingleOptionToBuffer(const MAPAPI::CanvasOption& options);
};

}
