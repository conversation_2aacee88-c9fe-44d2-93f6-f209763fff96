#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {
class MapImpl;
class TXMAPSDK_EXPORT TargetLaneOptions : public OverlayOptions{
public:
    TargetLaneOptions();
    TargetLaneOptions(const TargetLaneOptions & other);
    TargetLaneOptions& operator=(const TargetLaneOptions & other);
    ~TargetLaneOptions();

    TargetLaneOptions & SetPoints(const std::vector<MapVector3d> & points);
    const std::vector<MapVector3d> & GetPoints() const;

    TargetLaneOptions & SetTargetPoint(const MapVector3d & target_point);
    const MapVector3d & GetTargetPoint() const;

    TargetLaneOptions & SetAnimationTime(double time);
    double GetAnimationTime() const;

    TargetLaneOptions & SetRotate(float degree);
    float GetRotate() const;

    TargetLaneOptions & SetSkewAngle(float skew_degree);
    float GetSkewRotate() const;

    void CoordinateTransform(MapImpl * mapImpl);
    
    virtual OverlayType GetType() const override { return OverlayType::TargetLane; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
private:
    struct Impl;
    std::shared_ptr<TargetLaneOptions::Impl> GetImpl();
    std::shared_ptr<TargetLaneOptions::Impl> GetImpl() const;
};

}
#endif