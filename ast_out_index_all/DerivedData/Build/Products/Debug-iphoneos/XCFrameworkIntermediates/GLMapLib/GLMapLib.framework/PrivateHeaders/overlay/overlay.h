//
//  Created by TencentMap on 2020/4/29.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_OVERLAY_H__
#define __MAPAPI_OVERLAY_OVERLAY_H__

#include <stdio.h>
#include <string>
#include <cstring>
#include <vector>
#include "base/map_define.h"
#include "overlay_options.h"
#include "animation/animation_enable_object.h"

namespace tencentmap {
class Overlay;
class OverlayCollisionCollector;
class OverlayCollisionMgr;
}

namespace MAPAPI {

class MapImpl;

enum OverlayAvoidType : uint8_t {
  kAnnotation = 0x1 << 0,
  kAvoidOtherOverlay = 0x1 << 1,
  kAvoidUI = 0x1 << 2,
  kAvoidLocator = 0x1 << 3,
  kAvoid3D = 0x1 << 4,
  kAvoidMainRoute = 0x1 << 5,
  kAvoidSelectedRoute = 0x1 << 6
};

class CandidateLayout {
 public:
    virtual ~CandidateLayout() = default;

    virtual std::vector<MapVector4f> GetScreenLayout() const = 0;

    virtual MapVector2f GetScreenPoint() const = 0;

    virtual float GetZDepth() const =0;

    virtual std::vector<MapVector2f> GetScreenOffset() const = 0;

    virtual MapVector2f GetAnchor() const = 0;

    virtual int GetIndex() const = 0;

    virtual bool IsFirstIconRect() const = 0;
};

class TXMAPSDK_EXPORT Overlay: public AnimationEnableObject{
public:
    /**
     * 设置overlay优先级，默认优先级为0，值越大优先级越高
     *
     * @param priority 优先级，支持负数
     */
    virtual void SetPriority(int priority);
    
    /**
     * 获取overlay优先级，默认优先级为0
     *
     * @return 优先级，可能为负数
     */
    virtual int  GetPriority();
    
    /**
     * 设置overlay显示的比例尺范围
     *
     * @param nMinScaleLevel 最小比例尺Level，最小取值为0
     * @param nMaxScaleLevel 最大比例尺Level, 最大取值为30
     */
    virtual void SetScaleLevelRange(int nMinScaleLevel, int nMaxScaleLevel);
    
    /**
     * 设置临时优先级为最高，不影响priority
     *
     * @param bOnTop true->临时最高优先级；false->取消临时最高优先级
     */
    virtual void SetOnTop(bool bOnTop);
    
    /**
     * 设置overlay隐藏状态
     */
    virtual void SetHidden(bool hidden);
    
    /**
     * 获取overlay隐藏状态
     */
    virtual bool GetHidden();
    
    /**
     * 设置是否可点击
     */
    virtual void SetClickable(bool bClickable);
    
    /**
     * 设置透明度
     *
     * @param alpha 透明度(0.0f ~ 1.0f)
     */
    virtual void SetAlpha(float alpha);
    
    enum class TXMAPSDK_EXPORT GeometryType{
        Point   = 0,
        Line    = 1,
        Polygon = 2
    };
    
    /**
     * Overlay内部是按点，线，面三个图层的顺序从上到下绘制，如果需要把某个overlay移到
     * 其它图层显示，可以修改其GeometryType
     */
    virtual void SetGeometryType(enum GeometryType geoType);
    
    /**
     * 获取 GeometryType
     */
    virtual GeometryType GetGeometryType();
    
    /**
    * 获取动画的对象类型
    *
    * @return 获取动画的对象类型枚举
    */
    AnimationOwnerEnum GetAnimationOwnerType() const override;
    
    /**
    * 获取动画的对象ID
    *
    * @return 获取动画的对象ID，底图为0，Overlay是自己的ID
    */
    int GetID() const override;

    /**
     * 设置 Overlay 附属信息
     */
    void SetCustomAttr(std::shared_ptr<OverlayCustomAttr> ext) {
      ext_ = std::move(ext);
    }

    /**
     * 获取 Overlay 附属信息
     */
    template<class T>
    std::shared_ptr<T> GetCustomAttr() const {
      return std::dynamic_pointer_cast<T>(ext_);
    }

    /*
     * 查询避让掩码
     */
    virtual uint8_t GetAvoidMask() const;

    /**
     * 查询避让类型
     */
    bool HasAvoidType(OverlayAvoidType type) const;

    /**
     * 设置避让状态
     */
    void SetAvoided(bool flag);

    /**
     * 获取是否已经避让
     */
    bool IsAvoided() const;

    /**
     * 获取候选布局
     */
    std::vector<CandidateLayout*> GetCandidateLayouts() const;

    /**
     * 设置后选布局 id
     */
    void SetSelectedLayout(int idx);

    /**
     * 获取当前布局
     */
    int GetSelectedLayout() const;

    /**
     * 获取后选布局数量
     */
    int GetCandidateLayoutsSize() const;

 public:
    friend class OverlayFactory;
    friend class MapImpl;
    Overlay();//for unit test
    ~Overlay() override;

    template<class T>
    static std::shared_ptr<T> Make(std::weak_ptr<MapImpl> map_impl,
                                   std::shared_ptr<tencentmap::Overlay> impl) {
        return std::make_shared<T>(map_impl, impl, false);
    }

    Overlay(std::weak_ptr<MapImpl>, std::shared_ptr<tencentmap::Overlay>);
protected:
    friend class tencentmap::Overlay;
    friend class tencentmap::OverlayCollisionMgr;
    friend class tencentmap::OverlayCollisionCollector;
    virtual void RemoveFromMap();
    std::weak_ptr<tencentmap::Overlay> GetBaseImpl() const;
    std::weak_ptr<MapImpl> GetMapImpl();

protected:
    std::shared_ptr<OverlayOptions> options_;
    
private:
    //enum Overlay::GeometryType geo_type_;
    
    //impl_不能为nullptr，如果内部CreateOverlay失败，造一个空对象weak_ptr
    std::weak_ptr<tencentmap::Overlay> impl_;
    
    std::weak_ptr<MapImpl> map_impl_;

    std::shared_ptr<OverlayCustomAttr> ext_;
};

}

#endif /* __MAPAPI_OVERLAY_OVERLAY_H__ */
