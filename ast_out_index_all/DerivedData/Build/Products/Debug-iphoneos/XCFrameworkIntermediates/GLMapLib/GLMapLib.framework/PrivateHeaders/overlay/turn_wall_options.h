#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {
class MapImpl;
class TXMAPSDK_EXPORT TurnWallOptions : public OverlayOptions{
public:
    TurnWallOptions();
    TurnWallOptions(const TurnWallOptions & other);
    TurnWallOptions& operator=(const TurnWallOptions & other);
    ~TurnWallOptions();

    TurnWallOptions & SetPoints(const std::vector<MapVector3d> & points);
    const std::vector<MapVector3d> & GetPoints() const;
    
    void CoordinateTransform(MapImpl * mapImpl);
    
    virtual OverlayType GetType() const override { return OverlayType::TurnWall; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
private:
    struct Impl;
    std::shared_ptr<TurnWallOptions::Impl> GetImpl() ;
    std::shared_ptr<TurnWallOptions::Impl> GetImpl() const;
};

}
#endif