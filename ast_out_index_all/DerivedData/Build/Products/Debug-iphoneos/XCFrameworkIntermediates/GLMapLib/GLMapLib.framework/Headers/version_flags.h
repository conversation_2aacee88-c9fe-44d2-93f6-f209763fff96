// Copyright 2024 Tencent Inc. All rights reserved.
//
// Generated by FindMBS.cmake::mbs_generate_version_flags, do not edit.
//
// Informations for version
//
#pragma once

// Module Name
#define TXMAPSDK_MODULE_NAME "txmapsdk"
// Version
#define TXMAPSDK_VERSION "6.19.0.1006"

//
// Informations for git repository
//

// Branch name
#define TXMAPSDK_BRANCH_NAME "tag/Engine/v4.22.24"
// Last commit hash
#define TXMAPSDK_COMMIT_HASH "301e2bf0891cfc04d1b626d60d48c4e6787a65d0"
// Last commit datetime
#define TXMAPSDK_COMMIT_DATETIME "2025-06-05 00:35:13"
// Genarate this file's datetime
#define TXMAPSDK_BUILD_DATETIME "2025-06-05 01:02:59"
// Uncommit changes
#define TXMAPSDK_HAS_LOCAL_CHANGE 5
// Pangu version, empty if current build process is not in the pangu shell
#define TXMAPSDK_PANGU_VERSION ""
// Pangu brand, for example:"wecar", "sosomap", "opensdk", or ""
#define TXMAPSDK_PANGU_BRAND "sosomap"


