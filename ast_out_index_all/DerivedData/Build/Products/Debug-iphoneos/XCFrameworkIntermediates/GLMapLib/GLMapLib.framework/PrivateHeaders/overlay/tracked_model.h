#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "overlay/overlay.h"
#include "overlay/tracked_model_options.h"

namespace tencentmap {
class TrackedModelImpl;
}

namespace MAPAPI {

/**
 * 有固定轨迹的模型
 */
class TXMAPSDK_EXPORT TrackedModel : public Overlay {
  friend class OverlayFactory;

 public:
  ~TrackedModel() override;
  /**
   * 设置模型的的轨迹线数据
   * @param track_line 轨迹线，经纬度坐标
   */
  void SetModelTrackLine(const std::vector<mapbase::GeoCoordinateZ> &track_line);

  /**
   * 设置模型mesh、纹理、json、箭头文件路径
   */
  void SetModelFilePath(const std::string &model_mesh, const std::string &model_texture,
                        const std::string &model_json, const std::string &arrow_texture);

  /**
   * 设置模型宽度，单位：米
   */
  void SetModelWidth(float width);

  /**
   * @param step_count 箭头动画帧数
   * @param duration 箭头动画时长，单位：秒
   */
  void SetModelArrowStepCountAndDuration(uint8_t step_count, float duration);

  /**
   * @param alpha 设置的模型透明度
   */
  void SetModelAlpha(float alpha);

  /**
   * @param alpha 设置的前向箭头透明度
   */
  void SetArrowAlpha(float alpha);

  /**
   * 获取对向来车模型位置
   * @return 经纬度坐标
   */
  MapVector3d getModelPos();

  /**
   * 获取对向来车模型在轨迹线上的比例
   * @return 对向来车模型中心位置在轨迹线上的比例
   */
  float GetModelLengthRatio();

  /**
   * 获取前向箭头在轨迹线上的比例
   * @return 箭头结束位置在轨迹线上的比例
   */
  float GetArrowLengthRatio();

  /**
   * 获取对向来车模型包围盒点位，经纬度坐标
   * @return 包围盒四个顶点三维坐标，{左上，左下，右上，右下}
   */
  std::array<MapVector3d, 4> getModelBoundingBox();

  /**
   * 获取对向来车箭头包围盒点位，经纬度坐标
   * @return 包围盒四个顶点三维坐标，{左上，左下，右上，右下}
   */
  std::array<MapVector3d, 4> getTrackBoundingBox();

  /**
   * 获取对向来车模型和箭头包围盒点位，经纬度坐标
   * @return 包围盒四个顶点三维坐标，{左上，左下，右上，右下}
   */
  std::array<MapVector3d, 4> getBoundingBox();

 private:
  using Overlay::Overlay;
  std::shared_ptr<tencentmap::TrackedModelImpl> GetImpl();
};

}
#endif
