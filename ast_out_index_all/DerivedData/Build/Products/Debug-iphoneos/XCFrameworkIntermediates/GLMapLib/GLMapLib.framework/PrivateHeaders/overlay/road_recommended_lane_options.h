//
// Created by neilxiang on 2024/9/19.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <functional>
#include <tuple>
#include "../base/map_define.h"
#include "overlay.h"
#include "had_structure.h"
namespace MAPAPI {

class MapImpl;
class TXMAPSDK_EXPORT RoadRecommendedLaneOptions : public OverlayOptions {
public:
    RoadRecommendedLaneOptions();
    
    virtual ~RoadRecommendedLaneOptions();
    
    /**
     获取OverlayType
     */
    MAPAPI::OverlayType GetType() const override;
    
    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
    /**
     坐标转换
     */
    void CoordinateTransform(MapImpl * mapImpl);
    void CoordinateTransform();
    
    /**
     * 设置是否支持点击
     * @param interactive
     */
    void SetInteractive(bool interactive);
    bool GetInteractive() const;
//    
    /**
     设置推荐车道数据
     @param data 数据
     */
    void SetRecommendedLaneData(RecommendedLaneData& data);
    
    
    /**
     设置推荐车道数据
     */
    RecommendedLaneData& GetRecommendedLaneData() const;
    
private:
    void LoadLog();

    
private:
    struct Impl;
    std::shared_ptr<RoadRecommendedLaneOptions::Impl> GetImpl();
    std::shared_ptr<RoadRecommendedLaneOptions::Impl> GetImpl() const;
};

}
#endif //NOT_USE_4K_ELEMENT
