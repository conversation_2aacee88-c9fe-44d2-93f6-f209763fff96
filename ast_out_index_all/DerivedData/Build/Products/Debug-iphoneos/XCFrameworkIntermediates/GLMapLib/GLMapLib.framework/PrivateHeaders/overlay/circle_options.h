//
//  circle_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/9.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_CIRCLE_OPTIONS_H__
#define __MAPAPI_OVERLAY_CIRCLE_OPTIONS_H__

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT CircleOptions : public OverlayOptions{
public:
    CircleOptions();
    CircleOptions(const CircleOptions & other);
    ~CircleOptions();

    CircleOptions &     SetCenter(const MapVector2d & center);
    const MapVector2d & GetCenter() const;
    
    /**
     * 单位 meter
     */
    CircleOptions &     SetRadius(float radius);
    float               GetRadius() const;
    CircleOptions &     SetShrinkDist(float dist);
    float               GetShrinkDist() const;
    
    CircleOptions &     SetFillColor(const MapColor4ub & fillColor);
    const MapColor4ub & GetFillColor() const;
    
    CircleOptions &     SetBorderColor(const MapColor4ub & borderColor);
    const MapColor4ub & GetBorderColor() const;
    
    /**
     * 单位dp
     */
    CircleOptions &     SetBorderWidth(float borderWidth);
    float               GetBorderWidth() const;
    
    CircleOptions &     SetDrawFill(bool isDrawFill);
    bool                GetDrawFill() const;
    
    CircleOptions &     SetDrawBorder(bool isDrawBorder);
    bool                GetDrawBorder() const;
    
    virtual OverlayType GetType() const override { return OverlayType::Circle; }
    
    CircleOptions &     SetClickable(bool clickable);
    bool                GetClickable() const;
    
private:
    struct Impl;
    std::shared_ptr<CircleOptions::Impl> GetImpl();
    std::shared_ptr<CircleOptions::Impl> GetImpl() const;
};

}

#endif /* __MAPAPI_OVERLAY_CIRCLE_OPTIONS_H__ */
