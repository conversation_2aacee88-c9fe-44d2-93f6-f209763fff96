//
//  marker_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/8.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_MARKER_OPTIONS_H__
#define __MAPAPI_OVERLAY_MARKER_OPTIONS_H__

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {

enum class TXMAPSDK_EXPORT TEXT_POS
{
    TXT_INVALID = 0,
    TXT_LEFT = 4,
    TXT_TOP = 8,
    TXT_RIGHT = 6,
    TXT_BOTTOM = 2
};

class TXMAPSDK_EXPORT TextOptions{
public:
    TextOptions();
    virtual ~TextOptions();
    
    void operator= (const TextOptions & text_options);
    
    /**
     * 设置文字相对icon的位置，最多可指定四个位置，引擎按顺序选择最优位置进行显示
     * 具体内容见 TEXT_POS
     */
    TextOptions & SetSubType(const std::vector<unsigned char> & subType);
    const std::vector<unsigned char> & GetSubType() const;
    
    /**
     * (bit:0) for underline; (bit:1) for bold; (bit:2) for outline or halo; (bit:3) for background; (bit:4) for textonicon; other bits is unused now;
     */
    TextOptions & SetEffect(unsigned char effect);
    unsigned char GetEffect() const;
    
    /**
     * 字体大小，单位dp
     */
    TextOptions & SetFontSize(float fontSize);
    float         GetFontSize() const;
    
    /**
     * 描边大小，单位dp
     */
    TextOptions & SetHaloSize(float haloSize);
    float         GetHaloSize() const;
    
    /**
     * 文字间隔，单位dp
     */
    TextOptions & SetTextSpace(float textSpace);
    float         GetTextSpace() const;
    
    /**
     文字行间距，单位dp
     */
    TextOptions & SetRowSpace(int rowSpace);
    int            GetRowSpace() const;
    
    /**
     * 图标间隔，单位dp
     */
    TextOptions & SetIconSpace(float iconSpace);
    float         GetIconSpace() const;
    
    TextOptions & SetTextColor(MapVector4ub textColor);
    MapVector4ub  GetTextColor() const;
    
    TextOptions & SetHaloColor(MapVector4ub haloColor);
    MapVector4ub  GetHaloColor() const;
    
    TextOptions & SetText(const std::string & text);
    const std::string & GetText() const;
    
    /**
     * 当自身文字无法避让时，是否隐藏自身文字， 默认false。
     */
    TextOptions & SetHideTextWhenAvoidFailed(bool isHideTextWhenAvoidFailed);
    bool          GetHideTextWhenAvoidFailed() const;
    
    TextOptions & SetShowDebugRect(bool isShowDebugRect);
    bool          GetShowDebugRect() const;
    
private:
    class Impl;
    std::unique_ptr<Impl> _impl;
};

enum class TXMAPSDK_EXPORT AnimIconType
{
    Icon_2D_GeoCoord_GeoAngle = 0,       // 2D Icon, 地理坐标，地理角度，正交绘制于屏幕上
    Icon_2D_GeoCoord_ScreenAngle,        // 2D Icon, 地理坐标，屏幕角度
    Icon_2D_OnScreen,                    // 2D Icon, 类似于屏幕UI
    Icon_3D                              // 3D Icon, 贴于地面
};

//序列帧动画参数
typedef struct TXMAPSDK_EXPORT AnimationImageParam{
    std::string image_name{""};//图片名称
    std::shared_ptr<TMBitmap> anim_bitmap;//动画bitmap
    float angle{0};//逆时针旋转角度，默认为0
    MapVector2i split_number{1,1};//多帧图片横向和纵向的单元个数
    MapVector2f anchor{0.5f,0.5f};//相对与父图片的百分比
    int anim_duration{1000};//动画在指定的时间内播完，可能跳帧。单位1/1000秒
    AnimIconType icon_type{AnimIconType::Icon_2D_GeoCoord_ScreenAngle};//为了兼容旧业务的默认动画图标类型
    bool IsEqual(const AnimationImageParam& param);
    MapVector3f mix_color{255.0f, 255.0f, 255.0f};
    float color_anim_duration = 0.0f;
}AnimationImageParam;

class TXMAPSDK_EXPORT MarkerOptions : public OverlayOptions{
public:
    MarkerOptions();
    MarkerOptions(const MarkerOptions & other);
    virtual ~MarkerOptions();
    
    enum class TXMAPSDK_EXPORT IconType
    {
        Icon_2D_GeoCoord_GeoAngle = 0,       // 2D Icon, 地理坐标，地理角度，正交绘制于屏幕上
        Icon_2D_GeoCoord_ScreenAngle,        // 2D Icon, 地理坐标，屏幕角度
        Icon_2D_OnScreen,                    // 2D Icon, 类似于屏幕UI
        Icon_3D                              // 3D Icon, 贴于地面
    };


    MarkerOptions &     SetIconType(enum IconType type);
    enum IconType       GetIconType() const;
    
    MarkerOptions &     SetImageName(const std::string & imageName);
    const std::string & GetImageName() const;
    bool                GetImageChanged() const;
    
    MarkerOptions &     SetCooridnate(const MapVector2d & coordinate);
    const MapVector2d & GetCoordinate() const;
    
    MarkerOptions &     SetCoordinate(const MapVector3d & coordinate);
    const MapVector3d & GetCoordinate3D() const;
    
    MarkerOptions &     SetAnchorPoint(const MapVector2f & anchorPoint);
    const MapVector2f & GetAnchorPoint() const;
    
    MarkerOptions &     SetAngle(float angle);
    float               GetAngle() const;
    
    MarkerOptions &     SetAlpha(float alpha);
    float               GetAlpha() const;
    
    MarkerOptions &     SetMixColor(const MapColor4ub & mixColor, float animationDuration = 0.0);
    const MapVector3f & GetMixColor() const;
    float               GetColorAnimDuration()const;

    MarkerOptions &     SetSubMixColor(const MapColor4ub & mixColor, float animationDuration = 0.0);

    MarkerOptions &     SetSubImages(const std::vector<std::string>& images_name, const std::vector<std::shared_ptr<TMBitmap>>&  bg_bitmaps);

    MarkerOptions &     SetScale(const MapVector2f & scale);
    const MapVector2f & GetScale() const;

    MarkerOptions &     SetFlip(const MapVector2ub & flip);
    const MapVector2ub& GetFlip() const;

    MarkerOptions &     SetAvoidAnnotation(bool avoidAnnotation);
    bool                GetAvoidAnnotation() const;
    
    MarkerOptions &     SetClickable(bool clickable);
    bool                GetClickable() const;
    
    MarkerOptions &     SetNeedAvoidCallback(bool isNeedAvoidCallback);
    bool                GetNeedAvoidCallback() const;
    
    MarkerOptions &     SetHasText(bool hasText);
    bool                GetHasText() const;
    
    MarkerOptions &     SetTextOptions(const TextOptions & textOptions);
    const TextOptions & GetTextOptions() const;
    
    MarkerOptions &     SetRockerImage(const std::string& dot_image, const std::string& line_image);
    const std::string & GetRockerLineImage() const;
    const std::string & GetRockerDotImage() const;

    MarkerOptions&      SetHSL(bool hsl);
    bool                GetHSL() const;

    // Marker裁剪类型，默认无裁剪
    // 0: 无裁剪逻辑 1: 和底图文字一致 2: 和封路marker一致
    MarkerOptions&      SetCullType(int type);
    int                 GetCullType() const;
  
    MarkerOptions& SetAnimationImageParam(const std::vector<MAPAPI::AnimationImageParam> & animation_image_params,
                                          std::shared_ptr<TMBitmap>  bg_bitmap);
    std::vector<MAPAPI::AnimationImageParam> GetAnimationImageParam() const;

    bool GetAnimationColorChanged() const;

    bool GetAnimationImagesChanged() const;
    
    std::shared_ptr<TMBitmap> GetBgBitmap() const;

    virtual OverlayType GetType() const override { return OverlayType::Marker; }
private:
    struct Impl;
    std::shared_ptr<MarkerOptions::Impl> GetImpl();
    std::shared_ptr<MarkerOptions::Impl> GetImpl() const;
};

#ifndef NOT_USE_IN_OPENSDK
class TXMAPSDK_EXPORT ImageLabelMarkerOptions : public OverlayOptions{
public:
    ImageLabelMarkerOptions();
    ImageLabelMarkerOptions(const ImageLabelMarkerOptions & other);
    virtual ~ImageLabelMarkerOptions();
    
    enum class TXMAPSDK_EXPORT IconType
    {
        Icon_2D_GeoCoord_GeoAngle = 0,       // 2D Icon, 地理坐标，地理角度，正交绘制于屏幕上
        Icon_2D_GeoCoord_ScreenAngle,        // 2D Icon, 地理坐标，屏幕角度
        Icon_2D_OnScreen,                    // 2D Icon, 类似于屏幕UI
        Icon_3D                              // 3D Icon, 贴于地面
    };
    
    ImageLabelMarkerOptions &     SetIconType(enum IconType type);
    enum IconType       GetIconType() const;//要放开吗？
    
    ImageLabelMarkerOptions &     SetMarkerImageName(const std::string & imageName);
    const std::string & GetMarkerImageName() const;
    ImageLabelMarkerOptions &     SetMarkerImageAnchor(const MapVector2f & anchorPoint);
    const MapVector2f & GetMarkerImageAnchor() const;
    
    ImageLabelMarkerOptions &     SetLabelImageName(const std::string & imageName);
    const std::string & GetLabelImageName() const;
    ImageLabelMarkerOptions &     SetLabelImageAnchor(const MapVector2f & anchorPoint);
    const MapVector2f & GetLabelImageAnchor() const;
    
    ImageLabelMarkerOptions &     SetCloseImageName(const std::string & imageName);
    const std::string & GetCloseImageName() const;
    ImageLabelMarkerOptions &     SetCloseImageAnchor(const MapVector2f & anchorPoint);
    const MapVector2f & GetCloseImageAnchor() const;
    
    ImageLabelMarkerOptions &     SetCooridnate(const MapVector2d & coordinate);
    const MapVector2d & GetCoordinate() const;
    
    ImageLabelMarkerOptions &     SetCoordinate(const MapVector3d & coordinate);
    const MapVector3d & GetCoordinate3D() const;
    
    ImageLabelMarkerOptions &     SetLabelHidden(const bool hidden);
    const bool & GetLabelHidden() const;
    
    ImageLabelMarkerOptions &     SetSpacing(float markerLabelSpacing, float markerTextSpacing);
    
    ImageLabelMarkerOptions &     SetAnchorPoint(const MapVector2f & anchorPoint);
    const MapVector2f & GetAnchorPoint() const;
    
    ImageLabelMarkerOptions &     SetAngle(float angle);
    float               GetAngle() const;
    
    ImageLabelMarkerOptions &     SetAlpha(float alpha);
    float               GetAlpha() const;
    
    ImageLabelMarkerOptions &     SetScale(const MapVector2f & scale);
    const MapVector2f & GetScale() const;
    
    ImageLabelMarkerOptions &     SetAvoidAnnotation(bool avoidAnnotation);
    bool                GetAvoidAnnotation() const;
    
    ImageLabelMarkerOptions &     SetClickable(bool clickable);
    bool                GetClickable() const;
    
    ImageLabelMarkerOptions &     SetNeedAvoidCallback(bool isNeedAvoidCallback);
    bool                GetNeedAvoidCallback() const;
    
    ImageLabelMarkerOptions &     SetHasText(bool hasText);
    bool                GetHasText() const;
    
    ImageLabelMarkerOptions &     SetTextOptions(const TextOptions & textOptions);
    const TextOptions & GetTextOptions() const;
    
    virtual OverlayType GetType() const override { return OverlayType::ImageLabelMarker; }
    
private:
    struct Impl;
    std::shared_ptr<ImageLabelMarkerOptions::Impl> GetImpl();
    std::shared_ptr<ImageLabelMarkerOptions::Impl> GetImpl() const;
};
#endif

}

#endif /* __MAPAPI_OVERLAY_MARKER_OPTIONS_H__ */
