//
//  polyline.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/30.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_COLOR_POLYLINE_H__
#define __MAPAPI_OVERLAY_COLOR_POLYLINE_H__

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/polyline.h"
#include "overlay/flow_arrow.h"
#include "overlay/turn_arrow.h"
#include "overlay/polyline_text.h"
#include "overlay/color_polyline_options.h"

namespace MAPAPI {



class TXMAPSDK_EXPORT ColorPolylineObserver {
public:
    virtual ~ColorPolylineObserver() {}
    virtual void OnPassedPoint(int pointIndex, const MapVector2d&, float locatorAngle, bool isAnimationFinished) = 0;
};

/**
 * 导航彩虹蚯蚓路线
 */
class TXMAPSDK_EXPORT ColorPolyline : public Polyline{
    friend class OverlayFactory;
public:
    /**
     * 更新坐标点、以及路段分段信息。
     * 主要用于路况刷新时调用。
     */
    void SetPoints(const std::vector<MapVector2d> & points, const std::vector<LineSection> &sections);
    
    /**
     * 修改样式
     */
    bool SetStyle(ColorPolylineStyle * style);
    
    /**
     * 设置关键节点（此节点将不被允许抽稀）
     *
     * @param pointIndex 点串
     * @param indexCount 点串坐标
     */
    void SetKeyPointIndex(int* pointIndex, int indexCount);
  
    /**
     * 设置经过的点, 在此点之前的区域将会置灰
     *
     * @param pointIndex 经过点对应的路线顶点索引
     * @param coordinate 经过点的坐标
     * @param locatorAngle 车标角度
     * @param durationInSeconds 从当前置灰点置灰到coordinate的时间间隔,单位秒
     * @param pObserver 引擎插值点监听回调
     */
    void SetPassedPoint(int pointIndex, MapVector2d coordinate, float locatorAngle, float durationInSeconds, std::shared_ptr<ColorPolylineObserver> pObserver);
  
    /**
     * 启动蚯蚓线生长动画，创建蚯蚓线的ColorPolylineStyle的draw_type必须设置为Grown
     *
     * @param durationInSeconds 动画持续时间,单位秒
     * @param pObserver 动画结束监听回调
     */
    void StartGrownAnimation(float durationInSeconds, std::shared_ptr<ColorPolylineObserver> pObserver);
    
    /**
     * 停止置灰动画
    */
    void StopPassedAnimation();
    
    /**
     * 设置清除点, 在此点之前的区域路线将会消失
     *
     * @param pointIndex 清楚点对应的路线顶点索引
     * @param coordinate 清除点的坐标
     */
    void SetClearPoint(int pointIndex, MapVector2d coordinate);
    
    /**
     * 获取流向箭头对象
     */
    FlowArrow & GetFlowArrow();
    
    /**
     * 设置路线上的文字（动态路名）
     */
    void SetText(const PolylineTextOptions & textOptions);
    
    /**
     * 获取动态路名对象
     */
    PolylineText & GetText();
    
    /**
     * 设置导航转弯箭头的可见性
     */
    void SetTurnArrowVisible(bool bVisible);

    /**
    * 获取导航转弯箭头的可见性
    */
    bool GetTurnArrowVisible();

    /**
    * 获取当前蚯蚓线是否为Top
    */
    bool GetIsTop();
    /**
     * 获取主转弯箭头
     */
    TurnArrow & GetTurnArrow();
    
    /**
     * 获取第二个转弯箭头，也叫“然后箭头”。目前手图有这个需求。
     */
    TurnArrow & GetSecondTurnArrow();
    
    /*
     * 以下接口功能对 ColorPolyline 无效
     */
    virtual void    SetPoints(const std::vector<MapVector2d> & points) override;
    
    virtual void    SetWidth(float width) override;
    virtual float   GetWidth() override;
    
    virtual void            SetColor(MapVector4ub color) override;
    virtual MapVector4ub    GetColor() override;
    
    virtual void    SetBorderWidth(float borderWidth) override;
    virtual float   GetBorderWidth() override;
    
    virtual void            SetBorderColor(MapVector4ub borderColor) override;
    virtual MapVector4ub    GetBorderColor() override;
    
    virtual void    SetFillType(PolylineFillType fillType) override;
    virtual PolylineFillType GetFillType() override;
    
    virtual void    SetJoinType(PolylineJoinType joinType) override;
    virtual PolylineJoinType GetJoinType() override;
    
    virtual void    SetStartCapType(PolylineCapType startCapType) override;
    virtual PolylineCapType GetStartCapType() override;
    
    virtual void    SetEndCapType(PolylineCapType endCapType) override;
    virtual PolylineCapType GetEndCapType() override;
    
    virtual void RemoveFromMap() override;
    
    
public:
    //外面不要调用此接口
    ColorPolyline(std::weak_ptr<MapImpl> map_impl, std::shared_ptr<tencentmap::PolylineImpl>);
    
    struct Impl;
private:
    std::unique_ptr<Impl> color_line_impl_;
};

}

#endif /* __MAPAPI_OVERLAY_COLOR_POLYLINE_H__ */
