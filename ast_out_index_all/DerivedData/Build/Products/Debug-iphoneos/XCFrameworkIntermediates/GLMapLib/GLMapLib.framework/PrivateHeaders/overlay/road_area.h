//
// Created by cuipanpan on 2022/6/24.
//

#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "../base/map_define.h"
#include "overlay.h"
#include "road_area_animation.h"
#include "road_area_options.h"
#include "road_area_texture_options.h"
#include "had_structure.h"


namespace tencentmap{
class RoadAreaOverlay;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT RoadArea : public Overlay{
friend class OverlayFactory;
public:
    /**
     更新渲染单元
     @param road_area_data  道路信息
     */
    void SetRoadAreaData(mapbase::HAD::RoadAreaData & road_area_data);
    /**
     更新纹理信息
     @param bitmap  纹理信息
     */
    void SetRoadAreaTexture(TMBitmap* bitmap);

    /**
    * 设置边线信息数据
    * @param width: 边线的物理宽度,单位dp
    * @param bitmap: 边线的纹理
    */
    void SetStrokeTexture(float width, TMBitmap* bitmap);

    void SetRoadAreaTextureByName(std::string texture, float animationTime);

    void SetRoadAreaStrokeTextureByName(std::string& texture, float width);

    void SetRoadAreaAlpha(float start, float end, float animationTime);

    /**
     更新动画采样纹理信息
     @param sample_bitmap  纹理信息
     */
    void SetAnimationSampleTexture(TMBitmap* sample_bitmap);
    
    /**
     更新动画信息
     @param animation  动画信息
     */
    void SetRoadAreaAnimation(const RoadAreaAnimation & animation);
    
    /**
     设置roadarea层级关系类型
     @param type  kCrossLane 在标线下，kInLanez在标线上
     */
    void SetRoadAreaRelationshipType(mapbase::HAD::RelationShipType type);


private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::RoadAreaOverlay> GetImpl();
};

}
#endif
