//
//  circle.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/2.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_CIRCLE_H__
#define __MAPAPI_OVERLAY_CIRCLE_H__

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/circle_options.h"

namespace tencentmap{
class OVLCircle;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT Circle : public Overlay{
    friend class OverlayFactory;
public:
    virtual void     SetCenter(const MapVector2d & center);
    const    MapVector2d & GetCenter() const;
    
    /*
     * 单位 meter
     */
    void     SetRadius(float radius);
    float    GetRadius() const;
    void     SetShrinkDist(float dist);
    float    GetShrinkDist() const;
    
    void     SetFillColor(const MapColor4ub & fillColor);
    const    MapColor4ub & GetFillColor() const;
    
    void     SetBorderColor(const MapColor4ub & borderColor);
    const    MapColor4ub & GetBorderColor() const;
    
    /*
     * 单位dp
     */
    void     SetBorderWidth(float borderWidth);
    float    GetBorderWidth() const;
    
    void     SetDrawFill(bool isDrawFill);
    bool     GetDrawFill() const;
    
    void     SetDrawBorder(bool isDrawBorder);
    bool     GetDrawBorder() const;
    
private:
    using Overlay::Overlay;
    
    std::shared_ptr<tencentmap::OVLCircle> GetImpl();
    
    std::shared_ptr<CircleOptions> GetOptions() const{
        return std::dynamic_pointer_cast<CircleOptions>(options_);
    }
};

}

#endif /* __MAPAPI_OVERLAY_CIRCLE_H__ */
