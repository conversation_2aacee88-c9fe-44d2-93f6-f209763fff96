//
//  Created by TencentMap on 2020/5/2.
//  Copyright © 2020 TencentMap. All rights reserved.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "overlay/lane_options.h"

namespace tencentmap{
class OVLLane;
}

namespace MAPAPI {

/**
 * 感知车道线Overlay
 */
class TXMAPSDK_EXPORT Lane : public Overlay{
    friend class OverlayFactory;
public:
    
    /**
     * 直接更新单条车道线信息
     */
    void SetLane(const LaneData & lane);
    
    /**
     * 直接更新多条车道线信息
     */
    void SetLanes(const std::vector<LaneData> & lanes);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::OVLLane> GetImpl() ;
};


}
#endif

