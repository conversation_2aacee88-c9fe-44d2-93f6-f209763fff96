//
// Created by cuipanpan on 2022/6/24.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "../base/map_define.h"
#include "overlay.h"
#include "had_structure.h"
namespace MAPAPI {

class TXMAPSDK_EXPORT RoadAreaTextureOptions : public OverlayOptions {
public:
  
    RoadAreaTextureOptions();

    RoadAreaTextureOptions(const RoadAreaTextureOptions &other);

    RoadAreaTextureOptions &operator=(const RoadAreaTextureOptions &other);
    
    ~RoadAreaTextureOptions();
    
    /**
     设置bitmap
     @param bitmap 纹理信息
     */
    void SetBitMap(TMBitmap* bitmap);

    /**
     设置bitmap
     @param bitmap 纹理信息
     */
    void GetBitMap(TMBitmap** bitmap) const;

    /**
     获取OverlayType
     */
    virtual OverlayType GetType() const override { return OverlayType::RoadArea; }
    
private:
    struct Impl;
    std::shared_ptr<RoadAreaTextureOptions::Impl> GetImpl();
    std::shared_ptr<RoadAreaTextureOptions::Impl> GetImpl() const;
};

}
#endif
