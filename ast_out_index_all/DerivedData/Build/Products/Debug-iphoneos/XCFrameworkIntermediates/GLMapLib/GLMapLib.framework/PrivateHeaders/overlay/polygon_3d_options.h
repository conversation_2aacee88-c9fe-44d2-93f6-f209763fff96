//
// Created by THY on 2025/4/15.
// Copyright © 2025 TencentMap. All rights reserved.
//

#ifndef _POLYGON_3D_OPTIONS_H_
#define _POLYGON_3D_OPTIONS_H_
#include "polygon_options.h"

namespace MAPAPI {

enum class ZOffsetType{
    Other,
    Zebra
};

class TXMAPSDK_EXPORT Polygon3DOptions : public PolygonOptions{
public:
    Polygon3DOptions();
    Polygon3DOptions(const Polygon3DOptions & other);
    ~Polygon3DOptions() override;

    Polygon3DOptions &     SetPoints(const std::vector<MapVector3d> & points);
    const std::vector<MapVector3d> & Get3DPoints() const;

    Polygon3DOptions &   SetFillColor(const MapColor4ub & fillColor) override;
    const MapColor4ub &  GetFillColor() const override;

    Polygon3DOptions & SetOffsetType(ZOffsetType zOffsetType);
    ZOffsetType GetZOffsetType() const;
    
    bool GetClickable() const;
    
    OverlayType GetType() const override { return OverlayType::Polygon3D; }

private:
    struct Impl;
    std::shared_ptr<Polygon3DOptions::Impl> GetImpl();
    std::shared_ptr<Polygon3DOptions::Impl> GetImpl() const;
};

}

#endif  // _POLYGON_3D_OPTIONS_H_
