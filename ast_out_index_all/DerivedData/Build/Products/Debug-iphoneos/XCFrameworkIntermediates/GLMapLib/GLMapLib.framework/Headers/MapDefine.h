//
//  MapDefine.h
//  GLMapLib2.0
//
//  Created by fullname on 2/16/17.
//  Copyright © 2017 nopwang. All rights reserved.
//

#ifndef MapDefine_h
#define MapDefine_h

#if defined(txmapsdk_EXPORTS)
#define TXMAPSDK_EXPORT __attribute__((visibility("default")))
#else
#define TXMAPSDK_EXPORT
#endif  // defined(txmapsdk_EXPORTS)

#if defined(UNITY_RENDER)
#define UNITY_EXPORT __attribute__((visibility("default")))
#else
#define UNITY_EXPORT
#endif
#define MAP_PI (3.14159265358979323846264338327950288)

#define MAP_EARTH_RADIUS (6378137.0)

#define MAP_EARTH_CIRCUM (2.0 * MAP_PI * MAP_EARTH_RADIUS)

#define MAP_MERCATOR_WIDTH ((double)(1 << 28))

#define MAP_INVALID_ID (0)

#define toRadian_d(x) ((x) * (MAP_PI / 180.0))
#define toDegree_d(x) ((x) * (180.0 / MAP_PI))
#define toRadian_f(x) ((float)(x) * (float)(MAP_PI / 180.0))
#define toDegree_f(x) ((float)(x) * (float)(180.0 / MAP_PI))
#define Is_Equal(l, r)          ((fabs((l) - (r)) < (1e-6)) ? true : false)
#define Is_Equal_D(l, r)          ((fabs((l) - (r)) < (1e-15)) ? true : false)

#endif /* MapDefine_h */
