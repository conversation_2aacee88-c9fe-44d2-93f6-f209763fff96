//
//  color_polyline_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/9.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_COLOR_POLYLINE_OPTIONS_H__
#define __MAPAPI_OVERLAY_COLOR_POLYLINE_OPTIONS_H__

#include <stdio.h>
#include <vector>
#include "overlay/overlay.h"
#include "overlay/polyine_options.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT ColorPolylineStyle {
public:
    enum class TXMAPSDK_EXPORT StyleType{
        Texture,
        ColorList,
        Composite,
        Dash
    };
  
  enum class DrawType{
      Normal,
      PassGrey,
      PassClear,
      Grown
  };
  virtual ~ColorPolylineStyle() = default;
  virtual StyleType GetType() = 0;
  DrawType draw_type = DrawType::PassGrey;
};

struct LineSection{
    int start_index;                           // route point start index
    int end_index;                             // route point end index
    int color;                                 // color index
};

class TXMAPSDK_EXPORT ColorPolylineOptions : public PolylineOptions{
public:
    ColorPolylineOptions();
    ColorPolylineOptions(const ColorPolylineOptions & other);
    ~ColorPolylineOptions();

    ColorPolylineOptions &               SetPoints(const std::vector<MapVector2d> & points);
    const std::vector<MapVector2d> &     GetPoints() const;
    
    ColorPolylineOptions &               SetPointsWithTraffic(const std::vector<MapVector2d> & trafficPoints);
    const std::vector<MapVector2d> &     GetPointsWithTraffic() const;
    
    ColorPolylineOptions &               SetSections(const std::vector<LineSection> & sections);
    const std::vector<LineSection> &     GetSections() const;
    
    ColorPolylineOptions &               SetStyle(ColorPolylineStyle * style);
    ColorPolylineStyle *                 GetStyle() const;
    
    virtual OverlayType GetType() const override { return OverlayType::ColorPolyline; }
    
private:
    struct Impl;
    std::shared_ptr<ColorPolylineOptions::Impl> GetImpl();
    std::shared_ptr<ColorPolylineOptions::Impl> GetImpl() const;
};

//使用纹理图片的方式
class TXMAPSDK_EXPORT ColorPolylineTextureStyle : public ColorPolylineStyle{
public:
    struct TextureStyleAtScale{
        float           start_scale;
        float           end_scale;
        float           width;       //路线宽度:(0,128], 单位为dp, 不需要乘density
        std::string     image_name;  //路线纹理名称
    };
    
    std::vector<TextureStyleAtScale> styles;
    
    virtual StyleType GetType() override { return StyleType::Texture; }
};

//使用颜色列表的方式
class TXMAPSDK_EXPORT ColorPolylineColorListStyle : public ColorPolylineStyle{
public:
    std::vector<MapVector4ub>   color_list;            //colorCount <= 16
    std::vector<MapVector4ub>   border_color_list;     //borderWidth == 0时，可以不设置borderColorList
    float                       width;                 //路线宽度:(0,128], 单位为dp, 不需要乘density
    float                       border_width;          //borderWidth * 2 < width
    
    virtual StyleType GetType() override { return StyleType::ColorList; }
};

/*
 * 虚线段风格
 * dashPattern 虚实长度(实线长度1；虚线长度1；实线长度2，虚线长度2.......),总长度不超过256
 * dashFillColor 为实线填充颜色
 * dashBorderColor 为实线描边颜色
 * dashBorderWidth 为描边宽度
 * passedColor 为走过路段颜色
 */
struct DashStyle {
    std::vector<int>    dashPattern;
    MapVector4ub        dashFillColor;
    MapVector4ub        dashBorderColor;
    int                 dashBorderWidth; //dp 单位
    MapVector4ub        passedColor;
};

/*
 * 虚线段信息
 * style指定虚线风格
 * sectionStartIndex 为虚线段的起始 section index
 * sectionEndIndex 为虚线段的结束 section index
 */
struct DashSectionInfo {
    DashStyle       style;
    int             sectionStartIndex;
    int             sectionEndIndex;
};

//实线与虚线混合模式：轮渡线需求
class TXMAPSDK_EXPORT ColorPolylineCompositeStyle : public ColorPolylineTextureStyle{
public:
    std::vector<DashSectionInfo> dashSections;
    
    virtual StyleType GetType() override { return StyleType::Composite; }
};

//虚线
class TXMAPSDK_EXPORT ColorPolylineDashStyle : public ColorPolylineStyle {
 public:
  std::vector<int> dashPattern;
  MapVector4ub dashFillColor;
  MapVector4ub dashBorderColor;
  int dashBorderWidth;  // dp 单位
  int dashWidth;
  virtual StyleType GetType() override { return StyleType::Dash; }
};

}

#endif /* __MAPAPI_OVERLAY_COLOR_POLYLINE_OPTIONS_H__ */
