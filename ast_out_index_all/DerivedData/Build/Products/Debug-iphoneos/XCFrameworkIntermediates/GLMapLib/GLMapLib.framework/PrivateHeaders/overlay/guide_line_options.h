#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {

//车道变化点信息
enum LaneChangeType {
     kLaneChangeTypeNone = 0,
     /* 普通变道 */
     kLaneChangeTypeNormal = 1,
     /* 紧急变道，过了推荐变道位置后的提示 */
     kLaneChangeTypeUrgent = 2,
     /* 小折角，非变道类型，比如汇流、分流等处的车道 */
     kLaneChangeTypeSmallCorner = 3
 };

/**
  * 车道变化信息
  */
struct LaneChangeInfo {
     /* 变化位置开始索引 */
     int start_index;
     /* 变化位置结束索引 */
     int end_index;
     /* 车道变化个数 */
     int lane_change_num;
     /* 变道类型 */
     LaneChangeType lane_change_type;
 };

class MapImpl;
class TXMAPSDK_EXPORT GuideLineOptions : public OverlayOptions{
public:
    GuideLineOptions();
    GuideLineOptions(const GuideLineOptions & other);
    GuideLineOptions& operator=(const GuideLineOptions & other);
    ~GuideLineOptions();

    // 设置引导线
    GuideLineOptions & SetPoints(const std::vector<MapVector3d> & points);
    const std::vector<MapVector3d> & GetPoints() const;

    // 设置引导线路况信息
    GuideLineOptions & SetTrafficSections(const std::vector<ColorSection> & sections);
    const std::vector<ColorSection> & GetTrafficSections() const;

    // 设置变道信息
    GuideLineOptions & SetLaneChangeInfos(const std::vector<LaneChangeInfo> & lane_change_infos);
    const std::vector<LaneChangeInfo> & GetLaneChangeInfos() const;
    
    // 设置是否连接车标定位点
    GuideLineOptions & SetConnectLocation(bool is_connect_location);
    bool GetConnectLocation() const;
    
    // 设置是否偏航
    GuideLineOptions & SetYaw(bool is_yaw);
    bool GetYaw() const;

    GuideLineOptions & SetGuideLineColor(const long color);
    long GetGuideLineColor() const;

    /**
     * 宽度单位: 20级像素.  1像素=0.15米
     * https://km.woa.com/group/basemap/articles/show/297754
     */
    GuideLineOptions & SetGuideLineWidth(const float width);
    float GetGuideLineWidth() const;
    
    /**
     * 设置引导线起始部分渐变区域的长度，长度单位: 20级像素.  1像素=0.15米
     * https://km.woa.com/group/basemap/articles/show/297754
     */
    GuideLineOptions & SetStartGradientRatio(const float start_ratio);
    float GetStartGradientRatio() const;
    
    /**
     * 设置引导线结尾部渐变区域的长度，长度单位: 20级像素.  1像素=0.15米
     * https://km.woa.com/group/basemap/articles/show/297754
     */
    GuideLineOptions & SetEndGradientRatio(const float end_ratio);
    float GetEndGradientRatio() const;

    /**
     宽度动画时间
     */
    GuideLineOptions & SetAnimationTime(const float time);
    float GetAnimationTime() const;

    GuideLineOptions & SetYawColor(const long color);
    long GetYawColor() const;

    void CoordinateTransform(MapImpl * mapImpl);
    
    void CoordinateTransform();
    
    virtual OverlayType GetType() const override { return OverlayType::GuideLine; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
    /**
     设置被遮挡部分半透绘制的透明度，初始0.3
     @param iAlpha 透明度，范围[0,1]
     @return option
     */
    GuideLineOptions &SetAlpha(const float iAlpha);
    /**
     获取被遮挡部分半透绘制的透明度
     @return 透明度，范围[0,1]
     */
    float GetAlpha() const;
    
private:
    struct Impl;
    std::shared_ptr<GuideLineOptions::Impl> GetImpl();
    std::shared_ptr<GuideLineOptions::Impl> GetImpl() const;
};

}
#endif