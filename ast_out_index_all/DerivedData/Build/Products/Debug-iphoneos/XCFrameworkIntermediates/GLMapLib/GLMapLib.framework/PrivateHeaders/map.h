//
//  map.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/26.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_MAP_INCLUDE__
#define __MAPAPI_MAP_INCLUDE__

#include <stdio.h>
#include <string>
#include "base/map_define.h"
#include "base/map_options.h"
#include "base/compass.h"
#include "base/locator.h"
#include "base/map_camera.h"
#include "base/map_layers.h"
#include "base/map_operator.h"
#include "base/tile_overlay_manager.h"
#include "base/map_observer.h"
#include "overlay/overlay.h"
#include "overlay/model_3d_options.h"
#include "overlay/road_area.h"
#include "overlay/road_companionArea.h"
#include "animation/animation_controller.h"
#include "base/map_core.h"
#include "agilemap/agilemap_controller.h"

namespace MAPAPI {

class MapCore;
class MapImpl;

class TXMAPSDK_EXPORT ImageLoader {
public:
    virtual ~ImageLoader() {
        
    }
    virtual TMBitmap* OnLoadImage(const std::string & imageName) = 0;
    virtual std::string GetResourceTag() {
        return "";
    }
};

class TXMAPSDK_EXPORT Map{
public:
    /**
     * 内部创建引擎句柄
     */
    Map(const MapOptions & options);
    
    /**
     * 共享外部调用MapCreate创建的引擎句柄
     * 适用于SDK层调用的是C接口，但是业务层（如MapBiz）调用的是C++接口。
     */
    Map(void * world, MapCoordinateType coordinateType);

    /**
     * @param isInternal 参数用于标识是否是引擎内部创建的Map,如果是true，则不会注册回调信息
     */
    Map(void * world, MapCoordinateType coordinateType,bool isInternal);

    
    virtual ~Map();
    
    /**
     * 地图的一些控制接口
     */
    MapController & GetController();
    
    /**
     * 底图核心渲染相关接口，此接口为私有，一般只由SDK层调用。
     */
    MapCore & GetMapCore();
        
    /**
     * 获取地图相机控制器：set/get 地图的center, scale, skew, rotate等
     */
    MapCamera & GetCamera();
    
    /**
     * 获取自车标对象
     */
    Locator & GetLocator();
    
    /**
     * 获取地图罗盘对象
     */
    Compass & GetCompass();
    
    /**
     * 样式控制器
     */
    StyleOperator & GetStyleOperator();
    
    /**
     * 鹰眼图控制器
     */
    OverviewOperator & GetOverviewOperator();
    
    /**
     * 底图文字控制器
     */
    AnnotationOperator & GetAnnotationOperator();
    
    /**
     * 路况图层
     */
    TrafficLayer & GetTrafficLayer();
    
    /**
     * 卫星图图层
     */
    SatelliteLayer & GetSatelliteLayer();
    
    /**
     * 室外楼块图层
     */
    OutdoorBuildingLayer & GetOutdoorBuildingLayer();
    
    /**
     * 室内图图层
     */
    IndoorBuildingLayer & GetIndoorBuildingLayer();
    
    /**
     * 封路图层
     */
    BlockRouteLayer & GetBlockRouteLayer();
    
    /**
     * 街景图层
     */
    StreetViewLayer & GetStreetviewLayer();
    
    /**
     * 动态POI图层
     */
    DynamicPOILayer & GetDynamicPOILayer();
    
    /**
     * 手绘图图层
     */
    HandDrawLayer & GetHandDrawLayer();

	/**
	 * 获取聚类图层
	 * @return
	 */
	ClusterGroupLayer & GetClusterGroupLayer();

    /**
     * 栅格瓦片图层
     */
    TileOverlayManager & GetTileOverlayManager();
    
    /**
     * 动画控制接口
     */
    AnimationController & GetAnimationController();
    
    /**
     * 添加3D模型的材质数据。
     *
     * 如果同一种模型类型的材质多次添加，后添加的会覆盖之前添加的。
     */
    void AddMaterial(MaterialOptions* materials, int materials_count);

    /**
     * @brief 添加3D模型的材质数据
     * @note 不使用传入的Type，在材质池中的实际索引为返回值
     * @return 引擎内自动生成的该材质Type值，-1为无效值，代表创建失败
     * @todo 该接口作为临时方案，本质原因是引擎中材质池对外暴露Type值，后续应优化相关结构
     */
    int AddMaterialAndReturnType(const MaterialOptions& material);
  
    /**
     * 删除某种材质
     * @param spirit_type:材质类型
     */
    void RemoveMaterial(int spirit_type);

    /**
     * 修改材质缩放，若已经创建了模型，也会同步修改模型缩放
     * @param spirit_type 材质类型 //SpiritType 类型
     * @param scale 缩放倍数
     */
    void SetMaterialScale(int spirit_type, float scale);
    /**
     *
     * @param iSpiritType 材质类型 //SpiritType 类型
     * @return 缩放倍数
     */
    float GetMaterialScale(int iSpiritType);
    
    /**
     * 添加Overlay(Maker, Polygon, Circle等), Polyline 请调用 AddRouteOverlay
     *
     * demo code:
     * PolygonOptions op;
     * std::shared_ptr<Polygon> p = std::dynamic_pointer_cast<Polygon>(map->AddOverlay(op));
     */
    std::shared_ptr<Overlay> AddOverlay(const OverlayOptions & options);
    
    
    /**
     * 此接口是对上面AddOverlay调用的简化。
     *
     * demo code:
     * PolygonOptions op;
     * std::shared_ptr<Polygon> p = map->AddOverlay<Polygon>(op);
     */
    template<OverlayType>
    std::shared_ptr<OverlayType> AddOverlay(const OverlayOptions & options){
        return std::dynamic_pointer_cast<OverlayType>(AddOverlay(options));
    }

    /**
     * 添加路线相关的overlay，增加bLast 标记来标识是否是最后一条
     *
     *param options
     *param bLast 是否是本次添加的最后一条路线
     */
    std::shared_ptr<Overlay> AddRouteOverlay(const OverlayOptions & options, bool bLast = true);

    /**
     * 从地图上移除某个overlay
     */
    void RemoveOverlay(std::shared_ptr<Overlay> overlay);

    /**
     * 批量更新overlay
     */
    void ModifyOverlay(std::vector<std::shared_ptr<OverlayOptions> > options);

    /**
     * 添加图片加载器
     */
    void AddImageLoader(ImageLoader * loader);
    
    /**
     * 移除某个图片加载器
     */
    void RemoveImageLoader(ImageLoader * loader);
    
    /**
     * 添加地图事件监听器
     */
    void AddMapObserver(std::shared_ptr<MapObserver> observer);
    
    /**
     * 移除某个地图事件监听器
     */
    void RemoveMapObserver(std::shared_ptr<MapObserver> observer);
    
    /**
     * 解绑某个地图的回调，主线程中调用
     */
    void DetachMapCallback();
  
    /**
     * 设置下载能力回调
     */
    void SetCustomNetProvider(NetProvider* net_provider);
    
    /**
     * 获取和设置底图矢量缓存策略天数
     */
    void GetFileCacheDays(int &strategyDays_m, int &togetherDays_n);
    bool SetFileCacheDays(int strategyDays_m, int togetherDays_n);

#ifndef IS_SLIM
    /**
     * 获取和设置底图矢量瓦片的的优先加载模式
     * mode 对应定义为MapTileLoadMode
     */
    int GetMapTileLoadMode();
    /**
     *
     * @param mode
     * 0:离线优先加载模式
     * 1:在线优先加载模式
     * 2:仅加载离线数据
     * 3:仅加载在线数据
     */
    bool SetMapTileLoadMode(int mode);

    /**
     * 设置世界图开关状态
     * @param isCountry 是否设定全国级别(全国级别指数据级别3/5/7, 更大为城市级别) true:全国级别,false:城市级别
     * @param enable    是否开启, true:开启状态, false:关闭状态
     */
    bool SetSupportWorldMapEnable(bool isCountry, bool enable);
    /**
     * 获取世界图开关状态
     * @param isCountry 是否是全国级别, true:全国级别, false:城市界别
     * @return true:开启状态, false:关闭状态
     */
    bool IsSupportWorldMap(bool isCountry);

    /**
     * 设置 2D overlay 的屏幕碰撞策略
     */
    void SetOverlayStrategy(std::weak_ptr<MAPAPI::ICollisionStrategy> &strategy);
#endif

    /**
     * 设置Point类型Overlay按照屏幕距离降序绘制,即远的先绘制近的后绘制,近的压盖远的;
     * 说明: 1> 该接口开启后所有Point类型普通Overlay一起处理;
     *      2> 该接口一般用于导航场景,进入导航场景开启退出导航场景关闭;
     * @param enable true开启, false:关闭;
     */
     void SetMapMarkerRenderOrderByDepth(const bool enable);

     /**
     * 获取和设置蚯蚓线是否压盖楼块
     */
    bool GetIsRouteCoverBuilding(int route_id);
    void SetIsRouteCoverBuilding(bool isCover, int route_id);
    
    /**
     * 百变地图接口
     */
    AgileMapController &GetAgileMapController();
    
    /**
     * 添加地图canvas排版回调
     */
    void SetCanvasTyper(std::weak_ptr<ICanvasTyper> canvas_typer);
    
private:
    std::shared_ptr<MapImpl> impl_;
};

}

#endif /* __MAPAPI_MAP_INCLUDE__ */
