//
//  overlay_options.h
//  Pods
//
//  Created by l<PERSON><PERSON><PERSON> on 2023/9/13.
//

#ifndef __MAPAPI_OVERLAY_OVERLAY_OPTIONS_H__
#define __MAPAPI_OVERLAY_OVERLAY_OPTIONS_H__

#include <stdio.h>
#include <string>
#include <cstring>
#include <vector>
#include "base/map_define.h"
#include "animation/animation_enable_object.h"

namespace MAPAPI {


enum class TXMAPSDK_EXPORT OverlayType{
    Marker = 1,
    GroupIconMarker,
    Polyline,
    ColorPolyline,
    Polygon,
    Circle,
    CustomIconMarker,
    Lane,
    Model3D,
    GuideLine,
    TargetLane,
    TurnWall,
    CommonLane = 17,
    SpecialLane = 18,
    RegionLane = 19,
    GuideArea,
    GuideArrow,
    RoadArea,
    RoadCompanionArea,
    Locator,
    EqualWidthGuideLine,
    RoadAreaArrow,
    ImageLabelMarker,
    RoadRecommendedLane,
    ModelGroup,
    TrackedModel,
    AgileMarker,
    Polygon3D
};

enum class TXMAPSDK_EXPORT OverlayAvoid3dType{
    <PERSON><PERSON><PERSON>, //L1,L4,Universal Model
    Obj_3D_Poiid,  //L1 or L4 attached with certain poiid
    None
};

/**
 * Overlay外部挂载属性
 */
class TXMAPSDK_EXPORT OverlayCustomAttr {
 public:
  virtual ~OverlayCustomAttr() = default;
};

class TXMAPSDK_EXPORT OverlayOptions : public std::enable_shared_from_this<OverlayOptions>{
public:
    virtual OverlayType GetType() const = 0 ;
    virtual ~OverlayOptions() = default;
    
    /**
    * 设置overlay优先级，默认优先级为0
    *
    * @param priority 优先级，支持负数
    */
    virtual void SetPriority(int priority);
    
    /**
    * 获取overlay优先级，默认优先级为0
    *
    * @return 优先级，可能为负数
    */
    virtual int GetPriority() const;
    
    /**
    * 设置显示的比例尺范围
    *
    * @param nMinScaleLevel 最小比例尺Level，最小取值为0
    * @param nMaxScaleLevel 最大比例尺Level, 最大取值为30
    */
    virtual void SetScaleLevelRange(int nMinScaleLevel, int nMaxScaleLevel);
    
    /**
    * 获取显示的比例尺范围
    *
    * @param nMinScaleLevel 最小比例尺Level，最小取值为0
    * @param nMaxScaleLevel 最大比例尺Level, 最大取值为30
    */
    virtual void GetScaleLevelRange(int& nMinScaleLevel, int& nMaxScaleLevel) const;
    /**
     * 设置overlay隐藏状态
     */
    virtual void SetHidden(bool hidden);
    
    /**
     * 获取overlay隐藏状态
     */
    virtual bool GetHidden() const;
    
    /**
     * 获取overlay的ID
     */
    virtual void SetID(int id);
    
    /**
     * 获取overlay的ID
     */
    virtual int GetID() const;

    /**
     * 拷贝返回一个对象，默认返回自身
     * 子类需要重载实现返回不同的拷贝对象
     * 添加该接口的原因是 MapImpl::ModifyOverlay 时传入的option上层还会再修改,需要在调用时保存当时的状态数据
     * @return 拷贝后的option对象，未重载该函数的类对象返回自身，重载的返回一个新的对象
     */
    virtual std::shared_ptr<OverlayOptions> CopyModifyData();
    
    virtual std::string Description() const;

    /**
     * 为创建的 marker 绑定一组自定义属性
     */
    void SetCustomAttr(std::shared_ptr<OverlayCustomAttr> ext);

    /**
     * 获取 marker 的自定义属性
     */
    std::shared_ptr<OverlayCustomAttr> GetCustomAttr() const;

    struct Impl;
    
protected:
    std::shared_ptr<Impl> impl_;
    
private:
    std::shared_ptr<Impl> GetImpl();
    std::shared_ptr<Impl> GetImpl() const;
};

}


#endif /* __MAPAPI_OVERLAY_OVERLAY_OPTIONS_H__ */
