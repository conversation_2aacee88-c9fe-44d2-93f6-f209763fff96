//
//  animation_observer.h
//  GLMapLib2.0
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_ANIMATION_OBSERVER_H__
#define __MAPAPI_ANIMATION_OBSERVER_H__

#include <stdio.h>
#include <memory>
#include "base/map_define.h"

namespace MAPAPI {

class Animation;

class TXMAPSDK_EXPORT AnimationObserver{
public:
    /**
     * 动画完成通知
     */
    virtual void OnAnimationFinished(std::shared_ptr<Animation> animPt) = 0;
    
    /**
     * 动画取消通知，暂未实现
     */
    virtual void OnAnimationCancled(std::shared_ptr<Animation> animPt) = 0;
};

}

#endif /* __MAPAPI_ANIMATION_OBSERVER_H__ */
