//
//  marker.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/2.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_MARKER_H__
#define __MAPAPI_OVERLAY_MARKER_H__

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/marker_options.h"
namespace tencentmap{
class Overlay;
}

namespace MAPAPI {

/**
 * 返回避让信息，
 * selected_position：是否占位成功，-1 表示占位失败；非负值，表示占位成功，值代表占位的具体位置（groupmarker可能有多个候选点）
 * avoid_killers 占位失败后，需要告知 失败的原因
 * */
struct AvoidResultInfo{
    int selected_position;
    std::vector<RankKiller> avoid_killers;
    AvoidResultInfo(int selected_position) : selected_position(selected_position) {
        avoid_killers.clear();
    }
};


class TXMAPSDK_EXPORT Marker : public Overlay {
    friend class OverlayFactory;
public:
     Marker();//for unit test only
    
    /**
     * 修改Marker所有属性
     *
     */
    virtual void SetOptions(const OverlayOptions & options);
    /**
     * 更新Overlay坐标
     * 功能：按地理坐标对marker进行移动。
     * 如相应overlay不存在该属性，更新无任何影响
    */
    virtual void SetCoordinate(MapVector2d coordinate);

    /**
     * 更新Overlay坐标
     * 功能：按地理坐标对marker进行移动。
     * 在渲染线程调用时直接生效，仅能在渲染线程调用
    */
    void SetCoordinateDirectly(MapVector2d coordinate);
  
    /**
     * 更新Overlay坐标
     * 功能：按地理坐标对marker进行移动。
     * 如相应overlay不存在该属性，更新无任何影响
     */
    virtual void SetCoordinate(MapVector3d coordinate);

    /**
     * 获取overlay地理坐标
     *
     * @return 地理坐标，如查找失败，返回(0.0，0.0)
     */
    virtual MapVector2d GetCoordinate();

    /**
     * 获取overlay地理坐标
     * @return 地理坐标，如查找失败，返回(0.0，0.0, 0.0)
     */
    virtual MapVector3d GetCoordinate3d();
    
    /**
     * 更新Overlay在屏幕坐标系下，相对于当前坐标的一个偏移量。默认为（0,0）。
     * 功能：按像素坐标对marker进行移动。支持动画。
     * 如相应overlay不存在该属性，更新无任何影响
     * @param screenOffset 屏幕坐标偏移量，像素坐标，x轴向右，y轴向下。
     */
    virtual void SetScreenOffset(MapVector2f screenOffset);

    /**
     * ⚠️ 警告: 只能在渲染线程中执行!
     *          如果不在渲染线程, debug版本会触发断言崩溃, release版本则不会有任何效果
     *          对应错误日志: only work for render thread
     * 功能: 立即更新 Marker 的布局更新
     * @param screenOffset 屏幕坐标偏移量，像素坐标，x轴向右，y轴向下
     */
    virtual void SetScreenOffsetDirectly(MapVector2f screenOffset);

    /**
     * 更新混色
     * 如相应overlay不存在该属性，该操作无任何影响
     */
    virtual void SetMixColor(const MapColor4ub& mixColor, float colorAnimDuration = 0.0);

    /**
     * 更新子图标混色（目前动画图属于子图标，所有子图标统一修改）
     * 如相应overlay不存在该属性，该操作无任何影响
     */
    virtual void SetSubMixColor(const MapColor4ub& mixColor, float colorAnimDuration = 0.0);

    /**
     * 更新Overlay角度
     * 如相应overlay不存在该属性，该操作无任何影响
     */
    virtual void SetAngle(float angle);
        
    /**
     * 更新Overlay角度（非异步，仅在渲染线程调用）
     * 如相应overlay不存在该属性，该操作无任何影响
     */
    virtual void SetAngleDirectly(float angle);
  
    /**
     * 更新子图标角度（目前动画图属于子图标，所有子图标统一修改）
     * 如相应overlay不存在该属性，该操作无任何影响
     * @param 每个子图标的角度
     * @return 设置成功返回true，失败反回false
     */
    virtual bool SetSubAngles(const std::vector<float>& angles);
    
    /**
     * 更新子图标角度（目前动画图属于子图标，所有子图标统一修改）
     * 非异步更新，仅在渲染线程调用
     * @param 每个子图标的角度
     * @return 设置成功返回true，失败反回false
     */
    virtual bool setSubAnglesDirectly(const std::vector<float>& angles);
    /**
     * 获取overlay角度
     *
     * @return 角度，如查找失败，返回0.0f
     */
    virtual float GetAngle();
    
    /**
     * 更新overlay透明度
     * 如相应overlay不存在该属性，更新无任何影响
     * @param alpha 透明度（0.0f ~ 1.0f）
     */
    virtual void SetAlpha(float alpha) override;
    
    /**
     * 获取overlay透明度
     *
     * @return 透明度，如查找失败，返回1.0f
     */
    virtual float GetAlpha();
    
    /**
     * 更新overlay缩放比例
     * 如相应overlay不存在该属性，更新无任何影响
     * @param scale 缩放比例
     */
    virtual void SetScale(MapVector2f scale);

    /**
     * 更新marker翻转参数
     * @param flip 纹理镜像翻转参数（是否水平翻转，是否垂直翻转）
     */
    void SetFlip(MapVector2ub flip);
  
    /**
     * 获取overlay缩放比例
     *
     * @return 缩放比例，如查找失败，返回(1.0f, 1.0f)
     */
    virtual MapVector2f GetScale();
    
    /**
     * 更新Overlay图片
     *
     * @param imageName 图片名
     * @param anchorPoint 锚点
     */
    virtual void SetImage(const std::string &imageName, MapVector2f anchorPoint);

    /**
     * 更新锚点
     *
     * @param anchorPoint 锚点
     */
    virtual void SetAnchorPoint(MapVector2f anchorPoint);

    /**
     * 更新动画图标的锚点（目前动画图标属于子图标）
     *
     * @param anchorPoints 锚点（数量必需与当前子图标数量相等）
     * @return 成功返回true，失败返回false
     */
    virtual bool SetSubAnchorPoints(const std::vector<MapVector2f>& anchorPoints);
  
    /**
     * 强制overlay同步加载资源，不延迟展示
     *
     * @param bForceLoad 是否强制加载
     */
    virtual void SetForceLoad(bool bForceLoad);
    
    /**
     * 获取overlay对应的屏幕区域
     *
     * @return 屏幕区域，如查找失败，返回(0.0f, 0.0f, 0.0f, 0.0f)
     */
    virtual MapVector4f GetScreenArea();
    
    /**
     * 设置marker在遇到更高优先级的marker时，是否避让其它marker.
     * 默认false，不避让，直接被高优先级marker压盖。
     */
    virtual bool SetAvoidEnabled(bool isAvoidEnabled);
    
    enum class TXMAPSDK_EXPORT MarkerAvoidRouteType
    {
        None      = 0,                   // Marker 不避让任何路线
        All       = 1,                   // Marker 避让所有蚯蚓线；引擎计算
        Appointed = 2,                   // Marker 避让指定路线，上层设置
        MainRoad  = 3                    // Marker 避让主路（内部默认最上层一条蚯蚓线就是主路）
    };
    
    struct AvoidRouteRule{
        MarkerAvoidRouteType  avoid_type;                //避让蚯蚓线规则
        std::vector<std::shared_ptr<Overlay> > avoid_routes;
    };
    
    enum class TXMAPSDK_EXPORT AvoidType{
        UI,
        Route,
        Locator
    };
    
    /**
     * 设置marker避让路线的规则
     */
    virtual bool SetAvoidRouteRule(const struct AvoidRouteRule & rule);
    
    /**
     * 设置marker避让无法显示时，是否将因与avoid_type的元素（Route、UI、Locator）避让而失效的布局重新恢复有效，仅在avoidenable为true时生效
     */
    virtual void SetReviveWhenAvoided(AvoidType avoid_type, bool revive_if_avoided);
    
    /**
     * 设置能够被自车标避让影响布局，不调用默认为false，与926线上一致，与最终能否显示无关
     */
    virtual void SetAvoidLocator(bool need_avoid);
    
    /**
     * 设置能够被UI区域避让影响布局，不调用默认为true，与926线上一致，与最终能否显示无关
     */
    virtual void SetAvoidUI( bool need_avoid);
    
    struct AvoidDetailedRule{
        /**
         * 当前Marker的数据源类型(0-99 保留为引擎内部使用, 100以上端上自行设定维护)
         */
        int    source_type;
        
        /**
         * 与相同数据源之间避让的最小预留距离
         * (负数 代表同类之间不进行避让, 其他代表 设定元素避让情况下 同类元素之间的最小间隔)
         */
        int    min_margin_with_same_source_type;
    }AvoidDetailedRule;
    
    /**
     * 为某个Marker设置更详细的避让规则
     */
    virtual void SetAvoidDetailedRule(const struct AvoidDetailedRule & pRule);
    
    /**
     * 为当前marker(子marker)设置一个MainMarker(主marker)，添加一层虚拟的关联关系。mainMarkerID=-1，清除关系。
     * 如果MainMarker被避让掉，当前marker也一同被避让掉；
     * 如果MainMarker没有被避让掉，当前marker按照自身情况避让。
     * 如果用户主动对MainMarker调用SetHidden操作，当前marker不受影响。
     */
    virtual void SetMainMarker(const Marker & mainMarker);
    
    virtual AnimationOwnerEnum GetAnimationOwnerType() const override;
    
    virtual void SetTextOptions(const TextOptions & textOptions);
    virtual TextOptions & GetTextOptions();

    enum class TXMAPSDK_EXPORT CullType
    {
        None               = 0,                 // 无裁剪逻辑
        Annotation         = 1,                 // 和底图文字一致
        RoadClosureMarker  = 2,                 // 和封路marker一致
    };
    /**
     * Marker被裁剪的类型，默认无裁剪。目前只支持Icon, GroupIcon, Annotation
     *
     * @param cullType 裁剪的类型
     */
    virtual void SetCullType(CullType cullType);
    
    /**
     * 设置2dmarker是否可以被3d物体压盖， 默认为false。命名应该为3dover2d
     *
     * @param cover_by_3d  true可以压盖，fale不可以。
     */
    void SetOver3D(bool cover_by_3d);
     /**
     * 获取2dmarker是否可以被3d物体压盖状态
     *true可以压盖，fale不可以。
     */
    bool GetOver3D();
    /*
     *设置icon是否使用室内图高度
     */
    void SetIconEnableIndoorHeight(bool enableHeight);

    /**
     *改变marker的位置，进行碰撞测试，
     *返回碰撞结果
     *@param try_positions 经纬度坐标(含高度)
     */
     AvoidResultInfo TryGetAvoidState(const std::vector<MapVector3d>& try_positions);
    
    /*
     *设置摇杆的尾部点,实际效果起始点不动，尾部点随气泡动
     * @param end 尾部点
     * @param reverse_x 杆左右反转
     */
    void SetRockerLineEnd(const MapVector2f& end, bool reverse_x = false);

    /**
     * 设置杆点和线图片
     */
    void UpdateRockerImage(const std::string& dot_name, const std::string& line_name);

    /**
     * 添加序列帧动画
     */
    void SetAnimationImageParam(const std::vector<MAPAPI::AnimationImageParam> &animations,
                                const std::string& bg_image_name,
                                std::shared_ptr<TMBitmap> bg_image);
    
    /**
     * 文字避让的生效区间
     */
    void SetAvoidAnnoLevel(int start_level, int end_level);

protected:
    virtual void OnAnimationStart(std::shared_ptr<Animation> anim, const std::string& anim_value) override;
    virtual void OnAnimationFinish(std::shared_ptr<Animation> anim, const std::string& anim_value) override;
    
private:
    std::shared_ptr<MarkerOptions> Options() const{
        return std::dynamic_pointer_cast<MarkerOptions>(options_);
    }
    
    std::shared_ptr<tencentmap::Overlay> GetImpl();
    
private:
    using Overlay::Overlay;
};

}


#endif /* __MAPAPI_OVERLAY_MARKER_H__ */
