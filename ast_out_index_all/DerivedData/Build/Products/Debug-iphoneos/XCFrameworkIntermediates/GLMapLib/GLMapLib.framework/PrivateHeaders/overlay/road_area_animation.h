//
// Created by cuipanpan on 2022/6/24.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "../base/map_define.h"
#include "overlay.h"
#include "had_structure.h"
namespace MAPAPI {

class TXMAPSDK_EXPORT RoadAreaAnimation{
public:
    RoadAreaAnimation();
    RoadAreaAnimation(const RoadAreaAnimation & other);
    RoadAreaAnimation& operator=(const RoadAreaAnimation & other);
    ~RoadAreaAnimation();
    
    /**
     alpha擦除动画效果
     @param info 动画信息
     */
    void SetFadeInfo(const std::vector<mapbase::HAD::FadeinInfo>& info);
    
    /**
     获取擦除动画信息
     */
    const std::vector<mapbase::HAD::FadeinInfo>& GetFadeInfo() const;
    
    /**
     UV动画
     @param info UV动画信息
     */
    void SetUVInfo(const mapbase::HAD::UVCtrlItem& info);
    
    /**
     获取UV动画信息
     */
    const mapbase::HAD::UVCtrlItem& GetUVInfo() const;
    
    /**
     闪烁动画
     @param info 闪烁动画信息
     */
    void SetAlphaInfo(const mapbase::HAD::AlphaCtrlItem& info);
    
    /**
     获取闪烁动画信息
     */
    const mapbase::HAD::AlphaCtrlItem& GetAlphaInfo() const;
    
private:
    mapbase::HAD::UVCtrlItem uv_ctrl_item_;
    mapbase::HAD::AlphaCtrlItem alpha_ctrl_item_;
    std::vector<mapbase::HAD::FadeinInfo> fade_info_vec_;
};

}
#endif //NOT_USE_4K_ELEMENT

