//
// Created by virgil on 2021/11/9.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "overlay/overlay.h"
#include <vector>

namespace MAPAPI {

class MapImpl;
class TXMAPSDK_EXPORT GuideAreaOptions : public OverlayOptions {
public:
    GuideAreaOptions();
    GuideAreaOptions(const GuideAreaOptions& options);
    GuideAreaOptions& operator=(const GuideAreaOptions & other);
    ~GuideAreaOptions();

    virtual OverlayType GetType() const override;
    /**
     * 设置引导面左边线数据 需要与右边线的数据一一对应
     * @param left_points 左边线数据
     * @param break_index 索引
     */
    void SetLeftpoints(const std::vector<MapVector3d> &left_points,
                       const std::vector<uint32_t> &break_index);

    const std::vector<MapVector3d>& GetLeftPoints() const;

    const std::vector<uint32_t>& GetLeftBreakIndex() const;
    /**
     * 设置引导面右边线数据
     * @param right_points 需要与左边线的数据一一对应
     */
    void SetRightPoints(const std::vector<MapVector3d> &right_points,
                        const std::vector<uint32_t> &break_index);

    const std::vector<MapVector3d>& GetRightPoints() const;

    const std::vector<uint32_t>& GetRightBreakIndex() const;
    
    std::vector<MapVector3d>& GetLeftPoints();
    std::vector<MapVector3d>& GetRightPoints();

public:
    void CoordinateTransform(MapImpl * mapImpl);
    
private:
    struct Impl;
    std::shared_ptr<GuideAreaOptions::Impl> GetImpl();
    std::shared_ptr<GuideAreaOptions::Impl> GetImpl() const;
};

class TXMAPSDK_EXPORT GuideAreaTrafficOptions : public OverlayOptions {
public:
    GuideAreaTrafficOptions();
    GuideAreaTrafficOptions(const GuideAreaTrafficOptions& options);
    GuideAreaTrafficOptions& operator=(const GuideAreaTrafficOptions & other);
    ~GuideAreaTrafficOptions();

    virtual OverlayType GetType() const override;

    /**
     * 设置引导面路况分布
     * @param sections 路况
     */
    void SetTrafficSections(const std::vector<ColorSection>& sections);
    /**
     * 获取路况
     * @return 路况信息
     */
     const std::vector<ColorSection>& GetTrafficSections() const;

public:
     void CoordinateTransform(MapImpl * mapImpl);
    
private:
    struct Impl;
    std::shared_ptr<GuideAreaTrafficOptions::Impl> GetImpl();
    std::shared_ptr<GuideAreaTrafficOptions::Impl> GetImpl() const;
};

}
#endif


