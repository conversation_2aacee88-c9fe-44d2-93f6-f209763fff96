// Copyright 2020 Tencent. All rights reserved.

#ifndef TXMAPSDK_INCLUDE_NET_HTTP_INTERFACE_H_
#define TXMAPSDK_INCLUDE_NET_HTTP_INTERFACE_H_

#include <atomic>
#include <cstdint>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace tencentmap {

class HttpCallback {
 public:
  virtual ~HttpCallback() {}

 public:
  /**
   * @brief HTTP数据回传接口
   *
   * @param req_id     HTTP请求 ID
   * @param http_code  HTTP返回码, 200, 404, 500
   * @param data       HTTP报文数据
   * @param size       HTTP报文数据长度
   * @return 处理成功返回true
   */
  virtual bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                              int32_t size) = 0;

  /**
   * @brief HTTP数据回传接口
   *
   * @param req_id     HTTP请求 ID
   * @param http_code  HTTP返回码, 200, 404, 500
   * @param data       HTTP报文数据
   * @param size       HTTP报文数据长度
   * @param headers    HTTP Headers
   * @return 处理成功返回true
   */
  virtual bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                              int32_t size, const std::map<std::string, std::vector<std::string>> &headers) {
    return OnHttpResponse(req_id, http_code, std::move(data), size);
  }
  /**
   * @brief HTTP数据回传出错接口
   *
   * @param req_id     HTTP请求 ID
   * @param error      出错码,-1:未知网络错误, 1:超时，2:取消，3:其他
   * @return 处理成功返回true
   */
  virtual bool OnHttpError(int32_t req_id, int32_t error) = 0;
};

class HttpInterface {
 public:
  HttpInterface() : inner_id_{1} {}
  virtual ~HttpInterface() {}

 public:
  int32_t generateRequestId() { return inner_id_.fetch_add(1); }

  /**
   * @brief Get请求
   * @param[in] req_id      请求ID
   * @param[in] url         url
   * @param[in] headers     HTTP头
   * @param[in] callback    请求回调
   * @param[in] timeout_ms  超时设置
   * @param[in] is_quic  是否为quic协议
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool RequestHttpGet(int32_t req_id, const std::string &url,
                              const std::map<std::string, std::string> &headers,
                              std::weak_ptr<HttpCallback> callback, int32_t timeout_ms = 10000,
                              bool is_quic = false) = 0;

  /**
   * @brief Post请求
   * @param[in] req_id      请求ID
   * @param[in] url         url
   * @param[in] headers     HTTP头
   * @param[in] data        发送数据
   * @param[in] size        数据大小
   * @param[in] callback    请求回调
   * @param[in] timeout_ms  超时设置
   * @param[in] is_quic  是否为quic协议
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool RequestHttpPost(int32_t req_id, const std::string &url,
                               const std::map<std::string, std::string> &headers,
                               std::unique_ptr<int8_t[]> data, int32_t size,
                               std::weak_ptr<HttpCallback> callback, int32_t timeout_ms = 10000,
                               bool is_quic = false) = 0;

  /**
   * @brief 取消请求
   * @param[in] req_id    请求ID
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool CancelRequest(int32_t req_id) = 0;

 private:
  std::atomic_int inner_id_;
};
}  // namespace tencentmap

#endif  // TXMAPSDK_INCLUDE_NET_HTTP_INTERFACE_H_
