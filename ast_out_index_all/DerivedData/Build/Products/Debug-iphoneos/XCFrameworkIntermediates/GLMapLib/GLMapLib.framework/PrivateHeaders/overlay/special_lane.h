//
// Created by cuipanpan on 2021/7/7.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "overlay/overlay.h"
#include "overlay/special_lane_options.h"

namespace tencentmap{
class Macro4KSpecialLane;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT SpecialLane : public Overlay{
    friend class OverlayFactory;
public:
    void SetOptions(const SpecialLaneOptions & options);
        
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KSpecialLane> GetImpl();
};

}
#endif