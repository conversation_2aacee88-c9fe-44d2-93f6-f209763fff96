//
//  map_kit.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/11.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_MAP_KIT_INCLUDE__
#define __MAPAPI_MAP_KIT_INCLUDE__

/**
 * map_kit.h作为对外的出口。
 * 外部只需要include此头文件即可。
 */
#include "map.h"
#include "base/TMBitmapContext.h"
#include "base/MapDataType.h"
#include "base/MapDefine.h"
#include "base/map_util.h"
#include "base/map_core.h"
#include "base/map_observer.h"
#include "base/map_controller.h"
#include "base/map_operator.h"
#include "base/map_camera.h"
#include "base/map_layers.h"
#include "base/tile_overlay_manager.h"
#include "base/locator.h"
#include "base/compass.h"
#include "animation/animation_controller.h"
#include "animation/animation.h"
#include "animation/animation_value.h"
#include "animation/animation_observer.h"
#include "overlay/marker.h"
#include "overlay/group_icon_marker.h"
#include "overlay/image_label_marker.h"
#include "overlay/polyline.h"
#include "overlay/color_polyline.h"
#include "overlay/polygon.h"
#include "overlay/circle.h"
#include "overlay/custom_icon_marker.h"
#include "overlay/lane.h"
#include "overlay/model_3d.h"
#include "overlay/guide_line.h"
#include "overlay/target_lane.h"
#include "overlay/turn_wall.h"
#include "overlay/guide_area.h"
#include "overlay/guide_arrow.h"
#include "overlay/special_lane.h"
#include "overlay/common_lane.h"
#include "overlay/road_recommended_lane.h"
#include "overlay/region_lane.h"
#include "overlay/road_area.h"
#include "overlay/road_companionArea.h"
#include "overlay/road_area_pro.h"
#include "overlay/road_area_arrow.h"
#include "overlay/model_group.h" 
#include "overlay/tracked_model.h"
#include "overlay/polygon_3d.h"

#endif /* __MAPAPI_MAP_KIT_INCLUDE__ */
