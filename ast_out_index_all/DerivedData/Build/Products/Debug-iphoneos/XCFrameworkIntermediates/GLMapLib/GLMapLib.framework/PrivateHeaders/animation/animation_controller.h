//
//  map_animation.h
//  GLMapLib2.0
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/27.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_ANIMATION_CONTROLLER_INCLUDE__
#define __MAPAPI_ANIMATION_CONTROLLER_INCLUDE__

#include <stdio.h>
#include <string>
#include "base/map_define.h"
#include "animation/animation_value.h"
#include "animation/animation.h"

namespace MAPAPI {

class Animation;
class AnimationEnableObject;
class AnimationObserver;

class TXMAPSDK_EXPORT AnimationController : public MapComponent {
    friend class Map;
public:
    
    /**
     * 创建动画
     *  @param animObj 需要做动画对象， 比如 Marker
     *  @param animType 动画类型
     */
    std::shared_ptr<Animation> CreateAnimation(std::shared_ptr<AnimationEnableObject> animObj, AnimationType animType);
    
    /**
     * 设置动画精度，控制动态降帧。动画精度最高时，动态降帧可能被关闭
     *
     * @param type 精度
     */
    void SetAnimationQuality(MapAnimationQualityType type);
    
    /**
     * 启动一个动画
     *
     * @param anim  要执行动画对象
     */
    void StartAnimation(std::shared_ptr<Animation> anim);
    
    /**
     * 取消一个动画, 暂未实现
     *
     * @param anim 执行动画对象
     * @param toTarget  为true时直接执行到目标状态，false时停止在当前状态
     */
    void StopAnimation(std::shared_ptr<Animation> anim, bool toTarget = false);
    
    /**
     * 停止所有动画, 暂未实现
     * @param toTarget  为true时直接执行到目标状态，false时停止在当前状态
     */
    void StopAllAnimation(bool toTarget = false);
    
    /**
     * 某个对象是否有某类动画, 暂未实现
     * @param object 执行动画对象
     * @param property 动画的属性
     * @return  是否包含某个动画
     */
    bool HasAnimation(const std::shared_ptr<AnimationEnableObject> object,const std::string & property);
};

}

#endif /* __MAPAPI_ANIMATION_CONTROLLER_INCLUDE__ */
