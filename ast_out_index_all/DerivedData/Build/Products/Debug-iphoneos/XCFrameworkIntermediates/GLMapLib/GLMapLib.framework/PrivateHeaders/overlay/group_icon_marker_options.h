//
//  group_icon_marker_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/8.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_GROUP_ICON_MARKER_OPTIONS_H__
#define __MAPAPI_OVERLAY_GROUP_ICON_MARKER_OPTIONS_H__

#include <stdio.h>
#include <string.h>
#include "base/map_define.h"
#include "overlay/overlay.h"
#include "marker_options.h"
namespace MAPAPI {

struct TXMAPSDK_EXPORT SubIcon{
    SubIcon();
    SubIcon(const std::string & _image_name, const MapVector2f & _anchor, const MapEdgeInsets & _edge);
    SubIcon(const std::string & _image_name, const MapVector2f & _anchor, const MapEdgeInsets & _edge,const std::vector<MAPAPI::AnimationImageParam>& animations);
    
    std::string     image_name; //纹理图片名称
    std::shared_ptr<TMBitmap>       bg_bitmap;//背景图片纹理
    MapVector2f     anchor;    //锚点
    MapEdgeInsets   edge;      //图片用于碰撞的矩形框相对图片四边的偏移, 单位dp
    std::vector<MAPAPI::AnimationImageParam> animations;//动画参数
};

//TODO:接口存在设计问题：GroupIconMarker继承自Marker，但是GroupIconOptions没有继承自MarkerOptions
class TXMAPSDK_EXPORT GroupIconMarkerOptions : public OverlayOptions{
public:
    GroupIconMarkerOptions();
    ~GroupIconMarkerOptions();

    /**
     * 添加气泡候选坐标，在现有坐标列表之上追加，不能超过8个
     */
    GroupIconMarkerOptions & AddPoint(const MapVector3d & point);
    
    /**
     * 设置气泡候选坐标列表，不能超过8个，覆盖之前设置的坐标列表
     */
    GroupIconMarkerOptions & SetPoints(const std::vector<MapVector3d> & points);
    const std::vector<MapVector3d> & GetPoints() const;
    
    /**
     * 气泡的候选子marker，不要超过8个
     */
    GroupIconMarkerOptions & AddSubIcon(const SubIcon & subIcon);
    GroupIconMarkerOptions & SetSubIcons(const std::vector<SubIcon> & subIcons);
    const std::vector<SubIcon> & GetSubIcons() const;
    
    enum class TXMAPSDK_EXPORT VisualRectShowType{
        None  = 0 ,                // 不需要显示在某个指定可视范围内（如屏幕内）
        First = 1 ,                // 当组合气泡与其它Overlay相压盖，又与屏幕范围冲突时，优先显示在屏幕内，可以压盖其它Overlay
        Last  = 2 ,                // 当组合气泡与其它Overlay相压盖，又与屏幕范围冲突时，优先显示在屏幕外，不能压盖其它Overlay
    };
    
    /**
     * 是否需要优先显示在指定可视范围内
     */
    GroupIconMarkerOptions & SetVisualRectShowType(enum VisualRectShowType visual_rect_show_type);
    enum VisualRectShowType  GetVisualRectShowType() const;
    
    /**
     * 指定的可视的屏幕像素范围
     * visualRectShowType = ShowInVisualRect_None时不需要设置
     */
    GroupIconMarkerOptions & SetVisualRect(const TMRect & visualRect);
    const TMRect &           GetVisualRect() const;
    
    /**
     * 是否可以点击响应
     */
    GroupIconMarkerOptions & SetClickable(bool clickable);
    bool                     GetClickable() const;
    
    /**
     * 底图文字是否对其进行避让
     */
    GroupIconMarkerOptions & SetAvoidAnnotation(bool isAvoidAnnotation);
    bool                     GetAvoidAnnotation() const;
    
    /**
     * 图片点击区域相对图片四边的偏移，单位：dp
     */
    GroupIconMarkerOptions & SetClickRegionExtend(float clickRegionExtend);
    float                    GetClickRegionExtend() const;
    
    /**
     * 是否显示碰撞检测矩形框，调试用
     */
    GroupIconMarkerOptions & SetShowDebugRect(bool isShowDebugRect);
    bool                     GetShowDebugRect() const;
    
    /**
     * 是否显示点击扩展区域
     */
    GroupIconMarkerOptions & SetShowExtendRect(bool isShowExtendRect);
    bool                     GetShowExtendRect() const;
    
    virtual OverlayType GetType() const override { return OverlayType::GroupIconMarker; }

    GroupIconMarkerOptions & SetNeedAvoidCallback(bool isNeedAvoidCallback);
    bool                     GetNeedAvoidCallback() const;

    GroupIconMarkerOptions & SetRealTimeUpdate(bool isRealTimeUpdate);
    bool                     GetRealTimeUpdate() const;

private:
    struct Impl;
    std::shared_ptr<GroupIconMarkerOptions::Impl> GetImpl();
    std::shared_ptr<GroupIconMarkerOptions::Impl> GetImpl() const;
};

}

#endif /* __MAPAPI_OVERLAY_GROUP_ICON_MARKER_OPTIONS_H__ */
