//
//  GLMapLib_Route.h
//  GLMapLib2.0
//
//  Created by lihuafeng on 2018/11/12.
//  Copyright © 2018 nopwang. All rights reserved.
//

#ifndef GLMapLib_Route_h
#define GLMapLib_Route_h

#include "GLMapLib_Base.h"

#ifdef __cplusplus
extern "C" {
#endif
    
    #define kMaxRoadNameLength      (32)
    
    /**
     QRouteOverlay使用
     */
    typedef MAP_ENUM(int, MapRouteType)
    {
        MapRouteType_ColorLine,         //彩色
        MapRouteType_Repeat,            //repeat,虚线
        MapRouteType_FootPrint,         //点
        MapRouteType_Max
    };
    
    typedef MAP_ENUM(int, MapRouteDrawType)
    {
        RouteDrawType_Normal,   //保持原样
        RouteDrawType_PassGrey,  //走过置灰
        RouteDrawType_PassClear,  //走过清空
        RouteDrawType_Grown     //生长，与PassClear相反
    };

    //路段section描边类型
    typedef MAP_ENUM(int, MapRouteSectionType)
    {
        RouteSectionType_Composite, //轮渡虚线
        RouteSectionType_Normal,    //双实线
        RouteSectionType_Border     //双虚实线
    };

    //路线段信息结构体 MapRouteType_ColorLine 类型
    typedef struct _MapRouteSection
    {
        int startNum;                           // route point start index
        int endNum;                             // route point end index
        int color;                              // traffic color
        int speed;                              // speed (not in use)
    } MapRouteSection;
    
    /**
     动态路名
     */
    typedef struct MapRouteSectionWithName
    {
        int startNum;
        int endNum;
        int color;
        unsigned short roadName[kMaxRoadNameLength];
    } MapRouteSectionWithName;
    
    /**
     QRouteOverlay对应结构体
     */
    typedef struct _MapRouteInfo
    {
        MapRouteType    type;//路线类型
        MapVector2d*    points;//点串
        int             pointCount;//点数
        MapRouteSection*sections;//section 信息
        int             sectionCount;//section 大小, 可以为0，表示只有一个section·
        float           width;//路线宽度:(0,128]
        char            imageName[MapImageNameMaxLength];//路线纹理名称
        bool            bTurnArrow;//是否画转弯箭头
        bool            bForceLoad;//YES,同步加载
        int             routeID; // 返回routeId, 添加成功 > 0
        MapRouteDrawType  routeDrawType; //走过路线绘制类型
        bool            bHidden; // 创建时是否显示
    } MapRouteInfo;
    
    //绘制RGBA带Border纯色Route所需要的额外参数
    typedef struct _RGBAColorLineExtraParam{
        TMColor         colorList[32];
        TMColor         borderColorList[32]; //borderWidth == 0时，可以不设置borderColorList
        float           borderWidth;         //borderWidth * 2 < width
        int             colorCount;          //colorCount <= 32
    } RGBAColorLineExtraParam;
    
    //绘制RGBA带Border虚线Route所需要的额外参数
    typedef struct _RGBADashedLineExtraParam{
        TMColor         color;         //实线部分颜色
        TMColor         borderColor;   //实线部分border颜色
        float           borderWidth;   //实线部分border宽度：borderWidth * 2 < width
        int*            pattern;       //(实线长度1；虚线长度1；实线长度2，虚线长度2.......）,总长度不超过256，否则创建失败
        int             patternCount;  //patternCount，必须是偶数，否则创建失败
    } RGBADashedLineExtraParam;

    //绘制RGBA带Border虚线Route所需要的额外参数
    typedef struct _RGBADashedLineStyleAtScale{
        float    startScale;
        float    endScale;
        float    width;
        RGBADashedLineExtraParam* dashParam; //级别对应的dashline属性
    }RGBADashedLineStyleAtScale;

    typedef struct _SectionDashedLineParam{
        MapRouteSectionType      sectionType;   //section类型,不设置默认轮渡虚线
        RGBADashedLineExtraParam*dashParam;
        TMColor                  passedColor;   //实线部分走过的颜色
        int                      sectionStartIndex;
        int                      sectionEndIndex;
    } SectionDashedLineParam;
    
 
    typedef struct RouteGradientInfo
    {
        int statusColorNum;//  颜色的个数
        int *statusColors;//  颜色内容数组
        int segNum;//分段点的个数
        float *segRation; //分段点位置比例
        int * segColorIndex; //分段点对应的颜色
    }RouteGradientInfo;
    /************************************************************************/
    /*                               Route相关接口                           */
    /************************************************************************/
    #pragma mark Route相关接口
    
    /**
     创建纹理绘制的导航路线
     数组并未带来性能优化，建议修改为单个创建，并返回路线ID.
     路线默认属于overlay，优先级低于底图文字（非overlay）。
     @param pWorld 场景句柄
     @param routeInfos 路线信息
     @param size 数组大小
     */
    void MapRouteCreate(void *pWorld, MapRouteInfo *routeInfos, int size);

    /**
    创建渐变色蚯蚓线
    @param pWorld 场景句柄
    @param routeInfos 路线信息
    @param gradientInfo 渐变色信息
    */
    void MapRouteCreateWithGradientColor(void *pWorld, MapRouteInfo *routeInfos, RouteGradientInfo *gradientInfo);
    
    //绘制带有passedIndex的Route
    //todo:1，这个接口设计的不是很合理，多个route共用一个passCoor，所以应该对外使用做一个限制size=1
    //2，这个接口应该废弃，使用MapRouteSetPassedPoint代替
    //3，passCoor.y应该取负数，才能正常使用
    void MapRouteWithPassedIndexCreate(void *pWorld, MapRouteInfo *routeInfos, int size, int* passedIndex,MapVector2d *passCoor);
    
    /**
     创建颜色列表绘制的导航路线，支持颜色列表绘制的border
     
     @param pWorld 场景句柄
     @param routeInfo 路线信息
     @param extramParam 颜色等信息
     @return 是否创建成功
     */
    bool MapRouteCreateRGBAColorLine(void *pWorld, MapRouteInfo *routeInfo, RGBAColorLineExtraParam *extramParam);
    
    /**
     *设置路线添加全部调用完成，引擎根据此标记来判断是否进行动态路名相关的计算
     *
     *@param pWorld 场景句柄
     *@param bFinshed 标记是否路线添加结束
     */
    void MapRouteSetCreateFinshed(void *pWorld, bool bFinshed);
    /**
     * 修改route属性
     * 当routeInfo中传入的points和sections同时为空时，将对原route进行属性修改：宽度修改，颜色修改；
     * 若routeInfo中传入的points和sections同时不为空，宽度，颜色，extramParam信息需要同时传入；
     * 以上两种情况，修改返回true，其它情况，返回false。
     */
    bool MapRouteModifyRGBAColorLine(void *pWorld, int routeID, MapRouteInfo *routeInfo, RGBAColorLineExtraParam *extramParam);
    
    /**
     绘制RGBA虚线Route
     
     @param pWorld 场景句柄
     @param routeInfo 路线信息
     @param extramParam 颜色等信息
     @return 是否创建成功
     */
    bool MapRouteCreateRGBADashedLine(void *pWorld, MapRouteInfo *routeInfo, RGBADashedLineExtraParam *extramParam);
    
    /**
     * 修改route属性
     * 当routeInfo中传入的points和sections同时为空时，将对原route进行属性修改：宽度修改，颜色修改；
     * 若routeInfo中传入的points和sections同时不为空，宽度，颜色，extramParam信息需要同时传入；
     * 以上两种情况，修改返回true，其它情况，返回false。
     */
    bool MapRouteModifyRGBADashedLine(void *pWorld, int routeID, MapRouteInfo *routeInfo, RGBADashedLineExtraParam *extramParam);
    
    /**
    绘制带轮渡线的彩色蚯蚓线
    
    @param pWorld 场景句柄
    @param routeInfo 路线信息
    @param sectionParams 颜色等信息
    @return 是否创建成功
    */
    bool MapRouteCreateCompositeLine(void *pWorld, MapRouteInfo *routeInfo, SectionDashedLineParam *sectionParams, int paramCount);

    /**
    * 修改route属性
    * 当routeInfo中传入的points和sections同时为空时，将对原route进行属性修改：宽度修改，颜色修改；
    * 若routeInfo中传入的points和sections同时不为空，宽度，颜色，extramParam信息需要同时传入；
    * 以上两种情况，修改返回true，其它情况，返回false。
    */
    bool MapRouteModifyCompositeLine(void *pWorld, int routeID, MapRouteInfo *routeInfo, SectionDashedLineParam *sectionParams, int paramCount);

    /**
     删除路线
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     */
    void MapRouteDelete(void *pWorld, int routeID);
    
    /**
     获取路线信息，注意：返回的points, sections为NULL;
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param routeInfo 路线信息
     @return 是否成功获取
     */
    bool MapRouteGetInfo(void *pWorld, int routeID, MapRouteInfo *routeInfo);
    
    /**
     更新路线属性信息（可能包括数据）
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param routeInfo 路线属性信息（当points不为空时，数据一并更新）
     @return 是否成功更新
     */
    bool MapRouteModify(void *pWorld, int routeID, const MapRouteInfo *routeInfo);
    
    /**
     设置路线使用单一颜色，通常应用于未激活的路线
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param useSingleColor 是否使用单一颜色（纹理行）
     @param colorIndex 颜色（纹理行）索引
     */
    void MapRouteSetUseSingleColor(void *pWorld, int routeID, bool useSingleColor, int colorIndex);
    
    /**
     设置经过的点
     在此点之前的区域将会置灰
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param pointIndex 经过点对应的路线顶点索引
     @param coordinate 经过点的坐标
     */
    void MapRouteSetPassedPoint(void *pWorld, int routeID, int pointIndex, MapVector2d coordinate);
    
    /**
     设置清除点
     在此点之前的区域路线将会消失
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param pointIndex 清楚点对应的路线顶点索引
     @param coordinate 清除点的坐标
     */
    void MapRouteSetClearPoint(void *pWorld, int routeID, int pointIndex, MapVector2d coordinate);
    
    /**
     设置路线关键点，关键字不能被抽稀
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param pointIndex 索引点串
     @param indexCount 索引点的个数
     */
    void MapRouteSetKeyPointIndex(void *pWorld, int routeID, int* pointIndex, int indexCount);

    /**
     设置路线是否可点击
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param interactive 是否可点击
     */
    void MapRouteSetInteractive(void *pWorld, int routeID, bool interactive);
    
    /**
     设置是否绘制路线小箭头
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param bDrawArrow 是否绘制
     */
    void MapRouteSetDrawArrow(void *pWorld, int routeID, bool bDrawArrow);
    
    /**
     设置路线箭头间距
     
     @param pWorld pWorld
     @param routeID 路线ID
     @param spacing 间距单位 dp
     */
    void MapRouteSetArrowSpacing(void *pWorld, int routeID, float spacing);
    
    /**
     设置路线箭头纹理
     
     @param pWorld pWorld
     @param routeID 路线ID
     @param textureName 纹理名称
     */
    void MapRouteSetArrowTextureName(void *pWorld, int routeID, const char* textureName);
    
    /**
     设置是否绘制路线端的cap
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param bDrawCap 是否绘制
     */
    void MapRouteSetDrawCap(void *pWorld, int routeID, bool bDrawCap);
    
    /**
     设置选中的路线
     
     将选中的路线移到所有route之上，其修改后的优先级等于之前位于最上面的route的优先级
     
     【注意】此接口不再支持，多个线程同时调用时，结果存在不确定性。
     
     @param pWorld 场景句柄
     @param routeID 路线ID数组
     @param count 数组大小
     */
    void MapRouteSetSelectedRoutes(void *pWorld, int *routeID, int count);
    
    
    /**
     * 将某条route移到所有route的最下面
     *
     *【注意】此接口不再支持，多个线程同时调用时，结果存在不确定性。
     */
    void MapRouteBringToBottom(void *pWorld, int routeID);
    
    /**
     设置路线透明度，支持在外部设置动画
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param alpha 透明度(0.0f ~ 1.0f)
     */
    void MapRouteSetAlpha(void *pWorld, int routeID, float alpha);
    
    /**
     路线执行一次动画效果。
     @param routeID ，route的ID
     @param duration ，动画执行的总时长，单位s
     @param callback ，动画结束回调的函数指针
     @param context ，回调的context
     */
    void MapRouteGrownAnimation(void*pWorld, int routeID, double duration, MapRoutePassedPointCallback callback, void *context);
    
    /**
     设置点路线的间距
     
     @param pWorld pWorld
     @param routeID route id
     @param spacing 间距 单位dp
     */
    void MapRouteSetFootPrintSpacing(void *pWorld, int routeID, float spacing);
  
 
    //开平蚯蚓线的渐变模式
    typedef MAP_ENUM(int, MapRouteGradientMode) {
        MapRouteGradientMode_None = 0,     //无渐变
        MapRouteGradientMode_Segment,      //对整个segment进行渐变，即大渐变。
        MapRouteGradientMode_Node,         //在节点处进行渐变，即小渐变。类似高德导航路况渐变效果。目前还不支持。
        MapRouteGradientMode_Max
    };

    typedef struct _RouteGradientParamForSegmentMode {
        TMColor endPointColor;             //渐变模式下的终点颜色
    } RouteGradientParamForSegmentMode;

    /**
     *  开平
     *  设置蚯蚓线的渐变模式。
     *  目前渐变只对RGBAColorLine有效，目前不支持描边的渐变。
     *
     *  @param routeID 路线ID
     *  @param gradientMode 渐变模式
     *  @param gradientParam 其它渐变参数。
     *         gradientMode=None时，gradientParam可以为NULL。
     *         其它情况gradientParam不能为空，且其类型需要和gradientMode匹配。
     *         例如：gradientMode=Segment时，gradientParam必须为RouteGradientParamForSegmentMode类型。
     */
    bool MapRouteSetGradientMode(const void* pWorld, const int routeID, const MapRouteGradientMode gradientMode, const void* gradientParam);
    
    /**
     * 参数合法性检查：
     * 1, startScale <= endScale
     * 2, startScale, endScale must be in [1,30]
     * 3, scale 设置不能交叉，例如[3, 6], [4, 7]是不允许的
     * 4, image is not null
     * 5, width > 0
     */
    typedef struct _RouteStyleAtScale{
        float    startScale;
        float    endScale;
        float    width; // dp单位
        char     image[MapImageNameMaxLength];
    }RouteStyleAtScale;
    
    /**
     * 设置route，在不同比例尺下，有不同的宽度和纹理。
     * 只适用于MapRouteType_ColorLine
     * 
     * 如果调用了MapRouteSetStyleByScale，再调用MapRouteModify修改route样式是不会生效的。
     *
     * @param style route样式数组
     * @param styleCount route样式数组个数
     * @return 如果样式不合法，内部打印日志，并return false；
     */
    bool MapRouteSetStyleByScale(void * pWorld, int routeID, RouteStyleAtScale * style, int styleCount);
    
    
    /************************************************************************/
    /*                              动态路名相关接口                           */
    /************************************************************************/
    #pragma mark 动态路名相关接口
    
    typedef struct _RouteNameStyle{
        TMColor color;
        TMColor backgroundColor;
        int     fontSize;
        int     rank;//主路线设为1，备选路线设成0，主路和备选路的差异体现的路名压盖时，主路路名有更高优先级
    }RouteNameStyle;
    
    /**
     * 添加动态路名
     * @return 添加成功，返回路名ID，ID > 0；失败，返回0
     */
    int GLMapAddRouteNameSegments(void *pWorld, MapRouteSectionWithName *sections, int sectionsCount,
                                  MapVector2d *mapPoints, int mapPointsCount, RouteNameStyle style);
    
    /**
     * 修改某条动态路名的样式，所有比例尺都是一样的样式
     */
    void GLMapSetRouteNameSegmentsStyle(void *pWorld, int routeNameID, RouteNameStyle *style);
    
    /**
     * 设置路名rank
     * @param rank 主路线设为1，备选路线设成0，主路和备选路的差异体现的路名压盖时，主路路名有更高优先级
     */
    void GLMapSetRouteNameSegmentsRank(void *pWorld, int routeNameID, int rank);
    
    
    /**
     * 参数合法性检查：
     * 1, startScale <= endScale
     * 2, startScale, endScale must be in [1,30]
     * 3, scale 设置不能交叉，例如[3, 6], [4, 7]是不允许的
     * 4, fontSize > 0
     */
    typedef struct _RouteNameStyleAtScale{
        float     startScale;
        float     endScale;
        TMColor   color;
        TMColor   backgroundColor;
        int       fontSize;
    }RouteNameStyleAtScale;
    
    /**
     * 修改某条动态路名，在不同比例尺下，有不同的样式
     * @param style 样式数组
     * @param styleCount 样式个数
     */
    bool GLMapSetRouteNameSegmentsStyleByScale(void *pWorld, int routeNameID, RouteNameStyleAtScale *style, int styleCount);
    
    /**
     * 清除指定的动态路名
     */
    void GLMapRemoveRouteNameSegments(void *pWorld, int routeNameID);

    /**
     * 隐藏/显示指定的动态路名
     */
    void GLMapHideRouteNameSegments(void *pWorld, int routeNameID, bool bHidden);
    
    /**
     清除的所有动态路名
     */
    void GLMapClearRouteNameSegments(void *pWorld);
    
    
    /************************************************************************/
    /*                              路线探测相关接口                           */
    /************************************************************************/
    #pragma mark 路线探测相关接口
    
    /**
     伴随气泡结构体
     */
    typedef struct _MapRouteDescInfo {
        int              routeID;
        unsigned short * descText;
        int              textCount;
        TMColor          textColor;
        TMColor          borderColor;
        TMColor          backgroundColor;
    } MapRouteDescInfo;
    
    /**
     手图路线探测需要的三个接口，已经废弃。
     */
    void MapRouteCalDescriptionAnchorPos(void *pWorld, int *routeIDs, int routeCount);
    int  MapRouteSetDescription(void *pWorld, MapRouteDescInfo *info);
    void MapRouteClearDescription(void * pWorld);
    
    /************************************************************************/
    /*                              导航箭头相关接口                           */
    /************************************************************************/
    #pragma mark 导航箭头相关接口
    
    /**
     * 以前全局导航箭头只能依赖某条Route同时创建，现在增加此接口来单独创建导航箭头（包括主箭头，然后箭头，同时创建）。
     * @param routeID 关联的routeID，用来保证箭头的宽度与route宽度一致
     * @param points 箭头的坐标点串，与route的原始坐标串一致
     * @param pointsCount 点数
     */
    bool MapRouteSetTurnArrowData(void *pWorld, int routeID, MapVector2d * points, int pointsCount);

    /**
     * 隐藏导航箭头
     * @param routeID 关联的routeID，用来保证箭头的宽度与route宽度一致
     * @param bHidden 是否隐藏
     */
    bool MapRouteSetTurnArrowHidden(void *pWorld, int routeID, bool bHidden);

    /**
     设置白箭头的边缘宽度，设置后将采用该值，样式文件值不再生效.
     @param fEdgeWidth  白箭头边缘宽度，应传大于0的值
     */
    bool MapRouteSetTurnArrowEdgeWidth(void *pWorld, float fEdgeWidth);

    /**
    * 获取导航箭头的OverlayID
    */
    int MapRouteGetTurnArrowRouteID(void *pWorld);
    /**
    * 根据当前OverlayID获取当前蚯蚓线是否为Top级别
    */
    bool MapRouteGetIsTop(void *pWorld, int OverlayID);
    
    /**
     设置第一个导航箭头（主箭头）的index
     */
    void MapRouteSetTurnArrowIndex(void *pWorld, int segmentIndex, int actionLength);
    void MapRouteSetTurnArrowIndexs(void *pWorld, int *segmentIndex,int count, int actionLength);
    
    /**
     设置第二个导航箭头（“然后“箭头）的index
     */
    void MapRouteSetSecondTurnArrowIndex(void *pWorld, int segmentIndex, int actionLength);
    /**
     设置第一个导航箭头（主箭头）的index与转弯类型
     @param iIntersectionType 转弯类型，由mapbiz透传，用来计算UTurn方向
     */
    void MapRouteSetTurnArrowIndexWithType(void *pWorld, int segmentIndex, int actionLength, int iIntersectionType);
    void MapRouteSetTurnArrowIndexsWithType(void *pWorld, int *segmentIndex,int count, int actionLength, int iIntersectionType);
    /**
     设置第二个导航箭头（“然后“箭头）的index与转弯类型
     @param iIntersectionType 转弯类型，由mapbiz透传，用来计算UTurn方向
     */
    void MapRouteSetSecondTurnArrowIndexWithType(void *pWorld, int segmentIndex, int actionLength, int iIntersectionType);

    /**
     设置第二个箭头从半透渐变到第一个箭头样式的动画执行进度。
     @param progress , 范围[0， 1.0]
     */
    void MapRouteSetSecondTurnArrowAnimationProgress(void *pWorld, float progress);
    
    /**
     * 这个接口需要废弃！！！
     *
     * 箭头为了保证在不同比例尺之间实现长度平滑过度，引擎内部目前是按地理长度来进行计算的，不使用外部传入的像素长度了。
     */
    void MapRouteSetTurnArrowDistance(void *pWorld, MapVector2i *distance, int levelStart, int levelEnd);//(包含levelstart,levelend)
    
    /**
     设置2D导航箭头的样式
     */
    void MapRouteSetTurnArrowStyle(void *pWorld, TMColor color, TMColor outlineColor);
    
    /**
     设置导航箭头是否为3D样式。默认为3D样式
     */
    void MapRouteSetTurnArrow3D(void * pWorld, bool is3D);
    
    typedef struct _RouteTurnArrow3DStyle {
        TMColor roofColor; //顶面颜色
        TMColor wallColor; //侧面颜色
        TMColor edgeColor; //边线颜色
        TMColor shadowColor; //阴影颜色
    }RouteTurnArrow3DStyle;
    
    /**
     设置3D导航箭头的样式
     */
    void MapRouteSetTurnArrow3DStyle(void *pWorld, RouteTurnArrow3DStyle * style);
    
    /**
     设置第二个3D导航箭头（“然后“箭头）的样式
     */
    void MapRouteSetSecondTurnArrow3DStyle(void *pWorld, RouteTurnArrow3DStyle * style);
    
    /**
     * 获取第一个箭头的bound坐标范围.
     * 箭头不显示的时候，返回{0,0,0,0}.
     */
    TMRect MapRouteGetTurnArrowBound(void *pWorld);

    /**
     * 设置箭头的宽度、高度缩放系数，
     * 按照产品要求，宽度，高度分开设置比例系数，且系数范围为(0,100)
    */
    void MapRouteSetTurnArrowScale(void *pWorld, float fWidthScale, float fHeightScale);
    
    /**
     设置经过的点
     在此点之前的区域将会置灰
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param pointIndex 经过点对应的路线顶点索引
     @param coordinate 经过点的坐标
     @param angle 车标角度
     @param durationInSeconds 动画完成时间,以秒为单位
     @param callback 动画回调
     @param pCallbackContext 传递的context参数
     */
    void MapRouteSetPassedPointAnimation(void *pWorld, int routeID, int pointIndex, MapVector2d coordinate, float angle, float durationInSeconds, MapRoutePassedPointCallback callback, void* pCallbackContext);

    /*
     *  停止路线置灰动画
     */
    void MapRouteStopPassAnimation(void* pWorld, int routeID);

    /**
     设置路线是否碰撞掉路名
     
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param collideAnnotation  是否碰撞掉底图路名
     */
    void MapRouteSetCollideAnnotation(void *pWorld, int routeID, bool collideAnnotation);
    
    /**
     设置经过的点
     在此点之前的区域将会清除
     @param pWorld 场景句柄
     @param routeID 路线ID
     @param pointIndex 经过点对应的路线顶点索引
     @param coordinate 经过点的坐标
     @param angle 车标角度
     @param durationInSeconds 动画完成时间,以秒为单位
     @param callback 动画回调
     @param pCallbackContext 传递的context参数
     */
    void MapRouteSetClearPointAnimation(void *pWorld, int routeID, int pointIndex, MapVector2d coordinate, float angle, float durationInSeconds, MapRoutePassedPointCallback callback, void* pCallbackContext);

    /**
    * //获取和设置蚯蚓线是否压盖楼块
     * @param pWorld 场景句柄
     * @param  route_id 路线ID
    */
    bool MapGetIsRouteCoverBuilding(void *pWorld, int route_id);
    void MapSetIsRouteCoverBuilding(void *pWorld, bool isCover, int route_id);

#ifdef __cplusplus
}
#endif

#endif /* GLMapLib_Route_h */
