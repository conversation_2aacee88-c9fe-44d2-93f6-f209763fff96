//
//  PolylineText.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/30.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_POLYLINE_TEXT_H__
#define __MAPAPI_OVERLAY_POLYLINE_TEXT_H__

#include <stdio.h>
#include <vector>
#include "overlay/overlay.h"

namespace MAPAPI {

struct SegmentText{
    int         start_index;      //文字开始显示的点串索引
    int         end_index;        //文字停止显示的点串索引
    std::string text;            //文字内容
};

struct TextStyle{
    MapVector4ub    text_color;        //文字颜色
    MapVector4ub    text_border_color; //文字描边颜色
    int             font_size;         //字体大小,有效范围8-20,单位为dp
    float           start_scale;       //开始生效的显示级别
    float           end_scale;         //结束生效的显示级别
};

struct PolylineTextOptions {
    std::vector<SegmentText>    sections;//文字分段信息
    std::vector<TextStyle>      style;   //样式信息
    int                         priority;//显示优先级
};

/**
 * 动态路名
 */
class TXMAPSDK_EXPORT PolylineText{
    friend class ColorPolyline;
public:
    /**
     * 设置显示优先级：主路线设为1，备选路线设成0。主路和备选路的差异体现的路名压盖时，主路路名有更高优先级。
     */
    void SetPriority(int priority);
    
    /**
     * 修改动态路名，在不同比例尺下，有不同的样式。
     * @param styles 样式数组
     */
    bool SetStyle(const std::vector<TextStyle> & styles);
    
    /**
     * 清除动态路名
     */
    void Clear();
    
    /**
     * 隐藏/显示 动态路名, true隐藏 false显示
     */
    void SetHidden(bool hidden);
    
    /**
     * 动态路名是否隐藏
     */
    bool GetHidden();
    
public:
    //外部不要调用
    PolylineText(void* world);
    
private:
    friend class ColorPolylineImpl;
    class Impl;
    std::unique_ptr<Impl> impl_;
};

}

#endif /* __MAPAPI_OVERLAY_POLYLINE_TEXT_H__ */
