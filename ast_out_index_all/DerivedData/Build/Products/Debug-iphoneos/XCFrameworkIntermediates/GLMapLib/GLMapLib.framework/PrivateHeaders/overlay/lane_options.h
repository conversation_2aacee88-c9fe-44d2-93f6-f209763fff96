//
//  Created by v<PERSON><PERSON><PERSON> on 2021/3/27.
//  Copyright © 2021 TencentMap. All rights reserved.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "overlay/overlay.h"

namespace MAPAPI {

/**
 * 感知--车道线的类型
 */
enum LaneType{
    kLaneTypeSolid =0 ,  //实线
    kLaneTypeDash,       //虚线
    kLaneTypeDouble,     //双线
    kLaneTypeNum
};

/**
 * 车道线的ADAS状态
 */
enum LaneAdasStatus {
    kLaneAdasStatusNormal = 0, //正常状态
    kLaneAdasStatusKeepLane,   //车道保持
    kLaneAdasStatusOverLane,   //压线
    kLaneAdasStatusNum
};

/**
 * 单条车道线数据
 */
class TXMAPSDK_EXPORT LaneData{
public:
    LaneData();
    LaneData(const LaneData & other);
    LaneData& operator=(const LaneData & other);
    ~LaneData();
    
    /**
     * 坐标点串
     */
    LaneData & AddPoint(const MapVector3d & point);
    LaneData & AddPoints(const std::vector<MapVector3d> & points);
    std::vector<MapVector3d> & GetPoints() const;
    
    /**
     * 车道线类型
     */
    LaneData & SetType(LaneType type);
    const LaneType & GetType() const;
    
    /**
     * 车道线的ADAS状态
     */
    LaneData & SetAdasStatus(LaneAdasStatus adas_status);
    const LaneAdasStatus & GetAdasStatus() const;
    
private:
    struct Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * 感知车道线数据
 */
class TXMAPSDK_EXPORT LaneOptions : public OverlayOptions{
public:
    LaneOptions();
    LaneOptions(const LaneOptions & other);
    LaneOptions& operator=(const LaneOptions & other);
    virtual ~LaneOptions();
    
    /**
     * 添加单条车道线信息
     */
    LaneOptions & AddLane(const LaneData & lane);
    
    /**
     * 添加多条车道线信息
     */
    LaneOptions & AddLanes(const std::vector<LaneData> & lanes);
    
    std::vector<LaneData> & GetLanes() const;
    
    virtual OverlayType GetType() const  override { return OverlayType::Lane; }
    
private:
    struct Impl;
    std::shared_ptr<LaneOptions::Impl> GetImpl();
    std::shared_ptr<LaneOptions::Impl> GetImpl() const;
    
};

}
#endif