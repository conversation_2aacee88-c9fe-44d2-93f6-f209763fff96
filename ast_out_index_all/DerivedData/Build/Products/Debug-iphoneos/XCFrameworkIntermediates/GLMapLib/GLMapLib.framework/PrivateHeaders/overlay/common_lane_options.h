//
// Created by cuipanpan on 2021/7/14.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "overlay/overlay.h"
#include "had_structure.h"

namespace MAPAPI {
class MapImpl;

 // 车道id结构
struct LaneInfo {
    uint32_t tile_id;
    uint32_t lane_id;
    int start_idx; // 开始打断点
    float start_ratio;
    int end_idx; // 终止打断点
    float end_ratio;
};

class TXMAPSDK_EXPORT CommonLaneOptions : public OverlayOptions{
public:
    CommonLaneOptions();
    CommonLaneOptions(const CommonLaneOptions & other);
    CommonLaneOptions& operator=(const CommonLaneOptions & other);
    ~CommonLaneOptions();

    /**
     * 设置绘制数据相对中心线的位置
     * @param pos: 相对位置 kLeft: 中心线的左边; kCenter: 中心线位置; kRight: 中心线右边
     */
    void SetRelativePosition(mapbase::HAD::BoundaryRelativePos pos);

    mapbase::HAD::BoundaryRelativePos GetRelativePosition() const;

    /**
     * 车道类型
     * 0:公交车道
	 * 1:应急车道
     * 2:非机动车道
     * @param lane_type 车道类型
     */
    CommonLaneOptions & SetLaneType(int lane_type);
    int GetLaneType() const;

    /**
     * 车道颜色，格式：rgba
     */
    CommonLaneOptions & SetLaneColor(long lane_color);
    long GetLaneColor() const;

    /**
     * 车道id信息
     * @param lane_info 车道信息
     */
    CommonLaneOptions & SetLaneInfo(std::vector<LaneInfo> lane_info);
    std::vector<LaneInfo> GetLaneInfo() const;

    /**
     * 车道宽度
     * @param lane_width 车道宽度
     */
    CommonLaneOptions & SetLaneWidth(double lane_width);
    double GetLaneWidth() const;

    /**
     * 消隐动画时间，单位秒
     * @param animation_time 消隐动画时间
     */
    CommonLaneOptions & SetAnimationTime(float animation_time);
    float GetAnimationTime() const;

    /**
     * 插帧动画时间，单位秒
     * @param interpolation_time 插帧动画时间
     */
    CommonLaneOptions & SetInterpolationTime(float interpolation_time);
    float GetInterpolationTime() const;

    /**
     * 宽度动画时间，单位秒
     * @param time 宽度动画时间
     */
    CommonLaneOptions & SetWidthAnimationTime(float time);
    float GetWidthAnimationTime() const ;

    CommonLaneOptions & SetImgName(std::string img_name);
    std::string GetImgName() const;

    void CoordinateTransform(MapImpl * mapImpl);

    // 设置车道线坐标点串
    CommonLaneOptions & SetPoints(const std::vector<MapVector3d> & points);

    /**
     * 是否支持双端渐变
     * @param use_gradual 是否支持双端渐变
     */
    CommonLaneOptions & SetUseGradual(bool use_gradual);

    bool GetUseGradual() const;

    /**
     * 首部渐变比例
     */
    CommonLaneOptions& SetHeadGradualRatio(float head_gradual_ratio);
    float GetHeadGradualRatio() const;

    /**
     * 尾部渐变比例
     */
    CommonLaneOptions& SetTailGradualRatio(float tail_gradual_ratio);
    float GetTailGradualRatio() const;

    const std::vector<MapVector3d> & GetPoints() const;

    /**
     * 设置纹理重复长度
     */
    CommonLaneOptions & SetTextureRepeatLength(float length);

    /**
     * 获取纹理重复长度
     */
    float GetTextureRepeatLength() const;

    virtual OverlayType GetType() const override { return OverlayType::CommonLane; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;
    
protected:
    struct Impl;
    
private:
    std::shared_ptr<CommonLaneOptions::Impl> GetImpl();
    std::shared_ptr<CommonLaneOptions::Impl> GetImpl() const;
};

}
#endif