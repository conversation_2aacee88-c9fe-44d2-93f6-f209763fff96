//
//  GLMapLib_LocatorCompass.h
//  GLMapLib2.0
//
//  Created by lihuafeng on 2018/11/12.
//  Copyright © 2018 nopwang. All rights reserved.
//

#ifndef GLMapLib_LocatorCompass_h
#define GLMapLib_LocatorCompass_h

#include "GLMapLib_Base.h"
#ifdef __cplusplus
extern "C" {
#endif
#define MapLocatorLog       "MapLocatorLog"
#define MapMarkerLocator_DefaultIndiatorImage   ""
#define MapMarkerLocator_DefaultCompassImage    ""
#define MapMarkerLocator_DefaultRouteDirPointImage "navi_location_green_point.png"
#define MapMarkerLocator_DefaultGreenRingImage   "green_ring.png"
#define MapMarkerLocator_DefaultOrangeRingImage  "orange_ring.png"
#define MapMarkerLocator_DefaultRedRingImage     "red_ring.png"
  

#define MapMarkerLocator_DefaultDirectionArrow    "compass_direction_compass.png"
#define MapMarkerLocator_DefaultCompassRingImage    "compass_direction_circle.png"
#define MapMarkerLocator_DefaultDirectionCoverImage  "compass_direction_cover.png"
#define MapMarkerLocator_DefaultDirectionEastImage    "compass_direction_east.png"
#define MapMarkerLocator_DefaultDirectionSouthImage    "compass_direction_south.png"
#define MapMarkerLocator_DefaultDirectionWestImage    "compass_direction_west.png"
#define MapMarkerLocator_DefaultDirectionNorthImage    "compass_direction_north.png"
#define MapMarkerLocator_DefaultBreathImage    "compass_breath.png"

#define MapMarkerLocator_DefautAccuracyColor     0x19ff9538
    /**
     定位点 结构体
     */
    typedef struct MapMarkerLocatorInfo
    {
        char        indicatorImageName[MapImageNameMaxLength];
        char        cover_image_name[MapImageNameMaxLength];
        char        background_image_name[MapImageNameMaxLength];
        char        compassImageName[MapImageNameMaxLength];
        char        routeDirectionPointImageName[MapImageNameMaxLength];
        char        greenRingImageName[MapImageNameMaxLength];
        char        orangeRingImageName[MapImageNameMaxLength];
        char        redRingImageName[MapImageNameMaxLength];
        char        radarWaveImageName[MapImageNameMaxLength];
        
        char        directionEastImageName[MapImageNameMaxLength];
        char        directionSouthImageName[MapImageNameMaxLength];
        char        directionWestImageName[MapImageNameMaxLength];
        char        directionNorthImageName[MapImageNameMaxLength];
        char        compassBreathImageName[MapImageNameMaxLength];
        float       maxAngleForGreenRing;  //角度小于此值时，圆环采用绿色绘制，默认30度
        float       maxAngleForOrangleRing;//角度小于此值时，圆环采用橙色绘制，默认60度
        MapVector2f indicatorAnchorPoint;
        MapVector2f compassAnchorPoint;
        MapVector2f routeDirectionPointAnchor;
        MapVector2f radarWaveAnchorPoint;
        MapVector4f radarWaveColor;
        float       radarWaveColorDuration;
        MapVector2d coordinate; //已废弃:只能get，不能set
        float       height;//已废弃:只能get，不能set
        TMColor     accuracyColor;
        float       indicatorAngle;//已废弃:只能get，不能set
        float       routeDirectionPointAngle;
        bool        interactive;
        MapVector2d redlineEndCoord;
        float       redlineWidth;
        TMColor     redlineColor;
        int         markerID; // Output markerID
    }MapMarkerLocatorInfo;

    
    typedef struct MapLocatorSpeedTextParam
    {
        int                verticalOffset;  //相对于中心点的垂直像素偏移
        TMColor            textColor;       //文字颜色
        int                fontSize;        //文字大小
        unsigned short     content[5];      //描述速度单位的文字，为空时使用默认单位文字，比如km/h
    }MapLocatorSpeedTextParam;
 
    typedef enum MapLocatorDisplayType
    {
        MapLocatorDisplayNormal = 0,
        MapLocatorDisplaySpeed,
        MapLocatorDisplayModel3D,
        MapLocatorDisplayModel3DSwitch2D,
        MapLocatorDisplayModel3DGuideArea
    } MapLocatorDisplayType;

    typedef enum MapLocatorScanLightDirection
    {
        kMapLocatorScanLightLeftToRight = 0,
        kMapLocatorScanLightRightToLeft,
        kMapLocatorScanLightTopToBottom,
        kMapLocatorScanLightBottomToTop,
        kMapLocatorScanLightFrontToBack,
        kMapLocatorScanLightBackToFront
    } MapLocatorScanLightDirection;

		typedef struct MapLocatorScanLightOptions
		{
			TMColor 			  		 color;	  	// 扫光颜色 rgba
			float   			  		 width;   	// 扫光宽度
			float				  		 intensity; // 泛光强度 范围[0.0, 1.0] 越接近1.0f越强，影响并非线性，建议区间[0.5, 0.7]
			float				  		 delay;	  	// 扫光淡入/浅出时间比例
			double 				  		 duration;	// 扫光时间
			float						 cutting; 	// 裁剪高度
			MapLocatorScanLightDirection direction; // 扫光方向
		}MapLocatorScanLightOptions;

		typedef enum MapLocatorComponent
		{
      MapLocatorIndicator = 0,
      MapLocatorCompass   = 1,
      MapLocatorHDAccuracyCircle = 2,
		}MapLocatorComponent;

		typedef enum MapLocatorBreatheEndType
		{
		  MapLocatorStopAtCurrent = 0,
		  MapLocatorStopAtEnd   = 1,
		  MapLocatorStopAtBegin = 2,
		}MapLocatorBreatheEndType;

		typedef struct MapLocatorBreatheNodes {
      float ratio;
      float scale;
      float alpha;
    }MapLocatorBreatheNodes;

		typedef struct MapLocatorBreatheOptions
    {
      double duration;	// 动画时长
      int 	 cycle;			// 动画循环次数
      MapLocatorBreatheNodes* nodes; // 动画节点
      int 	 count;     // 动画节点数量
		}MapLocatorBreatheOptions;

    typedef struct MapModel3DImageBuffer
    {
      char *             imageBuffer;     //图像缓冲，格式要求是jpg、png等常用格式
      int                bufferLen;       //缓冲长度
    }MapModel3DImageBuffer;

		typedef struct MapNaviAccuracyCircleGradientNode {
		  float ratio;
		  float alpha;
		}MapNaviAccuracyCircleGradientNode;
		
		/**
		 * 导航精度圈绘制参数
		 */
		typedef struct MapNaviAccuracyCircleOptions {
		  TMColor color;  // 精度圈颜色
		  float   scale;  // 相对罗盘圈的缩放比例，保证绘制时与车标罗盘圈大小比例一致
      MapNaviAccuracyCircleGradientNode* nodes; // 精度圈绘制的渐变节点，控制渐变绘制精度圈的方式
      int			count;  // 渐变节点数量
		}MapNaviAccuracyCircleOptions;

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    bool MapMarkerLocatorGetInfo(void *pWorld, MapMarkerLocatorInfo *markerInfo);
    void MapMarkerLocatorModify(void *pWorld, MapMarkerLocatorInfo *markerInfo);
    void MapMarkerLocatorSetHidden(void *pWorld, bool hidden);
    void MapMarkerLocatorSetLaneID(void *pWorld, MapLaneID lane_id);
    
    void MapLocatorModifyCompassImage(void *pWorld, const char *imageName, MapVector2f anchorPoint);

    void MapLocatorModifyCompassGroupImages(void *pWorld, const char *ringName, const char *eastImageName, const char *southImageName,
                                        const char *westImageName, const char *northImageName, MapVector2f anchorPoint);
   
    void MapLocatorModifyIndicatorImage(void *pWorld, const char* background_name, const char *indicator_name, const char *cover_name, MapVector2f anchor_point);
    void MapLocatorModifyBreathImage(void *pWorld, const char *imageName, MapVector2f anchorPoint);

    void MapLocatorSetCompassHidden(void *pWorld, bool hidden);

    //隐藏车标中心ICON
    void MapLocatorSetIndicatorHidden(void *pWorld, bool hidden);

    void MapLocatorSetRedLineHidden(void *pWorld, bool hidden);
    void MapLocatorModifyAccuracyAreaColor(void *pWorld, TMColor color);
    void MapLocatorSetAccuracyAreaHidden(void *pWorld, bool hidden);
    void MapLocatorSetColorRingHidden(void *pWorld, bool hidden);
    void MapLocatorSetBreathAnimHidden(void *pWorld, bool hidden);
		void MapLocatorSetNaviAccuracyHidden(void *pWorld, bool hidden);
   
    void MapLocatorSetRouteDirection(void*pWorld, float dir);
    void MapLocatorSetLocatorMaxSkewAngle(void*pWorld, float angle);
    void MapLocatorSetLocatorBackgroundMaxSkewAngle(void*pWorld, float angle);
    void MapLocatorSetLocatorPenetratingTransparent(void *pWorld, bool is_on);
    void MapLocatorPreWarmAssetsBundle(void *pWorld, const char* name);

    void GLMapSetLocationInfo(void *pWorld, MapVector2d mapPoint, float course, float accuracy, bool animated);

    void GLMapSetLocationInfo3D(void *pWorld, MapVector3d mapPoint, float course, float pitch, float accuracy, bool animated, double duration, bool directRun);
    /**
     * @brief 设置定位姿态
     * @param location_info 姿态信息
     * @param animated 是否做动画
     * @param duration 动画时长
     */
//    void GLMapSetLocationPosture(void *pWorld, LocationInfo* location_info, bool animated, double duration);

    //bWalkNavigation,bNavigating目前未被使用
    void GLMapSetLocationFollow(void *pWorld, bool bFollow, bool headingForward);
    void GLMapSetLocationHeading(void *pWorld, float angle);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /**
     获取指南针的坐标(屏幕坐标)信息；
     */
    MapVector2d GLMapGetCompassPosition(void *pWorld);

    /**
     设置指南针的坐标(屏幕坐标)信息
     */
    void GLMapSetCompassPosition(void *pWorld, MapVector2d coord);

    /**
     设置是否显示指南针
     */
    void MapCompassSetHidden(void *pWorld, bool bHidden);

    /**
     获取指南针显示状态
     */
    bool MapCompassIsHidden(void *pWorld);
    
    /**
     修改指南针的资源文件
     */
    void MapCompassModifyImage(void *pWorld, const char *imageName);

   /**
    设置3D模型的数据，必须在MapLocatorSetDisplayType之后调用
    @param pWorld                   场景句柄
    @param modelType            模型种类，用于底层区别调用模型解析器
    @param modelBuffer       模型数据
    @param modelBuflen       模型数据字节长度
    @param imgBuffer            模型纹理数据缓冲
    @param imageNum              纹理数据的个数
    @param config                   配置文件
    *
    *【此接口已经无效，请调用C++接口。】
    */
    void MapLocatorSetModel3DBuffer(void *pWorld, MapLocatorModel3DType modelType, const char* modelBuffer, int modelBuflen, MapModel3DImageBuffer *imgBuffer, int imageNum, const char* config);

    /**
    设置3d车标材质
    @param pWorld                  场景句柄
    @param options            3d车标option
    */
    void MapLocatorSetLocatorMaterial(void*pWorld, void* options);
    /**
    设置3d车标材质
    @param pWorld                  场景句柄
    @param options            3d车标option
    @param anchorPoint          偏移锚点，默认中心为(0.5, 0.5)，z无效果以备后用
    */
    void MapLocatorSetLocatorMaterialWithAnchor(void*pWorld, void* options,  MapVector3f anchorPoint);

    /**
    设置车速车标的速度值，必须在MapLocatorSetDisplayType之后调用
    @param pWorld                  场景句柄
    @param speedNum             速度值，范围应当>=-1，<= 999，否则无效，当没有gps信号时设置为-1，显示--
    */
    void MapLocatorSetSpeedNum(void *pWorld, int speedNum);

   /**
   设置车速车标的文字参数，必须在MapLocatorSetDisplayType之后调用
   @param pWorld                                     场景句柄
   @param paramForSpeedText            速度文字的参数
   @param paramForUnitText               速度单位的参数
   */
   void MapLocatorSetSpeedTextParam(void *pWorld, const MapLocatorSpeedTextParam *paramForSpeedText,const MapLocatorSpeedTextParam *paramForUnitText);

    /**
    获取当前车标的种类
    @param pWorld               场景句柄
    @return             当前的车标种类
    */
    MapLocatorDisplayType MapLocatorGetDisplayType(void *pWorld);

    /**
    设置当前车标的种类
    @param pWorld               场景句柄
    @param type                    车标类型枚举
    */
    void MapLocatorSetDisplayType(void *pWorld, MapLocatorDisplayType type);


    /**
    设置扫光参数
    @param pWorld 		场景句柄
    @param options 		扫光参数
     */
    void MapLocatorSetScanLightOptions(void *pWorld, const MapLocatorScanLightOptions* options);

		/**
		设置扫光关闭
		@param pWorld               场景句柄
		@param enabled	        是否开启扫光
		 */
		void MapLocatorSetScanLightEnabled(void *pWorld, bool enabled);
	
		/**
		设置车标呼吸动画
		@param pWorld               场景句柄
    @param componet           车标组件
		@param options            呼吸效果参数
		*/
		void MapLocatorStartBreatheAnim(void *pWorld, MapLocatorComponent component, const MapLocatorBreatheOptions* options);
	
		/**
		车标呼吸状态动画
		@param pWorld            场景句柄
    @param componet        车标组件
		@param end_type	      结束方式
		*/
		void MapLocatorEndBreatheAnim(void *pWorld, MapLocatorComponent component, MapLocatorBreatheEndType end_type);

		/**
    设置精度圈参数
    @param pWorld       场景句柄
    @param options     绘制参数
     */
		void MapLocatorSetNaviAccuracyCircleOptions(void *pWorld, const MapNaviAccuracyCircleOptions* options);


    /**
    设置当前车标的透视效果透明度
    @param pWorld               场景句柄
    @param transparency         半透车标的透明度[0, 1.0]。默认值1.0，车道级导航建议设置0.4
    */
    void MapLocatorSetTransparency(void *pWorld, float transparency);

    /**
    获取当前车标的透视效果透明度
    @param pWorld               场景句柄
    */
    float MapLocatorGetTransparency(void *pWorld);

    /**
   	设置骨骼动画的参数
   	@param pWorld               场景句柄
   	@param action              动画参数的json字串
   	*/
    void MapLocatorSetSkeletonAnimAction(void *pWorld, const char* action);
    
    /**
     * 设置自车3D模型的大小是否随着地图缩放而变化。
     * 普通导航场景下，自车3D模型大小通常不应该随着地图缩放而改变，始终显示恒定的像素大小。
     * 车道级导航场景下，自车3D模型大小需要和车道宽度保持一致，会随着地图缩放而变化。
     * 默认为FALSE。
     @param pWorld        场景句柄
     @param isChange    模型大小是否随地图缩放
     */
    void MapLocatorSetLocatorSizeChangeWhenZoomMap(void* pWorld, bool isChange);
    void MapLocatorSetLocatorSetZoomMapLevel(void* pWorld, float scaleLevel);
    /**
     * 设置车标cover层占据3d模型的比例，必须在设置了cover对象之后调用，默认值0，0显示原始贴地效果
     * @param horizon_ratio 水平方向cover图片占据模型的比例
     * @param height_ratio 高度方向cover图片占据模型高度比例
     */
    void MapLocatorSetCoverOnModelRatio(void *world, float horizon_ratio, float height_ratio);
    /**
     * 设置车标是否开启分层绘制功能
     * @param pWorld        场景句柄
     * @param layered_rendering 是否开启分层绘制功能
     * 开启：车标部分（非 IndicatorIcon,cover）提前到 点类型的overlay之前绘制
     * 关闭：车正常绘制
     */
     void MapLocatorSetLayeredRendering(void *pWorld, bool layered_rendering);
#ifdef __cplusplus
}
#endif

#endif /* GLMapLib_LocatorCompass_h */
