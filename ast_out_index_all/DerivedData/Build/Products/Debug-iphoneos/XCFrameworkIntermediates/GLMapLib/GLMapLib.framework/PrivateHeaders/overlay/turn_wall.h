#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/turn_wall_options.h"

namespace tencentmap{
class Macro4KTurnWall;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT TurnWall : public Overlay{
    friend class OverlayFactory;
public:
    void SetOptions(const TurnWallOptions & options);
    
protected:
    std::shared_ptr<tencentmap::Macro4KTurnWall> GetImpl();
    
    using Overlay::Overlay;
    
};

}
#endif
