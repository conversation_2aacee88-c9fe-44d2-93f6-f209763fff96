//
// Created by cuipanpan on 2021/10/14.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "../base/map_define.h"
#include "overlay.h"
#include "common_lane_options.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT RegionLaneOptions : public OverlayOptions{
public:
    RegionLaneOptions();
    RegionLaneOptions(const RegionLaneOptions & other);
    RegionLaneOptions& operator=(const RegionLaneOptions & other);
    ~RegionLaneOptions();

    /**
     * 设置左右边线点集，绘制车道面
     * @param left_points 左边border点集
     * @param right_points 右边border点集
     */
    RegionLaneOptions & SetPoints(const std::vector<MapVector3d> & left_points, const std::vector<MapVector3d> & right_points);

    void GetPoints(std::vector<MapVector3d>& left_points, std::vector<MapVector3d>& right_points) const;

    /**
     * 设置车道面颜色，格式：rgba
     */
    RegionLaneOptions & SetLaneColor(long lane_color);
    long GetLaneColor() const;

    /**
     * 设置渐隐渐显动画时间，单位秒
     * @param time 动画时间
     */
    RegionLaneOptions & SetAnimationTime(float time);
    float GetAnimationTime() const;

    void CoordinateTransform(MapImpl * mapImpl);

    virtual OverlayType GetType() const override { return OverlayType::RegionLane; }
    
private:
    struct Impl;
    std::shared_ptr<RegionLaneOptions::Impl> GetImpl();
    std::shared_ptr<RegionLaneOptions::Impl> GetImpl() const;
};

}
#endif