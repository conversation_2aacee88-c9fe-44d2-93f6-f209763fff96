//
//  animation_value_object.h
//  GLMapLib2.0
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_ANIMATION_VALUE_H__
#define __MAPAPI_ANIMATION_VALUE_H__

#include <stdio.h>
#include <vector>
#include "base/map_define.h"

namespace MAPAPI {

class Animation;
/**
 * 动画目标值类,封装动画目标值
 */
class TXMAPSDK_EXPORT AnimationValue{
public:
    AnimationValue();
    
    /**
     * 设置的动画值，支持类型: int, float, double
     * 比如Marker Scale动画的目标值
     * std::vector<float> scale{2.0,2.0};
     * SetValue(scale);
     */
    template<typename T>
    void SetValue(std::vector<T>& value);
    
protected:
    friend class Animation;
    class Impl;
    std::shared_ptr<Impl> _impl;
};

}

#endif /* __MAPAPI_ANIMATION_VALUE_H__ */
