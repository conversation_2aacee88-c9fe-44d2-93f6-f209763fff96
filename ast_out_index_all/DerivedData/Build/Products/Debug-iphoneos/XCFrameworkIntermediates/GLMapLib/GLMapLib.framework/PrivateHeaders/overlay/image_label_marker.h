//
//  image_label_marker.h
//  Pods
//
//  Created by libin on 2023/12/21.
//
#ifndef NOT_USE_IN_OPENSDK
#ifndef image_label_marker_h
#define image_label_marker_h

#include "overlay/marker.h"
#include "overlay/marker_options.h"

namespace tencentmap{
class MapMarkerImageLabel;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT ImageLabelMarker : public Marker{
    friend class OverlayFactory;
public:
    ImageLabelMarker();
    /**
     * 修改ImageLabelMarker所有属性
     *
     */
    virtual void SetOptions(const OverlayOptions & options) override;
    
    virtual void SetTextOptions(const TextOptions & textOptions) override;
    virtual TextOptions & GetTextOptions() override;
    
    virtual void SetImages(const std::string &markerName, const MapVector2f markerAnchor,
                           const std::string &labelName, const MapVector2f labelAnchor,
                           const std::string &closeName, const MapVector2f closeAnchor);
    
    virtual void SetLabelHidden(const bool);
    
    virtual void SetSpacing(float markerLabelSpacing, float markerTextSpacing);
    
private:
    std::shared_ptr<ImageLabelMarkerOptions> Options() const{
        return std::dynamic_pointer_cast<ImageLabelMarkerOptions>(options_);
    }
    
    using Marker::Marker;
    std::shared_ptr<tencentmap::MapMarkerImageLabel> GetImpl();
};

}

#endif /* image_label_marker_h */
#endif