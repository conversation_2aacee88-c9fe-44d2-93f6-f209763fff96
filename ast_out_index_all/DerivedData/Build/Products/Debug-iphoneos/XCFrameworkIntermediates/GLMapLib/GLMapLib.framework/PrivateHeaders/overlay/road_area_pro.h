#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "had_structure.h"
#include "overlay/overlay.h"
#include <functional>

namespace tencentmap {
    class MapRoadAreaProInfo;
    class MapRoadAreaPro;
}

namespace MAPAPI {


class TXMAPSDK_EXPORT RoadAreaProOptions : public OverlayOptions {
public:
    friend class tencentmap::MapRoadAreaProInfo;
    
    struct UpdateCallBackParam {
        int overlay_id;
        MapVector3d from_pos;
        MapVector3d locator_pos;
        MapVector3d to_pos;
        float locator_angle;
    };

    using UpdateCallback = std::function<void(UpdateCallBackParam)>;
    using AnimationCallback = std::function<void(bool)>;
    // 回调函数中车标坐标系：墨卡托坐标系，Y轴向下
    RoadAreaProOptions();
    RoadAreaProOptions(const RoadAreaProOptions &options);
    ~RoadAreaProOptions();

    virtual OverlayType GetType() const override {
        return OverlayType::EqualWidthGuideLine;
    }

    void setUpdateFn(const UpdateCallback &fn);
    const UpdateCallback *getUpdateFn() const;

    /**
     * 引导线alpha动画
     * @param start 开始alpha
     * @param end 结束alpha
     * @param animationTime 动画时长（秒单位）
     * @param animationCallback 动画回调
     */
    void setAlpha(float start, float end, float animationTime, const AnimationCallback &animationCallback);

    void setGuideLineTexture(TMBitmap *bitmap) {
        TMBitmapRelease(&mGuideLineBitmap);
        mGuideLineBitmap = bitmap;
        TMBitmapRetain(mGuideLineBitmap);
        mGuideLineBitmapChanged = true;
    }

    void setFlowArrowTexture(TMBitmap* bitmap) {
        TMBitmapRelease(&mFlowArrowBitmap);
        mFlowArrowBitmap = bitmap;
        TMBitmapRetain(mFlowArrowBitmap);
        mFlowArrowBitmapChanged = true;
    }

    TMBitmap *getGuideLineTexture() const {return mGuideLineBitmap;}
    TMBitmap *getFlowArrowBitmap() const {return mFlowArrowBitmap;}

    bool isGuideLineTextureChanged() const {return mGuideLineBitmapChanged;}
    bool isFlowArrowBitmapChanged() const {return mFlowArrowBitmapChanged;}
    bool isAlphaChanged() const {return mAlphaChanged;}

    virtual std::shared_ptr<OverlayOptions> CopyModifyData() override;

    void ResetChangeSign();
    
    std::string Description() const override;
    
private:
    std::unique_ptr<UpdateCallback> mUpdateFun = nullptr;

    float mStartAlpha = 0.0f;
    float mEndAlpha = 0.0f;
    float mAnimationTime = 0.0f;
    AnimationCallback mAlphaAnimationCallback = nullptr;

    TMBitmap *mGuideLineBitmap = nullptr;
    TMBitmap *mFlowArrowBitmap = nullptr;

    bool mAlphaChanged = false;
    bool mGuideLineBitmapChanged = false;
    bool mFlowArrowBitmapChanged = false;
    
    struct Impl;
    std::shared_ptr<RoadAreaProOptions::Impl> GetImpl();
    std::shared_ptr<RoadAreaProOptions::Impl> GetImpl() const;
};

struct GuideLineVertex {
    MapVector3f points; // 点坐标
    MapVector2f uv;     // 纹理坐标
    float ratio;
};


/**
 * 该overlay所有接口需在渲染线程调用
 */
class TXMAPSDK_EXPORT RoadAreaPro : public Overlay {
    friend class OverlayFactory;

public:
    /**
     *
     * 引导线数据协议：左右boundary、breakpoints、纹理坐标
     * 坐标系：墨卡托坐标系
     */
    void setGeometryData(const mapbase::HAD::RoadAreaProData &data);

    /*
     * 设置引导线的路况纹理
     * */
    void setGuideLineTexture(TMBitmap* bitmap);

    /*
     * 设置引导线的擦除位置及透明渐变区间
     * eraseGradStartRatio: 擦除透明渐变开始的位置
     * eraseGradEndRatio: 擦除透明渐变结束的位置
     * endGradStartRatio: 尾部透明渐变开始的位置
     * endGradEndRatio: 尾部透明渐变结束的位置
     * required: eraseGradEndRatio <= endGradStartRatio
     * */
    void setGuideLineAlphaRange(float eraseGradStartRatio,
                                float eraseGradEndRatio,
                                float endGradStartRatio,
                                float endGradEndRatio);

    /*
     * 设置流向箭头的纹理
     * */
    void setFlowArrowTexture(TMBitmap* bitmap);

    /*
     * 设置流向箭头在引导线上的显示区间及透明度
     * startRatio: 流向箭头显示的起始位置
     * endRatio: 流向箭头显示的结束位置
     * alpha: 流向箭头透明度
     * */
    void setFlowArrowProperties(float startRatio, float endRatio, float alpha);

    /*
     * 设置alpha纹理
     * */
    void setAlphaTexture(TMBitmap* bitmap);

    /*
     * 设置alpha纹理在mesh上的显示区间
     * startRatio: 显示区间的起始位置，范围-1.0f~2.0f
     * endRatio: 显示区间的结束位置，，范围-1.0f~2.0f
     * endRatio > startRatio时有效；startRatio,endRatio都不在0.0分～1.0f区间时，则路况颜色不显示
     * */
    void setAlphaTextureProperties(float startRatio, float endRatio);
    
    //------------以下接口渲染内部测试使用-----------------
    void setGeometryDataAsync(mapbase::HAD::RoadAreaProData data);
    void setGuideLineTextureAsync(const std::string &texture);
    void setFlowTextureTextureAsync(const std::string &texture);

    void setAreaAlphaRangeAsync(float eraseGradStartRatio,
                                float eraseGradEndRatio,
                                float endGradStartRatio,
                                float endGradEndRatio);
    void setFlowArrowPropertiesAsync(float startRatio, float endRatio, float alpha);
    void SetAlphaAnimation(float start, float end, float animationTime);

    //------------以上接口渲染内部测试使用-----------------
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::MapRoadAreaPro> GetImpl();
};

}
#endif
