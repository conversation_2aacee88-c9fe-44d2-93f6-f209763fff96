//
//  map_camera.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/27.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_CAMERA_H__
#define __MAPAPI_BASE_MAP_CAMERA_H__

#include <stdio.h>
#include <memory>
#include <cstring>
#include <map>
#include "base/map_define.h"
#include "base/camera_options.h"
#include "animation/animation_enable_object.h"

namespace MAPAPI {

/*
 * sight: 屏幕内地理坐标范围, origin 为经纬度左上角, origin + size 为右下角
 * corners: 屏幕左上、左下、右下、右上 对应的经纬度坐标
 */
struct SightInfo
{
    MapRectD sight;
    MapVector2d corners[4];
};
/**
 * 地图相机控制器：set/get 地图的center, scale, skew, rotate等
 */
class TXMAPSDK_EXPORT MapCamera :public MapComponent, public AnimationEnableObject{
public:
    /**
     * 屏幕坐标转换为地理坐标
     *
     * @param screenCoordinate 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     * @param excludeSky 是否计算天空盒，默认为true,为false则不计算天空盒
     */
    MapVector2d GetWorldCoordinate(const MapVector2f & screenCoordinate,bool excludeSky = true);
    /**
     * 地理坐标转换为屏幕坐标
     *
     * @return 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     */
    MapVector2f GetScreenCoordinate(const MapVector2d & geoCoordinate);
    
    /**
     * 3D地理坐标转换为屏幕坐标
     *
     * @return 屏幕坐标，单位：像素；左上角 (0.0f, 0.0f)
     */
    MapVector2f GetScreenCoordinate(const MapVector3d & geoCoordinate);
    
    /**
     * 设置俯视看时，是否使用正投影 (效果: 正2D下 楼块是否3D效果带阴影)
     * 备注: 引擎默认true, 开平:需要单独设置
     *
     * @param enabled    使用正投影开关，true:打开， false:关闭；
     */
    void SetUsingOrthoWhenLookingDown(bool enabled);
    
    /**
     * 设置viewPort，坐标pixel
     *
     * @param x x
     * @param y y
     * @param width width
     * @param height height
     */
    void SetViewport(int x, int y, int width, int height);
    
    /**
     * 获取viewPort，坐标pixel
     * @param x x
     * @param y y
     * @param width width
     * @param height height
     */
    void GetViewport(int& x, int& y, int& width, int& height);

    /**
     * 获取张角
     * @return 张角fovy
     */
    float  GetFovy();
    
    /**
     * 获取比例尺缩放系数
     *
     * @return 缩放比例 20级->1.0;19级->0.5;18级->0.25...
     */
    double  GetScale();
    
    /**
     * 设置比例尺缩放系数
     *
     * @param scale 比例尺
     * @param animated 是否开启动画
     */
    void   SetScale(double scale, bool animated);
    
    /**
     * 设置比例尺缩放系数
     *
     * @param scale 比例尺
     * @param durationInSeconds 动画时间,单位秒,默认0.5s, 有效范围(0,5),设置值在有效范围外使用默认时间
     */
    void   SetScale(double scale, float durationInSeconds);
    
    /**
     * 获取缩放级别
     * @return 缩放级别
     */
    int    GetScaleLevel();
    
    /**
     * 设置缩放级别
     *
     * @param scaleLevel 缩放级别
     * @param animated 是否开启动画渐变
     */
    void    SetScaleLevel(int scaleLevel, bool animated);
    
    /**
     * 设置缩放级别
     *
     * @param scaleLevel 缩放级别
     * @param durationInSeconds 动画时间,单位秒,默认0.5s, 有效范围(0,5),设置值在有效范围外使用默认时间
     */
    void    SetScaleLevel(int scaleLevel, float durationInSeconds);
    
    /**
     缩放比例到缩放级别的转换，线程无关
     
     @param scale 缩放比例
     @return 缩放级别
     */
    int     ScaleToScaleLevel(double scale);
    float   ScaleToScaleLevelF(double scale);
    double  ScaleLevelToScale(float scalelevel);
    
    /**
     * 获取地图中心点
     */
    MapVector2d GetCenter();
    
    /**
     设置地图中心点
     
     @param mapPoint 中心点坐标
     @param animated 是否开启动画渐变
     @param didStopCallback 动画结束回调
     @param pCallBackObject 回调对象句柄
     */
    void    SetCenter(MapVector2d mapPoint, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     * @brief 获取像素点代表的距离；
     * @param fov_angle 传入Fov角度，默认(0.0f)使用相机当前fov
     * @param scale 目标比例尺，默认(0.0f)使用底图当前的比例尺
     */
    double  GetPerPixelDistance(float fov_angle = 0.0f, float scale = 0.0f);
    
    /**
     * @brief 计算相机到中心点距离
     * @param view_port_height 需要计算的viewport，默认(0.0f)使用底图当前的viewport
     * @param scale 目标比例尺，默认(0.0f)使用底图当前的比例尺
     */
    float   GetEyeCenterDistance(float view_port_height = 0.0f, float scale = 0.0f);
    
    /**
     * 手势平移操作
     *
     * @param dx 屏幕x方向像素偏移
     * @param dy 屏幕y方向像素偏移
     */
    void    MoveBy(float dx, float dy, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     * 检查是否能够继续放大地图
     */
    bool    CanZoomIn();
    
    /**
     *检查是否能够继续缩小地图
     */
    bool    CanZoomOut();
    
    /**
     * 放大地图
     *
     * @param x 缩放中心点x像素坐标
     * @param y 缩放中心点y像素坐标
     * @return -1, 缩放失败  ; 0, 以（x，y）为中心点缩放; 1 以屏幕中心点进行缩放
     */
    int     ZoomIn(float x, float y, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     * 缩小地图
     *
     * @param animated 动画渐变
     * @return -1, 缩放失败;1,缩放成功
     */
    int     ZoomOut(bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     * 获取倾斜角: 视野方向中心轴与地面垂线的夹角
     */
    float   GetSkew();
    
    /**
     * 设置倾斜角: 视野方向中心轴与地面垂线的夹角
     * @param skew 倾斜角
     * @param animated XXX
     * @param didStopCallback XXX
     * @param context XXX
     * @param curveType 差值方式
     */
    void    SetSkew(float skew, bool animated, MapAnimationDidStopCallback didStopCallback, void *context, MapAnimationCurveType curveType = MapAnimationCurveType_EaseIn);
    
    /**
     * 设置倾斜角: 视野方向中心轴与地面垂线的夹角
     * @param skew 倾斜角
     * @param durationInSeconds 动画时间,单位秒,默认0.5s, 有效范围(0,5),设置值在有效范围外使用默认时间
     * @param didStopCallback XXX
     * @param context XXX
     * @param curveType 差值方式
     */
    void    SetSkew(float skew, float durationInSeconds, MapAnimationDidStopCallback didStopCallback, void *context, MapAnimationCurveType curveType = MapAnimationCurveType_EaseIn);
    
    /**
     * 获取旋转角
     * 旋转角：假设墨卡托地图南极方向为V0，视野方向中心轴映射到地面的矢量为V1，该角度为V0逆时针旋转到V1所经历的角度。
     */
    float   GetRotate();
    
    /**
     * 设置旋转角
     * 旋转角：假设墨卡托地图南极方向为V0，视野方向中心轴映射到地面的矢量为V1，该角度为V0逆时针旋转到V1所经历的角度。
     * @param rotate 旋转角
     * @param animated XXX
     * @param didStopCallback XXX
     * @param context XXX
     */
    void    SetRotate(float rotate, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     * 旋转操作
     * @param centerX 旋转中心点像素坐标x值
     * @param centerY 旋转中心点像素坐标y值
     * @param radian 旋转弧度
     * @return 旋转中心点是否吸附到屏幕中心点
     */
    bool    Rotate(float centerX, float centerY, float radian);
    
    /**
     *滑动手势结束时惯性动画
     *
     * @param speedX 滑动速度X值(pixels per second)
     * @param speedY 滑动速度Y值(pixels per second)
     * @param animated XXX
     * @param didStopCallback XXX
     * @param context XXX
     */
    void    Swipe(float speedX, float speedY, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     * 捏合手势，控制缩放
     *
     * @param centerX 中心点像素坐标X值
     * @param centerY 中心点像素坐标Y值
     * @param scale 缩放变化比例
     * @return 是否吸附到屏幕中心点
     */
    bool    Pinch(float centerX, float centerY, float scale, float durationInSeconds = 0.0f);
    
    /**
     * 建议废弃的函数，与pinch功能重复
     *
     * TODO:LATER测试是否可以用pinch()替代
     *
     * @param scale XXX
     * @param isPercent XXX
     * @return XXX
     */
    bool    PinchInCenter(float scale, bool isPercent);
    
    /**
     * 全览
     * 在特定倾斜角和旋转角下，通过自动调整缩放比例和地图中心点，将特定地理区域限制在特定屏幕区域内。
     *
     * @param geoRect 地理区域
     * @param devRect 屏幕区域
     * @param skewAngle 目标倾斜角
     * @param rotateAngle 目标旋转角
     * @param screenCenterOffsetType 考虑到执行 Overlook 时可能处于 ScreenOffset 动画中，ScreenCenterOffset 不稳定导致计算结果不稳定，因此由调用方指定 ScreenCenterOffsetType
     * @param userScreenCenterOffset 调用方指定的 ScreenCenterOffset 值，只在 screenCenterOffsetType == User 时生效，参数规格同 MapSetScreenCenterOffset 接口（Y大于0视角向上移，取值范围[-0.5, 0.5]）
     * @param animating XXX
     * @param callback XXX
     * @param context XXX
     * @param durationInSeconds 持续时间，单位s
     */
    void   OverLook(const MapRectD& geoRect, const MapRectD& devRect, float skewAngle, float rotateAngle, bool animating, MapAnimationDidStopCallback callback, void *context, float durationInSeconds);
    void   OverLook(const OverlookParam& overlookParam);

    /**
     * 全览目标显示参数预获取
     *
     * @param geoRect 地理区域
     * @param devRect 屏幕区域
     * @param skewAngle 目标倾斜角
     * @param rotateAngle 目标旋转角
     * @param screenCenterOffsetType 考虑到计算过程可能处于 ScreenCenterOffset 动画中，ScreenCenterOffset 不稳定导致计算结果不稳定，因此由调用方指定 ScreenCenterOffsetType
     * @param userScreenCenterOffset 调用方指定的 ScreenCenterOffset 值，只在 screenOffsetType == User 时生效，参数规格同 MapSetScreenCenterOffset 接口（Y大于0视角向上移，取值范围[-0.5, 0.5]）
     * @return 目标显示参数
     */
    MapDisplayParam GetOverLookDisplayParam(const MapRectD& geoRect, const MapRectD& devRect, float skewAngle, float rotateAngle);
    MapDisplayParam GetOverLookDisplayParam(const CameraOverlookParam& cameraParam);

    /**
     * 导航缩放动画
     * 配合setPaddingToZoomForNavigation使用，将下一个关键点显示在屏幕特定高度。
     *
     * @param nextPoint 下一个关键点
     * @param minScaleLevel 最小缩放级别
     * @param maxScaleLevel 最大缩放级别
     * @param animated XXX
     * @param didStopCallback XXX
     * @param context XXX
     */
    void    ZoomForNavigation(MapVector2d nextPoint, int minScaleLevel, int maxScaleLevel,
                              bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     * 导航缩放动画
     * 配合setPaddingToZoomForNavigation使用，将下一个关键点显示在屏幕特定范围内。
     *
     * @param zoomParameter 缩放动画参数
     *        scalevelStatus和skewAngleStatus 决定缩放结束时的俯仰角和缩放级别
     *        scalevelStatus:
     *              SCALELEVEL_USE_MIN_LEVEL: 缩放结束时,  缩放级别调整为zoomParameter.minScaleLevel
     *              SCALELEVEL_USE_RIGHT_LEVEL: 缩放结束时,  缩放级别调整到使 zoomParameter.nextPoint 正好在padding范围内
     *        skewAngleStatus:
     *              SKEWANGLE_USE_MAX_SKEW: 缩放结束时, 俯仰角度调整为 zoomParameter.maxSkewAngle
     *              SKEWANGLE_USE_MIN_SKEW: 缩放结束时, 俯仰角度调整为 zoomParameter.minSkewAngle
     *        durationInSeconds: 动画时间,默认1.1s, 有效范围(0,5),设置值在有效范围外使用默认时间
     * @param animated 是否带动画
     * @param didStopCallback 回调
     * @param context 上下文
     */
    void    ZoomForNavigation(ZoomForNaviParameter zoomParameter, bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     * 导航缩放动画，不考虑白箭头
     * 配合setPaddingToZoomForNavigation使用，将下一个关键点显示在屏幕特定高度。
     *
     * @param nextPoint 下一个关键点
     * @param minScaleLevel 最小缩放级别
     * @param maxScaleLevel 最大缩放级别
     * @param animated XXX
     * @param didStopCallback XXX
     * @param context XXX
     */
    void    ZoomForPureNavigation(const MapVector2d nextPoint, int minScaleLevel, int maxScaleLevel,
                                  bool animated, MapAnimationDidStopCallback didStopCallback, void *context);
    
    /**
     * 废弃接口, 下个版本删除
     * 计算最佳比例尺，提供一系列地理坐标点和屏幕范围，根据比例尺进行缩放能够让所有的点都位于屏幕内，并且距离中心最远的点恰好位于屏幕边缘
     * 单位：像素
     *
     * @param geoPoints 给定的地理坐标点
     * @param screenRect 给定的屏幕范围
     */
    float getBestScaleLevel(const std::vector<MapVector2d> &geoPoints, const MapRectD &screenRect);
    
    /**
     * 废弃接口, 下个版本删除
     * 通过给定的目标俯仰角计算最佳比例尺，提供一系列地理坐标点和屏幕范围，根据比例尺进行缩放能够让所有的点都位于屏幕内，并且距离中心最远的点恰好位于屏幕边缘
     * 单位：像素
     *
     * @param geoPoints 给定的地理坐标点
     * @param screenRect 给定的屏幕范围
     * @param skewAngle 给定的目标俯仰角(0-65)
     */
    float getBestScaleLevel(const std::vector<MapVector2d> &geoPoints, const MapRectD &screenRect, const float &skewAngle);
    /**
     * 废弃接口, 下个版本删除
     * 通过给定的目标俯仰角计算最佳比例尺，提供一系列地理坐标点和屏幕范围，根据比例尺进行缩放能够让所有的点都位于屏幕内，并且距离中心最远的点恰好位于屏幕边缘
     * 单位：像素
     *
     * @param geoPoints 给定的地理坐标点
     * @param screenRect 给定的屏幕范围
     * @param skewAngle 给定的目标俯仰角(0-65)
     * @param rotateAngle 给定的目标旋转角
     */
    float getBestScaleLevel(const std::vector<MapVector2d> &geoPoints, const MapRectD &screenRect, const float &skewAngle, const float &rotateAngle);
    
     /**
     * 按照指定的地图中心、屏幕区域、俯仰角、旋转角、地图中心的屏幕中心偏移 计算最佳比例尺，使 指定的一系列地理坐标点都位于屏幕内，并且距离地图中心最远的点恰好位于屏幕边缘
     *
     * @param target_center 目标地图中心点
     * @param geo_points 希望显示在屏幕指定区域内的地理坐标点串
     * @param screen_rect 给定的屏幕范围
     * @param skew_angle 给定的目标俯仰角(0-65),单位度
     * @param rotate_angle 给定的目标旋转角,单位度
     * @param screen_center_offset 给定的地图中心在屏幕的偏移,不设置时使用当前的屏幕中心偏移, 合法范围 [0-0.5]
     */
    float getBestScaleLevel(const MapVector2d& target_center, const std::vector<MapVector2d> &geo_points, const MapRectD &screen_rect, const float &skew_angle, const float &rotate_angle, const MapVector2f& screen_center_offset = MapVector2fMake(-1.0f, -1.0f));

    /**
     * 按照指定的地图中心、屏幕区域、俯仰角、旋转角、地图中心的屏幕中心偏移 计算最佳比例尺，使 指定的一系列地理坐标点都位于屏幕内，并且距离地图中心最远的点恰好位于屏幕边缘
     *
     * @param target_center 目标地图中心点
     * @param geo_points 希望显示在屏幕指定区域内的地理坐标点串
     * @param screen_rect 给定的屏幕范围
     * @param skew_angle 给定的目标俯仰角(0-65),单位度
     * @param rotate_angle 给定的目标旋转角,单位度
     * @param fov_angle 给定的目标Fov(10 - 90),单位度，传入0代表使用相机当前Fo v
     * @param screen_center_offset 给定的地图中心在屏幕的偏移,不设置时使用当前的屏幕中心偏移, 合法范围 [0-0.5]
     */
    float getBestScaleLevel(const MapVector2d& target_center, const std::vector<MapVector2d> &geo_points, const MapRectD &screen_rect, const float &skew_angle, const float &rotate_angle, const float& fov_angle, const MapVector2f& screen_center_offset = MapVector2fMake(-1.0f, -1.0f));
  
    /**
     * 根据目标状态计算天空比例
     * @param geo_center 相机中心点
     * @param screen_center_offset 屏幕偏移
     * @param scale 比例尺
     * @param rotate_angle 给定的目标旋转角,单位度
     * @param skew_angle 给定的目标俯仰角,单位度
     * @param fov_angle 给定的目标Fov(10 - 90),单位度，传入0代表使用相机当前Fov
     */
    float GetSkyRatioWithVirtualCamera(const MapVector2d& geo_center, const MapVector2f& screen_center_offset, double scale, float rotate_angle, float skew_angle, float fov_angle = 0.0f);
    
    /**
     * 导航缩放动画，padding限制
     * 单位：像素
     *
     * @param topPadding 限制：屏幕上侧
     * @param leftPadding 限制：屏幕左侧
     * @param bottomPadding 限制：屏幕下侧
     * @param rightPadding 限制：屏幕右侧
     */
    
    void    SetPaddingToZoomForNavigation(float topPadding, float leftPadding, float bottomPadding, float rightPadding);
    
    /**
     * 获取导航缩放动画，padding限制
     * 单位：像素
     *
     * @param topPadding 限制：屏幕上侧
     * @param leftPadding 限制：屏幕左侧
     * @param bottomPadding 限制：屏幕下侧
     * @param rightPadding 限制：屏幕右侧
     */
    void    GetPaddingToZoomForNavigation(float& topPadding, float& leftPadding, float& bottomPadding, float& rightPadding);
    
    /**
     * 设定底图显示的最小比例尺和地理范围
     *
     * @param minScaleLevel    显示的开始比例尺
     * @param restrictBounds   显示的地理范围；地理像素坐标
     * @note  1>当限制范围值均为0, 则代表取消之前设置的限制范围;
     *        2>取消比例尺限制 请用其他现有接口设置;
     */
    void    SetRestrictBounds(int minScaleLevel, MapRectD restrictBounds);
    
    /**
     * 设置地图最小缩放级别
     *
     * @param minScaleLevel 最小缩放级别
     */
    void    SetMinScaleLevel(int minScaleLevel);
    
    /**
     * 设置地图最大缩放级别
     *
     * @param maxScaleLevel 最大缩放级别
     */
    void    SetMaxScaleLevel(int maxScaleLevel);
    
    /**
     * 设置屏幕中心点偏移
     * offset = (offsetX / screenWidth, offsetY / screenHeight)
     *
     * @param offset 中心点下移占屏幕比例(0.0f, 0.0f)无下移;(0.2f, 0.0f)屏幕下侧，距离底部(0.5 - 0.2) = 3/10处.
     * @param duration_in_s 动画时长, 单位s
     * @param begin_from_current 动画起始值是否是当前状态 (新的动画开始的时候,如果上次动画未完成, true:新动画从当前值开始, false:新动画从上次动画的目标结束值开始)
     */
    void SetScreenCenterOffset(MapVector2f offset, float duration_in_s, bool begin_from_current);
    
    /**
     * 获取屏幕中心点偏移（动画过程中，获取的是目标偏移值，而非当前值）
     *
     * @return 中心点偏移（-0.5 ~ 0.5, -0.5 ~ 0.5）
     */
    MapVector2f GetScreenCenterOffset();
    
    /**
     * 设置=false，只是影响了camera的center值，camera的center不再等于地图center
     * 目前手图设置为true, 车机为flase, 内部默认true.
     */
    void SetCenterOffsetByFrustum(bool bCenterOffset);

    
    /**
     * 获取动画的对象类型
     *
     * @return 获取动画的对象类型枚举
     */
    AnimationOwnerEnum GetAnimationOwnerType() const override;
    
    
    /**
     * 获取动画的对象ID
     *
     * @return 获取动画的对象ID，底图为0，Overlay是自己的ID
     */
    int GetID() const override;
    
    SightInfo getSight();
    std::vector<MapVector2d> TransformToMapPixel(const std::vector<MapVector2d> &geoPoints);
 
    /**
     * 获取当前是否为hd场景
     */
    bool IsHdScene();

    /**
     设置是否使用外部hd sd规则
     @param use_external_config true: 外部设置生效  false：外部设置不生效
     @param scene 1: HD优先  2: sd优先  3:4k优先
     */
    void UseHdSdExternalConfig(bool use_external_config, int scene);
 
    /*
    * 获取当前屏幕内scaleLevel和视野Rect
    */
    void getCurrentScaleAndRect(float& scaleLevel, MapRectD& geoRect);

    /**
    * 获取文字显示的视野
    * @param geoRect
    */
    void getAnnotationRect(float& scaleLevel, MapRectD& geoRect);
    
    /**使用一个虚拟的相机计算geo地理坐标对应的屏幕坐标，使用实例现有的screenoffset、viewport、投影矩阵
     * @param center 目标相机的中心点
     * @param scale 目标相机缩放比例
     * @param rotate 目标相机的旋转角
     * @param skew 目标相机的倾斜角
     * @param geo 要转化的地理坐标
     * @param fov fov角度 默认0.0使用当前相机角度
     * @return 屏幕坐标（像素），落在屏幕外时返回（-1，-1）
     */
    MapVector2f GetScreenPointWithVirtualCamera(const MapVector2d& center, double scale, float rotate, float skew, const MapVector3d& geo, float fov = 0.0f);
    
    /**使用一个虚拟的相机计算geo地理坐标对应的屏幕坐标，使用传入的screenoffeset
     * @param center 目标相机的中心点
     * @param scale 目标相机缩放比例
     * @param rotate 目标相机的旋转角
     * @param skew 目标相机的倾斜角
     * @param screenOffset 目标相机的偏移量
     * @param geo 要转化的地理坐标
     * @param fov fov角度 默认0.0使用当前相机角度
     * @return 屏幕坐标（像素），落在屏幕外时返回（-1，-1）
     */
    MapVector2f GetScreenPointWithVirtualCamera(const MapVector2d& center, double scale, float rotate, float skew,
                                                const MapVector2f& screenOffset, const MapVector3d& geo, float fov = 0.0);
    
    /**使用一个虚拟的相机计算屏幕坐标对应的geo地理坐标
     * @param center 目标相机的中心点
     * @param scale 目标相机缩放比例
     * @param rotate 目标相机的旋转角
     * @param skew 目标相机的倾斜角
     * @param screenOffset 目标相机的偏移量
     * @param screenPos 要转化的屏幕坐标
     * @param fov fov角度 默认0.0使用当前相机角度
     * @return 地理坐标
     */
    MapVector2d GetGeographPointWithVirtualCamera(const MapVector2d& center, double scale, float rotate, float skew,
                                                  const MapVector2f& screenOffset, const MapVector2f& screenPos, float fov = 0.0);
    
    /**创建自定义的相机lod参数，作为内部判断离相机远近的参数
     * @param lod_param  相机lod参数，新设置的会覆盖掉旧的。key:类型ID（不能为0，由端上自定义），value: lod的比例系数，越小远处裁剪的越多（默认值4，必须小于8大于1，否则不生效）
     */
    void SetLodParam(const std::map<int, float>& lod_param);

    
    /**获取当前地图显示部分在屏幕中占的大小，单位像素
     * @return 显示地图的像素大小
     */
    float GetSightLengthOnScreen();

  
  /**
   * 所有地图姿态操作（移动、旋转、缩放、俯仰、中心偏移），对外统一都使用此接口。
   *
   * @param camera_options 目标相机姿态
   * @param animaiton_options 动画参数
   * @param move_way 相机移动方式
   */
  void MoveCamera(const CameraOptions & camera_options, const AnimationOptions & animaiton_options,
                  CameraMoveWay move_way = CameraMoveWay::kMoveTo);
};

}

#endif /* __MAPAPI_BASE_MAP_CAMERA_H__ */
