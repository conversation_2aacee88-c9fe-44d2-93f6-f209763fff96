//
//  model_group.h
//  Pods
//
//  Created by al<PERSON><PERSON><PERSON> on 2024/10/21.
//

#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/model_group_options.h"

namespace tencentmap{
    class ModelGroupOverlay;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT ModelGroup : public Overlay {
    friend class OverlayFactory;
public:
    virtual void SetOptions(const ModelGroupOptions& options);
    
protected:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::ModelGroupOverlay> GetImpl();
};

}
#endif
