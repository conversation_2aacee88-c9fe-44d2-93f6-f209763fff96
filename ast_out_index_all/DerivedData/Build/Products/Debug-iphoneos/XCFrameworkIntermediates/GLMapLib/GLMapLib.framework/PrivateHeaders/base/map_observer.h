//
//  map_observer.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/9.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_OBSERVER_H__
#define __MAPAPI_BASE_MAP_OBSERVER_H__

#include "base/map_define.h"

namespace MAPAPI {

class Marker;

class TXMAPSDK_EXPORT MapObserver{
public:
    virtual ~MapObserver() {}
    
    /*
    * 底图相机变化回调
    * bScaleChange 缩放级别是否变化
    */
    virtual void OnCameraChanged(bool bScaleChange) {}
    
    /*
    * 底图相机停止回调
    */
    virtual void OnCameraChangeStopped() {}
    
    //目前没有此回调了
    //virtual void onCenterChanged() {}
    
    /*
    * 底图缩放级别变化回调
    */
    virtual void OnScaleChanged() {}
    
    /*
    * 底图俯仰角变化回调
    */
    virtual void OnSkewAngleChanged() {}
    
    /*
    * 底图旋转角变化回调
    */
    virtual void OnRotateAngleChanged() {}
    
    /*
    * marker位置变化回调
    */
    virtual void OnMarkerPositionChanged(std::shared_ptr<Marker> marker, const MapVector3d& markerPos) {}

    /*
     * 重载 OnMarkerPositionChanged，弥补其不足，
     * 后续业务应该尽量使用带type的接口
     * 引擎内部会通过GetMarker接口根据markerID 获取对应的marker,
     * 该业务逻辑发生在cpp接口层，所以非cpp接口创建的marker(例如车标)，无法获取marker,此时会返回null
     */
    virtual void OnMarkerPositionChanged(std::shared_ptr<Marker> marker, OverlayType type, const MapVector3d& markerPos) {}
    
    /*
    * 当前激活室内图改变回调
    */
    virtual void OnActiveIndoorBuildingChanged() {}
    
    /*
     * 底图出现异常退出之前的信息通知回调
     */
    virtual void OnReportBeforeExit(const std::string & info) {}
    
    /*
    * 蚯蚓线第一帧时间上报回调
    */
    virtual void OnReportDrawTime(int routeID, double drawConsumeTime) {}
        
    //MapMarkerIcon显示从主图片切换到备选图片，或从备选图片切换到主图片
    //type=1 ,表示，切换到备选图片， =0，表示切换到主图片
    virtual void OnMarkerIconSwitchAlternative(int markerID, int type) {}
    
    /*
     * 上报灯塔信息
     */
    virtual void OnReportStatisticsInfo(const std::string & info) {}

    /*
    * 地图样式切换
    */
    virtual void OnMapStyleChanged(int oldStyleID, int newStyleID) {}
    
    /*
    * 底图资源加载完并绘制完成
    */
    virtual void OnMapLoadingFinished() {}
    
    //地图marker overlay因为被避让而消失
    //TODO:LATER结构比较复杂 暂未实现
    virtual void OnMarkerIsAvoided() {}
    
    //无用了
    //virtual void onPositionChangedAfterDrawFrame() {}
        
    /*
    * 电子眼扎点位置变化通知回调，暂未实现
    */
    virtual void OnSafetyCameraAnchorChanged() {}
    
    /*
    * 3D模型的解析结果
    */
    virtual void OnModel3DResult(const std::string & modeName, bool loadSuccess) {}
    
    /*
    * 底图退出之前回调
    */
    virtual void OnMapDestroyed() {}
    
    /*
     * 底图点击选中item回调
     */
    virtual void onMapTap(MapTapInfo& tapInfo) {}
    
    /*
    * 底图viewport改变回调
    */
    virtual void onViewportChanged(int x, int y, int width, int height) {}
    
    /*
    * 底图padding改变回调
    */
    virtual void onPaddingChanged(int left, int right, int bottom, int top) {}
    
    /*
    * 底图ScreenCenterOffset改变回调
    */
    virtual void onScreenCenterOffsetChanged(float x, float y) {}
    
    /*
     * 底图density改变回调
     */
    virtual void onDensityChanged(float density) {}
    
    /*
     * 底图Fps改变回调
     */
    virtual void onMapFpsChanged(int fps) {}
    
    /*
     * 底图sd hd 状态切换回调
     * param switch_flag 切换状态  1.hd  2.sd 3.4k
     * param reason 切换原因 1. config触发 2. nerd 数据触发
     */
    virtual void onSdHdSwitch(int switch_flag, int reason) {}

    /*
     * 底图landmark碰撞或恢复回调
     * param landmark_id 碰撞或恢复的landmark id
     * param collide_or_resume 碰撞或恢复 0:碰撞 1:恢复
     */
    virtual void onLandmarkCollideOrResume(const std::string& landmark_id, int collide_or_resume) {}

    /**
     * 获取聚合数据中单点元素(不符合聚合条件的)和聚合元素(符合聚合条件的)的百变Marker绘制选项信息
     * @param group_id               聚类图层数据ID, 通过接口createClusterGroup返回的ID
     * @param agileStyle            百变风格,如"day", "night"
     * @param itemKeyList          需要加载的聚合/单点的源数据的ID信息的列表
     *  当元素是单点元素时, 是由该单点的原始数据的唯一ID转换的字符串, 如: "10000"
     *  当元素是聚合元素时, 是由该聚合元素包含的所有单点原始数据的唯一ID转换为字符串并采用下划线拼接而成,
     *    如一个聚合元素包含三个单点数据, 则为"10000_10001_10002"
     * @param callbackId    回调ID, 调用setClusterGroupAgileMarkerOption时传入
     *                      说明: 非功能参数用来统计回调处理性能
     * 说明: 业务层收到回调后为这些元素创建百变Marker绘制选项后, 再通过引擎接口一次性设置给引擎,接口如下:
     *                               setClusterGroupAgileMarkerOption
     */
    virtual void onLoadAgileMarkerOptionList(int group_id, const std::string& agileStyle, const std::vector<std::string>& itemKeyList, int callbackId) {}
};

}

#endif /* __MAPAPI_BASE_MAP_OBSERVER_H__ */
