//
//  map_controller.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/4.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_CONTROLLER_H__
#define __MAPAPI_BASE_MAP_CONTROLLER_H__

#include <stdio.h>
#include <string>
#include <vector>
#include <map>
#include "base/map_define.h"
#include "had_structure.h"

namespace nerd {
namespace api {
class TPIDType;
}
}

namespace MAPAPI {

class MapCore;

class TXMAPSDK_EXPORT MapController : public MapComponent{
public:
    /**
     * 设置帧率
     */
    void SetFps(int fps);

    /**
     * 设置帧率
     */
    int GetFps();
  
    /**
     * 设置帧率
     */
    int GetRealFps();
  
    /**
     * 通过屏幕坐标(pixel)获取地图上的元素
     *
     * @param screenCoordinate 屏幕坐标
     * @param element 返回element
     */
    void OnTap(MapVector2f screenCoordinate, MapTapInfo & element);
    
    /**
     * 屏幕截图
     * Needs OpenGL context
     *
     * @param area 截图区域（像素单位）
     * @param pBuffer 截图数据存储位置（仅支持RGBA格式）
     */
    void Snapshot(MapRectD area, unsigned char *pBuffer);
    
    /**
     * 动态msaa，只有在camera停止发生变化时开启多重采样
     *
     * @param bDynamicMSAA 开关状态
     */
    void SetDynamicMSAA(bool bDynamicMSAA);
    
    /**
     * 更新底图配置资源(底图配置/图片等)
     *
     * 调用场景：从服务器更新最新配置文件之后，调用此接口，触发引擎更新图面样式。
     */
    void UpdateMapResource();
    
    /**
     * reload 所有texture
     */
    void ReloadAllTexture();
    
    /**
     * 重新设置路径
     *
     * @param dataDir 在线缓存数据目录
     * @param configDir 配置文件目录
     * @param satelliteDir 卫星图目录
     * @param offlineDataDir 离线数据目录
     */
    void ResetDataAndConfigPath(const std::string & dataDir, const std::string & configDir,
                                const std::string & satelliteDir, const std::string & demDir,
                                const std::string & offlineDataDir);
    
    /*
     * 重新设置路径；更轻量； 仅仅改变路径，不需要重新创建数据引擎
     * @param configDir 配置文件目录
     */
    void ResetConfigPath(const std::string & configDir);
    
    /**
     * 清除缓存
     * Thread safe, usage: GLMapLockEngine(); GLMapClearCache(); GLMapUnlockEngine();
     */
    void ClearCache();
    
    /**
     * lock engine
     * 清除缓存，离线地图现在完成需要lock engine
     */
    void LockEngine();
    
    /**
     * unlock engine
     */
    void UnlockEngine(void *pWorld);
    
    /**
     * 获取背景颜色
     */
    MapVector4ub GetBackgroundColor();

    /**
     * 设置天空盒子显示时，天空所占比例。范围（0， 1.0）
     */
    void SetSkyRatioOnScreen(float ratio);
    
    /**
     * Check Cache, when it Exceed the size, reduce the cache;
     * 车机专用
     */
    int CheckAndClearMapCache(int nSize);
    
    /**
     * 获取地图城市名称
     *
     * @param x x 坐标
     * @param y y 坐标
     */
    std::string GetCityName(int x, int y);
    
    /**
     * 从服务端获取数据版本。日更新场景下可能会用。
     */
    void FetchMapVersions();
    
    /**
     * 获取本地数据版本
     */
    int GetDataVersion();
    
    /**
     获取当前渲染引擎版本号
     */
    std::string GetMapEngineVersion();
    
    /**
     * 查询指定图区范围内的城市列表
     */
    int QueryCityList(MapRectD geoRect, int scaleLevel, int* cityList, int count);
    
    /**
     * 通用的设置参数接口
     */
    void SetPipe(TXMapDataType type, TXMapEventType eventType, void* pValue);
    
    /**
     * 设置Marker需要避让的全量UI控件区域; 当个数为0代表清空UI区域, 主线程调用；
     * @param uiAreas 控件区域列表；像素坐标
     * @param size    控件区域个数
     * @param isDebugUiAreas 调试使用，将框画到屏幕上
     */
    void SetMarkerAvoidingUIAreas(TMRect* uiAreas, int size, bool isDebugUiAreas);
    
    /**
     * 启用临时任务，任务执行后立即回调(MapCallBack_MapEvent);
     *
     * @param taskType        任务类型
     * 1> MapTaskType_CheckMapLoadingFinished 类型时回调类型为 MapCallBackType_MapLoadingFinished
     * @return 是:启动成功; 否:启动不成功
     * 备注：首次创建引擎实例，内部默认启动MapTaskType_CheckMapLoadingFinished任务，执行一次回调；其他时机 需要外部触发；
     */
    bool StartTaskAndDidStopCallBack(MapTaskType taskType);
    
    /**
     设置Icon动画开关
     @param enable    Icon动画开关
     */
    void SetIconAnnotationEnable(bool enable);

    /**
     设置Icon动画的方向
     @param direct    Icon动画方向
     */
    void SetIconAnnotationDirect(bool direct);

    /**
     设置Icon动画的classcode
     @param classcode    动画icon的classcode
     @param freq    动画批次
     */
    void SetIconAnnotationClassCode(int classcode, int freq);

    /**
     设置引导面经过的鱼骨数据
     */
    void SetLaneFishBoneData(
        const std::map<nerd::api::TPIDType,
        std::vector<mapbase::HAD::LaneGroupData> >& lane_group_data,
        const std::map<nerd::api::TPIDType,
        mapbase::HAD::CrossData>& cross_data
    );

    void SetLaneTransparentScene(mapbase::HAD::TransparentScene transparentScene);

    /**
     * 设置当前车道上的隧道场景
     * @param guideLaneSceneScene 当前车道的隧道场景
     * @param animationDuration  动画效果时长，单位为秒
     */
    void SetGuideLaneTunnelScene(mapbase::HAD::GuideLaneScene guideLaneSceneScene, float animationDuration);

    /**
     * 增量更新引导面伴随面经过的lanegroupids
     *  @param key 路线id，lane_group_ids, 路线经过的 lane_group_ids
     *  @param lane_group_ids，若传空，表示该路线需要被删除；其他情况，表示该路线经过 lane_group_ids 有更新
     */
    void UpdatePassedLaneGroupID(const std::string& key, const std::vector<nerd::api::TPIDType>& lane_group_ids);

    /**
     * 切换路线
     * @param key: 路线id
     */
    void SwitchRoute(const std::string& key);
    
    /**
     * 开启瓦片显示调试功能
     */
    void ShowDebugTile(bool b_show);
    
    /**
     * 开启帧号显示调试功能
     * @param b_show 是否显示帧号
     * @param pos 帧号显示的像素位置
     * @param alpha 帧号透明度 0-1
     * @param color 帧号颜色, RGBA
     * @param fontSize 帧号字体大小
     */
    void ShowFrameNumber(bool b_show, MapVector2i pos, float alpha, MapColor4ub color, int fontSize);
    
    /**
     * 开启显示文字避让框
     * @param b_show  true 显示
     */
    void ShowPointDebugRect(bool b_show);

#ifndef NOT_USE_SCAN
	/**
	 *	 设置路面扫光动画
	 */
	void SetRoadScanLight(const RoadScanLightOptions& options);
	
	/**
	 *	 打断路面扫光动画
	 */
	void ShutDownRoadScanLight();
#endif
};

}

#endif /* __MAPAPI_BASE_MAP_CONTROLLER_H__ */
