//
//  MapDataType.h
//  GLMapLib2.0
//
//  Created by fullname on 9/7/16.
//  Copyright © 2016 nopwang. All rights reserved.
//

#ifndef MapDataType_h
#define MapDataType_h

#include <math.h>
#include <stdint.h>
#include "MapDefine.h"

typedef union MapVector2f {
  struct {
    float x, y;
  };
  struct {
    float width, height;
  };
} MapVector2f;

typedef union MapVector2d {
  struct {
    double x, y;
  };
  struct {
    double width, height;
  };
  struct {
    double longitude, latitude;
  };
} MapVector2d;

typedef union MapVector2i {
  struct {
    int x, y;
  };
  struct {
    int width, height;
  };
} MapVector2i;

typedef union MapVector2ub {
  struct {
    unsigned char x, y;
  };
} MapVector2ub;

typedef union MapVector3f {
  struct {
    float x, y, z;
  };
} MapVector3f;

typedef union MapVector3d {
  struct {
    double x, y, z;
  };
  struct{ double longitude, latitude, altitude; };
} MapVector3d;

typedef union MapVector4f {
  struct {
    float x, y, z, w;
  };
  struct {
    float r, g, b, a;
  };
} MapVector4f;
typedef MapVector4f MapColor4f;

typedef union MapVector4ub {
  struct {
    unsigned char x, y, z, w;
  };
  struct {
    unsigned char r, g, b, a;
  };
} MapVector4ub;
typedef MapVector4ub MapColor4ub;

typedef union MapVector4i {
  struct {
    int x, y, z, w;
  };
} MapVector4i;

typedef struct MapRectF {
  MapVector2f origin;
  MapVector2f size;
} MapRectF;

typedef union MapRectD {
    struct {
        MapVector2d origin;
        MapVector2d size;
    };
    struct {
        MapVector2d left_up;
        MapVector2d right_down;
    };
} MapRectD;

typedef struct MapEdgeInsets {
  float top, left, bottom, right;
} MapEdgeInsets;

typedef struct MapLocation {
  double longitude;
  double latitude;
} MapLocation;

typedef struct MapLaneID {
    uint32_t tile_id;
    uint32_t lane_id;
} MapLaneID;

typedef struct MapRoadScanOptions {
	int 		duration;				// 扫光时长，单位ms
	float 		line_width;				// 扫光线宽度，单位dp
	float 		area_width;				// 扫光面宽度，单位dp
	float 		frame_width;			// 扫光边框宽度，单位dp
	bool 		is_under_guide_surface; // 扫光是否在引导面以下
	const char* line_texture_addr;		// 扫光线纹理资源名
	const char* area_texture_addr;		// 扫光面纹理资源名
	const char* frame_texture_addr;		// 扫光边框纹理资源名
} MapRoadScanOptions;

typedef struct MapTree {
  MapVector2d coordinate;
  float height;
  float scale;
  float angle;
} MapTree;

typedef struct MapTextDrawInfo {
  unsigned short* texts;
  int count;
  int fontSize;
  bool bBold;
} MapTextDrawInfo;

enum MapBitmapFormat {
  MapBitmapFormat_RGBA8888 = 0,
  MapBitmapFormat_RGB888 = 1,
  MapBitmapFormat_RGB565 = 2,
  MapBitmapFormat_RGBA4444 = 3,
  MapBitmapFormat_A8 = 4,
  MapBitmapFormat_Num = 5
};

typedef struct MapBitmap {
  enum MapBitmapFormat format;
  MapVector2i size;  // width and height in pixel.
  int rowSize;       // row size in byte.
  void* pData;       // data buffer.
} MapBitmap;

typedef enum _SCALELEVEL_STATUS {
  SCALELEVEL_USE_MIN_LEVEL = 0,
  SCALELEVEL_USE_RIGHT_LEVEL
} SCALELEVEL_STATUS;

typedef enum _SKEWANGLE_STATUS {
  SKEWANGLE_USE_MAX_SKEW = 0,
  SKEWANGLE_USE_MIN_SKEW
} SKEWANGLE_STATUS;

typedef struct _ZoomForNaviParameter {
  SCALELEVEL_STATUS scalevelStatus;
  SKEWANGLE_STATUS skewAngleStatus;
  int minScaleLevel;
  int maxScaleLevel;
  float minSkewAngle;
  float maxSkewAngle;
  MapVector2d nextPoint;
  float durationInSeconds;
} ZoomForNaviParameter;

typedef enum {
  MapUrlTagType_BaseMap = 0,              // default "mvd_map"
  MapUrlTagType_Traffic,                  // default "mobile_traffic"
  MapUrlTagType_StreetView,               // default "mobile_street"
  MapUrlTagType_IndoorBuilding,           // default "indoor_map"
  MapUrlTagType_Landmark,
  MapUrlTagType_Config,
  MapUrlTagType_BlockRoad,
  MapUrlTagType_HandDraw,
  MapUrlTagType_Satellite,
  MapUrlTagType_Dem,
  MapUrlTagType_Num,

} MapUrlTagType;

/**
 地图内部支持的任务类型
 */
typedef enum {
  MapTaskType_NoMapTask = 0,
  MapTaskType_CheckMapLoadingFinished,  // 所需资源LoadState_Loaded 同时渲染完 不需要再渲染
  MapTaskType_Num
} MapTaskType;

typedef enum {
  TXMapDataType_Int = 0,
  TXMapDataType_Double = 1,
  TXMapDataType_CString = 2,
  TXMapDataType_WString = 3,
  TXMapDataType_Null
} TXMapDataType;

typedef enum {
  TXMapEventType_ThemeMapSetSceneID = 0,
  TXMapEventType_IndoorMapSetGroup = 1,
  TXMapEventType_BuildingMapSetOrtholooking = 2,
  TXMapEventType_InteractorPinchMode = 3,
  TXMapEventType_RenderContextScale = 4,
 TXMapEventType_LightStyleType = 5,//环境光样式
    TXMapEventType_SkyAnimDuration = 6,//天空动画时长

 TXMapEventType_DymaticSkyboxTime= 7,//动态天空盒
TXMapEventType_SwitchSkybox = 8,//静态动态天空盒切换

 TXMapEventType_SwicthShadow = 9,//阴影开关接口
 TXMapEventType_SetSDRoadWidthScale = 10,  // SD道路的样式宽度因子
    TXMapEventType_SwitchEnlargeMapAA = 11,  // MSAA开关
  TXMapEventType_FrameRenderLimit =12,//单帧渲染
    TXMapEventType_EnlargeMapAA_Quality = 13,//MSAA
    TXMapEventType_Switch_Debug_Info = 14,//调试信息开关
    TXMapEventType_SetShadowCascadeCount = 15,
    TXMapEventType_SetShadowCascadeDistance = 16,
    TXMapEventType_SwitchMapAA = 17,  // 底图抗锯齿
    TXMapEventType_MapAA_Quality = 18,  // 底图抗锯齿质量
    TXMapEventType_LightAnimDuration = 19,//光照动画时长
 
    TXMapEventType_NerdRenderUnitReuse = 20,
    TXMapEventType_ChangeStyleNBuildMeshOnlyTree = 21, //更换样式不重新建模，仅仅对树有效

    TXMapEventType_PoiAdaptNormal = 22, //文字朝向适配法线
    TXMapEventType_PoiShowDebugRect = 23, //文字debug框
    TXMapEventType_ReloadALL = 24,
    TXMapEventType_PoiDotStyle = 25, //麻点图开关
    TXMapEventType_TrafficLineDebug = 26,//路况Debug
    TXMapEventType_UnderStore = 27, //底商模型开关
    
    TXMapEventType_SatelliteDebug = 28,
    TXMapEventType_CameraDebug = 29,
    TXMapEventType_BaseMapDebug = 30,
    TXMapEventType_NerdDebug = 31,
    TXMapEventType_DebugTilePannel=32,
    TXMapEventType_ExtendAnnoTile =33, //文字扩展开关
    TXMapEventType_ExtendAnnoTile_Load =34, //文字扩展加载开关
    TXMapEventType_DebugRenderDensity = 35, // 渲染密度线
    TXMapEventType_Num
 
} TXMapEventType;

/**
 动画曲线类型
 */
typedef enum {
  MapAnimationCurveType_EaseInOut,  // slow at beginning and end
  MapAnimationCurveType_EaseIn,     // slow at beginning
  MapAnimationCurveType_EaseOut,    // slow at end
  MapAnimationCurveType_Linear,
  MapAnimationCurveType_EaseOutBack,
  MapAnimationCurveType_EaseOutElastic,
  MapAnimationCurveType_EaseOut_Inertial
} MapAnimationCurveType;

typedef enum { MapAnimationQualityType_Low, MapAnimationQualityType_High } MapAnimationQualityType;

/**
 地图动画完成回调

 @param finished 动画是否被打断
 @param context context
 */
typedef void (*MapAnimationDidStopCallback)(bool finished, void* context);

// 计算相机位置与姿态时使用的 ScreenCenterOffset 类型
// 目前仅用于 Overlook 相关接口，由调用方指定，默认 Target
typedef enum _OverlookScreenCenterOffsetType {
    OverlookScreenCenterOffsetType_Target = 0, // 若当前处于动画中，则使用动画目标值，否则使用当前值
    OverlookScreenCenterOffsetType_Current,    // 无论是否处于动画中，都使用当前值
    OverlookScreenCenterOffsetType_User        // 使用调用接口时指定的值
} OverlookScreenCenterOffsetType;

typedef struct _CameraOverlookParam {
    MapRectD geoRect;
    MapRectD devRect;
    float skewAngle;
    float rotateAngle;
    OverlookScreenCenterOffsetType screenCenterOffsetType; // 考虑到执行 Overlook 时可能处于 ScreenOffset 动画中，ScreenOffset 不稳定导致计算结果不稳定，因此由调用方指定 ScreenOffsetType
    MapVector2f userScreenCenterOffset; // 调用方指定的 ScreenOffset 值，只在 screenOffsetType == User 时生效，参数规格同 MapSetScreenCenterOffset 接口（Y大于0视角向上移，取值范围[-0.5, 0.5]）
} CameraOverlookParam;

typedef struct _CameraAnimationParam {
    bool animating;
    float durationInSeconds;
    void *callback; // 此处类型用 void*，规避OC编译问题，外部使用时必须传 MapAnimationDidStopCallback 这个类型
    void *context;
} CameraAnimationParam;

typedef struct _OverlookParam {
    CameraOverlookParam cameraParam;
    CameraAnimationParam animParam;
} OverlookParam;

/**
 地图显示状态参数
 */
typedef struct MapDisplayParam {
  MapVector2d center;  //中心点
  double scale;        //比例尺
  float rotateAngle;   //旋转角度
  float skewAngle;     //倾斜角度
  float fovAngle;      //Fov角度
} MapDisplayParam;


/*
 下载任务优先级定义
*/
typedef enum _MapTileDownloadPriority {
  MapTileDownloadPriorityHigh,
  MapTileDownloadPriorityMiddle,
  MapTileDownloadPriorityLow
} MapTileDownloadPriority;

/*
 下载任务数据源类型
*/
typedef enum _MapTileDownloadDataSource {
  MapTileDownloadDataSource_Satellite = 0,
  MapTileDownloadDataSource_Dem,
  MapTileDownloadDataSource_Map,
  MapTileDownloadDataSource_StreetViewRoad,
  MapTileDownloadDataSource_TrafficNetwork,
  MapTileDownloadDataSource_IndoorBuildings,
  MapTileDownloadDataSource_Landmark,
  MapTileDownloadDataSource_TileOverlay,
  MapTileDownloadDataSource_IndoorConfig,
  MapTileDownloadDataSource_Num
} MapTileDownloadDataSource;

/**
 贴图时对应的 x, y, z, 使用时可能需要返回urlString，然后通过MapCallback_Download去网络请求数据
 */
typedef struct MapTileID {
  int x;
  int y;
  int z;
  const char* urlString;
  MapTileDownloadPriority priority;
  MapTileDownloadDataSource datasource;  // 底图发起的下载数据 源类型
  int tileTag;  // 底图发起的下载数据的二级分类；目前仅当datasource==MapTileDownloadDataSource_TileOverlay时；
                // 值为TileOverlayID;
} MapTileID;

#define MAX_GUID_LENGTH 64
typedef enum _InterestAreaType {
  InterestAreaType_Indoor = 0,
  InterestAreaType_ScenicSpot = 1,
} InterestAreaType;

typedef struct _InterestAreaInfo {
  InterestAreaType type;
  char categoryCode[MAX_GUID_LENGTH];
  char guid[MAX_GUID_LENGTH];
} InterestAreaInfo;

typedef struct _InterestAreaInfo InterestAreaInfo;

typedef struct _InterestIndoorAreaInfo
{
    InterestAreaType type;
    char categoryCode[MAX_GUID_LENGTH];
    char guid[MAX_GUID_LENGTH];
    int nDefaultfloor;
} InterestIndoorAreaInfo;

typedef struct _InterestScenicAreaInfo
{
    InterestAreaType type;
    char categoryCode[MAX_GUID_LENGTH];
    char guid[MAX_GUID_LENGTH];
    int nDefaultfloor;
} InterestScenicAreaInfo;

/**
 * 支持黑白名单机制的图层
 */
typedef enum _BlackWhiteListLayer{
    kBlackWhiteListLayerIndoor = 0,  //室内图
    kBlackWhiteListLayerLandmark = 1, //L4模型
}BlackWhiteListLayer;

/**
 黑白名单列表内容类型
 */
typedef enum _BlackWhiteListType {
  BlackWhiteListType_None = 0,      // 列表为空
  BlackWhiteListType_All = 1,       // 列表为所有
  BlackWhiteListType_Appointed = 2  // 列表为指定，由外部传入
} BlackWhiteListType;

/**
 黑白单显示控制规则
 */
typedef struct IndoorShowControlRule {
  BlackWhiteListLayer layer;        //  需要开启黑白单的图层
  bool bBlackListMode;              //  是否为黑名单模式; true黑名单，false为白名单
  BlackWhiteListType guidlisttype;  //  列表内容类型
  const char** guidlist;            //  室内图guid名单列表
  unsigned int guidlistsize;        //  室内图guid名单列表大小(个数)
} BlackWhiteListRule;

typedef struct DownloadData {
    unsigned char* buffer;
    unsigned int bufferSize;
    int http_code;
} DownloadData;

typedef enum
{
    TXMapTile_BaseMap = 2,
    TXMapTile_Building = 6,
} TXMapTileType;


/**
 overlay避让类型
 */

typedef enum {
    Killer_None                  = 0,
    Killer_UIRevive              = 10,      //被上层设置UI避让但设置仍然可用（Group Marker）
    Killer_RouteRevive           = 15,      //被蚯蚓线避让但设置仍然可用（Group Marker）
    Killer_LocatorRevive         = 20,      //被车标避让但设置仍然可用（Group Marker）
    Killer_ScreenLastIntersect   = 60,      //无用
    Killer_ScreenLastOut         = 80,      // 
    Killer_OptionalRoute         = 100,     // 被备选路线避让
    Killer_SelectedRoute         = 110,     // 被主路线避让
    Killer_Overlay               = 200,     // 被overlay避让
    Killer_UI                    = 210,     // 被UI避让
    Killer_Locator               = 230,     // 被车标避让
    Killer_ScreenFirstIntersect  = 260,     //
    Killer_ScreenFirstOut        = 280,     //
    Killer_MainOvlAvoided        = 290,     //绑定主子后，因主避让而自身(子)隐藏
    Killer_SubOvlAvoided         = 291,     //绑定主子后，因自避让而自身(主)隐藏
    Killer_SubOvlSyncAvoided     = 292,     //绑定主子后，因同步其他子状态而自身(子)隐藏
    Killer_Self                  = 0xFFFF   //出屏幕
} RankKiller;

/**
 * 阴影的显示形态
 */
typedef enum _ShadowState{
    Shadow_Disable = 0,  //关闭阴影
    Shadow_Static,       //静态阴影
    Shadow_Dynamic       //动态天空盒联动阴影
}ShadowState;
#define MAX_SHADOW_COLOR_SIZE 10
typedef struct _ShadowSetting{
    ShadowState state;          //阴影的显示形态
    int start_scale;            //阴影显示的起始比例尺
    MapVector2f max_min_ratio;  //阴影长度和本体的最大最小比例
    MapVector4f shadow_color[MAX_SHADOW_COLOR_SIZE];   //阴影颜色
    MapVector3f shadow_cast_dir;//投射阴影的定向光方向（相对于底图中心点的三维方向向量）
}ShadowSetting;

//////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////
#ifdef __cplusplus
extern "C" {
#endif
static inline MapVector2f MapVector2fMake(float x, float y) {
  MapVector2f v;
  v.x = x;
  v.y = y;
  return v;
}

static inline MapVector2d MapVector2dMake(double x, double y) {
  MapVector2d v;
  v.x = x;
  v.y = y;
  return v;
}

static inline MapVector2i MapVector2iMake(int x, int y) {
  MapVector2i v;
  v.x = x;
  v.y = y;
  return v;
}

static inline MapVector2ub MapVector2ubMake(unsigned char x, unsigned char y) {
    MapVector2ub v;
    v.x = x;
    v.y = y;
    return v;
}

static inline MapVector3f MapVector3fMake(float x, float y, float z) {
  MapVector3f v;
  v.x = x;
  v.y = y;
  v.z = z;
  return v;
}

static inline MapVector3d MapVector3dMake(double x, double y, double z) {
  MapVector3d v;
  v.x = x;
  v.y = y;
  v.z = z;
  return v;
}

static inline MapVector4f MapVector4fMake(float x, float y, float z, float w) {
  MapVector4f v;
  v.x = x;
  v.y = y;
  v.z = z;
  v.w = w;
  return v;
}

static inline MapVector4ub MapVector4ubMake(unsigned char r, unsigned char g, unsigned char b,
                                            unsigned char a) {
  MapVector4ub v;
  v.x = r;
  v.y = g;
  v.z = b;
  v.w = a;
  return v;
}

static inline MapRectF MapRectFMake(float x, float y, float width, float height) {
  MapRectF v;
  v.origin = MapVector2fMake(x, y);
  v.size = MapVector2fMake(width, height);
  return v;
}

static inline MapRectD MapRectDMake(double x, double y, double width, double height) {
  MapRectD v;
  v.origin = MapVector2dMake(x, y);
  v.size = MapVector2dMake(width, height);
  return v;
}

static inline MapEdgeInsets MapEdgeInsetsMake(float top, float left, float bottom, float right) {
  MapEdgeInsets v;
  v.top = top;
  v.left = left;
  v.bottom = bottom;
  v.right = right;
  return v;
}

static inline MapLocation MapLocationMake(double longitude, double latitude) {
  MapLocation v = {longitude, latitude};
  return v;
}

static inline bool MapVector2fEqual(MapVector2f l, MapVector2f r) {
  return l.x == r.x && l.y == r.y;
}

static inline bool MapVector2dEqual(MapVector2d l, MapVector2d r) {
  return l.x == r.x && l.y == r.y;
}

static inline bool MapVector2iEqual(MapVector2i l, MapVector2i r) {
  return l.x == r.x && l.y == r.y;
}

static inline bool MapVector3fEqual(MapVector3f l, MapVector3f r) {
  return l.x == r.x && l.y == r.y && l.z == r.z;
}

static inline bool MapVector3dEqual(MapVector3d l, MapVector3d r) {
  return l.x == r.x && l.y == r.y && l.z == r.z;
}

static inline bool MapVector4fEqual(MapVector4f l, MapVector4f r) {
  return l.x == r.x && l.y == r.y && l.z == r.z && l.w == r.w;
}

static inline bool MapVector4ubEqual(MapVector4ub l, MapVector4ub r) {
  return l.x == r.x && l.y == r.y && l.z == r.z && l.w == r.w;
}

static inline bool MapRectFEqual(MapRectF l, MapRectF r) {
  return MapVector2fEqual(l.origin, r.origin) && MapVector2fEqual(l.size, r.size);
}

static inline bool MapRectDEqual(MapRectD l, MapRectD r) {
  return MapVector2dEqual(l.origin, r.origin) && MapVector2dEqual(l.size, r.size);
}

static inline bool MapEdgeInsetsEqual(MapEdgeInsets l, MapEdgeInsets r) {
  return l.top == r.top && l.left == r.left && l.bottom == r.bottom && l.right == r.right;
}

static inline bool MapLocationEqual(MapLocation l, MapLocation r) {
  return l.longitude == r.longitude && l.latitude == r.latitude;
}

static inline unsigned int MapVector4ub2Int(MapVector4ub color) {
  return (((unsigned int)color.r) | ((unsigned int)color.g << 8) | ((unsigned int)color.b << 16) |
          ((unsigned int)color.a << 24));
}

#define MapColor4fMake MapVector4fMake
#define MapColor4ubMake MapVector4ubMake
#define MapColor4fEqual MapVector4fEqual
#define MapColor4ubEqual MapVector4ubEqual

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// FEATURE
static inline double MapLongitudeForMercatorX(double MercatorX) {
  return MercatorX * (360.0 / 268435456.0) - 180.0;
}

static inline double MapMercatorXForLongitude(double longitude) {
  return (longitude + 180.0) * (268435456.0 / 360.0);
}

static inline double MapLatitudeForMercatorY(double MercatorY) {
  double dblMercatorLat = 180.0 - MercatorY * (360.0 / 268435456.0);
  double value = exp(dblMercatorLat * 0.017453292519943295769236907684886);
  return atan(value) / 0.0087266462599716478846184538424431 - 90.0;
}

static inline double MapMercatorYForLatitude(double latitude) {
  latitude = fmin(fmax(latitude, -85.05), 85.05);
    
  double dblMercatorLat = tan((90.0 + latitude) * 0.0087266462599716478846184538424431);
  dblMercatorLat = log(dblMercatorLat) / 0.017453292519943295769236907684886;
  return (180.0 - dblMercatorLat) * (268435456.0 / 360.0);
}

static inline MapVector2d MapMercatorForLocation(MapLocation location) {
  return MapVector2dMake(MapMercatorXForLongitude(location.longitude),
                         MapMercatorYForLatitude(location.latitude));
}

static inline MapLocation MapLocationForMercator(MapVector2d mercator) {
  return MapLocationMake(MapLongitudeForMercatorX(mercator.x), MapLatitudeForMercatorY(mercator.y));
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// FEATURE
static inline double MapMeterToMercatorRatioByLatitude(double latitude) {
  return (MAP_MERCATOR_WIDTH / MAP_EARTH_CIRCUM) / cos(toRadian_d(latitude));
}

static inline double MapMeterToMercatorRatio(double mercatorY) {
  return MapMeterToMercatorRatioByLatitude(MapLatitudeForMercatorY(mercatorY));
}

static inline double MapMeterToScreenPointRatioByLatitude(double latitude, double scale) {
  return MapMeterToMercatorRatioByLatitude(latitude) * scale;
}

static inline double MapMeterToScreenPointRatio(double mercatorY, double scale) {
  return MapMeterToScreenPointRatioByLatitude(MapLatitudeForMercatorY(mercatorY), scale);
}

// Versions for better performance.
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// FEATURE
static inline float MapLatitudeForMercatorY_f(float mercatorY) {
  float dblMercatorLat = 180.0f - mercatorY * (float)(360.0 / 268435456.0);
  return atanf(expf(dblMercatorLat * 0.017453292f)) / 0.0087266462f - 90.0f;
}

static inline float MapMeterToMercatorRatioByLatitude_f(float latitude) {
  return (float)(MAP_MERCATOR_WIDTH / MAP_EARTH_CIRCUM) / cosf(toRadian_f(latitude));
}

static inline float MapMeterToMercatorRatio_f(float mercatorY) {
  return MapMeterToMercatorRatioByLatitude_f(MapLatitudeForMercatorY_f(mercatorY));
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// FEATURE

#ifdef __cplusplus
}
#endif
//////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////

#endif /* MapDataType_h */
