//
//  tile_overlay_manager.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/2.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_TILE_OVERLAY_MANAGER_H__
#define __MAPAPI_BASE_TILE_OVERLAY_MANAGER_H__

#include <stdio.h>
#include <memory>
#include "base/map_define.h"
#include "base/tile_overlay.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT TileOverlayManager : public MapComponent{
public:
    /**
     * 添加一个新TileOverlay图层
     */
    std::unique_ptr<TileOverlay> AddTileOverlay(const std::shared_ptr<TileOverlayOption> & option);
    
    /**
     * 移除指定TileOverlay图层
     */
    void RemoveTileOverlay(std::unique_ptr<TileOverlay> tileOverlay);
    
    /**
     * 设置所有TileOverlay图层的启用状态
     */
    void SetTileOverlayEnabled(bool enabled);
    
    /**
     * 查询所有Tile Overlay图层的启用状态
     */
    bool GetTileOverlayEnabled();
};

}

#endif /* __MAPAPI_BASE_TILE_OVERLAY_MANAGER_H__ */
