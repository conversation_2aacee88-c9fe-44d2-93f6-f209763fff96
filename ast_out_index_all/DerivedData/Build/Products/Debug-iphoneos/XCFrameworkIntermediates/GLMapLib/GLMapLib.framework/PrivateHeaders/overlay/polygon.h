//
//  polygon.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/2.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_POLYGON_H__
#define __MAPAPI_OVERLAY_POLYGON_H__

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/polygon_options.h"

namespace tencentmap{
class OVLPolygon;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT Polygon : public Overlay{
    friend class OverlayFactory;
public:
    virtual void  SetPoints(const std::vector<MapVector2d> & points);
    virtual const std::vector<MapVector2d> & GetPoints() const;
    
    void  SetHolePoints(const HolePointType & holePoints);
    const HolePointType & GetHolePoints() const;
    
    virtual void SetFillColor(const MapColor4ub & fillColor);
    virtual const MapColor4ub & GetFillColor() const;
    
    void  SetBorderColor(const MapColor4ub & borderColor);
    const MapColor4ub & GetBorderColor() const;
    
    /**
     * 单位 dp
     */
    void  SetLineWidth(float lineWidth);
    float GetLineWidth() const;
    
    /**
     * 单位 m
     */
    void  SetShrinkDist(float dist);
    float GetShrinkDist() const;
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::OVLPolygon> GetImpl();
    
    std::shared_ptr<PolygonOptions> GetOptions() const{
        return std::dynamic_pointer_cast<PolygonOptions>(options_);
    }
};

}

#endif /* __MAPAPI_OVERLAY_POLYGON_H__ */
