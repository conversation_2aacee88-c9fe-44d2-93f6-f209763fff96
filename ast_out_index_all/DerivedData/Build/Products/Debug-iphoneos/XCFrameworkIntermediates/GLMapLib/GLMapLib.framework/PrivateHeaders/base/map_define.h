//
//  map_define.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/4.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_DEFINE_H__
#define __MAPAPI_BASE_MAP_DEFINE_H__

#include <memory>
#include <set>
#include <string>
#include <vector>

#include "base/MapDataType.h"
#include "base/TMBitmapContext.h"

#define MAPAPI_NAMESPACE_BEGIN namespace MAPAPI {
#define MAPAPI_NAMESPACE_END }
#define USING_MAPAPI_NAMESPACE using namespace MAPAPI;

namespace MAPAPI {

enum class TXMAPSDK_EXPORT LogLevel { Verbose = 0, Debug, Info, Warning, Error, Fatal };

class Overlay;

enum HttpCode {
  HTTP_OK = 200,
};

/**
 * http_code 参考 标准http code
 */
struct MapNetResponse {
  std::string buffer;
  int http_code;
};

struct MapTapInfo {
  enum class TXMAPSDK_EXPORT TapType {
    None,                      // 未点中任何元素
    TextAnnotation,            // 点中文字标注
    ClosureAnnotation,         // 已经废弃
    Compass,                   // 点中罗盘
    MarkerItem,                // 点中Maker
    LineOverlayItem,           // 点中Polyline
    Locator,                   // 点击自车标
    BlockRouteAnnotation,      // 点中封路ICON
    IndoorBuilding,            // 点中室内图楼块
    RoadAreaOverlay,           // 点中引导面
    RoadCompanionAreaOverlay,  // 点中伴随面
    AgileMapPoint,             // 点中新百变的点
  };

  /**
   地图点击后返回的信息
   */
  enum class TXMAPSDK_EXPORT AnnotationType {
    BaseMap,           // 基础底图标注
    IndoorMap,         // 室内图标注
    Aoi3D,             // 3daoi
    RoadName,          // 道路名称
    AnnotationTypeNum  // 类型总数
  };

  TapType type;
  AnnotationType annotation_subtype;
  int x;
  int y;

  std::string name;
  long itemId;

  char poiType;              // poiType 数据用来标识公交(1)、地铁(2)、出入口(4)、底商（5）、城市名(101)
  unsigned long long poiId;  // poiid

  std::vector<std::shared_ptr<Overlay>> overlays;

  std::string ext_info;
  std::string agilemap_id;
};

class TXMAPSDK_EXPORT MapComponent {
 public:
  friend class MapImpl;
  MapComponent();
  virtual ~MapComponent() = default;

  class Impl;
  std::unique_ptr<Impl> impl_;
};

enum class TXMAPSDK_EXPORT Model3DType {
  kModel3DS 		  = 0,
  kModelSkeleton  = 1,
  kModelObj 		  = 2,  // 内部obj模型格式,收费站通模等
  kModel3D_StdObj = 3,  // 标准obj模型格式
};

// 描述线上一个任意点的位置
struct BreakPoint {
  int index = 0;       // 向前的第一个点的索引
  double ratio = 0.0;  // 点位于两点之间的比率
};

// 描述路况颜色类型
enum TrafficColorType {
  TrafficColor_Normal = 0,  // 无路况
  TrafficColor_RAPID,       // 畅通
  TrafficColor_SLOW,        // 缓行
  TrafficColor_BLOCK,       // 拥堵
  TrafficColor_WORST,       // 严重拥堵
};

struct ColorSection {
  TrafficColorType traffic_color_;  // 路况颜色
  BreakPoint beg;                   // 起始位置
  BreakPoint end;                   // 终止位置
};

struct CustomLineStyle {
  int classCode;
  MapVector4ub strokeColor;
  MapVector4ub borderColor;
  float strokeWidth;
  float borderWidth;
};

struct RoadScanLightOptions {
  std::string line_texture;         // 扫光线资源名
  float line_width = 10;            // 扫光线宽度，单位dp
  std::string area_texture;         // 扫光面资源名
  float area_width = 500;           // 扫光面宽度，单位dp
  std::string frame_texture;        // 扫光边框资源名
  float frame_width = 4;            // 扫光边框宽度，单位dp
  int duration = 1000;              // 扫光时长，单位ms
  bool is_under_guide_area = true;  // 扫光是否在引导面以下

  RoadScanLightOptions() {};

  RoadScanLightOptions(const MapRoadScanOptions& options);
};

typedef std::pair<MapVector4f, std::shared_ptr<MAPAPI::Overlay>> ScreenOverlay;

class SightInfo;

class RenderCamera {
 public:
  virtual ~RenderCamera() = default;
  virtual MapVector2f GetScreenCoordinate(const MapVector3d& world_point, bool change_data) = 0;
  virtual MapVector2f GetScreenCoordinate(const MapVector2d& world_point, bool change_data);

  virtual MapVector2f GetWorldPoint(const MapVector2f& screen_point) = 0;

  virtual SightInfo GetSightInfo() = 0;

  virtual float GetSkew() = 0;
  virtual double GetScale() = 0;
  virtual float GetRotate() = 0;
  virtual MapVector3d GetCenter() = 0;
};

class OverlayCollisionTask {
 public:
  virtual ~OverlayCollisionTask() = default;
  std::vector<std::shared_ptr<MAPAPI::Overlay>> render_overlay_list_;
  std::vector<std::shared_ptr<MAPAPI::Overlay>> render_3D_overlay_list_;
  std::vector<std::shared_ptr<MAPAPI::Overlay>> render_screen_overlay_list_;
  MapVector4f screen_box;                                // MapView的屏幕范围
  std::vector<ScreenOverlay> overlay_used_screen_rects;  // 已经被其他高优 overlay 占用的屏幕位置
  std::vector<ScreenOverlay> ui_used_screen_rects;       // UI屏幕区域
  MapVector4f locator_screen_rects;                      // 车标屏幕区域
  int global_margin{0};
  float screen_density{0.f};  // 屏幕密度
  bool guide_avoid_poi_enable{false};
  bool guide_avoid_building_enable_{false};

  RenderCamera* camera{nullptr};       // 渲染相机
  int frame_number{0};                 // 帧号
  std::set<int> avoid_revive_ui;       // 已经显示出来的情况下, 避让 UI 仍然要显示的 id
  std::set<int> avoid_revive_route;    // 已经显示出来的情况下, 避让 路线 仍然要显示的 id
  std::set<int> avoid_revive_locator;  // 已经显示出来的情况下, 避让 车标 仍然要显示的 id
  //-------- 以下是计算结果
  std::vector<ScreenOverlay> avoid_annotation_rects;
};

class ICollisionStrategy {
 public:
  virtual bool HandleOverlayCollisionTask(OverlayCollisionTask* task) = 0;
};

// 推荐车道数据类型
enum RecommendedLaneType {
  kRecommendedLaneText = 0,  // 推荐车道文字
  kRecommendedLaneArrow,     // 推荐车道级箭头
  kRecommendedLaneTypeNum
};

// 推荐车道资源数据
struct RecommendedRenderingData {
  std::shared_ptr<TMBitmap> bitmap = nullptr;  // 资源
  float texture_width = -1.0f;
  float texture_height = -1.0f;

  void operator=(const RecommendedRenderingData& other);

  bool IsValid() const;

  void Init();
};

// 推荐车道动画数据
struct RecommendedAnimationData {
  float animationTime = -1.0f;
  int start_index = -1;
  float start_ratio = -1.0f;
  int end_index = -1;
  float end_ratio = -1.0f;

  void operator=(const RecommendedAnimationData& other);

  bool IsValid() const;

  void Init();
};

// 推荐车道边线数据, 协议规定l_points和r_points个数必须相同，否则认为是非法数据
struct RecommendedGeometryData {
  std::vector<MapVector3d> l_points;  // 左边线
  std::vector<MapVector3d> r_points;  // 右边线
  float offset = 0.0f;                // 相对自车偏移
  bool is_show = true;                // 独立控制是否绘制
  void operator=(const RecommendedGeometryData& other);

  bool IsValid() const;

  void Init();
};

// 推荐车道数据
struct RecommendedData {
  RecommendedLaneType type = RecommendedLaneType::kRecommendedLaneTypeNum;  // 类型
  RecommendedGeometryData geometry_data;                                    // 边线几何数据
  RecommendedAnimationData animation_data;                                  // 动画数据
  RecommendedRenderingData rendering_data;                                  // 渲染资源数据

  void operator=(const RecommendedData& other);

  bool IsValid() const;

  void Init();
};

/**
 * 推荐车道数据
 */
struct RecommendedLaneData {
  std::vector<RecommendedData> recommende_data_array;
  void operator=(const RecommendedLaneData& other) { recommende_data_array = other.recommende_data_array; }
};

class ClusterData {
 public:
  /**
   * 源数据的唯一ID, 百变样式回调和点击回调时使用
   */
  int item_id{0};
  /**
   * 源数据的坐标, 坐标系经纬度
   */
  MapVector3d point;
  /**
   * 源数据的名称, 当使用AnnotationMarker方式呈现时需要设置
   */
  std::string name;
  /**
   * 源数据的百变Marker绘制选项, 当使用百变Marker方式呈现时使用, 若首次没有设置则需要处理百变Marker选项回调;
   */
  std::string agile_option;
  /**
   * 数据绘制的优先级, 说明: 当多个元素聚合到一起时使用优先级最高的值;
   */
  int priority;
  /**
   * 是否避让3D, 说明: 当多个元素聚合到一起时若由一个需要避让聚合整体都会避让;
   */
  bool avoid_3d;

 public:
  /**
   * 创建Marker后给的唯一ID, 由GroupID(8)_UId(32)构成;
   */
  int overlayId;

 public:
	bool operator<(const ClusterData& src) const;
};

} // namespace MAPAPI

#endif /* __MAPAPI_BASE_MAP_DEFINE_H__ */
