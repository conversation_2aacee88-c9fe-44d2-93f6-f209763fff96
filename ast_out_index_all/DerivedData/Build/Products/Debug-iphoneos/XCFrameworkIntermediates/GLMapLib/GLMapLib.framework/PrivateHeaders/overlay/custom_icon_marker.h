//
//  custom_icon_marker.h
//  GLMapLib2.0
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/10/25.
//  Copyright © 2021 TencentMap. All rights reserved.
//

#ifndef CUSTOM_ICON_MARKER_H
#define CUSTOM_ICON_MARKER_H

#include "overlay/marker.h"
#include "overlay/custom_icon_marker_options.h"

namespace tencentmap{
class MapMarkerCustomIcon;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT CustomIconMarker : public Marker{
    friend class OverlayFactory;
public:
    /**
     * 修改CustomIconMarker所有属性
     *
     */
    virtual void SetOptions(const OverlayOptions & options) override;
    void SetAboveLocator(bool above_locator);
private:
    using Marker::Marker;
    std::shared_ptr<tencentmap::MapMarkerCustomIcon> GetImpl();
};

}

#endif /* custom_icon_marker_h */
