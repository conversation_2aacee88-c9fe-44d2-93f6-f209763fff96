#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <tuple>
#include <vector>
#include "common_location.h"
#include "base/map_define.h"
#include "overlay.h"
#include "had_structure.h"

namespace MAPAPI {

struct RoadAreaArrowGeometry {
    std::vector<mapbase::GeoCoordinateZ> center_line; // 箭头中心线，由路口转向区域向外至少扩展50m(如果长度足够)，小于15m则不下发
    uint32_t turn_range_start; // 路口转向区域开始点
    uint32_t turn_range_end; // 路口转向区域结束点
};

class TXMAPSDK_EXPORT RoadAreaArrowOptions : public OverlayOptions {
public:
    RoadAreaArrowOptions();

    virtual OverlayType GetType() const override { return OverlayType::RoadAreaArrow; }
    /**
     * 设置白箭头的中心线数据（modify支持设置）
     * @param center_lines 有序的多个白箭头的中心线形点，经纬度坐标
     */
    void SetCenterLineData(const std::vector<RoadAreaArrowGeometry> &center_lines);
    const std::vector<RoadAreaArrowGeometry> &GetCenterLineData() const;

    /**
     * 每一个白箭头的头部是否需要旋转（modify支持设置）
     * @param needs的顺序与center_lines顺序一致，默值{false}
     */
    void SetRotateHead(const std::vector<bool> &needs);
    const std::vector<bool> &GetRotateHead() const;

    /**
    * 每一个白箭头的头部的旋转参数
    * @param max_rotate_angle 最大旋转角度
    * @param start_pitch 起始俯仰角度
    * @param end_pitch 结束俯仰角度
    */
    void SetRotateHeadParams(float max_rotate_angle, float start_pitch, float end_pitch);
    std::tuple<float, float, float> GetRotateHeadParams() const;

    /**
     * 白箭头宽度的缩放比例
     * @param ratio 默认值1.0
     */
    void SetWidthRatio(float ratio);
    float GetWidthRatio() const;
    
    /**
     * 白箭头高/宽比
     * @param ratio 默认值0.2
     */
    void SetHeightToWidthRatio(float ratio);
    float GetHeightToWidthRatio() const;

    /**
     * 白箭头三角底边宽度/宽比
     * @param ratio 默认值1.56
     */
    void SetHeadBottomToWidthRatio(float ratio);
    float GetHeadBottomToWidthRatio() const;

    /**
     * 白箭头颜色值，可设置也可不设置，有默认值
     * @param roof_fill_color 顶面填充颜色
     * @param roof_border_color 顶面描边颜色
     * @param wall_color 侧面描边颜色
     * @param inner_border_color 顶面内描边颜色
     */
    void SetArrowColor(const MapColor4ub &roof_fill_color, const MapColor4ub &roof_border_color, const MapColor4ub &wall_color, const MapColor4ub &inner_border_color);
    std::tuple<MapColor4ub, MapColor4ub, MapColor4ub, MapColor4ub> GetArrowColor() const;

    /**
     * 箭头渐变参数
     */
    void SetGradualParam(float roof_gradual_start, float roof_gradual_end, float other_gradual_start,
            float other_gradual_end);
    std::tuple<float, float, float, float> GetGradualParam() const;

    /**
    * 白箭头渐变动画（modify支持设置）
    * @param base_positions 每个中心线基准点的百分比位置
    * @param animation_time 动画播放时间，单位：秒，设置为0.0时变为最终位置
    */
    void SetGradualBasePoint(const std::vector<float> &base_positions, float animation_time);
    const std::vector<float>& GetGradualBasePoint() const;

    /**
    * 白箭头流光动画开关（modify支持设置）
    * @param flow_light_status 每个白箭头是否开启流光动画
    */
    void SetFlowLightStatus(const std::vector<bool> &flow_light_status);
    const std::vector<bool>& GetFlowLightStatus() const;

    bool GetGeometryChanged() const;

    bool GetRotateHeadChanged() const;

    float GetGradualAnimationTime() const;

    bool GetGradualBasePointChanged() const;

    bool GetFlowLightStatusChanged() const;

    std::string Description() const override;

    std::shared_ptr<OverlayOptions> CopyModifyData() override;

    void ResetChangeSign();

 private:
    class Impl;
    std::shared_ptr<Impl> GetImpl();
    std::shared_ptr<Impl> GetImpl() const;
};

}
#endif
