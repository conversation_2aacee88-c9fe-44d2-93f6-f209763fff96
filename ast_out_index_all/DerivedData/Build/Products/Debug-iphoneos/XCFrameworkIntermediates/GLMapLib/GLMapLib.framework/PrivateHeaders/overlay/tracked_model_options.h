#include <array>
#include <tuple>
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "common_location.h"
#include "overlay.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT TrackedModelOptions : public OverlayOptions {
 public:
  TrackedModelOptions();
  TrackedModelOptions(const TrackedModelOptions &options);
  virtual OverlayType GetType() const override { return OverlayType::TrackedModel; }

  /**
   * 设置模型mesh、纹理、json、箭头文件路径
   */
  void SetModelFilePath(const std::string &model_mesh, const std::string &model_texture,
                        const std::string &model_json, const std::string &arrow_texture);
  std::array<std::string, 4> GetModelFilePath() const;

  /**
   * 设置模型宽度，单位：米
   */
  void SetModelWidth(float width);
  float GetModelWidth() const;

  /**
   * @param step_count 箭头动画帧数
   * @param duration 箭头动画时长，单位：秒
   */
  void SetModelArrowStepCountAndDuration(uint8_t step_count, float duration);
  std::pair<uint8_t, float> GetModelArrowStepCountAndDuration() const;

  /**
   * 设置模型的的轨迹线数据（modify支持设置）
   * @param track_line 轨迹线，经纬度坐标
   */
  void SetModelTrackLine(const std::vector<mapbase::GeoCoordinateZ> &track_line);
  const std::vector<mapbase::GeoCoordinateZ> &GetModelTrackLine() const;

  /**
   * 设置模型位置（modify支持设置）
   * @param index/ratio, 为模型位置在轨迹线中的索引，如（5，0.0002）；
   * @param animation_time 动画播放时间，单位：秒，设置为0.0时直接变为最终位置
   */
  void SetModelPos(uint32_t index, float ratio, float animation_time);
  std::tuple<uint32_t, float, float> GetModelPos() const;

  /**
   * 设置模型颜色（modify支持设置）
   * @param (model_color)模型、(arrow_color)箭头的颜色
   * @param animation_time 动画播放时间，单位：秒，设置为0.0时直接变为最终位置
   */
  void SetColor(const MapColor4ub &model_color, const MapColor4ub &arrow_color,
                float animation_time);
  std::tuple<MapColor4ub, MapColor4ub, float> GetColor() const;

  bool IsTrackChanged() const;
  bool IsModelPosChanged() const;
  bool IsColorChanged() const;

  virtual std::shared_ptr<OverlayOptions> CopyModifyData() override;

  void ResetChangeSign();

  std::string Description() const override;

 private:
  class Impl;
  std::shared_ptr<Impl> GetImpl();
  std::shared_ptr<Impl> GetImpl() const;
};

}
#endif
