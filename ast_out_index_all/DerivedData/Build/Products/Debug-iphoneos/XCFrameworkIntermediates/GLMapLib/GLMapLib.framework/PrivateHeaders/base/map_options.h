//
//  map_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/11.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_MAP_OPTIONS_H__
#define __MAPAPI_BASE_MAP_OPTIONS_H__

#include <stdio.h>
#include <string>
#include "base/map_define.h"

namespace MAPAPI {

enum class TXMAPSDK_EXPORT MapCoordinateType{
    LonLat = 1,   //经纬度坐标
    MapPixel      //底图像素坐标
};

/**
 * 创建地图需要的相关配置
 */
class TXMAPSDK_EXPORT MapOptions{
public:
    MapOptions();
    virtual ~MapOptions();
    
    /**
     * 设置坐标类型
     */
    MapOptions & SetCoordinateType(enum MapCoordinateType coorType);
    enum MapCoordinateType GetCoordinateType() const;
    
    /**
     * 像素密度
     */
    MapOptions & SetDensity(float density);
    float GetDensity() const;
    
    /**
     * tile 大小
     */
    MapOptions & SetTileSize(int size);
    int GetTileSize() const;
    
    /**
     * 文字比例
     */
    MapOptions & SetTextScale(float textScale);
    float GetTextScale() const;
    
    /**
     * 数据缓存目录
     */
    MapOptions & SetDataDir(const std::string & dir);
    const std::string GetDataDir() const;
    
    /**
     * 配置文件存放目录
     */
    MapOptions & SetConfigDir(const std::string & dir);
    const std::string GetConfigDir() const;
    
    /**
     * 字体文件存放目录
     */
    MapOptions& SetFontFileDir(const std::string& dir);
    const std::string GetFontFileDir() const;

    /**
     * 卫星图数据目录
     */
    MapOptions & SetSatelliteDir(const std::string & dir);
    const std::string GetSatelliteDir() const;
    
    /**
     * 离线数据目录
     */
    MapOptions & SetOfflineDataDir(const std::string & dir);
    const std::string GetOfflineDataDir() const;
    
    /**
     * 是否开启高质量显示效果，对于性能差的设备，可以关闭一些效果，以达到性能平衡
     */
    MapOptions & SetHighQuality(bool highQuality);
    bool GetHighQuality() const;
  
  
    /**
     *设置设备名称(iphone, android, car)
     */
    MapOptions& SetDeviceName(const std::string& device_name);
    std::string GetDeviceName() const;
    
    /**
     * 设置设备id
     */
    MapOptions& SetDeviceId(const std::string device_id);
    std::string GetDeviceId() const;
    
    /**
     * 设置app版本号
     */
    MapOptions& SetAppVersion(const std::string& app_version);
    std::string GetAppVersion() const;
    
    /**
     * 设置引擎版本号
     */
    MapOptions& SetEngineVersion(const std::string& engine_version);
    std::string GetEngineVersion() const;
    
    /*
    * 设置地图加载模式
    */
    MapOptions& SetMapTileLoadMode(int mapTileLoadMode);
    /*
    *获取地图加载模式
    */
    int GetMapTileLoadMode() const;

    /**
    * 世界图开关参数
    * @param isCountry 是否设定全国级别(全国级别指数据级别3/5/7, 更大为城市级别) true:全国级别,false:城市级别
    * @param enable    是否开启, true:开启状态, false:关闭状态
    * @return true:开启状态, false:关闭状态
     */
    MapOptions& SetSupportWorldMapEnable(bool isCountry, bool enable);
    /**
    * 返回是否支持世界图开关状态
    * @param isCountry 是否是全国级别, true:全国级别, false:城市界别
    * @return true:开启状态, false:关闭状态
     */
    bool IsSupportWorldMap(bool isCountry);

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

}

#endif /* __MAPAPI_BASE_MAP_OPTIONS_H__ */
