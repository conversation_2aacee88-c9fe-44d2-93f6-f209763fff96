//
// Created by cuipanpan on 2021/11/12.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include "../base/map_define.h"
#include "overlay.h"
#include "region_lane_options.h"

namespace tencentmap{
class Macro4KRegionLane;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT RegionLane : public Overlay{
    friend class OverlayFactory;
public:
    void SetOptions(const RegionLaneOptions & options);
    
protected:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KRegionLane> GetImpl();
};

}
#endif