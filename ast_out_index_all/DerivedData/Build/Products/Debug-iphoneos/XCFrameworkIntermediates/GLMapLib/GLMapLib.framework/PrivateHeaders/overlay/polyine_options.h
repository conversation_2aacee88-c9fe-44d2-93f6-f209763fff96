//
//  polyine_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/9.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_POLYLINE_OPTIONS_H__
#define __MAPAPI_OVERLAY_POLYLINE_OPTIONS_H__

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {

enum class TXMAPSDK_EXPORT PolylineFillType{
    Solid = 1,  //实线
    Dash  = 2,  //虚线
    Dot   = 3,  //小圆点重复
};

enum class TXMAPSDK_EXPORT PolylineJoinType{
    Round = 1,  //圆角连接
    Bevel = 2,  //尖角连接
    Miter = 3,  //斜接连接
};

enum class TXMAPSDK_EXPORT PolylineCapType{
    Round  = 1, //圆形cap
    Square = 2, //方形cap
    Butt   = 3, //无cap
};

enum class TXMAPSDK_EXPORT PolylineDrawType {
    Normal,   //保持原样
    PassGrey,  //走过置灰
    PassClear,  //走过清空
    Grown     //生长，与PassClear相反
};

class TXMAPSDK_EXPORT PolylineOptions : public OverlayOptions{
public:
    PolylineOptions();
    PolylineOptions(const PolylineOptions & other);
    ~PolylineOptions();

    PolylineOptions & SetPoints(const std::vector<MapVector2d> & points);
    const std::vector<MapVector2d> & GetPoints() const;
    
    /*
     * 单位 dp
     */
    PolylineOptions & SetWidth(float width);
    float             GetWidth() const;
    
    PolylineOptions & SetColor(MapVector4ub color);
    MapVector4ub      GetColor() const;
    
    /*
     * 单位 dp
     */
    PolylineOptions & SetBorderWidth(float borderWidth);
    float             GetBorderWidth() const;
    
    PolylineOptions & SetBorderColor(MapVector4ub borderColor);
    MapVector4ub      GetBorderColor() const;
    
    PolylineOptions & SetFillType(PolylineFillType fillType);
    PolylineFillType  GetFillType() const;
    
    PolylineOptions & SetJoinType(PolylineJoinType joinType);
    PolylineJoinType  GetJoinType() const;
    
    PolylineOptions & SetStartCapType(PolylineCapType startCapType);
    PolylineCapType   GetStartCapType() const;
    
    PolylineOptions & SetEndCapType(PolylineCapType endCapType);
    PolylineCapType   GetEndCapType() const;
    
    const std::vector<int> & GetDashPattern() const;
    PolylineOptions & SetDashPattern(const std::vector<int> & pattern);
    
    const std::string & GetTexture() const;
    PolylineOptions & SetTexture(const std::string & texture);

    const PolylineDrawType & GetDrawType() const;
    PolylineOptions & SetDrawType(PolylineDrawType type);

    virtual OverlayType GetType() const override { return OverlayType::Polyline; }
    
protected:
    struct Impl;
    
private:
    std::shared_ptr<PolylineOptions::Impl> GetImpl();
    std::shared_ptr<PolylineOptions::Impl> GetImpl() const;
};

}

#endif /* __MAPAPI_OVERLAY_POLYLINE_OPTIONS_H__ */
