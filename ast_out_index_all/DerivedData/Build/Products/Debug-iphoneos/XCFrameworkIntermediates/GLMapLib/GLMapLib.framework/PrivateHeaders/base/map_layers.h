#ifndef __MAPAPI_BASE_MAP_LAYERS_H__
#define __MAPAPI_BASE_MAP_LAYERS_H__

#include <string>
#include <vector>
#include <map>
#include "base/map_define.h"
#include "agilemap/agilemap_controller.h"
//#include "base/model_3d_options.h"

namespace MAPAPI {

class TXMAPSDK_EXPORT MapLayer : public MapComponent{
public:
    virtual void SetEnable(bool enable);
    
    virtual void SetPriority(int p);
    
    virtual void SetLevelRange(int minLevel, int maxLevel);
    
    virtual void ClearCache();
    
    virtual void ReloadData();
    
    virtual void ReloadConfig();
};

class TXMAPSDK_EXPORT TrafficLayer : public MapLayer{
public:
    virtual void ReloadData() {}
    
    virtual void Clear() {}
};

class TXMAPSDK_EXPORT SatelliteLayer : public MapLayer{
public:
};

class TXMAPSDK_EXPORT OutdoorBuildingLayer : public MapLayer{
public:
    // 函数名需更正为 MapBuildingSet3DEnabled；因为在俯视正交投影时，3DEnabled为true时3DEffect仍为false。
    /**
     设置楼快是否允许3D效果。设置了也不一定表示当前就是显示3D效果，内部还受到比例尺的控制。
     
     @param pWorld 场景句柄
     @param bEnabled false->2D, true->3D;
     */
    //TODO:DD
//    void MapBuildingSetEffect3D(void *pWorld, bool bEnabled);
    
    /**
     获取楼快是否允许3D效果。
     
     @param pWorld 场景句柄
     @return false->2D, true->3D;
     */
    //TODO:DD
//    bool MapBuildingIsEffect3D(void *pWorld);
    // 函数名需更正为 MapBuildingIs3DEnabled；原因同上。
    
    //TODO:set enabled与set visible不同
    
    /**
     * 查询当前是否显示3D楼块效果
     */
    bool IsShowing3D();
    
    /**
    设置引导是否避让楼块
    @param enable 业务控制引导面是否避让楼块
    */
    void SetGuideAvoidBuildingEnable(bool enable);
};

//vinsentli 目前IndoorBuildingLayer外部先不要使用，相关接口内部还在斟酌修改中。。。
class TXMAPSDK_EXPORT IndoorBuildingLayer : public MapLayer{
public:
    
    /**
     * 获取指定室内图的显示起始级别
     * 若guid对应的室内图数据存在，则可返回有效的起始级别, 否则返回一个默认级别.
     *
     * @param guid     室内图guid
     * @param showFromLevel     显示起始级别
     * @return true 有数据，有效起始级别，false 缺少数据，用默认级别
     */
    bool GetShowFromScaleLevel(const unsigned long long* guid, int* showFromLevel);
    
    /**
     * 设置室内图显示隐藏
     *
     * @param hidden 隐藏状态
     */
    void SetHidden(bool hidden);
    
    /**
     * 设置室内图激活（当前显示）的楼层
     *
     * @param floorID 楼层ID
     * @return 是否设置成功
     */
    bool SetActiveFloorID(int floorID);
    
    /**
     * 获取激活的室内图（当前高亮）ID
     *
     * @param guid 室内图ID，通过参数返回
     * @return 是否成功获取
     */
    bool GetActiveBuildingGUID(unsigned long long *guid);
    
    /**
     * 获取室内图楼层总数
     *
     * @param floorNum 楼层总数，通过参数返回
     * @return 是否成功获取
     */
    bool GetActiveFloorNum(int *floorNum);
    
    /**
     * 获取室内图所有的楼层名称
     *
     * @param floorNum 楼层总数
     * @param floorNames 楼层名称，通过参数返回
     * @return 是否成功获取
     */
    bool GetActiveFloorNames(int floorNum, std::string & floorNames);
    
    /**
     * 获取室内图激活（当前显示）的楼层
     *
     * @param floorID 楼层ID，通过参数返回
     * @return 是否成功获取
     */
    bool GetActiveCurrentFloor(int *floorID);
    
    /**
     * 获取室内图当前激活的楼层名称
     *
     * @param name 楼层名称，通过参数返回
     * @param size 名称字符数组长度
     * @param coordinate 楼层坐标中心，通过参数返回
     * @return 是否成功获取
     */
    bool GetActiveFloorName(unsigned short *name, int size, MapVector2d *coordinate);
    
    /**
     * 获取当前激活的室内图闭包矩形
     *
     * @param bounds 闭包矩形，通过参数返回
     * @return 是否成功获取
     */
    bool GetActiveBounds(MapRectD *bounds);
    
    /**
     * 设置室内图屏幕激活区域，在该区域内的室内图可能被激活高亮显示
     *
     * @param x 屏幕区域左上角x (0.0f ~ 1.0f)
     * @param y 屏幕区域左上角y (0.0f ~ 1.0f)
     * @param width 屏幕区域宽度 (0.0f ~ 1.0f)
     * @param height 屏幕区域高度 (0.0f ~ 1.0f)
     */
    void SetActiveScreenArea(float x, float y, float width, float height);
    
    /**
     * 室内图高亮时，高亮室内图以外的地方覆盖了蒙版，设置该蒙版的颜色值
     *
     * @param color 颜色值
     */
    void SetGrayMaskColor(MapVector4ub color);
    
    /**
     * 设置室内图 楼层小面是否可选中
     *
     * @param enabled  可选状态
     */
    void SetPickEnabled(bool enabled);
    
    /**
     * 设置在点击室内图标注时，是否返回室内图基础信息；
     *
     * @param enabled    true自动返回，false不返回；
     */
    void SetTextAttachBuildingInfoOnTapEnable(bool enabled);
    
    /**
     * 设置指定室内图的指定楼层的名称
     *
     * @param guid      指定室内图的唯一ID
     * @param floorName 楼层名称，如"F1"
     */
    void SetSelectedIndoorBuildingGuidAndFloorName(const char *guid, const char *floorName);
    
    /**
     废弃：可以用MapIndoorBuildingSetShowIndoorBuildingControlRule接口代替
     设置室内图显示的白名单
     若白名单不为空时，按照白名单显示室内图；若白名单为空，所有室内图均可显示.
     @param pWorld 场景句柄
     @param guidlist 室内图guid白名单
     @param size     室内图白名单大小(个数)
     */
//    void MapIndoorBuildingSetShowIndoorBuildingWhiteList(void *pWorld, const char **guidlist, int size);
    
    /**
     * 设置室内图显示黑白名单控制规则
     * 说明：1.目前引擎同一时刻仅支持一种模式，非黑即白；不设置内部为黑名单方式，黑名单类型为空，即按照配置显示；
     *      2.该接口可以重复调用, 新规则会覆盖之前设置的规则
     * @param pRule  室内图显示控制规则
     */
    void SetShowIndoorBuildingControlRule(IndoorShowControlRule* pRule);

    /**
     * 设置室外蒙层是否生效
     * @param enable enable设置为true则生效；设置为false则不生效。
     * @param guid 室外蒙层生效的建筑物的guid
     * @param floor_name 生效的建筑物的楼层名称
     */
    void SetEnableIndoorMask(bool enable, const char* guid, const char* floor_name);

    /**
     * 设置室外蒙层的颜色
     * @param color 室外蒙层的颜色值。
     */
    void SetIndoorMaskColor(MapVector4ub color);
};

class TXMAPSDK_EXPORT BlockRouteLayer : public MapLayer{
public:
    /**
     * 显示或者隐藏封路信息（封路路线+封路ICON）
     */
    void HideBlockRoute(bool hide);
    
    /**
     * 是否显示封路ICON
     */
    void ShowBlockIcon(bool needShow);
    
    /**
     * 获取创建封路的优先级
     */
    int GetBlockRouteIconPriority();
    
    /**
     * 设置创建封路的优先级
     */
    void SetBlockRouteIconPriority(int priority);
    
    
    
    /**
     * 获取封路ID信息
     *
     * 使用场景：封路ID用于向服务器请求封路详情时使用
     *
     * @param itemId  底图上封路ICON的markerID，通过手势点击获取到。
     * @param routeid 服务端对应的封路路段ID
     * @param iconid  服务端对应的封路事件ID
     */
    void GetBlockRouteInfo(int itemId, unsigned int *routeid, unsigned int *iconid);
};

class TXMAPSDK_EXPORT StreetViewLayer : public MapLayer{
public:
    /**
     * 街景路网是否显示
     */
    bool IsStreetViewRoadShow();
    
    /**
     * 检查特定城市是否存在街景路网
     *
     * @param cityName 城市名称
     */
    bool IsCityHasStreetViewRoad(const std::string & cityName);
    
};

struct DynamicPOI{
    MapVector2d point;

    int priority;
    int class_code;
    int from_scale;
    int dynamic_flag;

    std::string     name;
    unsigned int    first_line_name_count;
};

class TXMAPSDK_EXPORT DynamicPOILayer : public MapLayer{
public:
    /**
     * Function: In walk or cycling mode, add some import pois data along route to display on base map
     */
    bool WritePOI(unsigned short index, const std::vector<DynamicPOI> & pois);
    
    /**
     * Function: When there are more than zero route on map, to use this interface switch active pois along the route.
     */
    bool ShowPOI(unsigned short index, bool bShow);
    
    /**
     * Function: When User give up walk or cycling map, clear dynamic poi data;
     *
     * index == -1, clear all pois;
     * index == 0, 1, 2 ... , clear some bat poi;
     */
    bool ClearPOI(int index);
    
};

struct HandDrawConfigItem{
    std::string scene_id;
    std::string scene_name;
    std::string city_name;
    int         city_code;
    int         version;
    MapRectD    rect;
    int         display_level;
};

class TXMAPSDK_EXPORT HandDrawLayer : public MapLayer{
public:
    /**
     * 手绘图：读取配置
     */
    bool ReadConfigureFile(const std::string & cfgFile);
    
    /**
     * 手绘图：查询
     */
    bool Query(MapRectD rect, int level, std::vector<HandDrawConfigItem> & items);
};

/**
 * 聚类数据操作类
 */
class TXMAPSDK_EXPORT ClusterGroupLayer : public MapLayer {
 public:
	/**
	 * 创建一个聚类实例, 使用百变Marker样式呈现
	 * @param dataList          聚类原始数据
	 * @param agileStyle        百变Marker整体风格
	 * @param clusterStyle      聚合时的显示样式, 通过百变模块获取, 说明:
	 * 				1> 若为空则需要设置并处理回调setClusterGroupAgileMarkerOptionCallback
	 * 				2> 若所有的聚合元素使用统一的样式, 这里需要设置有效样式, 聚合的数量文本按照maxShowNum, 采用固定字符串占位,引擎内部动态替换;
	 * @param radiusDp           以Dp为单位的聚合范围，方形范围内的点会聚合显示
	 * @param maxShowNum         聚合的最大数量
	 * @param minMaxDisplayLevel 大于x，小于y时聚合点显示，其他范围时隐藏，必须：x<y
	 * @param minMaxClusterLevel 聚合发生的级别范围，低于x时始终显示聚合形态但不重新聚合，高于y时显示单独的点，[x,y]以内正常聚合显示，必须：x<y
	 * @return 生成的新聚类实例的ID
	 */
	int CreateClusterGroup(const std::vector<ClusterData>& dataList,
						   std::string agileStyle, const MAPAPI::CanvasLayoutParam& clusterStyle,
						   float radiusDp, int maxShowNum,
						   MapVector2i minMaxDisplayLevel, MapVector2i minMaxClusterLevel);

 	/**
 	 * 更新聚合数据的百变Marker绘制选项
 	 * @param group_id          聚类组ID
 	 * @param agileStyle        百变Marker整体风格, 如"day", "night"
 	 * @param itemId2OptionMap  更新的聚类百变Marker绘制选项
   	 *   Key: 回调元素中对应的Key
     *   Value: 对应元素的百变Marker绘制参数, 百变SDK内部通过native层工具类(AgileMapUtils)转换为字符串,
     *          业务端通过百变SDK的AgileMarkerOption的toString方法获取
 	 * @param  callbackId    回调ID 说明使用回调返回的ID直接传入, 用于性能统计;
 	 */
	void SetClusterGroupAgileMarkerOption(const int groupId, const std::string& agileStyle,
										  const std::map<std::string, MAPAPI::CanvasLayoutParam>& itemId2OptionMap,
										  const int callbackId);

	/**
	 * 设置聚类百变Marker的整体风格样式， 样式变化后会重新回调请求百变Marker绘制选项;
	 * @param groupId       聚类组ID
	 * @param agileStyle    风格样式名称, 如"day", "night"
	 */
	void SetClusterGroupAgileStyle(const int groupId, const std::string& agileStyle);
	/**
	 * 删除指定的聚合图层数据
	 * @param group_id 聚类组ID
	 * @return true:删除成功, false:删除失败
	 */
  	bool RemoveGroup(int group_id);
	/**
	 * 设置指定聚类组的可见性
	 * @param group_id 聚类组ID
	 * @param visible  是否显示, true显示,false隐藏
	 */
  	void SetClusterVisible(int group_id, bool visible);
	/**
	 * 添加一个原始数据到指定的聚类组
	 * @param group_id 聚类组ID
	 * @param dataItem 添加的原始数据
	 */
  	void AddItemToGroup(int group_id, const std::shared_ptr<MAPAPI::ClusterData> &dataItem);
  	/**
  	 * 删除一个原始数据从指定的聚类组
  	 * @param group_id 聚类组ID
  	 * @param item_id  删除的元素ID
  	 */
  	void RemoveItemFromGroup(int group_id, int item_id);
};

}

#endif
