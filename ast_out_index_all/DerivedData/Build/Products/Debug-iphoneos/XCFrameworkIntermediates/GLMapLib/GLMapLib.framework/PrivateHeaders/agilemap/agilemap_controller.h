//
//  agilemap_controller.h
//  GLMapLib2.0
//
//  Created by ji<PERSON><PERSON><PERSON> on 2024/6/26.
//


#ifndef __MAPAPI_MAP_CANVASLAYER_INCLUDE__
#define __MAPAPI_MAP_CANVASLAYER_INCLUDE__

#include <stdio.h>
#include <string>
#include <map>
#include "base/map_define.h"

namespace MAPAPI {
typedef int32_t AgileAnimationID;

/**
 * 百变marker的Icon类型
 */
enum AgileIconType :int{
    AgileIconType_Normal = 0, //普通
    AgileIconType_Standplate_BG  = 1, //立牌背景
    AgileIconType_Standplate_Line  = 2 //立牌拉线
};
 
struct TXMAPSDK_EXPORT CanvasBaseOption{
    uint32_t byte_num = 0; //外部无需设置
    MapRectF show_rect; //文字显示的区域，单位dp
    int priority = 0; //一个canvas内部的绘制优先级
    std::string pick_name; //点选时返回的名称
    AgileAnimationID anim_id = 0; //动画控制使用的id, 一个marker的所有布局中唯一
    
    MapVector2f shadow_offset = {0, 0}; //阴影偏移，单位 dp
    MapVector4ub shadow_color = {0,0,0,0}; //阴影颜色
    float shadow_border = 0; //阴影模糊宽度

    bool FromBuffer(const char*& buffer, size_t& buffer_size);
    std::vector<char> ToBuffer() const;
    bool IsValid() const;
};

struct TXMAPSDK_EXPORT CanvasTextOption :public CanvasBaseOption{
    std::string content; //utf8编码的文字内容
    int fontsize = 0; //单位dp
    float border_width = 0; //描边宽度，单位dp
    MapVector4ub border_color = {0,0,0,0}; //描边颜色，使用MapVector4ubMake构造
    MapVector4ub fill_color = {0,0,0,0}; //填充颜色，使用MapVector4ubMake构造
    bool is_bold = false; //是否粗体
    bool FromBuffer(const char*& buffer, size_t& buffer_size);
    std::vector<char> ToBuffer() const;
    bool IsValid() const;
};

struct TXMAPSDK_EXPORT CanvasIconOption :public CanvasBaseOption{
    std::string icon_file_path; //icon的文件全路径
    MapVector4f corner_radius = {0.0f, 0.0f, 0.0f, 0.0f}; //不对称圆角的半径，单位 dp，左上，右上，右下，左下
    AgileIconType icon_type = AgileIconType_Normal;
    
    bool FromBuffer(const char*& buffer, size_t& buffer_size);
    std::vector<char> ToBuffer() const;
    bool IsValid() const;
};

 
struct TXMAPSDK_EXPORT CanvasRectOption :public CanvasBaseOption{
    float border_width = 0.f; //描边宽度，单位dp
    MapVector4ub border_color = {0,0,0,0}; //描边颜色，使用MapVector4ubMake构造
    MapVector4ub fill_color = {0,0,0,0}; //描边颜色，使用MapVector4ubMake构造
    MapVector4f corner_radius = {0.0f, 0.0f, 0.0f, 0.0f}; //不对称圆角的半径，单位 dp，左上，右上，右下，左下

    bool FromBuffer(const char*& buffer, size_t& buffer_size);
    std::vector<char> ToBuffer() const;
    bool IsValid() const;
};

struct TXMAPSDK_EXPORT CanvasDotNineOption :public CanvasBaseOption{
    MapVector2f stretch_size = {0.0f, 0.0f}; // 点九图拉伸后宽高，单位 dp
    MapVector4f stretch_line = {0.0f, 0.0f, 0.0f, 0.0f}; // 点九图控制拉伸线，单位 dp 左（竖），右（竖），上（横），下（横）
    std::string icon_file_path; // 点九图片的文件全路径
    
    bool FromBuffer(const char*& buffer, size_t & buffer_size);
    std::vector<char> ToBuffer() const;
    bool IsValid() const;
};

/**
* 百变地图的canvas参数（一种布局）
*/
struct TXMAPSDK_EXPORT CanvasOption{
    std::vector<MapVector2i> level_range; //显示级别
    MapVector2i size = {0, 0};//canvas大小，单位dp
    MapVector2f anchor = {0.f, 0.f}; //锚点
    std::vector<CanvasTextOption> text_options;
    std::vector<CanvasIconOption> icon_options;
    std::vector<CanvasRectOption> rect_options;
    std::vector<CanvasDotNineOption> dot_nine_options;
};

/**
 * 百变marker的布局避让
 */
enum AgileAvoidType {
    AgileAvoidType_Normal = 0, //没有特殊布局（正常避让，按顺序避让，为了显示稳定性，有显示空间时也不会还原为其他布局）
    AgileAvoidType_Disapear  = 1, //最后一个是特殊布局，在空间允许时可以还原为前边的布局，避让失败后消失
    AgileAvoidType_Overlap  = 2 //最后一个是特殊布局，在空间允许时可以还原为前边的布局，避让失败后压盖显示
};

/**
 * 百变地图的canvas参数（多种布局，带避让参数）
 * canvas_options 布局描述列表
 * avoid_type   最后一个布局的避让方式，见AgileAvoidTypeEnum说明
*/
struct TXMAPSDK_EXPORT CanvasLayoutParam {
    std::vector<CanvasOption> canvas_options;
    AgileAvoidType avoid_type = AgileAvoidType_Normal;

    /**
     * 使用AgileMapUtils转换为Base64字符串, 目的降低JNI层的复杂结构转换
     * @return 选项转换为字符串形式
     */
    std::string ToString() const;
    /**
     * 使用AgileMapUtils转换为Base64字符串, 目的降低JNI层的复杂结构转换
     * @return
     */
    std::vector<char> ToBuffer() const;

    /**
     * 使用AgileMapUtils将序列化Base64字符串转换为结构对象
     * @param buffer 序列化Buffer
     */
    void FromString(const std::string& buffer);
};

class TXMAPSDK_EXPORT ICanvasTyper{
public:
/**
 * static_info   静态信息
 * dynamic_info  动态信息
 * attach_info  附属信息，百变地图中为图层的id， 返回多个布局，引擎按照从前到后的顺序选择。目前仅支持一个。
 * return 一个marker的布局信息
*/
virtual CanvasLayoutParam GetCanvasContent(const std::string &static_info, const std::string &dynamic_info, const std::string& attach_info, uint64_t poiid) = 0;
};

/**
 * 百变渲染数据过滤
 *  layer_id 百变图层id
 *  filter_json 过滤器json字符串, 示例 "{\"timeline\":1717068000}"
 *  - 2024/10/24: Requirements change, "{\"timeline\":1717068000, \"enableBlur\":true}"
 */
struct TXMAPSDK_EXPORT AgileMapLayerFilter {
  std::string layer_id;
  std::string filter_json;
};
 
class TXMAPSDK_EXPORT IAgileAnimObserver{
public:
    virtual void OnAnimStop(const std::string& marker_id) = 0;
};

enum class TXMAPSDK_EXPORT CanvasAnimType : uint8_t{
    Move = 0,
    Rotate = 1,
    Scale = 2,
    Transparency = 3
};

struct TXMAPSDK_EXPORT CanvasAnimKeyFrame{
    //keyframe在整个动画total_time中的比例
    float ratio = 0;
    // 动画变换的数值，长度要和transform_type相同
    std::vector<MapVector2f> transform_value;
};
 
struct TXMAPSDK_EXPORT CanvasAnimationAxis{
    uint32_t anim_loopcount = 1; //循环执行的次数，0为一直循环
    float anim_delay = 0; //延时执行的时间，单位s
    float total_time = 0;  //动画总时间，单位s
    // 动画变换的数值
    std::vector<CanvasAnimKeyFrame> keyframe;
    
    // 动画变换的类型，含义：0平移  1旋转  2缩放  3透明度，旋转和透明度只有一个float生效
    std::vector<CanvasAnimType> transform_type;
    
    // 动画变换的锚点,一个轴一个锚点
    MapVector2f transform_anchor;
    bool CheckValid() const;
    std::vector<char> ToBinary()const;
    void FromBinary(const char*& buffer, size_t& buffer_count);
};
 
struct TXMAPSDK_EXPORT CanvasAnimParam{
    //每个节点所使用的动画参数
    std::map<AgileAnimationID, std::vector<CanvasAnimationAxis>> anim_axis;
    //动画结束和开始的回调
    std::shared_ptr<IAgileAnimObserver> observer;
    
    std::string ToString() const;
    void FromString(const std::string& base64);
};

class TXMAPSDK_EXPORT AgileMapLayerListener {
public:
    enum class CreateResult {
        SUCCESS = 0,
        FAIL = 1
    };
    AgileMapLayerListener() = default;
    virtual ~AgileMapLayerListener() = default;
    
    /**
     * 通知百变图层创建结果
     */
    virtual void OnCreateResult(CreateResult result, const std::string& reason) = 0;
};

class TXMAPSDK_EXPORT AgileMapController :public MapComponent{
public:
    /**
     * 添加图层
     * @param layer_id   图层唯一id
     * @param data_config_content 数据配置json内容
     * @param style_config_content  样式配置文件json内容
     * @param listener 图层创建成功或失败 的回调通知
     * @param meta_version 天气图层meta版本
     * @param opts_json 过滤器
    */
    void AddLayer(const std::string &layer_id, const std::string &data_config_content, const std::string &style_config_content, std::shared_ptr<AgileMapLayerListener> listener = nullptr, const std::string& meta_version = "", const std::string& opts_json = "");
    /**
     * 删除图层
     */
    void RemoveLayer(const std::string &layer_id);

    /**
     * 切换皮肤样式时刷新图层
     * @param style_config_content 样式文件内容
     */
    void RefreshLayerStyle(const std::string &layer_id, const std::string &style_config_content);
    
    /**
     * 设置过滤器
     */
    void FilterLayers(const std::vector<AgileMapLayerFilter>& filters);

    /**
     * 设置模糊半径，单位：像素
     */
    void Blur(const std::string& layer_id, const float& radius);
    
     /**
      * 计算文字大小
      * content 文字内容，utf8编码
      * font_size 文字字体大小
      * border_width 描边的宽度
      * bold  是否粗体
      * return  排版纹理的宽高
      */
    MapVector2f CalcTextSize(const std::string &content, int font_size, float border_width, bool bold);
    
    /**
     * 刷新特定的poi，重新通过callback获取布局
     * layerid 图层的id
     * poiid  要刷新的poi的id
     */
    void RefreshPoi(const std::string& layerid, uint64_t poiid);
    
    /**
     * 设置图层可见性
     * layerid 图层的id
     * visible  图层是否可见
     */
    void SetLayerVisible(const std::string& layerid, bool visible);
    
    /**
     * 刷新图层，当前屏幕的瓦块缓存无效，触发重新请求
     * layerid 图层的id
     */
    void RefreshTiles(const std::string& layerid);
    
    /**
     * 设置图层中的点是否参与3D碰撞
     * layerid 图层的id
     * enable 是否参与3D碰撞
     */
    void SetCollision3DEnable(const std::string& layerid, bool enable);
};
}

#endif /* __MAPAPI_MAP_CANVASLAYER_INCLUDE__ */
