#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "overlay.h"
#include "guide_arrow_options.h"

namespace tencentmap{
class Macro4KGuideArrow;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT GuideArrow : public Overlay {
    friend class OverlayFactory;
public:
    void SetOptions(const GuideArrowOptions & options);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KGuideArrow> GetImpl();
};

}
#endif
