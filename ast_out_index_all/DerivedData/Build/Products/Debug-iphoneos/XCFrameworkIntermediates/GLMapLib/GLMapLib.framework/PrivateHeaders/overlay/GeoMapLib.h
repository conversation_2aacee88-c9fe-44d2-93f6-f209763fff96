//
//  GeoMapLib.hpp
//  Lib4kGeometry
//
//  Created by Qibla on 2022/3/7.
//

#ifndef GeoMapLib_hpp
#define GeoMapLib_hpp

#include <stdio.h>
#include "TMBitmapContext.h"
#include "MapDataType.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 *
 * @param rect 放大图显示区域，如果区域为0，junctionRectPointsCount则返回0
 * @param topLeftRadius 左上角圆弧
 * @param topRightRadius 右上角圆弧
 * @param bottomLeftRadius 左下角圆弧
 * @param bottomRightRadius 右下角圆弧
 * @param cornerPointCount 圆角点个数，默认18个
 * @param junctionRectPoints 输出点串
 * @param junctionRectPointsCount 输出点的个数
 */
TXMAPSDK_EXPORT void GetRoundedPoints(TMRect rect, int topLeftRadius, int topRightRadius, int bottomLeftRadius,
                      int bottomRightRadius, int cornerPointCount, MapVector2d** junctionRectPoints,
                      int *junctionRectPointsCount);

#ifdef __cplusplus
}
#endif

#endif /* GeoMapLib_hpp */
