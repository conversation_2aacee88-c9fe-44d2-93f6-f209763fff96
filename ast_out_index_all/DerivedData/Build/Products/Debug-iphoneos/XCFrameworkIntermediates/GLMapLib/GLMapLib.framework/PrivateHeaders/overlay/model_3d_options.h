//
//  Created by v<PERSON><PERSON><PERSON> on 2021/3/25.
//  Copyright © 2021 TencentMap. All rights reserved.
//

#pragma once

#include <map>
#include "overlay/overlay.h"

namespace MAPAPI {


/**
 * 引擎预定义的模型类型，用户也可以自己定义新的扩展的模型类型。
 */
enum SpiritType{
    kSpiritTypeNone = -1,           //无效值
    kSpiritTypeMainCar = 0,         //我的车
    kSpiritTypeCar = 1,             //小客车
    kSpiritTypeBus = 2,             //大客车
    kSpiritTypeTruck = 3 ,          //大货车
    kSpiritTypePickupTruck = 4,     //小货车
    kSpiritTypePoliceCar = 5,       //特殊车辆:警车
    kSpiritTypeAmbulance = 6,       //特殊车辆:救护车
    kSpiritTypeFireCar = 7,         //特殊车辆:消防车
    kSpiritTypeHuman = 8,           //行人
    kSpiritTypeCyclist = 9,         //骑车人
    kSpiritTypeConebarrel = 10,     //锥桶
    kSpiritTypeFangZhuangTong = 11, //防撞桶
    kSpiritTypeGeLiDun = 12,        //隔离墩
    kSpiritTypeGeLiZhu = 13,        //隔离柱
    kSpiritTypeNum,
};

/**
 * 感知障碍物的ADAS状态类型
 */
enum SpiritAdasStatus{
    kSpiritAdasStatusNormal = 0,    //状态正常
    kSpiritAdasStatusClosest,       //离自车距离过近
    kSpiritAdasStatusDangerous,     //对自车构成危险
    kSpiritAdasStatusWarning,   // 警示
    kSpiritAdasStatusNum,
};

/**
 * 感知障碍物显示状态类型
 */
enum SpiritDisplayStatus{
    kSpiritDisplayNormal = 0,    //正常显示
    kSpiritDisplayAppear,        //开始显示动画
    kSpiritDisplayHidden,         //隐藏显示
    kSpiritDisplayDefault, // 外部不控制状态，使用内部自动的状态变化(外部可能一直在更新overlay的同一个spirit，使用此状态不影响内部动画)
};


/**
 * 设置Model3D的恒大类型
 */
enum SpiritConstantDisplayStyle {
    kSpiritConstantDisplayNO = 0,           //不恒大
    kSpiritConstantDisplayGeo = 1,           //基于地理的位置的恒大方式
    kSpiritConstantDisplayCenter = 2,        //基于中心的恒大方式
};


/**
 * 方向定义
 */
enum Direction{
    kDirectionNone=0,
    kDirectionLeftTop,
    kDirectionTop,
    kDirectionRightTop,
    kDirectionRight ,
    kDirectionRightBottom,
    kDirectionBottom,
    kDirectionLeftBottom,
    kDirectionLeft,
    kDirectionNum,
};

/**
 * 3D 物体对象
 */
class TXMAPSDK_EXPORT SpiritOptions{
public:
    SpiritOptions();
    SpiritOptions(const SpiritOptions & other);
    SpiritOptions& operator=(const SpiritOptions & other);
    ~SpiritOptions();
    
    /**
     * 模型的ID。
     * 如果需要模型的移动有插值动画 或者 渐显动画，必须设置ID。
     */
    SpiritOptions & SetId(int Id);
    int GetId() const;
    
    /**
     * 使用的模型类型，参见enum SpiritType。
     */
    SpiritOptions & SetType(int type);
    int GetType() const;
    
    /**
     * 3D物体的位置
     */
    SpiritOptions & SetCoordinate(const MapVector3d & coordinate);
    MapVector3d & GetCoordinate() const;
    
    /**
     * 物体的方位角、偏航角(yaw)。
     * 单位：度。
     * 默认为0。
     */
    SpiritOptions & SetAngle(float angle);
    float GetAngle() const;
    
    /**
     * 物体的俯仰角，上坡为正。
     * 单位：度。
     * 默认为0。
     */
    SpiritOptions & SetPitch(float pitch);
    float GetPitch() const;
    
    /**
     * 感知障碍物的ADAS状态
     */
    SpiritOptions & SetAdasStatus(SpiritAdasStatus status);
    SpiritAdasStatus GetAdasStatus() const;

    /**
     * 外部动态计算的放缩比例，当前放缩比例需要乘以动态计算的比例
     * @param scale 缩放比例
     */
    SpiritOptions & SetXScale(float scale);
    float GetXScale() const;

    SpiritOptions & SetYScale(float scale);
    float GetYScale() const;

    /**
     * 设置单个Spirit的恒大方式：
     * 不恒大，基于地理恒大，基于中心恒大
     * */
    SpiritOptions & SetConstantDisplayStyle(SpiritConstantDisplayStyle constant_style);

    /**
     * 获取当前Spirit的恒大方式（在视角/比例尺变化时）
     * */
    SpiritConstantDisplayStyle GetConstantDisplayStyle() const;

    /**
        * 是否显示角标
    */
    SpiritOptions & SetIsHasCornerMark(bool isHasCornerMark);
    bool GetIsHasCornerMark() const;

    /**
     * 使用的模型类型，参见enum SpiritDisplayStatus。
     */
    SpiritOptions & SetDisplayStatus(SpiritDisplayStatus status);
    SpiritDisplayStatus GetDisplayStatus() const;

    /**
     * 是否启用和自车碰撞的功能，默认关闭
     * @param enable true: 启用, false: 关闭
     */
    void EnableCollisionWithCar(bool enable);

    /**
     * 是否支持和自车碰撞
     * @return true: 支持, false: 不支持
     */
    bool IsSupportCollisionWithCar() const;

private:
    struct Impl;
    Impl * GetImpl() { return impl_; }
    Impl * GetImpl() const { return impl_; } 
    Impl* impl_;
};

/**
 * 3D模型overlay选项， 每个overlay中可以有一个3D对象，也可以有多个3D对象
 */
class TXMAPSDK_EXPORT Model3DOptions : public OverlayOptions{
public:
    Model3DOptions();
    Model3DOptions(const Model3DOptions & other);
    Model3DOptions & operator =(const Model3DOptions & other);
    ~Model3DOptions();
    
    /**
     * 添加一个3D对象到overlay中。
     */
    Model3DOptions & AddSpirit(const SpiritOptions &spirit);
    
    /**
     * 添加多个3D对象到overlay中。
     */
    Model3DOptions & AddSpirits(const std::vector<SpiritOptions> &spirits);
    
    std::vector<SpiritOptions> & GetSpirits() const;

    std::map<int, long> & GetColorMap() const;
    Model3DOptions & SetColorMap(std::map<int, long> color_map);

    std::map<int, long> & GetCornerColor() const;
    Model3DOptions & SetCornerColor(std::map<int, long> color_map);
    
    virtual OverlayType GetType() const override { return OverlayType::Model3D; }
    
    std::shared_ptr<OverlayOptions> CopyModifyData() override;

    /**
     * 设置模型渐显动画时长，单位秒，默认0.2秒
     */
    Model3DOptions & SetSpiritAppearDuration(float duration);
    float GetSpiritAppearDuration() const;

    /**
     * 设置模型消隐的最后透明度值(即从1.0->alpha)
     * @param alpha: 取值范围[0,1](不在该范围无效)
     * @return
     */
    Model3DOptions& SetFadingOutAlpha(float alpha);
    float GetFadingOutAlpha() const;
    
public:
    int overlay_id_;
    
private:
    struct Impl;
    std::shared_ptr<Model3DOptions::Impl> GetImpl();
    std::shared_ptr<Model3DOptions::Impl> GetImpl() const;
};

/**
 * 材质对象, 指定材质id使用的纹理图片
 * texture为绝对路径 或者 带tag的文件名
 */
class TXMAPSDK_EXPORT Material {
public:
    Material();
    Material(int id, const std::string& texture);
    Material(const Material & other);
    Material & operator = (const Material & other);
    virtual ~Material();
    
    int GetID() const;
    std::string GetTexture() const;
    
    class Impl;
protected:
    std::unique_ptr<Impl> impl_;
};

/**
 * 一个UV动画 参数
 */
class TXMAPSDK_EXPORT UVAnimationParam {
public:
    UVAnimationParam();
    /**
     * uv_offset_start  UV动画初始偏移
     * uv_single_step   UV动画单次步长(0-1)
     * uv_step_count    UV动画步数
     */
    UVAnimationParam(const MapVector2f& uv_offset_start, const MapVector2f& uv_single_step, int uv_step_count);
    UVAnimationParam(const UVAnimationParam & other);
    UVAnimationParam & operator = (const UVAnimationParam & other);
    virtual ~UVAnimationParam();
    
    MapVector2f GetUVOffsetStart() const;
    MapVector2f GetUVSingleStep() const;
    int GetUVStepCount() const;
    
    class Impl;
protected:
    std::unique_ptr<Impl> impl_;
};

/**
 * 材质UV动画参数, 多个材质可以是相同的UV动画参数
 */
class TXMAPSDK_EXPORT MaterialUVAnimationParams {
public:
    MaterialUVAnimationParams();
    /**
     * material_id/material_ids 做该uv动画的 材质id
     * uv_params  UV参数
     */
    MaterialUVAnimationParams(int material_id, const std::vector<UVAnimationParam>& uv_params);
    MaterialUVAnimationParams(const std::vector<int>& material_ids, const std::vector<UVAnimationParam>& uv_params);
    MaterialUVAnimationParams(const MaterialUVAnimationParams & other);
    MaterialUVAnimationParams & operator = (const MaterialUVAnimationParams & other);
    virtual ~MaterialUVAnimationParams();
    
    const std::vector<int>& GetMaterialIDs() const;
    const std::vector<UVAnimationParam>& GetUVParams() const;
    
    class Impl;
protected:
    std::unique_ptr<Impl> impl_;
};

/**
 * 材质UV动画
 * 可以是单个UV动画 或 多个UV动画的组合
 */
class TXMAPSDK_EXPORT MaterialAnimation {
public:
    MaterialAnimation();
    /**
     * duration_in_ms   动画时长,单位ms
     * delay_in_ms 动画延时执行,单位ms
     * uv_param/uv_params  UV动画参数
     * begin_from_current_state 动画被打断时, 停止在当前状态还是终点状态. true为当前状态
     * callback 动画完成/打断 回调
     * callback_context 动画回调上下文
     */
    MaterialAnimation(float duration_in_ms, float delay_in_ms, const MaterialUVAnimationParams& uv_param, bool begin_from_current_state, MapAnimationDidStopCallback callback, void* callback_context);
    
    MaterialAnimation(float duration_in_ms, float delay_in_ms, const std::vector<MaterialUVAnimationParams>& uv_params, bool begin_from_current_state, MapAnimationDidStopCallback callback, void* callback_context);
	
	MaterialAnimation(float duration_in_ms, float delay_in_ms, const MaterialUVAnimationParams& uv_param, bool begin_from_current_state, bool is_cycle, MapAnimationDidStopCallback callback, void* callback_context);
	
	MaterialAnimation(float duration_in_ms, float delay_in_ms, const std::vector<MaterialUVAnimationParams>& uv_params, bool begin_from_current_state, bool is_cycle, MapAnimationDidStopCallback callback, void* callback_context);
    
    MaterialAnimation(const MaterialAnimation & other);
    MaterialAnimation & operator = (const MaterialAnimation & other);
    virtual ~MaterialAnimation();
    
    float GetDuration() const;
    float GetDelay() const;
	bool  GetIsCycle() const;
    const std::vector<MaterialUVAnimationParams>& GetUVParams() const;
    bool BeginFromCurrentState() const;
    MapAnimationDidStopCallback GetCallback() const;
    void* GetCallbackContext() const;
    void SetCallback(MapAnimationDidStopCallback callback, void* context);
    class Impl;
protected:
    std::unique_ptr<Impl> impl_;
};

/**
 * 模型材质数据。
 *
 * 每个模型需要视觉提供三个文件：dat、png、json。
 *
 * dat：模型的顶点数据
 * png：模型的纹理图片
 * json：其它配置信息。
 */
class TXMAPSDK_EXPORT MaterialOptions{
public:
    MaterialOptions();
    MaterialOptions(const MaterialOptions & other);
    MaterialOptions & operator = (const MaterialOptions & other);
    virtual ~MaterialOptions();
    
    /**
     * 模型类型，参见enum SpiritType。
     * 是否必须设置：【必选】
     */
    MaterialOptions & SetSpiritType(int spirit_type);
    int GetSpiritType() const;
    
    /**
     * 模型的格式：3DS，骨骼等。
     * 是否必须设置：【必选】
     */
    MaterialOptions & SetFormat(Model3DType format);
    Model3DType GetFormat() const;
    
    /**
     * dat文件名称。
     * 是否必须设置：【必选】
     */
    MaterialOptions & SetModelName(const std::string & model_name);
    const std::string & GetModelName() const;
    
    /**
     * png/jpg文件名称。
     * 是否必须设置：【必选】
     */
    MaterialOptions & SetImageName(const std::string & image_name);
    const std::string & GetImageName() const;
    
    /**
     * png/jpg夜间文件名称。
     * 是否必须设置：【可选】
     */
    MaterialOptions & SetNightImageName(const std::string & image_name);
    const std::string & GetNightImageName() const;
    
    /**
     * json配置文件名称。
     * 是否必须设置：【可选】
     *
     * 可以在json中配置模型的缩放比例。
     * 骨骼模型可以配置相关动画参数。
     */
    MaterialOptions & SetJsonName(const std::string & json_name);
    const std::string & GetJsonName() const;
    
    /**
     * 模型的缩放比例。
     * 是否必须设置：【可选】
     *
     * 默认为1.0。
     * json配置文件中也可以配置模型缩放比例，此接口会在配置文件的缩放比例基础上进一步缩放。
     */
    MaterialOptions & SetScale(float scale);
    float GetScale() const;
    MaterialOptions & SetZoomWithMap(bool zoomWithMap);
    bool GetZoomWithMap() const;
    /**
     * 设置用到的附加材质纹理, 多材质模型必须设置
     * https://iwiki.woa.com/pages/viewpage.action?pageId=1985784160
     */
    MaterialOptions & SetMaterialImages(std::vector<std::string>& images);
    std::vector<std::string>& GetMaterialImages() const;
    
    /**
     * 设置附加材质纹理, 多材质模型必须设置
     * 没有设置纹理的材质,默认使用 SetImageName 设置的图片
     */
    MaterialOptions & SetMaterials(std::vector<Material>& materials);
    std::vector<Material>& GetMaterials() const;


    /**
    * 设置角标mesh的index
    * 没有设置index则默认不显示
    */
    MaterialOptions & SetCornerMeshIndex(int cornerMeshIndex);
    int GetCornerMeshIndex() const;

    /**
    * 设置角标mesh的纹理名称
    */
    MaterialOptions & SetCornerMeshImageName(std::string cornerMeshImageName);
    std::string GetCornerMeshImageName() const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

}
