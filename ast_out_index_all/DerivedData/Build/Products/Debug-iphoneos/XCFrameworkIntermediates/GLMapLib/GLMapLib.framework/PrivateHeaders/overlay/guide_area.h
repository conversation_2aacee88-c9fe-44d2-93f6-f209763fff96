//
// Created by virgil on 2021/11/9.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once
#include "overlay.h"
#include "guide_area_options.h"

namespace tencentmap{
class Macro4KGuideArea;
}

namespace MAPAPI {

class Texture;
class OverlayFactory;

class TXMAPSDK_EXPORT GuideArea : public Overlay {
    friend class OverlayFactory;
    
public:
    void SetOptions(const GuideAreaOptions & options);
    void SetOptions(const GuideAreaTrafficOptions & options);
    
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::Macro4KGuideArea> GetImpl();
};

}
#endif



