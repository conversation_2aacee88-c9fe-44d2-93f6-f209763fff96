//
//  polyline.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/6.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_POLYLINE_H__
#define __MAPAPI_OVERLAY_POLYLINE_H__

#include <stdio.h>
#include "overlay/overlay.h"
#include "overlay/polyine_options.h"

namespace tencentmap{
class PolylineImpl;
}

namespace MAPAPI {

/**
 * 单一颜色的线。
 */
class TXMAPSDK_EXPORT Polyline : public Overlay{
    friend class OverlayFactory;
    friend class PolylineImpl;
public:
    virtual void    SetPoints(const std::vector<MapVector2d> & points);
    virtual const   std::vector<MapVector2d> & GetPoints();
    
    /**
     * 单位 dp
     */
    virtual void    SetWidth(float width);
    virtual float   GetWidth();
    
    virtual void            SetColor(MapVector4ub color);
    virtual MapVector4ub    GetColor();
    
    /**
     * 单位 dp
     */
    virtual void    SetBorderWidth(float borderWidth);
    virtual float   GetBorderWidth();
    
    virtual void            SetBorderColor(MapVector4ub borderColor);
    virtual MapVector4ub    GetBorderColor();
    
    virtual void    SetFillType(PolylineFillType fillType);
    virtual PolylineFillType GetFillType();
    
    virtual void    SetJoinType(PolylineJoinType joinType);
    virtual PolylineJoinType GetJoinType();
    
    virtual void    SetStartCapType(PolylineCapType startCapType);
    virtual PolylineCapType GetStartCapType();
    
    virtual void    SetEndCapType(PolylineCapType endCapType);
    virtual PolylineCapType GetEndCapType();
    
    /**
     * 设置路线是否可点击
     */
    virtual void SetClickable(bool bClickable) override;
    
    /**
     * 设置路线透明度.
     *
     * @param alpha 透明度(0.0f ~ 1.0f)
     */
    virtual void SetAlpha(float alpha) override;
    
    /**
     * 此接口仅仅对   PolylineFillType::Dot的polyline有效，引擎内部对应 MapRouteType_FootPrint
     * 其它类型设置该值无效
     */
    virtual void SetFootPrintSpacing(float spacing);
protected:
    using Overlay::Overlay;
    
    virtual void RemoveFromMap() override;
    
    std::shared_ptr<tencentmap::PolylineImpl> GetRoute();
    
private:
    std::shared_ptr<PolylineOptions> Options() const{
        return std::dynamic_pointer_cast<PolylineOptions>(options_);
    }
};


}

#endif /* __MAPAPI_OVERLAY_POLYLINE_H__ */
