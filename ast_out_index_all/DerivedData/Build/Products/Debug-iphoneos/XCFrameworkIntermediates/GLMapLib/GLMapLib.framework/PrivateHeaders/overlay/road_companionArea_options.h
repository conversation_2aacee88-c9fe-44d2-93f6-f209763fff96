//
// Created by felix on 2024/4/18.
//
#ifndef NOT_USE_4K_ELEMENT
#pragma once

#include <functional>
#include <tuple>
#include "../base/map_define.h"
#include "overlay.h"
#include "had_structure.h"
#include "road_area_animation.h"
namespace MAPAPI {

class MapImpl;

class TXMAPSDK_EXPORT RoadCompanionAreaOptions : public OverlayOptions {
    public:
    RoadCompanionAreaOptions();

    ~RoadCompanionAreaOptions();

    /**
     设置道路数据
     @param data 数据
     */
    void SetRoadAreaData(mapbase::HAD::RoadAreaData& data);

    /**
     获取道路数据
     */
    mapbase::HAD::RoadAreaData& GetRoadAreaData() const;

    /**
     获取道路数据是否发生了改变
     */
    bool HasChangedRoadAreaData() const;

    /**
    获取道路texture是否发生了改变
    */
    bool HasChangedRoadAreaTexture() const;

    /**
     获取OverlayType
     */
    OverlayType GetType() const override { return OverlayType::RoadCompanionArea; }

    std::shared_ptr<OverlayOptions> CopyModifyData() override;

    /**
     设置伴随面路况纹理
     @param bitmap 纹理数据
     */
    void SetRoadAreaTexture(TMBitmap* bitmap, float animationTime=0.0f, std::function<void(bool)> animationCallback = nullptr);

    /**
     设置伴随虚线路况纹理
     @param bitmap 纹理数据
     */
    void SetRoadLineTexture(TMBitmap* bitmap);

    /**
     设置虚线mask纹理
     @param bitmap 纹理数据
     */
    void SetRoadMaskTexture(TMBitmap* bitmap);

    void ResetChangeSign() const;

    /**
     坐标转换
     */
    void CoordinateTransform(MapImpl * mapImpl);
    void CoordinateTransform();

    /**
     * 设置是否支持点击
     * @param interactive
     */
    void SetInteractive(bool interactive);
    bool GetInteractive() const;

    void GetRoadAreaTexture(TMBitmap** bitmap) const;
    void GetRoadLineTexture(TMBitmap** bitmap) const;
    void GetRoadMaskTexture(TMBitmap** bitmap) const;

    void SetCompanionType(mapbase::HAD::CompanionType type);
    mapbase::HAD::CompanionType GetCompanionType() const;
private:
    struct Impl;
    std::shared_ptr<RoadCompanionAreaOptions::Impl> GetImpl();
    std::shared_ptr<RoadCompanionAreaOptions::Impl> GetImpl() const;
};

}
#endif //NOT_USE_4K_ELEMENT
