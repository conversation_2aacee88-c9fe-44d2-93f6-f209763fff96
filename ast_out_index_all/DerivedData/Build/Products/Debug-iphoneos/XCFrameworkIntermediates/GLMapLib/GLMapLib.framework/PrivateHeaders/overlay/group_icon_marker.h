//
//  group_marker.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/4.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_GROUP_ICON_MARKER_H__
#define __MAPAPI_OVERLAY_GROUP_ICON_MARKER_H__

#include <stdio.h>
#include "overlay/marker.h"
#include "overlay/group_icon_marker_options.h"

namespace tencentmap{
class MapMarkerGroupIcon;
}

namespace MAPAPI {

class TXMAPSDK_EXPORT GroupIconMarker : public Marker{
    friend class OverlayFactory;
public:
    /**
     * 修改GroupIconMarker所有属性
     *
     */
    virtual void SetOptions(const OverlayOptions & options) override;
    
    struct PosInfo{
        int point_index;
        int anchor_index;
    };
    
    /**
     * 获取组合气泡在进行内部避让计算之后，最终摆放的位置和方向。
     *
     * @param pos, output param
     */
    void GetPos(PosInfo &pos);
private:
    using Marker::Marker;
     std::shared_ptr<tencentmap::MapMarkerGroupIcon> GetImpl();
};

}

#endif /* __MAPAPI_OVERLAY_GROUP_ICON_MARKER_H__ */
