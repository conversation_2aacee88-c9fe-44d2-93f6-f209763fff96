//
//  polygon_options.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/9.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_POLYGON_OPTIONS_H__
#define __MAPAPI_OVERLAY_POLYGON_OPTIONS_H__

#include <stdio.h>
#include "overlay/overlay.h"

namespace MAPAPI {

typedef std::vector<std::vector<MapVector2d> > HolePointType;

class TXMAPSDK_EXPORT PolygonOptions : public OverlayOptions{
public:
    PolygonOptions();
    PolygonOptions(const PolygonOptions & other);
    ~PolygonOptions() override;

    PolygonOptions &     SetPoints(const std::vector<MapVector2d> & points);
    const std::vector<MapVector2d> & GetPoints() const;
    
    PolygonOptions &     SetHolePoints(const HolePointType & holePoints);
    const HolePointType &GetHolePoints() const;

    virtual PolygonOptions &     SetFillColor(const MapColor4ub & fillColor);
    virtual const MapColor4ub &  GetFillColor() const;
    
    virtual PolygonOptions &     SetBorderColor(const MapColor4ub & borderColor);
    virtual const MapColor4ub &  GetBorderColor() const;
    
    PolygonOptions &     SetDashPattern(const std::vector<int> & pattern);
    const std::vector<int> & GetDashPattern() const;
    
    /**
     * 单位 dp
     */
    PolygonOptions &     SetLineWidth(float lineWidth);
    float                GetLineWidth() const;
    
    /**
     * 单位 m
     */
    PolygonOptions &     SetShrinkDist(float dist);
    float                GetShrinkDist() const;
    
    OverlayType GetType() const override { return OverlayType::Polygon; }
    
    PolygonOptions &    SetClickable(bool clickable);
    bool                GetClickable() const;
    
private:
    struct Impl;
    std::shared_ptr<PolygonOptions::Impl> GetImpl();
    std::shared_ptr<PolygonOptions::Impl> GetImpl() const;
};

}

#endif /* __MAPAPI_OVERLAY_POLYGON_OPTIONS_H__ */
