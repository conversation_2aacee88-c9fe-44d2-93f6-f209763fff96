//
//  turn_arrow.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/2.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_TURN_ARROW_H__
#define __MAPAPI_OVERLAY_TURN_ARROW_H__

#include <stdio.h>
#include <vector>
#include "base/map_define.h"

namespace MAPAPI {

/**
 * 导航转弯箭头
 */
class TXMAPSDK_EXPORT TurnArrow {
public:
    /**
     * 设置箭头的转弯index
     */
    void SetTurnIndex(int segmentIndex, int actionLength);
    /**
     *  设置箭头的转弯index与转弯类型
     *  @param IntersectionType 转弯类型，用来计算UTurn方向
     */
    void SetTurnIndexWithType(int segmentIndex, int actionLength,  int IntersectionType);
    /**
     * 设置导航箭头是否为3D样式。默认为3D样式
     * 产品决策 后续只对外提供3d箭头，这个接口后续逐步废弃
     */
    void Set3DStyleEnabled(bool is3DStyleEnabled);
    
    /**
     * 设置2D导航箭头的样式
     */
    void Set2DStyle(MapVector4ub color, MapVector4ub outlineColor);
    
    struct Arrow3DStyle {
        MapVector4ub roof_color; //顶面颜色
        MapVector4ub wall_color; //侧面颜色
        MapVector4ub edge_color; //边线颜色
        MapVector4ub shadow_color; //阴影颜色
    };
    
    /**
     * 设置3D导航箭头的样式
     */
    void Set3DStyle(const struct Arrow3DStyle & style);
    
    /**
     * 获取第一个箭头的bound坐标范围.
     * 箭头不显示的时候，返回{0,0,0,0}.
     */
    TMRect GetBound();
    
    /**
     * 设置第二个箭头从半透渐变到第一个箭头样式的动画执行进度。
     * @param progress , 范围[0， 1.0]
     */
    void SetAnimationProgress(float progress);
    
    /**
     * 设置箭头的宽度 高度缩放系数
     * @param fWidthScale , 范围([0,100)
     * @param fHeightScale , 范围([0,100)
     */
    void SetTurnArrowScale(float fWidthScale, float fHeightScale);

public:
    //外部不要调用
    enum class TurnArrowOrder {
        None,
        First,
        Second
    };
    
    TurnArrow(TurnArrowOrder order, void *world);
    
    class Impl;
    
private:
    friend class ColorPolylineImpl;
    std::unique_ptr<Impl> impl_;
};

}

#endif /* __MAPAPI_OVERLAY_TURN_ARROW_H__ */
