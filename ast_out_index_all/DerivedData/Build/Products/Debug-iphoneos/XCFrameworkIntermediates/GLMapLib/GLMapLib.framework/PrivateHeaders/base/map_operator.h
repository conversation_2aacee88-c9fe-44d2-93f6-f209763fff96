//
//  map_style_operator.h
//  GLMapLib2.0
//

#ifndef __MAPAPI_BASE_MAP_OPERATOR_H__
#define __MAPAPI_BASE_MAP_OPERATOR_H__

#include <stdio.h>
#include <string>
#include <vector>
#include "base/map_define.h"

namespace MAPAPI {

class MapCore;
class MapController;

class TXMAPSDK_EXPORT MapOperator : public MapComponent{

};


class TXMAPSDK_EXPORT OverviewOperator : public MapOperator{
public:
    /**
     * 设置鹰眼图圆角样式
     * TODO:需要详细描述每个字段的意义
     */
    void SetCornerStyle(bool bEnabled, int cornerLength, float opacity);
    
    /**
     * 设置鹰眼图描边样式
     *
     * @param color 描边颜色
     * @param width 描边宽度
     */
    void SetFrameStyle(bool bEnabled, MapColor4ub color, float width);
};

//deprecated
struct POIInfo{
    MapLocation        coordinate;
//    unsigned short     name[POINameMaxLength];
    std::string        name;
};

/**
 * layer_id: 百变poi的图层id, 非百变poi为""
 * poi_id: poi id
 * content:百变poi的static content和dynamic content 拼接,如 {"static":{"styleid":4000,"name":"奥林匹克森林公园","is_wx":0},"dynamic":{"hotClass":"S","hotText":"人流量较小"}}
 */
struct TXMAPSDK_EXPORT MapPOI{
    MapPOI();
    MapPOI(const std::string& layer_id, uint64_t poi_id, const std::string& content);
    std::string layer_id;
    uint64_t poi_id;
    std::string content;
};


class TXMAPSDK_EXPORT AnnotationOperator : public MapOperator{
public:
    
    /**
     * 返回当前屏幕范围内的兴趣点列表
     * Deprecated
     *
     * @param bExtendMargin     是否返回屏幕外边缘的兴趣点；
     * @return                  实际返回的POI个数；
     */
    int GetPoisOnScreen(bool bExtendMargin, std::vector<POIInfo> & pois);
    
    /**
     * 设置底图文字是否避让蚯蚓线
     */
    void SetAnnotationNeedAvoidColorRoutes(bool needAvoid);
    
    /**
     * 设置Annotation 文字能否点击，默认false
     */
    void SetAnnotationClickTextEnable(bool clickTextEnable);
    
    /**
     * 重新加载底图标注资源
     */
    void ReloadAnnotations();
    
    /**
     *  显示/隐藏 底图标注
    */
    void SetAnnotationVisible(bool bVisible);
    
    /**
     * 是否开启当前屏幕可见POI收集, 开启后才会收集可见POI, 调用GetVisiblePOI才会返回当前屏的可见POI
     */
    void EnableCollectVisiblePOI(bool enalbe);
    
    /**
     * 获取当前屏展示的POI信息, 目前只返回百变POI
     */
    std::vector<MapPOI> GetVisiblePOI();
};

class TXMAPSDK_EXPORT StyleOperator : public MapOperator{
public:
    /**
     * 设置地图样式索引
     *
     * @param mapStyleID 样式索引
     * @param reuseOnSwitch 切换的过程中是否复用当前样式
     */
    void SetMapStyle(int mapStyleID, bool reuseOnSwitch);
    
    /**
     * 设置地图样式索引
     *
     * @param mapStyleID 样式索引
     * @param reuseOnSwitch 切换的过程中是否复用当前样式
     * @param isAnimation 切换样式时，是否有动画过度
     * @param animationDuration 样式过度动画的执行时长,isAnimation=true时才生效
     */
    void SetMapStyleWithAnimation(int mapStyleID, bool reuseOnSwitch,
                                  bool isAnimation, double animationDuration);
    
    /**
     * 获取当前地图样式索引
     *
     * @return 样式索引
     */
    int  GetMapStyle();
    
    /**
     * 获取背景颜色
     */
    MapVector4ub GetBackgroundColor();
    
    /**
     * 设置皮肤对应动态样式索引
     *
     * @param mapSkinUID 动态样式索引
     * @param reuseOnSwitch 切换的过程中是否复用当前样式
     */
    void SetMapSkin(int mapSkinUID, bool reuseOnSwitch);
    
    /**
     * 设置皮肤对应动态样式索引，带动画
     *
     * @param mapSkinUID 样式索引
     * @param reuseOnSwitch 切换的过程中是否复用当前样式
     * @param isAnimation 切换样式时，是否有动画过度
     * @param animationDuration 样式过度动画的执行时长,isAnimation=true时才生效
     */
    void SetMapSkinWithAnimation(int mapSkinUID, bool reuseOnSwitch,
                                 bool isAnimation, double animationDuration);
    
    /**
     * 获取当前地图动态样式索引
     *
     @return 动态样式索引
     */
    int  GetMapSkin();
    
    /**
     * 设置底图文字大小级别
     *
     * 此接口针对所有地图实例。
     *
     * TODO:不好的设计
     *
     * @param fontSizeFlag  MF_INVALID = -1, MF_NORMAL = 0, MF_LARGE = 1, MF_HUGE = 2, MF_SMALL = 3, MF_CARPLAY = 4
     */
    bool SetFontSize(int fontSizeFlag);
    
    /**
     * 设置底图文字大小级别
     * 此接口只对当前地图实例生效。
     */
    bool SetSceneFontSize(int fontSizeFlag);
    
    /**
     * 获取底图文字大小级别
     * @return MF_INVALID = -1, MF_NORMAL = 0, MF_LARGE = 1, MF_HUGE = 2, MF_SMALL = 3, MF_CARPLAY = 4
     */
    int GetFontSize();
    
    /**
     * 设置天空盒子显示时，天空所占比例。范围（0， 1.0）
     */
    void SetSkyRatioOnScreen(float ratio);
    
    /**
     * 获取天空盒子显示时，天空所占比例。范围（0， 1.0）
     */
    float GetSkyRatioOnScreen();
    
    /**
     * 设置背景图片
     */
    void SetBackgroundImage(const std::string & imageName);
};

}

#endif
