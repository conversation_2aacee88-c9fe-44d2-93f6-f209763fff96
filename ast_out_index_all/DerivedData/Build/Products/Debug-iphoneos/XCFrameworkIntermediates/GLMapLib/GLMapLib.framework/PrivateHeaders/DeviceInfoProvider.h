//
// Created by morgansun on 2022/6/29.
//

#ifndef TXMAPSDK_ANDROID_NEW_DEVICEINFOPROVIDER_H
#define TXMAPSDK_ANDROID_NEW_DEVICEINFOPROVIDER_H

#include <string>

namespace tencentmap {

class IDeviceInfoProvider {
 public:

  virtual ~IDeviceInfoProvider() = default;
  /**
   * 获取QIMEI
   * @return
   */
  virtual std::string GetQImei() = 0;

    /**
     * 获取QIMEI36
     * @return
     */
    virtual std::string GetQImei36() = 0;

  /**
   * 获取TokenID
   * @return
   */
  virtual std::string GetTokenID() = 0;

  /**
   * 描述: 获取fr
   *
   * @return fr
   */
  virtual std::string GetFr() = 0;

  /**
   * 描述: 获取渠道号
   *
   * @return 渠道号
   */
  virtual std::string GetChannel() = 0;

  /**
   * 描述: 获取网络类型
   *
   * @return 网络类型
   */
  virtual std::string GetNetType() = 0;

  /**
   * 描述: 获取登陆后的user id
   *
   * @return 登陆后的user id
   */
  virtual long GetUserId() = 0;

  /**
   * 描述: 获取版本号
   *
   * @return 版本号
   */
  virtual std::string GetVersion() = 0;

  /**
   * 描述: 获取完整设备版本号（version_name+version_code)
   *
   * @return 完整设备版本号
   */
  virtual std::string GetFullVersion() = 0;

};

}
#endif //TXMAPSDK_ANDROID_NEW_DEVICEINFOPROVIDER_H
