//
//  tile_overlay.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/4/28.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_TILE_OVERLAY_H__
#define __MAPAPI_BASE_TILE_OVERLAY_H__

#include <stdio.h>
#include <string>
#include "base/map_define.h"

namespace MAPAPI {

struct BitmapTileID
{
    int x;
    int y;
    int z;
    std::string url;
};

class TXMAPSDK_EXPORT TileOverlayOption {
public:
    //加载瓦片回调函数
    virtual TMBitmap* OnLoadTile(const BitmapTileID & param) = 0;
    
    //回写瓦片回调函数
    virtual void OnWriteTile(const BitmapTileID & param, const void *data, int data_size) = 0;
    
    //高清/普清显示：true for HD, false for SD
    virtual bool isBetterQuality() = 0;
};

class TXMAPSDK_EXPORT TileOverlayImpl
{
public:
    TileOverlayImpl(){}
    virtual ~TileOverlayImpl() {}
    void * mpWorld;
    int  mTileOverlayId;
    std::shared_ptr<TileOverlayOption> mOptions;
};

class TXMAPSDK_EXPORT TileOverlay {
    friend class TileOverlayManager;
public:
    /**
     * 重新加载TileOverlay图层(内部会重新创建图层，特殊属性需要外部重新设置，如数据级别范围)
     */
    void Reload();
    
    /**
     * 设置TileOverlay对应业务可用的数据级别；(不设置引擎默认4到18级)
     */
    void SetDataLevelRange(int minDataLevel, int maxDataLevel);
    
    /**
     * 设置显示优先级
     * kTileOverlayDefaultPriority +1, +2, +3 ...
     */
    void SetPriority(int priority);
    
    /**
     * 获取显示优先级
     */
    int GetPriority();
    
private:
    TileOverlay(void * world, const std::shared_ptr<TileOverlayOption> & option);
    
    void RemoveFromMap();

protected:
    std::unique_ptr<TileOverlayImpl> _impl;
};

}

#endif /* __MAPAPI_BASE_TILE_OVERLAY_H__ */
