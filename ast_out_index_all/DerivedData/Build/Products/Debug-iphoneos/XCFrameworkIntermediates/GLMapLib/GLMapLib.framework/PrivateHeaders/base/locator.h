//
//  locator.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/3.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_BASE_LOCATOR_H__
#define __MAPAPI_BASE_LOCATOR_H__

#include "locator_info.h"


namespace tencentmap{
class MarkerLocator;
};

namespace MAPAPI {

class TXMAPSDK_EXPORT Locator {

public:
    /**
     * 更新自车点位置信息。（这个接口后续废弃掉，用下面的LocationInfo）
     * @param mapPoint GPS坐标
     * @param angle    GPS方向
     * @param accuracy GPS定位精度
     * @param animated 是否动画移到新的位置上
     * @param pitch    GPS俯仰角
     */
    void SetLocationInfo(const MapVector3d & mapPoint, float angle, float accuracy, bool animated, float pitch = 0.0f);
    
    /**
     * 更新自车点位置信息。
     * @param location_info GPS坐标
     * @param animated 是否动画移到新的位置上
     */
    void SetLocationInfo(const LocationInfo & location_info, bool animated);
    
    /**
     * 设置数据更新时间间隔，即调用SetLocationInfo()接口的频率。
     * 默认为0.1秒。(注释要和代码对应)
     */
    void SetUpdateDuration(double duration);
    
    /**
     * 设置地图中心是否follow自车位置。
     * bFollow： 地图中心是否跟随车标位置
     * bHeadingForward：地图旋转角度是否跟随车标角度
     */
    void SetLocationFollow(bool bFollow, bool bHeadingForward);
    
    /**
     * 更新自车点方向。
     */
    void SetLocationHeading(float angle);
    
    /**
     * 设置是否隐藏自车点.
     */
    void SetHidden(bool hidden);
    
    /**
     * 设置是否可点击.
     */
    void SetClickable(bool bClickable);
    
    /**
     * 设置自车点图标
     */
    void SetIndicatorImage(const std::string & imageName, const MapVector2f & anchorPoint);
    void SetIndicatorImage(const std::string & imageName, const std::string & coverName, const MapVector2f & anchorPoint);
    void SetIndicatorImage(const std::string & background_name, const std::string & imageName, const std::string & coverName, const MapVector2f & anchorPoint);
    
    /**
     * 设置呼吸动画效果需要的图片。
     */
    void SetBreathImage(const std::string & image);
    
    /**
     设置呼吸圈图标与偏移锚点
     @param imageName 呼吸圈图标
     @param anchorPoint 偏移锚点
     */
    void SetBreathImage(const std::string & imageName, const MapVector2f & anchorPoint);
    
    /**
     * 设置是否隐藏自车点的compass[东南西北]
     */
    void SetCompassHidden(bool hidden);
    
    /**
     * 设置自车点的compass.
     * 这是旧样式：东南西北+圆环在一张图片上，存在文字倒着显示的问题。
     */
    void SetCompassImage(const std::string & imageName, const MapVector2f & anchorPoint);
    
    /**
    * 设置自车点的compass.
    * 这是新样式：东南西北分别是不同的图片，所以显示时，可以实现文字方向始终朝上。
    */
    void SetCompassGroupImages(const std::string & ringName,
                               const std::string & eastImageName, const std::string & southImageName,
                               const std::string & westImageName, const std::string & northImageName,
                               const MapVector2f & anchorPoint);
    
    /**
     * 设置起点到终点的红线的显示状态。
     */
    void SetRedLineHidden(bool hidden);
    
    /**
     * 起点到终点的红线参数
     */
    struct RedLineParam{
        MapVector2d     end_point;
        float           line_width; //单位 dp
        MapVector4ub    line_color;
    };
    
    /**
     * 设置红线的相关参数
     */
    void SetRedLineParam(const RedLineParam & param);
    
    /**
     * 设置GPS定位精度圈的显示状态。
     */
    void SetAccuracyAreaHidden(bool hidden);
    
    /**
     * 设置GPS定位精度圈的颜色。
     */
    void SetAccuracyAreaColor(MapVector4ub color);
    
    /**
     * 设置步行导航时的方向环的显示状态。
     * 路线方向与用户方向的夹角会显示一个圆环。
     * 小于30度，为绿色；小于60，为黄色；其它为红色。
     */
    void SetColorRingHidden(bool hidden);
    
    /**
     * 步行方向圆环的参数
     */
    struct ColorRingParam{
        std::string        route_direction_point_image;
        std::string        green_ring_image;
        std::string        orange_ring_image;
        std::string        red_ring_image;
        float              max_angle_for_green_ring;  //角度小于此值时，圆环采用绿色绘制，默认30度
        float              max_angle_for_orangle_ring;//角度小于此值时，圆环采用橙色绘制，默认60度
        MapVector2f        route_direction_point_anchor;
    };
    
    /**
     * 设置步行方向圆环的样式参数。
     */
    void SetColorRingParam(const ColorRingParam &param);
    
    /**
     * 设置步行路线的方向。
     */
    void SetRouteDirection(float dir);
  
    void SetIndicatorMaxSkew(float angle);
    void SetBackgroundMaxSkew(float angle);
    
    /**
     * 设置呼吸动画的显示状态。
     */
    void SetBreathAnimHidden(bool hidden);
    
    /**
     * 车标显示类型
     */
    enum class TXMAPSDK_EXPORT DisplayType{
        Normal          = 0,      //普通车标
        Speed           = 1,      //车速车标
        Model3D         = 2,      //3D车标
        Model3DSwitch2D = 3
    };
    
    /**
     * 获取当前车标的种类
     * @return            当前的车标种类
     */
    enum DisplayType GetDisplayType();
    
    /**
     * 设置当前车标的种类
     * @param type        车标类型枚举
     */
    void SetDisplayType(enum DisplayType type);
    
    /**
     *  扫光方向类型
     */
		enum class ScanLightDirection
  	  {
  	      kLeftToRight = 0,
  	      kRightToLeft,
  	      kTopToBottom,
  	      kBottomToTop,
  	      kFrontToBack,
  	      kBackToFront
  	  };
		
    /**
     *	扫光参数
     */
    struct LocatorScanLightOptions {
      TMColor 						color 	  = TMColorMake(255, 0, 0, 255);		// 扫光rgba
      float								width 	  = 0.0f;								// 扫光宽度，按比例计算
      float								intensity = 0.0f;								// 泛光强度，范围[0.0, 1.0] 越接近1.0f越强，影响并非线性，建议区间[0.5, 0.7]
      float								delay 	  = 0.0f;								// 扫光淡入/浅出时间比例，范围[0.0, 0.5]
      double							duration  = 0.0;   								// 扫光持续时间，单位s
      float								cutting   = 0.0f;								// 裁剪高度
      ScanLightDirection	direction = ScanLightDirection::kFrontToBack; 	// 扫光方向
      
      // 参数检查
      bool Check() const;
    };
    
    /**
     * 设置扫光参数
     * @param options	扫光参数
     */
    void SetScanLightOptions(const LocatorScanLightOptions& options);

		/**
		 * 关闭扫光
		 */
		void ShutDownScanLight();
		
  	enum class LocatorComponent {
  	  kIndicator         	= 0, // 车模主体
  	  kCompass            = 1, // 罗盘圈
  	  kNaviAccuracyCircle 	= 2, // 导航精度圈
  	};
  
  	/**
     * 呼吸节点参数
     */
  	struct LocatorBreatheNode {
  	  float ratio; // 时间比例
  	  float alpha; // 透明度
  	  float scale; // 缩放大小
  	};
  
    /**
     *	呼吸参数
     */
    struct LocatorBreatheOptions {
      double duration = 0.0; 	 //	动画时长
      int    cycle 		= 0;	 	 // 循环次数
      std::vector<LocatorBreatheNode> nodes; // 动画节点
      
      // 参数检查
      bool Check() const;
    };
		
    /**
     * 播放呼吸动画
     * @param component 车标组件
     * @param options	呼吸动效参数
     */
    void StartBreatheAnim(LocatorComponent component, const LocatorBreatheOptions& options);
		
  	enum class BreatheAnimEndType {
  	  kStopAtCurrent,
  	  kStopAtEnd,
  	  kStopAtBegin,
  	};
  
    /**
     * 结束呼吸动画
     * @param component 车标组件
     * @param end_type 结束方式
     */
    void EndBreatheAnim(LocatorComponent component, BreatheAnimEndType end_type);
  
  	/**
     * 导航精度圈绘制渐变节点
     */
  	struct NaviAccuracyCircleGradientNode {
    	float ratio;
    	float alpha;
    };

  	/**
     * 导航精度圈绘制参数
     */
  	struct NaviAccuracyCircleOptions {
  	  TMColor color;  // 精度圈颜色
  	  float   scale;  // 相对罗盘圈的缩放比例，保证绘制时与车标罗盘圈大小比例一致
  	  std::vector<NaviAccuracyCircleGradientNode> nodes; // 精度圈绘制的渐变节点，控制渐变绘制精度圈的方式
      
      bool Check() const;
  	};
  
  	/**
     * 设置导航精度圈绘制参数
     */
  	void SetNaviAccuracyCircleOptions(const NaviAccuracyCircleOptions& options);
  
  	/**
     * 设置导航精度圈是否隐藏
     */
  	void SetNaviAccuracyCircleHidden(bool is_hidden);
  
    /**
     * 获取当前车标的透视效果透明度
     * @return            当前的车标透视效果透明度
     */
    float GetTransparency();
    
    /**
     * 设置当前车标的透视效果透明度
     * @param transparency        当前的车标透视效果透明度
     */
    void SetTransparency(float transparency);
    
    /**
     * 设置3D模型数据
     */
    void SetModel3DMaterial(const MAPAPI::MaterialOptions & material);
    /**
     * 设置3D模型数据与偏移锚点
     * @param   material            3d车标option
     * @param   anchorPoint     偏移锚点，默认中心为(0.5, 0.5)，z无效果以备后用
     */
    void SetModel3DMaterialWithAnchor(const MAPAPI::MaterialOptions & material, const MapVector3f & anchorPoint);
    /**
     * 设置自车3D模型的大小是否随着地图缩放而变化。
     * 普通导航场景下，自车3D模型大小通常不应该随着地图缩放而改变，始终显示恒定的像素大小。
     * 车道级导航场景下，自车3D模型大小需要和车道宽度保持一致，会随着地图缩放而变化。
     * 默认为FALSE。
     */
    void SetModel3DSizeChangeWhenZoomMap(bool isChange);
    /*
     * 设置自车是否随着地图缩放的比例尺。当未达到该比例尺时，显示恒定的像素大小。
     * 当达到该比例尺时，会随着地图缩放而变化。
     * 默认zoomLevel为-1，不开启缩放比例尺缩放功能。
     */
    void SetModel3DZoomMapLevel(float zoomLevel);

    /**
      * 获取自车标屏幕空间包围盒
      * @return {左上，左下，右上，右下}
      */
    std::array<MapVector2f, 4> GetBoundRect();

    /**
     * 车道级导航时，设置危险障碍物的方位
     */
    void SetDangerousDirection(Direction dir);
    
    /**
     * 设置车速车标的速度值，必须在setDisplayType之后调用
     * @param speedNum   速度值，范围应当>=-1，<= 999，否则无效，当没有gps信号时设置为-1，显示--
     */
    void SetSpeedNum(int speedNum);
    
    struct SpeedTextParam{
        int                vertical_offset;  //相对于中心点的垂直像素偏移
        MapVector4ub       text_color;       //文字颜色
        int                font_size;        //文字大小, 单位dp
        std::string        content;          //描述速度单位的文字，为空时使用默认单位文字，比如km/h
    };
    
    /**
     * 设置车速车标的文字参数，必须在setDisplayType之后调用
     * @param paramForSpeedText   速度文字的参数
     * @param paramForUnitText    速度单位的参数
     */
    void SetSpeedTextParam(const SpeedTextParam & paramForSpeedText,
                           const SpeedTextParam & paramForUnitText);
    
    /**
     * 设置骨骼动画的参数
     * @param action              动画参数的json字串
     */
    void SetSkeletonAnimAction(const std::string & action);
  
    struct SkeletonAnimOption {
      std::string group; // 骨骼组名称
      std::string anim;  // 骨骼动画名称
      int cycle;         // 骨骼动画循环次数 INT_MAX 为无限循环
      float transistion; // 过渡时间，单位s
      float min_alpha;   // 过渡动画最小透明度
    };

    /**
     * 设置骨骼动画
     * @param option  动画参数
     */
    void SetSkeletonAnimAction(const SkeletonAnimOption& option);

    
    AnimationOwnerEnum GetAnimationOwnerType() const;
    
    /**
     * 修改材质纹理, 启动材质动画
     * materials 为空时只启动动画, 不修改材质纹理
     * animation 为空时, 只修改材质纹理, 不启动材质动画
     */
    void SetMaterialsWithAnimation(const std::vector<Material>& materials, const std::shared_ptr<MaterialAnimation>& animation);

    /**
     * 修改车模卡尺颜色
     * @param material_ids 材质的id
     * @param mix_color 混合的颜色 格式是RGBA
     */
    void SetMaterialsMixColor(const std::vector<int>& material_ids, const MapColor4ub& mix_color);
    /**
     * 停止材质动画
     * material_ids 需要停止动画的材质id
     */
    void StopMaterialAniamtions(const std::vector<int>& material_ids);

    /**
    * 设置探测波素材&偏移锚点
    * @param imageName 探测波图片
    * @param iAnchor 偏移锚点
    * @param color 探测波的颜色
    */
    void setRadarWaveImageWithAnchor(const std::string & imageName, const MapVector2f & anchorPoint, const MapColor4ub &color);
    /**
    * 设置探测波是否隐藏。默认隐藏
    */
    void setRadarWaveHidden(bool hidden);
    /**
    * 设置探测波颜色
    * @param color探测波的颜色
    * @param duration 动画播放时间，单位：秒，设置为0.0时直接变为最终位置
    */
    void setRadarWaveColorWithDuration(const MapColor4ub &color, float duration);
    /**
     * 设置探测波方向
     * @param angle 与正北方向夹角；顺时针夹角为正
     * @param duration 动画播放时间，单位：秒，设置为0.0时直接变为最终角度
     */
    void setRadarWaveAngleWithDuration(float angle, float duration);
  
    /**
     * 获取探测波包围盒
     * @return 包围盒四个顶点屏幕坐标，{左上，左下，右上，右下}
     */
    std::array<MapVector2f, 4> getRadarWaveBoundingBox();
  
    /**
     * 设置车标是否分层绘制，允许非指示标部分被压盖
     */
    void SetIsLayeredRendering(bool is_layered);

private:
    Locator();
    friend class MapImpl;
    class Impl;
    std::unique_ptr<Impl> impl_;
    std::unique_ptr<Impl> & GetImpl();
    
    std::shared_ptr<tencentmap::MarkerLocator> GetLocator();
};

}

#endif /* __MAPAPI_BASE_LOCATOR_H__ */
