//
//  GLMapLib2.0_Private.h
//  TXMapSDKiOS
//
//  Created by lihuafeng on 2018/9/13.
//  Copyright © 2018年 lihuafeng. All rights reserved.
//

#ifndef GLMapLib2_0_Private_h
#define GLMapLib2_0_Private_h

#include "GLMapLib_Base.h"
#include "GLMapLib_Marker.h"
#include "GLMapLib_Route.h"
#ifdef __cplusplus
extern "C" {
#endif
    
    /**
     地图回调事件类型
     */
    typedef MAP_ENUM(int, MapCallBackType)
    {
        MapCallBackType_CameraChanged,//占位，sdk不处理此事件
        MapCallBackType_CameraChangeStopped,//地图状态变化完成
        MapCallBackType_CenterChanged,
        MapCallBackType_ScaleChanged,
        MapCallBackType_SkewAngleChanged,//地图俯仰角发生变化 //4
        MapCallBackType_RotateAngleChanged,
        MapCallBackType_MarkerPositionChanged,//marker 的位置发生变化，对应参数回返回itemid
        MapCallBackType_ActiveInterestAreaChanged,//景区面与室内   逐步弃用
        MapCallBackType_ReportBeforeExit,
        MapCallBackType_ReportDrawTime, // 上报路线从创建到第一次绘制出来的时间  //9
        
        //MapMarkerIcon显示从主图片切换到备选图片，或从备选图片切换到主图片
        //param0是overlayID，类型为long
        //param1 :  =1 ,表示，切换到备选图片， =0，表示切换到主图片，类型为long
        MapCallBackType_MarkerIconSwitchAlternative,
        
        MapCallBackType_ReportStatisticsInfo,     // 统计上报相关 由上层通过灯塔上报
        
        //地图样式切换
        //param0:旧style
        //param1：新style
        MapCallBackType_MapStyleChanged,
        MapCallBackType_MapLoadingFinished,       //底图资源加载完并绘制完成
        MapCallBackType_MarkerIsAvoided,//地图marker overlay因为被避让而消失  //14
        MapCallBackType_PositionChangedAfterDrawFrame,
        
        MapCallBackType_SafetyCameraAnchorChanged,
        MapCallBackType_Model3DResult,    //3D模型的解析结果
        MapCallBackType_MapDestroy,
        MapCallBackType_MapOnTapInfo,  //19
        MapCallBackType_ViewportChanged,
        MapCallBackType_ScreenCenterOffsetChanged,
        MapCallBackType_PaddingChanged,

        MapCallBackType_CacheRemoved,
        MapCallBackType_JuncMapCompleted,  //24
        MapCallBackType_IndoorActiveAreaChanged,  // 室内激活回调
        MapCallBackType_ScenicActiveAreaChanged,  // 景区激活回调
        MapCallBackType_SatTileStatusChanged, // 卫星图瓦片生效状态变化
        
        MapCallBackType_MapFpsChanged,        // 底图FPS被设置 //28
        
        MapCallBackType_CustomPonitLoaded, // 百变地图一个瓦块内的点数据透出
        MapCallBackType_SD_HD_Switch,      // sd hd 切换
 
        MapCallBackType_ParticleRunOver, //粒子动效运行结束  //31

        MapCallBackType_LandmarkLoadOrDraw, //landmark数据被加载或者首次绘制,参数为0是加载成功，1是首帧绘制完成  //32
 
        MapCallBackType_Object3DPicked,      // 3D点选,参数0 3d物体的类型和ID，参数1==0单击选中，==1双击选中

        MapCallBackType_MapClick,        //单指双击点击回调
        MapCallBackType_Building_First_Visited,    //建筑物被打卡首次显示
        MapCallBackType_Building_First_HighLight,    //建筑物高亮首次显示
        MapCallBackType_VIP_AdvertisingPanel,    // 投放建筑物广告牌显示
        MapCallBackType_Landmark_CollideOrResume, // 模型碰撞ID回调，参数为0是碰掉，参数为1是恢复 38
        MapCallBackType_LocatorPositionChanged,    //车标位置的回调
        MapCallBackType_ModelComponentReport,    // 模型和组件埋点上报 40
        MapCallBackType_PedestrianReport,         // 天桥埋点上报 41
	    MapCallBackType_ClusterGroup_GetAgileMarkerOption = 42, // 获取聚合点群的聚合点信息 42
        MapCallBackType_Num
    };
    
    /**
     地图callback 事件回调
     
     @param type 回调事件类型
     @param param0 MapCallBackType_MarkerPositionChanged 时为item id
     @param param1 MapCallBackType_PositionChanged 时表示scale是否发生变化
     @param context 一般传递mapview地址
     */
    typedef void    (*MapCallBack_MapEvent)(MapCallBackType type, const void* param0, const void *param1, void* context);

    /**
     * 扫描动画完成以后的回调
     */
    typedef void    (*MapCallBack_OnScanFinished)(void *context);

    /*
     * 扫描过程中的回调
     */
    typedef void    (*MapCallBack_OnScanChanged)(void *context, MapVector2i left_top, MapVector2i right_bottom);
    
    typedef void    (*MapCallback_DrawText)(TMBitmap *textBitmap, unsigned short *text, int count, int fontSize, bool bold, float density, GlyphMetrics metrics, void *context);
    
    typedef GlyphMetrics  (*MapCallback_CalcTextSize)(unsigned short *text, int count, int fontSize, bool bold, void *context);

    typedef TMBitmap* (*MapCallback_LoadGlyph)(unsigned short text, int fontSize, bool bold, MapRectF* rect, void *context);

    /**
     在sdk层解压缩图片
     @param data 数据内容
     @param data_size 数据的大小
     @param source_type  压缩数据的类型 jpg=0；png=1；webp=2;
     @param target_type 解压后bitmap的格式 rgba=0；rgb565=1；
     @param context  回调的context
     */
    typedef TMBitmap* (*MapCallback_DecodeBitmap)(unsigned char* data, int data_size, int source_type, int target_type, void *context);
    
    typedef void    (*MapCallback_BlockRoute)(char urlStr[512], void *context);

    /*
     渲染的回调类型
     */
    typedef enum _MapDrawCallBackType
    {
        MapMapDrawCallBackType_UnderMainMap       = 2,
        MapMapDrawCallBackType_Under3DBuiding             = 4,
        MapMapDrawCallBackType_UnderHandDraw  = 6,
        MapMapDrawCallBackType_UnderPoi  = 8,
        MapMapDrawCallBackType_UnderToplayer = 10,
        MapMapDrawCallBackType_AboveToplayer        = 12,
        MapMapDrawCallBackType_Num = 6
    } MapDrawCallBackType;

    /**
     请求下载tile的回调
     
     @param urlString tile url
     @param context context
     */
    typedef void    (*MapCallback_Download)(const char *urlString, MapTileID tileID, void *context);
    
    /**
     请求载入纹理的回调
     
     @param fileName 纹理名称
     @param code 是不是绝对路径
     @param anchorPointX1 锚点 x
     @param anchorPointY1 锚点 y
     @param context context
     */
    typedef TMBitmap* (*MapCallback_LoadImage)(const char *fileName, int code, float *anchorPointX1, float *anchorPointY1, void *context);


    /**
     * 通过上层SDK回调读取文件。
     * @param file_name 文件名
     * @param data_size 文件读取成功，文件buffer的大小
     *
     * @return 文件buffer，需要释放。
     */
    typedef void* (*MapCallback_ReadFile)(const char *file_name, int *data_size, void *context);
    
    /**
     将数据写入本地保存的回调，与MapCallback_WriteTile功能相似
     
     @param fileName 纹理名称，通吃是带完整路径的文件名
     @param data 数据
     @param dataSize 数据大小
     @param context context
     */
    typedef void    (*MapCallback_WriteFile)(const char *fileName, const void *data, int dataSize, void *context);
    
    typedef void*   (*MapCallback_GetRenderContext)(void* context);
    
    /**
     将数据写入本地保存的回调，与MapCallback_WriteTile功能相似
     
     @param projectMat 地图投影矩阵
     @param viewMatrix 地图视图矩阵
     @param width 屏幕像素宽度
     @param height 屏幕像素高度
     @param ppd perpixledistance 当前底图一像素代表墨卡托坐标的地理距离
     @param context 用户调用MapSetRenderedFrameCallback时设置进去的自定义context
     @return 是否发生了绘制，发生返回true，否则返回false
     */
    typedef bool   (*MapCallback_RenderedFrame)(MapDrawCallBackType renderLayerType,const float *projectMat,const double *viewMatrix,int width,int height,float ppd,void *context);
    
    /**
     resource 路径
     */
    typedef bool    (*MapCallback_GetFilePath)(const char *fileName, char *filePath, int pathSize);
    
    typedef void    (*GetCitySecondPath_FP)(const char* cityName, char* locPath, int locPathSize);
 
    typedef void (*MapCallBack_MapStyleChanged)(int styleId, void* context);//样式改变接口
 


    typedef void (*MapCallBack_FpsChanged)(int fps, void* context);

    typedef  void (*MapCallBack_MapSwitchOptionChange)(TXMapEventType mapEvent,void *switchOption);

    typedef float (*MapCallBack_AutoSkewControl)(float scale_f, void* context);

    /**
     * 从assets拷贝文件到指定路径（copy_or_unzip为true）或者解压指定路径file_fullpath的文件（copy_or_unzip为false）
     * @param file_fullpath 内部存储路径
     * @param copy_or_unzip  true拷贝，false解压
     * @param context 回调context
     * @return 成功返回true, 失败返回false
     */
    typedef bool (*MapCopyAssetsUnzipCallback)(const char* file_fullpath, bool copy_or_unzip, void *context);

    /**
     * 从assets读取文件
     * @param str: 读取文件名
     * @param result: 读取的数据
     * @param resultLen: 读取数据的长度
     */
    typedef bool (*MapReadAssetCallback)(const char* str, unsigned char **result, int* resultLen);

    #pragma mark 绘制相关接口

    typedef enum _MapRenderSystemType
    {
      MapRenderSystemType_OpenGL = 0,//支持ios,android平台
      MapRenderSystemType_Metal = 1, //支持ios平台
      MapRenderSystemType_Vulkan = 2,//支持android平台
      MapRenderSystemType_Mock = 3, //单元测试用，支持macos/linux平台
      MapRenderSystemType_Empty = 4, //空渲染系统，用于不渲染的场景，支持macos/linux平台
    } MapRenderSystemType;
    /**
     创建地图
     @param type 渲染系统类型
     @param density 像素密度
     @param tileSize tile 大小
     @param textScale 文字比例
     @param supportShareContext 同一个sharegroup 的EAGLContext 对象.
     @param dataDir 数据目录
     @param configDir 配置目录
     @param sateDir 卫星图目录
     @param demDir 地形图目录
     @param offlineDir 离线地图目录
     @param high 是否支持高清
     @param mapTileLoadMode 加载模式
     @return pWorld指针
     */
    UNITY_EXPORT void* MapCreate(MapRenderSystemType type, float density, int tileSize, float textScale, bool supportShareContext,
                    const char *dataDir, const char *configDir, const char *sateDir, const char *demDir,
                    const char *offlineDir, bool highQuality,int mapTileLoadMode);

    typedef struct _WorldOption
    {
        MapRenderSystemType render_type;//渲染系统类型
        float density;//像素密度
        int tile_size;//tile 大小
        float text_scale;//文字比例
        bool support_share_context;//同一个sharegroup 的EAGLContext 对象.
        int android_api_level;//android操作系统的API版本
        bool high_quality;//是否支持高清
        int map_tile_load_mode;//加载模式
		bool support_world_map_for_country;
		bool support_world_map_for_city;
        unsigned int sdk_cost_ms;//MapView创建到执行到MapCreate时sdk层的耗时,用于上报和耗时统计
        char map_view_name[MapPathMaxLength];//底图实例名称
        char data_dir[MapPathMaxLength];//数据目录
        char config_dir[MapPathMaxLength];//配置目录
        char sate_dir[MapPathMaxLength];//卫星图目录
        char dem_dir[MapPathMaxLength];//地形图目录
        MapReadAssetCallback read_asset_callback;//读取包内文件的回调
        MapCallback_StyleParse parse_style_callback;//解析样式结果的回调
    } WorldOption;

    /**
     创建地图
     @param options 创建World参数
     @return pWorld指针
     */
    void* MapCreateWithOption(const WorldOption* options);

    /**
     设置poi点选热点的扩大dp数目, 对所有实例有效, 需要在创建引擎前设置
     @param icon_margin_dp poi 点选icon扩大每个方向的dp值
     @param text_margin_dp poi 点选text扩大每个方向的dp值
     */
    void SetPoiEnlargeMarginInDP(float icon_margin_dp, float text_margin_dp);

    /**
     调试功能,打开/关闭poi碰撞、点选框
     @param b_show 是否显示poi的点选范围框
     */
    void ShowPoiDebugRect(bool b_show);

    /**
     * 引擎启动配置
    * @param pWorld pWorld
    * @param engineCfg 配置选项（需解析成json）
    */
    void MapEngineCfgInit(void* pWorld, const char* engineCfg);

    /**
     销毁地图
     Note: Need OpenGL context
     @param pWorld 通过MapCreate返回的pWorld指针
     */
    UNITY_EXPORT void MapDestroy(void *pWorld);

    void EnableLog(void *pWorld, bool b_log);
    
    /**
     地图回前台
     @param pWorld 通过MapCreate返回的pWorld指针
     */
    void MapResume(void *pWorld);

    /**
     地图回前台前恢复渲染线程消息队列
     @param pWorld 通过MapCreate返回的pWorld指针
     */
    void MapResumeRenderMsgQueue(void *pWorld);
    
    /**
     地图退后台
     @param pWorld 通过MapCreate返回的pWorld指针
     */
    void MapPause(void *pWorld);
    
    /**
    设置卫星图数据服务的完整URL，含瓦片ID占位符(z=%d&x=%d&y=%d)
    引擎内部有默认卫星图服务域名； 若外部有需求切换卫星图服务地址请用该接口；
    @param pWorld 通过MapCreate返回的pWorld指针
    @param pServerFullUrl 卫星图y完整URL
    */
    void MapSetSatelliteServerFullUrl(void *pWorld, const char *pServerFullUrl);

    /**
     设置卫星图版本号
     @param pWorld 通过MapCreate返回的pWorld指针
     @param version 卫星图版本号
     */
    void MapSetSatelliteServerVersion(void *pWorld, int version);

    /**
     设置地形图图版本号
     @param pWorld 通过MapCreate返回的pWorld指针
     @param version 地形图版本号
     */
    void MapSetDemServerVersion(void *pWorld, int version);

    /**
     设置底图数据域名
     
     @param pWorld pWorld
     @param pServerHost url
     */
    void MapSetServerHost(void *pWorld, const char *pServerHost);

    /**
     设置语言文字
     @param pWorld pworld
     @param type   语言类型
     */
    void MapSetLanguageType(void *pWorld, MapLanguageType type);

    /**
     *获取设置的语言类型
     *@param pWorld pworld
     *@return 设置的语言类型MapLanguageType
     */
    int MapGetLanguageType(void *pWorld);
    
    /**
     根据不同的type设置tag.比如
     MapSetServerUrlTag(pWorld, MapUrlTagType_BaseMap, "mvd_map")
     MapSetServerUrlTag(pWorld, MapUrlTagType_Landmark, "landmark")
     
     @param pWorld pWorld
     @param type 指定的type
     @param pTag tag
     */
    void MapSetServerUrlTag(void *pWorld, MapUrlTagType type, const char *pTag);

    /**
     *直接设置服务地址
     */
    void MapSetServerUrlDirectly(void *pWorld, int type, const char *url);
    
    // Needs OpenGL context
    
    /**
     渲染一帧
     Note: Need OpenGL context
     @param pWorld 场景具柄
     */
    UNITY_EXPORT void MapDrawFrame(void *pWorld);

    /**
     * 打印底图一帧耗时细节
     */
    void LogDrawFrameDetail(void* pWorld, int total_time, const char* sdk_details);
    
    // duration is diff of timestamp, (timestamp - lastTimestamp)
    
    /**
     更新一帧，主要做动画
     Note:可以更改为当前时间，时间差在引擎内部计算
     @param pWorld 场景句柄
     @param duration 两帧之间时间差
     */
    UNITY_EXPORT void MapUpdate(void *pWorld, double duration);
    

    /**
     * 默认没有淡入淡出功能，此接口可开启淡入淡出功能
     * 设置SD与HD切换淡入淡出动画时间, fadingInTime和fadingOutTime有一个小于等于0，则无淡入淡出功能
     * @param pWorld
     * @param fadingInTime : 淡入时间,单位秒,不超过5秒
     * @param fadingOutTime : 淡出时间,单位秒,不超过5秒
     */
    void MapSetFadingTime(void *pWorld, double fadingInTime, double fadingOutTime);

    /**
     设置地图回调
     
     @param pWorld pWorld
     @param pCallbackContext 传递的context参数，一般为mapview地址
     @param callback_LoadImage 载入纹理图片的回调
     @param callback_Download 请求下载瓦片的回调
     @param callback_CalcTextSize 计算text size的属性
     @param callback_DrawText draw text回调
     @param callback_WriteFile 保存数据到本地的回调
     @param callback_LoadGlyph load glyph 回调
     */
    void MapSetCallbacks(void*                      pWorld,
                         void*                      pCallbackContext,
                         MapCallback_LoadImage      callback_LoadImage,
                         MapCallback_Download       callback_Download,
                         MapCallback_Download       callback_CancelDownload,
                         MapCallback_CalcTextSize   callback_CalcTextSize,
                         MapCallback_DrawText       callback_DrawText,
                         MapCallback_ReadFile       callback_ReadFile,
                         MapCallback_WriteFile      callback_WriteFile,
                         MapCallback_LoadGlyph      callback_LoadGlyph);

    void MapSetDecodeImageCallback(void* p_world, MapCallback_DecodeBitmap callback_decode_bitmap);
    /**
     * 设置自动俯仰角回调
     * @param pWorld 底图实例句柄
     * @param callback_auto_skew 自动俯仰角回调
     * @param context 回调上下文（对应底图实例）
     */
    void MapSetAutoSkewCallback(void* pWorld, MapCallBack_AutoSkewControl callback_auto_skew, void* context);

    void MapAddResourceTag(void* pWorld, void* context, const char* tag);
    void MapRemoveResourceTag(void* pWorld, void* context, const char* tag);


    void MapSetRenderedFrameCallback(void*  pWorld, MapCallback_RenderedFrame  callback_RenderedFrame ,void *pCallbackObject);

    struct DownloadData;
    //add for linux , vinsentli
    typedef struct DownloadData* (*onDownloadData)(const char *urlString);
    typedef void (*onFreeDownloadData)(struct DownloadData* pData);

    void MapSetDownloadCallback(void * pWorld, onDownloadData callback_Download, onFreeDownloadData callback_Free);
    
    void MapSetCityPathCallback(void *pWorld,GetCitySecondPath_FP func);

    void MapSetCallback_MapEvent(void *pWorld, MapCallBack_MapEvent pCallbackFunc, void *pCallbackObject);
    void MapAddCallback_MapEvent_CPP(void *pWorld, MapCallBack_MapEvent pCallbackFunc, void *pCallbackObject);
    void MapRemoveCallback_MapEvent_CPP(void *pWorld, MapCallBack_MapEvent pCallbackFunc, void *pCallbackObject);

    void GLMapAddImageLoadCallback_CPP(void *pWorld, MapCallback_LoadImage pCallbackFunc, void *pCallbackObject);
    void GLMapRemoveImageLoadCallback_CPP(void *pWorld, MapCallback_LoadImage pCallbackFunc, void *pCallbackObject);

    /**
     * 引擎销毁通知Map的全局锁
     * 通过全局锁 解决 ab-ba 死锁问题
     * 渲染线程：引擎销毁 锁引擎 world lock， 通知Map时需要锁 map lock
     * 其他线程：Map销毁时 锁 map lock， 移除引擎回调 锁引擎 world lock
     */
    void GLMapEngineDestroyLock(void);
    void GLMapEngineDestroyUnLock(void);

    /**
     设置获取EAGLContext/MTKView 对象的回调
     @param pWorld pWorld
     @param callback_getContext 回调方法
     */
    void MapSetCallback_GetRenderContext(void *pWorld, MapCallback_GetRenderContext callback_getContext);
    
    /**
     设置获取resource 路径的回调
     
     @param pWorld pWorld
     @param callback_getFilePath 回调方法
     */
    void MapSetCallback_GetFilePath(void *pWorld, MapCallback_GetFilePath callback_getFilePath);
    
    
    void MapSetCallback_BlockRoute(void *pWorld, MapCallback_BlockRoute callback_BlockRoute, void *pCallbackContext);
    
    /**
     地图载入resource(比如底图瓦片)过程是否完成
     
     @param pWorld pWorld
     @return 完成YES
     */
    UNITY_EXPORT bool MapLoadResources(void *pWorld);
    
    /**
     判断地图资源是否加载完成，可以显示完整画面（延迟加载机制）
     
     @param pWorld 场景句柄
     @return 是/否
     */
    bool MapIsLoadingFinished(void* pWorld);
    
    /**
     更新底图配置资源(底图配置/图片等)
     @param pWorld 场景句柄
     */
    void GLMapUpdateMapResource(void* pWorld);

    void GLMapNeedUnCompressCfg(void* pWorld, bool needUncompress);
    // 1. Needs OpenGL context
    // 2. Task thread should be stopped before hibernate.
    
    /**
     休眠通知
     地图会在该调用内部销毁部分资源
     @param pWorld 场景句柄
     */
    void MapHibernate(void *pWorld);
    
    /**
     内存警告
     降低cache阈值，并未立刻释放资源
     @param pWorld 场景句柄
     */
    void MapMemoryWarning(void *pWorld);
    
    /**
     reload 所有texture
     
     @param pWorld pWorld
     */
    void MapTextureReloadAll(void *pWorld);
    
    /**
     重新reload 指定的texture
     
     @param pWorld pWorld
     @param imageName 需要reload的纹理名称
     */
    void MapTextureReload(void *pWorld, const char *imageName);
    
    /**
     清理下载请求，触发下载失败的url重新download
     Thread safe, after GLMapClearDownloaderFinishedItems, failed url will retry download.
     @param pWorld pWorld
     */
    void GLMapClearDownloaderFinishedItems(void *pWorld);
    
    
    /************************************************************************/
    /*                            路况相关接口                                */
    /************************************************************************/
    #pragma mark 路况相关接口
/**
     开启路况
     
     @param pWorld pWorld
     @param enabled 是否开启路况
     */
    void GLMapSetTrafficEnabled(void *pWorld, bool enabled);

    typedef struct _TrafficStyle{
        TMColor fillColor;
        TMColor borderColor;
        int     fillWidth;
        int     borderWidth;
    }TrafficStyle;
#ifdef UNITY
    typedef struct _BuildingUnityInfo{//建筑信息，传给Unity用
        float    height;//楼高度
        float    ceilHeight;
        int      ceilNumber;
        int      texWidth;//图片宽度
        int      texHeight;//图片高度
        float    meterToMercatorRatio;//米转化成墨卡托尺寸
        bool    buildingIsHeightMetric;
        MapVector3f center;
    }BuildingUnityInfo;

    typedef struct _BuildingExtraInfo { // 建筑的额外信息，原unity端PB文件信息(https://tapd.woa.com/10130181/prong/stories/view/1010130181876050161)
        float root_color_start; // 地面颜色（侧面底部的渐变色）的起点位置百分比
        float root_color_end; // 地面颜色（侧面底部的渐变色）的终点位置百分比
        int wall_texture_type; // 0：窗格平铺+拉伸贴法, 1：窗格拉伸贴法, 2：窗格平铺贴法
        char wall_texture_normal[64]; // 建筑窗格法线纹理
        char wall_texture_lightmark[64]; // 建筑窗格发光标记纹理
        char wall_texture[64];
        float wall_texture_roofmargin; // 建筑顶部贴图留白距离
        float floor_height; // 层高
        MapVector4ub wall_color;
        MapVector4ub root_color;
        MapVector4ub roof_color;

    }BuildingExtraInfo;
#endif
    /**
     * 自定义设置路况样式。
     *
     * @param smooth                        畅通路况样式
     * @param slow                            缓行路况样式
     * @param congested                 拥堵路况样式
     * @param seriousCongested 严重拥堵路况样式
     */
    void GLMapSetTrafficStyle(void *pWorld, TrafficStyle* smooth, TrafficStyle* slow, TrafficStyle* congested, TrafficStyle* seriousCongested);

    void GLMapSetTrafficDataTypeVersion(void *pWorld,  int version, int start_scale_level);

    /**
     * 设置自定义底图手势中心（主要针对缩放和旋转，目前缩放和旋转共用一个缩放中心）
     * @param pWorld  底图实例句柄
     * @param gesture_center  缩放中心（相对左上角的屏幕坐标）
     */
    void MapSetGestureCenter(void *pWorld, MapVector2f gesture_center);

    /**
     设置缩放比例

     @param pWorld 场景句柄
     @param scale 缩放比例
     @param animated 动画渐变
     */
    void  GLMapSetScaleWithAutoSkew(void *pWorld, double scale, bool animated, float duration, bool auto_skew);
    
    /************************************************************************/
    /*                             封路相关接口                               */
    /************************************************************************/
    #pragma mark 封路相关接口
    
    void GLShowBlockIcon(void *pWorld,bool needShow);
    
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    void GLMapHideBlockRoute(void *pWorld, bool hide);
    
    void GLMapGetBlockRouteInfo(void *pWorld, int itemId, unsigned int *routeid, unsigned int *iconid);
    
    void GLMapWriteBlockRouteData(const char *urlString, const void* data, int iDataLen);

    int GLMapGetBlockRouteIconPriority(void *pWorld);

    void GLMapSetBlockRouteIconPriority(void *pWorld, int priority);


    /************************************************************************/
    /*                             针对电子眼提供的无锁接口                               */
    /************************************************************************/
    #pragma mark 针对电子眼提供的无锁接口
    void MapMarkerManagerLock(void *pWorld);
    void MapMarkerManagerUnLock(void *pWorld);
    void MapMarkerSetAvoidDetailedRule_Lock(void *pWorld, int markerID, MapMarkerAvoidDetailedRule* pRule, bool bLock);
    void MapMarkerSetPriority_Lock(void *pWorld, int markerID, int priority, bool bLock);
    void MapMarkerModifyScale_Lock(void *pWorld, int markerID, MapVector2f scale, bool bLock);
    //bool MapMarkerSetAllowAvoidOtherMarker_Lock(void *pWorld, int markerID, bool isAllowAvoidOtherMarker, bool bLock);
    void MapMarkerSetHidden_Lock(void *pWorld, int *markerIDs, int size, bool hidden, bool bLock);
    bool MapMarkerSetAvoidRouteRule_Lock(void *pWorld, int markerID, MapMarkerAvoidRouteRule * rule, bool bLock);
    
    
    /************************************************************************/
    /*                           手绘图相关接口                               */
    /************************************************************************/
    #pragma mark 手绘图相关接口
    
    /**
     手绘图信息
     */
    typedef struct _HandDrawingConfigItem
    {
        char* sceneId;
        char* sceneName;
        char* cityName;
        int  cityCode;
        int  version;
        MapRectD rect;
        int displayLevel;
    }HandDrawingConfigItem;
    
    //手绘图：读取配置
    bool GLMapReadHandDrawingConfigureFile(const char* cfgFile);
    
    //手绘图：查询
    //queryItems需要调用GLMapReleaseHandDrawing释放掉
    bool GLMapQueryHandDrawing(MapRectD rect, int level, HandDrawingConfigItem** queryItems, int *itemcount);
    
    //手绘图：释放内存
    void GLMapReleaseHandDrawing(HandDrawingConfigItem * items, int count);
    
    /************************************************************************/
    /*                            建议废弃的函数                              */
    /************************************************************************/
    #pragma mark 建议废弃的函数
    
    /**
     建议废弃的函数，原因：可以分别通过组合接口调用实现
     
     @param pWorld XXX
     @param mapPoint XXX
     @param scaleLevel XXX
     @param animated XXX
     @param didStopCallback XXX
     @param pCallBackObject XXX
     */
    void    GLMapSetCenterMapPointAndScaleLevel(void *pWorld, MapVector2d mapPoint, int scaleLevel, bool animated, MapAnimationDidStopCallback didStopCallback, void *pCallBackObject);
    
    /**
     重置skew，rotate为0
     建议废弃的函数，可通过其他接口组合实现
     
     @param pWorld XXX
     @param animated XXX
     */
    void GLMapStartResetAnimation(void *pWorld, bool animated);
    
    /************************************************************************/
    /*                           FOR DEBUG ONLY                             */
    /************************************************************************/
    #pragma mark FOR DEBUG ONLY
    
    /**
     * 为了更深入测试引擎内部逻辑而开发的接口，仅供测试用，不对外开放。
     */
    
    void MapRouteCreateWithPrivateArrow(void * world, MapRouteInfo *routeInfo);
    
    bool MapRouteCreateRGBAColorLineWithPrivateArrow(void*world,
                                                     MapRouteInfo * routeInfo,
                                                     RGBAColorLineExtraParam * extramParam);
    
    void MapRouteSetPrivateTurnArrowIndex(void *pWorld, int routeID, int segmentIndex);
    
    //这个接口不要调用，引擎目前调参用，不久将删除
    void MapSetMapStyleWithAnimationEx(void *pWorld, int mapStyleID, bool reuseOnSwitch,
                                       bool isAnimation, double animationDuration, MapAnimationCurveType animType);
    
    /**
     * @param newPoints , 保存坐标，需要外部手动释放
     * @param extraInfo ,    保存x=方向，y=index(当前点在原始route上的index)，需要外部手动释放
     */
    void MapInterpolationForLongSegment(MapVector2d * srcPoints, int srcPointCount,
                                        MapVector2d ** newPoints, MapVector2d** extraInfo, int *newPointCount, double step);
    
    bool checkPointCoordinateValid(MapVector2d *points, int pointsCount, const char * funcName);
    bool checkPoint3dCoordinateValid(MapVector3d *points, int pointsCount, const char * funcName);    
    
    MapVector2d MapVector2dForLngLatCoordinate(double lon, double lat);

    // 添加扫描动画
    bool SetScanAnimation(void *pWorld, const MapRoadScanOptions *map_scan_options);
    void StopScanAnimation(void *pWorld);

    void MapSetCallback_ScanStatus(void *pWorld, MapCallBack_OnScanFinished on_scan_finished, MapCallBack_OnScanChanged on_scan_changed, void *pCallbackObject);

    /**
     * 地图内部支持的统计任务类型
     */
    typedef MAP_ENUM(int, MapStatisticsType)
    {
        MapStatisticsType_RouteName               = 0,    // 动态路名相关统计
        MapStatisticsType_Num
    };

    /**
     * 启动指定的统计任务
     *
     * @param pWorld        场景句柄
     * @param taskType      任务类型
     * @return              是否成功开始统计
     */
    bool MapStartStatistics(void *pWorld, MapStatisticsType taskType);

    /**
     * 停止指定的统计任务
     *
     * @param pWorld            场景句柄
     * @param taskType          任务类型
     * @param outputBuffer      存储输出结果的缓存区
     * @param outputBufferLen   存储输出结果缓存区的大小
     * @return                  是否有输出结果
     */
    bool MapStopStatistics(void *pWorld, MapStatisticsType taskType, void* outputBuffer, int outputBufferLen);
    
    /*
     * 获取屏幕密度。
     * 就是MapCreate时，外部传入的density。
     */

    float MapGetScreenDensity(void* pWorld);
  /*
   * icon                     增量更新文件下载后的处理。
   * operate:                 要进行的操作，合并写"merge" 完整性检验检验为“crc”，png图片生成为“unpack”
   * oldFilePath:             合并：旧的全量包地址        完整性检验：待检测文件  png图片生成：icon全量包
   * incrFilePath：           合并：新下载的文件          完整性检验：无         png图片生成：无
   * newFilePath：            合并：合并后的文件名         完整性检验：无        png图片生成：无
   * resourcePath：                                                       png图片生成：图片生成的位置
   * cfgPath:                                                             png图片生成：大图配置文件的位置
   * 合并后会将增量文件与旧文件直接删除
   */
    bool MapIconIncremntal(char const* operate, char* oldFilePath, char* incrFilePath, char* newFilePath, char* resourcePath, char* cfgPath);

    float MapGetScreenDensity(void * pWorld);

    /*
    *  开启渲染耗时log打印 For Android
    */
    void MapEnableDrawLog(void *pWorld, bool enableLog);

    /**
     设置屏幕中心点偏移, cpp api专用，不触发回调
     offset = (offsetX / screenWidth, offsetY / screenHeight)
     @param pWorld 场景句柄
     @param offset 中心点下移占屏幕比例(0.0f, 0.0f)无下移;(0.2f, 0.0f)屏幕下侧，距离底部(0.5 - 0.2) = 3/10处.
     @param duration_in_s 动画时长, 单位s
     @param begin_from_current 动画起始值是否是当前状态 (新的动画开始的时候,如果上次动画未完成, true:新动画从当前值开始, false:新动画从上次动画的目标结束值开始)
     */
    void MapSetScreenCenterOffsetEx(void *pWorld, MapVector2f offset, float duration_in_s, bool begin_from_current);


    /**
     * 获取屏幕内地理区域, origin 为坐下角 引擎内坐标
    */
    MapRectD MapGetSight(void* pWorld, MapVector2d vertices[4]);

    /*
     * 获取天空盒高度屏幕像素比例
     */
    float MapGetSkyRatioOnScreen(void* pWorld);

    /**
     * 打印渲染引擎帧号开关
     * @param enable true开启,false关闭
     * @param pos 帧号显示的像素位置
     * @param alpha 帧号透明度
     * @param color 帧号颜色
     * @param fontSize 帧号字体大小,单位dp
     */
    void MapEnableFrameDebug(void* pWorld, bool enable, MapVector2i pos, float alpha, MapColor4ub color, int fontSize);

    /**
     * 设置底图瓦片加载最大个数
     * @param pWorld
     * @param tile_type 瓦片类型，底图、建筑
     * @param max_tile_count 最大瓦片个数
     */
    bool MapSetMaxTileCount(void *pWorld, TXMapTileType tile_type, int max_tile_count);
#ifdef UNITY
    void* GLMapGetUnityQuailtySetting(void*pWorld);
    void* GLMapGetUnityLightStyleSetting(void* pWorld);
    void* MapGetBuildingExtraParam(void* pWorld, int style_id);
#endif

    void ExceuteLocalConfigCheck(const char* app_key, const char* cfg_path, const char *res_path, const char* version_json, MapCopyAssetsUnzipCallback callback, MapReadAssetCallback readCallback, void *context);

    void ResetAllConfigVersion(MapCopyAssetsUnzipCallback callback, MapReadAssetCallback readCallback, void *context);

    void SetCSFileUpdateReqID(const char* sID);
    void ExceuteRemoteConfigCheck(int million_sec, const char *url);
    void MapGetBasemapConfigTag(char *tag, int tag_buffer_len, const char *json);
    void MapGetStylizationConfigTag(char *tag, int tag_buffer_len, const char *json);

    void DestroyParticleManager(void* pWorld);
    void CreateOpenGLParticleManager(void *pWorld);

    /**
     * 同步执行三维点选检测，iOS同步调用
     */
    bool MapExecute3DClickTest(void *pWorld, MapVector2f screenCoordinate, bool is_single_click, bool run_directly);

    int MapGetConfigVersion(const char* name);

    /**
     * 初始化日志. plog
     * @param log_path 日志存放的文件夹路径，必须是已经存在的目录
     * @param log_level 日志级别，见plog::LogLevel
     * @param log_dest 日志输出&记录方式，见plog::LogDest
     */
    void MapInitLogger(const char* logPath, int logLevel, int logDest);

    bool MapTestLogger(int logLevel);

    /**
     * 开启引擎性能分析日志(会无视日志级别进行输出)
     */
    void MapEnablePerformanceLog(bool enable);

    /**
     * 获取plog初始化后返回的InstanceId. 如果没有初始化，则返回uint32 max值
     */
    unsigned int MapGetLoggerInstanceId(void);

    void MapSetHandDrawEnabled(void *pWorld, bool enable);
     
    bool MapGetHandDrawEnabled(void *pWorld);

    bool MapHasHanddrawInScreen(void *pWorld);

    void MapSurfaceCreated(void *pWorld, void *window);

    void MapReportSDKEvent(const char* event_type, const char* details);
    
#ifdef __cplusplus
}
#endif


#endif /* GLMapLib2_0_Private_h */
