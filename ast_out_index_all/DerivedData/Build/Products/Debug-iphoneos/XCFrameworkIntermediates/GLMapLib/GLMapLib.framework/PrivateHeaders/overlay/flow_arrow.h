//
//  flow_arrow.h
//  GLMapLib2.0
//
//  Created by TencentMap on 2020/5/6.
//  Copyright © 2020 TencentMap. All rights reserved.
//

#ifndef __MAPAPI_OVERLAY_FLOW_ARROW_H__
#define __MAPAPI_OVERLAY_FLOW_ARROW_H__

#include <stdio.h>
#include <string>
#include "base/map_define.h"

namespace tencentmap{
class PolylineImpl;
}

namespace MAPAPI {

/**
 * 路线上的流向小箭头
 */
class TXMAPSDK_EXPORT FlowArrow{
public:
    FlowArrow();
    virtual ~FlowArrow();

    /**
     * 设置流向箭头纹理名称
     *
     * @param textureName 纹理名称
     */
    void SetTexture(const std::string & textureName);
    
    /**
     * 设置是否显示流向小箭头
     *
     * @param bDrawArrow 是否绘制
     */
    void SetVisible(bool bDrawArrow);
    
    /**
     * 设置流向箭头间距
     *
     * @param spacing 间距单位 dp
     */
    void SetSpacing(float spacing);
    
public:
    FlowArrow(std::weak_ptr<tencentmap::PolylineImpl> p);
    
private:
    std::weak_ptr<tencentmap::PolylineImpl> parent_;
};

}

#endif /* __MAPAPI_OVERLAY_FLOW_ARROW_H__ */
