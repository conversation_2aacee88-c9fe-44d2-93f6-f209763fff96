//
//  Created by vins<PERSON><PERSON> on 2021/3/29.
//  Copyright © 2021 TencentMap. All rights reserved.
//
#pragma once

#include "overlay/model_3d_options.h"

namespace tencentmap{
class OVLModel3D;
}

namespace MAPAPI {

/**
 * 3D模型overlay， 每个overlay中可以有一个3D对象，也可以有多个3D对象
 */
class TXMAPSDK_EXPORT Model3D : public Overlay{
    friend class OverlayFactory;
public:
    /**
     * 直接更新一个3D对象到Overlay中
     */
    void SetSpirit(const SpiritOptions& spirit);
    
    /**
     * 直接更新多个3D对象到Overlay中
     */
    void SetSpirits(const std::vector<SpiritOptions>& spirits);
    
    /**
     * 设置数据更新时间间隔，即调用SetSpirit()或SetSpirits()接口的频率。
     * 默认为1秒。
     * 此接口仅在SpiritOptions中包含spiritID，内部会执行插值动画的情况下才有意义。
     *
     * 车道级导航场景下，此值应该与Locator::SetUpdateDuration()保持一致。
     */
    void SetUpdateDuration(double duration);
 

    /**
     * 设置位置，在渲染线程调用时直接生效，coord个数必须和spirit个数一致
     */
    void SetCoordinateSync(const std::vector<MapVector3d>& coord);
    
    /**
     * 设置角度，在渲染线程调用时直接生效，angle个数必须和spirit个数一致
     */
    void SetAngleSync(const std::vector<float>& angle);
    

    /**
     * 设置动作名称repeat_action，执行repeat_time次，然后进入停止状态（重复执行stop_action）,repeat_time = -1时无限循环，执行完毕后执行stop_cbk。repeat_action为空或者非法时直接进入stop_action的状态
     */
    void ExecuteSkeletonAction(const std::string& repeat_action, int repeat_time, const std::string& stop_action, std::function<void(const std::string& modelname, const std::string& action_name)> stop_cbk);
    
    /**
     * 动画是否在当前帧暂停，true进入暂停，false恢复运行
     */
    void SetPauseSkeletonAnim(bool pause);
    
    /**
     * 每帧的回调，返回当前模型的名称、地理位置（经纬度+米），模型的高度（米）
     */
    void SetDrawCallBack(std::function<void(const std::string& model_name, const MapVector3d& position, float model_height)> draw_cbk);
    
    /**
     * 模型被遮挡时的半透的比例[0-1]，ratio = 1时完全显示模型，ratio = 0时不显示被遮挡部分，默认是完全透出的效果
     */
    void SetMixRatio(float ratio);
    /*
    * 设置摄像机跟随模型的位置，bFollow为true时,相机会跟随模型的位置.默认为false
    */
    void SetFollow(bool bFollow);
    
    /*
    * 设置是否使用Lod裁剪,及回调
    * lod_type  < 0：不使用；0：使用默认的lod参数4；其他（> 0）：使用指定的lod策略
    * lod_change_cbk 所在的lod范围有变化时回调，true由远端变到近端，false近端变到远端
    */
    void SetLodShowAndCallback(int lod_type, std::function<void(const std::string& model_name,bool far_to_near)> lod_change_cbk);
    
    /*
    * 设置模型顶部被楼块或者Landmark遮挡的回调
    * model_name 模型名称
    * to_occluded true：由未遮挡状态切换到遮挡状态 false：由遮挡状态切换到未遮挡状态
    */
    void SetOcclusionCallback(std::function<void(const std::string& model_name, bool to_occluded)> occlusion_cbk);

    void SetAboveLocator(bool above_locator_render);
private:
    using Overlay::Overlay;
    std::shared_ptr<tencentmap::OVLModel3D> GetImpl();
};

}

