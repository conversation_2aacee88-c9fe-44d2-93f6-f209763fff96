//
//  QMFloatLayerTableCellDefine.h
//  SOSOMap
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/6/8.
//  Copyright © 2019 Tencent. All rights reserved.
//

#ifndef QMFloatLayerTableCellDefine_h
#define QMFloatLayerTableCellDefine_h

typedef NS_ENUM(NSInteger, QMFloatLayerButtonStyleType) {
    QMFloatLayerButtonStyleTypeRectangle,
    QMFloatLayerButtonStyleTypeCircle
};

typedef NS_ENUM(NSInteger, QMFloatLayerCellTargetType) {
    QMFloatLayerCellTargetTypeTwoD,
    QMFloatLayerCellTargetTypeThreeD,
    QMFloatLayerCellTargetTypeSatelite,
    QMFloatLayerCellTargetTypeStreet,
    QMFloatLayerCellTargetTypeFavorite,
    QMFloatLayerCellTargetTypeHandPainting,
    QMFloatLayerCellTargetTypeRealTimeBus,
    QMFloatLayerCellTargetTypeThemeMap = 100,
};

@protocol QMFloatLayerCollectionViewCellDelegate <NSObject>

- (void)collectionViewCell:(UICollectionViewCell *)collectionView didClickedCellForTargetType:(QMFloatLayerCellTargetType)targetType;
- (BOOL)isCollectionViewCellSelected:(UICollectionViewCell *)collectionView cellForTargetType:(QMFloatLayerCellTargetType)targetType;

@end
#endif /* QMFloatLayerTableCellDefine_h */
