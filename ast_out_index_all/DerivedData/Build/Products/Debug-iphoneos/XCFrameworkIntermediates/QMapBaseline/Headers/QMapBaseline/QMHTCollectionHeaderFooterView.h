//
//  QMHTCollectionHeaderFooterView.h
//  SOSOMap
//
//  Created by wyh on 2020/12/25.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

UIKIT_EXTERN NSString * const QMHTCollectionRecentHeaderViewReuseIdentifier;
UIKIT_EXTERN NSString * const QMHTCollectionNormalHeaderViewReuseIdentifier;
UIKIT_EXTERN NSString * const QMHTCollectionFirstNormalHeaderViewReuseIdentifier;
UIKIT_EXTERN NSString * const QMHTCollectionFooterViewReuseIdentifier;
UIKIT_EXTERN NSString * const QMHTCollectionRecentFooterViewReuseIdentifier;

UIKIT_EXTERN CGFloat const QMHTCollectionRecentGroupHeaderHeight;
UIKIT_EXTERN CGFloat const QMHTCollectionFirstNormalGroupHeaderHeight;
UIKIT_EXTERN CGFloat const QMHTCollectionNormalGroupHeaderHeight;
UIKIT_EXTERN CGFloat const QMHTCollectionFooterViewHeight;

@interface QMHTCollectionRecentHeaderView : UICollectionReusableView

@end

@interface QMHTCollectionFirstHeaderView : UICollectionReusableView

@property (nonatomic, copy) NSString *groupName;

@end

@interface QMHTCollectionNormalHeaderView : UICollectionReusableView

@property (nonatomic, copy) NSString *groupName;

@end

@interface QMHTCollectionRecentFooterView : UICollectionReusableView

@end

@interface QMHTCollectionNormalFooterView : UICollectionReusableView

@end

NS_ASSUME_NONNULL_END
