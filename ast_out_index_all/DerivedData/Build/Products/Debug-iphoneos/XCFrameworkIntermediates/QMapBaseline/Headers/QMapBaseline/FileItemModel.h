//
//  FileItem.h
//  HealthKitDemo
//
//  Created by <PERSON><PERSON><PERSON> on 2017/9/11.
//  Copyright © 2017年 aiijim. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, FileType) {
    DirectoryType,
    GIFType,
    JPGType,
    JSONType,
    PDFType,
    PLISTType,
    PNGType,
    ZIPType,
    UnknownType,
};

@interface FileItemModel : NSObject
//Display name
@property (nonatomic, strong) NSString* displayName;
//is Directory
@property (nonatomic, assign) BOOL isDirectory;
//file extension
@property (nonatomic, strong) NSString* fileExtension;
//file attributes
@property (nonatomic, strong) NSDictionary* fileAttributes;
//file absolute path
@property (nonatomic, strong) NSString* filePath;
//file size
@property (nonatomic) double fileSize;
//file type
@property (nonatomic, assign) FileType type;
// create time
@property (nonatomic) NSDate *createTime;
// modify time
@property (nonatomic) NSDate *modifyTime;

@end
