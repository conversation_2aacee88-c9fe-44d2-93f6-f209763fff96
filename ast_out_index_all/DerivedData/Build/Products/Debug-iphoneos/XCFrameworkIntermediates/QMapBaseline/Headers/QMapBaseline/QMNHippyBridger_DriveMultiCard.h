//
//  QMNHippyBridger_DriveMultiCard.h
//  QMapBusiness
//
//  Created by wyh on 2022/2/12.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapHippy/QMNHippyBridgerFactory.h>
#import <QMapMiddlePlatform/QMCarRouteNaviCommon.h>

@class QMNHippyBridger_DriveMultiCard;

@protocol QMNHippyBridgerDriveMultiCardPosting <NSObject>

- (void)bridger:(QMNHippyBridger_DriveMultiCard * _Nonnull)bridger cardListHeightWillUpdate:(CGFloat)totalHeight;

- (NSDictionary *_Nonnull)bridgerGetScrollParameters:(QMNHippyBridger_DriveMultiCard * _Nonnull)bridger;

@end

NS_ASSUME_NONNULL_BEGIN

@interface QMNHippyBridger_DriveMultiCard : QMNHippyBaseBridger

@property (nonatomic, strong, readonly) TMHippyViewBox *hippyBox;

// 用routeNaviType区分多方案页的卡片bridge
@property (nonatomic, assign) QMCarRouteNaviType routeNaviType;

@property (nonatomic, assign, readonly) CGFloat cardHeight;

- (void)callbackCardHeightWillUpdate:(CGFloat)cardHeight;

- (NSDictionary *)getScrollParameter;

/// 点击定位按钮通知 hippy
- (void)dispatchToHippyClickLocationButton;

/// 路线方案切换通知 hippy 接口
- (void)dispatchToHippyOnSchemeChanged;

/// 起终点变更通知hippy接口
- (void)dispatchToHippyOnStartEndChanged;

/// 点击刷新后通知hippy接口
- (void)dispatchToHippyOnRouteRefresh;

/// 更新卡片列表高度
/// @param progress 滑动进度
- (void)dispatchToHippyListScrollProgress:(CGFloat)progress;

/// 便于hippy埋点使用，在滑动视图点开时调用
- (void)dispatchToHippyDrawerStateChanged;

/// 便于hippy埋点使用，在hippy父滑动视图滑动停止时调用
- (void)dispatchToHippyDrawerScrollDidDecelerated;

/// 当前 ETA 变化的通知
- (void)dispatchToHippyOnEtaRefresh;

/// 页卡开始上拉事件通知
- (void)dispatchToHippyOnListScrollStart;

@end

NS_ASSUME_NONNULL_END
