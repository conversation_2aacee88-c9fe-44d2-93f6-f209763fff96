//
//  QMZoomContainerView.h
//  QMapBusiness
//
//  Created by 王瑞华 on 2021/8/6.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMMapZoomView.h>
#import <QMapMiddlePlatform/QMNSlideZoomView.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMZoomContainerView : UIView

@property (nonatomic, weak) DMMapView *mapview;
@property (nonatomic, weak) id<MapZoomEventDelegate, QMNSlideZoomViewDelegate> zoomDelegate;
@property (nonatomic, strong, nullable) QMMapZoomView *zoomView;
@property (nonatomic, strong, nullable) QMNSlideZoomView *dSlideZoomView;

@property (nonatomic) CGFloat minShowHeight;//最小显示高度 原来的逻辑高度小于240不显示
@property (nonatomic) BOOL    customUI;//是否需要自定义UI 默认NO

/// 是否符合相应手势 默认NO;
@property (nonatomic) BOOL canMutilTouch;

- (void)updateZoomViewDisplay;

@end

NS_ASSUME_NONNULL_END
