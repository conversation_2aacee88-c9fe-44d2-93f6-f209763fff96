//
//  QMCSTHomeCompanyDataService.h
//  QMapBusiness
//
//  Created by wyh on 2021/8/17.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/QMCSTBaseDataService.h>
#import <QMapMiddlePlatform/QMCommuteDataModel.h>
#import "QMCSTPersonalInfoPO.h"

extern NSString * _Nonnull const QMCSTHomeAndCompanyUpdateEventKey;
extern NSString * _Nonnull const QMCSTHomeEventValue;
extern NSString * _Nonnull const QMCSTCompanyEventValue;

extern NSString * _Nonnull const QMCSTHomeTypeKey;
extern NSString * _Nonnull const QMCSTCompanyTypeKey;

NS_ASSUME_NONNULL_BEGIN

@interface QMCSTHomeDataService : QMCSTBaseDataService <QMCSExtServiceProtocol>

@property (nonatomic, assign) BOOL isQMCSHasData;
@property (nonatomic, strong, nullable) QMPersonalInfo *home;
@property (nonatomic, strong) QMCSTPersonalInfoPO *homePO;

@end

@interface QMCSTCompanyDataService : QMCSTBaseDataService <QMCSExtServiceProtocol>

@property (nonatomic, assign) BOOL isQMCSHasData;
@property (nonatomic, strong, nullable) QMPersonalInfo *company;
@property (nonatomic, strong) QMCSTPersonalInfoPO *companyPO;

@end

/**
 New home & company data service (begin at v9.16.0)
 */
@interface QMCSTHomeCompanyDataService : NSObject

@property (nonatomic, strong, readonly) QMCSTHomeDataService *homeService;
@property (nonatomic, strong, readonly) QMCSTCompanyDataService *companyService;

@property (nonatomic, strong, nullable) QMPersonalInfo *home;
@property (nonatomic, strong, nullable) QMPersonalInfo *company;

+ (instancetype)defaultService;

- (void)sync;

@end

NS_ASSUME_NONNULL_END
