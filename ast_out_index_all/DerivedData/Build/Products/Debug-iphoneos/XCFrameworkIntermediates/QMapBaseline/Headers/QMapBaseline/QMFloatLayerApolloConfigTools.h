//
//  QMFloatLayerApolloConfigTools.h
//  QMapBaseline
//
//  Created by hangjiang on 2023/8/9.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMFloatLayerDefine.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMFloatLayerApolloConfigTools : NSObject

/// 开关在具体场景下是否显示
/// @param displayType 开关
/// @param scene 场景：首页/检索
/// @param mapType 地图类型：标准地图/公交地图/卫星地图
+ (BOOL)mapLayerCanShowWithDisplayType:(QMFloatLayerMapDisplayType)displayType
                                 scene:(QMFloatLayerFromType)scene
                               mapType:(QMFloatLayerMapType)mapType;

/// 开关显示优先级
/// @param displayType 开关
+ (NSInteger)mapLayerIndexForDisplayType:(QMFloatLayerMapDisplayType)displayType;

/// 开关是否已下线
/// @param displayType 开关
+ (BOOL)isMapDiaplayTypeOfflineForMapDisplayType:(QMFloatLayerMapDisplayType)displayType;

/// 主题地图配置
+ (NSDictionary *)themeMapList;


/// 地图显示开关默认值
/// @param displayType 开关
+ (BOOL)mapLayerDefaultValueForMapDisplayType:(QMFloatLayerMapDisplayType)displayType;

/// 是否拉取到了type对应的配置
+ (nullable NSDictionary *)configsForDisplayType:(QMFloatLayerMapDisplayType)displayType;

@end

NS_ASSUME_NONNULL_END
