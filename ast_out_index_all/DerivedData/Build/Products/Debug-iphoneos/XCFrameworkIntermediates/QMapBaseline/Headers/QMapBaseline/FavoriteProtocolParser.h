//
//  FavoriteProtocolParser.h
//  SOSOMap
//
//  Created by vito<PERSON> zhang on 12/28/13.
//  Copyright (c) 2013 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FavoriteFolderDefines.h"

#pragma mark - Macro Definition

extern NSString * const kFavLogicCmd;
extern NSString * const kFavRspLogicErrNo;
extern NSString * const kFavRspLogicErrStr;

extern NSString * const kFavRspDeleteFailedIs;
extern NSString * const kFavRspSimpleInfoList;
extern NSString * const kFavRspFavAllInfoList;
extern NSString * const kFavRspFavNewRspInfoList;
extern NSString * const kFavRspFavUpdateFailedIdList;

extern NSString * const kFavRspJceFavoriteProtocolErrorCode;

#pragma mark - JceFavSimpleInfo
@interface JceFavSimpleInfo : NSObject <NSCoding>

@property (nonatomic, copy) NSString *favId;
@property (nonatomic, assign) int subType; // Now unused.
@property (nonatomic, assign) double lastEditTime;

- (BOOL)isHaveFavId;

- (void)displayDebugInfo;

@end

#pragma mark - JceFavBasicInfo
@interface JceFavBasicInfo : NSObject <NSCoding>

@property (nonatomic, copy) NSString *favName;
@property (nonatomic, copy) NSString *favDesc;

- (void)displayDebugInfo;

@end

//error_code 10001; sessionid失效

#pragma mark - JceFavSVPOIDetail
@interface JceFavSVPOIDetail : NSObject <NSCoding>

@property (nonatomic, copy) NSString *svid;
@property (nonatomic, assign) double pitch;
@property (nonatomic, assign) double heading;

@property (nonatomic, copy) NSString *name;  // 从JceFavBasicInfo.favName赋值,不进行持久化.

@end

#pragma mark - JceFavAllInfo
@interface JceFavAllInfo : NSObject <NSCoding>

@property (nonatomic, assign) int32_t favType;

@property (nonatomic, strong) JceFavSimpleInfo *simpleInfo;
@property (nonatomic, strong) JceFavBasicInfo *basicInfo;

// viton: "街景收藏"的详细数据信息.
@property (nonatomic, strong) JceFavSVPOIDetail *svPOIDetail;

- (id)initWithType:(FavoriteType)type;

- (void)displayDebugInfo;

// YES: 本地新添加的收藏; NO: 已和QQCloud同步过.
- (BOOL)isLocalNewAdded;

// YES: 详细信息的ID相等; NO: 详细信息的ID不相等
- (BOOL)isDetailInfoIdEqual:(id)object;

// YES: JceFavAllInfo的Id和favId相等.
- (BOOL)isFavIdEqual:(NSString *)favId;

// YES: 该JceFavAllInfo对象有FavId.
- (BOOL)isHaveFavId;

// 返回JavFavAllInfo的Id, 如果没有Id则返回nil(本地新添加的收藏没有Id).
- (NSString *)favId;

// 返回JavFavAllInfo的Name.
- (NSString *)favName;

// 返回JavFavAllInfo的Description.
- (NSString *)favDesc;

- (void)setLastEditTime:(double)time;

- (double)lastEditTime;

// YES: Fav是街景类型, 并且有街景Id.
- (BOOL)isHaveStreetViewId;

// 返回街景Id.
- (NSString *)streetViewId;

@end

#pragma mark - JceFavNewReqInfo
@interface JceFavNewReqInfo : NSObject

@property (nonatomic, strong) JceFavBasicInfo *basicInfo;
@property (nonatomic, strong) JceFavSVPOIDetail *svPOIDetail;
@property (nonatomic, assign) int subType;

@end

#pragma mark - JceFavNewRspInfo
@interface JceFavNewRspInfo : NSObject

@property (nonatomic, copy) NSString *favName;
@property (nonatomic, copy) NSString *favId;
@property (nonatomic, assign) int errorCode;
@property (nonatomic, assign) double lastEditTime;

@end

#pragma mark - JceFavUpdateReqInfo
@interface JceFavUpdateReqInfo : NSObject

@property (nonatomic, copy) NSString *favId;
@property (nonatomic, copy) NSString *favName;
@property (nonatomic, copy) NSString *favDesc;
// 1: name 2: desc 3: name & desc
@property (nonatomic, assign) int type;

@end

#pragma mark - FavoriteProtocolParser
/**
 * 负责 1. FavoriteProtocol的数据进行解析; 2. 构建FavoriteProtocol的数据包.
 *
 */
@interface FavoriteProtocolParser : NSObject

+ (NSData *)packDeleteFavReqPkg:(NSArray *)favIds
                    withFavType:(FavoriteType)favType
                     withUserId:(NSString *)userId
                  withSessionId:(NSString *)sessionId;

+ (NSData *)packListFavSimpleReqPkgWithFavType:(FavoriteType)favType
                                    withUserId:(NSString *)userId
                                 withSessionId:(NSString *)sessionId;

+ (NSData *)packListFavDetailReqPkgwithFavType:(FavoriteType)favType
                                    withUserId:(NSString *)userId
                                 withSessionId:(NSString *)sessionId;

+ (NSData *)packUploadNewReqPkgWithFavsList:(NSArray *)favsList
                                withFavType:(FavoriteType)favType
                                 withUserId:(NSString *)userId
                              withSessionId:(NSString *)sessionId;

+ (NSData *)packGetFavAllInfoReqPkgWithFavsList:(NSArray *)favList
                                    withFavType:(FavoriteType)favType
                                     withUserId:(NSString *)userId
                                  withSessionId:(NSString *)sessionId;
+ (NSData *)packUpdateFavReqPkgWithInfo:(NSArray *)infoList
                            withFavType:(FavoriteType)favType
                             withUserId:(NSString *)userId
                          withSessionId:(NSString *)sessionId;

/**
 * 解析FavoroteProcotol相关的相应数据包.
 */
+ (NSDictionary *)unpackFavoriteProtocolResPkg:(NSData *)data;

/**
 * 由JceFavAllInfo对象来构建JceFavNewReqInfo对象.
 */
+ (JceFavNewReqInfo *)packJceFavNewReqInfoFromJceFavAllInfo:(JceFavAllInfo *)favAllInfo;

/**
 * 由JceFavAllInfo对象构建JceFavUpdateReqInfo对象.
 */
+ (JceFavUpdateReqInfo *)packJceFavUpdateReqInfoFromJceFavAllInfo:(JceFavAllInfo *)favAllInfo;

@end
