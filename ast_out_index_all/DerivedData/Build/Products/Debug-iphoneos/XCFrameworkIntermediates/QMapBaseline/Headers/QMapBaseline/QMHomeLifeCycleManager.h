//
//  QMHomeLifeCycleManager.h
//  QMapBaseline
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/3/15.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^QMHomePageLifecycleBlock)(void);

/// 管理全局只加载一次的任务
@interface QMHomeLifeCycleManager : NSObject

QMDefaultManager_H(QMHomeLifeCycleManager)

/// 添加首页首次空闲时执行的任务，全局只执行一次
/// @param block 任务
- (void)observeHomePageMapViewRunloopFreeBlock:(QMHomePageLifecycleBlock)block;

/// 首页底图渲染完成后执行的任务
- (void)observeHomeMapViewDidDrawBlock:(QMHomePageLifecycleBlock)block;

/// 通知首页底图渲染完成
- (void)notifyHomeMapViewDidDraw;

/// 首页FrontView初始化完成后执行的任务
- (void)observeHomeFrontViewInitializedBlock:(QMHomePageLifecycleBlock)block;

/// 通知首页FrontView初始化完成
- (void)notifyHomeFrontViewInitialized;

/// 首页CardList初始化完成后执行的任务
- (void)observeCardListInitializedBlock:(QMHomePageLifecycleBlock)block;

/// 通知首页CardList初始化完成
- (void)notifyCardListInitialized;

@end

NS_ASSUME_NONNULL_END
