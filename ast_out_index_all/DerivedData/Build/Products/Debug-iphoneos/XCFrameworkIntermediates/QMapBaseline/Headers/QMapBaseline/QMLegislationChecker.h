//
//  QMLegislationChecker.h
//  QMapBusiness
//
//  Created by wyh on 2021/8/23.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMHLegislationTopView.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMLegislationChecker : NSObject

@property (nonatomic, assign) QMLegislationSourceType sourceType;
/**
 是否需要在设置家/公司时展示合规的Alert（如点击设置首页家/公司时）
 */
+ (BOOL)isNeedToPromptLegislationAlert;

/**
 是否需要在详情页展示合规提醒允许上云的switch（如在常去地点页面等）
 */
+ (BOOL)isNeedToShowDetailLegislationSwitch;

/**
 是否需要在详情页展示合规提醒允许上云的switch（如在常去地点页面等）918再未登录态也显示 switch
 */
+ (BOOL)isNeedToShowDetailLegislationSwitchWithSource:(NSString *)source;
    
@end

NS_ASSUME_NONNULL_END
