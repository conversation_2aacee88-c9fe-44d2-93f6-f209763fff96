//
//  QMHippyDebuggingTableViewCell.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/18.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapFoundation/QMHippyDebuggingModel.h>

NS_ASSUME_NONNULL_BEGIN

@class QMHippyDebuggingTableViewCell;
@protocol QMHippyDebuggingTableViewCellDelegate <NSObject>

@optional
- (void)cellDidUpdateModel:(QMHippyDebuggingTableViewCell *)cell;
- (void)cellDidRemoveModel:(QMHippyDebuggingTableViewCell *)cell;

- (BOOL)textFieldShouldReturn:(UITextField *)textField;
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField;

@end

@interface QMHippyDebuggingTableViewCell : UITableViewCell

@property (nonatomic, strong) QMHippyDebuggingHippyListModel *model;

@property (nonatomic, weak) id<QMHippyDebuggingTableViewCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
