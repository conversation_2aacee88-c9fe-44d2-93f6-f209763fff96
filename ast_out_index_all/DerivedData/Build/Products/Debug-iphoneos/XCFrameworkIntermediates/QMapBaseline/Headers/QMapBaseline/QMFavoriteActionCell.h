//
//  QMFavoriteActionCell.h
//  QMapBaseline
//
//  Created by he yan on 2022/4/6.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/// 收藏地点cell
@interface QMFavoriteActionTopCell : UITableViewCell
@property (copy, nonatomic) void (^handleAddCallback)(void);    //点击“添加”
@property (copy, nonatomic) void (^handleSearchCallback)(void); //点击“搜索”

/// 更新收藏数
/// @param count count
- (void)updateCollectionCount:(NSInteger)count;

/// 是否隐藏分割线
/// @param hide hide
- (void)hideBottomLine:(BOOL)hide;
@end

@interface QMFavoriteActionBottomCell : UITableViewCell
- (void)updateTitle:(NSString *)title;
@end

NS_ASSUME_NONNULL_END
