//
//  QMTableWidgetsView.h
//  SOSOMap
//
//  Created by vector on 2020/5/19.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMTableWidgetsView : UIView

@property (class, nonatomic, assign, readonly) CGFloat contentWidth; // 38.0
@property (class, nonatomic, assign, readonly) CGFloat cellHeight; // 31.0
@property (class, nonatomic, assign, readonly) CGFloat horizentalPadding; // 10.0
@property (class, nonatomic, assign, readonly) CGFloat verticalPadding; // 10.0

@property (nonatomic, strong, readonly) UIButton *topButton;
@property (nonatomic, strong, readonly) UITableView *tableView;

+ (CGFloat)viewHeightWithCellCount:(NSInteger)count;

@end

NS_ASSUME_NONNULL_END
