//
//  QMTagTableViewCell.h
//  QMapBaseline
//
//  Created by z<PERSON><PERSON> on 2022/5/11.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol QMTagTableViewCellDelegate <NSObject>

- (void)didSelectTagMoreButtonWith:(id)info;

@end

@interface QMTagTableViewCell : UITableViewCell

@property (nonatomic, weak) id<QMTagTableViewCellDelegate> delegate;
@property (nonatomic) BOOL isEditingState;
@property (nonatomic) BOOL isLastCell;
@property (nonatomic) BOOL isFirstCell;
@property (nonatomic, strong, nullable) id info;
@property (nonatomic) BOOL hideMoreButton;

- (void)hideBottomLineImageView:(BOOL)hide;
- (void)setDataAccess;
- (void)setKeyword:(NSString *)keyword;

@end

NS_ASSUME_NONNULL_END
