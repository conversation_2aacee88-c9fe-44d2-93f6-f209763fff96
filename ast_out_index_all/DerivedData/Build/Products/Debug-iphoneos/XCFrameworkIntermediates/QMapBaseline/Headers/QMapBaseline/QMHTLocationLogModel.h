//
//  QMHTLocationLogModel.h
//  QMapBaseline
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/26.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMHTLocationLogModel : NSObject

/// 初始化方法 传入三个参数供内部计算高度
- (instancetype)initWithStr:(NSString*)str font:(UIFont*)font width:(CGFloat)width;


/// 获取保存的字符串
- (NSString*)getStr;


/// 获取计算出的字符串的高度
- (CGFloat)getHeight;
@end

NS_ASSUME_NONNULL_END
