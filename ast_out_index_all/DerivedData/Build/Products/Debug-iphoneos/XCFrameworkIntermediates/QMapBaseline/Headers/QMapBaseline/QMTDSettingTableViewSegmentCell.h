//
//  QMTDSettingTableViewSegmentCell.h
//  QMapBaseline
//
//  Created by allensun on 2022/6/13.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import "QMSettingTableViewCell.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMTDSettingTableSegmentValueKey;

@class QMTDSettingTableViewSegmentCell;

@protocol QMTDSettingTableViewSegmentCellDelegate <NSObject>

- (void)segmentCell:(QMTDSettingTableViewSegmentCell *)cell selectedIndexChanged:(NSInteger)index;

@end

@interface QMTDSettingTableViewSegmentCell : QMSettingTableViewCell

@property (nullable, nonatomic, weak) id<QMTDSettingTableViewSegmentCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
