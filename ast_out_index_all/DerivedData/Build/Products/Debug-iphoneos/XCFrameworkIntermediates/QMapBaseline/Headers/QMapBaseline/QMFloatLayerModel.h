//
//  QMMapFloatLayerModel.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/8/10.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMFloatLayerDefine.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMFloatLayerListModelType) {
    QMFloatLayerListModelTypeUnknown = 0,
    QMFloatLayerListModelTypeMapType,   //地图类型
    QMFloatLayerListModelTypeDispalyType,   //元素显示开关
    QMFloatLayerListModelTypeThemeMapList,  //主题图
};

@interface QMFloatLayerConfig : NSObject

@property (nonatomic, assign) BOOL canShowTravelMap;

@property (nonatomic, assign) BOOL canShowTrafficEvents;

@property (nonatomic, assign) BOOL canShowDynamicEvent;

@property (nonatomic, assign) BOOL canShowYoungFashion;

@property (nonatomic, assign) BOOL canShowDiscountService;

@property (nonatomic, assign) BOOL canShowHotActivity;

@end

@class QMFloatLayerListModel;
@interface QMFloatLayerModel : NSObject

@property (nonatomic, copy) NSArray *mapTypeList;

@property (nonatomic, copy) NSArray *mapAngleList;

@property (nonatomic, copy) NSArray *mapDisplayList;

@property (nonatomic, copy) NSArray *themeMapList;

+ (QMFloatLayerModel *)modelWithConfig:(QMFloatLayerConfig *)config;

- (NSArray<QMFloatLayerListModel *> *)mapDisplayModelsWithScene:(QMFloatLayerFromType)scene
                                                        mapType:(QMFloatLayerMapType)mapType
                                                         isDark:(BOOL)isDark;

@end

@interface QMFloatLayerListModel : NSObject

@property (nonatomic, copy) NSString *title;

@property (nonatomic, copy) NSString *subTitle;

@property (nonatomic, copy) NSString *iconUrl;

@property (nonatomic, copy) NSString *jumpUrl;

@property (nonatomic, assign) NSInteger index;

@property (nonatomic, assign) CGFloat width;

@property (nonatomic, assign) CGFloat height;

@property (nonatomic, assign) CGFloat map_display_height;

@property (nonatomic, assign) CGFloat map_angle_width;

@property (nonatomic, assign) CGFloat map_angle_height;

@property (nonatomic, assign) CGFloat theme_width;

@property (nonatomic, assign) CGFloat theme_height;

@property (nonatomic, assign) QMFloatLayerMapType mapType;  //地图类型

@property (nonatomic, assign) QMFloatLayerMapDisplayType mapDisplayType;    //地图显示

@property (nonatomic, copy) NSString *businessKey;  //地图显示,具体业务标识符

@property (nonatomic) QMFloatLayerListModelType listModelType;

@property (nonatomic) NSString *resource;

@property (nonatomic) NSString *qqmap;

@property (nonatomic) NSString *darkIcon;

@end


@protocol QMFloatLayerViewItemDelegate <NSObject>

- (void)didClickItem:(QMFloatLayerListModel *)model;

@optional
- (void)didClickMapSetting;

@end

NS_ASSUME_NONNULL_END
