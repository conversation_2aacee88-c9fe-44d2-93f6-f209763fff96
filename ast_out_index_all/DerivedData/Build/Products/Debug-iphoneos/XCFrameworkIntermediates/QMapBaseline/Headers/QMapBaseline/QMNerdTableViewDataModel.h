//
//  QMNerdTableViewDataModel.h
//  QMapBaseline
//
//  Created by 郑思宇 on 2024/7/24.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMNerdTableViewDataModel : NSObject

@property (nonatomic, assign) BOOL isSelected;
@property (nullable, nonatomic, copy) NSString *optionImageName;
@property (nullable, nonatomic, copy) NSString *detailURLString;
@property (nonatomic) BOOL editable;

- (instancetype)initWithoptionImageName:(nullable NSString *)optionImageName 
                        detailURLString:(nullable NSString *)detailURLString
                             isSelected:(BOOL)isSelected
                               editable:(BOOL)editable;

@end

NS_ASSUME_NONNULL_END
