//
//  QMNHippyBridger_POIRecommend.h
//  QMapBusiness
//
//  Created by wyh on 2022/2/12.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapHippy/QMNHippyBridgerFactory.h>

NS_ASSUME_NONNULL_BEGIN

@class QMNHippyBridger_POIRecommend;

@protocol QMNHippyBridgePOIRecommendPosting <NSObject>

- (void)bridger:(QMNHippyBridger_POIRecommend *)bridger poiCardHeightWillUpdated:(CGFloat)height;

- (NSDictionary *)bridgerGetScrollParameters:(QMNHippyBridger_POIRecommend *)wrapper;

@end

@interface QMNHippyBridger_POIRecommend : QMNHippyBaseBridger

@property (nonatomic, strong, readonly) TMHippyViewBox *hippyBox;

- (NSDictionary *)getScrollParameter;
- (void)callbackCardHeightWillUpdate:(CGFloat)cardHeight;

@end

NS_ASSUME_NONNULL_END
