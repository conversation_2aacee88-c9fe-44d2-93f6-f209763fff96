//
//  QMHomeToolDataCache.h
//  SOSOMap
//
//  Created by wyh on 2020/12/24.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMHomeToolDataProtocol.h"

typedef NS_ENUM(NSUInteger, QMMapToolsBucketId) {
    QMMapToolsBucketIdUNKNOWN = 0,
    QMMapToolsBucketIdTOOL3DICON,
};

NS_ASSUME_NONNULL_BEGIN

@interface QMHomeToolDataCache : NSObject

@property (nonatomic, copy) NSArray<NSString *> *recentToolKeys;

@property (nonatomic, copy) NSString *cityCode;

@property (nonatomic, copy) NSString *cityName;

@property (nonatomic, strong) NSDictionary<NSString *, QMHomeToolData*> *allTools;

@property (nonatomic, strong) NSArray<QMHomeToolGroupObject*> *groups;

@property (nonatomic, strong) NSDictionary<NSString *, NSNumber *> *remindDotCache;

@property (nonatomic, assign) BOOL showMoreIcon;

@property (nonatomic, assign) QMMapToolsBucketId bucketId;

+ (instancetype)firstTimeCache;

- (BOOL)syncToFileWithSaving:(void(^)(QMHomeToolDataCache *cache))savingBlock;
+ (QMHomeToolDataCache *)unarchiveFromFile;

@end

NS_ASSUME_NONNULL_END
