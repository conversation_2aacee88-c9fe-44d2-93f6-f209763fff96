//
//  QMMapFloatLayerPanelDefine.h
//  SOSOMap
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/8/6.
//  Copyright © 2019 Tencent. All rights reserved.
//

#ifndef QMMapFloatLayerPanelDefine_h
#define QMMapFloatLayerPanelDefine_h

extern CGFloat const QMFloatLayerLabelFontSize;
extern NSInteger const QMFloatLayerLabelNormalColor;
extern CGFloat const QMFloatLayerLabelHeight;
extern NSInteger const QMFloatLayerLabelSelectedColor;

extern CGFloat const QMMapFloatLayerPanelViewWidth;
extern CGFloat const QMMapFloatLayerPanelButtonLeftMargin;
extern NSInteger const QMFloatLayerLabelNormalColor;

extern NSString *const QMMapFloatLayerPanelItemTitle;
extern NSString *const QMMapFloatLayerPanelItemImage;
extern NSString *const QMMapFloatLayerPanelItemShape;
extern NSString *const QMMapFloatLayerPanelItemTargetType;

#endif /* QMMapFloatLayerPanelDefine_h */
