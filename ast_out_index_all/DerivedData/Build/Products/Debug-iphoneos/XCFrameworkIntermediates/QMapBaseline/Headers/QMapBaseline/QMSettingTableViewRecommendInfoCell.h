//
//  QMSettingTableViewRecommendInfoCell.h
//  QMapBusiness
//
//  Created by 梁德龙 on 2022/1/17.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMRecommendInfoCellModel : NSObject

/** 主标题 */
@property (nonatomic, copy) NSString *title;
/** 副标题 */
@property (nonatomic, copy) NSString *subtitle;

@property (nonatomic, copy) NSString *firstImage;

@property (nonatomic, copy) NSString *secondImage;
/** 图片下方文案 */
@property (nonatomic, copy) NSString *firstText;

@property (nonatomic, copy) NSString *secondText;

@end

@class QMSettingTableViewRecommendInfoCell;

@protocol QMSettingTableViewRecommendInfoCellDelegate <NSObject>

- (void)recommendInfoCellDidChanged:(QMSettingTableViewRecommendInfoCell *)recommendInfoCell selectFirst:(BOOL)selectFirst;

@end

@interface QMSettingTableViewRecommendInfoCell : UITableViewCell

@property(nonatomic,weak) id<QMSettingTableViewRecommendInfoCellDelegate> delegate;

- (instancetype)initWithModel:(QMRecommendInfoCellModel *)model selectFirst:(BOOL)selectFirst reuseIdentifier:(NSString *)reuseIdentifier;

@end

NS_ASSUME_NONNULL_END
