//
//  OfflineMapDetailViewController.h
//  SOSOMap
//
//  Created by vito<PERSON> zhang on 3/10/14.
//  Copyright (c) 2014 Tencent. All rights reserved.
//
  
#import <QMapMiddlePlatform/CityData.h>
#import <QMapMiddlePlatform/QMStrikeThroughLabelView.h>
#import <QMapMiddlePlatform/QMStrikeThroughLabelView.h>


#define QQ_MAP_DATA_DETAIL_DOWNLOADING_NOTIFICATION     @"downloading.mapDataDetail.offline.map.qq.com"
#define QQ_MAP_DATA_DETAIL_DOWNLOADING_KEY              @"downloandingCityKey"

#define QQ_MAP_DATA_DETAIL_INSTALL_KEY                  @"installingCityKey"
#define QQ_MAP_DATA_DETAIL_START_INSTAL_NOTIFICATION    @"beginInstall.mapDataDetail.offline.map.qq.com"
#define QQ_MAP_DATA_DETAIL_STOP_INSTALL_NOTIFICATION    @"endInstall.mapDataDetail.offline.map.qq.com"

typedef enum {
    OfflineDetailType_City,             // 离线地图
    OfflineDetailType_CrossingScene,    // 路口导实景航图
    OfflineDetailType_Route,            // 离线导航
} OfflineDetailType;

@protocol OfflineMapDetailViewDelegate;

@interface OfflineMapDetailViewController : QMViewController

@property (nonatomic) OfflineDetailType detailType;
@property (nonatomic, strong) CityData *city;
@property (nonatomic, weak) id<OfflineMapDetailViewDelegate> delegate;

@property (strong, nonatomic) IBOutlet UIView *bgView;
@property (strong, nonatomic) IBOutlet UILabel *mNameLable;
@property (strong, nonatomic) IBOutlet UILabel *mSizeLable;
@property (strong, nonatomic) IBOutlet QMStrikeThroughLabelView *mSizeLableWithStrike;
@property (strong, nonatomic) IBOutlet UILabel *mUpdateDateLabel;
@property (strong, nonatomic) IBOutlet UILabel *mUpdateTextLabel;
@property (strong, nonatomic) IBOutlet UILabel *mZeroTrafficLabel;
@property (strong, nonatomic) IBOutlet UILabel *mRoutePicLabel;
@property (strong, nonatomic) IBOutlet UILabel *walkNavLabel;

@property (strong, nonatomic) IBOutlet UILabel *mWalkNavigationLabel;

@property (strong, nonatomic) IBOutlet UILabel *mDriveNavigationLabel;

@property (strong, nonatomic) IBOutlet UILabel *mVoiceBroadcastLabel;

@property (strong, nonatomic) IBOutlet UILabel *mSceneLabel;

@property (strong, nonatomic) IBOutlet UIButton *startDownloadButton;

@property (strong, nonatomic) IBOutlet UIButton *updateButton;
@property (strong, nonatomic) IBOutlet UIButton *checkButton;
@property (strong, nonatomic) IBOutlet UIButton *deleteButton;
@property (strong, nonatomic) IBOutlet UIButton *cancelDownloadButton;
@property (strong, nonatomic) IBOutlet UIButton *continueDownloadButton;
@property (strong, nonatomic) IBOutlet UIButton *pauseDownloadButton;
@property (strong, nonatomic) IBOutlet UIButton *downloadButton;
@property (strong, nonatomic) IBOutlet UIView *buttonBgView;
@property (strong, nonatomic) IBOutlet UITextView *detailDescTextView;


- (IBAction)OnUpdate:(id)sender;            // NOCC:MethodNaming(老代码)
- (IBAction)OnCheck:(id)sender;             // NOCC:MethodNaming(老代码)
- (IBAction)OnDelete:(id)sender;            // NOCC:MethodNaming(老代码)
- (IBAction)OnPause:(id)sender;             // NOCC:MethodNaming(老代码)
- (IBAction)OnCancelDownload:(id)sender;    // NOCC:MethodNaming(老代码)
- (IBAction)OnContinueDownload:(id)sender;  // NOCC:MethodNaming(老代码)
- (IBAction)OnStart:(id)sender;             // NOCC:MethodNaming(老代码)

@end

/// ------------------- OfflineMapDetailViewDelegate ------------------
@protocol OfflineMapDetailViewDelegate <NSObject>

@required
- (void)updateDataForCity:(CityData* )city;
- (void)deleteDataForCity:(CityData* )city;
- (void)checkDataForCity:(CityData* )city;
- (void)pauseDownloadTask:(CityData* )city;
- (void)cancelDownloadTask:(CityData *)city;
- (void)continueDownloadTask:(CityData *)city;
- (void)startDownloadTask:(CityData *)city;

@optional
- (void)startTimeReport:(CityData *)city;
- (void)endTimeReport:(CityData *)city;
@end
