//
//  QMOperationFloatView.h
//  SOSOMap
//
//  Created by wangzz on 15/8/27.
//  Copyright (c) 2015年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMClientOperationModel.h>

typedef NS_ENUM(NSUInteger, QMOperationFloatViewAnimation) {
    QMOperationFloatViewAnimationNone,    // 无动画
    QMOperationFloatViewAnimationNormal,  // 有动画
};

@class QMOperationFloatView;
typedef void(^QMOperationFloatViewButtonAction)(QMOperationFloatView *alertView);

// 签到活动弹框
@interface QMOperationFloatView : UIView

@property (nonatomic, copy) QMOperationFloatViewButtonAction detailButtonAction;

@property (nonatomic, copy) QMOperationFloatViewButtonAction closeButtonAction;

@property (nonatomic) QMOperationFloatViewAnimation animation;

@property (nonatomic, readonly) BOOL didClickCloseBtn;

- (instancetype)initWithLayerInfo:(QMClientOperationLayer *)layerInfo image:(UIImage *)image;

- (void)showInView:(UIView *)view;

- (void)dismiss;

@end
