//
//  QMDownloadTaskManager.h
//  
//
//  Created by 张宇 on 15/12/3.
//
//

#import <Foundation/Foundation.h> 
#import <QMapMiddlePlatform/OfflineMapDataDownloader.h>
#import <QMapMiddlePlatform/QMDownloadTaskManagerProtocol.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMDownloadTaskManagerSessionIdentifier;

extern NSString *const QMResumeUserForceQuitAppDataNotification;

@interface QMDownloadTaskManager : NSObject <QMDownloadTaskManagerProtocol>

QMSharedInstance_H(QMDownloadTaskManager)

@property (nullable, nonatomic) void (^backgroundSessionCompletionHandler)(void);

+ (void)registerAppBridge;
- (NSURLSessionDownloadTask *)startTaskWithRequest:(NSURLRequest *)request fromDownloader:(OfflineMapDataDownloader *)downloader;
- (void)pause:(NSURLSessionTask *)task fromDownloader:(OfflineMapDataDownloader *)downloader;
- (void)cancel:(NSURLSessionTask *)task fromDownloader:(OfflineMapDataDownloader *)downloader;

@end

NS_ASSUME_NONNULL_END
