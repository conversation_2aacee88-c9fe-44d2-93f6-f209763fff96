//
//  QMHomePageFootprintPopupModel.h
//  QMapBaseline
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/5/20.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


@interface QMHippyRichTextItemModel : QMModel

@property (nonatomic, copy) NSString *color;
@property (nonatomic, copy) NSString *text;
@property (nonatomic, strong) NSNumber *size;

@end

@interface QMHippyRichTextIconModel : QMModel

@property (nonatomic, copy) NSString *url;
@property (nonatomic, strong) NSNumber *width;
@property (nonatomic, strong) NSNumber *height;

@end

@interface QMHippyRichTextModel : QMModel

@property (nonatomic, strong) QMHippyRichTextIconModel *icon;
@property (nonatomic, strong) QMHippyRichTextItemModel *text;

@end


@interface QMHippyRichTextBgModel : QMModel

@property (nonatomic, copy) NSString *endColor;
@property (nonatomic, copy) NSString *startColor;

@end

@interface QMFootprintPopupTitleModel : QMModel

@property (nonatomic, strong) QMHippyRichTextBgModel *bg;
@property (nonatomic, strong) NSArray <QMHippyRichTextModel *>*riches;

@end


@interface QMFootprintPopupImageModel : QMModel

@property (nonatomic, copy) NSString *regionURL;
@property (nonatomic, strong) NSNumber *height;
@property (nonatomic, strong) NSNumber *width;
@property (nonatomic, copy) NSString *url;
@property (nonatomic, strong) NSArray <QMFootprintPopupTitleModel *>*textarea;

@end


@interface QMHomePageFootprintPopupModel : QMModel

@property (nonatomic, copy) NSString *bgColor;
@property (nonatomic, strong) NSArray <QMFootprintPopupTitleModel *>*buttons;
@property (nonatomic, copy) QMFootprintPopupImageModel *image;
@property (nonatomic, copy) QMFootprintPopupTitleModel *title;

@end

NS_ASSUME_NONNULL_END
