//
//  QMSwitchContainerCell.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON>(林汉达) on 2018/8/25.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
@class QMSettingSwitch;

@protocol QMSwitchContainerCellDelegate;
@interface QMSwitchContainerCell : UITableViewCell

@property (nonatomic,   weak) id<QMSwitchContainerCellDelegate> delegate;
@property (nonatomic, assign, getter=isSwitchOn) BOOL switchOn;
@property (nonatomic, strong, readonly) QMSettingSwitch *switchButton;
@property (nonatomic, assign) BOOL isNight;

@property (nonatomic, strong) NSIndexPath *indexPath;

@end

@protocol QMSwitchContainerCellDelegate<NSObject>

@optional
- (void)switchContainerCell:(QMSwitchContainerCell *)cell switchClicked:(BOOL)isOn;

@end
