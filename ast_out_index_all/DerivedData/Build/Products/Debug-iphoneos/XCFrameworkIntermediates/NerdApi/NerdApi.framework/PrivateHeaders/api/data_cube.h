/*
 * Copyright 2021 Tencent Copyright 2020 Tencent. All rights reserved
 * @version: 1.0
 * @LastEditTime: 2021-03-21 00:13:30
 */

#pragma once

#include <memory>
#include <set>
#include <vector>

#include "api/config.h"
#include "dto/background.h"
#include "dto/iface_close_road.h"
#include "dto/iface_dtm.h"
#include "dto/iface_indoor.h"
#include "dto/iface_link.h"
#include "dto/iface_object3d.h"
#include "dto/iface_traffic_road.h"
#include "dto/iface_agilemap.h"
#include "dto/iface_hdair.h"
#include "dto/overlap.h"

namespace nerd {
namespace api {

struct ReqOnceKey;
/**
 * @brief 数据层基类
 */
class NERD_EXPORT IDataLayer {
 public:
  virtual ~IDataLayer() = default;
  /**
   * 获取图层数据的类型
   * @return
   */
  virtual MapDataBuildingBlockID GetId() const = 0;

  /**
   * 获取当前图层的TileID
   * @return TileID
   */
  virtual TileIDType GetTileID() const = 0;

  /**
   * @brief 获取图层内容状态
   * */
  virtual LayerRelationStatus GetRelationStatus() const = 0;

  /**
   * @brief 获取当前图层的数据版本
   * @return
   */
  virtual VersionIDType GetBBVersionId() const = 0;

  /**
   * @brief 获取当前瓦片的数据版本区间
   * @return
   */
  virtual VersionIDRange GetVersionRange() const = 0;

  /**
   * @brief 获取当前图层的数据状态
   * @return
   */
  virtual DataStatus GetDataStatus() const = 0;

  /**
   * @brief 获取当前瓦片的语言类型，只有lane/route/bmd支持多语言，object3d和debug不支持
   * @return
   */
  virtual MapDataLanguageType GetLanguageType() const = 0;

  /**
   * @brief 获取该瓦片所关联的额外的瓦片ID列表
   * 如: 若某个元素F跨多个瓦片A,B,C中，数据F实际存储只存储在A中, B,C瓦片分别存储了额外瓦片A;
   * @warning 业务方根据实际需要自己请求额外的瓦片;
   * @return 瓦片ID列表
   */
  virtual std::set<TileIDType> GetExternalTileIDs() const = 0;

  /**
   * 是否海外瓦片(海外数据采用wgs84坐标系)
   * @return true海外瓦片, false:国内瓦片
   */
  virtual bool IsOutOfChina() const = 0;

  /**
   * 获取瓦片的世界图区域类型
   * @return 区域类型
   */
  virtual WorldMapRegionType GetWorldMapRegionType() const = 0;
};

class ILane;
typedef std::shared_ptr<ILane> ILanePtr;
class IBoundary;
typedef std::shared_ptr<IBoundary> IBoundaryPtr;
class ILaneGroup;
typedef std::shared_ptr<ILaneGroup> ILaneGroupPtr;

/**
 * 只包含ID的Layer拓扑接口
 */
class ISDLayerTopo {
 public:
  virtual ~ISDLayerTopo() = default;
  /**
   * @brief 获取Link的下游LinkID
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetNextLinkIDs(LinkIDType linkID, LinkDirection direction,
                                                 bool with_indoor = false) = 0;

  /**
   * @brief 获取Link的上游LinkID
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetPreviousLinkIDs(LinkIDType linkID, LinkDirection direction,
                                                     bool with_indoor = false) = 0;

  /**
   * @brief 获取Link的下游LinkID，包含交叉点内link
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetNextLinkIDsWithinIntersection(LinkIDType linkID, LinkDirection direction,
                                                                   bool with_indoor = false) = 0;

  /**
   * @brief 获取Link的上游LinkID，包含交叉点内link
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetPreviousLinkIDsWithinIntersection(LinkIDType linkID, LinkDirection direction,
                                                                       bool with_indoor = false) = 0;
};

/**
 * @brief SDLink数据层基类
 */
class NERD_EXPORT ISDLinkLayer : public IDataLayer, public ISDLayerTopo {
 public:
  ~ISDLinkLayer() override = default;

  /**
   * @brief 获取tile内所有的道路Link
   * @param with_indoor 是否包含室内Link
   * @return Link列表
   */
  virtual std::vector<ILinkConstPtr> GetLinkArr(bool with_indoor = false) const = 0;

  /**
   * @brief 按照LinkId查询指定的Link数据
   * @param linkID Link Id
   * @return tile中和LinkId匹配的Link
   */
  virtual ILinkConstPtr GetLinkByID(LinkIDType linkID) = 0;
  /**
   * @brief 根据原始linkId查询指定的link数据，如果原始link_id不在当前瓦片中，返回空
   * @param raw_link_id 原始link id
   * @return 与原始link id匹配的link
   */
  virtual ILinkConstPtr GetLinkByRawID(int64_t raw_link_id) = 0;

  /**
   * @brief 根据TPID查询指定link数据
   * @param tpid
   * @return TPID存在返回link，否则返回nullptr
   */
  virtual ILinkConstPtr GetLinkByTPID(const TPIDType& tpid) = 0;

  /**
   * @brief 按照NodeId查询指定的Node数据
   * @param node_id Node Id
   * @return tile中和NodeID匹配的Node
   */
  virtual ILinkNodeConstPtr GetLinkNodeByID(NodeIDType node_id) = 0;

  /**
   * @brief 根据TPID查询指定linknode数据
   * @param tpid
   * @return TPID存在返回linknode，否则返回nullptr
   */
  virtual ILinkNodeConstPtr GetLinkNodeByTPID(const TPIDType& tp_id) = 0;

  /**
   * @brief 根据原始linkNodeId查询指定的linkNode数据，如果原始linkNode_id不在当前瓦片中，返回空
   * 说明: 只有开启对应宏并且指定了拉去linkNodeRawID数据开关才会拉去对应的数据, 返回有效值
   *  1> 关闭宏: DISABLE_LINKNODE_RAW_ID 2> 设定DataFeatureType: kRouteLinkNodeRawId
   * @param raw_node_id 原始node id
   * @return 与原始node id匹配的linkNode
   */
  virtual ILinkNodeConstPtr GetLinkNodeByRawID(int64_t raw_node_id) = 0;
  
  /**
   * @brief 获取Link的下游列表
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetNextLinks(LinkIDType linkID, LinkDirection direction,
                                                   bool with_indoor = false) = 0;

  /**
   * @brief 获取Link的上游列表
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetPreviousLinks(LinkIDType linkID, LinkDirection direction,
                                                       bool with_indoor = false) = 0;

  /**
   * @brief 获取Link的下游列表，包含交叉点内link
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetNextLinksWithinIntersection(LinkIDType linkID, LinkDirection direction,
                                                                     bool with_indoor = false) = 0;

  /**
   * @brief 获取Link的上游列表，包含交叉点内link
   * @param linkID link id
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetPreviousLinksWithinIntersection(LinkIDType linkID, LinkDirection direction,
                                                                         bool with_indoor = false) = 0;

  /**
   * @brief 获取绑定在Link上的指定类型的POIId
   * @param linkID
   */
  virtual std::set<uint64_t> GetReferencePOIId(LinkIDType linkID) = 0;

  /**
   * 铁路数据： 铁路道路数据和普通道路数据是两个完全无关集，互相不参与对方的拓扑逻辑
   */
  /**
   * @brief 获取铁路全部link列表
   * @return
   */
  virtual std::vector<ILinkConstPtr> GetRailwayLinkArr() = 0;

  /**
   * @brief 获取特定linkid的铁路道路数据
   * @param linkID
   */
  virtual ILinkConstPtr GetRailwayLinkById(LinkIDType linkID) = 0;

  /**
   * @brief 获取特定铁路节点的数据
   * @param linkNodeId
   */
  virtual ILinkNodeConstPtr GetRailwayLinkNodeById(NodeIDType linkNodeId) = 0;

  /** ------------------------HDAirToB------------------------------**/
  /**
   * 返回瓦片中是否已经加载并解析了HDAir的数据
   * @return true:已经加载解析完毕; false:尚未加载这部分数据;
   */
  virtual bool HaveLoadedHDAirData() const = 0;
  /**
   * 返回瓦片内所有的FeaturePointID列表
   * 备注: HDAirToB规格数据
   * @return FeaturePointID列表
   */
  virtual std::vector<hdair::FeaturePointIDType> GetFeaturePointIDs() const = 0;
  /**
   * 返回指定LinkID的FeaturePointID列表
   * @param linkId LinkId
   * @return FeaturePointID列表
   */
  virtual std::vector<hdair::FeaturePointIDType> GetFeaturePointIDsByLinkId(LinkIDType linkId) const = 0;
  /**
   * 根据FeaturePointID查询FeaturePoint对象信息
   * @param fpId FeaturePointID
   * @return 非空则代表对应的FeaturePoint信息,
   *         空则代表没有找到, 需要到对应TileID的瓦片中找;
   */
  virtual std::shared_ptr<hdair::IFeaturePoint> GetFeaturePointById(hdair::FeaturePointIDType fpId) const = 0;
  /**
   * 返回瓦片内所有的StopLineIDType列表
   * 备注: HDAirToB规格数据
   * @return StopLineIDType列表
   */
  virtual std::vector<hdair::StopLineIDType> GetStopLineIDs() const = 0;
  /**
   * 返回指定LinkID的StopLineIDType列表
   * @param linkId LinkId
   * @return StopLineIDType列表
   */
  virtual std::vector<hdair::StopLineIDType> GetStopLineIDsByLinkId(LinkIDType linkId) const = 0;
  /**
   * 根据StopLineID查询StopLine对象信息
   * @param spId   停止线对象ID
   * @return 非空则代表对应的StopLine信息,
   *         空则代表没有找到, 需要到对应TileID的瓦片中找;
   */
  virtual std::shared_ptr<hdair::IStopLine> GetStopLineById(hdair::StopLineIDType spId) const = 0;
  /**
   * 返回瓦片内所有的Zebra ID列表
   * 备注: HDAirToB规格数据
   * @return ZebraID 列表
   */
  virtual std::vector<hdair::FeaturePointIDType> GetZebraIDs() const = 0;
  /**
   * 返回指定LinkID的ZebraID列表
   * @param linkId LinkId
   * @return ZebraID列表
   */
  virtual std::vector<hdair::FeaturePointIDType> GetZebraIDsByLinkId(LinkIDType linkId) const = 0;
  /**
   * 根据ZebraID查询Zebra对象信息
   * @param fId   ZebraID
   * @return 非空则代表对应的Zebra信息,
   *         空则代表没有找到, 需要到对应TileID的瓦片中找;
   */
  virtual std::shared_ptr<hdair::IZebra> GetZebraById(hdair::FeaturePointIDType fId) const = 0;
  /**
   * @brief 获取tile中所有的车道
   * @return Lane列表
   */
  virtual std::vector<std::shared_ptr<hdair::ILane>> GetLaneArr() const = 0;
  /**
   * @brief 根据laneId查询
   * @param laneId
   * @return Lane
   */
  virtual std::shared_ptr<hdair::ILane> GetLaneById(hdair::LaneIDType laneId) const = 0;
  /**
   * @brief 获取tile内所有的路口内虚拟道路
   * @return IntersectionRoad列表
   */
  virtual std::vector<std::shared_ptr<hdair::IIntersectionRoad>> GetIntersectionRoadArr() const = 0;
  /**
   * @brief 按照路口虚拟道路ID查询指定的道路数据
   * @param roadId IntersectionRoadID
   * @return tile中和roadId匹配的Road
   */
  virtual std::shared_ptr<hdair::IIntersectionRoad> GetIntersectionRoadByID(hdair::IntersectionRoadID roadId) const = 0;
  /**
   * @brief 按照路口虚拟道路节点Id查询指定的Node数据
   * @param node_id IntersectionRoadNodeID
   * @return tile中和NodeID匹配的RoadNode
   */
  virtual std::shared_ptr<hdair::IIntersectionRoadNode> GetIntersectionRoadNodeByID(hdair::IntersectionRoadNodeID nodeId) const = 0;
  /**
   * 返回指定LinkID关联的IntersectionRoadID列表
   * @param linkId LinkId
   * @return IntersectionRoadID列表
   */
  virtual std::vector<hdair::IntersectionRoadID> GetIntersectionRoadIDsByLinkId(LinkIDType linkId) const = 0;
  /**
   * 返回指定FeaturePointID关联的IntersectionRoadID列表
   * @param fId FeaturePointIDType
   * @return IntersectionRoadID列表
   */
  virtual std::vector<hdair::IntersectionRoadID> GetIntersectionRoadIDsByFpId(hdair::FeaturePointIDType fpId) const = 0;
  /** ------------------------HDAirToB------------------------------**/
};

/**
 * @brief HDLaneLayer数据层基类
 */
class NERD_EXPORT IHDLaneLayer : public IDataLayer {
 public:
  /**
   * @brief 获取tile中所有的LaneGroup
   * @return LaneGroup列表
   */
  virtual std::vector<ILaneGroupConstPtr> GetLaneGroupArr() const = 0;

  /**
   * @brief 获取tile中所有的LaneGroup，只存储在当前tile
   * @return LaneGroup列表
   */
  virtual std::vector<ILaneGroupConstPtr> GetInnerLaneGroupArr() const = 0;

  /**
   * 获取范围内的LaneGroup
   * @param rect
   * @return 如果不存在返回空
   */
  virtual std::vector<ILaneGroupConstPtr> GetLaneGroups(const Rect &rect) const = 0;
  /**
   * 获取范围内的CrossArea
   * @param rect
   * @return 如果不存在返回空
   */
  virtual std::vector<ICrossAreaConstPtr> GetCrossAreas(const Rect &rect) const = 0;

  /**
   * 获取范围内的Lanes
   * @param rect
   * @return 如果不存在返回空
   */
  virtual std::vector<ILaneConstPtr> GetLanes(const Rect &rect) const = 0;

  /**
   * 获取范围内的Boundaries
   * @param rect
   * @return 如果不存在返回空
   */
  virtual std::vector<IBoundaryConstPtr> GetBoundaries(const Rect &rect) const = 0;

  /**
   * 获取范围内的交通灯箱
   * @param rect 范围
   * @return 如果不存在返回空
   */
  virtual std::vector<std::shared_ptr<const TrafficLightBox>> GetTrafficLights(const Rect &rect) const = 0;

  /**
   * 获取范围内的站点区
   * @param rect 范围
   * @return 如果不存在返回空
   */
  virtual std::vector<std::shared_ptr<const RoadMarkObject>> GetStations(const Rect &rect) const = 0;

  /**
   * 获取所有的路名参考线
   * @return 如果不存在返回空
   */
  virtual std::vector<std::shared_ptr<const RoadNameLine>> GetRoadNameLines() const = 0;

  /**
   * 获取所有绿化带
   * @return  所有绿化带
   */
  virtual std::vector<std::shared_ptr<const GreenBeltObject>> GetGreenBelts() const = 0;

  /**
   * 获取所有的站点区
   * @return 如果不存在返回空
   */
  virtual std::vector<std::shared_ptr<const RoadMarkObject>> GetStations() const = 0;

  /**
   * 获取所有的元素
   * @return 所有元素
   */
  virtual std::vector<ISurfaceObjectConstPtr> GetSurfaceObjects() const = 0;

  /**
   * 获取所有导流带
   * @return  tile范围内的所有导流区
   */
  virtual std::vector<std::shared_ptr<const LeadAreaObject>> GetLeadAreas() const = 0;

  /**
   * 按id获取元素
   * @return 如果不存在返回nullptr
   */
  virtual ISurfaceObjectConstPtr GetSurfaceObjectById(SurfaceIDType id) const = 0;
  /**
   * 获取所有精模收费站ID
   * @return 所有精模收费站ID
   */
  virtual PreciseTollIDVec GetPreciseTollIDs() const = 0;
  /**
   * 按类型获取所有的地面标志元素
   * @param type 类型
   * @return 所有地面元素
   */
  virtual std::vector<std::shared_ptr<const RoadMarkObject>> GetRoadMarkObjectByType(RoadMarkType type) const = 0;
  /**
   * 按id获取地面标志类型元素
   * @param id 地面标志元素id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const RoadMarkObject> GetRoadMarkObjectById(SurfaceIDType id) const = 0;
  /**
   * 获取范围内的地面标志元素
   * @param rect 范围
   * @return 如果不存在返回空
   */
  virtual std::vector<std::shared_ptr<const RoadMarkObject>> GetRoadMarkObjects(const Rect &rect) const = 0;
  /**
   * 获取范围内的地面标志元素
   * @param rect 范围
   * @param types 过滤类型
   * @return 如果不存在返回空
   */
  virtual std::vector<std::shared_ptr<const RoadMarkObject>> GetRoadMarkObjects(
      const Rect &rect, std::vector<RoadMarkType> types) const = 0;
  /**
   * 按id获取减速带信息
   * @param id 减速带id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const RoadMarkObject> GetSlowdownBarrier(SurfaceIDType id) const = 0;
  /**
   * 按id获取禁停区信息
   * @param id 禁停区id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const RoadMarkObject> GetForbiddenParkArea(SurfaceIDType id) const = 0;
  /**
   * 按id获取停止线信息
   * @param id 停止线id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const RoadMarkObject> GetStopLineById(SurfaceIDType id) const = 0;
  /**
   * 按id获取站台信息
   * @param id 站台id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const RoadMarkObject> GetStationById(SurfaceIDType id) const = 0;

  /**
   * 按id获取斑马线信息
   * @param id 站台id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const RoadMarkObject> GetZebraCrossById(SurfaceIDType id) const = 0;

  /**
   * 按id获取交通灯灯箱元素
   * @param id 灯箱id
   * @return 如果不存在返回nullptr
   */
  virtual std::shared_ptr<const TrafficLightBox> GetTrafficLightById(SurfaceIDType id) const = 0;

  /**
   * 获取路口面元素
   * @param id 路口面id
   * @return 如果不存在返回nullptr
   */
  virtual ICrossAreaConstPtr GetCrossArea(CrossAreaIDType id) const = 0;

  /**
   * 通过TPID获取路口面
   */
  virtual ICrossAreaConstPtr GetCrossAreaByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取tile中所有的车道
   * @return Lane列表
   */
  virtual std::vector<ILaneConstPtr> GetLaneArr() const = 0;

  /**
   * @brief 获取tile中所有的标线/边线
   * @return Boundary列表
   */
  virtual std::vector<IBoundaryConstPtr> GetLaneBoundaryArr() const = 0;

  /**
   * @brief 获取tile中所有的路口面
   * @return 路口面列表
   */
  virtual std::vector<ICrossAreaConstPtr> GetCrossAreaArr() const = 0;

  /**
   * @brief 获取tile中所有的路口处隧道
   * @return 隧道列表
   */
  virtual std::vector<std::shared_ptr<TunnelCrossArea>> GetTunnelCrossAreas() const = 0;
    
  /**
   *  @brief 获取tile内被分歧口隧道覆盖的跨tile的lgid，解决其他tile内覆盖的lg无法过滤掉问题(如分歧口隧道在TileA,覆盖TileB的lg,此时TileB存储被覆盖的跨tile lg)
   *  @return lgid列表
   */
  virtual std::set<nerd::api::LaneGroupIDType> GetCoveredLgByTunnel() const = 0;
    
  /**
   *  @brief 获取tile内被分歧口隧道覆盖的跨tile的crossid，解决其他tile内覆盖的cross无法过滤掉问题
   *  @return crossid列表
   */
  virtual std::set<nerd::api::CrossAreaIDType> GetCoveredCrossByTunnel() const = 0;
    
  /**
   * @brief 获取tile中所有的路口面，只存储在当前tile内
   * @return 路口面列表
   */
  virtual std::vector<ICrossAreaConstPtr> GetInnerCrossAreaArr() const = 0;

  /**
   * @brief 按照LaneGroupId查询指定的LaneGroup数据
   * @param lg_id LaneGroup Id
   * @return tile中和LaneGroupId匹配的LaneGroup, 如果不存在则返回nullptr
   */
  virtual ILaneGroupConstPtr GetLaneGroupByID(LaneGroupIDType lg_id) const = 0;

  /**
   * 通过TPID获取Lanegroup元素
   */
  virtual ILaneGroupConstPtr GetLaneGroupByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 按照LaneId查询指定的Lane数据, 如果返回nullptr，请注意是否传入的
   *        laneID.tile_id和当前的cubeTileID匹配，或者传入的laneID.lane_id
   *        是非法值
   * @param laneID LaneId
   * @return Lane数据，如果不存在则返回nullptr
   */
  virtual ILaneConstPtr GetLaneByID(LaneIDType laneID) const = 0;

  /**
   * 通过TPID获取Lane常指针
   */
  virtual ILaneConstPtr GetLaneByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 按照BoundarId查询指定的Boundary数据
   * @param boundary_id
   * @return Boundary数据，如果不存在则返回nullptr
   */
  virtual IBoundaryConstPtr GetBoundaryByID(BoundaryIDType boundary_id) const = 0;

  /**
   * 通过TPID获取Boundary
   */
  virtual IBoundaryConstPtr GetBoundaryByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取tile中所有按照Lane Group分组的的Boundary集合, 只在通过tile id获取时生效
   * @return 按照Lane Group分组的Boundary集合
   */
  virtual std::vector<std::vector<nerd::api::BoundarySetIDType>> GetBoundaryGroupsByTileID() const = 0;

  /**
   * @brief 获取hd lane layer中距离point最近的信号灯
   * @param point: 定位点
   * @param distance: 前向搜索距离
   * @param heading: 搜索角度, [0,360)
   * @param max_tolerance_angle 最大容忍角度偏差 [0.0, 360)
   * @return 信号灯集合
   */
  virtual std::vector<TrafficLightBoxConstPtr> GetClosestTrafficLights(const nerd::api::GeoCoordinate &point,
                                                                       double distance, AngleDegreeType heading,
                                                                       AngleDegreeType max_tolerance_angle) const = 0;

  /**
   * @brief 获取距离当前点最近车道
   * @param point: 定位点
   * @param distance: 搜索半径
   * @param heading_angle: 航线角度 [0, 360)
   * @param max_tolerance_angle 最大容忍角度偏差 [0.0, 360)
   * @return
   * - ILaneConstPtr 车道指针
   * - InLinePosition 线内坐标点
   */
  virtual std::vector<std::pair<ILaneConstPtr, InLinePosition>> GetClosetLanes(
      const nerd::api::GeoCoordinate &point, double distance, AngleDegreeType heading_angle,
      AngleDegreeType max_tolerance_angle) const = 0;

  /**
   * @brief 获取hd lane layer中距离point最近的路牙
   * @param point: 定位点
   * @param distance: 前向搜索距离
   * @param heading: 搜索角度, [0,360)
   * @param max_tolerance_angle 最大容忍角度偏差 [0.0, 360)
   * @return （路牙集合(surface_object)，路牙的sl offset）
   */
  virtual std::vector<std::pair<RoadMarkObjectConstPtr, std::vector<InLinePosition>>> GetClosestCurbs(
      const nerd::api::GeoCoordinate &point, double distance, AngleDegreeType heading,
      AngleDegreeType max_tolerance_angle) const = 0;

  /**
   * @brief 根据ID获取停止线
   * @param stop_line_id 指定的停止线id
   * @return 相关停止线
   */
  virtual std::shared_ptr<const RoadMarkObject> GetCrossStopLineById(const SurfaceIDType &stop_line_id) const = 0;

  /**
   * @brief 判断点是否在地理围栏内
   * @param point 定位点
   * @param tolerance_distance_m 偏离道路最大容忍距离
   * @return
   * - true 在围栏内
   * - false 不在围栏内
   */
  virtual bool WithinGeoFence(const nerd::api::GeoCoordinate &point, double tolerance_distance_m) const = 0;

  /**
   * 查询SurfaceObject关联的车道
   * @param id SurfaceObject
   * @return 车道列表，如果id无效或者和车道无关联返回空
   */
  virtual std::vector<ILaneConstPtr> GetObjectRelatedLane(SurfaceIDType id) const = 0;

  /**
   * @brief 获取tile的矩形框
   */
  virtual const nerd::api::Rect &GetBBox() const = 0;

  /**
   * @deprecated
   * @brief 获取所有的overlap
   * @return overlaps
   */
  virtual std::vector<IOverlapConstPtr> GetOverlaps() const = 0;

  /**
   * @deprecated
   * @brief 按id获取overlap
   * @return 如果不存在返回nullptr
   */
  virtual IOverlapConstPtr GetOverlapById(OverlapIDType id) const = 0;

  /**
   * @deprecated
   * @brief 获取范围内的overlaps
   * @return 如果不存在返回nullptr
   */
  virtual std::vector<IOverlapConstPtr> GetOverlaps(const Rect &rect) const = 0;

  /**
   * @deprecated
   * @brief 获取object关联的overlaps
   * @return 如果不存在返回nullptr
   */
  virtual std::vector<IOverlapConstPtr> GetOverlaps(LaneOverlap::AttachedObjectID object_id,
                                                    OverlapType overlap_type) const = 0;

  /**
   * 获取范围内的surface object
   * @param rect 范围
   * @return 如果不存在返回空
   */
  virtual std::vector<ISurfaceObjectConstPtr> GetSurfaceObjects(const Rect &rect) const = 0;

  /**
   * @brief 获取路口非机动车道, 每个路口非机动车道包含其左右两个boundary
   * @return 路口非机动车道列表
   */
  virtual const std::vector<std::array<std::shared_ptr<std::vector<Coordinate>>, 2>> &GetBicycleLanes() const = 0;
  virtual uint8_t GetHDMark() const = 0;

  /**
   * @brief 获取lane_id的下游lane，保证Rect内跨瓦片的拓扑也能取到
   * @param lane_id
   * @return 下游lane集合
   */
  virtual std::vector<ILaneConstPtr> GetNextLanes(LaneIDType lane_id) const = 0;

  /**
   * 通过lane 的 tpid获取下游lane
   */
  virtual std::vector<ILaneConstPtr> GetNextLanesByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_id的上游lane，保证Rect内跨瓦片的拓扑也能取到
   * @param lane_id
   * @return 上游lane集合
   */
  virtual std::vector<ILaneConstPtr> GetPreviousLanes(LaneIDType lane_id) const = 0;

  /**
   * 通过lane 的 tpid获取上游lane
   */
  virtual std::vector<ILaneConstPtr> GetPreviousLanesByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_group_id的下游lane group，保证Rect内跨瓦片的拓扑也能取到
   * @param lane_group_id
   * @return 下游lane group集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetNextLaneGroups(LaneGroupIDType lane_group_id) const = 0;

  /**
   * 通过lanegroup 的 tpid获取下游lanegroup
   */
  virtual std::vector<ILaneGroupConstPtr> GetNextLaneGroupsByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_group_id的上游lane group，保证Rect内跨瓦片的拓扑也能取到
   * @param lane_group_id
   * @return 上游lane group集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetPreviousLaneGroups(LaneGroupIDType lane_group_id) const = 0;

  /**
   * 通过lanegroup 的 tpid获取上游lanegroup
   */
  virtual std::vector<ILaneGroupConstPtr> GetPreviousLaneGroupsByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_id的下游lane_id
   * @param lane_id
   * @return 下游lane_id集合
   */
  virtual std::vector<LaneIDType> GetNextLaneIDs(LaneIDType lane_id) const = 0;

  /**
   * 通过lane的tpid获取下游lane tpid
   */
  virtual std::vector<TPIDType> GetNextLaneTPIDs(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_id的上游lane_id
   * @param lane_id
   * @return 上游lane_id集合
   */
  virtual std::vector<LaneIDType> GetPreviousLaneIDs(LaneIDType lane_id) const = 0;

  /**
   * 通过lane的tpid获取上游lane tpid
   */
  virtual std::vector<TPIDType> GetPreviousLaneTPIDs(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_group_id的下游lane_group_id
   * @param lane_group_id
   * @return 下游lane_group_id集合
   */
  virtual std::vector<LaneGroupIDType> GetNextLaneGroupIDs(LaneGroupIDType lane_group_id) const = 0;

  /**
   * 通过lanegroup的tpid获取下游lanegroup tpid
   */
  virtual std::vector<TPIDType> GetNextLaneGroupTPIDs(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取lane_group_id的上游lane_group_id
   * @param lane_group_id
   * @return 上游lane_group_id集合
   */
  virtual std::vector<LaneGroupIDType> GetPreviousLaneGroupIDs(LaneGroupIDType lane_group_id) const = 0;

  /**
   * 通过lanegroup的tpid获取上游lanegroup tpid
   */
  virtual std::vector<TPIDType> GetPreviousLaneGroupTPIDs(const TPIDType& tpid) const = 0;
  /**
   * 获取当前 cube 的数据级别
   * @return @see LaneDetailLevel
   */
  virtual LaneDetailLevel GetLaneDetailLevel() const = 0;
  /**
   * 获取道路内车道数据数量
   * @return 道路内车道数据数量
   */
  virtual size_t GetRoadLaneSize() const = 0;

  /**
   * 获取路口内车道数据数量
   * @return 路口内车道数据数量
   */
  virtual size_t GetIntersectionLaneSize() const = 0;
  /**
   * 获取面路况道路(高精或4K)信息
   * @return
   */
  virtual std::unordered_map<uint32_t, std::shared_ptr<TrafficRoadInfo>> GetTrafficRoadInfoMap() const = 0;

  /**
   * @brief 获取所有的天桥数据
   * @return tile范围内的所有天桥
   */
  virtual std::vector<std::shared_ptr<PedestrianOverpass>> GetPedestrianOverpass() const = 0;

  /**
   * @brief 获取所有的龙门架数据
   * @return tile范围内的所有龙门架
   */
  virtual std::vector<std::shared_ptr<GantryObject>> GetGantryObjects() const = 0;

  /**
   * @brief 获取所有的组件模型数据
   * @return tile范围内的所有组件模型
   */
  virtual std::vector<std::shared_ptr<ComponentModelObjectList>> GetComponentModelObjects() const = 0;
    
  /**
   * @brief HDLaneLayer析构函数
   * @return
   */
  ~IHDLaneLayer() override = default;
};

/**
 * @brief BackgroundLayer数据层基类
 */
class NERD_EXPORT IBackgroundLayer : public IDataLayer {
 public:
  /**
   * @brief IBackgroundLayer析构函数
   * @return
   */
  ~IBackgroundLayer() override = default;

  /**
   * @brief 获取当前图层的普通背景+AOI+地铁面
   * @return const std::vector<RegionFeature>&
   */
  virtual const std::vector<RegionFeature> &GetRegionFeatures() const = 0;

  /**
   * @brief 获取当前图层的SVG(球场, 停车位等)
   * @return const std::vector<SVGFeature>&
   */
  virtual const std::vector<SVGFeature> &GetSVGFeatures() const = 0;

  /**
   * @brief 获取当前图层的人行天桥面
   * @return const std::vector<PedestrianOverpassFeature>&
   */
  virtual const std::vector<PedestrianOverpassFeature> &GetPedestrianOverpassFeatures() const = 0;

  /**
   * @brief 获取当前图层的点类型(普通POI, 行政地名, 公交站, 地铁站, 地铁出入口, 红绿灯, 湖泊标注)
   * @return const std::vector<PointFeature>&
   */
  virtual const std::vector<PointFeature> &GetPointFeatures() const = 0;

  /**
   * @brief 获取当前图层的标牌(道路标牌, 地铁标牌)
   * @return const std::vector<SignFeature>&
   */
  virtual const std::vector<SignFeature> &GetSignFeatures() const = 0;

  /**
   * @brief 获取当前图层的箭头
   * @return const std::vector<ArrowFeature>&
   */
  virtual const std::vector<ArrowFeature> &GetArrowFeatures() const = 0;

  /**
   * @brief 获取当前图层的线文字标注信息
   * @return const std::vector<TextLabelFeature>&
   */
  virtual const std::vector<TextLabelFeature> &GetTextLabelFeatures() const = 0;

  /**
   * @brief 获取当前图层的带标注信息的线(道路线, 地铁线, 铁路线, 河流中线等)
   * @return const std::vector<LineWithLabelFeature>&
   */
  virtual const std::vector<LineWithLabelFeature> &GetLineWithLabelFeatures() const = 0;

  /**
   * @brief 获取当前图层的普通线(背景线, 行政线, 球场线, ZLevel道路线)
   * @return const std::vector<LineFeature>&
   */
  virtual const std::vector<LineFeature> &GetLineFeatures() const = 0;
};

/**
 * @brief Object3d数据层基类
 */
class NERD_EXPORT IObject3DLayer : public IDataLayer {
 public:
  /**
   * @brief 获取BuildingBlock类型
   * @return 获取BuildingBlock枚举
   * */
  MapDataBuildingBlockID GetId() const override = 0;

  /**
   * 获得该瓦片对应的行政区域Code信息
   * 说明: 精确到区的, 非0有效;
   * @return 行政区域码
   */
  virtual AdminCode GetAdminCode() const = 0;

  /**
   * @brief 获取当前图层的精模模型信息
   * @return const std::vector<IObject3DFeatureConstPtr>&
   */
  virtual std::vector<IObject3DFeatureConstPtr> GetObject3DFeatures() const = 0;

  /**
   * @brief 获取当前图层的楼块
   * @return const std::vector<BuildingFeature>&
   */
  virtual const std::vector<BuildingFeature> &GetBuildingFeatures() const = 0;
    
    /**
     * @brief 获取当前图层的l3信息
     * @return const std::vector<L3Feature>&
     */
   virtual const std::vector<L3Feature> &GetL3Features() const = 0;
    
    /**
     * @brief 获取当前图层的l3 windows 信息
     * @return const std::vector<L3WindowsDisplayFeature>&
     */
   virtual const std::vector<L3WindowsDisplayFeature> &GetL3WindowsDisplayFeatures() const = 0;
    

    /**
     * @brief 获取当前图层的l3 索引 信息
     * @return const std::vector<L3IndexFeature>&
     */
   virtual const std::vector<L3IndexFeature> &GetL3IndexFeatures() const = 0;
    
    
  /**
   * @brief 获取当前图层的所有通模模型点(种树, 加油站等)
   * @return const std::vector<ModelFeature>&
   */
  virtual const std::vector<ModelFeature> &GetModelFeatures() const = 0;

  /**
   * @brief 获取当前图层的通模收费站
   * @return const std::vector<TollStationFeature>&
   */
  virtual const std::vector<TollStationFeature> &GetTollStationFeatures() const = 0;

  /**
   * @brief 获取精模与室内图、白模的避让关系
   * @return const std::vector<LandMarkOverlapGroup>
   */
  virtual const std::map<uint64_t , LandMarkOverlapGroup> &GetLaneMarkOverlapGroups() const = 0;

  /**
   * @brief 获取室内图与白模的避让关系
   * @return const std::vector<IndoorOverlapGroup>
   */
  virtual const std::map<uint64_t, IndoorOverlapGroup> &GetIndoorOverlapGroups() const = 0;

  /**
   * @brief 根据POI ID获取对应的建筑物轮廓
   * @param poi_id
   * @return MultiPolygon, nullptr: poi_id不存在
   */
  virtual const std::vector<std::vector<nerd::api::Coordinate>> *GetBuildingContoursByPoiId(uint64_t poi_id) const = 0;

  /**
   * @brief 获取地块数据
   * @return 地块列表
   */
  virtual const std::vector<LandUseFeature> &GetLandUseFeatures() const = 0;

  /**
   * 按类型获取模型范围，搭配: GetModelFeatures 使用
   * @param type 模型类型
   * @return 返回查询类型在 GetModelFeatures 数组中的开始，结束位置，[first, second)
   */
  virtual std::pair<uint32_t, uint32_t> GetModelFeatureByType(DataFeatureType type) const = 0;
    
  /**
   * @brief 获取地块数据
   * @return 地块列表
   */
  virtual const std::vector<AncientFeature> &GetAncientFeatures() const = 0;
};

/**
 * @brief 室内图索引数据层基类
 */
class NERD_EXPORT IIndoorIndexLayer : public IDataLayer {
 public:
  /**
   * @brief 获取当前图层的室内建筑物索引
   * @return const std::vector<IndoorBuildingInfo>
   */
  virtual std::vector<IndoorBuildingInfo> GetIndoorBuildingInfos() const = 0;

  /**
   * @brief 获取室内建筑物白名单信息
   * @return const std::vector<IndoorWhiteInfo>
   */
  virtual std::vector<IndoorWhiteInfo> GetIndoorWhiteInfos() const = 0;
};

/**
 * @brief 三维地形数据层
 */
class NERD_EXPORT IDtmLayer : public IDataLayer {
 public:
  /**
   * 获取dtm三维地形图层数据
   * @return std::shared_ptr<DtmTileData>
   */
  virtual std::shared_ptr<DtmTileData> GetDtmTileData() const = 0;
};

/**
 * @brief 百变地图数据层
 */
class NERD_EXPORT IAgileMapDataLayer : public IDataLayer {
 public:
  /**
   * @brief 获取当前图层的百变点类型数据列表
   * @return 元素列表
   */
  virtual const std::vector<AgileMapPointInfo>& GetAgileMapPointInfo() const = 0;
  /**
   * @brief 获取当前图层的百变线类型数据列表
   * @return 元素列表
   */
  virtual const std::vector<AgileMapLineInfo>& GetAgileMapLineInfo() const = 0;
  /**
   * @brief 获取当前图层的百变面类型数据列表
   * @return 元素列表
   */
  virtual const std::vector<AgileMapRegionInfo>& GetAgileMapRegionInfo() const = 0;
  /**
   * 获取该图层ID, 字符串格式
   * @return 图层唯一ID
   */
  virtual std::string GetDataLayerId() const = 0;
  /**
   * 获取该瓦片动态版本号
   * @return
   */
  virtual uint32_t GetDynamicVersion() const = 0;
  /**
   * 获取该瓦片的静态版本号
   * @return
   */
  virtual uint32_t GetStaticVersion()  const = 0;
};

/**
 * @brief 数据瓦片
 * 重要说明: 按照当前设计, 一个EngineApi只会设置一种类型,因此每个瓦片实际只会存在一种类型的数据,
 *      存储多个对业务使用并不方便, 因此不支持存储多种类型的数据在一个瓦片中;
 *
 * |---- HDLaneLayer -----|
 * |                      |
 * |---- SDLinkLayer -----|
 * |          .           |
 * |          .           |
 * |          .           |
 * |----   ......    -----|
 */
class NERD_EXPORT IDataCube {
 public:
  /**
   * @brief 析构函数
   */
  virtual ~IDataCube() = default;

  /**
   * 获取tileId
   * @return tileId
   */
  virtual nerd::api::TileIDType GetTileId() const = 0;

  /**
   * 获取数据状态
   * @return 数据状态，见 DataStatus
   */
  virtual DataStatus GetStatus() const = 0;

  /**
   * 获取TileId和状态信息的列表
   * 备注: 返回字符串格式的"tile_id,tile_status"; 若是多个瓦片构成会采用"|"进一步拼接
   * @return
   */
  virtual std::string GetTileIdAndStatusStr() const = 0;

  /**
   * 获取当前瓦片数据的种类;
   * @return
   */
  virtual MapDataBuildingBlockID GetBuildingBlockID() const = 0;

  /**
   * 是否海外瓦片(海外数据采用wgs84坐标系)
   * @return true海外瓦片, false:国内瓦片
   */
  virtual bool IsOutOfChina() const = 0;
  /**
   * 获取当前瓦片中数据的坐标系
   * @return 国内瓦片返回gcj02, 国外瓦片返回wgs84
   */
  virtual CoordSysType GetCoordSysType() const = 0;

  /**
   * 获取瓦片的世界图区域类型
   * @return 区域类型
   */
  virtual WorldMapRegionType GetWorldMapRegionType() const = 0;

  /**
   * @brief 高精数据层
   * @return 如果没有则返回nullptr
   */
  virtual IHDLaneLayer *GetHDLaneLayer() const = 0;

  /**
   * @brief 非导航高精数据层
   * @return 如果没有则返回nullptr
   */
  virtual IHDLaneLayer *GetOptionalHDLaneLayer() const = 0;

  /**
   * @brief SD link数据层
   * @return 如果没有则返回nullptr
   */
  virtual ISDLinkLayer *GetSDLinkLayer() const = 0;

  /**
   * @brief Background数据层
   * @return 如果没有则返回nullptr
   */
  virtual IBackgroundLayer *GetBackgroundLayer() const = 0;

  /**
   * @brief Object3D即精模,通模数据层
   * @return 如果没有则返回nullptr
   * */
  virtual IObject3DLayer *GetObject3DLayer() const = 0;

  /**
   * @brief 室内图建筑索引图层
   * @return 如果没有则返回nullptr
   * */
  virtual IIndoorIndexLayer *GetIndoorIndexLayer() const = 0;

  /**
   * @brief 三维地形数据
   * @return 如果没有则返回nullptr
   * */
  virtual IDtmLayer *GetDtmLayer() const = 0;

  /**
   * @brief 获取百变地图数据
   * @return 如果没有则返回nullptr
   */
  virtual IAgileMapDataLayer *GetAgileMapDataLayer() const = 0;

  /**
   * @brief 获取tile对应的Building block的version id，用于判断当前数据是否是最新的
   * @return tile对应的Building block的version id
   */
  virtual VersionIDType GetBBVersionId() const = 0;

  /**
   * @brief 获取tile的数据版本区间
   * @return tile的数据版本区间
   */
  virtual VersionIDRange GetVersionRange() const = 0;

  /**
   * @brief 本瓦片内是否有高精数据存在
   * @return 有或没有
   */
  virtual uint8_t HasHDData() const = 0;

  /**
   * 获取瓦片的语言类型
   * @return 语言类型
   */
  virtual MapDataLanguageType GetLanguageType() const = 0;

  /**
   * 判断当前 data_cube 的 cache 信息是否和配置的 feature 特性一致
   * @param data_features feature配置
   * @return true 相同 false 不相同
   */
  virtual bool IsFeatureMatch(const std::map<DataFeatureType, DataFeatureSet>& data_features) const = 0;

  /**
   * 获取当前cube的所有持有者
   */
  virtual void GetAllHolders(std::vector<ReqOnceKey>& holders) const {}

 private:
  /**
   * 添加持有该瓦片的持有者
   * @param reqKey
   */
  virtual void AddHolder(const ReqOnceKey& reqKey) {}
  /**
   * 移除持有该瓦片的持有者
   * @param reqKey
   */
  virtual void DelHolder(const ReqOnceKey& reqKey) {}

  friend class DataCubeWrapper;
};

}  // namespace api
}  // namespace nerd
