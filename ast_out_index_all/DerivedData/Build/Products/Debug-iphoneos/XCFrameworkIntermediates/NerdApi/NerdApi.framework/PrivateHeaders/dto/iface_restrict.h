// Copyright 2021 Tencent Inc.  All rights reserved.

#pragma once

#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * Link自身限制类型
 */
enum class LinkRestrictType : uint8_t {
  kNone = 0,
  kONEWAY,   // 单行限制
  kACCESS,   // 进入限制
  kTHROUGH,  // 禁止穿行
  kMOBBRI,   // 移动式桥
  kBUS,      // 公交专用
  kBUS_FIRST // 公交优先
};

/**
 * Link自身限制
 */
class NERD_EXPORT ILinkRestrict {
 public:
  virtual ~ILinkRestrict() = default;

  /**
   * 返回Link自身限制类型
   * @return 道路限制类型
   */
  virtual LinkRestrictType GetType() const = 0;

  /**
   * 返回限制方向
   * @return 限制方向
   */
  virtual LinkDirection GetLimitDirection() const = 0;

  /**
   * 获取条件限制时间段。周日周一上午7点~上午8点时间段，标识为"[(h7m0)(h8m0)]*(t7t1)"
   * @return 条件限制时间段，可空
   */
  virtual std::string GetLimitTimePeriod() const = 0;

  /**
   * 获取作用车辆类型
   * @return 作用车辆类型列表，可空
   */
  virtual std::shared_ptr<const std::vector<VehicleType>> GetLimitVehicleTypes() const = 0;

  /**
   * 获取特殊时间类型
   * @return 特殊时间类型，可空
   */
  virtual std::shared_ptr<const std::vector<SpecialTimeType>> GetLimitSpecialTimeTypes() const = 0;
};

typedef std::shared_ptr<ILinkRestrict> ILinkRestrictPtr;
typedef std::shared_ptr<const ILinkRestrict> ILinkRestrictConstPtr;

/**
 * 交限条件
 */
class IRestrictionCond {
 public:
  virtual ~IRestrictionCond() = default;
  /**
   * 获取条件限制时间段。周日周一上午7点~上午8点时间段，标识为"[(h7m0)(h8m0)]*(t7t1)"
   * @return 条件限制时间段，可空
   */
  virtual std::string GetLimitTimePeriod() const = 0;

  /**
   * 获取作用车辆类型
   * @return 作用车辆类型列表，可空
   */
  virtual std::shared_ptr<const std::vector<VehicleType>> GetLimitVehicleTypes() const = 0;

  /**
   * 获取特殊时间类型
   * @return 特殊时间类型，可空
   */
  virtual std::shared_ptr<const std::vector<SpecialTimeType>> GetLimitSpecialTimeTypes() const = 0;
};

typedef std::shared_ptr<IRestrictionCond> IRestrictionCondPtr;
typedef std::shared_ptr<const IRestrictionCond> IRestrictionCondConstPtr;

/**
 * 拓扑交限
 */
class NERD_EXPORT IRestriction {
 public:
  virtual ~IRestriction() = default;
  /**
   * 进入 Link ID
   */
  virtual LinkIDType GetInLinkID() const = 0;
  /**
   * 第一根线与第二根线的交点
   */
  virtual NodeIDType GetNodeID() const = 0;
  /**
   * 退出 Link 序列
   */
  virtual std::vector<LinkIDType> GetPassLinkIDS() const = 0;

  /**
   * 交限条件表
   */
  virtual std::shared_ptr<std::vector<IRestrictionCondConstPtr>> GetRestrictionConds() const = 0;
};

typedef std::shared_ptr<IRestriction> IRestrictionPtr;
typedef std::shared_ptr<const IRestriction> IRestrictionConstPtr;

}  // namespace api
}  // namespace nerd
