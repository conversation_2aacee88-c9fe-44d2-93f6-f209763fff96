/*
 * Copyright (c) 2021 Tencent Inc. All rights reserved.
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

#pragma once

#include <functional>
#include <map>
#include <set>
#include <vector>

#include "api/config.h"
#include "api/data_cube.h"
#include "api/event_bus.h"
#include "nerd_export.h"

#include "dto/version.h"

#include "api/async_callback.h"
#include "nerd_event_listener.h"
#include "map_data_engine_api_callbacks.h"

namespace plog {
class LoggerConfig;
enum class LogLevel;
enum class LogDest;
}  // namespace plog

namespace mapbase {
class WaitableEvent;
}  // namespace mapbase

namespace nerd {
namespace api {

/**
 * @brief 批量获取指定tile数据参数
 */
class NERD_EXPORT GetMapDataBatchParam {
 public:
  GetMapDataBatchParam() = default;
  GetMapDataBatchParam(const GetMapDataBatchParam &other) {
    tile_id_list = std::set<TileIDType>(other.tile_id_list);
    data_lod_config = other.data_lod_config;
  }
  /**
   * 瓦片ID列表
   */
  std::set<TileIDType> tile_id_list;
  /**
   * 数据分层配置
   * 备注: 默认最详细级别, 保持已使用者无需强制适配; 对流量有强要求业务方关注此接口
   */
  DataDetailConfig data_lod_config{LaneDetailLevel::kLaneDetailLevel_Intersection};
  /**
   * 设置 DataFeatureSet,用于设置获取各种数据的时候的层级(如果不填，内部只请求必选图层)
   * 背景说明: 用于业务段根据自己的业务场景定制加载数据; 目前主要渲染使用;
   * kLane无必选图层
   * kOBJECTS_3D的必选图层为 kObject3DTable,  kTollStationTileTable, kLandmarkOverlapIndoorLayer
   * @details 具体设置细节情参考: [拆表能力](https://iwiki.woa.com/p/4010644215)
   */
  std::map<DataFeatureType, DataFeatureSet> feature_set_map = {{DataFeatureType::kLane, {1}}};
};

/**
 * @brief 批量获取指定tile数据结果
 */
class NERD_EXPORT GetMapDataBatchResult {
 public:
  /**
   * 返回结果状态: 若状态码为0, 数据有效; 非0数据数据无效
   * 备注: 具体异常参考error_code;
   */
  RetMessage ret{0, ""};
  /**
   * 返回数据的BB类型, 请求一致;
   */
  MapDataBuildingBlockID data_source{MapDataBuildingBlockID::kNONE};
  /**
   * 返回瓦片数据共享指针;
   */
  std::vector<std::shared_ptr<IDataCube>> data_cubes;
  /**
   * 标识当前返回的数据是在线数据(含在线缓存)还是离线数据;
   */
  bool is_online_result{true};
};

/**
 * @brief 批量获取指定tile路况数据
 */
class NERD_EXPORT GetTrafficStatusBatchParam {
 public:
  bool test_is_offline{false};  // 测试使用：离线数据+在线路况或在线数据+在线路况，将来应该只会有在线路况+在线数据模式
  bool use_zip{false};          // 是否压缩
  int level{0};  // 路况显示级别，目前复用渲染级别(备注:仅当使用kLine类型路况时需要设置)
  float ratio{1.0};                      // 道路拓宽比例，
  TrafficType type{TrafficType::kArea};  // 类型(SD路况,HD路况)
  std::set<TileIDType> tile_id_list;     // 瓦片ID列表
};

/**
 * @brief 批量获取指定tile数据结果
 * @return tileid到拥堵状态映射
 */
class NERD_EXPORT GetTrafficStatusBatchResult {
 public:
  /**
   * 返回结果状态: 若状态码为0, 数据有效; 非0数据数据无效
   */
  RetMessage ret{0, ""};
  /**
   * 瓦片ID与瓦片数据接口
   */
  std::shared_ptr<std::map<TileIDType, ITrafficRoadStatusConstPtr>> traffic_status_tiles;
};

/**
 * @brief 批量获取指定封路瓦片参数
 */
class NERD_EXPORT GetCloseStatusBatchParam {
 public:
  CloseType type{CloseType::kZLevel};
  std::set<TileIDType> tile_id_list;
};

/**
 * @brief 批量获取指定封路瓦片结果
 * @return tileid到拥堵状态映射
 */
class NERD_EXPORT GetCloseStatusBatchResult {
 public:
  /**
   * 返回结果状态: 若状态码为0, 数据有效; 非0数据数据无效
   */
  RetMessage ret{0, ""};
  std::shared_ptr<std::map<uint32_t, ICloseRoadStatusConstPtr>> close_status_tiles;
};

/**
 * @brief 获取指定范围数据参数
 */
class NERD_EXPORT GetMapDataByRectParam {
 public:
  /**
   * 级别
   */
  MapLevel level{MapLevel::kLEVEL_NONE};
  /**
   * 矩形范围 {p0: 左下角; p1: 右上角}
   */
  Rect rect;
  /**
   * 数据分层配置
   * 备注: 默认最详细级别, 保持已使用者无需强制适配; 对流量有强要求业务方关注此接口
   */
  DataDetailConfig data_lod_config{LaneDetailLevel::kLaneDetailLevel_Intersection};
  /**
   * 设置 DataFeatureSet,用于设置获取各种数据的时候的层级(如果不填，内部只请求必选图层)
   * 背景说明: 用于业务段根据自己的业务场景定制加载数据;
   * kRoute 可选数据:
   *   kRouteLinkAttribute,       Link语义信息, 子类型具体参考类型定义
   *   kRouteLinkExperienceSpeed, Link经验速度, 无子类型, 填充0
   *   kRouteLinkCoverageStatus,  Link覆盖标记, 无子类型, 填充0
   *   kRouteLinkHdAir，          HdAir数据，无子类型, 填充0
   *   kRouteLinkSdPlus,          SD+数据，子类型具体参考类型定义
   */
  std::map<DataFeatureType, DataFeatureSet> feature_set_map;
};

/**
 * @brief 获取指定范围数据结果
 */
class NERD_EXPORT GetMapDataByRectResult {
 public:
  /**
   * 返回结果状态: 若状态码为0, 数据有效; 非0数据数据无效
   */
  RetMessage ret{0, ""};
  /**
   * 返回数据的BB类型, 请求一致;
   */
  MapDataBuildingBlockID data_source{MapDataBuildingBlockID::kNONE};
  /**
   * 返回瓦片数据共享指针;
   */
  std::unique_ptr<IDataCube> data_cube;
  /**
   * 标识当前返回的数据是在线数据(含在线缓存)还是离线数据;
   */
  bool is_online_result{true};
};

/**
 * @brief 获取指定ID对应的数据时的类型
 */
enum class GetModelDataByIDType {
  kModel = 0,
  kTexture = 1,
  kMaterial = 2,
};

/**
 * @brief 获取指定ID对应的模型数据参数
 */
class NERD_EXPORT GetModelDataByIDParam {
 public:
  // 类型: model, texture, material
  GetModelDataByIDType type{GetModelDataByIDType::kModel};
  // ID
  std::string id;
  // 版本号
  uint32_t version{0};
  // 是否为通模: true -> 通模, false -> 精模
  bool is_template{false};
  // 渲染不适用，仅透传给nerd，nerd对数据打上tile的标记用于缓存清理
  TileIDType tile_id{0};

 public:
  bool operator==(const GetModelDataByIDParam &param) const {
    return std::tie(type, id, version, is_template) == std::tie(param.type, param.id, param.version, is_template);
  }

  bool operator<(const GetModelDataByIDParam &param) const {
    return std::tie(type, id, version, is_template) < std::tie(param.type, param.id, param.version, is_template);
  }

  bool IsValid() const { return !id.empty(); }

  std::string ToString() const {
    return "{" + std::to_string(static_cast<int>(type)) + "," + id + "," + std::to_string(version) + "," +
           std::to_string(is_template) + "}";
  }
};

/**
 * @brief 获取指定ID对应的模型数据结果
 */
class NERD_EXPORT GetModelDataByIDResult {
 public:
  RetMessage ret{0, ""};
  // 类型: model, texture, material
  GetModelDataByIDType type;
  // 模型数据, 类型为model时有效
  std::shared_ptr<BodyGeometry> body_geometry;
  // 纹理数据, 类型为texture时有效
  std::shared_ptr<TextureMap> texture_map;
  // 材质数据, 类型为material时有效
  std::shared_ptr<Material> material;
};

/**
 * @brief 获取指定ID对应的室内图参数
 */
class NERD_EXPORT GetIndoorDataByIDParam {
 public:
  /**
   * 室内图建筑唯一ID
   */
  uint64_t id{0};
  /**
   * 版本号
   */
  uint32_t version{0};
  /**
   * 渲染不适用，仅透传给nerd，nerd对数据打上tile的标记用于缓存清理
   * 来自从室内图索引图层中的信息;
   */
  TileIDType tile_id{0};

 public:
  bool operator==(const GetIndoorDataByIDParam &param) const {
    return std::tie(id, version) == std::tie(param.id, param.version);
  }

  bool operator<(const GetIndoorDataByIDParam &param) const {
    return std::tie(id, version) < std::tie(param.id, param.version);
  }

  bool IsValid() const { return id != 0; }

  std::string ToString() const { return "{" + std::to_string(id) + "," + std::to_string(version) + "}"; }
};

/**
 * @brief 获取指定ID对应的室内图数据结果
 */
class NERD_EXPORT GetIndoorDataByIDResult {
 public:
  /**
   * 返回结果状态: 若状态码为0, 数据有效; 非0数据数据无效
   * 备注: 具体异常参考error_code;
   */
  RetMessage ret{0, ""};
  /**
   * 若查询成功, 建筑数据
   */
  std::shared_ptr<IIndoorBuilding> indoor_building{nullptr};
};

/**
 * @brief 清除本地缓存参数
 */
class NERD_EXPORT ClearLocalCacheParam {
 public:
  // 超时回调发送时间, -1表示不发送
  int32_t timeout_ms{-1};
  // 是否清理文件缓存
  bool clear_file_cache{true};
  // 是否清理内存缓存
  bool clear_memory_cache{true};
};

/**
 * @brief 在线特别数据类型
 */
enum class OnlineFeatureType {
  /**
   * 路口内 LaneGroup
   */
  kIntersectionLaneGroup = 0,
};

/**
 * @brief 在线特别数据网络查询参数
 */
class NERD_EXPORT GetOnlineFeatureBatchParam {
 public:
  /**
   * 数据类型
   * BuildingBlockID会追随使用的引擎配置
   */
  OnlineFeatureType online_feature_type{OnlineFeatureType::kIntersectionLaneGroup};
  /**
   * 瓦片特别数据 id 清单
   * Key: TileID, Value: 瓦片内要素ID;
   */
  std::map<nerd::api::TileIDType, std::vector<uint32_t>> tile_feature_ids;
};

class OnlineFeatureResponseBuilder;
/**
 * @brief 在线特别数据请求结果
 */
class NERD_EXPORT OnlineFeatureResponse {
 public:
  ~OnlineFeatureResponse() {
    if (online_feature_type_ == OnlineFeatureType::kIntersectionLaneGroup && features_.lane_group_features != nullptr) {
      delete features_.lane_group_features;
      features_.lane_group_features = nullptr;
    }
  }

  /**
   * 提取 路口内 LaneGroup 数组
   * @return
   */
  std::unique_ptr<std::vector<ILaneGroupPtr>> GetIntersectionLaneGroupVec() {
    if (online_feature_type_ == OnlineFeatureType::kIntersectionLaneGroup) {
      auto *ptr = features_.lane_group_features;
      features_.lane_group_features = nullptr;
      return std::unique_ptr<std::vector<ILaneGroupPtr>>(ptr);
    }
    return nullptr;
  }

  /**
   * 获取响应中元素个数, 用于打印日志
   * @return 返回元素个数
   */
  uint32_t GetFeatureCount() const {
    if (online_feature_type_ == OnlineFeatureType::kIntersectionLaneGroup && features_.lane_group_features != nullptr) {
      return features_.lane_group_features->size();
    }
    return 0;
  }

  /**
   * 获取数据版本号
   * @return
   */
  VersionIDType GetDataVersion() const { return data_version_; }

 private:
  friend class OnlineFeatureResponseBuilder;
  /**
   * 返回数据类型
   */
  OnlineFeatureType online_feature_type_{OnlineFeatureType::kIntersectionLaneGroup};
  /**
   * 数据版本
   */
  VersionIDType data_version_{0};
  /**
   * 返回数据
   */
  union {
    std::vector<ILaneGroupPtr> *lane_group_features;
  } features_{nullptr};
};

/**
 * @brief 百变数据请求参数
 */
struct NERD_EXPORT GetAgileMapDataBatchParam {
  struct TileLayerInfo {
    // 图层id
    std::string layer_id_;
    // 瓦片id
    TileIDType tile_id_;
    // meta_version必须是一个uint32_t的string
    std::string meta_version_{""};
    // 刷新间隔
    int64_t interval_{10};
  };
  /**
   * 百变sdk 请求图层id_瓦片ID列表
   */
  std::vector<TileLayerInfo> param_list_;
  /**
   * 是否强制请求，默认false
   * 强制刷新时，会忽略缓存，直接请求数据
   */
  bool force_request_ = false;

  GetAgileMapDataBatchParam() = default;
  GetAgileMapDataBatchParam(const GetAgileMapDataBatchParam &other) {
    param_list_ = other.param_list_;
    force_request_ = other.force_request_;
  }
};

/**
 * @brief 百变数据返回结果
 */
class NERD_EXPORT GetAgileMapDataBatchResult {
public:
  explicit GetAgileMapDataBatchResult() {
  }

  /**
   * 返回瓦片数据共享指针;
   */
  std::map<std::pair<std::string, TileIDType>, std::shared_ptr<IDataCube>> data_cubes_;
  // 下载/更新/解析情况
  std::map<std::pair<std::string, TileIDType>, api::DataStatus> tile_status;
  // 下载tile的监听事件
  std::map<std::pair<std::string, TileIDType>, std::weak_ptr<mapbase::WaitableEvent>> downloading_tile_events_;
};

class NERD_EXPORT AgileMapDataCallback : public AsyncCallback<GetAgileMapDataBatchParam, GetAgileMapDataBatchResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  virtual void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetAgileMapDataBatchParam> req,
                        std::unique_ptr<GetAgileMapDataBatchResult> rsp) = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  virtual void OnCanceled(JobId job_id, bool success, const RetMessage &ret) = 0;
};

/**
 * @brief 异步批量获取指定tile数据基类, 需要使用方提供实现类
 */
class NERD_EXPORT MapDataResultCallback : public AsyncCallback<GetMapDataBatchParam, GetMapDataBatchResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetMapDataBatchParam> req,
                std::unique_ptr<GetMapDataBatchResult> rsp) override = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  void OnCanceled(JobId job_id, bool success, const RetMessage &ret) override = 0;
};

/**
 * @brief 异步批量获取指定tile数据基类, 需要使用方提供实现类
 */
class NERD_EXPORT TrafficStatusResultCallback
    : public AsyncCallback<GetTrafficStatusBatchParam, GetTrafficStatusBatchResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetTrafficStatusBatchParam> req,
                std::unique_ptr<GetTrafficStatusBatchResult> rsp) override = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  void OnCanceled(JobId job_id, bool success, const RetMessage &ret) override = 0;
};

/**
 * @brief 异步批量获取指定tile数据基类, 需要使用方提供实现类
 */
class NERD_EXPORT CloseStatusResultCallback
    : public AsyncCallback<GetCloseStatusBatchParam, GetCloseStatusBatchResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetCloseStatusBatchParam> req,
                std::unique_ptr<GetCloseStatusBatchResult> rsp) override = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  void OnCanceled(JobId job_id, bool success, const RetMessage &ret) override = 0;
};

/**
 * @brief 异步获取指定范围数据基类，需要使用方提供实现类
 */
class NERD_EXPORT MapDataByRectResultCallback : public AsyncCallback<GetMapDataByRectParam, GetMapDataByRectResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetMapDataByRectParam> req,
                std::unique_ptr<GetMapDataByRectResult> rsp) override = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  void OnCanceled(JobId job_id, bool success, const RetMessage &ret) override = 0;
};

/**
 * @brief 异步获取指定ID数据基类，需要使用方提供实现类
 */
class NERD_EXPORT ModelDataByIDResultCallback : public AsyncCallback<GetModelDataByIDParam, GetModelDataByIDResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetModelDataByIDParam> req,
                std::unique_ptr<GetModelDataByIDResult> rsp) override = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  void OnCanceled(JobId job_id, bool success, const RetMessage &ret) override = 0;
};

/**
 * @brief 异步获取指定ID数据基类，需要使用方提供实现类
 */
class NERD_EXPORT IndoorDataByIDResultCallback : public AsyncCallback<GetIndoorDataByIDParam, GetIndoorDataByIDResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetIndoorDataByIDParam> req,
                std::unique_ptr<GetIndoorDataByIDResult> rsp) override = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  void OnCanceled(JobId job_id, bool success, const RetMessage &ret) override = 0;
};

/**
 * @brief 清除本地缓存回调基类
 */
class NERD_EXPORT ClearLocalCacheCallback {
 public:
  virtual ~ClearLocalCacheCallback() = default;

  /**
   * @brief 清除本地缓存回调
   * @param job_id 任务id
   * @param status 0: 清理成功, 1: 清理失败, 2: 清理中时重复调用, 3: 清理超时
   */
  virtual void OnResult(JobId job_id, int status) = 0;
};

/**
 * @brief 异步批量获取指定tile数据基类, 需要使用方提供实现类
 */
class NERD_EXPORT OnlineFeatureCallback : public AsyncCallback<GetOnlineFeatureBatchParam, OnlineFeatureResponse> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  virtual void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetOnlineFeatureBatchParam> req,
                        std::unique_ptr<OnlineFeatureResponse> rsp) = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  virtual void OnCanceled(JobId job_id, bool success, const RetMessage &ret) = 0;
};

/**
 * @brief 基础API基类, 使用方通过该类调用相应实现方法
 */
class NERD_EXPORT MapDataEngineApi : public IObserver {
 public:
  ~MapDataEngineApi() override = default;

  /**
   * @brief 获取当前引擎的配置
   * @return 引擎配置
   */
  virtual const APIConfig &GetAPIConfig() const = 0;

  /**
   * @brief 返回EngineApi的唯一标识
   * @return 引擎ID
   */
  virtual ApiId GetAPIId() const = 0;

  /**
   * @brief 动态设置引擎使用的在离线模式
   * @param preference 在离线模式
   * @return 设置是否成功
   */
  virtual bool SetDataPreference(DataPreference preference) = 0;

  /**
   * 动态切换世界图开关状态
   * 说明: 1> 目前只有渲染调用,BMD 和 OBJ支持世界图; 2> 改变开关后重新加载的瓦片生效
   * @param isCountry 是否设定全国级别(全国级别指数据级别3/5/7, 更大为城市级别) true:全国级别,false:城市级别
   * @param enable    是否开启, true:开启状态, false:关闭状态
   * @return
   */
  virtual bool SetSupportWorldMapEnable(bool isCountry, bool enable) = 0;
  /**
   * 获取世界图开关状态
   * @param isCountry 是否是全国级别, true:全国级别, false:城市界别
   * @return true:开启状态, false:关闭状态
   */
  virtual bool IsSupportWorldMap(bool isCountry) = 0;

  /**
   * @brief 设置缓存块个数
   * @param block_count: 0: 表示tile数据可以全部加载到内存; other: 加载指定tile个数到内存
   * @attention 如果是客户端场景，最好使用经验值16，如果为其它值，请验证内存资源是否充足，否则会出现分配内存失败
   *            如果是服务器场景，为了效率可以全部加载到内存
   */
  virtual void ChangeCacheSize(uint32_t block_count) = 0;

  /**
   * 清理内存缓存
   */
  virtual void ClearCache() = 0;

  /**
   * @deprecated
   * @brief 加载所有tile到内存缓存
   * @attention 要评估内存是否满足，否则会导致内存分配失败，甚至进程被操作系统kill掉
   */
  virtual std::vector<TileIDType> LoadAllDataCubes() = 0;

  /**
   * @brief 判断tile列表是不是都是高精数据，由上层渲染引擎同步调用，
   * @param param 参数列表
   * @return 返回数据状态和结果
   */
  virtual CheckHDDataResult CheckHDData(const TileIDTypeVec &param) = 0;

  /**
   * @brief 判断Rect范围是不是都是高精数据，由上层渲染引擎同步调用，
   * @param rect
   * @return 返回数据状态和结果
   */
  virtual CheckHDDataResult CheckHDDataByRect(const Rect &rect) = 0;

  /**
   * @brief 异步批量拉取Tile内的数据
   * @param param 参数列表
   * @param callback 回调对象，如果回调提前失效，则不再回调
   * @return 任务id
   */
  virtual JobId AsyncGetDataByTileId(const GetMapDataBatchParam &param,
                                     std::weak_ptr<MapDataResultCallback> callback) = 0;

  /**
   * 异步批量拉取百变地图数据
   * @param param 参数列表，对于每个layer下的tile， 应填写std::make_pair<layer_id, tileid>
   * @param callback 回调对象
   * @return
   */
  virtual JobId AsyncGetAgileMapData(const GetAgileMapDataBatchParam &param,
                                       std::weak_ptr<AgileMapDataCallback> callback) = 0;

  /**
   * 设置百变地图的图层的zoom_level配置
   * @param layer_id 图层名称
   * @param zoom_info 显示等级， 形如[-1,4,4,4,4,4,4,4,7,7,7,7,11,11,11,11,11,11,11,11,11];
   *    代表缩放等级为1-7时使用7级tile请求， 8-11 使用7级tile请求，以此类推
   */
  virtual void SetAgileLayerZoomInfo(const std::string& layer_id,
                                     const std::vector<int>& zoom_info) = 0;

  /**
   * @brief 异步获取指定区域内的数据
   * @param param 参数列表
   * @param callback 回调对象，如果回调提前失效，则不再回调
   * @note 备注: 1. 该接口仅当通过MapDataBuildingBlockID为 kROUTE 和 kLANE 类型的情况下返回值有效;
   *            2.  获取渲染类数据需通过瓦片类型方式获取
   * @return 任务id
   */
  virtual JobId AsyncGetDataByRect(const GetMapDataByRectParam &param,
                                   std::weak_ptr<MapDataByRectResultCallback> callback) = 0;

  /**
   * @brief 异步获取指定ID对应的模型数据
   * @param param 查询参数
   * @param callback 回调对象, 如果回调提前失效, 则不再回调
   * @return 任务id
   */
  virtual JobId AsyncGetModelDataByID(const GetModelDataByIDParam &param,
                                      std::weak_ptr<ModelDataByIDResultCallback> callback) = 0;

  /**
   * @brief 异步获取指定ID对应的室内图数据
   * @param param 查询参数
   * @param callback 回调对象, 如果回调提前失效, 则不再回调
   * @return 任务id
   */
  virtual JobId AsyncGetIndoorDataByID(const GetIndoorDataByIDParam &param,
                                       std::weak_ptr<IndoorDataByIDResultCallback> callback) = 0;

  /**
   * @brief 异步批量请求路况状态数据
   * @param param  批量路况请求参数
   * @param callback 路况结果回调
   * @param format_version 请求的格式版本，比如 "4.0"，空值会被认为无效值
   * @param version_id 请求的数据版本
   * @return 任务ID
   */
  virtual JobId AsyncGetTrafficStatusByTileId(const GetTrafficStatusBatchParam &param,
                                              std::weak_ptr<TrafficStatusResultCallback> callback,
                                              const std::string &format_version, VersionIDType version_id) = 0;

  /**
   * @brief 异步批量请求封路状态数据
   * @param param  批量封路请求参数
   * @param callback 路况结果回调
   * @param format_version 请求的格式版本，比如 "4.0"，空值会被认为无效值
   * @param version_id 请求的数据版本
   * @return 任务ID
   */
  virtual JobId AsyncGetCloseStatusByTileId(const GetCloseStatusBatchParam &param,
                                            std::weak_ptr<CloseStatusResultCallback> callback,
                                            const std::string &format_version, VersionIDType version_id) = 0;

  /**
   * @brief 按照 ID 查询服务,获取数据, 用于获取本地默认不下发的特殊数据
   * @param param          要素参数信息
   * @param callback       结果回调
   * @return 任务ID
   */
  virtual JobId AsyncGetOnlineFeatureById(const GetOnlineFeatureBatchParam &param,
                                          std::weak_ptr<OnlineFeatureCallback> callback) = 0;

  /**
   * @brief 异步批量获取龙门架电子牌的动态显示信息, 支持批量
   * @param param      要素参数信息
   * @param callback   结果回调
   * @return 任务ID
   */
  virtual JobId AsyncGetGantryBoardByIds(const GetGantryBoardBatchParam &param,
                                         std::weak_ptr<GetGantryBoardCallback> callback) = 0;

  /**
   * 异步批量获取龙门架电子牌的模型资源信息, 支持批量
   * @param param    要素参数信息
   * @param callback 结果回调
   * @return 任务ID
   */
  virtual JobId AsyncGetGantryResourceByIds(const GetGantryResourceBatchParam &param,
                                            std::weak_ptr<GetGantryResourceCallback> callback) = 0;

  /**
   * 取消指定任务
   * @param job_id
   */
  virtual void Cancel(JobId job_id) = 0;

  /**
   * 取消所有任务
   * 备注: 对应该Engine的所有任务
   */
  virtual void CancelAll() = 0;

  /**
   * @brief 离线模式下获得所有 tileId
   * 备注: 根据ApiConfig中的BB ID读取指定类型数据的瓦片ID, 仅在 offline_debug 为true时有效
   * @param tileIdList 瓦片ID 列表
   */
  virtual void GetAllTileIDs(std::vector<TileIDType> &tileIdList) = 0;

 public:  // 重载 IObserver
  /**
   * @brief 通知缓存预加载和rect相交的tile
   * @param param 预加载参数
   * @attention 如果每次更新坐标变化特别大，会导致缓存频繁替换，降级性能
   *            另外如果上层持有被缓存淘汰的tile元素句柄，内存也会增加
   */
  void NotifyLocation(const NotifyLocationParam &param) override = 0;
};

/**
 * @brief 清除本地数据, 必须在InitGlobalConfig之前调用
 * @param data_path 数据路径
 * @param log_path 日志路径
 * @param online_only 仅删除在线缓存
 */
NERD_EXPORT void ClearAllCacheBeforeInit(const std::string &data_path, const std::string &log_path, bool online_only);

/**
 * @brief 初始化全局配置，端上/盘古侧设置，应在其他模块初始化之前设置
 * @param config 全局设置
 */
NERD_EXPORT void InitGlobalConfig(const GlobalConfig &config);

/**
 * 销毁资源
 */
NERD_EXPORT void GlobalDestroy();

/**
 * @brief 切入后台，暂停nerd的定时任务，清空缓存，nerd_service进入休眠
 * @warning: 开平给微信可能会用，支持后台导航的不可以调用;
 */
NERD_EXPORT void PauseNerdService();
/**
 * @brief 唤醒nerd_service, 重新注册定时任务
 * @warning: 开平给微信可能会用，支持后台导航的不可以调用;
 */
NERD_EXPORT void ResumeNerdService();
/**
 * 注册Nerd事件回调(调试信息类)
 * @param listener_holder
 */
NERD_EXPORT void SetNerdEventListener(std::shared_ptr<NerdEventListener> listener_holder);
/**
 * 注册Nerd事件回调（全局功能事件，例如请求保留在线数据的中心点位）
 * @param listener_holder
 */
NERD_EXPORT void SetNerdGlobalEventListener(std::shared_ptr<NerdGlobalEventListener> listener_holder);
/**
 * @brief 切换语言
 * @param language_type 语言类型
 * @return 是否切换成功
 */
NERD_EXPORT bool SetGlobalLanguage(MapDataLanguageType language_type);
/**
 * @brief 获取语言
 * @return 当前语言
 */
NERD_EXPORT MapDataLanguageType GetGlobalLanguage();


/**
 * @brief 是否支持L3 Building
 * @return
 */
NERD_EXPORT bool GetGlobalSupportL3Building();

/**
 * @brief 离线文件更新时，重新加载
 */
NERD_EXPORT void ReloadOfflineData();

/**
 * @brief 创建数据引擎api实例
 * @param config: API配置信息
 * @return 引擎实例，如果失败则返回nullptr
 */
NERD_EXPORT std::unique_ptr<MapDataEngineApi> CreateEngine(const APIConfig &config);

/**
 * 获取指定数据的元信息，含版本等
 * @param db_file_path 文件路径
 * @param bb_id building block类型
 * @param metadata 返回值，数据元信息
 * @return 是否获取成功，文件不存在等情况下会返回false
 */
NERD_EXPORT bool GetDbMetadata(const std::string &db_file_path, MapDataBuildingBlockID bb_id, NdsMetadata &metadata);

/**
 * 获取building block的格式版本
 * @param bb_id building block的id
 * @return 版本信息
 */
NERD_EXPORT FormatVersion GetFormatVersionByBuildingBlock(MapDataBuildingBlockID bb_id);

/**
 * @brief 获取日志配置信息，用于更改输出日志等级，必须在InitLogger之后才可调用
 * @return 日志配置信息
 */
NERD_EXPORT std::shared_ptr<plog::LoggerConfig> GetLoggerConfig();

/**
 * @brief 配置数据引擎日志目录
 * @warning 该方法是初始化base日志库方法，在盘古中无需调用
 * @param path 日志目录
 * @param level 日志级别
 * @param dest 日志输出位置
 * @param log_name_prefix 日志名字前缀，默认"nerd"
 * @param single_log_size_limit 单个日志文件大小限制，单位为Byte，默认配置(200*1024*1024=200MB)
 */
NERD_EXPORT void InitLogger(const std::string &path, plog::LogLevel level, plog::LogDest dest,
                            const std::string &log_name_prefix = "nerd", const size_t &single_log_size_limit = 200 * 1024 * 1024);

/**
 * @brief 指定nerd中数据请求的版本，nerd中默认请求最新的tile数据，指定版本后，只请求对应版本的数据。
 * 如果设置的数据版本在服务端获取不到数据，则后续查询请求返回空结果。
 * 只支持在线。
 * tips: 指定版本version_id为0，则表示释放固定的数据版本（使用线上最新版本数据）
 * @param bb_id building block类型，见 MapDataBuildingBlockID, 暂时只支持高精数据
 * @param format_version 格式版本，比如 "5.0", 内部已经不再参考;
 * @param version_id 数据版本号（指定为0，释放固定数据版本，使用线上最新版本数据）
 * @return 是否设置成功，如果未初始化或格式版本不兼容，则设置失败
 */
NERD_EXPORT bool SetDataRequestVersion(MapDataBuildingBlockID bb_id, const std::string &format_version,
                                       VersionIDType version_id);

/**
 * @brief 异步清除本地缓存
 * @param param
 * @param callback
 * @return
 */
NERD_EXPORT JobId AsyncClearLocalCache(const ClearLocalCacheParam &param,
                                       std::weak_ptr<ClearLocalCacheCallback> callback);

/**
 * @brief 获取tile所在的行政区编号集合
 * 备注：1.仅使用分城市离线数据时有效，且依赖datamanager拉取adcode配置文件成功
 *      2.该接口会有几十毫秒耗时，用于数据库操作
 * @param tile_id
 * @param adcodes 返回值，行政区编号集合
 * @return 是否成功，如返回失败，一般是adcode配置文件加载失败
 */
NERD_EXPORT bool getAdCodesByTileId(const TileIDType tile_id, std::vector<AdCodeType>& adcodes);

/**
 * 获取高精数据的pv(product version)版本信息;
 * 备注: 高精数据编译规格的一种定义, 在数据引擎和诱导服务/Tile服务之间生效, 非FV和BBV;
 * 注意: 初始化完Nerd调用, 改值不会在应用使用过程中动态变化; 因此每次启动获取一次即可；
 * @return <=0 无效, 其他有效;
 */
NERD_EXPORT int GetProductVersionForHD();

/**
 * @brief 设置是否支持数据差分更新模式
 * @param is_support_diff_update 是否支持差分更新, true:支持; false:不支持;
 * 内部默认值支持, 启动过程也可以通过初始化配置设置;
 */
NERD_EXPORT void SetDataDiffUpdateEnable(bool is_support_diff_update);

/**
 * @brief 获取当前网络状态
 * @return 当前网络状态
 */
NERD_EXPORT NetworkStatus GetNetworkStatus();

/**
 * @brief 设置当前网络状态
 * @param network_status 当前网络状态
 */
NERD_EXPORT void SetNetworkStatus(NetworkStatus network_status);

/**
 * 获取OnlineFileMgr 工作状态
 * @return kNotInited: 未初始化， kInitedDB: 锁定数据库（独立或主NerdService), kInitedBinaryCache(无数据库工作或子NerdService)
 */
NERD_EXPORT OnlineFileMgrWorkStatus GetOnlineFileWorkStatus();

/**
 * @brief 获取当前网络条件
 * @return 当前网络条件
 */
NERD_EXPORT NetworkCondition GetNetworkCondition();

/**
 * @brief 设置当前网络条件
 * @param network_condition 当前网络条件
 */
NERD_EXPORT void SetNetworkCondition(NetworkCondition network_condition);

/**
 * 获取引擎的全局配置信息
 * @return
 */
NERD_EXPORT std::shared_ptr<GlobalConfig> GetGlobalConfig();

/**
 * @brief 获取tile周边的tile列表
 * @param tile_id
 * @param extend_row_col_num 需要拓展的行列数量，含义为上下扩展的行数，以及左右扩展的列数。小于等于0时返回为空，默认为1。
 *      例如：extend_row_col_num = 1，表示往上下左右各拓展1行1列，范围内总共9个tile，最终拓展了8个tile，返回8个tile id
 *      例如：extend_row_col_num = 2，表示往上下左右各拓展2行2列，范围内总共25个tile，最终拓展了24个tile，返回24个tile id
 *      例如：extend_row_col_num = 3，表示往上下左右各拓展3行3列，范围内总共49个tile，最终拓展了48个tile，返回48个tile id
 * @return 周边的tile tuple列表，每个tuple元素包含：(tile id，行号，列号)
 */
NERD_EXPORT std::vector<std::tuple<nerd::api::TileIDType, int32_t, int32_t>> GetAroundTiles(nerd::api::TileIDType tile_id, int32_t extend_row_col_num = 1);

}  // namespace api
}  // namespace nerd
