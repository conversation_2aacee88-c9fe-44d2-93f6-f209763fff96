//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by weidonghua on 2023/4/21.
//

#pragma once

#include <memory>
#include <string>
#include <vector>
#include "dto/types.h"
#include "nerd_export.h"

namespace nerd {
namespace api {

/**
 * 室内图图层类型
 */
enum class IndoorLayerType : uint8_t {
  kINDOOR_FRAME_REGION,           // 暂时无对应图层
  kINDOOR_SHOP_REGION,            // 暂时无对应图层
  kINDOOR_ASSISTANT_REGION,       // 暂时无对应图层
  kINDOOR_FACILITIES_REGION,      // 暂时无对应图层
  kINDOOR_REGION,                 // 对应于室内图的region图层
  kINDOOR_SHOP_POINT,             // 暂时无对应图层
  kINDOOR_PUBLIC_SERVICE,         // 暂时无对应图层
  kINDOOR_ESCALATOR,              // 暂时无对应图层
  kINDOOR_POI_LABEL,              // 暂时无对应图层
  kINDOOR_POI,                    // 对应于室内图的poi图层
  kOLD_PARTING_LINE,              // 暂时无对应图层
  kINDOOR_LINE,                   // 对应于室内图的line图层
  kINDOOR_ROAD_ARROW,             // 对应室内图的的road_arrow图层
  kINDOOR_UNKNOWN,                // 未知类型
};

/**
 * 室内图建筑Region的类型
 * 与BuildingFlag组合使用
 */
enum class BoxType : int8_t {
  kOTHER = -1,
  kDOOR = 0,
  kBRICK,
  kWALL,
  kPLANE,
  k3D,
  kPLANE_3D
};
/**
 * 室内图建筑Region的标志
 * 与BoxType组合时使用
 */
enum class BuildingFlag : uint32_t {
  kBUILDING_NORMAL = 0,
  kBUILDING_HOLE = 1,
  kBUILDING_FLOOR = 2,
  kBUILDING_3D = 4,
  kBUILDING_ESCALATOR = 8
};
/**
 * BoxType 与 BuildingFlag 组合使用举例:
 * kBUILDING_3D | kBUILDING_NORMAL + kBRICK：有顶cell
 * kBUILDING_3D | kBUILDING_NORMAL + kWALL：无顶cell
 * kBUILDING_3D | kBUILDING_HOLE + kWALL：坑
 * kBUILDING_3D | kBUILDING_FLOOR + kWALL：楼层整体
 * kBUILDING_3D | kBUILDING_NORMAL + kPLANE：区域面
 * kBUILDING_3D | kBUILDING_ESCALATOR + k3D：电梯
 * kBUILDING_3D | kBUILDING_NORMAL + kOTHER：丢弃
*/

/**
 * 室内图坐标，像素坐标
 */
struct IndoorPoint {
  int32_t x;
  int32_t y;
};

/**
 * 室内图小数位坐标，小数点2位后*100
 */
struct IndoorPointFloat {
  char x;
  char y;
};

/**
 * 精度到小数位坐标
 */
struct Indoor4KPoint {
  float x;
  float y;
  float z;

  void set(float _x, float _y, float _z) {
    x = _x;
    y = _y;
    z = _z;
  }
};

/**
 * 矩形框，像素坐标
 */
struct IndoorRect {
  int32_t left;
  int32_t top;
  int32_t right;
  int32_t bottom;
};

/**
 * 室内图索引中的建筑物信息
 */
struct NERD_EXPORT IndoorBuildingInfo {
  /**
   * 建筑物id, 用于获取具体室内图数据接口
   */
  uint64_t bld_id;
  /**
   * 建筑物自身的版本号, 用于获取具体室内图数据接口
   */
  uint32_t version;
  /**
   * 开始显示等级
   */
  uint32_t show_level;
  /**
   * 包围盒,像素坐标
   */
  IndoorRect bounding_box;
  /**
   * 该包围框覆盖的15级瓦片ID列表
   */
  nerd::api::TileIDTypeVec covered_tile_ids;
};

/**
 * 室内图白名单数据
 */
struct NERD_EXPORT IndoorWhiteInfo {
  /**
   * 公司名
   */
  std::string company_name;
  /**
   * 属于该公司的建筑物id
   */
  std::vector<std::uint64_t> building_ids;
};

/**
 * 建筑物属性
 */
struct NERD_EXPORT BuildingAttribute {
  /**
   * guid = cityCode+innerId
   */
  uint64_t guid;
  /**
   * 建筑物名称
   */
  std::string bld_name;
  /**
   * 默认楼层
   */
  uint8_t default_floor;
  /**
   * 楼层数
   */
  uint8_t floor_num;
  /**
   * 楼层名称
   */
  std::vector<std::string> floor_names;
  /**
   * 主点内容的包围盒，像素单位
   */
  IndoorRect bound;
  /**
   * category
   */
  std::string category;
  /**
   * 地面之上楼层数量
   */
  uint8_t fr_count_above_ground;
  /**
   * 地上轮廓
   */
  std::vector<IndoorPoint> region_above_ground;
  /**
   * 地下轮廓
   */
  std::vector<IndoorPoint> region_under_ground;
  /**
   * 地上包围盒
   */
  IndoorRect bound_above_ground;
  /**
   * 地下包围盒
   */
  IndoorRect bound_under_ground;
  /**
   * 建筑实际版本号
   */
  uint32_t version_id;
};

/**
 * 室内图建筑Region的3D信息
 */
struct Style3D {
  /**
   * 高度
   */
  char height_3d;
  /**
   * 显示类型
   */
  BoxType display_type;
  /**
   * 墙面厚度
   */
  char thickness;
  /**
   * 角度
   */
  uint16_t angle;
  /**
   * 宽度
   */
  uint16_t rect_width;
  /**
   * 高度
   */
  uint16_t rect_height;
  /**
   * 中心坐标
   */
  IndoorPoint center;
};

/**
 * 室内门对象
 */
struct IndoorGateInfo {
  /**
   * 是否有门梁
   */
  char has_ceiling;
  /**
   * 坐标
   */
  Indoor4KPoint point_4k[2];
};

/**
 * 面对象
 */
struct RegionObject {
  /**
   * 高度
   */
  int32_t height;
  /**
   * 坐标
   */
  std::vector<IndoorPoint> points;
  /**
   * 坐标的小数部分
   */
  std::vector<IndoorPointFloat> float_points;
  /**
   * city code << 16 | build_id
   */
  uint32_t id;
  /**
   * 包围盒
   */
  IndoorRect bound;
  /**
   * 建筑物flag, BuildingFlag的组合
   */
  uint32_t flag;
  /**
   * 3d style信息
   */
  std::shared_ptr<Style3D> style3D{nullptr};
  /**
   * 内部门信息
   */
  std::vector<IndoorGateInfo> gate_infos;
  /**
   * 该面绑定的区域ID
   * 备注:对应协议中的“EXPI”部分, 数据侧的area_id;
   */
  std::string guid;
  /**
   * 该面的原始ID
   * 备注: 对应协议中的“EXRI”部分, 数据侧的raw_id
   */
  std::string region_id;
  /**
   * 该面关联的poi_id
   * 备注: 对应协议中的“EXID”部分, 数据侧的index_id (真正与PoiObject.poi_id一致)
*/
  uint64_t poi_id;
};

/**
 * 面图层
 */
struct NERD_EXPORT RegionLayer {
  /**
   * 图层类型
   */
  IndoorLayerType type{IndoorLayerType::kINDOOR_REGION};
  /**
   * 样式
   */
  uint16_t style_id;
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 面集合
   */
  std::vector<std::shared_ptr<RegionObject>> region_objs;
};

/**
 * 电梯数据
 */
struct NERD_EXPORT Escalator {
  /**
   * 电梯矩形
   */
  IndoorRect rect;
};

/**
 * 点对象
 */
struct PoiObject {
  /**
   * 样式
   */
  uint16_t style_id;
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 低5位分别表示20,19,18,17,16级是否显示；eg.0000 1110 表示19,18,17级显示
   */
  uint8_t show_mask;
  /**
   * 16到20级每个级别的label方向
   */
  uint8_t dir[5];
  /**
   * 名称，可以是多行
   */
  std::vector<std::string> lines;
  /**
   * 坐标
   */
  IndoorPoint point;
  /**
   * 高度
   */
  uint16_t height;
  /**
   * 室内图的poi id
   * 备注:对应协议中的"EXID"部分, 数据侧的index_id字段
   */
  uint64_t poi_id;
  /**
   * 小程序角标样式id
   */
  uint32_t subscript_style{0};
  /**
   * rich文本
   */
  std::string rich_text;
};

/**
 * 点图层
 */
struct NERD_EXPORT PointLayer {
  /**
   * 图层类型
   */
  IndoorLayerType type{IndoorLayerType::kINDOOR_POI};
  /**
   * 该图层中的所有POI对象
   */
  std::vector<std::shared_ptr<PoiObject>> poi_objs;
};

/**
 * 线对象
 */
struct LineObject {
  /**
   * 几何信息
   */
  std::vector<IndoorPoint> points;
  /**
   * 包围盒
   */
  IndoorRect bound;
  /**
   * 样式
   */
  uint16_t style_id;
  /**
   * 高度
   */
  uint16_t height;
};

/**
 * 线图层
 */
struct NERD_EXPORT LineLayer {
  /**
   * 该图层类型
   */
  IndoorLayerType type{IndoorLayerType::kINDOOR_LINE};
  /**
   * 样式id
   * (style_id 和 class_code都是样式ID)
   */
  uint16_t class_code;
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 线对象集合
   */
  std::vector<std::shared_ptr<LineObject>> line_objs;
};

/**
 * 道路箭头
 */
struct RoadArrowObject {
  /**
   * 偏转角
   */
  int16_t angle;
  /**
   * 坐标
   */
  IndoorPoint point;
  /**
   * 样式id
   */
  uint16_t style_id;
  /**
   * 高度
   */
  uint16_t height;
};

/**
 * 道路箭头图层
 */
struct NERD_EXPORT RoadArrowLayer {
  /**
   * 该图层类型
   */
  IndoorLayerType type{IndoorLayerType::kINDOOR_ROAD_ARROW};
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 道路箭头集合
   */
  std::vector<std::shared_ptr<RoadArrowObject>> road_arrow_objs;
};

/**
 * 每个楼层的数据
 */
class NERD_EXPORT IFloorData {
 public:
  virtual ~IFloorData() = default;
  /**
   * 获取面数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<RegionLayer>> GetRegionLayers() const = 0;

  /**
   * 获取电梯数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<Escalator>> GetEscalators() const = 0;

  /**
   * 获取点数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<PointLayer>> GetPointLayers() const = 0;

  /**
   * 获取线数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<LineLayer>> GetLineLayers() const = 0;

  /**
   * 获取道路箭头数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<RoadArrowLayer>> GetRoadArrowLayers() const = 0;
};

/**
 * 室内图建筑结构体
 */
class NERD_EXPORT IIndoorBuilding {
 public:
  virtual ~IIndoorBuilding() = default;
  /**
   * 建筑物属性
   */
  virtual const BuildingAttribute& GetAttribute() const = 0;

  /**
   * 每个楼层的数据
   */
  virtual const std::vector<std::shared_ptr<IFloorData>>& GetFloors() const = 0;
};

}  // namespace api
}  // namespace nerd
