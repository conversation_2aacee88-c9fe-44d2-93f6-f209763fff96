//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON>(张仁昌) on 2022/11/24.
//

#pragma once

#include <sstream>
#include <string>
#include <vector>
#include "common_location.h"

namespace nerd {
namespace api {

/**
 * @brief  编译错误信息透出类型
 */
enum class NerdDebugInfoTypes : int {
  kDefault = 0,
  // 下载超时
  kTileDownloadTimeout,
  // 请求数据版本号不一致
  kDataVersionNotMatch,
  // 请求队列过多
  kRequestTooMany,
  // 没有坐标展示的最大值
  kNoCoordinateMax = 1000,
  // 编译时期错误
  kCompileError,
  // 有坐标展示的最大值
  kCoordinateMax,
};

/**
 * 编译错误信息
 */
class NerdDebugInfoItem {
 public:
  NerdDebugInfoTypes type;
  mapbase::GeoCoordinateZ pos;
  std::string msg;
};

inline std::ostream& operator<<(std::ostream& os, const NerdDebugInfoItem& sb) {
  std::stringstream str;
  str << static_cast<int>(sb.type) << "," << sb.msg;
  str << "," << sb.pos;
  os << str.str();
  return os;
}
/**
 * 编译错误信息列表
 */
class NerdDebugInfo {
 public:
  std::vector<NerdDebugInfoItem> debug_infos;
};

inline std::ostream& operator<<(std::ostream& os, const NerdDebugInfo& sb) {
  std::stringstream str;
  for (auto& item : sb.debug_infos) {
    str << "[" << item << "]";
  }
  os << str.str();
  return os;
}

/**
 * 编译错误信息透出类
 */
class NerdEventListener {
 public:
  virtual ~NerdEventListener() = default;
  /**
   * @brief  编译错误信息透出
   * @param debug_info_list
   */
  virtual void OnDebugInfo(const NerdDebugInfo& debug_info_list) {}
};

}  // namespace api
}  // namespace nerd
