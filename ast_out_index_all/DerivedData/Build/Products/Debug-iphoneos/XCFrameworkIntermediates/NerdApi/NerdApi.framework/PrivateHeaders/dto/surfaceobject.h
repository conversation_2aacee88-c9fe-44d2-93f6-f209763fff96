// Copyright 2021 Tencent Copyright 2021 Tencent Inc.  All rights reserved.
//
// Author: ni<PERSON><PERSON><PERSON>@tencent.com (Emperor Penguin)
//

#pragma once

#include <bitset>
#include <iostream>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "dto/background.h"
#include "dto/types.h"

namespace nerd {
namespace api {

typedef std::vector<double> PoleHeightType;
typedef uint32_t PreciseTollID;
typedef std::vector<PreciseTollID> PreciseTollIDVec;
typedef uint32_t PreciseTollVersion;

/**
 * @brief 保护设施
 */
enum class ProtectionFacilityType : uint8_t {
  /**
   * 无
   */
  kNONE = 0,
  /**
   * 防护栏
   */
  kPROTECTION_FENCE,
  /**
   * 路缘石
   */
  kSTONE,
  /**
   * 墙体(隔音墙，保护墙等)
   */
  kWALL,
  /**
   * 防护网
   */
  kNET,
  /**
   * 垂直墙体
   */
  kVERTICAL_WALL,
  /**
   * 绿化带
   */
  kGREEN_BELT = 6,
  /**
   * 土坡
   */
  kUPRAISE_SLOP = 7,
  /**
   * 山体
   */
  kMOUNTAIN = 8,
  /**
   * 边沟
   */
  kSIDE_DITCH = 9,
  /**
   * 公交停靠站
   */
  kBUS_PARKING_STATION = 10,
  /**
   * 路边大型建筑
   */
  kROAD_SIDE_BUILDING = 11,
  kCURB,                /** 路沿石 */
  kNATURAL_BORDER,      /** 自然边界 */
  kMOVABLE_FENCE,       /** 移动护栏 */
  kJERSEY_FENCE,        /** 新泽西护栏 */
  kWAVE_FENCE,          /** 波形护栏 */
  kCABLE_GUARDRAIL,     /** 缆索式护栏 */
  kOPENING_GUARDRAIL,   /** 开口护栏 */
  kMUNICIPAL_GUARDRAIL, /** 市政护栏 */
  kANTI_GLARE_BOARD,    /** 防眩板 */
  kANTI_GLARE_NET,      /** 防眩网 */
  kCROSS_BAR_GUARDRAIL, /** 横杆式护栏 */
  kISOLATION_PILE,      /** 隔离桩 */
  kCONSTRUCTION_FENCE   /** 施工围挡 */
};

/**
 * @brief 隔离带类型
 */
enum class SeparatorBeltType : uint8_t {
  kNONE = 0,      /** 未分类*/
  kBLANK = 1,     /** 空地*/
  kGREENLAND = 2, /** 绿地*/
  kOTHER = 3,     /** 其他*/
};

/**
 * @brief 交通牌形状类型
 */
enum class TrafficSignShape : uint8_t {
  /**
   * 未调查
   */
  kNONE = 0,
  /**
   * 矩形
   */
  kSQUARE,
  /**
   * 圆形
   */
  kCIRCLE,
  /**
   * 三角形
   */
  kTRIANGLE,
  /**
   * 菱形
   */
  kDIAMOND,
  /**
   * 五角形
   */
  kPENTAGON,
  /**
   * 六角形
   */
  kHEXAGON,
  /**
   * 八角形
   */
  kOCTAGON,
  /**
   * 叉形
   */
  kX_SHAPE,
  /**
   * 组合型
   */
  kCOMBINATION,
  /**
   * 其他
   */
  kOTHER,
  /**
   * 倒三角
   */
  kREVERSE_TRIANGLE = 50,
  /**
   * 箭头
   */
  kARROW,
  /**
   * 自行车
   */
  kBICYCLE,
  /**
   * 轮椅
   */
  kWHEEL_CHAIR,
  /**
   * 折线
   */
  kPOLY_LINE,
  /**
   * 多边形
   */
  kPOLYGON
};

/**
 * @brief ROADMARK_P,ROADMARK_L 数据源
 */
enum class DataSourceType : uint8_t {
  kNONE = 0,             /** 无数据 */
  kHD_SELF_COLLECTION,   /** 高精自采 */
  kHD_CROWDSOURCING,     /** 高精众包 */
  kSD_CROWDSOURCING,     /** 低精众包 */
  kINTELLIGENCE,         /** 情报 */
  kIMAGE,                /** 影像 */
  kHIGH_RESOLUTION_IMSGE /** 高分影像 */
};

/**
 * @brief 道路标记类型
 */
enum class RoadMarkType : uint8_t {
  /**
   * 路沿
   */
  kCURB = 1,
  /**
   * 护栏
   */
  kBARRIER = 2,
  /**
   * 未分类地面图形
   */
  kLAND_SIGN = 14,
  /**
   * 地面箭头
   */
  kLAND_ARROW = 18,
  /**
   * 地面文字
   */
  kLAND_TEXT,
  /**
   * 车道虚线段
   */
  kLANE_VIRTUAL_SEG = 22,
  /**
   * 停车位
   */
  kPARKING_LOT,
  /**
   * 人行横道
   */
  kZEBRA_CROSSING,
  /**
   * 路口内自行车道
   */
  kBICYCLE_LANE_IN_CROSS_AREA = 25,
  /**
   * 禁停区
   */
  kFORBIDDEN_STOP_ZONE = 26,
  /**
   * 墙
   */
  kWALL = 27,
  /**
   * 垂直墙
   */
  kWALL_VERTICAL = 28,
  /**
   * 桥梁
   */
  kBRIDGE = 29,
  /**
   * 桥梁立面
   */
  kBRIDGE_VERTICAL_AREA = 30,
  /**
   * 桥梁底面
   */
  kBRIDGE_BOTTOM_AREA = 31,
  /**
   * 停靠站台
   */
  kSTATION_PARKING_LOT = 32,
  /**
   * 公共交通停靠线
   */
  kPUBLIC_STATION_PARKING_LOT_LINE = 33,
  /**
   * 墙
   */
  kWALL_BOTTOM_LINE = 34,
  /**
   * 步行道
   */
  kWALKER_WAY = 35,
  /**
   * 警示区域
   */
  kWARNING_AREA = 36,
  /**
   * 立面标记
   */
  kVERTICAL_MARK = 37,
  /**
   * 不可通行区域
   */
  kNON_THROUGH_AREA = 38,
  /**
   * 路测停车位
   */
  kROAD_SIDE_PARKING_LOT = 39,
  /**
   * 其他符号
   */
  kSIGN_OTHER = 40,
  /**
   * 公交车标记
   */
  kSIGN_BUS = 41,
  /**
   * 非机动车标记
   */
  kSIGN_NON_VEHICLES = 42,
  /**
   * 注意前方路况标记
   */
  kSIGN_ATTENTION_TRAFFIC_STATUS = 43,
  /**
   * 未分类等待区
   */
  kWAITING_ZONE = 99,
  /**
   * 直行待转区
   */
  kSTRAIGHT_WAITING_ZONE = 100,
  kLAND_NUMBER, /** 数字 */
  /**
   * 地面数字：最大速度
   */
  kLAND_NUMBER_MAX_SPEED,
  /**
   * 地面数字: 最小速度
   */
  kLAND_NUMBER_MIN_SPEED,
  /**
   * 地面数字：时间
   */
  kLAND_NUMBER_TIME,
  /**
   * 导流带
   */
  kDIVERSION_ZONE,
  /**
   * V字型导流带
   */
  kDIVERSION_V_ZONE,
  /**
   * 直线型导流带
   */
  kDIVERSION_LINE_ZONE,
  /**
   * 其他导流带
   */
  kDIVERSION_OTHER,
  /**
   * 未分类减速标志
   */
  kSLOW_DOWN,
  /**
   * 减速防滑带
   */
  kSLOW_DOWN_ANTI_SLIP,
  /**
   * 减速带
   */
  kSLOW_DOWN_ZONE,
  /**
   * 减速丘
   */
  kSLOW_DOWN_SLOPE,
  /**
   * 凸起路标(横向)
   */
  kSLOW_DOWN_RAISED_MARKING,
  /**
   * 让行线
   */
  kGIVE_WAY,
  /**
   * 停车让行线
   */
  kGIVE_WAY_STOP,
  /**
   * 减速让行线
   */
  kGIVE_WAY_SLOW_DOWN,
  /**
   * 铁路道口
   */
  kRAILWAY_CROSSING,
  /**
   * 非机动车交叉口
   */
  kNON_ROADWAY_CROSSING,
  /**
   * 左转待转区
   */
  kTURN_LEFT_WAITING_ZONE,
  /**
   * 网状线
   */
  kNET_CROSSING,
  /**
   * 人行横道预告标识
   */
  kZEBRA_PRECAST_LINE,
  /**
   * 车距确认线
   */
  kCAR_DISTANCE_CONFIRM_LINE,
  /**
   * 车道边线
   */
  kLANE_BOUNDARY,
  /**
   * 慢行线标志
   */
  kSLOW_DRIVING,
  /**
   * 防滑坡道标线
   */
  kANTI_SLIP_SLOP_MARKING,
  kGRAPH,                   // 图形
  kMANHOLE_COVER,           // 井盖
  kSTOP,                    // 停止线
  kVIRTUAL_STOP_LINE,       // 虚拟停止线
  kSLOW,                    // 慢行线
  kTURN_LEFT_WAITING_STOP,  // 左转待转区停止线
  kSTRAIGHT_WAITING_STOP,   // 直行待转区停止线
  kSTOP_OTHER,              // 其他类型停止线
  kBUS_STATION,             // 公交车站
  /**
   * 其他
   */
  OTHER,
  /**
   * 收费站岗亭未分类
   * */
  kTOLL_SENTRY,
  /**
   * 虚拟设施
   * */
  kVIRTUAL_FACILITY,
  /**
   * 潮汐车道停止线
   * */
  kTIDAL_LANE_STOP_LINE,
  /**
   * 逆向车道停止线
   * */
  kREVERSE_STOP_LINE,
  /**
   * 组合停止线
   * */
  kCOMBINATION_STOP_LINE,
  /**
   * 收费站导向线
   */
  kTOLL_STATION_GUIDANCE_LINE,
  /**
   * 收费站通道文字
   */
  kTOLL_STATION_TEXT,
  /**
   * 公交车道文字,包含特殊车道限行时间(如"8:00-18:00")
   */
  kBUS_LANE_TEXT,
  /**
   * 车道白色文字,与公交车道文字共用纹理，用于区分颜色
   */
  kLANE_WHITE_TEXT,
  /**
   * @brief 待转区停止线
   *
   */
  kTURN_WAITING_STOP_SD_PLUS,
  /**
   * @brief 待转区箭头
   *
   */
  kTURN_WAITING_ARROW_SD_PLUS
};

/**
 * @brief 道路方向箭头类型
 */
struct RoadMarkArrowMask {
  /**
   * @brief 禁行
   */
  uint32_t forbidden : 1;
  /**
   * @brief 正前方直行
   */
  uint32_t straight : 1;
  /**
   * @brief 90度左转
   */
  uint32_t left : 1;
  /**
   * @brief 90度右转
   */
  uint32_t right : 1;
  /**
   * @brief 左掉头
   */
  uint32_t u_turn : 1;
  /**
   * @brief 左前方(30度)
   */
  uint32_t skew_left : 1;
  /**
   * @brief 右前方(30度)
   */
  uint32_t skew_right : 1;
  /**
   * @brief 右掉头
   */
  uint32_t right_u_turn : 1;
  /**
   * @brief 左斜前（顺时针正西方向30°）
   */
  uint32_t merge_to_left_30 : 1;
  /**
   * @brief 左斜前（顺时针正西方向60°）
   */
  uint32_t merge_to_left_60 : 1;
  /**
   * @brief 左斜下（顺时针正南方向30°）
   */
  uint32_t left_back_30 : 1;
  /**
   * @brief 左斜下（顺时针正南方向60°）
   */
  uint32_t left_back_60 : 1;
  /**
   * @brief 右斜前（顺时针正北方向30°）
   */
  uint32_t merge_to_right_30 : 1;
  /**
   * @brief 右斜前（顺时针正北方向60°）
   */
  uint32_t merge_to_right_60 : 1;
  /**
   * @brief 右斜下（顺时针正东方向30°）
   */
  uint32_t right_back_30 : 1;
  /**
   * @brief 右斜下（顺时针正东方向60°）
   */
  uint32_t right_back_60 : 1;
  /**
   * @brief 禁止左转
   */
  uint32_t forbidden_left : 1;
  /**
   * @brief 禁止掉头
   */
  uint32_t forbidden_u_turn : 1;
  /**
   * @brief 禁止直行
   */
  uint32_t forbidden_straight : 1;
  /**
   * @brief 禁止右转
   */
  uint32_t forbidden_right : 1;
  /**
   * @brief 禁止左掉
   */
  uint32_t forbidden_left_u_turn : 1;
  /**
   * @brief 禁止直左
   */
  uint32_t forbidden_straight_left : 1;
  /**
   * @brief 禁止直右
   */
  uint32_t forbidden_straight_right : 1;

  RoadMarkArrowMask() {
    forbidden = 0;
    straight = 0;
    left = 0;
    right = 0;
    u_turn = 0;
    skew_left = 0;
    skew_right = 0;
    right_u_turn = 0;
    merge_to_left_30 = 0;
    merge_to_left_60 = 0;
    left_back_30 = 0;
    left_back_60 = 0;
    merge_to_right_30 = 0;
    merge_to_right_60 = 0;
    right_back_30 = 0;
    right_back_60 = 0;
    forbidden_left = 0;
    forbidden_u_turn = 0;
    forbidden_straight = 0;
    forbidden_right = 0;
    forbidden_left_u_turn = 0;
    forbidden_straight_left = 0;
    forbidden_straight_right = 0;
  }

  uint32_t GetValue() const {
    std::bitset<32> value;
    value[0] = forbidden;
    value[1] = straight;
    value[2] = left;
    value[3] = right;
    value[4] = u_turn;
    value[5] = skew_left;
    value[6] = skew_right;
    value[7] = right_u_turn;
    value[8] = skew_right;
    value[9] = right_u_turn;
    value[10] = merge_to_left_30;
    value[11] = merge_to_left_60;
    value[12] = left_back_30;
    value[13] = left_back_60;
    value[14] = merge_to_right_30;
    value[15] = merge_to_right_60;
    value[16] = right_back_30;
    value[17] = right_back_60;
    value[18] = forbidden_left;
    value[19] = forbidden_u_turn;
    value[20] = forbidden_straight;
    value[21] = forbidden_right;
    value[22] = forbidden_left_u_turn;
    value[23] = forbidden_straight_left;
    value[24] = forbidden_straight_right;
    return static_cast<uint32_t>(value.to_ulong());
  }
};

/**
 * @brief 杆状物类型
 */
enum class PoleType : uint8_t {
  /**
   * 未分类
   */
  kOTHER = 0,
  /**
   * 悬式标牌杆
   */
  kHANGING_SIGN_POLE,
  /**
   * 龙门架杆(横)
   */
  kPORTAL_FRAME_HORIZON,
  /**
   * 龙门架杆(竖)
   */
  kPORTAL_FRAME_VERTICAL,
  /**
   * 警示杆
   */
  kWARNING,
  /**
   * 信号灯杆
   */
  kTRAFFIC_SIGN,
  /**
   * 轮廓标
   */
  kSHAPE_SIGN,
  kHORIZON,  /** 横杆 */
  kVERTICAL, /** 竖杆 */
  kLIGHT,    /** 路灯杆 */
};

/**
 * @brief 指示灯类型
 */
enum class TrafficLightBoxType : uint8_t {
  /**
   * 其他
   */
  kOTHER = 0,
  /**
   * 机动车信号灯
   */
  kVEHICLE,
  /**
   * 非机动车信号灯
   */
  kNONE_VEHICLE,
  /**
   * 车道信号灯
   */
  kLANE,
  /**
   * 方向信号灯
   */
  kDIRECTION,
  /**
   * 铁路道口信号灯
   */
  kRAILWAY,
  /**
   * 闪光警告信号灯
   */
  kFLASH,
  /**
   * 人行横道信号灯
   */
  kWALK,
  /**
   * 倒计时器信号灯
   */
  kCOUNTDOWN,
  /**
   * 公交车专用道信号灯
   */
  kBUS,
  kZEBRA_CROSSING, /** 人行横道信号灯 */
};

struct TrafficLightLampMask {
  /**
   * 圆形
   */
  uint8_t round : 1;
  /**
   * 左转箭头
   */
  uint8_t left : 1;
  /**
   * 右转箭头
   */
  uint8_t right : 1;
  /**
   * 直行箭头
   */
  uint8_t straight : 1;
  /**
   * 掉头箭头
   */
  uint8_t turn_over : 1;
  /**
   * 数字读秒
   */
  uint8_t number_count_down : 1;
  /**
   * 行人
   */
  uint8_t walker : 1;
  /*
   * 自行车
   */
  uint8_t bicycle : 1;
  /**
   * 进度条读秒
   */
  uint8_t process_bar_count_down : 1;
  /**
   * 交叉形
   */
  uint8_t cross : 1;
  /**
   * 向下箭头
   */
  uint8_t down : 1;

  TrafficLightLampMask() {
    round = 0;
    left = 0;
    right = 0;
    straight = 0;
    turn_over = 0;
    number_count_down = 0;
    walker = 0;
    bicycle = 0;
    process_bar_count_down = 0;
    cross = 0;
    down = 0;
  }
};

struct TrafficLightPhaseMask {
  TrafficLightPhaseMask() {
    left = 0;
    straight = 0;
    right = 0;
    turn_back = 0;
  }
  /**
   * 左转
   */
  uint8_t left : 1;
  /**
   * 直行
   */
  uint8_t straight : 1;
  /**
   * 右转
   */
  uint8_t right : 1;
  /**
   * 掉头
   */
  uint8_t turn_back : 1;
};

/**
 * @brief 灯体方向
 */
enum class TrafficLightShape : uint8_t {
  /**
   * 未分类
   */
  kNONE = 0,
  /**
   * 横
   */
  kHORIZON,
  /**
   * 竖
   */
  kVERTICAL,
  /**
   * 单独
   */
  kALONG
};

/**
 * @brief 高启设施类型
 */
enum class OverHeadType : uint8_t {
  /**
   * 未分类
   */
  kUNCLASSIFIED,
  /**
   * 龙门架(包含牌坊)
   */
  kGANTRY,
  /**
   * 桥梁(包含跨河桥，跨谷桥，立交桥，栈 桥等)
   */
  kBRIDGE,
  /**
   * 隧道(包含山岭隧道，水底隧道，城市隧 道)
   */
  kTUNNEL,
  /**
   * 其他
   */
  kOTHER,
};

/**
 * @brief 障碍物类型
 */
enum class ObstacleType : uint8_t {
  /**
   * 未分类
   */
  kNONE = 0,
  /**
   * 设施障碍
   */
  kFACILITY,
  /**
   * 阻碍类
   */
  kOBSTACLE,
  /**
   * 道闸
   */
  kBARRIER_GATE,
  /**
   * 井盖
   */
  kMANHOLE_COVER,
  /**
   * 安全岛
   */
  kSAFE_ISLAND
};

enum class LargeBuildingType : uint8_t { kOTHER = 0, kTOLL, kCHECK_POINT };

struct TrafficLightLampColorMask {
  TrafficLightLampColorMask() {
    other = 0;
    red = 0;
    yellow = 0;
    green = 0;
  }
  uint8_t other : 1;
  uint8_t red : 1;
  uint8_t yellow : 1;
  uint8_t green : 1;
};

/**
 * 条件限制方向
 * */
struct ResDirectionMask {
  ResDirectionMask() {
    all = 0;
    straight = 0;
    left = 0;
    right = 0;
    skew_left = 0;
    skew_right = 0;
    u_turn = 0;
    forward = 0;
    backward = 0;
  }
  /**
   * 所有方向
   */
  uint16_t all : 1;
  /**
   * 直行
   */
  uint16_t straight : 1;
  /**
   * 左转
   */
  uint16_t left : 1;
  /**
   * 右转
   */
  uint16_t right : 1;
  /**
   * 向左合流
   * */
  uint16_t skew_left : 1;
  /**
   * 向右合流
   * */
  uint16_t skew_right : 1;
  /**
   * 掉头
   * */
  uint16_t u_turn : 1;
  /**
   * 正向
   * */
  uint16_t forward : 1;
  /**
   * 逆向
   * */
  uint16_t backward : 1;
};

/**
 * 关联方向
 * */
enum class RelatedDirectionType : uint8_t {
  /**
   * 无效值
   * */
  kNONE,
  /**
   * 顺向
   * */
  kSTART_TO_END,
  /**
   * 逆向
   * */
  kEND_TO_START,
  /**
   * 双向
   * */
  kBOTH_DIRECTION,
  /**
   * 未调查
   * */
  kUNKNOWN
};

/**
 * 条件限制类型
 */
enum RoadMarkResType : uint8_t {
  kNONE,
  /**
   * 允许
   */
  kALLOW,
  /**
   * 禁止
   */
  kFORBID
};

/**
 * 分离合并点类型
 */
enum class SplitMergeType : uint8_t {
  kNONE = 0,
  /** 共用道路分离点 */
  kCOMMON_LINK_SPLIT_POINT,
  /** 共用道路合并点 */
  kCOMMON_LINK_MERGE_POINT,
  /** 通用道路分离点 */
  kGENERAL_LINK_SPLIT_POINT,
  /** 通用道路合并点 */
  kGENERAL_LINK_MERGE_POINT,
  /** 特殊道路分离点 */
  kSPECIAL_LINK_SPLIT_POINT,
  /** 特殊道路合并点 */
  kSPECIAL_LINK_MERGE_POINT,
  /** 车道分离点 */
  kLANE_SPLIT_POINT,
  /** 车道合并点 */
  kLANE_MERGE_POINT
};

/**
 * @brief Surface基类
 */
struct ISurfaceObject {
  virtual ~ISurfaceObject() = default;
  /**
   * Surface 对象唯一ID
   */
  SurfaceIDType id{0, 0};
  /**
   * Surface 对象类型
   */
  SurfaceObjectType object_type;
  /**
   * 和当前对象存在逻辑关系的对象
   * example:
   *    同一组停止线和停车标牌会建立关联
   */
  std::vector<SurfaceIDType> related_object_ids;
  /**
   * 和当前对象关联的lane, 可能为空
   */
  std::vector<LaneIDType> related_lane_ids;
  /**
   * 和当前对象关联的lane group, 可能为空
   */
  std::vector<LaneGroupIDType> related_lane_group_ids;
  /**
   * 和当前对象关联的lane group TPID, 可能为空
   * 备注:应该和related_lane_group_ids大小相等
   */
  std::vector<TPIDType> related_lane_group_tp_ids;
  /**
   * 和当前对象关联的cross area, 可能为空
   */
  std::vector<CrossAreaIDType> related_cross_area_ids;
  /**
   * 代表这个元素在这个瓦片中是否需要通过接口透出;
   * 备注:跨Tile元素存储只会存储在一个瓦片中, 新版本通过瓦片ID拉去, 这个标记为了解决线上版本的兼容问题;
   * false: 标识非重复存储，需要透出; ture: 标识在其他瓦片中存储了;
   */
  bool repeat_flag_{false};

  /**
   * 获取 SurfaceIDType
   * @return SurfaceIDType
   */
  SurfaceIDType GetID() const { return id; }
  /**
   * 获取TileID
   * @return TileID
   */
  TileIDType GetTileId() const { return id.tile_id; }
  /**
   * @brief 获取surface object在tile中的序列号
   * @return seq num
   */
  TileIDType GetSeqID() const { return id.obj_id; }
  /**
   * @brief 返回该对象是否重复存储的
   * @return false: 非重复存储; true: 重复存储, 在所属的瓦片中关联的额外瓦片中也有存储;
   */
  bool IsRepeatFlag() const { return repeat_flag_; }

  /**
   * @brief 构建surface id
   */
  static SurfaceIDType IDBuilder(uint32_t tile_id, uint32_t seq) {
    return {tile_id, seq};
  }

  std::string IDToString() const {
    std::stringstream ss;
    ss << id.tile_id << ":" << id.obj_id;
    return ss.str();
  }
};
typedef std::shared_ptr<ISurfaceObject> ISurfaceObjectPtr;
typedef std::shared_ptr<const ISurfaceObject> ISurfaceObjectConstPtr;

/**
 * 交通灯灯头
 */
struct TrafficLightLamp {
  TrafficLightID id{0, 0};
  TrafficLightLampMask light_arrow;
  TrafficLightLampColorMask color;
  TrafficLightPhaseMask phase;
  Coordinate position;
};

struct TrafficLightBoxDetail {
  /**
   * 控制方向掩码
   */
  TrafficLightPhaseMask phase;
  /**
   * 空间形态
   */
  std::shared_ptr<std::vector<Coordinate>> geo_shape;
  /**
   * 关联的路口ID
   */
  CrossAreaIDType cross_area_id;
  /**
   * 相互关联的交通灯灯箱列表
   */
  std::vector<SurfaceIDType> related_traffic_light_boxes;
  /**
   * 自行车道面ID列表
   */
  std::vector<SurfaceIDType> bicycle_lane_areas;
  /**
   * 控制的斑马线列表
   */
  std::vector<SurfaceIDType> zebra_areas;
  /**
   * 灯头列表
   */
  std::vector<TrafficLightLamp> traffic_light_lamps;
  /**
   * 关联的LaneGroup
   */
  std::vector<LaneGroupIDType> related_lane_group_ids;
  /**
   * 关联的车道组
   */
  std::vector<LaneIDType> related_lane_ids;
};

struct TrafficLightBoxBrief {
  /**
   * 灯头朝向,[0,360)
   */
  int direction;
  /**
   * 灯头形态(如果没有形状点时，提供)
   */
  TrafficLightShape light_shape;
  /**
   * 灯头数量
   */
  int m_light_number;
  /**
   * 控制方向掩码
   */
  TrafficLightPhaseMask phase;
  /**
   * 坐标
   */
  Coordinate m_position;
  /**
   * 空间形态
   */
  std::shared_ptr<std::vector<Coordinate>> geo_shape;
  /**
   * 备注
   */
  std::string memo;
  /**
   * 关联的LaneGroup
   */
  std::vector<LaneGroupIDType> related_lane_group_ids;
  /**
   * 关联的车道组
   */
  std::vector<LaneIDType> related_lane_ids;
};
/**
 * @brief 交通信号灯类型
 */
struct TrafficLightBox : ISurfaceObject {
  TrafficLightBox() { object_type = SurfaceObjectType::kTRAFFIC_LIGHT; }
  ~TrafficLightBox() override {
    if (is_detail_light && box.detail != nullptr) {
      delete box.detail;
    }
    if (!is_detail_light && box.brief != nullptr) {
      delete box.brief;
    }
  }
  /**
   * 详细摄像头数据
   */
  bool is_detail_light{false};
  /**
   * 灯箱类型
   */
  TrafficLightBoxType light_type{TrafficLightBoxType::kOTHER};
  /**
   * 距离地面高度，单位:米
   */
  double height{0.0};
  /**
   * 详细信息, 根据light_type进行选择
   */
  union {
    TrafficLightBoxBrief *brief;
    TrafficLightBoxDetail *detail;
  } box{nullptr};

  /**
   * 关联方向
   * */
  RelatedDirectionType related_direction{RelatedDirectionType::kUNKNOWN};
};

/**
 * @brief 道路标记类型
 */
struct RoadMarkObject : ISurfaceObject {
  RoadMarkObject() {
    object_type = SurfaceObjectType::kROAD_MARK;
  }
  /**
   * 标记类型
   */
  RoadMarkType road_mark_type;
  /**
   * 箭头类型
   */
  RoadMarkArrowMask arrow_type;
  /**
   * 文本内容
   */
  std::string text;
  /**
   * 形状点，当存在车道索引时不存在该数据
   */
  std::shared_ptr<std::vector<Coordinate>> geometry;
  /**
   * 到正北方向夹角 [0, 360)
   */
  uint16_t angle_to_north;
  /**
   * 到车道中心线上的坐标下标索引，车道内元素有效
   * kLAND_TEXT,kLAND_ARROW,kLAND_NUMBER_MAX_SPEED,kLAND_NUMBER_MIN_SPEED,kLAND_NUMBER_TIME
   */
  uint32_t lane_geo_indicator;
  /**
   * 到车道中心线上一个点的距离偏移比例[0,100)
   * kLAND_TEXT,kLAND_ARROW,kLAND_NUMBER_MAX_SPEED,kLAND_NUMBER_MIN_SPEED,kLAND_NUMBER_TIME
   */
  float lane_geo_offset_ratio;
  /**
   * 中心线
   */
  std::shared_ptr<std::vector<Coordinate>> center_line;
  /**
   * 车道最小宽度，单位cm
   */
  uint16_t lane_min_width;
  /**
   * 限速值, 单位: 千米/小时；
   * 说明: 非限速牌的值为0;
   */
  uint16_t limit_speed{0};
  /**
   * 关联方向
   * */
  RelatedDirectionType related_direction;
  /**
   * 关联方向
   * */
  RelatedDirectionType direction;

  /**
   * 数据置信度标识,取值如下
   * 1: 高置信
   * 2: 有干扰
   * 3: 未对齐
   * 4: 有遮挡
   * */
  uint32_t confidence;

  /**
   * 车道类型
   * 1:公交车道
   * 2:HOV车道
   * 3:潮汐车道
   * 4:可变车道
   * 5:定向车道
   * 9:其他
   * */
  std::vector<uint8_t> lane_types;

  /**
   * 条件限制类型
   */
  RoadMarkResType res_type;

  /**
   * 数据源
   */
  DataSourceType data_source_type;

  bool IntersectsWithRange(const Rect &range) const;
};
typedef std::shared_ptr<RoadMarkObject> RoadMarkObjectPtr;

/**
 * @brief 杆状类型
 */
struct Pole : ISurfaceObject {
  Pole() { object_type = SurfaceObjectType::kPOLE; }
  Coordinate pos;
  PoleHeightType z;
  PoleType pole_type;
  std::string memo;
  RelatedDirectionType related_direction;
};
typedef std::shared_ptr<Pole> PolePtr;

/**
 * @brief 高起设施
 */
struct OverHead : ISurfaceObject {
  OverHead() { object_type = SurfaceObjectType::kOVERHEAD; }
  OverHeadType over_head_type;
  Coordinate position;
  std::string memo;
};
typedef std::shared_ptr<OverHead> OverHeadPtr;

/**
 * @brief 障碍物类型
 */
struct Obstacle : ISurfaceObject {
  Obstacle() { object_type = SurfaceObjectType::kOBSTACLE; }
  ObstacleType obstacle_type;
  Coordinate position;
  std::string memo;
  DataSourceType data_source_type;
};
typedef std::shared_ptr<Obstacle> ObstaclePtr;

/**
 * @brief 保护设施类型
 */
struct ProtectionFacility : ISurfaceObject {
  ProtectionFacility() { object_type = SurfaceObjectType::kPROTECTION_FACILITY; }
  ProtectionFacilityType facility_type;
  std::shared_ptr<std::vector<Coordinate>> geometry;
  std::string memo;
  DataSourceType data_source_type;
};
typedef std::shared_ptr<ProtectionFacility> ProtectionFacilityPtr;

/**
 * @brief 隔离带类型
 */
struct SeparatorBelt : ISurfaceObject {
  SeparatorBelt() { object_type = SurfaceObjectType::kSEPARATOR_BELT; }
  SeparatorBeltType separator_belt_type;
  std::shared_ptr<std::vector<Coordinate>> geometry;
  std::vector<std::shared_ptr<std::vector<Coordinate>>> center_lines;
  std::string memo;
  DataSourceType data_source_type;
  std::string redot_seq;
};
typedef std::shared_ptr<SeparatorBelt> SeparatorBeltPtr;

/**
 * @brief 独立导流带类型
 */
struct AloneLeadArea : ISurfaceObject {
  AloneLeadArea() { object_type = SurfaceObjectType::kALONE_LEADAREA; }
  std::shared_ptr<std::vector<Coordinate>> geometry;
  DataSourceType data_source_type;
  std::vector<std::shared_ptr<std::vector<Coordinate>>> lead_area_lines;
};
typedef std::shared_ptr<AloneLeadArea> AloneLeadAreaPtr;

/**
 * @brief 分离合并点类型
 */
struct SplitMerge : ISurfaceObject {
  SplitMerge() { object_type = SurfaceObjectType::kSPLIT_MERGE; }
  Coordinate position;
  SplitMergeType split_merge_type;
  std::string memo;
};
typedef std::shared_ptr<SplitMerge> SplitMergePtr;

/**
 * @brief 警告标记类型
 */
struct WarningSign : ISurfaceObject {
  WarningSign() { object_type = SurfaceObjectType::kWARNING_SIGN; }
  TrafficSignType sign_type;
  uint16_t angle_to_north;
  std::vector<SpeedType> max_speed;
  std::vector<SpeedType> min_speed;
  std::string info;
  TrafficSignShape sign_shape;
  Coordinate position;
  std::shared_ptr<std::vector<Coordinate>> sign_shape_geos;
  bool restrict_type;
  ResDirectionMask restrict_direction;
  RelatedDirectionType related_direction;
  RelatedDirectionType control_direction;
  /**
   * 车道类型
   * 1:公交车道
   * 2:HOV车道
   * 3:潮汐车道
   * 4:可变车道
   * 5:定向车道
   * 9:其他
   * */
  std::vector<uint8_t> lane_types;
};
typedef std::shared_ptr<WarningSign> WarningSignPtr;

enum LeadAreaType {
  /**
   * 直线型
   */
  kLINE,
  /**
   * V型
   */
  kV,
  /**
   * 其它（包含多个lane group围成的三角形导流区、平行lane group之间的直线导流区等）
   */
  kOTHER,
    
/**
 * 闭合型导流区
 */
  CLOSE,
  
/**
* 无条纹导流区
*/
  FILL
};

/**
 * 在boundary上的一段范围。沿着boundary方向（保证end大于start）
 */
struct OnBoundaryRange {
  /**
   * 起始点下标
   */
  int start_index{};
  /**
   * 起始点距离比例, ratio = d1 / d2
   *
   *    |              d2            |
   *    |        d1      |           |
   * ---+----------------x-----------+----------
   * start_index               start_index+1
   *
   */
  float start_ratio{};
  /**
   * 起点高度，单位cm
   */
  int start_height{};
  /**
   * 结束点下标
   */
  int end_index{};
  /**
   * 结束点距离比例
   */
  float end_ratio{};
  /**
   * 终点高度，单位cm
   */
  int end_height{};
  /**
   * 所属boundary id
   */
  BoundaryIDType boundary_id;
  /**
   * boundary关联的lanegroupId，tileId和Boundary一致
   */
  uint32_t attached_inner_lanegroup_id;
};

class LineLeadAreaObject;
class VLeadAreaObject;
class OtherLeadAreaObject;
class GeneralLeadAreaObject;
/**
 * @brief 导流区类型
 */
struct LeadAreaObject : ISurfaceObject {
  virtual LeadAreaType GetType() const = 0;
  /**
   * 以下三个函数需要按照GetType结果来调用。类型不匹配会返回nullptr
   * 下面三个函数也可以用强转代替
   */
  virtual const LineLeadAreaObject *GetLineLeadArea() const;
  virtual const VLeadAreaObject *GetVLeadArea() const;
  virtual const OtherLeadAreaObject *GetOtherLeadArea() const;
  virtual const GeneralLeadAreaObject *GetGeneralLeadArea() const;
};
typedef std::shared_ptr<LeadAreaObject> LeadAreaObjectPtr;

/**
 * 和lane group是一对多的关系，描述现实中的一个导流区
 */
class LineLeadAreaObject : public LeadAreaObject {
 public:
  LeadAreaType GetType() const override;
  const LineLeadAreaObject *GetLineLeadArea() const override;

  /**
   * 左侧边界
   */
  std::vector<OnBoundaryRange> left_ranges;
  /**
   * 右侧边界
   */
  std::vector<OnBoundaryRange> right_ranges;
  /**
   * 关联的V型导流区id【tile_id为0说明没有关联】
   */
  SurfaceIDType v_lead_area_id;
  /**
   * 数据源
   */
  DataSourceType data_source;
};

/**
 * 和lane group是一对多的关系，描述现实中的一个导流区
 */
class VLeadAreaObject : public LeadAreaObject {
 public:
  LeadAreaType GetType() const override;
  const VLeadAreaObject *GetVLeadArea() const override;

  /**
   * 左边对应的boundary列表，顺序为：沿着lane group通行方向
   * 当join为true时，终点为尖角位置；为false时，起点为尖角位置
   */
  std::vector<OnBoundaryRange> left_ranges;
  /**
   * 右边对应的boundary列表，顺序为：沿着通行方向
   */
  std::vector<OnBoundaryRange> right_ranges;
  /**
   * 左右边终点对应的break point下标（终点肯定和break point重合）
   * 【终点】两个lane group和尖角相对的另一端，并不是指lane group通行方向的终点
   */
  int left_end_break_point_index;
  int right_end_break_point_index;
  /**
   * true:导流区是由两个lane group交汇形成的
   * false:导流区是由两个lane group分流形成的
   */
  bool join;
  /**
   * 闭合多边形。
   * 当join为true时，1是左边终点，geometry.size - 2（因为是闭合的，所以不是size - 1)是右边终点。
   * 当join为false时，1是右边终点，geometry.size - 2（因为是闭合的，所以不是size - 1)是左边终点。
   * 左边终点与右边终点之间，可能会插入0~2个点：
   *    左边终点不在左lane group右边界上，在左lane group右边界上插入一个点，否则不插入
   *    右边终点不在右lane group左边界上，在右lane group左边界上插入一个点，否则不插入
   */
  std::shared_ptr<std::vector<Coordinate>> geometry;
  /**
   * 左侧关联的导流区id【tile_id为0说明没有关联】
   */
  SurfaceIDType left_lead_area_id;
  /**
   * 右侧关联的直线型导流区id【tile_id为0说明没有关联】
   */
  SurfaceIDType right_lead_area_id;
  /**
   * 数据源
   */
  DataSourceType data_source;
};

/**
 * 描述其它导流区的一条边
 */
struct OtherLeadAreaEdge {
  /**
   * 此边在各个
   */
  std::vector<OnBoundaryRange> ranges;
  /**
   * 每一条边起始点在geometry中的下标。endIndex小于startIndex时，需要将边反转
   */
  int geometry_start_index;
  int geometry_end_index;
};

class OtherLeadAreaObject : public LeadAreaObject {
 public:
  LeadAreaType GetType() const override;
  const OtherLeadAreaObject *GetOtherLeadArea() const override;

  /**
   * 导流区多边形边集合。每一个边可能由多个boundary组成
   * 会存在部分边的方向需要反置才能满足导流区多边形逆时针闭合
   */
  std::vector<OtherLeadAreaEdge> edges;
  /**
   * 闭合多边形。edges数量可能比geometry点数量-1少：两个反向平行lane group组成的导流区，lane group之间是没有边的
   */
  std::shared_ptr<std::vector<Coordinate>> geometry;
  /**
   * 数据源
   */
  DataSourceType data_source;
};

/**
 * 描述导流区的一条边在导流区轮廓上的顶点区间
 */
struct LeadAreaEdgePos {
  /**
   * 每一条边起始点在geometry中的下标。endIndex小于startIndex时，需要将边反转
   */
  int geometry_start_index;
  int geometry_end_index;
};

/**
 * 新导流区通用协议
 */
class NERD_EXPORT GeneralLeadAreaObject : public LeadAreaObject {
 public:
  LeadAreaType GetType() const override;
  const GeneralLeadAreaObject *GetGeneralLeadArea() const override;
  bool IsJoin() const;

  // 导流区类型
  LeadAreaType type;
  /**
   * 导流区多边形边集合
   */
  std::vector<LeadAreaEdgePos> edges;
  /**
   * 闭合多边形。edges数量可能比geometry点数量-1少：两个反向平行lane group组成的导流区，lane group之间是没有边的
   */
  std::shared_ptr<std::vector<Coordinate>> geometry;
  // 导流线方向
  float angle;
};

/**
 * @brief 收费站通道类型
 */
struct TollStationChannelMask {
  TollStationChannelMask();
  /**
   * 未知
   */
  uint8_t unknown:1;
  /**
  * 现金
  */
  uint8_t cash:1;
  /**
   * etc通道
   */
  uint8_t etc:1;
  /**
   * 微信
   */
  uint8_t wechat:1;
  /**
   * 支付宝
   */
  uint8_t alipay:1;
  /**
   * 其他支付
   */
  uint8_t other_payment:1;
  /**
   * 免费
   */
  uint8_t free:1;
  /**
   * 预留字段
   */
  uint8_t reserved:1;
};

/**
 * @brief 收费岛
 */
struct TollStationTunnel {
  /**
   * 收费岛左侧边线中心点
   */
  Coordinate island_left_center_point;
  /**
   * 通道类型牌位置
   */
  Coordinate channel_type_sign_pos;
  /**
  * 收费通道类型
  */
  TollStationChannelMask charge_channel_types;
};

/**
 * @brief 收费站类型
 */
struct NERD_EXPORT TollStationObject : public ISurfaceObject {
  /**
   * 通行方向,正北方向顺时针夹角 [0,360)
   */
  int direction;
  /**
   * 上盖中心点
   */
  Coordinate upper_cover_center_point;
  /**
   * 上盖矩形框，共4个点，以左下角为起点，逆时针方向四个顶点坐标
   */
  std::shared_ptr<std::vector<Coordinate>> upper_cover_geometry;
  /**
   * 各收费岛，从左到右排列
   */
  std::shared_ptr<std::vector<std::shared_ptr<TollStationTunnel>>> charge_tunnels;
  /**
   * 收费站名称每个字的坐标，从左到右
   */
  std::shared_ptr<std::vector<Coordinate>> name_points;
  /**
   * 收费站名称，无"收费站"后缀，汉字数量和name_points数量一致;
   */
  std::string name;
  /**
   * 收费站英文名
   */
  std::string name_eng;
  /**
   * 收费站位置是否有精模
   */
  bool has_precise_toll_station;
  /**
   * 精模收费站id
   */
  PreciseTollID precise_toll_id;
  /**
   * 精模收费站version
   */
  PreciseTollVersion precise_toll_version;
  /**
   * 精模收费站高度
   * @return
   */
  int GetPreciseTollHeight() const {
    if (upper_cover_geometry == nullptr) {
      return 0;
    }
    int32_t height = std::numeric_limits<int32_t>::max();
    for (auto const &point : *upper_cover_geometry) {
      height = std::min(point.relative_z, height);
    }
    return height;
  }
};

typedef std::shared_ptr<TollStationObject> TollObjectPtr;

/**
 * @brief 绿化带类型
 */
enum class GreenBeltType {
  /**
   * 单黄线
   */
  kSINGLE_YELLOW_LINE,
  /**
   * 双黄线
   */
  kTWO_YELLOW_LINE,
  /**
   * 绿化带面
   */
  kGREEN_BELT,
  /**
   * 隔离带
   */
  kISOLATION_BELT,
  /**
   * 带boundary 绿化带
   */
  kGREEN_BELT_WITH_BOUNDARY,
  /**
   * 水泥墩
   */
  kMEDIAN_BARRIER,
};

/**
 * @brief 绿化带
 */
class NERD_EXPORT GreenBeltObject : public ISurfaceObject {
 public:
  /**
   * 绿化带一条边；不返回空
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetOneEdge() const = 0;
  /**
   * 绿化带另一条边; 当绘制物为线时，此接口返回空
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetOtherEdge() const = 0;
  /*
   * Get Upper line boundary Coordinates of Greenbelt
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetTopEdge() const = 0;
  /*
   * Get Lower line boundary Coordinates of Greenbelt
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetBottomEdge() const = 0;
  /*
   * Get if Upper line boundary of Greenbelt crossed tile
   */
  virtual bool IsTopEdgeCrossTile() const = 0;
  /*
   * Get if Lower line boundary of Greenbelt crossed tile
   */
  virtual bool IsBottomEdgeCrossTile() const = 0;
  /**
   * 获取绿化带类型
   */
  virtual GreenBeltType GetGreenBeltType() const = 0;
  /**
   * 获取绿化带高度, 单位分米
   */
  virtual float GetGreenBeltHeight() const = 0;

  /**
   * 获取绿化带关联Boundary， 如果没有返回空数组
   */
  virtual std::vector<std::shared_ptr<const IBoundary>> GetRelatedBoundaries() const = 0;
};

typedef std::shared_ptr<GreenBeltObject> GreenBeltObjectPtr;

/**
 * @brief 大型建筑类型
 */
struct LargeBuilding : ISurfaceObject {
  LargeBuilding() { object_type = SurfaceObjectType::kLARGE_BUILDING; }
  LargeBuildingType large_building_type;
  std::shared_ptr<std::vector<Coordinate>> geometry;
};
typedef std::shared_ptr<ProtectionFacility> LargeBuildingPtr;

/**
 * 高精道路路名区间信息
 */
struct RoadNameLineHdRange {
  /**
   * LaneGroupID信息, 可能跨Tile;
   */
  LaneGroupIDType lane_group_id{0, 0};
  /**
   * LaneGroupID信息, 可能跨Tile;
   */
  TPIDType lane_group_tp_id{0, 0};
  /**
   * 该LaneGroup在整条路名几何信息的开始索引
   */
  uint16_t geometry_idx_start{0};
  /**
   * 该LaneGroup在整条路名几何信息的结束索引
   */
  uint16_t geometry_idx_end{0};
};

/**
 * @brief 高精场景下动态路名数据
 */
struct LineWithLabelFeature;
typedef LineWithLabelFeature RoadNameLineFeature;
typedef std::shared_ptr<LineWithLabelFeature> RoadNameLineFeaturePtr;
struct RoadNameLine : ISurfaceObject {
  RoadNameLine() { object_type = SurfaceObjectType::kROAD_NAME_LINE; }
  /**
   * 高精动态路名信息(复用BMD的数据结构)
   */
  RoadNameLineFeaturePtr feature;
  /**
   * 顺向HD LaneGroup区间信息
   * 备注: 当单向路线时, 只有forward_hd_range_list;
   */
  std::vector<RoadNameLineHdRange> forward_hd_range_list;
  /**
   * 逆向HD LaneGroup区间信息
   */
  std::vector<RoadNameLineHdRange> backward_hd_range_list;
};

/**
   * 天桥组建-通道/连接类型
 */
struct PedestrianOverpassPassage {
  /**
     * 要素ID
   */
  FeatureIDType id_;
  /**
     * 类型
   */
  OverpassPassageType type_;
  /**
     * 通道中心线信息(三维)
   */
  std::shared_ptr<std::vector<Coordinate>> geometries_;
  /**
     * 宽度信息
   */
  uint32_t width_{0};
  /**
     * 通过ID获取Boundary对象
   */
  std::vector<std::shared_ptr<IBoundary>> boundaries_;
  /**
     * 该通道对象关联的平台ID列表
   */
  std::vector<FeatureIDType> platform_ids_;
};

/**
 * @brief 桥墩
 */
struct PierObject : ISurfaceObject {
  PierObject() { object_type = SurfaceObjectType::kPIER; }
  std::shared_ptr<std::vector<Coordinate>> geometry = nullptr;
  double radius = 0.f;
};

/**
 * 天桥组建-平台类型
 */
struct PedestrianOverpassPlatform {
  /**
   * 要素ID
   */
  FeatureIDType  id_;
  /**
   * 平台的面信息(逆向)
   */
  std::shared_ptr<std::vector<Coordinate>> geometries_ = nullptr;
  /**
   * 平台不可通行边
   */
  std::vector<std::shared_ptr<IBoundary>> no_through_edges_;
  /**
   * 获取桥墩对象;
   */
  std::vector<std::shared_ptr<PierObject>> piers_;
  /**
   * 该通道对象关联的通道ID列表
   */
  std::vector<FeatureIDType> passage_ids_;
};

/**
   * 天桥完整对象
 */
struct PedestrianOverpass :  ISurfaceObject {
  PedestrianOverpass() {
    object_type = SurfaceObjectType::kPedestrianOverpass;
  }
  /**
   * 该天桥包含的平台列表
   */
  std::vector<PedestrianOverpassPlatform>  platform_list_;
  /**
   * 该天桥包含的通道列表
   */
  std::vector<PedestrianOverpassPassage>   passage_list_;

  /**
   * 天桥ID
   */
  std::string pedestrian_id_;
};

/**
 * 龙门架显示牌对象信息描述
 * 如Led电子牌
 */
struct GantryDisplayBoard {
  /**
   * 显示牌设备ID, 要求唯一有继承性
   */
  std::string device_id_;
  /**
   * 该显示牌展示宽度信息, 单位:厘米
   */
  uint32_t  width_{0};
  /**
   * 该显示牌展示高度信息, 单位:厘米
   */
  uint32_t height_{0};
  /**
   * 该显示牌关联对应的车道ID 列表
   */
  std::vector<LaneIDType> lane_id_list_;
};

/**
 * 龙门架对象信息描述
 * 一个龙门架可能包含多个显示牌对象;
 */
struct GantryObject {
  /**
   * 要素ID
   */
  FeatureIDType id_;
  /**
   * 龙门架的几何信息, 几何信息详细细节:
   * 1> 按照"左侧竖杆点(1个)->横杆左侧点(1个)->显示牌中心点列表(N个, N>=0)->横杆右侧点(1个)->右侧竖杆点(1个)"
   * 2> 显示牌中心点数量和 "board_info_list_"大小相等
   */
  std::shared_ptr<std::vector<Coordinate>> geometries_ = nullptr;
  /**
   * 龙门架上关联的显示牌列表
   * 注意: 显示牌的存储顺序和电子牌中心点列表存储顺序一致
   */
  std::vector<GantryDisplayBoard> display_board_list_;
  /**
   * 该显示牌关联对应的车道组ID 列表
   */
  std::vector<LaneGroupIDType> lane_group_id_list_;
};

/**
 * 组件对象类型
 */
enum class ComponentModelObjectType : uint8_t {
  /**
   * 缺省类型
   */
  kNONE = 0,
    
  /**
   * 组件化斜拉桥/悬索桥梁
   */
  kBRIDGE,
    
  /**
   * 路灯
   */
  kSTREET_LAMP,

  /*
   * 绿化带绿植
   */
  kGREENBELT_TREE,

  /**
   * 精细化桥梁
   */
  kREFINED_BRIDGE,
    
  /**
   * 组件化拱式/桁架桥
   */
  kGIRDER_BRIDGE,
    
  /**
   * 组件化拱式/桁架桥
   */
  kBRIDGE_PIER,
    
  /**
   * 组件化栏杆
   */
  kBRIDGE_RAILING,

  /**
   * 组件化护栏
   */
  kPEDESTRIAN_GUARDRAIL,
};

/**
 * 原子组件模型类型描述
 */
struct ComponentModelObjectBase {
  /**
   * 样式ID
   */
  uint32_t style_id{0};
    
  /**
   * 组件颜色种类
   */
  uint32_t color_type{0};

  /**
   * 材质种类
   */
  uint32_t material_type{0};
        
  /**
   * 变换参数
   */
  ComponentModelTransferMatrix transfer_matrix;

  /**
   * 样条线
   */
  std::shared_ptr<std::vector<Coordinate>> spline_geometry;
};

/**
 * 通用组件模型聚合类型描述，实际应用于桥梁、路灯等车道级精细化元素
 */
struct ComponentModelObjectList {
  /**
   * 组件聚合类id
   */
  std::string component_model_id;

  /**
     * 组件POI ID
   */
  uint64_t poi_id;

  /**
   * 组件模型类型
   */
  ComponentModelObjectType display_object_type;
    
  /**
   * 组件模型列表
   */
  std::shared_ptr<std::vector<ComponentModelObjectBase>> component_model_base_list;
    
  /**
   * 组件模型对应的车道组ID 列表
   */
  std::vector<LaneGroupIDType> lane_group_id_list_;

  /**
   * 组件模型对应的车道组ID 列表
   */
  std::vector<TPIDType> lane_group_tp_id_list_;
};

/**
 * @brief 空类型，占位用，为了兼容旧引擎
 */
struct EmptyObject : ISurfaceObject {
  EmptyObject() { object_type = SurfaceObjectType::kEMPTY; }
};

/**
 * @brief 路面对象集合类
 */
class NERD_EXPORT SurfaceObjectCollect {
 private:
  std::unordered_map<SurfaceObjectType, std::vector<ISurfaceObjectConstPtr>> surface_objects_;

 public:
  /**
   * @brief 设置路面对象
   * @param[in] surface_objects 路面对象集合
   */
  void SetSurfaceObjects(std::unordered_map<SurfaceObjectType, std::vector<ISurfaceObjectConstPtr>> &&surface_objects) {
    this->surface_objects_ = surface_objects;
  }

  /**
   * 插入一个SurfaceObject
   * @param type
   * @param obj
   */
  void AddSurfaceObject(SurfaceObjectType type, const ISurfaceObjectConstPtr &obj) {
    const auto &iter_objs = this->surface_objects_.find(type);
    if (iter_objs == this->surface_objects_.end()) {
      this->surface_objects_.insert({type, {}});
    }
    auto &object_list = surface_objects_.at(type);
    for (const auto &item : object_list) {
      if (item->id == obj->id) {
        return;
      }
    }
    this->surface_objects_.at(type).emplace_back(obj);
  }

  const std::unordered_map<SurfaceObjectType, std::vector<ISurfaceObjectConstPtr>> &GetSurfaceObjectMap() const {
    return surface_objects_;
  }

  /**
   * 批量插入SurfaceObject
   */
  void AddSurfaceObjects(
      const std::unordered_map<SurfaceObjectType, std::vector<ISurfaceObjectConstPtr>> &surface_objects) {
    for (auto const &obj : surface_objects) {
      for (auto const &o : obj.second) {
        AddSurfaceObject(obj.first, o);
      }
    }
  }

  /**
   * @brief 是否存在路面对象
   * @return 是否存在路面对象
   */
  bool IsExistSurfaceObject() const { return !surface_objects_.empty(); }

  /**
   * @brief 获取指定类型路面对象
   * @return 路面对象
   */
  template <class T>
  std::vector<std::shared_ptr<const T>> GetObjectByType(SurfaceObjectType type) const {
    std::vector<std::shared_ptr<const T>> objects;
    auto it = surface_objects_.find(type);
    if (it == surface_objects_.end()) {
      return objects;
    }
    objects.reserve(it->second.size());
    for (const auto &obj : it->second) {
      objects.push_back(std::static_pointer_cast<const T>(obj));
    }
    return objects;
  }

  /**
   * @brief 获取交通信号灯
   * @return 交通信号灯集合
   */
  std::vector<std::shared_ptr<const TrafficLightBox>> GetTrafficLights() const {
    return GetObjectByType<TrafficLightBox>(SurfaceObjectType::kTRAFFIC_LIGHT);
  }

  /**
   * @brief 获取路面标记
   * @return 路面标记集合
   */
  std::vector<std::shared_ptr<const RoadMarkObject>> GetRoadMarkObjects() const {
    return GetObjectByType<RoadMarkObject>(SurfaceObjectType::kROAD_MARK);
  }

  /**
   * @brief 获取指定类型路面标记
   * @return 路面标记集合
   */
  std::vector<std::shared_ptr<const RoadMarkObject>> GetRoadMarkObjects(RoadMarkType type) const {
    std::vector<std::shared_ptr<const RoadMarkObject>> objects;
    auto it = surface_objects_.find(SurfaceObjectType::kROAD_MARK);
    if (it == surface_objects_.end()) {
      return objects;
    }
    objects.reserve(it->second.size());
    for (const auto &obj : it->second) {
      auto ptr = std::static_pointer_cast<const RoadMarkObject>(obj);
      if (ptr->road_mark_type == type) {
        objects.push_back(ptr);
      }
    }
    return objects;
  }

  /**
   * @brief 获取指定类型警示牌
   * @return 警示牌集合
   */
  std::vector<std::shared_ptr<const WarningSign>> GetWarningSigns(TrafficSignType type) const {
    std::vector<std::shared_ptr<const WarningSign>> objects;
    auto it = surface_objects_.find(SurfaceObjectType::kWARNING_SIGN);
    if (it == surface_objects_.end()) {
      return objects;
    }
    objects.reserve(it->second.size());
    for (const auto &obj : it->second) {
      auto ptr = std::static_pointer_cast<const WarningSign>(obj);
      if (ptr->sign_type == type) {
        objects.push_back(ptr);
      }
    }
    return objects;
  }

  /**
   * @brief 获取杆状物
   * @return 杆状物集合
   */
  std::vector<std::shared_ptr<const Pole>> GetPoles() const { return GetObjectByType<Pole>(SurfaceObjectType::kPOLE); }

  std::vector<std::shared_ptr<const TollStationObject>> GetTollStations() const {
    return GetObjectByType<TollStationObject>(SurfaceObjectType::kTOLL_STATION);
  }

  /**
   * @brief 获取高起设施
   * @return 高起设施集合
   */
  std::vector<std::shared_ptr<const OverHead>> GetOverHeads() const {
    return GetObjectByType<OverHead>(SurfaceObjectType::kOVERHEAD);
  }

  /**
   * @brief 获取障碍物
   * @return 障碍物集合
   */
  std::vector<std::shared_ptr<const Obstacle>> GetObstacles() const {
    return GetObjectByType<Obstacle>(SurfaceObjectType::kOBSTACLE);
  }

  /**
   * @brief 获取保护设施
   * @return 保护设施集合
   */
  std::vector<std::shared_ptr<const ProtectionFacility>> GetProtectionFacilities() const {
    return GetObjectByType<ProtectionFacility>(SurfaceObjectType::kPROTECTION_FACILITY);
  }

  /**
   * @brief 获取警告标志
   * @return 警告标志集合
   */
  std::vector<std::shared_ptr<const WarningSign>> GetWarningSigns() const {
    return GetObjectByType<WarningSign>(SurfaceObjectType::kWARNING_SIGN);
  }

  /**
   * @brief 获取桥墩
   * @return 桥墩集合
   */
  std::vector<std::shared_ptr<const PierObject>> GetPiers() const {
    return GetObjectByType<PierObject>(SurfaceObjectType::kPIER);
  }

  /**
   * @brief 获取导流区
   * @return 导流区集合
   */
  std::vector<std::shared_ptr<const LeadAreaObject>> GetLeadAreas() const {
    return GetObjectByType<LeadAreaObject>(SurfaceObjectType::kLEAD_AREA);
  }

  /**
   * @brief 获取大型路面建筑物
   * @return 大型路面建筑物集合
   */
  std::vector<std::shared_ptr<const LargeBuilding>> GetLargeBuildings() const {
    return GetObjectByType<LargeBuilding>(SurfaceObjectType::kLARGE_BUILDING);
  }

  /**
   * @brief 获取路名参考线
   * @return 路名参考线集合
   */
  std::vector<std::shared_ptr<const RoadNameLine>> GetRoadNameLines() const {
    return GetObjectByType<RoadNameLine>(SurfaceObjectType::kROAD_NAME_LINE);
  }

  /**
   * @brief 获取分离合并点
   * @return 分离合并点集合
   */
  std::vector<std::shared_ptr<const SplitMerge>> GetSplitMerge() const {
    return GetObjectByType<SplitMerge>(SurfaceObjectType::kSPLIT_MERGE);
  }

  std::vector<ISurfaceObjectConstPtr> GetAllSurfaceObjects() const {
    std::vector<ISurfaceObjectConstPtr> all;
    for (auto const &obj : surface_objects_) {
      all.insert(all.end(), obj.second.begin(), obj.second.end());
    }
    return all;
  }

  /**
   * 获取包含的Object类型列表
   * @return 类型列表
   */
  std::vector<SurfaceObjectType> GetSurfaceObjectTypes() const {
    std::vector<SurfaceObjectType> types;
    types.reserve(surface_objects_.size());
    for (const auto &it : surface_objects_) {
      types.push_back(it.first);
    }
    return types;
  }
};

typedef std::shared_ptr<TrafficLightBox> TrafficLightBoxPtr;
typedef std::shared_ptr<const TrafficLightBox> TrafficLightBoxConstPtr;

typedef std::shared_ptr<RoadMarkObject> RoadMarkObjectPtr;
typedef std::shared_ptr<const RoadMarkObject> RoadMarkObjectConstPtr;

}  // namespace api
}  // namespace nerd
