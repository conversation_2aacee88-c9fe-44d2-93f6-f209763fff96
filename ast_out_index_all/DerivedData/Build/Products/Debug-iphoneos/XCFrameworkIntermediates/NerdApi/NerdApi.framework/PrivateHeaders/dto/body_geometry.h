// Copyright 2022 Tencent Inc. All rights reserved.

#pragma once

#include <array>
#include <cstdint>
#include <memory>
#include <vector>

#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 3D Objects渲染类型
 */
enum class BodyGeometryType : int8_t {
  /**
   * @brief 三角形条带
   * */
  BG_INDEXED_TRIANGLE_STRIP = 0,
  /**
   * @brief 三角形数组
   */
  BG_INDEXED_TRIANGLE_ARRAY = 1,
  /**
   * @brief 线数组
   */
  BG_INDEXED_LINE_STRIP = 2
};

struct SubGeometry {
  /**
   * @brief 模型ID
   */
  PackedIDType id;
  /**
   * @brief 变换矩阵
   */
  std::array<std::array<float, 4>, 4> transform_matrix{};
};

struct BodyGeometry {
  /**
   * @brief 模型ID
   * */
  PackedIDType id;
  /**
   * @brief 类别
   */
  BodyGeometryType type{BodyGeometryType::BG_INDEXED_TRIANGLE_ARRAY};
  /**
   * @brief 顶点数组, 单位: 米
   */
  std::vector<Vector3D> vertices;
  /**
   * @brief 纹理坐标数组
   */
  std::vector<TextureCoordinate> texture_coords;
  /**
   * @brief 法向量数组
   */
  std::vector<NormalVector> normals;
  /**
   * @brief 索引数组
   */
  std::vector<uint32_t> indices;
  /**
   * @brief 模型子节点
   */
  std::vector<SubGeometry> children;
  /**
   * @brief 外轮廓顶点数组, 单位: 米
   */
  std::vector<Vector3D> contours;
};

}  // namespace api
}  // namespace nerd
