// Copyright 2021 Tencent Copyright 2021 Tencent Inc.  All rights reserved.
//
// Author: ni<PERSON><PERSON><PERSON>@tencent.com (Emperor <PERSON>)
//
// 数据引擎NERD之NERD数据读取模块

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dto/iface_access_restrict.h"
#include "dto/iface_boundary.h"
#include "dto/iface_lanegroup.h"
#include "dto/laneposition.h"
#include "dto/overlap.h"
#include "dto/surfaceobject.h"
#include "dto/types.h"
#include "lane_changepoint.h"

namespace nerd {
namespace api {

/**
 *  @brief 车道交换类型
 *  示例：
 *
 *  ----- 实线  ..... 虚线
 *
 *
 *  ------------------------------------------------------------------------------->
 *                   _______________              ................                   目标车道
 *  ----------------+.............................________________+---------------->
 *   kBothForbidden | kToTargetOpen |  kBothOpen | kToCurrentOpen | kBothForbidden   当前车道
 *  ------------------------------------------------------------------------------->
 *
 */
enum class TraversalType : uint8_t {
  /**
   * @brief 当前车道可通行时，目标车道可向当前车道并线，当前车道不可向目标车道并线
   */
  kToCurrentOpen = 1,
  /**
   * @brief 目标车道可通行时，当前车道可向目标车道并线，目标车道不可向当前车道并线
   */
  kToTargetOpen = 2,
  /**
   * @brief 双侧车道可通行时，可双向并线
   */
  kBothOpen = 3,
  /**
   * @brief 目标车道和当前车道不可并线
   */
  kBothForbidden = 0
};

/**
 * @brief 车道箭头方向
 */
union LaneDirectionMask {
  struct {
    /**
     * @brief 直行
     */
    uint8_t STRAIGHT : 1;
    /**
     * @brief 左转
     */
    uint8_t LEFT : 1;
    /**
     * @brief 右转
     */
    uint8_t RIGHT : 1;
    /**
     * @brief 左前方
     */
    uint8_t SKEW_LEFT : 1;
    /**
     * @brief 右前方
     */
    uint8_t SKEW_RIGHT : 1;
    /**
     * @brief 左转掉头
     */
    uint8_t U_TURN : 1;
    /**
     * @brief 右转掉头
     */
    uint8_t RIGHT_U_TURN : 1;
    /**
     * @brief 预留
     */
    uint8_t RESERVED : 1;
  } mask;
  uint8_t value;

  explicit LaneDirectionMask(uint8_t value) { this->value = value; }
};

/**
 * @brief 路口车道转向信息
 * */
enum class CrossTurnType : uint8_t {
  /**
   * 无
   * */
  kNONE,
  /**
   * 直行
   * */
  kSTRAIGHT,
  /**
   * 左转
   * */
  kLEFT,
  /**
   * 右转
   * */
  kRIGHT,
  /**
   * 掉头
   * */
  kU_TURN
};

/**
 * @brief 车道类型
 */
enum class LaneType : uint8_t {
  kNONE,                     // 无
  kREGULAR,                  // 普通车道
  kCOMPOUND,                 // 加减速复合车道
  kLEFT_ACCELERATION,        // 左侧加速车道
  kLEFT_DECELERATION,        // 左侧减速车道
  kHOV,                      // HOV
  kSLOW,                     // 慢车道
  kSHOULDER,                 // 路肩
  kDRIVABLE_SHOULDER,        // 可行驶路肩
  kCONTROL,                  // 管制车道
  kEMERGENCY_PARKING_STRIP,  // 紧急停车带
  kBUS,                      // 公交车道
  kBICYCLE,                  // 非机动车道
  kTIDE,                     // 潮汐车道
  kDIRECTION_CHANGE,         // 可变车道
  kPARKING_ROAD,             // 停车车道
  kDRIVABLE_PARKING_ROAD,    // 可行驶停车道
  kTRUCK,                    // 货车专用道
  kTIME_LIMIT_BUS,           // 限时公交车道
  kPASSING_BAY,              // 错车道
  kREVERSAL_LEFT_TURN,       // 借道左转车道
  kTAXI,                     // 出租车车道
  kTURN_WAITING,             // 转弯待转区车道
  kDIRECTION_LOCKED,         // 定向车道
  kVIRTUAL,                  // 路口车道
  kVEHICLE_BICYCLE_MIX,      // 机非混合车道
  kMOTOR,                    // 摩托车道
  kU_TURN,                   // 掉头车道
  kTOLL,                     // 收费站车道
  kCHECK_POINT,              // 检查站车道
  kDANGEROUS_ARTICLE,        // 危险品专用车道
  kFORBIDDEN_DRIVE,          // 非行驶区域
  kTHOUGH_LANE_ZONE,         // 借道区
  kSTREET_RAILWAY,           // 有轨电车车道
  kBUS_BAY,                  // 公交港湾车道
  kSPECIAL_CAR,              // 特殊车辆专用车道
  kPEDESTRIANS,              // 人行道
  kETC,                      // ETC车道
  kEMERGENCY,                // 应急车道
  kSTRAIGHT_WAITING,         // 直行待行区车道
  kSTRAIGHT_VIRTUAL,         // 直行路口车道
  kLEFT_VIRTUAL,             // 左转路口车道
  kRIGHT_VIRTUAL,            // 右转路口车道
  kU_TURN_VIRTUAL,           // 掉头路口车道
  kHEDGING,                  // 避险车道
  kEMPTY,                    // 空车道
  kOTHER,                    // 其他车道
  kRIGHT_ACCELERATION,       // 右侧加速车道
  kRIGHT_DECELERATION,       // 右侧减速车道
  kNON_LANE,                 // 非车道
  kSLOPE,                    // 爬跑车道
  kREVERSE_NON_VEHICLE,      // 逆向非机动车道
  kEDGE                      // 边缘车道
};

/**
 * @brief 交换车道类型
 */
enum class TransLaneType : uint8_t {
  /**
   * 无
   */
  NONE = 1,
  /**
   * 车道形成
   */
  BEGIN,
  /**
   * 车道结束
   */
  END,
  /**
   * 车道分流
   */
  DISPATCH,
  /**
   * 车道合流
   */
  JOINT,
  /**
   * 逻辑交换车道
   */
  LOGIC
};

/**
 * @brief 限速采集来源
 */
enum class SpeedSource : uint8_t {
  /**
   * 实际采集
   */
  kREAL = 0,
  /**
   * 理论推断
   */
  kSPECULATION,
  /**
   * 未知
   */
  kNONE = 255
};

enum class LaneNodeChangeKind : uint8_t {
  /**
   * 车道分离
   */
  kLANE_NODE_DETACH = 0,
  /**
   * 车道汇合
   */
  kLANE_NODE_JOIN,
  /**
   * 无变化
   */
  kNONE = 255
};

enum class TopologyChangeKind : uint8_t {
  /**
   * 左向扩展车道
   * Lane.StartNode.LaneNodeChangeKind == kLANE_NODE_DETACH
   */
  kLEFT_DETACH = 0,
  /**
   * 右向扩展车道
   * Lane.StartNode.LaneNodeChangeKind == kLANE_NODE_DETACH
   */
  kRIGHT_DETACH,
  /**
   * 左向汇入车道
   * Lane.EndNode.LaneNodeChangeKind == kLANE_NODE_JOIN
   */
  kLEFT_JOIN,
  /**
   * 右向汇入车道
   * Lane.EndNode.LaneNodeChangeKind == kLANE_NODE_JOIN
   */
  kRIGHT_JOIN,
  /**
   * 双向扩展车道
   */
  kBOTH_DETACH,
  /**
   * 双向汇入车道
   */
  kBOTH_JOIN,
  /**
   * 其他
   */
  kOTHER,
  /**
   * 无变化
   */
  kNONE = 255,
};

/**
 * @brief 可并线区间
 */
struct TraversalRange {
  /**
   * 并线区开始位置，相对于车道中心线
   */
  uint16_t start_position_index{0};
  /**
   * 并线区结束位置，相对于车道中心线
   */
  uint16_t end_position_index{0};
  /**
   * 并线区开始位置的offset比例,*1000
   */
  uint16_t start_offset_ratio{0};
  /**
   * 并线区结束位置offset比例,*1000
   */
  uint16_t end_offset_ratio{0};
  /**
   * 并线区类型
   */
  TraversalType trans_type{TraversalType::kBothForbidden};
};

class ILane;
typedef std::shared_ptr<ILane> ILanePtr;
class IBoundary;
typedef std::shared_ptr<IBoundary> IBoundaryPtr;
typedef std::shared_ptr<const IBoundary> IBoundaryConstPtr;

class ILaneNode;
typedef std::shared_ptr<ILaneNode> ILaneNodePtr;
typedef std::shared_ptr<const ILaneNode> ILaneNodeConstPtr;

/**
 * @brief 车道Node基类
 */
class ILaneNode {
 public:
  virtual ~ILaneNode() = default;

  /**
   * @brief 获取当前NodeID
   * @return NodeID
   */
  virtual NodeIDType GetNodeID() const = 0;

  /**
   * @brief 获取进入的车道集合
   * @return 车道集合
   */
  virtual std::vector<ILaneConstPtr> GetEnterElements() const = 0;

  /**
   * @brief 获取出度的车道集合
   * @return 车道集合
   */
  virtual std::vector<ILaneConstPtr> GetOutElements() const = 0;

  /**
   * @brief 获取以id为下游的进入车道集合
   * 1----------0
   * 2----------0
   * 3----------0
   * @param id 下游id
   * @return 车道集合
   */
  virtual std::vector<ILaneConstPtr> GetEnterElementsByID(LaneIDType id) const = 0;

  /**
   * 根据tpid获取node入度元素
   */
  virtual std::vector<ILaneConstPtr> GetEntryElementsByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取以id为上游的出度车道集合
   * 0----------1
   * 0----------2
   * 0----------3
   * @param id 上游id
   * @return 车道集合
   */
  virtual std::vector<ILaneConstPtr> GetOutElementsByID(LaneIDType id) const = 0;

  /**
   * 根据tpid获取node出度元素
   */
  virtual std::vector<ILaneConstPtr> GetOutElementsByTPID(const TPIDType& tpid) const = 0;

  /**
   * @brief 获取tile内所有路口车道间连接线几何形状
   * @return 几何形状坐标
   */
  virtual std::vector<std::shared_ptr<std::vector<Coordinate>>> GetCrossLineGeometries() const = 0;

  /**
   * @brief 获取指定lane间的路口连接线几何形状
   * @return 连接线几何形状，如果为空，返回nullptr
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetCrossLineGeometriesByLanePair(
      const LaneIDPair &lane_id_pair) const = 0;

  /**
   * 获取车道拓扑变化状态,表述车道分离、汇合
   * @return 变化状态 {@link LaneNodeChangeKind}
   */
  virtual LaneNodeChangeKind GetChangeKind() const = 0;
};

/**
 * @brief 车道基类
 */
class NERD_EXPORT ILane : public ITopology<ILaneNodeConstPtr, ILaneConstPtr, LaneIDType> {
 public:
  /**
   * 获取车道TPID
   */
  virtual TPIDType GetTPID() const = 0;
  /**
   * @brief 获取该车道左侧车道
   * @return 如果无，返回nullptr
   */
  virtual ILaneConstPtr GetLeftLane() const = 0;

  /**
   * @brief 获取右侧车道
   * @return 如果无，返回nullptr
   */
  virtual ILaneConstPtr GetRightLane() const = 0;

  /**
   * @brief 获取左侧Boundary
   * @return 如果无，返回nullptr
   */
  virtual IBoundaryConstPtr GetLeftBoundary() const = 0;

  /**
   * @brief 获取右侧Boundary
   * @return 如果无，返回nullptr
   */
  virtual IBoundaryConstPtr GetRightBoundary() const = 0;

  /**
   * @brief 获取车道向左侧并道的并线条件
   *
   * 并线条件描述的是当前车道到目标车道(左侧或右侧)，按照标线关系计算出的可并线条件，具体参考
   * {@link TraversalType}，该关系不参考车道当前实际的通行状态(如：限行条件、拥堵条件等)
   *
   * 在实际使用时，需要结合目标车道的限行条件进行判断，如果当前车道不可通行，需无视TraversalRange
   *
   * 当车道存在限行条件时，会存在一种特殊的区间: 借道区 {@link BoundaryExtraInfoType::REVERSE_LANE_LEFT}
   * 借道区需要通过该车道的边线BoundarySet进行判断, 借道区内无视车道交规
   *
   * @return 如果没有左侧车道，数组为空
   */
  virtual const std::vector<TraversalRange> &GetOpenToLeft() const = 0;

  /**
   * @brief 获取车道向右侧并道的并线条件
   *
   * 参考GetOpenToLeft
   *
   * @return 如果没有右侧车道，数组为空
   */
  virtual const std::vector<TraversalRange> &GetOpenToRight() const = 0;

  /**
   * @brief 车道类型
   * @return 车道类型列表第一个
   */
  virtual LaneType GetType() const = 0;

  /**
   * @brief 车道类型
   * @return 车道类型列表
   */
  virtual const std::vector<LaneType> &GetTypes() const = 0;

  /**
   * @brief 车道箭头方向，{@link LaneDirectionMask}
   * @return 方向mask
   */
  virtual LaneDirectionMask GetDirection() const = 0;

  /**
   * @brief 最大限速
   * @return 单位:千米/小时
   */
  virtual SpeedType GetMaxSpeedKmph() const = 0;

  /**
   * @brief 最高速度来源
   * @return {@link nerd::api::SpeedSource}
   */
  virtual SpeedSource GetMaxSpeedSource() const = 0;

  /**
   * @brief 最低速度
   * @return 单位:千米/小时
   */
  virtual SpeedType GetMinSpeedKmph() const = 0;

  /**
   * @brief 最低速度来源
   * @return {@link nerd::api::SpeedSource}
   */
  virtual SpeedSource GetMinSpeedSource() const = 0;

  /**
   * @brief 当前车道的通行限制条件
   * @return 如果没有，数组为空
   */
  virtual const std::vector<IAccessRestrictPtr> &GetAccessRestrict() const = 0;

  /**
   * @brief 当前车道的坡度曲率数据
   * @return 如果不存在返回nullptr
   */
  virtual const std::shared_ptr<std::vector<LanePosition>> &GetLanePosition() const = 0;

  /**
   * 根据到起点距离，获取车道上地坡度曲率数据
   * 当存在车道曲率数据时有效
   * @param distance_to_begin 到车道起点距离
   * @return 坡度曲率数据,如果距离超过车道长度，返回最后一个坡度曲率数据, 如果不存在，则LanePositionID无效
   */
  virtual LanePosition CalcLanePosition(double distance_to_begin) const = 0;

  /**
   * 根据到起点距离，获取车道中线上的映射点
   * @param distance_to_begin
   * @param target_p
   * @return
   */
  virtual void CalcLaneCenterPosition(double distance_to_begin, InLinePosition &target_p) const = 0;

  /**
   * @brief 车道几何中心线
   * @note: 4K类型数据几何形点为空;
   * @return 车道几何中心线，数据坐标系
   */
  virtual const std::shared_ptr<std::vector<Coordinate>> &GetCenterGeometry() const = 0;

  /**
   * @brief 获取车道中心线上每个点到起点的距离
   * @return 车道中心线上每个点到起点的距离
   */
  virtual const std::vector<double> &GetLaneCenterGeometryDistanceToBegin() const = 0;

  /**
   * @brief 车道长度
   * @return 单位: CM
   */
  virtual LaneLengthType GetLengthCm() const = 0;

  /**
   * @brief 车道在LaneGroup中的顺序，从右到左升序
   * @return 序号
   */
  virtual SeqNumType GetSeqNum() const = 0;

  /**
   * @brief 车道相关的附属物，如车道线、警示牌、红绿灯等
   * @return 车道附属物集合
   */
  virtual const SurfaceObjectCollect &GetSurfaceObjects() const = 0;

  /**
   * @brief 车道相关变化点
   * @return 车道变化点集合
   */
  virtual const LaneChangePointCollect &GetLaneChangePoints() const = 0;

  /**
   * @brief 车道形态类型
   * @return 形态类型
   */
  virtual TransLaneType GetTransType() const = 0;

  /**
   * 获取车道相对于上游/下游拓扑变化类型
   * @return 变化类型
   */
  virtual TopologyChangeKind GetTopologyChangeKind() const = 0;

  /**
   * @brief lane属于的lane group id
   * @return lane group id
   */
  virtual LaneGroupIDType GetAttachedLaneGroupID() const = 0;

  /**
   * 所属lanegroup 的tpid
   */
  virtual TPIDType GetAttachedLaneGroupTPID() const = 0;

  /**
   * @brief lane属于的lane group
   * @return lane group
   */
  virtual ILaneGroupWeakPtr GetAttachedLaneGroup() const = 0;
  /**
   * @brief lane属于的cross area
   * @return cross area
   */
  virtual ICrossAreaWeakPtr GetAttachedCrossArea() const = 0;
  /**
   * @brief 获取当前lane与指定lane的跨路口连接线
   * @return 如果不存在，返回nullptr
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetCrossLineGeometryByID(const LaneIDType &lane_id) const = 0;

  /**
   * @brief 判断当前lane与rect是否相交
   * @return false: 不相交, true: 相交
   */
  virtual bool IntersectsWithRange(const Rect &range) const = 0;
  /**
   * 获取车道的最大宽度
   * @return 单位:米
   */
  virtual double GetWidthMax() const = 0;
  /**
   * 获取车道的最小宽度
   * @return 单位:米
   */
  virtual double GetWidthMin() const = 0;

  /**
   * 根据到车道起点距离，获取当前车道的宽度
   * @param distance_to_begin 到车道起点距离
   * @return 距离，单位:米，distance_to_begin大于车道长度时，返回最后的宽度
   */
  virtual double CalcWidth(double distance_to_begin) const = 0;

  /**
   * 根据到起始点的距离找到最近的中心线上点的下标
   * @param distance_to_begin
   * @return 距离超出中心线最长距离，返回-1
   */
  virtual int FindCenterIndexByDistanceToBegin(double distance_to_begin) const = 0;

  /**
   * 根据到车道起点距离，计算坡度曲率数据
   * @param distance_to_begin [IN] 到车道起点距离
   * @param position [OUT] 线内点位置信息
   * @return 坡度曲率数据，distance_to_begin大于车道长度时，返回最后的坡度曲率数据
   */
  virtual LanePosition CalculatePosition(double distance_to_begin, InLinePosition &position) const = 0;

  /**
   * 获取任意一个点到车道中心线起点的距离
   * @param coordinate [IN] 坐标，GCJ02
   * @param position [OUT] 线内点位置信息
   * @return 指定下标到起点的距离，单位:米
   */
  virtual double CalcCenterDistanceToBegin(const nerd::api::GeoCoordinate &coordinate,
                                           InLinePosition &position) const = 0;

  /**
   * 获取任意一个点到车道中心线起点的距离
   * @param coordinate [IN] 坐标，球面坐标
   * @param position [OUT] 线内点位置信息
   * @return 指定下标到起点的距离，单位:米
   */
  virtual double CalcCenterDistanceToBegin(const nerd::api::Coordinate &coordinate, InLinePosition &position) const = 0;

  /**
   * 获取任意一个面到车道中心线起点的距离
   * @param surface_obj [IN] ISurfaceObject表面类型
   * @param position [OUT] 线内点位置信息数组
   * @note SL S:垂足到车道中心线起点距离 L:点到垂足距离
   * @return InLinePosition的大小，根据SurfaceObject的类型分为0、1、2
   */
  virtual int CalcSurfaceObjectSL(nerd::api::ISurfaceObjectConstPtr surface_obj,
                                  std::vector<InLinePosition> &position) const = 0;

  /**
   * 根据到车道起点距离，获取到车道左侧边界的距离
   * @param distance_to_begin [IN] 距离，单位：米
   * @param position [OUT] 线内点位置信息
   * @return 指定下标到左侧边界的距离，单位：米
   */
  virtual double CalcDistanceToLeftBoundary(double distance_to_begin, InLinePosition &position) const = 0;

  /**
   * 根据到车道起点距离，获取到车道右侧边界的距离
   * @param distance_to_begin 距离，单位：米
   * @param[out] position 线内点位置信息
   * @return 指定下标到右侧边界的距离，单位：米
   */
  virtual double CalcDistanceToRightBoundary(double distance_to_begin, InLinePosition &position) const = 0;

  /**
   * 根据到车道起点距离，获取到道路左侧边界的距离
   * @warning 暂时不支持，返回-1;
   * @param distance_to_begin 距离，单位：米
   * @param[out] position 线内点位置信息
   * @return 指定下标到左侧边界的距离，单位：米
   */
  virtual double CalcDistanceToLeftRoadBoundary(double distance_to_begin, InLinePosition &position) const = 0;

  /**
   * 根据到车道起点距离，获取到道路右侧边界的距离
   * @warning 暂时不支持，返回-1;
   * @param distance_to_begin 距离，单位：米
   * @param[out] position 线内点位置信息
   * @return 指定下标到右侧边界的距离，单位：米
   */
  virtual double CalcDistanceToRightRoadBoundary(double distance_to_begin, InLinePosition &position) const = 0;

  /**
   * 根据到车道中心线的SL坐标，获取到车道左侧边界的距离
   * @param coordinate
   * @param position 线内点位置信息
   * @return 指定下标到左侧边界的距离，单位：米
   */
  virtual double CalcDistanceToLeftBoundary(const nerd::api::GeoCoordinate &coordinate,
                                            InLinePosition &position) const = 0;

  /**
   * 根据坐标，获取到车道右侧边界的距离
   * @param coordinate
   * @param position 线内点位置信息
   * @return 指定下标到右侧边界的距离，单位：米
   */
  virtual double CalcDistanceToRightBoundary(const nerd::api::GeoCoordinate &coordinate,
                                             InLinePosition &position) const = 0;

  /**
   * 根据到车道起点距离，获取到道路左侧边界的距离
   * @param coordinate
   * @param position 线内点位置信息
   * @return 指定下标到左侧边界的距离，单位：米
   */
  virtual double CalcDistanceToLeftRoadBoundary(const nerd::api::GeoCoordinate &coordinate,
                                                InLinePosition &position) const = 0;

  /**
   * 根据到车道起点距离，获取到道路右侧边界的距离
   * @param coordinate
   * @param position 线内点位置信息
   * @return 指定下标到右侧边界的距离，单位：米
   */
  virtual double CalcDistanceToRightRoadBoundary(const nerd::api::GeoCoordinate &coordinate,
                                                 InLinePosition &position) const = 0;

  /**
   * 获取当前车道及当前车道所在的道路的挂接surface object
   * @return 挂接的surface object
   */
  virtual SurfaceObjectCollect GetSurfaceObjectsWithLaneGroup() const = 0;

  /**
   * @deprecated
   * @brief 获取当前车道关联的覆盖面类型
   * 覆盖关系:
   *   1. 几何关系
   *   2. 逻辑关系
   * @return 有覆盖关系的覆盖面
   */
  virtual const OverlapCollect &GetOverlaps() const = 0;

  /**
   * @brief 获取当前车道中心线上面的break point点序列
   * @return 返回车道中心线的打断点序列, 返回数据的高程信息的单位是米;
   * */
  virtual const std::vector<BreakPointInfo> GetCenterBreakPoints() const = 0;
  /**
   * @brief 获取当前车道中心线上面的break point点序列
   * 备注: 该接口与GetCenterBreakPoints的差别在于返回高程信息单位不同;
   * @return 返回车道中心线的打断点序列, 返回数据的高程信息的单位是厘米;
   * */
  virtual const std::vector<BreakPointInfo> &GetCenterBreakPointsCm() const = 0;

  /**
   * @brief 获取该车道的转向信息
   * 目前只有路口内车道有有效值
   * 非路口车道统一返回无效值kNONE
   * @return 转向信息
   * */
  virtual CrossTurnType GetCrossTurn() const = 0;
  /**
   * 实时计算，耗时高
   * @brief 获取该车道线上面所有几何形点
   * 目前点序列包括两种
   * 1. 原始高精采集几何点 2. 编译增加的打断几何点
   * @return 返回点序列
   * */
  virtual std::vector<Coordinate> GetCenterAllGeometries() const = 0;
  /**
   * 实时计算，耗时高
   * @deprecated 建议在调用GetCenterAllGeometries后，
   *          使用: nerd::api::utils::CalcGeometryDistanceToBegin计算，否则性能开销极大
   * @brief 获取该车道线上面几何点到起点距离
   * @return 返回距离序列
   * */
  virtual std::vector<double> GetCenterAllGeometriesDistanceToBegin() const = 0;

  /**
   * @brief 获取下游LaneID
   * @return 下游LaneID集合
   */
  virtual std::vector<LaneIDType> GetNextIDs() const = 0;
  /**
   * @brief 获取上游LaneID
   * @return 上游LaneID集合
   */
  virtual std::vector<LaneIDType> GetPreviousIDs() const = 0;

  /**
   * 拓扑but tpid
   */
  virtual std::vector<TPIDType> GetNextTPIDs() const = 0;

  virtual std::vector<TPIDType> GetPreviousTPIDs() const = 0;
};

}  // namespace api
}  // namespace nerd
