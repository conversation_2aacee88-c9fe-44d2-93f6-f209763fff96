//
// Created by liaoxinting on 17/06/2024.
//

#pragma once

#include <string>
#include <vector>
#include <set>

namespace nerd {
namespace api {

/**
 * 百变数据要素类型
 */
enum class AgileMapInfoType : uint8_t {
  kPoint,     // 点
  kLine,      // 线
  kRegion     // 面
};

/**
 * 百变数据要素信息基础类
 * 根据点线面类型不同有集成类
 */
class IAgileMapInfo {
 public:
  /**
   * 要素id
   */
  FeatureIDType  feature_id_;
  /**
   * 要素优先级
   */
  uint32_t priority_;
  /**
   * 唯一ID
   */
  std::string raw_id_ = "";
  /**
   * 显示等级， 如过是13级tile， 将最多在14，15，16 级展示， 后端用3个bit位表示
   */
  std::vector<int8_t> zoom_level_;
  /**
   * nerd不解析json对象，将解码后的json str原文转发给端
   */
  std::string static_content = "{}";
  /**
   * nerd不解析json对象，将解码后的json str原文转发给端
   */
  std::string dynamic_content = "{}";
  /**
   * 获取要素类型
   * @return
   */
  virtual AgileMapInfoType GetAgileMapInfoType() = 0;
  /**
   * 析构函数
   */
  virtual ~IAgileMapInfo() = default;
};

/**
 * 百变点对象结构
 */
struct AgileMapPointInfo : public IAgileMapInfo {
  /**
   * 坐标信息
   */
  Coordinate geometry3d_;
  /**
   * 获取要素类型
   * @return
   */
  AgileMapInfoType GetAgileMapInfoType() override {
    return AgileMapInfoType::kPoint;
  }

  /**
   * 旋转角度, 范围[0,360], 备注: 若值为-1代表没有角度;
   * 场景: 对应底商的文字需要贴墙显示;
   */
  float angle{-1};
  /**
   * POI关联楼块的质心坐标
   */
  Coordinate build_center{0, 0};
};

/**
 * 百变线对象结构
 */
struct AgileMapLineInfo : public IAgileMapInfo {
  /**
   * 样式ID
   */
  uint32_t style_id_;
  /**
   * 如果tile_id 为0 则表示没有ref_poi
   */
  FeatureIDType ref_poi_idx_;
  /**
   * 轮廓坐标
   */
  std::vector<Coordinate> geometry2d_;
  /**
   * 获取要素类型
   * @return
   */
  AgileMapInfoType GetAgileMapInfoType() override {
    return AgileMapInfoType::kLine;
  }
};

/**
 * 百变区域面对象结构
 */
struct AgileMapRegionInfo : public IAgileMapInfo {
  /**
   * 样式ID
   */
  uint32_t style_id_;
  /**
   * 如果tile_id 为0 则表示没有ref_poi
   */
  FeatureIDType ref_poi_idx_;
  /**
   * 公共边下表
   */
  std::vector<uint16_t> common_edges_;
  /**
   * 轮廓坐标
   */
  std::vector<Coordinate> geometry2d_;
  /**
   * 获取要素类型
   * @return
   */
  AgileMapInfoType GetAgileMapInfoType() override {
    return AgileMapInfoType::kRegion;
  }
};

}  // namespace api
}  // namespace nerd
