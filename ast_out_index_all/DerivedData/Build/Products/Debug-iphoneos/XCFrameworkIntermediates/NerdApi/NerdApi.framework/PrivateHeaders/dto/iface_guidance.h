// Copyright 2021 Tencent Inc.  All rights reserved.

#pragma once

#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * 收费站支付类型
 */
enum class ChargeType : uint8_t {
  /**
   * 现金
   */
  kCash = 0,
  /**
   * ETC
   */
  kETC,
  /**
   * 微信支付
   */
  kWechat,
  /**
   * 支付宝支付
   */
  kAlipay,
  /**
   * 其他支付
   */
  kOtherPay,
  /**
   * 预留
   */
  kReserved,
};
/**
 * 收费站车道类型
 */
enum class TollType : uint8_t {
  kNone = 0,
  /**
   * 称重车道
   */
  kWeightLane = 1,
  /**
   * 绿色车道
   */
  kGreenLane,
  /**
   * 通用车道
   */
  kNormalLane
};
/**
 * 大门通行类型
 */
enum class PassageType : uint8_t {
  kNone = 0,
  /**
   * 无障碍门禁
   */
  kNoBarrier = 1,
  /**
   * 车牌/刷卡/抬杆
   */
  kLicenseCardLiftRod,
  /**
   * 授权门禁
   */
  kAuthorization,
  /**
   * 分时段
   */
  kDayParting,
  /**
   * 紧急通道门禁
   */
  kEmergency,
  /**
   * 不可通行门禁
   */
  kForbidden
};
/**
 * 大门收费类型
 */
enum class FeeType : uint8_t {
  kNone = 0,
  /**
   * 免费
   */
  kFree,
  /**
   * 门牌收费
   */
  kNumberPlateFee,
  /**
   * 其他收费
   */
  kOtherFee
};

/**
 * 授权类型
 */
enum class AllowType : uint8_t {
  kNone = 0,
  /**
   * 登记可通行
   */
  kRegister,
  /**
   * 有出入证可通行
   */
  kLicense,
  /**
   * 内部可入外部不可入
   */
  kInNoOut,
  /**
   * 其他
   */
  kOther
};

/**
 * 红绿灯类型
 */
enum class TrafficLightType : uint8_t {
  kNone = 0,
  /**
   * 掉头灯
   */
  kUturnLight = 1,
  /**
   * 右转灯
   */
  kTurnRightLight,
  /**
   * 借道左转灯
   */
  kBorrowLeftLight
};

/**
 * 坡度类型
 */
enum class LinkSlopeType : uint8_t {
  /**
   * 未调查
   */
  kNoSurvey = 0,
  /**
   * 上坡
   */
  kUpSlope,
  /**
   * 平坡
   */
  kFlatSlope,
  /**
   * 下坡
   */
  kDownSlope
};

/**
 *  车道箭头信息（车信信息扩展）
 */
enum LaneArrowType : uint16_t {
  /**
   * 空白
   */
  KLaneBlank = 0,
  /**
   * 直行
   */
  KLaneStraight = 1,
  /**
   * 左转
   */
  KLaneLeft = 1 << 1,
  /**
   * 右转
   */
  KLaneRight = 1 << 2,
  /**
   * 左后转
   */
  KLaneLeftRound = 1 << 3,
  /**
   * 右后转
   */
  KLaneRightRound = 1 << 4,
  /**
   * 左斜前
   */
  kLaneLeftDiagonalTurn = 1 << 5,
  /**
   * 右斜前
   */
  kLaneRightDiagonalTurn = 1 << 6,
};
/**
 *  车道类型（车信信息扩展）
 */
enum LaneExtensionType : uint32_t {
  /**
   * 常规车道
   */
  KNormal = 0,
  /**
   * 公交车道
   */
  KBus = 1,
  /**
   * 无时段公交车道（复合车道）
   */
  KMulti = 1 << 1,
  /**
   * 加速车道
   */
  KAccelerate = 1 << 2,
  /**
   * 减速车道
   */
  KDecelerate = 1 << 3,
  /**
   * HOV车道
   */
  KHov = 1 << 4,
  /**
   * 快车道
   */
  KFast = 1 << 5,
  /**
   * 慢车道
   */
  KSlow = 1 << 6,
  /**
   * 超车道
   */
  KOvertake = 1 << 7,
  /**
   * 可行驶路前带
   */
  KShoulder = 1 << 8,
  /**
   * 卡车停车道
   */
  KTruckstop = 1 << 9,
  /**
   * 管制车道
   */
  KControl = 1 << 10,
  /**
   * 潮汐车道
   */
  KTide = 1 << 11,
  /**
   * 中心转向道
   */
  KCenterturn = 1 << 12,
  /**
   * 转向车道
   */
  KTurn = 1 << 13,
  /**
   * 空车道
   */
  KBlank = 1 << 14,
  /**
   * 可变车道
   */
  KTurnalterable = 1 << 15,
  /**
   * 借道左转车道
   */
  KLeftbyway = 1 << 16,
  /**
   * 其他
   */
  KOthers = 1 << 17,
  /**
   * 摩托车专用车道
   */
  KMotorbike = 1 << 18,
};

/**
 * 诱导数据类型
 */
enum class GuidanceType : uint8_t {
  kNone = 0,
  /**
   * 收费站
   */
  kToll = 1,
  /**
   * 大门
   */
  kGate,
  /**
   * 分叉口提示
   */
  kFork,
  /**
   * 交通信号灯
   */
  kTrafficLight,
  /**
   * 电子眼
   */
  kCamera,
  /**
   * 收费站
   */
  kTollStation,
  /**
   * 交通警示
   */
  kWarningSign,
  /**
   * 车道信息
   */
  kLaneMetaInfo,
  /**
   * 长实线
   */
  kLsl,
  /**
   * 方向看板
   */
  kBoard,
  /**
   * 点限速
   */
  kPointSpeed,
  /**
   * 红绿灯控制
   */
  kTrafficLightCtrl,
  /**
   * 立交
   */
  kZLevelGroup,
  /**
   * 放大图
   */
  kBranchPic,
  /**
   * 坡度
   */
  kSlope,
  /**
   * 车道信息(离线)
   */
  kLaneInfo,
  /**
   * 转向模型(离线)
   */
  kTurnModle
};

/**
 * 电子眼类型
 */
enum class CameraType : uint8_t {
  kNone = 0,

  kRedLightRunning,

  kElectronicTagging,

  kFixedSpeed,

  kMovingSpeed,

  kBusLanes,

  kOneWay,

  kEmergencyLanes,

  kNonMotorizedVehicle,

  kInterSpotEnter,

  kInterSpotExit,

  kContinuousEnter,

  kContinuousExit,

  kOthers,

  kHov,

  kZipper,

  kVersion1,

  kTailNumber,

  kToBeijing,

  kWhistle,

  kBusStation,

  /**
   * 禁止右转
   */
  kForbidTurnRight,

  /**
   * 违反禁止标线
   */
  kForbidLine,

  /**
   * 非法停车
   */
  kIllegalStop,

  /**
   * 超低速
   */
  kLowSpeed,

  /**
   * 可变限速
   */
  kVariableSpeed,

  /**
   * 分车道限速
   */
  kLaneSpeed,

  /**
   * 分车种限速
   */
  kCarTypeSpeed,

  /**
   * 违规占车道
   */
  kLane,

  /**
   * 违规过路口
   */
  kIllegalCross,

  /**
   * 违反禁令标志
   */
  kForbidSign,

  /**
   * 违规用灯
   */
  kIllegalUseLight,

  /**
   * 安全带
   */
  kBelt,

  /**
   * 开车打手机
   */
  kPhone,

  /**
   * 环保限行
   */
  kEnvironmentLimit,

  /**
   * 礼让行人
   */
  kProtectHuman,

  /**
   * 车辆未按规定年检
   */
  kYearExamine,

  /**
   * 尾气超标
   */
  kOffgas,

  /**
   * 路况监控
   */
  kTrafficDispatch,

  /**
   * 出入口摄像头
   */
  kEntrance,

  /**
   * 禁止掉头摄像头
   */
  kForbidTurnAround,

  /**
   * etc收费电子眼
   */
  kEtcToll,

  /**
   * 不按导向车道行驶
   */
  kNotFollowGuideLane,

  /**
   * 流量监控（车流量）
   */
  kTrafficFlowMonitor,

  /**
   * 保持安全车距
   */
  kKeepSafeDistance,

  /**
   * 违法变道
   */
  kIllegalLaneChange
};

/**
 * 电子眼场景
 */
enum class CameraScene : uint8_t {
  kNone = 0,

  /**
   * 桥壁后
   */
  kAfterBridge,

  /**
   * 标牌后
   */
  kAfterBoard,

  /**
   * 高架后
   */
  kAfterElevate,

  /**
   * 斑马线前
   */
  kBeforeZebra,

  /**
   * 道路隔离带中
   */
  kIsolation,

  /**
   * 道路左侧
   */
  kRoadLeft,

  /**
   * 道路右侧
   */
  kRoadRight,

  /**
   * 人工挖掘高架处
   */
  kArtificalElevate,

  /**
   * 人工挖掘天桥处
   */
  kArtificalOverpass
};

/**
 * 拍摄位置
 */
enum class ShootPosition : uint8_t {
  kNone = 0,
  /**
   * 车头
   */
  kHead,

  /**
   * 车尾
   */
  kTail,

  /**
   * 未调查
   */
  kNotSurvey
};

/**
 * 收费站
 */
enum class TollGateType : uint8_t {
  /**
   * 未知
   */
  kUnknown = 0,

  /**
   * 出口
   */
  kExit,

  /**
   * 入口
   */
  kEntrance,

  /**
   * 出入口
   */
  KExitAndEntrance
};

enum class TollSeparateType : uint8_t {
  /**
   * 未知
   */
  kUnknown,

  /**
   * 单退出
   */
  kExitSingle,

  /**
   * 多退出
   */
  kExitMultiply
};

/**
 * 长实线类型
 */
enum class LSLType : uint8_t {
  kNone = 0,
  /**
   * 单实线
   */
  kSingleSolid,

  /**
   * 左虚右实
   */
  kLeftDotRightSolid,

  /**
   * 左实右虚
   */
  kLeftSolidRightDot
};

/**
 * 方向看板类型
 */
enum class BoardType : uint8_t {
  kNone = 0,
  /**
   * IC
   */
  kIC,
  /**
   * 高速方向
   */
  kHighway,
  /**
   * 普通方向
   */
  kNormal
};

/**
 * 方向看板名称属性
 */
enum class BoardNameDirection : uint8_t {
  kNone = 0,
  /**
   * 方向名称
   */
  kDirection,
  /**
   * 出口名称
   */
  kExit,
  /**
   * 入口信息
   */
  kEnter
};

/**
 * 点限速类型
 */
enum class PointSpeedType : uint8_t {
  kNone = 0,
  /**
   * 最高限速
   */
  kHighest,
  /**
   * 最低限速
   */
  kLowest,
  /**
   * 解除限速
   */
  kRelease,
  /**
   * 条件限速
   */
  kCondition,
  /**
   * 建议
   */
  kSuggest,
  /**
   * 可变
   */
  kVariable
};

/**
 * 点限速场景
 */
enum class PointSpeedScene : uint8_t {
  kNone = 0,
  /**
   * 隧道
   */
  kTunnel,
  /**
   * 匝道
   */
  kZamp,
  /**
   * 桥梁
   */
  kBridge,
  /**
   * 施工
   */
  kWorking,
  /**
   * 加减速车道
   */
  kAccDecLane,
  /**
   * 掉头口
   */
  kUTurn,
  /**
   * 高速终止
   */
  kHighwayEnd,
  /**
   * 桥下
   */
  kUnderBridge,
  /**
   * 转弯车辆
   */
  kTurnVehicle,
  /**
   * 服务区
   */
  kSA,
  /**
   * 检查站
   */
  kCheckpoint,
  /**
   * 收费站
   */
  kTollStation,
  /**
   * 提右
   */
  kAheadRight,
  /**
   * 提左
   */
  kAheadLeft,
  /**
   * 学校
   */
  kSchool,
  /**
   * 工厂
   */
  kFactory,
  /**
   * 村庄
   */
  kCountry,
  /**
   * 急转弯
   */
  kSharpTurn,
  /**
   * 路口
   */
  kCross,
  /**
   * 危险路段
   */
  kDanger,
  /**
   * 测速
   */
  kTestSpeed,
  /**
   * 区域
   */
  kArea,
  /**
   * 冻土
   */
  kGelisols,
  /**
   * 整条路段
   */
  kWhole,
  /**
   * 预留1
   */
  kReserved1,
  /**
   * 预留2
   */
  kReserved2
};

/**
 * 点限速相对位置
 */
enum class PointSpeedLocation : uint8_t {
  kNone = 0,
  /**
   * 相对作用道路的左侧
   */
  kLeft,
  /**
   * 相对作用道路的右侧
   */
  kRight,
  /**
   * 相对作用道路的上侧
   */
  kUp,
  /**
   * 相对作用道路的下侧
   */
  kDown,
  /**
   * 同向道路中间
   */
  kMiddle
};

/**
 * 放大图引导方向
 */
enum class GuideDirection : uint8_t {
  /**
   * 无方向
   */
  kNone = 0,
  /**
   * 右方向
   */
  kRight,
  /**
   * 左方向
   */
  kLeft
};

/**
 * 放大图类型
 */
enum class BranchPicType : uint8_t {
  kNone = 0,
  /**
   * 高速道路分歧图
   */
  kHighwayAmbiguous,
  /**
   * 高速出口实景图
   */
  kHighwayExit,
  /**
   * 高速入口实景图
   */
  kHighwayEnter,
  /**
   * 普通道路复杂路口图
   */
  kComplexCross,
  /**
   * 普通路口实景图
   */
  kNormalScene,
  /**
   * 3D图
   */
  kPic3D
};

/**
 * 放大图引导属性
 */
enum class GuideAttribute : uint8_t {
  kDefault = 0,
  kExit,
  kEnter,
  kSA,
  kPA,
  kJCT,
};

/**
 * 放大图名称标识
 */
enum class NameKind : uint8_t {
  kDefault = 0,
  kIC,
  kSA,
  kPA,
  kJCT,
  kExit,
  kEnter,
  kRamp,
  kEnterExit,
};

/**
 * 红绿灯控制类型
 */
enum class TrafficLightCtrlType : uint8_t {
  kNone = 0,
  /**
   * 掉头
   */
  kUTurn,
  /**
   * 借道左转
   */
  kBorrowLeft,
  /**
   * 右转
   */
  kTurnRight,
};

/**
 * 诱导类信息
 */
class NERD_EXPORT IGuidanceInfo {
 public:
  virtual ~IGuidanceInfo() = default;
  /**
   * 诱导数据类型
   */
  virtual GuidanceType GetType() const = 0;
  /**
   * 进入 Link ID
   */
  LinkIDType GetInLinkID() const { return in_link_id_; }
  /**
   * 第一根线与第二根线的交点
   */
  NodeIDType GetNodeID() const { return node_id_; }
  /**
   * 退出 Link 序列
   */
  std::vector<LinkIDType> GetPassLinkIDS() const { return pass_link_ids_; }

 public:
  // 私有接口
  /// @private
  void SetInLinkID(LinkIDType id) { in_link_id_ = id; }
  /// @private
  void SetNodeID(NodeIDType node_id) { node_id_ = node_id; }
  /// @private
  void SetPassLinks(std::vector<LinkIDType> &&link_ids) { pass_link_ids_ = std::move(link_ids); }

 private:
  LinkIDType in_link_id_;
  NodeIDType node_id_;
  std::vector<LinkIDType> pass_link_ids_;
};

/**
 * 坡度虚基类
 */
class IGuidanceSlope : public IGuidanceInfo {
 public:
  /**
   * 获取坡度信息
   */
  virtual LinkSlopeType GetSlopeType() const = 0;
};

/**
 * 坡度信息类
 */
struct GuidanceSlope : public IGuidanceSlope {
 public:
  /**
   * 诱导数据类型
   */
  GuidanceType GetType() const override { return GuidanceType::kSlope; }
  /**
   * 获取坡度类型
   */
  LinkSlopeType GetSlopeType() const override { return slope_type_; }

 public:
  void SetSlopeType(LinkSlopeType slope_type) { slope_type_ = slope_type; }

 private:
  LinkSlopeType slope_type_{LinkSlopeType::kNoSurvey};
};

/**
 * 收费门信息
 * 备注: 该类型已经废弃, 后期删除;
 */
struct TollGate : public IGuidanceInfo {
 public:
  /**
   * 诱导数据类型
   */
  GuidanceType GetType() const override { return GuidanceType::kToll; }
  /**
   * 收费方式
   */
  const std::vector<ChargeType> &GetCharges() const { return charges_; }
  /**
   * 通道类型
   */
  const std::vector<TollType> &GetTollTypes() const { return toll_types_; }

 public:
  void SetCharges(std::vector<ChargeType> &&charges) { charges_ = std::move(charges); }
  void SetToolTypes(std::vector<TollType> &&tolls) { toll_types_ = std::move(tolls); }

 private:
  std::vector<ChargeType> charges_;
  std::vector<TollType> toll_types_;
};

/**
 * 电子眼信息
 */
struct Camera : public IGuidanceInfo {
  /**
   * 类型 多类型取首位
   */
  CameraType type{CameraType::kNone};

  /**
   * 分组编号，区间或连续限速区域表示标识
   */
  uint64_t group_id{0};

  /**
   * 坐标x
   */
  Coordinate coordinate{0, 0};

  /**
   * 限速值
   */
  uint8_t speed_limit{0};

  /**
   * 作用方向
   */
  LinkDirection direction{LinkDirection::kUNKNOWN};

  /**
   * 拍摄位置
   */
  ShootPosition position{ShootPosition::kNone};

  /**
   * 开始作用时间
   */

  std::string start_date;

  /**
   * 结束作用时间
   */
  std::string end_date;

  /**
   * 作用车辆类型
   */
  std::vector<VehicleType> vehicles;

  /**
   * 是否高危
   */
  bool high_risk{false};

  /**
   * 场景
   */
  std::vector<CameraScene> scenes;

  /**
   * 时间段
   */
  std::string date;

  GuidanceType GetType() const override { return GuidanceType::kCamera; }
};

/**
 * 收费站
 */
struct TollStation : public IGuidanceInfo {
  /**
   * 名称
   */
  std::string name;

  /**
   * 作用方向
   */
  LinkDirection direction{LinkDirection::kUNKNOWN};

  /**
   * 通道收费方式
   */
  std::vector<uint8_t> toll_means;

  /**
   * 通道类型
   */
  std::vector<uint8_t> passage_ways;

  /**
   * 出入口标识
   */
  TollGateType gate_flag{TollGateType::kUnknown};

  /**
   * 英文名称 : Unimap规格无数据, 暂时无赋值处理, 维护一段时间后删除;
   */
  std::string name_eng;

  /**
   * 分岔路标识
   */
  TollSeparateType separate_flag{TollSeparateType::kUnknown};

  /**
   * 通道方向分组, 按照退出方向给车道进行分组, 左边三个右边四个为3|4, 可空
   */
  std::vector<uint8_t> lane_group;

  /**
   * 针对通道方向分组的方向名称, 3|4 左三右四车道对应 A方向|B方向, 可空
   */
  std::vector<std::string> group_name;

  GuidanceType GetType() const override { return GuidanceType::kTollStation; }
};

/**
 * 方向看板
 */
struct Board : public IGuidanceInfo {
  /**
   * 类型
   */
  BoardType type{BoardType::kNone};

  /**
   * 出口编号，可空
   */
  std::string ic_exitnum;

  /**
   * 方向看板名称，可空
   */
  std::vector<std::string> names;

  /**
   * 名称属性，可空，与names对应
   */
  std::vector<BoardNameDirection> name_directions;

  /**
   * 方向看板名称英文
   */
  std::vector<std::string> names_eng;

  GuidanceType GetType() const override { return GuidanceType::kBoard; }
};

/**
 * 点限速
 */
struct PointSpeed : public IGuidanceInfo {
  /**
   * 限速牌类型
   */
  PointSpeedType type{PointSpeedType::kNone};
  /**
   * 限速值
   */
  uint8_t speed_limit{0};
  /**
   * 提前预告距离，单位米，可空
   */
  uint16_t pre_dist{0};
  /**
   * 坐标
   */
  Coordinate coordinate{0, 0};
  /**
   * 作用方向
   */
  LinkDirection direction{LinkDirection::kUNKNOWN};
  /**
   * 作用道路，可空
   */
  LinkIDType affect_link_id{0, 0};
  /**
   * 作用道路方向，可空
   */
  LinkDirection affect_direction{LinkDirection::kUNKNOWN};
  /**
   * 限速场景，可空
   */
  std::vector<PointSpeedScene> speed_scenes;
  /**
   * 时间段限制，可空
   */
  std::string date;
  /**
   * 特殊时间类型，可空
   */
  std::vector<SpecialTimeType> special_times;
  /**
   * 引导坐标，可空
   */
  Coordinate nav_coordinate{0, 0};
  /**
   * 相对位置，可空
   */
  PointSpeedLocation location{PointSpeedLocation::kNone};
  /**
   * 作用车辆类型，可空
   */
  std::vector<VehicleType> vehicles;

  GuidanceType GetType() const override { return GuidanceType::kPointSpeed; }
};

/**
 * 红绿灯控制
 */
struct TrafficLightCtrl : public IGuidanceInfo {
  /**
   * 控制类型
   */
  TrafficLightCtrlType type{TrafficLightCtrlType::kNone};

  GuidanceType GetType() const override { return GuidanceType::kTrafficLightCtrl; }
};

/**
 * 交通告警
 */
struct LinkWarningSign : public IGuidanceInfo {
  /**
   * 警示牌类别
   */
  TrafficSignType trc_kind{TrafficSignType::kOTHER_UNDEFINED};

  /**
   * 提前预告距离
   */
  uint16_t foretold_dist{0};

  /**
   * 有效距离
   */
  uint16_t control_dist{0};

  /**
   * 作用方向
   */
  LinkDirection linkDirection{LinkDirection::kUNKNOWN};

  /**
   * 文字说明
   */
  std::string descript;

  /**
   * 坐标
   */
  Coordinate coordinate{0, 0};

  GuidanceType GetType() const override { return GuidanceType::kWarningSign; }
};

/**
 * 立交
 */
struct ZLinkLevel {
  LinkIDType link_id{0};
  /**
   * 高度值，-9~9，数字越大表示高度越高，0表示水平面
   */
  int8_t z{0};
};

/**
 * 车道通行信息
 */
struct LaneTrafficInfo {
  /**
   * 公交专用道
   */
  bool is_bus{false};
  /**
   * 车道类型 LaneExtensionType 中多个类型进行组合
   */
  uint32_t lane_type{0};
  /**
   * 箭头信息 LaneArrowType 中多个类型进行组合
   */
  uint32_t arrow{0};
};
/**
 * 车信条件表
 */
struct GuidanceLaneCond;
using LaneCond = GuidanceLaneCond;
/**
 * 车道转向拓扑
 */
struct LaneTopo {
  /**
   * 转向箭头
   */
  LaneArrowType lane_arrow{LaneArrowType::KLaneBlank};
  /**
   * 生效车道，16bit从左到右表示车道是否生效
   */
  uint16_t lane_flag{0};
  /**
   * 是否仅公交车道生效，16bit从左到右表示是否生效
   */
  uint16_t bus_flag{0};
  /**
   * 退出线号，可空
   */
  std::vector<LinkIDType> out_link_ids;
  /**
   * 车信条件
   */
  std::vector<LaneCond> lane_conds;
};
/**
 * 车道信息
 */
struct LaneInfoMeta : public IGuidanceInfo {
  /**
   * 车道数
   */
  int lane_num{0};
  /**
   * 车道类型，车道从左到右存储
   */
  std::vector<LaneTrafficInfo> lane_infos;
  /**
   * 左附加车道数
   */
  uint8_t lane_num_l{0};
  /**
   * 右附加车道数
   */
  uint8_t lane_num_r{0};
  /**
   * 车道转向拓扑
   */
  std::vector<LaneTopo> lane_topos;

  GuidanceType GetType() const override { return GuidanceType::kLaneMetaInfo; }
};
/**
 * 车信条件表
 */
struct GuidanceLaneCond {
  /**
   * 生效车道，16bit从左到右表示每个车道是否生效
   */
  uint16_t laneinfo_cond{0};
  /**
   * 车道方向
   */
  LinkDirection direction{LinkDirection::kUNKNOWN};
  /**
   * 时间段，可空
   */
  std::string date;
  /**
   * 限制车辆类型，可空
   */
  std::vector<VehicleType> vehicles;
  /**
   * 特殊时间类型，可空
   */
  std::vector<SpecialTimeType> special_times;
};
/**
 * 车道转向拓扑
 */
struct GuidanceLaneTopo {
  /**
   * 高亮
   */
  std::string lane_flag;
  /**
   * 退出线号，可空
   */
  std::vector<LinkIDType> out_link_ids;
  /**
   * 车信条件
   */
  std::vector<GuidanceLaneCond> lane_conds;
};

/**
 * 车道信息(离线)
 */
struct LaneInfo : public IGuidanceInfo {
  /**
   * 车道数
   */
  uint8_t lane_num{0};
  /**
   * 左附加车道数
   */
  uint8_t lane_num_l{0};
  /**
   * 右附加车道数
   */
  uint8_t lane_num_r{0};
  /**
   车道箭头
   */
  std::string arrow_info;
  /**
   * 车道类型
   */
  std::vector<uint32_t> lane_type;
  /**
   * 车道转向拓扑
   */
  std::vector<GuidanceLaneTopo> lane_topos;
  /**
   * 公交专用道
   */
  uint32_t is_bus{0};

  GuidanceType GetType() const override { return GuidanceType::kLaneInfo; }
};

/**
 * 立交关系组
 */
struct ZLevelGroup : public IGuidanceInfo {
  /**
   * 同一个立交关系组的link
   */
  std::vector<ZLinkLevel> links;
  /**
   * 坐标
   */
  Coordinate coordinate{0, 0};

  GuidanceType GetType() const override { return GuidanceType::kZLevelGroup; }
};

/**
 * 放大图
 */
struct BranchPic : public IGuidanceInfo {
  /**
   * 放大图编号
   */
  std::string pic_num;
  /**
   * 箭头图编号
   */
  std::string arrow_num;
  /**
   * 引导方向
   */
  GuideDirection direction{GuideDirection::kNone};
  /**
   * 类型
   */
  BranchPicType pic_type{BranchPicType::kNone};
  /**
   * 引导属性，可空
   */
  GuideAttribute guide_attr{GuideAttribute::kDefault};
  /**
   * 名称标识，可空
   */
  NameKind name_kind{NameKind::kDefault};

  GuidanceType GetType() const override { return GuidanceType::kBranchPic; }
};

/**
 * 长实线
 */
struct LSL : public IGuidanceInfo {
  /**
   * 作用车道序号
   * 从左至右，指该长实线所在的车道序号
   */
  uint8_t seqnum{1};
  /**
   * 长实线类型
   */
  LSLType lsl_type{LSLType::kNone};
  /**
   * 总车道数
   */
  uint8_t lane_nums{0};
  /**
   * 作用车道
   * 如总车道数为4，表示长实线作用于第1/2车道，记录为 1|2
   */
  std::vector<uint8_t> lsl_lanes;

  GuidanceType GetType() const override { return GuidanceType::kLsl; }
};

/**
 * 门限行信息
 */
struct GateCond {
  /**
   * 限行具体时间段
   */
  std::string date;
  /**
   * 限行特殊时间段类型
   */
  std::vector<SpecialTimeType> special_times;
  /**
   * 车辆类型
   */
  std::vector<VehicleType> vehicles;
};

/**
 * 门
 */
struct NodeGate : public IGuidanceInfo {
  /**
   * 退出线号
   */
  LinkIDType out_link_id{0};
  /**
   * 大门类型(车)，可空
   */
  PassageType passage_car{PassageType::kNone};
  /**
   * 大门类型(行人)，可空
   */
  PassageType passage_walk{PassageType::kNone};
  /**
   * 收费类型，可空
   */
  FeeType fee{FeeType::kNone};
  /**
   * 授权类型，可空
   */
  AllowType allow{AllowType::kNone};
  /**
   * 大门限制条件，可空
   */
  GateCond cond;

  GuidanceType GetType() const override { return GuidanceType::kGate; }
};

/**
 * 转向模型
 */
struct TurnModel : public IGuidanceInfo {
  /**
   * 是否是node模型
   */
  bool is_node_model{false};
  /*
   * 模型的值
   */
  uint8_t intersection{0};
  /**
   * 辅助播报
   */
  uint8_t accessorial_info{0};
  /**
   * 播报类型
   */
  uint8_t voice{0};
  /**
   * 主辅路标记
   */
  uint8_t ss_type{0};
  /**
   * tip类型
   */
  uint8_t tip_section{0};
  /**
   * 环岛出边角度
   */
  uint16_t roundabout_angle{0};
  /**
   * 环岛类型(0非环岛，1一般环岛, 2转向环岛)
   */
  uint8_t roundabout_rtype{0};
  /**
   * 环岛Link类型(1:pass, 2:pass exit 3:exit)
   */
  uint8_t roundabout_type{0};

  GuidanceType GetType() const override { return GuidanceType::kTurnModle; }
};

typedef std::shared_ptr<IGuidanceInfo> IConditionPtr;
typedef std::shared_ptr<const IGuidanceInfo> IConditionConstPtr;

typedef std::shared_ptr<IGuidanceSlope> ISlopePtr;
typedef std::shared_ptr<const IGuidanceSlope> ISlopeConstPtr;

}  // namespace api
}  // namespace nerd

namespace std {
/**
 * GCC5.4等低版本使用枚举值为unordered_map的key时，需要对应的哈希函数
 * 对作为key的枚举类型进行std::hash特化
 */
template <>
struct hash<nerd::api::GuidanceType> {
  size_t operator()(const nerd::api::GuidanceType &type) const noexcept {
    return static_cast<std::size_t>(type);
  }
};

}  // namespace std
