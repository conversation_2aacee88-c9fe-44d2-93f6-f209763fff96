//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON>(张仁昌) on 2022/11/28.
//

#pragma once

#include "nerd_export.h"
#include "types.h"

namespace nerd {
namespace api {

/**
 * Debug信息对象
 */
class NERD_EXPORT DebugItem {
 public:
  std::string type;

  /** 提示信息 */
  std::string info;

  /**
   * 位置: [x, y, z]
   * using NDS coordinates
   */
  Coordinate coordinate{0, 0};
};

}  // namespace api
}  // namespace nerd
