// Copyright 2024 Tencent Inc. All rights reserved.
//
// Generated by FindMBS.cmake::mbs_generate_version_flags, do not edit.
//
// Informations for version
//
#pragma once

// Module Name
#define NERD_API_MODULE_NAME "nerdapi"
// Version
#define NERD_API_VERSION "1.feature.FutureAutoMap.totem.ios.dev"

//
// Informations for git repository
//

// Branch name
#define NERD_API_BRANCH_NAME "tag/Engine/v4.22.24"
// Last commit hash
#define NERD_API_COMMIT_HASH "0417939bb4fd6fabd1b4170d653d6f25d8c80dc2"
// Last commit datetime
#define NERD_API_COMMIT_DATETIME "2025-05-26 11:58:40"
// Genarate this file's datetime
#define NERD_API_BUILD_DATETIME "2025-06-05 00:54:58"
// Uncommit changes
#define NERD_API_HAS_LOCAL_CHANGE 3
// Pangu version, empty if current build process is not in the pangu shell
#define NERD_API_PANGU_VERSION ""
// Pangu brand, for example:"wecar", "sosomap", "opensdk", or ""
#define NERD_API_PANGU_BRAND "sosomap"


