// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by wallst on 2021/5/17.
//

#pragma once

#include "dto/types.h"
#include "nerd_export.h"

namespace nerd {
namespace api {

/**
  * @brief NotifyLocation参数
  *  点信息，矩形宽度，DetailConfig
  *  * rect:
  *  -------
  * |       |
  * |  pos  |
  * |       |
  * |-------|
  *   rect_length
  */
struct NERD_EXPORT NotifyLocationParam {
  /**
   * 当前位置，坐标系为gcj02
   */
  nerd::api::GeoCoordinate pos;
  /**
   * 当前位置为中心的正方形边长,单位: 米
   */
  int32_t rect_length;
  /**
   * 数据等级， 0 -- 基础数据 1 -- 铁路数据
   */
  DataDetailConfig detail_config{kRouteDetailLevel_Base};
  /**
   * 设置 DataFeatureSet,用于设置获取各种数据的时候的层级(如果不填，内部只请求必选图层)
   * 背景说明: 用于业务段根据自己的业务场景定制加载数据;
   * kRoute 可选数据:
   *   kRouteLinkAttribute,       Link语义信息, 子类型具体参考类型定义
   *   kRouteLinkExperienceSpeed, Link经验速度, 无子类型, 填充0
   *   kRouteLinkCoverageStatus,  Link覆盖标记, 无子类型, 填充0
   *   kRouteLinkHdAir，          HdAir数据，无子类型, 填充0
   */
  std::map<DataFeatureType, DataFeatureSet> feature_set_map;
};

/**
 * @brief 发布者接口
 */
class NERD_EXPORT IObserver {
 public:
  /**
   * 业务方通知更新位置, 预加载该坐标附近的对应数据
   * @param param 预加载位置信息
   */
  virtual void NotifyLocation(const NotifyLocationParam &param) = 0;

  virtual ~IObserver() = default;
};

}  // namespace api
}  // namespace nerd
