// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_UNIMAPCLOSEROAD_UNIMAPCLOSEROAD_H_
#define FLATBUFFERS_GENERATED_UNIMAPCLOSEROAD_UNIMAPCLOSEROAD_H_

#include "flatbuffers/flatbuffers.h"

namespace UnimapCloseRoad {

struct Point;

struct BreakPoint;

struct Line;
struct LineBuilder;
struct LineT;

struct RoadGeo;
struct RoadGeoBuilder;
struct RoadGeoT;

struct RelativePoint;

struct RelativeLine;
struct RelativeLineBuilder;
struct RelativeLineT;

struct RelativeRoadGeo;
struct RelativeRoadGeoBuilder;
struct RelativeRoadGeoT;

struct EventInfo;
struct EventInfoBuilder;
struct EventInfoT;

struct RoadLayer;
struct RoadLayerBuilder;
struct RoadLayerT;

struct Marker;
struct MarkerBuilder;
struct MarkerT;

struct MarkerLayer;
struct MarkerLayerBuilder;
struct MarkerLayerT;

struct TileData;
struct TileDataBuilder;
struct TileDataT;

struct TileDescriptor;
struct TileDescriptorBuilder;
struct TileDescriptorT;

struct CRTileResponse;
struct CRTileResponseBuilder;
struct CRTileResponseT;

struct CRDetailResponse;
struct CRDetailResponseBuilder;
struct CRDetailResponseT;

enum TileDescStatus : int8_t {
  TileDescStatus_TDSNormalUpdate = 0,
  TileDescStatus_TDSNoNeedUpdate = 1,
  TileDescStatus_TDSNotExistData = 2,
  TileDescStatus_TDSException = 3,
  TileDescStatus_MIN = TileDescStatus_TDSNormalUpdate,
  TileDescStatus_MAX = TileDescStatus_TDSException
};

inline const TileDescStatus (&EnumValuesTileDescStatus())[4] {
  static const TileDescStatus values[] = {
    TileDescStatus_TDSNormalUpdate,
    TileDescStatus_TDSNoNeedUpdate,
    TileDescStatus_TDSNotExistData,
    TileDescStatus_TDSException
  };
  return values;
}

inline const char * const *EnumNamesTileDescStatus() {
  static const char * const names[5] = {
    "TDSNormalUpdate",
    "TDSNoNeedUpdate",
    "TDSNotExistData",
    "TDSException",
    nullptr
  };
  return names;
}

inline const char *EnumNameTileDescStatus(TileDescStatus e) {
  if (flatbuffers::IsOutRange(e, TileDescStatus_TDSNormalUpdate, TileDescStatus_TDSException)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesTileDescStatus()[index];
}

enum RoadSingleFlag : int8_t {
  RoadSingleFlag_RSFTwoWay = 0,
  RoadSingleFlag_RSFStartEnd = 1,
  RoadSingleFlag_RSFEndStart = 2,
  RoadSingleFlag_MIN = RoadSingleFlag_RSFTwoWay,
  RoadSingleFlag_MAX = RoadSingleFlag_RSFEndStart
};

inline const RoadSingleFlag (&EnumValuesRoadSingleFlag())[3] {
  static const RoadSingleFlag values[] = {
    RoadSingleFlag_RSFTwoWay,
    RoadSingleFlag_RSFStartEnd,
    RoadSingleFlag_RSFEndStart
  };
  return values;
}

inline const char * const *EnumNamesRoadSingleFlag() {
  static const char * const names[4] = {
    "RSFTwoWay",
    "RSFStartEnd",
    "RSFEndStart",
    nullptr
  };
  return names;
}

inline const char *EnumNameRoadSingleFlag(RoadSingleFlag e) {
  if (flatbuffers::IsOutRange(e, RoadSingleFlag_RSFTwoWay, RoadSingleFlag_RSFEndStart)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesRoadSingleFlag()[index];
}

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(4) Point FLATBUFFERS_FINAL_CLASS {
 private:
  int32_t x_;
  int32_t y_;
  int16_t z_;
  int16_t padding0__;

 public:
  Point()
      : x_(0),
        y_(0),
        z_(0),
        padding0__(0) {
    (void)padding0__;
  }
  Point(int32_t _x, int32_t _y, int16_t _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)),
        padding0__(0) {
    (void)padding0__;
  }
  int32_t x() const {
    return flatbuffers::EndianScalar(x_);
  }
  int32_t y() const {
    return flatbuffers::EndianScalar(y_);
  }
  int16_t z() const {
    return flatbuffers::EndianScalar(z_);
  }
};
FLATBUFFERS_STRUCT_END(Point, 12);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(4) BreakPoint FLATBUFFERS_FINAL_CLASS {
 private:
  uint16_t coordinate_index_;
  int16_t padding0__;
  float ratio_;
  float relative_height_;

 public:
  BreakPoint()
      : coordinate_index_(0),
        padding0__(0),
        ratio_(0),
        relative_height_(0) {
    (void)padding0__;
  }
  BreakPoint(uint16_t _coordinate_index, float _ratio, float _relative_height)
      : coordinate_index_(flatbuffers::EndianScalar(_coordinate_index)),
        padding0__(0),
        ratio_(flatbuffers::EndianScalar(_ratio)),
        relative_height_(flatbuffers::EndianScalar(_relative_height)) {
    (void)padding0__;
  }
  uint16_t coordinate_index() const {
    return flatbuffers::EndianScalar(coordinate_index_);
  }
  float ratio() const {
    return flatbuffers::EndianScalar(ratio_);
  }
  float relative_height() const {
    return flatbuffers::EndianScalar(relative_height_);
  }
};
FLATBUFFERS_STRUCT_END(BreakPoint, 12);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(2) RelativePoint FLATBUFFERS_FINAL_CLASS {
 private:
  uint16_t x_;
  uint16_t y_;

 public:
  RelativePoint()
      : x_(0),
        y_(0) {
  }
  RelativePoint(uint16_t _x, uint16_t _y)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)) {
  }
  uint16_t x() const {
    return flatbuffers::EndianScalar(x_);
  }
  uint16_t y() const {
    return flatbuffers::EndianScalar(y_);
  }
};
FLATBUFFERS_STRUCT_END(RelativePoint, 4);

struct LineT : public flatbuffers::NativeTable {
  typedef Line TableType;
  std::vector<UnimapCloseRoad::Point> points{};
  std::vector<UnimapCloseRoad::BreakPoint> break_points{};
};

struct Line FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef LineT NativeTableType;
  typedef LineBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POINTS = 4,
    VT_BREAK_POINTS = 6
  };
  const flatbuffers::Vector<const UnimapCloseRoad::Point *> *points() const {
    return GetPointer<const flatbuffers::Vector<const UnimapCloseRoad::Point *> *>(VT_POINTS);
  }
  const flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *> *break_points() const {
    return GetPointer<const flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *> *>(VT_BREAK_POINTS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           VerifyOffset(verifier, VT_BREAK_POINTS) &&
           verifier.VerifyVector(break_points()) &&
           verifier.EndTable();
  }
  LineT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(LineT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Line> Pack(flatbuffers::FlatBufferBuilder &_fbb, const LineT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct LineBuilder {
  typedef Line Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_points(flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::Point *>> points) {
    fbb_.AddOffset(Line::VT_POINTS, points);
  }
  void add_break_points(flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *>> break_points) {
    fbb_.AddOffset(Line::VT_BREAK_POINTS, break_points);
  }
  explicit LineBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<Line> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Line>(end);
    return o;
  }
};

inline flatbuffers::Offset<Line> CreateLine(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::Point *>> points = 0,
    flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *>> break_points = 0) {
  LineBuilder builder_(_fbb);
  builder_.add_break_points(break_points);
  builder_.add_points(points);
  return builder_.Finish();
}

inline flatbuffers::Offset<Line> CreateLineDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<UnimapCloseRoad::Point> *points = nullptr,
    const std::vector<UnimapCloseRoad::BreakPoint> *break_points = nullptr) {
  auto points__ = points ? _fbb.CreateVectorOfStructs<UnimapCloseRoad::Point>(*points) : 0;
  auto break_points__ = break_points ? _fbb.CreateVectorOfStructs<UnimapCloseRoad::BreakPoint>(*break_points) : 0;
  return UnimapCloseRoad::CreateLine(
      _fbb,
      points__,
      break_points__);
}

flatbuffers::Offset<Line> CreateLine(flatbuffers::FlatBufferBuilder &_fbb, const LineT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RoadGeoT : public flatbuffers::NativeTable {
  typedef RoadGeo TableType;
  uint64_t id = 0;
  std::vector<std::unique_ptr<UnimapCloseRoad::LineT>> lines{};
};

struct RoadGeo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RoadGeoT NativeTableType;
  typedef RoadGeoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_LINES = 6
  };
  uint64_t id() const {
    return GetField<uint64_t>(VT_ID, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Line>> *lines() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Line>> *>(VT_LINES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_ID) &&
           VerifyOffset(verifier, VT_LINES) &&
           verifier.VerifyVector(lines()) &&
           verifier.VerifyVectorOfTables(lines()) &&
           verifier.EndTable();
  }
  RoadGeoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RoadGeoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RoadGeo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadGeoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RoadGeoBuilder {
  typedef RoadGeo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_id(uint64_t id) {
    fbb_.AddElement<uint64_t>(RoadGeo::VT_ID, id, 0);
  }
  void add_lines(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Line>>> lines) {
    fbb_.AddOffset(RoadGeo::VT_LINES, lines);
  }
  explicit RoadGeoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RoadGeo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RoadGeo>(end);
    return o;
  }
};

inline flatbuffers::Offset<RoadGeo> CreateRoadGeo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t id = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Line>>> lines = 0) {
  RoadGeoBuilder builder_(_fbb);
  builder_.add_id(id);
  builder_.add_lines(lines);
  return builder_.Finish();
}

inline flatbuffers::Offset<RoadGeo> CreateRoadGeoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t id = 0,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::Line>> *lines = nullptr) {
  auto lines__ = lines ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::Line>>(*lines) : 0;
  return UnimapCloseRoad::CreateRoadGeo(
      _fbb,
      id,
      lines__);
}

flatbuffers::Offset<RoadGeo> CreateRoadGeo(flatbuffers::FlatBufferBuilder &_fbb, const RoadGeoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RelativeLineT : public flatbuffers::NativeTable {
  typedef RelativeLine TableType;
  std::vector<UnimapCloseRoad::RelativePoint> points{};
  std::vector<int16_t> relative_z{};
  std::vector<UnimapCloseRoad::BreakPoint> break_points{};
};

struct RelativeLine FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RelativeLineT NativeTableType;
  typedef RelativeLineBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POINTS = 4,
    VT_RELATIVE_Z = 6,
    VT_BREAK_POINTS = 8
  };
  const flatbuffers::Vector<const UnimapCloseRoad::RelativePoint *> *points() const {
    return GetPointer<const flatbuffers::Vector<const UnimapCloseRoad::RelativePoint *> *>(VT_POINTS);
  }
  const flatbuffers::Vector<int16_t> *relative_z() const {
    return GetPointer<const flatbuffers::Vector<int16_t> *>(VT_RELATIVE_Z);
  }
  const flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *> *break_points() const {
    return GetPointer<const flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *> *>(VT_BREAK_POINTS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           VerifyOffset(verifier, VT_RELATIVE_Z) &&
           verifier.VerifyVector(relative_z()) &&
           VerifyOffset(verifier, VT_BREAK_POINTS) &&
           verifier.VerifyVector(break_points()) &&
           verifier.EndTable();
  }
  RelativeLineT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RelativeLineT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RelativeLine> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RelativeLineT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RelativeLineBuilder {
  typedef RelativeLine Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_points(flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::RelativePoint *>> points) {
    fbb_.AddOffset(RelativeLine::VT_POINTS, points);
  }
  void add_relative_z(flatbuffers::Offset<flatbuffers::Vector<int16_t>> relative_z) {
    fbb_.AddOffset(RelativeLine::VT_RELATIVE_Z, relative_z);
  }
  void add_break_points(flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *>> break_points) {
    fbb_.AddOffset(RelativeLine::VT_BREAK_POINTS, break_points);
  }
  explicit RelativeLineBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RelativeLine> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RelativeLine>(end);
    return o;
  }
};

inline flatbuffers::Offset<RelativeLine> CreateRelativeLine(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::RelativePoint *>> points = 0,
    flatbuffers::Offset<flatbuffers::Vector<int16_t>> relative_z = 0,
    flatbuffers::Offset<flatbuffers::Vector<const UnimapCloseRoad::BreakPoint *>> break_points = 0) {
  RelativeLineBuilder builder_(_fbb);
  builder_.add_break_points(break_points);
  builder_.add_relative_z(relative_z);
  builder_.add_points(points);
  return builder_.Finish();
}

inline flatbuffers::Offset<RelativeLine> CreateRelativeLineDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<UnimapCloseRoad::RelativePoint> *points = nullptr,
    const std::vector<int16_t> *relative_z = nullptr,
    const std::vector<UnimapCloseRoad::BreakPoint> *break_points = nullptr) {
  auto points__ = points ? _fbb.CreateVectorOfStructs<UnimapCloseRoad::RelativePoint>(*points) : 0;
  auto relative_z__ = relative_z ? _fbb.CreateVector<int16_t>(*relative_z) : 0;
  auto break_points__ = break_points ? _fbb.CreateVectorOfStructs<UnimapCloseRoad::BreakPoint>(*break_points) : 0;
  return UnimapCloseRoad::CreateRelativeLine(
      _fbb,
      points__,
      relative_z__,
      break_points__);
}

flatbuffers::Offset<RelativeLine> CreateRelativeLine(flatbuffers::FlatBufferBuilder &_fbb, const RelativeLineT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RelativeRoadGeoT : public flatbuffers::NativeTable {
  typedef RelativeRoadGeo TableType;
  uint64_t id = 0;
  std::vector<std::unique_ptr<UnimapCloseRoad::RelativeLineT>> relative_lines{};
};

struct RelativeRoadGeo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RelativeRoadGeoT NativeTableType;
  typedef RelativeRoadGeoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_RELATIVE_LINES = 6
  };
  uint64_t id() const {
    return GetField<uint64_t>(VT_ID, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>> *relative_lines() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>> *>(VT_RELATIVE_LINES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_ID) &&
           VerifyOffset(verifier, VT_RELATIVE_LINES) &&
           verifier.VerifyVector(relative_lines()) &&
           verifier.VerifyVectorOfTables(relative_lines()) &&
           verifier.EndTable();
  }
  RelativeRoadGeoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RelativeRoadGeoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RelativeRoadGeo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RelativeRoadGeoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RelativeRoadGeoBuilder {
  typedef RelativeRoadGeo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_id(uint64_t id) {
    fbb_.AddElement<uint64_t>(RelativeRoadGeo::VT_ID, id, 0);
  }
  void add_relative_lines(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>>> relative_lines) {
    fbb_.AddOffset(RelativeRoadGeo::VT_RELATIVE_LINES, relative_lines);
  }
  explicit RelativeRoadGeoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RelativeRoadGeo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RelativeRoadGeo>(end);
    return o;
  }
};

inline flatbuffers::Offset<RelativeRoadGeo> CreateRelativeRoadGeo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t id = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>>> relative_lines = 0) {
  RelativeRoadGeoBuilder builder_(_fbb);
  builder_.add_id(id);
  builder_.add_relative_lines(relative_lines);
  return builder_.Finish();
}

inline flatbuffers::Offset<RelativeRoadGeo> CreateRelativeRoadGeoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t id = 0,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>> *relative_lines = nullptr) {
  auto relative_lines__ = relative_lines ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>>(*relative_lines) : 0;
  return UnimapCloseRoad::CreateRelativeRoadGeo(
      _fbb,
      id,
      relative_lines__);
}

flatbuffers::Offset<RelativeRoadGeo> CreateRelativeRoadGeo(flatbuffers::FlatBufferBuilder &_fbb, const RelativeRoadGeoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct EventInfoT : public flatbuffers::NativeTable {
  typedef EventInfo TableType;
  uint32_t id = 0;
  uint32_t start_time = 0;
  uint32_t end_time = 0;
  uint32_t class_code = 0;
  uint32_t priority = 0;
};

struct EventInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef EventInfoT NativeTableType;
  typedef EventInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_START_TIME = 6,
    VT_END_TIME = 8,
    VT_CLASS_CODE = 10,
    VT_PRIORITY = 12
  };
  uint32_t id() const {
    return GetField<uint32_t>(VT_ID, 0);
  }
  uint32_t start_time() const {
    return GetField<uint32_t>(VT_START_TIME, 0);
  }
  uint32_t end_time() const {
    return GetField<uint32_t>(VT_END_TIME, 0);
  }
  uint32_t class_code() const {
    return GetField<uint32_t>(VT_CLASS_CODE, 0);
  }
  uint32_t priority() const {
    return GetField<uint32_t>(VT_PRIORITY, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_ID) &&
           VerifyField<uint32_t>(verifier, VT_START_TIME) &&
           VerifyField<uint32_t>(verifier, VT_END_TIME) &&
           VerifyField<uint32_t>(verifier, VT_CLASS_CODE) &&
           VerifyField<uint32_t>(verifier, VT_PRIORITY) &&
           verifier.EndTable();
  }
  EventInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(EventInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<EventInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const EventInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct EventInfoBuilder {
  typedef EventInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_id(uint32_t id) {
    fbb_.AddElement<uint32_t>(EventInfo::VT_ID, id, 0);
  }
  void add_start_time(uint32_t start_time) {
    fbb_.AddElement<uint32_t>(EventInfo::VT_START_TIME, start_time, 0);
  }
  void add_end_time(uint32_t end_time) {
    fbb_.AddElement<uint32_t>(EventInfo::VT_END_TIME, end_time, 0);
  }
  void add_class_code(uint32_t class_code) {
    fbb_.AddElement<uint32_t>(EventInfo::VT_CLASS_CODE, class_code, 0);
  }
  void add_priority(uint32_t priority) {
    fbb_.AddElement<uint32_t>(EventInfo::VT_PRIORITY, priority, 0);
  }
  explicit EventInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<EventInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<EventInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<EventInfo> CreateEventInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t id = 0,
    uint32_t start_time = 0,
    uint32_t end_time = 0,
    uint32_t class_code = 0,
    uint32_t priority = 0) {
  EventInfoBuilder builder_(_fbb);
  builder_.add_priority(priority);
  builder_.add_class_code(class_code);
  builder_.add_end_time(end_time);
  builder_.add_start_time(start_time);
  builder_.add_id(id);
  return builder_.Finish();
}

flatbuffers::Offset<EventInfo> CreateEventInfo(flatbuffers::FlatBufferBuilder &_fbb, const EventInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RoadLayerT : public flatbuffers::NativeTable {
  typedef RoadLayer TableType;
  int8_t type = 0;
  uint16_t style_id = 0;
  std::vector<int8_t> scale_mask{};
  uint16_t event_idx = 0;
  uint32_t priority = 0;
  uint16_t second_priority = 0;
  std::vector<uint16_t> road_geo_idx{};
  std::vector<UnimapCloseRoad::RoadSingleFlag> single_flag{};
  int8_t feature_type = 0;
};

struct RoadLayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RoadLayerT NativeTableType;
  typedef RoadLayerBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE = 4,
    VT_STYLE_ID = 6,
    VT_SCALE_MASK = 8,
    VT_EVENT_IDX = 10,
    VT_PRIORITY = 12,
    VT_SECOND_PRIORITY = 14,
    VT_ROAD_GEO_IDX = 16,
    VT_SINGLE_FLAG = 18,
    VT_FEATURE_TYPE = 20
  };
  int8_t type() const {
    return GetField<int8_t>(VT_TYPE, 0);
  }
  uint16_t style_id() const {
    return GetField<uint16_t>(VT_STYLE_ID, 0);
  }
  const flatbuffers::Vector<int8_t> *scale_mask() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_SCALE_MASK);
  }
  uint16_t event_idx() const {
    return GetField<uint16_t>(VT_EVENT_IDX, 0);
  }
  uint32_t priority() const {
    return GetField<uint32_t>(VT_PRIORITY, 0);
  }
  uint16_t second_priority() const {
    return GetField<uint16_t>(VT_SECOND_PRIORITY, 0);
  }
  const flatbuffers::Vector<uint16_t> *road_geo_idx() const {
    return GetPointer<const flatbuffers::Vector<uint16_t> *>(VT_ROAD_GEO_IDX);
  }
  const flatbuffers::Vector<int8_t> *single_flag() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_SINGLE_FLAG);
  }
  int8_t feature_type() const {
    return GetField<int8_t>(VT_FEATURE_TYPE, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_TYPE) &&
           VerifyField<uint16_t>(verifier, VT_STYLE_ID) &&
           VerifyOffset(verifier, VT_SCALE_MASK) &&
           verifier.VerifyVector(scale_mask()) &&
           VerifyField<uint16_t>(verifier, VT_EVENT_IDX) &&
           VerifyField<uint32_t>(verifier, VT_PRIORITY) &&
           VerifyField<uint16_t>(verifier, VT_SECOND_PRIORITY) &&
           VerifyOffset(verifier, VT_ROAD_GEO_IDX) &&
           verifier.VerifyVector(road_geo_idx()) &&
           VerifyOffset(verifier, VT_SINGLE_FLAG) &&
           verifier.VerifyVector(single_flag()) &&
           VerifyField<int8_t>(verifier, VT_FEATURE_TYPE) &&
           verifier.EndTable();
  }
  RoadLayerT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RoadLayerT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RoadLayer> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadLayerT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RoadLayerBuilder {
  typedef RoadLayer Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_type(int8_t type) {
    fbb_.AddElement<int8_t>(RoadLayer::VT_TYPE, type, 0);
  }
  void add_style_id(uint16_t style_id) {
    fbb_.AddElement<uint16_t>(RoadLayer::VT_STYLE_ID, style_id, 0);
  }
  void add_scale_mask(flatbuffers::Offset<flatbuffers::Vector<int8_t>> scale_mask) {
    fbb_.AddOffset(RoadLayer::VT_SCALE_MASK, scale_mask);
  }
  void add_event_idx(uint16_t event_idx) {
    fbb_.AddElement<uint16_t>(RoadLayer::VT_EVENT_IDX, event_idx, 0);
  }
  void add_priority(uint32_t priority) {
    fbb_.AddElement<uint32_t>(RoadLayer::VT_PRIORITY, priority, 0);
  }
  void add_second_priority(uint16_t second_priority) {
    fbb_.AddElement<uint16_t>(RoadLayer::VT_SECOND_PRIORITY, second_priority, 0);
  }
  void add_road_geo_idx(flatbuffers::Offset<flatbuffers::Vector<uint16_t>> road_geo_idx) {
    fbb_.AddOffset(RoadLayer::VT_ROAD_GEO_IDX, road_geo_idx);
  }
  void add_single_flag(flatbuffers::Offset<flatbuffers::Vector<int8_t>> single_flag) {
    fbb_.AddOffset(RoadLayer::VT_SINGLE_FLAG, single_flag);
  }
  void add_feature_type(int8_t feature_type) {
    fbb_.AddElement<int8_t>(RoadLayer::VT_FEATURE_TYPE, feature_type, 0);
  }
  explicit RoadLayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RoadLayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RoadLayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<RoadLayer> CreateRoadLayer(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t type = 0,
    uint16_t style_id = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> scale_mask = 0,
    uint16_t event_idx = 0,
    uint32_t priority = 0,
    uint16_t second_priority = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint16_t>> road_geo_idx = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> single_flag = 0,
    int8_t feature_type = 0) {
  RoadLayerBuilder builder_(_fbb);
  builder_.add_single_flag(single_flag);
  builder_.add_road_geo_idx(road_geo_idx);
  builder_.add_priority(priority);
  builder_.add_scale_mask(scale_mask);
  builder_.add_second_priority(second_priority);
  builder_.add_event_idx(event_idx);
  builder_.add_style_id(style_id);
  builder_.add_feature_type(feature_type);
  builder_.add_type(type);
  return builder_.Finish();
}

inline flatbuffers::Offset<RoadLayer> CreateRoadLayerDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t type = 0,
    uint16_t style_id = 0,
    const std::vector<int8_t> *scale_mask = nullptr,
    uint16_t event_idx = 0,
    uint32_t priority = 0,
    uint16_t second_priority = 0,
    const std::vector<uint16_t> *road_geo_idx = nullptr,
    const std::vector<int8_t> *single_flag = nullptr,
    int8_t feature_type = 0) {
  auto scale_mask__ = scale_mask ? _fbb.CreateVector<int8_t>(*scale_mask) : 0;
  auto road_geo_idx__ = road_geo_idx ? _fbb.CreateVector<uint16_t>(*road_geo_idx) : 0;
  auto single_flag__ = single_flag ? _fbb.CreateVector<int8_t>(*single_flag) : 0;
  return UnimapCloseRoad::CreateRoadLayer(
      _fbb,
      type,
      style_id,
      scale_mask__,
      event_idx,
      priority,
      second_priority,
      road_geo_idx__,
      single_flag__,
      feature_type);
}

flatbuffers::Offset<RoadLayer> CreateRoadLayer(flatbuffers::FlatBufferBuilder &_fbb, const RoadLayerT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct MarkerT : public flatbuffers::NativeTable {
  typedef Marker TableType;
  uint16_t event_idx = 0;
  uint16_t style_id = 0;
  uint16_t road_style_id = 0;
  uint32_t priority = 0;
  std::unique_ptr<UnimapCloseRoad::Point> point{};
  int8_t feature_type = 0;
};

struct Marker FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef MarkerT NativeTableType;
  typedef MarkerBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EVENT_IDX = 4,
    VT_STYLE_ID = 6,
    VT_ROAD_STYLE_ID = 8,
    VT_PRIORITY = 10,
    VT_POINT = 12,
    VT_FEATURE_TYPE = 14
  };
  uint16_t event_idx() const {
    return GetField<uint16_t>(VT_EVENT_IDX, 0);
  }
  uint16_t style_id() const {
    return GetField<uint16_t>(VT_STYLE_ID, 0);
  }
  uint16_t road_style_id() const {
    return GetField<uint16_t>(VT_ROAD_STYLE_ID, 0);
  }
  uint32_t priority() const {
    return GetField<uint32_t>(VT_PRIORITY, 0);
  }
  const UnimapCloseRoad::Point *point() const {
    return GetStruct<const UnimapCloseRoad::Point *>(VT_POINT);
  }
  int8_t feature_type() const {
    return GetField<int8_t>(VT_FEATURE_TYPE, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_EVENT_IDX) &&
           VerifyField<uint16_t>(verifier, VT_STYLE_ID) &&
           VerifyField<uint16_t>(verifier, VT_ROAD_STYLE_ID) &&
           VerifyField<uint32_t>(verifier, VT_PRIORITY) &&
           VerifyField<UnimapCloseRoad::Point>(verifier, VT_POINT) &&
           VerifyField<int8_t>(verifier, VT_FEATURE_TYPE) &&
           verifier.EndTable();
  }
  MarkerT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(MarkerT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<Marker> Pack(flatbuffers::FlatBufferBuilder &_fbb, const MarkerT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct MarkerBuilder {
  typedef Marker Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_event_idx(uint16_t event_idx) {
    fbb_.AddElement<uint16_t>(Marker::VT_EVENT_IDX, event_idx, 0);
  }
  void add_style_id(uint16_t style_id) {
    fbb_.AddElement<uint16_t>(Marker::VT_STYLE_ID, style_id, 0);
  }
  void add_road_style_id(uint16_t road_style_id) {
    fbb_.AddElement<uint16_t>(Marker::VT_ROAD_STYLE_ID, road_style_id, 0);
  }
  void add_priority(uint32_t priority) {
    fbb_.AddElement<uint32_t>(Marker::VT_PRIORITY, priority, 0);
  }
  void add_point(const UnimapCloseRoad::Point *point) {
    fbb_.AddStruct(Marker::VT_POINT, point);
  }
  void add_feature_type(int8_t feature_type) {
    fbb_.AddElement<int8_t>(Marker::VT_FEATURE_TYPE, feature_type, 0);
  }
  explicit MarkerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<Marker> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<Marker>(end);
    return o;
  }
};

inline flatbuffers::Offset<Marker> CreateMarker(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t event_idx = 0,
    uint16_t style_id = 0,
    uint16_t road_style_id = 0,
    uint32_t priority = 0,
    const UnimapCloseRoad::Point *point = 0,
    int8_t feature_type = 0) {
  MarkerBuilder builder_(_fbb);
  builder_.add_point(point);
  builder_.add_priority(priority);
  builder_.add_road_style_id(road_style_id);
  builder_.add_style_id(style_id);
  builder_.add_event_idx(event_idx);
  builder_.add_feature_type(feature_type);
  return builder_.Finish();
}

flatbuffers::Offset<Marker> CreateMarker(flatbuffers::FlatBufferBuilder &_fbb, const MarkerT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct MarkerLayerT : public flatbuffers::NativeTable {
  typedef MarkerLayer TableType;
  std::vector<int8_t> scale_mask{};
  std::vector<std::unique_ptr<UnimapCloseRoad::MarkerT>> markers{};
};

struct MarkerLayer FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef MarkerLayerT NativeTableType;
  typedef MarkerLayerBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SCALE_MASK = 4,
    VT_MARKERS = 6
  };
  const flatbuffers::Vector<int8_t> *scale_mask() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_SCALE_MASK);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Marker>> *markers() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Marker>> *>(VT_MARKERS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SCALE_MASK) &&
           verifier.VerifyVector(scale_mask()) &&
           VerifyOffset(verifier, VT_MARKERS) &&
           verifier.VerifyVector(markers()) &&
           verifier.VerifyVectorOfTables(markers()) &&
           verifier.EndTable();
  }
  MarkerLayerT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(MarkerLayerT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<MarkerLayer> Pack(flatbuffers::FlatBufferBuilder &_fbb, const MarkerLayerT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct MarkerLayerBuilder {
  typedef MarkerLayer Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_scale_mask(flatbuffers::Offset<flatbuffers::Vector<int8_t>> scale_mask) {
    fbb_.AddOffset(MarkerLayer::VT_SCALE_MASK, scale_mask);
  }
  void add_markers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Marker>>> markers) {
    fbb_.AddOffset(MarkerLayer::VT_MARKERS, markers);
  }
  explicit MarkerLayerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<MarkerLayer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<MarkerLayer>(end);
    return o;
  }
};

inline flatbuffers::Offset<MarkerLayer> CreateMarkerLayer(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> scale_mask = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::Marker>>> markers = 0) {
  MarkerLayerBuilder builder_(_fbb);
  builder_.add_markers(markers);
  builder_.add_scale_mask(scale_mask);
  return builder_.Finish();
}

inline flatbuffers::Offset<MarkerLayer> CreateMarkerLayerDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int8_t> *scale_mask = nullptr,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::Marker>> *markers = nullptr) {
  auto scale_mask__ = scale_mask ? _fbb.CreateVector<int8_t>(*scale_mask) : 0;
  auto markers__ = markers ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::Marker>>(*markers) : 0;
  return UnimapCloseRoad::CreateMarkerLayer(
      _fbb,
      scale_mask__,
      markers__);
}

flatbuffers::Offset<MarkerLayer> CreateMarkerLayer(flatbuffers::FlatBufferBuilder &_fbb, const MarkerLayerT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct TileDataT : public flatbuffers::NativeTable {
  typedef TileData TableType;
  int8_t status = 0;
  uint32_t tileid = 0;
  uint32_t time_stamp = 0;
  std::unique_ptr<UnimapCloseRoad::Point> sw_conner{};
  uint16_t unit = 0;
  std::vector<std::unique_ptr<UnimapCloseRoad::EventInfoT>> events{};
  std::vector<std::unique_ptr<UnimapCloseRoad::RoadGeoT>> roads_geo{};
  std::vector<std::unique_ptr<UnimapCloseRoad::RelativeRoadGeoT>> reroads_geo{};
  std::vector<std::unique_ptr<UnimapCloseRoad::RoadLayerT>> road_layers{};
  std::vector<std::unique_ptr<UnimapCloseRoad::MarkerLayerT>> markers{};
};

struct TileData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef TileDataT NativeTableType;
  typedef TileDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STATUS = 4,
    VT_TILEID = 6,
    VT_TIME_STAMP = 8,
    VT_SW_CONNER = 10,
    VT_UNIT = 12,
    VT_EVENTS = 14,
    VT_ROADS_GEO = 16,
    VT_REROADS_GEO = 18,
    VT_ROAD_LAYERS = 20,
    VT_MARKERS = 22
  };
  int8_t status() const {
    return GetField<int8_t>(VT_STATUS, 0);
  }
  uint32_t tileid() const {
    return GetField<uint32_t>(VT_TILEID, 0);
  }
  uint32_t time_stamp() const {
    return GetField<uint32_t>(VT_TIME_STAMP, 0);
  }
  const UnimapCloseRoad::Point *sw_conner() const {
    return GetStruct<const UnimapCloseRoad::Point *>(VT_SW_CONNER);
  }
  uint16_t unit() const {
    return GetField<uint16_t>(VT_UNIT, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>> *events() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>> *>(VT_EVENTS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>> *roads_geo() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>> *>(VT_ROADS_GEO);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>> *reroads_geo() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>> *>(VT_REROADS_GEO);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>> *road_layers() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>> *>(VT_ROAD_LAYERS);
  }
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>> *markers() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>> *>(VT_MARKERS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_STATUS) &&
           VerifyField<uint32_t>(verifier, VT_TILEID) &&
           VerifyField<uint32_t>(verifier, VT_TIME_STAMP) &&
           VerifyField<UnimapCloseRoad::Point>(verifier, VT_SW_CONNER) &&
           VerifyField<uint16_t>(verifier, VT_UNIT) &&
           VerifyOffset(verifier, VT_EVENTS) &&
           verifier.VerifyVector(events()) &&
           verifier.VerifyVectorOfTables(events()) &&
           VerifyOffset(verifier, VT_ROADS_GEO) &&
           verifier.VerifyVector(roads_geo()) &&
           verifier.VerifyVectorOfTables(roads_geo()) &&
           VerifyOffset(verifier, VT_REROADS_GEO) &&
           verifier.VerifyVector(reroads_geo()) &&
           verifier.VerifyVectorOfTables(reroads_geo()) &&
           VerifyOffset(verifier, VT_ROAD_LAYERS) &&
           verifier.VerifyVector(road_layers()) &&
           verifier.VerifyVectorOfTables(road_layers()) &&
           VerifyOffset(verifier, VT_MARKERS) &&
           verifier.VerifyVector(markers()) &&
           verifier.VerifyVectorOfTables(markers()) &&
           verifier.EndTable();
  }
  TileDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(TileDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<TileData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const TileDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct TileDataBuilder {
  typedef TileData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_status(int8_t status) {
    fbb_.AddElement<int8_t>(TileData::VT_STATUS, status, 0);
  }
  void add_tileid(uint32_t tileid) {
    fbb_.AddElement<uint32_t>(TileData::VT_TILEID, tileid, 0);
  }
  void add_time_stamp(uint32_t time_stamp) {
    fbb_.AddElement<uint32_t>(TileData::VT_TIME_STAMP, time_stamp, 0);
  }
  void add_sw_conner(const UnimapCloseRoad::Point *sw_conner) {
    fbb_.AddStruct(TileData::VT_SW_CONNER, sw_conner);
  }
  void add_unit(uint16_t unit) {
    fbb_.AddElement<uint16_t>(TileData::VT_UNIT, unit, 0);
  }
  void add_events(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>>> events) {
    fbb_.AddOffset(TileData::VT_EVENTS, events);
  }
  void add_roads_geo(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>>> roads_geo) {
    fbb_.AddOffset(TileData::VT_ROADS_GEO, roads_geo);
  }
  void add_reroads_geo(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>>> reroads_geo) {
    fbb_.AddOffset(TileData::VT_REROADS_GEO, reroads_geo);
  }
  void add_road_layers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>>> road_layers) {
    fbb_.AddOffset(TileData::VT_ROAD_LAYERS, road_layers);
  }
  void add_markers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>>> markers) {
    fbb_.AddOffset(TileData::VT_MARKERS, markers);
  }
  explicit TileDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<TileData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<TileData>(end);
    return o;
  }
};

inline flatbuffers::Offset<TileData> CreateTileData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t status = 0,
    uint32_t tileid = 0,
    uint32_t time_stamp = 0,
    const UnimapCloseRoad::Point *sw_conner = 0,
    uint16_t unit = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>>> events = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>>> roads_geo = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>>> reroads_geo = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>>> road_layers = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>>> markers = 0) {
  TileDataBuilder builder_(_fbb);
  builder_.add_markers(markers);
  builder_.add_road_layers(road_layers);
  builder_.add_reroads_geo(reroads_geo);
  builder_.add_roads_geo(roads_geo);
  builder_.add_events(events);
  builder_.add_sw_conner(sw_conner);
  builder_.add_time_stamp(time_stamp);
  builder_.add_tileid(tileid);
  builder_.add_unit(unit);
  builder_.add_status(status);
  return builder_.Finish();
}

inline flatbuffers::Offset<TileData> CreateTileDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t status = 0,
    uint32_t tileid = 0,
    uint32_t time_stamp = 0,
    const UnimapCloseRoad::Point *sw_conner = 0,
    uint16_t unit = 0,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>> *events = nullptr,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>> *roads_geo = nullptr,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>> *reroads_geo = nullptr,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>> *road_layers = nullptr,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>> *markers = nullptr) {
  auto events__ = events ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>>(*events) : 0;
  auto roads_geo__ = roads_geo ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>>(*roads_geo) : 0;
  auto reroads_geo__ = reroads_geo ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>>(*reroads_geo) : 0;
  auto road_layers__ = road_layers ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>>(*road_layers) : 0;
  auto markers__ = markers ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>>(*markers) : 0;
  return UnimapCloseRoad::CreateTileData(
      _fbb,
      status,
      tileid,
      time_stamp,
      sw_conner,
      unit,
      events__,
      roads_geo__,
      reroads_geo__,
      road_layers__,
      markers__);
}

flatbuffers::Offset<TileData> CreateTileData(flatbuffers::FlatBufferBuilder &_fbb, const TileDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct TileDescriptorT : public flatbuffers::NativeTable {
  typedef TileDescriptor TableType;
  UnimapCloseRoad::TileDescStatus status = UnimapCloseRoad::TileDescStatus_TDSNormalUpdate;
  int8_t type = 0;
  uint32_t tileid = 0;
  uint32_t version = 0;
  uint32_t interval = 0;
  uint32_t origin_data_size = 0;
  std::vector<int8_t> data{};
};

struct TileDescriptor FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef TileDescriptorT NativeTableType;
  typedef TileDescriptorBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STATUS = 4,
    VT_TYPE = 6,
    VT_TILEID = 8,
    VT_VERSION = 10,
    VT_INTERVAL = 12,
    VT_ORIGIN_DATA_SIZE = 14,
    VT_DATA = 16
  };
  UnimapCloseRoad::TileDescStatus status() const {
    return static_cast<UnimapCloseRoad::TileDescStatus>(GetField<int8_t>(VT_STATUS, 0));
  }
  int8_t type() const {
    return GetField<int8_t>(VT_TYPE, 0);
  }
  uint32_t tileid() const {
    return GetField<uint32_t>(VT_TILEID, 0);
  }
  uint32_t version() const {
    return GetField<uint32_t>(VT_VERSION, 0);
  }
  uint32_t interval() const {
    return GetField<uint32_t>(VT_INTERVAL, 0);
  }
  uint32_t origin_data_size() const {
    return GetField<uint32_t>(VT_ORIGIN_DATA_SIZE, 0);
  }
  const flatbuffers::Vector<int8_t> *data() const {
    return GetPointer<const flatbuffers::Vector<int8_t> *>(VT_DATA);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_STATUS) &&
           VerifyField<int8_t>(verifier, VT_TYPE) &&
           VerifyField<uint32_t>(verifier, VT_TILEID) &&
           VerifyField<uint32_t>(verifier, VT_VERSION) &&
           VerifyField<uint32_t>(verifier, VT_INTERVAL) &&
           VerifyField<uint32_t>(verifier, VT_ORIGIN_DATA_SIZE) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
  TileDescriptorT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(TileDescriptorT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<TileDescriptor> Pack(flatbuffers::FlatBufferBuilder &_fbb, const TileDescriptorT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct TileDescriptorBuilder {
  typedef TileDescriptor Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_status(UnimapCloseRoad::TileDescStatus status) {
    fbb_.AddElement<int8_t>(TileDescriptor::VT_STATUS, static_cast<int8_t>(status), 0);
  }
  void add_type(int8_t type) {
    fbb_.AddElement<int8_t>(TileDescriptor::VT_TYPE, type, 0);
  }
  void add_tileid(uint32_t tileid) {
    fbb_.AddElement<uint32_t>(TileDescriptor::VT_TILEID, tileid, 0);
  }
  void add_version(uint32_t version) {
    fbb_.AddElement<uint32_t>(TileDescriptor::VT_VERSION, version, 0);
  }
  void add_interval(uint32_t interval) {
    fbb_.AddElement<uint32_t>(TileDescriptor::VT_INTERVAL, interval, 0);
  }
  void add_origin_data_size(uint32_t origin_data_size) {
    fbb_.AddElement<uint32_t>(TileDescriptor::VT_ORIGIN_DATA_SIZE, origin_data_size, 0);
  }
  void add_data(flatbuffers::Offset<flatbuffers::Vector<int8_t>> data) {
    fbb_.AddOffset(TileDescriptor::VT_DATA, data);
  }
  explicit TileDescriptorBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<TileDescriptor> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<TileDescriptor>(end);
    return o;
  }
};

inline flatbuffers::Offset<TileDescriptor> CreateTileDescriptor(
    flatbuffers::FlatBufferBuilder &_fbb,
    UnimapCloseRoad::TileDescStatus status = UnimapCloseRoad::TileDescStatus_TDSNormalUpdate,
    int8_t type = 0,
    uint32_t tileid = 0,
    uint32_t version = 0,
    uint32_t interval = 0,
    uint32_t origin_data_size = 0,
    flatbuffers::Offset<flatbuffers::Vector<int8_t>> data = 0) {
  TileDescriptorBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_origin_data_size(origin_data_size);
  builder_.add_interval(interval);
  builder_.add_version(version);
  builder_.add_tileid(tileid);
  builder_.add_type(type);
  builder_.add_status(status);
  return builder_.Finish();
}

inline flatbuffers::Offset<TileDescriptor> CreateTileDescriptorDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    UnimapCloseRoad::TileDescStatus status = UnimapCloseRoad::TileDescStatus_TDSNormalUpdate,
    int8_t type = 0,
    uint32_t tileid = 0,
    uint32_t version = 0,
    uint32_t interval = 0,
    uint32_t origin_data_size = 0,
    const std::vector<int8_t> *data = nullptr) {
  auto data__ = data ? _fbb.CreateVector<int8_t>(*data) : 0;
  return UnimapCloseRoad::CreateTileDescriptor(
      _fbb,
      status,
      type,
      tileid,
      version,
      interval,
      origin_data_size,
      data__);
}

flatbuffers::Offset<TileDescriptor> CreateTileDescriptor(flatbuffers::FlatBufferBuilder &_fbb, const TileDescriptorT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct CRTileResponseT : public flatbuffers::NativeTable {
  typedef CRTileResponse TableType;
  std::vector<std::unique_ptr<UnimapCloseRoad::TileDescriptorT>> tiles{};
};

struct CRTileResponse FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef CRTileResponseT NativeTableType;
  typedef CRTileResponseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TILES = 4
  };
  const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>> *tiles() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>> *>(VT_TILES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TILES) &&
           verifier.VerifyVector(tiles()) &&
           verifier.VerifyVectorOfTables(tiles()) &&
           verifier.EndTable();
  }
  CRTileResponseT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(CRTileResponseT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<CRTileResponse> Pack(flatbuffers::FlatBufferBuilder &_fbb, const CRTileResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CRTileResponseBuilder {
  typedef CRTileResponse Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_tiles(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>>> tiles) {
    fbb_.AddOffset(CRTileResponse::VT_TILES, tiles);
  }
  explicit CRTileResponseBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<CRTileResponse> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<CRTileResponse>(end);
    return o;
  }
};

inline flatbuffers::Offset<CRTileResponse> CreateCRTileResponse(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>>> tiles = 0) {
  CRTileResponseBuilder builder_(_fbb);
  builder_.add_tiles(tiles);
  return builder_.Finish();
}

inline flatbuffers::Offset<CRTileResponse> CreateCRTileResponseDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>> *tiles = nullptr) {
  auto tiles__ = tiles ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>>(*tiles) : 0;
  return UnimapCloseRoad::CreateCRTileResponse(
      _fbb,
      tiles__);
}

flatbuffers::Offset<CRTileResponse> CreateCRTileResponse(flatbuffers::FlatBufferBuilder &_fbb, const CRTileResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct CRDetailResponseT : public flatbuffers::NativeTable {
  typedef CRDetailResponse TableType;
  int8_t status = 0;
  uint64_t eventid = 0;
  std::string title{};
  std::string message{};
  std::string supplier{};
  std::string url{};
  uint64_t start_time = 0;
  uint64_t end_time = 0;
  uint64_t update_time = 0;
  uint32_t good_count = 0;
  uint32_t bad_count = 0;
  std::string imgurl{};
  int32_t display_time = 0;
};

struct CRDetailResponse FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef CRDetailResponseT NativeTableType;
  typedef CRDetailResponseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STATUS = 4,
    VT_EVENTID = 6,
    VT_TITLE = 8,
    VT_MESSAGE = 10,
    VT_SUPPLIER = 12,
    VT_URL = 14,
    VT_START_TIME = 16,
    VT_END_TIME = 18,
    VT_UPDATE_TIME = 20,
    VT_GOOD_COUNT = 22,
    VT_BAD_COUNT = 24,
    VT_IMGURL = 26,
    VT_DISPLAY_TIME = 28
  };
  int8_t status() const {
    return GetField<int8_t>(VT_STATUS, 0);
  }
  uint64_t eventid() const {
    return GetField<uint64_t>(VT_EVENTID, 0);
  }
  const flatbuffers::String *title() const {
    return GetPointer<const flatbuffers::String *>(VT_TITLE);
  }
  const flatbuffers::String *message() const {
    return GetPointer<const flatbuffers::String *>(VT_MESSAGE);
  }
  const flatbuffers::String *supplier() const {
    return GetPointer<const flatbuffers::String *>(VT_SUPPLIER);
  }
  const flatbuffers::String *url() const {
    return GetPointer<const flatbuffers::String *>(VT_URL);
  }
  uint64_t start_time() const {
    return GetField<uint64_t>(VT_START_TIME, 0);
  }
  uint64_t end_time() const {
    return GetField<uint64_t>(VT_END_TIME, 0);
  }
  uint64_t update_time() const {
    return GetField<uint64_t>(VT_UPDATE_TIME, 0);
  }
  uint32_t good_count() const {
    return GetField<uint32_t>(VT_GOOD_COUNT, 0);
  }
  uint32_t bad_count() const {
    return GetField<uint32_t>(VT_BAD_COUNT, 0);
  }
  const flatbuffers::String *imgurl() const {
    return GetPointer<const flatbuffers::String *>(VT_IMGURL);
  }
  int32_t display_time() const {
    return GetField<int32_t>(VT_DISPLAY_TIME, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_STATUS) &&
           VerifyField<uint64_t>(verifier, VT_EVENTID) &&
           VerifyOffset(verifier, VT_TITLE) &&
           verifier.VerifyString(title()) &&
           VerifyOffset(verifier, VT_MESSAGE) &&
           verifier.VerifyString(message()) &&
           VerifyOffset(verifier, VT_SUPPLIER) &&
           verifier.VerifyString(supplier()) &&
           VerifyOffset(verifier, VT_URL) &&
           verifier.VerifyString(url()) &&
           VerifyField<uint64_t>(verifier, VT_START_TIME) &&
           VerifyField<uint64_t>(verifier, VT_END_TIME) &&
           VerifyField<uint64_t>(verifier, VT_UPDATE_TIME) &&
           VerifyField<uint32_t>(verifier, VT_GOOD_COUNT) &&
           VerifyField<uint32_t>(verifier, VT_BAD_COUNT) &&
           VerifyOffset(verifier, VT_IMGURL) &&
           verifier.VerifyString(imgurl()) &&
           VerifyField<int32_t>(verifier, VT_DISPLAY_TIME) &&
           verifier.EndTable();
  }
  CRDetailResponseT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(CRDetailResponseT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<CRDetailResponse> Pack(flatbuffers::FlatBufferBuilder &_fbb, const CRDetailResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CRDetailResponseBuilder {
  typedef CRDetailResponse Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_status(int8_t status) {
    fbb_.AddElement<int8_t>(CRDetailResponse::VT_STATUS, status, 0);
  }
  void add_eventid(uint64_t eventid) {
    fbb_.AddElement<uint64_t>(CRDetailResponse::VT_EVENTID, eventid, 0);
  }
  void add_title(flatbuffers::Offset<flatbuffers::String> title) {
    fbb_.AddOffset(CRDetailResponse::VT_TITLE, title);
  }
  void add_message(flatbuffers::Offset<flatbuffers::String> message) {
    fbb_.AddOffset(CRDetailResponse::VT_MESSAGE, message);
  }
  void add_supplier(flatbuffers::Offset<flatbuffers::String> supplier) {
    fbb_.AddOffset(CRDetailResponse::VT_SUPPLIER, supplier);
  }
  void add_url(flatbuffers::Offset<flatbuffers::String> url) {
    fbb_.AddOffset(CRDetailResponse::VT_URL, url);
  }
  void add_start_time(uint64_t start_time) {
    fbb_.AddElement<uint64_t>(CRDetailResponse::VT_START_TIME, start_time, 0);
  }
  void add_end_time(uint64_t end_time) {
    fbb_.AddElement<uint64_t>(CRDetailResponse::VT_END_TIME, end_time, 0);
  }
  void add_update_time(uint64_t update_time) {
    fbb_.AddElement<uint64_t>(CRDetailResponse::VT_UPDATE_TIME, update_time, 0);
  }
  void add_good_count(uint32_t good_count) {
    fbb_.AddElement<uint32_t>(CRDetailResponse::VT_GOOD_COUNT, good_count, 0);
  }
  void add_bad_count(uint32_t bad_count) {
    fbb_.AddElement<uint32_t>(CRDetailResponse::VT_BAD_COUNT, bad_count, 0);
  }
  void add_imgurl(flatbuffers::Offset<flatbuffers::String> imgurl) {
    fbb_.AddOffset(CRDetailResponse::VT_IMGURL, imgurl);
  }
  void add_display_time(int32_t display_time) {
    fbb_.AddElement<int32_t>(CRDetailResponse::VT_DISPLAY_TIME, display_time, 0);
  }
  explicit CRDetailResponseBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<CRDetailResponse> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<CRDetailResponse>(end);
    return o;
  }
};

inline flatbuffers::Offset<CRDetailResponse> CreateCRDetailResponse(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t status = 0,
    uint64_t eventid = 0,
    flatbuffers::Offset<flatbuffers::String> title = 0,
    flatbuffers::Offset<flatbuffers::String> message = 0,
    flatbuffers::Offset<flatbuffers::String> supplier = 0,
    flatbuffers::Offset<flatbuffers::String> url = 0,
    uint64_t start_time = 0,
    uint64_t end_time = 0,
    uint64_t update_time = 0,
    uint32_t good_count = 0,
    uint32_t bad_count = 0,
    flatbuffers::Offset<flatbuffers::String> imgurl = 0,
    int32_t display_time = 0) {
  CRDetailResponseBuilder builder_(_fbb);
  builder_.add_update_time(update_time);
  builder_.add_end_time(end_time);
  builder_.add_start_time(start_time);
  builder_.add_eventid(eventid);
  builder_.add_display_time(display_time);
  builder_.add_imgurl(imgurl);
  builder_.add_bad_count(bad_count);
  builder_.add_good_count(good_count);
  builder_.add_url(url);
  builder_.add_supplier(supplier);
  builder_.add_message(message);
  builder_.add_title(title);
  builder_.add_status(status);
  return builder_.Finish();
}

inline flatbuffers::Offset<CRDetailResponse> CreateCRDetailResponseDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t status = 0,
    uint64_t eventid = 0,
    const char *title = nullptr,
    const char *message = nullptr,
    const char *supplier = nullptr,
    const char *url = nullptr,
    uint64_t start_time = 0,
    uint64_t end_time = 0,
    uint64_t update_time = 0,
    uint32_t good_count = 0,
    uint32_t bad_count = 0,
    const char *imgurl = nullptr,
    int32_t display_time = 0) {
  auto title__ = title ? _fbb.CreateString(title) : 0;
  auto message__ = message ? _fbb.CreateString(message) : 0;
  auto supplier__ = supplier ? _fbb.CreateString(supplier) : 0;
  auto url__ = url ? _fbb.CreateString(url) : 0;
  auto imgurl__ = imgurl ? _fbb.CreateString(imgurl) : 0;
  return UnimapCloseRoad::CreateCRDetailResponse(
      _fbb,
      status,
      eventid,
      title__,
      message__,
      supplier__,
      url__,
      start_time,
      end_time,
      update_time,
      good_count,
      bad_count,
      imgurl__,
      display_time);
}

flatbuffers::Offset<CRDetailResponse> CreateCRDetailResponse(flatbuffers::FlatBufferBuilder &_fbb, const CRDetailResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline LineT *Line::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<LineT>(new LineT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void Line::UnPackTo(LineT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = points(); if (_e) { _o->points.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->points[_i] = *_e->Get(_i); } } }
  { auto _e = break_points(); if (_e) { _o->break_points.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->break_points[_i] = *_e->Get(_i); } } }
}

inline flatbuffers::Offset<Line> Line::Pack(flatbuffers::FlatBufferBuilder &_fbb, const LineT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateLine(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Line> CreateLine(flatbuffers::FlatBufferBuilder &_fbb, const LineT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const LineT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _points = _o->points.size() ? _fbb.CreateVectorOfStructs(_o->points) : 0;
  auto _break_points = _o->break_points.size() ? _fbb.CreateVectorOfStructs(_o->break_points) : 0;
  return UnimapCloseRoad::CreateLine(
      _fbb,
      _points,
      _break_points);
}

inline RoadGeoT *RoadGeo::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RoadGeoT>(new RoadGeoT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RoadGeo::UnPackTo(RoadGeoT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = id(); _o->id = _e; }
  { auto _e = lines(); if (_e) { _o->lines.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->lines[_i] = std::unique_ptr<UnimapCloseRoad::LineT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RoadGeo> RoadGeo::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadGeoT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRoadGeo(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RoadGeo> CreateRoadGeo(flatbuffers::FlatBufferBuilder &_fbb, const RoadGeoT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RoadGeoT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _id = _o->id;
  auto _lines = _o->lines.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::Line>> (_o->lines.size(), [](size_t i, _VectorArgs *__va) { return CreateLine(*__va->__fbb, __va->__o->lines[i].get(), __va->__rehasher); }, &_va ) : 0;
  return UnimapCloseRoad::CreateRoadGeo(
      _fbb,
      _id,
      _lines);
}

inline RelativeLineT *RelativeLine::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RelativeLineT>(new RelativeLineT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RelativeLine::UnPackTo(RelativeLineT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = points(); if (_e) { _o->points.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->points[_i] = *_e->Get(_i); } } }
  { auto _e = relative_z(); if (_e) { _o->relative_z.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->relative_z[_i] = _e->Get(_i); } } }
  { auto _e = break_points(); if (_e) { _o->break_points.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->break_points[_i] = *_e->Get(_i); } } }
}

inline flatbuffers::Offset<RelativeLine> RelativeLine::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RelativeLineT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRelativeLine(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RelativeLine> CreateRelativeLine(flatbuffers::FlatBufferBuilder &_fbb, const RelativeLineT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RelativeLineT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _points = _o->points.size() ? _fbb.CreateVectorOfStructs(_o->points) : 0;
  auto _relative_z = _o->relative_z.size() ? _fbb.CreateVector(_o->relative_z) : 0;
  auto _break_points = _o->break_points.size() ? _fbb.CreateVectorOfStructs(_o->break_points) : 0;
  return UnimapCloseRoad::CreateRelativeLine(
      _fbb,
      _points,
      _relative_z,
      _break_points);
}

inline RelativeRoadGeoT *RelativeRoadGeo::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RelativeRoadGeoT>(new RelativeRoadGeoT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RelativeRoadGeo::UnPackTo(RelativeRoadGeoT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = id(); _o->id = _e; }
  { auto _e = relative_lines(); if (_e) { _o->relative_lines.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->relative_lines[_i] = std::unique_ptr<UnimapCloseRoad::RelativeLineT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RelativeRoadGeo> RelativeRoadGeo::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RelativeRoadGeoT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRelativeRoadGeo(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RelativeRoadGeo> CreateRelativeRoadGeo(flatbuffers::FlatBufferBuilder &_fbb, const RelativeRoadGeoT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RelativeRoadGeoT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _id = _o->id;
  auto _relative_lines = _o->relative_lines.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RelativeLine>> (_o->relative_lines.size(), [](size_t i, _VectorArgs *__va) { return CreateRelativeLine(*__va->__fbb, __va->__o->relative_lines[i].get(), __va->__rehasher); }, &_va ) : 0;
  return UnimapCloseRoad::CreateRelativeRoadGeo(
      _fbb,
      _id,
      _relative_lines);
}

inline EventInfoT *EventInfo::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<EventInfoT>(new EventInfoT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void EventInfo::UnPackTo(EventInfoT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = id(); _o->id = _e; }
  { auto _e = start_time(); _o->start_time = _e; }
  { auto _e = end_time(); _o->end_time = _e; }
  { auto _e = class_code(); _o->class_code = _e; }
  { auto _e = priority(); _o->priority = _e; }
}

inline flatbuffers::Offset<EventInfo> EventInfo::Pack(flatbuffers::FlatBufferBuilder &_fbb, const EventInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateEventInfo(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<EventInfo> CreateEventInfo(flatbuffers::FlatBufferBuilder &_fbb, const EventInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const EventInfoT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _id = _o->id;
  auto _start_time = _o->start_time;
  auto _end_time = _o->end_time;
  auto _class_code = _o->class_code;
  auto _priority = _o->priority;
  return UnimapCloseRoad::CreateEventInfo(
      _fbb,
      _id,
      _start_time,
      _end_time,
      _class_code,
      _priority);
}

inline RoadLayerT *RoadLayer::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RoadLayerT>(new RoadLayerT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RoadLayer::UnPackTo(RoadLayerT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = type(); _o->type = _e; }
  { auto _e = style_id(); _o->style_id = _e; }
  { auto _e = scale_mask(); if (_e) { _o->scale_mask.resize(_e->size()); std::copy(_e->begin(), _e->end(), _o->scale_mask.begin()); } }
  { auto _e = event_idx(); _o->event_idx = _e; }
  { auto _e = priority(); _o->priority = _e; }
  { auto _e = second_priority(); _o->second_priority = _e; }
  { auto _e = road_geo_idx(); if (_e) { _o->road_geo_idx.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->road_geo_idx[_i] = _e->Get(_i); } } }
  { auto _e = single_flag(); if (_e) { _o->single_flag.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->single_flag[_i] = static_cast<UnimapCloseRoad::RoadSingleFlag>(_e->Get(_i)); } } }
  { auto _e = feature_type(); _o->feature_type = _e; }
}

inline flatbuffers::Offset<RoadLayer> RoadLayer::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadLayerT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRoadLayer(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RoadLayer> CreateRoadLayer(flatbuffers::FlatBufferBuilder &_fbb, const RoadLayerT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RoadLayerT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _type = _o->type;
  auto _style_id = _o->style_id;
  auto _scale_mask = _o->scale_mask.size() ? _fbb.CreateVector(_o->scale_mask) : 0;
  auto _event_idx = _o->event_idx;
  auto _priority = _o->priority;
  auto _second_priority = _o->second_priority;
  auto _road_geo_idx = _o->road_geo_idx.size() ? _fbb.CreateVector(_o->road_geo_idx) : 0;
  auto _single_flag = _o->single_flag.size() ? _fbb.CreateVectorScalarCast<int8_t>(flatbuffers::data(_o->single_flag), _o->single_flag.size()) : 0;
  auto _feature_type = _o->feature_type;
  return UnimapCloseRoad::CreateRoadLayer(
      _fbb,
      _type,
      _style_id,
      _scale_mask,
      _event_idx,
      _priority,
      _second_priority,
      _road_geo_idx,
      _single_flag,
      _feature_type);
}

inline MarkerT *Marker::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<MarkerT>(new MarkerT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void Marker::UnPackTo(MarkerT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = event_idx(); _o->event_idx = _e; }
  { auto _e = style_id(); _o->style_id = _e; }
  { auto _e = road_style_id(); _o->road_style_id = _e; }
  { auto _e = priority(); _o->priority = _e; }
  { auto _e = point(); if (_e) _o->point = std::unique_ptr<UnimapCloseRoad::Point>(new UnimapCloseRoad::Point(*_e)); }
  { auto _e = feature_type(); _o->feature_type = _e; }
}

inline flatbuffers::Offset<Marker> Marker::Pack(flatbuffers::FlatBufferBuilder &_fbb, const MarkerT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateMarker(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<Marker> CreateMarker(flatbuffers::FlatBufferBuilder &_fbb, const MarkerT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const MarkerT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _event_idx = _o->event_idx;
  auto _style_id = _o->style_id;
  auto _road_style_id = _o->road_style_id;
  auto _priority = _o->priority;
  auto _point = _o->point ? _o->point.get() : 0;
  auto _feature_type = _o->feature_type;
  return UnimapCloseRoad::CreateMarker(
      _fbb,
      _event_idx,
      _style_id,
      _road_style_id,
      _priority,
      _point,
      _feature_type);
}

inline MarkerLayerT *MarkerLayer::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<MarkerLayerT>(new MarkerLayerT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void MarkerLayer::UnPackTo(MarkerLayerT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = scale_mask(); if (_e) { _o->scale_mask.resize(_e->size()); std::copy(_e->begin(), _e->end(), _o->scale_mask.begin()); } }
  { auto _e = markers(); if (_e) { _o->markers.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->markers[_i] = std::unique_ptr<UnimapCloseRoad::MarkerT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<MarkerLayer> MarkerLayer::Pack(flatbuffers::FlatBufferBuilder &_fbb, const MarkerLayerT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateMarkerLayer(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<MarkerLayer> CreateMarkerLayer(flatbuffers::FlatBufferBuilder &_fbb, const MarkerLayerT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const MarkerLayerT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _scale_mask = _o->scale_mask.size() ? _fbb.CreateVector(_o->scale_mask) : 0;
  auto _markers = _o->markers.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::Marker>> (_o->markers.size(), [](size_t i, _VectorArgs *__va) { return CreateMarker(*__va->__fbb, __va->__o->markers[i].get(), __va->__rehasher); }, &_va ) : 0;
  return UnimapCloseRoad::CreateMarkerLayer(
      _fbb,
      _scale_mask,
      _markers);
}

inline TileDataT *TileData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<TileDataT>(new TileDataT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void TileData::UnPackTo(TileDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = status(); _o->status = _e; }
  { auto _e = tileid(); _o->tileid = _e; }
  { auto _e = time_stamp(); _o->time_stamp = _e; }
  { auto _e = sw_conner(); if (_e) _o->sw_conner = std::unique_ptr<UnimapCloseRoad::Point>(new UnimapCloseRoad::Point(*_e)); }
  { auto _e = unit(); _o->unit = _e; }
  { auto _e = events(); if (_e) { _o->events.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->events[_i] = std::unique_ptr<UnimapCloseRoad::EventInfoT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = roads_geo(); if (_e) { _o->roads_geo.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->roads_geo[_i] = std::unique_ptr<UnimapCloseRoad::RoadGeoT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = reroads_geo(); if (_e) { _o->reroads_geo.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->reroads_geo[_i] = std::unique_ptr<UnimapCloseRoad::RelativeRoadGeoT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = road_layers(); if (_e) { _o->road_layers.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->road_layers[_i] = std::unique_ptr<UnimapCloseRoad::RoadLayerT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = markers(); if (_e) { _o->markers.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->markers[_i] = std::unique_ptr<UnimapCloseRoad::MarkerLayerT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<TileData> TileData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const TileDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateTileData(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<TileData> CreateTileData(flatbuffers::FlatBufferBuilder &_fbb, const TileDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const TileDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _status = _o->status;
  auto _tileid = _o->tileid;
  auto _time_stamp = _o->time_stamp;
  auto _sw_conner = _o->sw_conner ? _o->sw_conner.get() : 0;
  auto _unit = _o->unit;
  auto _events = _o->events.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::EventInfo>> (_o->events.size(), [](size_t i, _VectorArgs *__va) { return CreateEventInfo(*__va->__fbb, __va->__o->events[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _roads_geo = _o->roads_geo.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RoadGeo>> (_o->roads_geo.size(), [](size_t i, _VectorArgs *__va) { return CreateRoadGeo(*__va->__fbb, __va->__o->roads_geo[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _reroads_geo = _o->reroads_geo.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RelativeRoadGeo>> (_o->reroads_geo.size(), [](size_t i, _VectorArgs *__va) { return CreateRelativeRoadGeo(*__va->__fbb, __va->__o->reroads_geo[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _road_layers = _o->road_layers.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::RoadLayer>> (_o->road_layers.size(), [](size_t i, _VectorArgs *__va) { return CreateRoadLayer(*__va->__fbb, __va->__o->road_layers[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _markers = _o->markers.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::MarkerLayer>> (_o->markers.size(), [](size_t i, _VectorArgs *__va) { return CreateMarkerLayer(*__va->__fbb, __va->__o->markers[i].get(), __va->__rehasher); }, &_va ) : 0;
  return UnimapCloseRoad::CreateTileData(
      _fbb,
      _status,
      _tileid,
      _time_stamp,
      _sw_conner,
      _unit,
      _events,
      _roads_geo,
      _reroads_geo,
      _road_layers,
      _markers);
}

inline TileDescriptorT *TileDescriptor::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<TileDescriptorT>(new TileDescriptorT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void TileDescriptor::UnPackTo(TileDescriptorT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = status(); _o->status = _e; }
  { auto _e = type(); _o->type = _e; }
  { auto _e = tileid(); _o->tileid = _e; }
  { auto _e = version(); _o->version = _e; }
  { auto _e = interval(); _o->interval = _e; }
  { auto _e = origin_data_size(); _o->origin_data_size = _e; }
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); std::copy(_e->begin(), _e->end(), _o->data.begin()); } }
}

inline flatbuffers::Offset<TileDescriptor> TileDescriptor::Pack(flatbuffers::FlatBufferBuilder &_fbb, const TileDescriptorT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateTileDescriptor(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<TileDescriptor> CreateTileDescriptor(flatbuffers::FlatBufferBuilder &_fbb, const TileDescriptorT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const TileDescriptorT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _status = _o->status;
  auto _type = _o->type;
  auto _tileid = _o->tileid;
  auto _version = _o->version;
  auto _interval = _o->interval;
  auto _origin_data_size = _o->origin_data_size;
  auto _data = _o->data.size() ? _fbb.CreateVector(_o->data) : 0;
  return UnimapCloseRoad::CreateTileDescriptor(
      _fbb,
      _status,
      _type,
      _tileid,
      _version,
      _interval,
      _origin_data_size,
      _data);
}

inline CRTileResponseT *CRTileResponse::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<CRTileResponseT>(new CRTileResponseT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void CRTileResponse::UnPackTo(CRTileResponseT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = tiles(); if (_e) { _o->tiles.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->tiles[_i] = std::unique_ptr<UnimapCloseRoad::TileDescriptorT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<CRTileResponse> CRTileResponse::Pack(flatbuffers::FlatBufferBuilder &_fbb, const CRTileResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateCRTileResponse(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<CRTileResponse> CreateCRTileResponse(flatbuffers::FlatBufferBuilder &_fbb, const CRTileResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const CRTileResponseT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _tiles = _o->tiles.size() ? _fbb.CreateVector<flatbuffers::Offset<UnimapCloseRoad::TileDescriptor>> (_o->tiles.size(), [](size_t i, _VectorArgs *__va) { return CreateTileDescriptor(*__va->__fbb, __va->__o->tiles[i].get(), __va->__rehasher); }, &_va ) : 0;
  return UnimapCloseRoad::CreateCRTileResponse(
      _fbb,
      _tiles);
}

inline CRDetailResponseT *CRDetailResponse::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<CRDetailResponseT>(new CRDetailResponseT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void CRDetailResponse::UnPackTo(CRDetailResponseT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = status(); _o->status = _e; }
  { auto _e = eventid(); _o->eventid = _e; }
  { auto _e = title(); if (_e) _o->title = _e->str(); }
  { auto _e = message(); if (_e) _o->message = _e->str(); }
  { auto _e = supplier(); if (_e) _o->supplier = _e->str(); }
  { auto _e = url(); if (_e) _o->url = _e->str(); }
  { auto _e = start_time(); _o->start_time = _e; }
  { auto _e = end_time(); _o->end_time = _e; }
  { auto _e = update_time(); _o->update_time = _e; }
  { auto _e = good_count(); _o->good_count = _e; }
  { auto _e = bad_count(); _o->bad_count = _e; }
  { auto _e = imgurl(); if (_e) _o->imgurl = _e->str(); }
  { auto _e = display_time(); _o->display_time = _e; }
}

inline flatbuffers::Offset<CRDetailResponse> CRDetailResponse::Pack(flatbuffers::FlatBufferBuilder &_fbb, const CRDetailResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateCRDetailResponse(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<CRDetailResponse> CreateCRDetailResponse(flatbuffers::FlatBufferBuilder &_fbb, const CRDetailResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const CRDetailResponseT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _status = _o->status;
  auto _eventid = _o->eventid;
  auto _title = _o->title.empty() ? 0 : _fbb.CreateString(_o->title);
  auto _message = _o->message.empty() ? 0 : _fbb.CreateString(_o->message);
  auto _supplier = _o->supplier.empty() ? 0 : _fbb.CreateString(_o->supplier);
  auto _url = _o->url.empty() ? 0 : _fbb.CreateString(_o->url);
  auto _start_time = _o->start_time;
  auto _end_time = _o->end_time;
  auto _update_time = _o->update_time;
  auto _good_count = _o->good_count;
  auto _bad_count = _o->bad_count;
  auto _imgurl = _o->imgurl.empty() ? 0 : _fbb.CreateString(_o->imgurl);
  auto _display_time = _o->display_time;
  return UnimapCloseRoad::CreateCRDetailResponse(
      _fbb,
      _status,
      _eventid,
      _title,
      _message,
      _supplier,
      _url,
      _start_time,
      _end_time,
      _update_time,
      _good_count,
      _bad_count,
      _imgurl,
      _display_time);
}

inline const UnimapCloseRoad::TileData *GetTileData(const void *buf) {
  return flatbuffers::GetRoot<UnimapCloseRoad::TileData>(buf);
}

inline const UnimapCloseRoad::TileData *GetSizePrefixedTileData(const void *buf) {
  return flatbuffers::GetSizePrefixedRoot<UnimapCloseRoad::TileData>(buf);
}

inline bool VerifyTileDataBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<UnimapCloseRoad::TileData>(nullptr);
}

inline bool VerifySizePrefixedTileDataBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<UnimapCloseRoad::TileData>(nullptr);
}

inline void FinishTileDataBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<UnimapCloseRoad::TileData> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedTileDataBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<UnimapCloseRoad::TileData> root) {
  fbb.FinishSizePrefixed(root);
}

inline std::unique_ptr<UnimapCloseRoad::TileDataT> UnPackTileData(
    const void *buf,
    const flatbuffers::resolver_function_t *res = nullptr) {
  return std::unique_ptr<UnimapCloseRoad::TileDataT>(GetTileData(buf)->UnPack(res));
}

inline std::unique_ptr<UnimapCloseRoad::TileDataT> UnPackSizePrefixedTileData(
    const void *buf,
    const flatbuffers::resolver_function_t *res = nullptr) {
  return std::unique_ptr<UnimapCloseRoad::TileDataT>(GetSizePrefixedTileData(buf)->UnPack(res));
}

}  // namespace UnimapCloseRoad

#endif  // FLATBUFFERS_GENERATED_UNIMAPCLOSEROAD_UNIMAPCLOSEROAD_H_
