// Copyright 2022 Tencent Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <memory>
#include <vector>

#include "dto/types.h"
#include "nerd_export.h"

#define SUBWAY_TYPE 2
#define BUILDING_TYPE 6

#define DEFINE_32_VALUE 4294967295
#define DEFINE_16_VALUE 65535

namespace nerd {
namespace api {

class IObject3DFeature;

// 普通背景 + AOI + 地铁面
struct NERD_EXPORT RegionFeature {
  // ID
  AreaFeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺, 指明各级别比例尺是否需要显示
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 公共边索引, 指明公共边在几何形状中的下标
  std::vector<int32_t> common_edges;
  // 原始数据提供的ID, 根据src不同代表不同体系
  std::string raw_id;
  // 类型, 原始数据提供的类型码, 根据src不同代表不同体系; 备注: 该字段目前没有赋值;
  std::string kind_code;
  // 数据来源: background, aoi, subway
  std::string src;
  /**
   * 用于景区激活控件显示的开始级别;
   * 默认值:0xFF, 当渲染显示级别 > 该值时 做业务逻辑处理;
   **/
  uint8_t from_level_to_activate = 0xFF;
  int32_t style_id_new{-1};//新地铁线id,默认-1未无效值。
  uint32_t color{0xffffff};//地铁颜色，默认为无色,格式rgb
};

struct NERD_EXPORT BuildingInstanceElement {
    /**
     *实例化类型（0:不可实例化，1 矩形，2:圆角矩形）
     */
    uint32_t type;
    
    /**
     *  实例化矩形/圆角矩形 宽度 单位（cm）
     */
    uint32_t width;
    
    /***
     * 实例化端点（NDS坐标）
     *
     */
    std::vector<nerd::api::Coordinate> points;
    
    /***
     * 实例化d的建筑类型：0:建筑主体 1:顶结构
     *
     */
    uint32_t building_type;
};

// 楼块
struct NERD_EXPORT BuildingFeature {
  // ID, 备注: 引擎动态生成, 瓦片内唯一;
  FeatureIDType id{0, 0};
  // 样式ID
  uint32_t style_id{0};
  // 高度(米)
  float height{0};
  // 关联的POI ID, 0: 无效
  uint64_t poi_id{0};
  // 主表featureID
  uint16_t main_feature_id{0};
  // 描述楼块与道路的压盖关系, 每个二进制位代表一种压盖类型, 具体参考需求链接, 默认没有任何压盖;
  uint8_t overlap_flag_with_road{0};
  // 是否有女儿墙
  bool has_parapet{false};
  // 是否被精模覆盖
  bool is_covered_by_model{false};
  // 压盖 L1 的通模类型，详见枚举BuildingOverlapWithTemplateModelType
  uint32_t cover_L1_template_model_type{0};
  // 是否可点击
  bool is_clickable{false};
  // 楼的质心(备注: 若多个楼属于同一个楼, 该质心代表综合楼体的质心; 若为0代表无效)
  nerd::api::Coordinate centroid;
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 公共边索引, 指明公共边在几何形状中的下标
  std::vector<uint16_t> common_edges;
  // 关联的通模ID
  std::vector<FeatureIDType> model_ids;
  // 顶结构几何, MultiPolygon
  std::vector<std::vector<nerd::api::Coordinate>> roofs;
    
  /**
   * 被那些L4压盖了,L4的ID列表,存储对应L4的指针,
   * 为后续扩展，此处将ID存储改为L4的指针存储
   */
  std::vector<std::shared_ptr<IObject3DFeature>> is_covered_by_models;
  // 名称, 目前只支持中文
  std::string name;
  // 名称摆放的点串, 为两个点组成的直线, 个数不为2，为无效点串
  std::vector<Coordinate> name_points = {};
  // 名称字符间留白在点串长度中的占比
  uint8_t name_gap_percent{0};
  
  /**
   * 实例化列表，
   * 对于实例化的数据，该列表至少包含一个（主体）和 若干的附属体（例如顶结构）
   * 对于不可实例化的数据，该列表长度为零
   * 对于建筑L1，原始的建模方式是：数据给建筑边线的点串，由渲染引擎完成楼块的建模
   * 对于可以实例化L1: 数据直接给引擎 可以唯一标识一个楼块形状的参数，该产生可以通过
   * 对一个cube(包括圆角)做变换，然后生成一个楼块
   *
   */
    std::vector<BuildingInstanceElement> instance_list;
};



// L3窗户的类型
enum class L3WindowsDisplayLayoutType : uint8_t {
    kWINDOW_DISPLAY_LAYOUT_TYPE_NONE             = 0,                    // None
    kWINDOW_DISPLAY_LAYOUT_TYPE_PANE             = 1,                    // 窗格
    kWINDOW_DISPLAY_LAYOUT_TYPE_HORIZONTAL       = 2,                    // 横向
    kWINDOW_DISPLAY_LAYOUT_TYPE_VERTICAL         = 4,                    // 纵向
    kWINDOW_DISPLAY_LAYOUT_TYPE_DIAMOND_FILLED   = 5,                    // 菱形(铺满)
    kWINDOW_DISPLAY_LAYOUT_TYPE_DIAMOND_SPARSE   = 6,                    // 菱形(稀疏)
};

// L3窗户大小，间距，位置描述
struct NERD_EXPORT L3BuildingWindowsDisplaySetting {
    L3WindowsDisplayLayoutType windows_display_layout_type = L3WindowsDisplayLayoutType::kWINDOW_DISPLAY_LAYOUT_TYPE_NONE;                 // the windows display layout type
    uint32_t window_width{DEFINE_32_VALUE};                                                  // 窗格宽度，单位 cm
    uint32_t window_height{DEFINE_32_VALUE};                                                 // 窗格高度，单位 cm
    uint16_t window_start_x{DEFINE_16_VALUE};                                                // 窗格x开始位置 , 比例1000%单位
    uint16_t window_start_y{DEFINE_16_VALUE};                                                // 窗格x结束位置 , 比例1000%单位
    uint16_t window_end_x{DEFINE_16_VALUE};                                                  // 窗格y开始位置 , 比例1000%单位
    uint16_t window_end_y{DEFINE_16_VALUE};                                                  // 窗格y结束位置 , 比例1000%单位
    uint32_t window_top_spacing{DEFINE_32_VALUE};                                            // 窗格顶部留白，单位 cm
    uint32_t window_bottom_spacing{DEFINE_32_VALUE};                                         // 窗格底部留白，单位 cm
    uint32_t window_left_spacing{DEFINE_32_VALUE};                                           // 窗格左边留白，单位 cm
    uint32_t window_right_spacing{DEFINE_32_VALUE};                                          // 窗格右边留白，单位 cm
    uint32_t windows_spacing_in_horizontal{DEFINE_32_VALUE};                                 // 窗格之间水平留白，单位 cm
    uint32_t windows_spacing_in_vertical{DEFINE_32_VALUE};                                   // 窗格之间垂直留白，单位 cm
};

// L3窗户绘制格子对应的索引结构
struct NERD_EXPORT L3BuildingWallDisplaySetting {
    uint32_t outer_wall_edge_start_position{DEFINE_32_VALUE};                                // 外环点
    uint32_t inner_polygon_line_string_index{DEFINE_32_VALUE};                               // 内环的索引
    uint32_t inner_wall_edge_start_position{DEFINE_32_VALUE};                                // 内环点
    std::vector<uint32_t> windows_display_setting_index = {};                                // 对应窗户的数据的索引
};

struct NERD_EXPORT L3LineStringGeometry {
    std::shared_ptr<std::vector<Coordinate>> geometry{nullptr};
};

// L3建筑物多边形数据，outer_line_string 外环数据， inner_line_string 内环数据
struct NERD_EXPORT L3ComplexPolygonGeometry {
    L3LineStringGeometry outer_line_string;
    std::vector<L3LineStringGeometry> inner_line_string = {};
};

//  L3建筑物数据
struct NERD_EXPORT L3BuildingFeature {
    L3ComplexPolygonGeometry complex_polygon_geometry;
    std::vector<L3BuildingWallDisplaySetting> building_wall_display_setting = {};
};

struct NERD_EXPORT L3Feature {
    L3BuildingFeature l3_feature_infos;
};

// L3窗户显示数据描述
struct NERD_EXPORT L3WindowsDisplayFeature {
    L3BuildingWindowsDisplaySetting l3_windows_display_setting;
};

/**
 * 组件模型矩阵变换参数
 */
struct ComponentModelTransferMatrix {
  // 矩阵缩放参数，xyz三个方向
  std::vector<float> transfer_scales;
  // 矩阵旋转参数，xyz三个方向
  std::vector<float> transfer_rotations;
  // 矩阵平移参数，xyz三个方向
  std::vector<int> transfer_position;
  // 组件模型锚点/组件模挤出式方向
  std::vector<int> transfer_anchor;
};

/**
*   古建筑风格
*/
enum class AncientBuildingStyleType : uint8_t {
  // None空类型不做处理
  kANCIENT_BUILDING_STYLE_TYPE_NONE                            = 0,
  // 京派
  kANCIENT_BUILDING_STYLE_TYPE_BEI_JING_STYLE                  = 1,
  // 徽派
  kANCIENT_BUILDING_STYLE_TYPE_HUI_STYLE                       = 2,
  // 苏派
  kANCIENT_BUILDING_STYLE_TYPE_SU_STYLE                        = 3,
  // 晋派
  kANCIENT_BUILDING_STYLE_TYPE_JIN_STYLE                       = 4,
  // 川派
  kANCIENT_BUILDING_STYLE_TYPE_CHUAN_STYLE                     = 5,
  // 闽派
  kANCIENT_BUILDING_STYLE_TYPE_MING_STYLE                      = 6,
  // 粤派
  kANCIENT_BUILDING_STYLE_TYPE_YUE_STYLE                       = 7,
};

/**
*   古建筑屋顶风格
*/
enum class AncientBuildingRoofStyleType : uint8_t {
  // None空类型不做处理
  kANCIENT_BUILDING_ROOF_STYLE_TYPE_NONE                        = 0,
  // 歇山顶
  kANCIENT_BUILDING_ROOF_STYLE_TYPE_XIE_MOUNTAIN_TOP            = 1,
  // 悬山顶
  kANCIENT_BUILDING_ROOF_STYLE_TYPE_HANGING_MOUNTAIN_TOP        = 2,
  // 硬山顶
  kANCIENT_BUILDING_ROOF_STYLE_TYPE_HARD_MOUNTAIN_TOP           = 3,
};

enum class PrimitivesGeometryType : uint8_t {
  // None空类型不做处理
  kSPLINE_PRIMITIVE_TYPE_NONE                        = 0,
  // 样条线类型
  kSPLINE_PRIMITIVE_TYPE_LINE                        = 1,
  // 挤出式多边形类型
  kSPLINE_PRIMITIVE_TYPE_POLYGON                     = 2,
  // 三角面类型
  kSPLINE_PRIMITIVE_TYPE_TRIANGLE                    = 3,
  // 条带类型
  kSPLINE_PRIMITIVE_TYPE_STRIP                       = 4,
  // 闭合条带类型
  kSPLINE_PRIMITIVE_TYPE_CLOSED_STRIP                = 5,
};


struct NERD_EXPORT PrimitivesGeometry {
  // 置换曲线id（为0时则不含置换曲线）
  uint32_t curve_id{0};
  // 置换曲线id
  PrimitivesGeometryType spline_type{PrimitivesGeometryType::kSPLINE_PRIMITIVE_TYPE_NONE};
  // 置换区域百分比
  uint8_t displacement_curve_range_percentage{0};
  // 样条线几何点
  std::shared_ptr<std::vector<Coordinate>> spline_geometry;
};

struct NERD_EXPORT AncientBuildingComponent {
  // 组件id
  uint32_t component_id{0};
  // 组件着色类型
  uint32_t color_type{0};
  // 组件材质类型
  uint32_t material_type{0};
  // 组件顶结构类型（埋点用）
  AncientBuildingRoofStyleType retro_roof_style{AncientBuildingRoofStyleType::kANCIENT_BUILDING_ROOF_STYLE_TYPE_NONE};
  // 组件样条线集合
  std::shared_ptr<std::vector<PrimitivesGeometry>> primitives_geometry_group;
  // 组件变换矩阵
  ComponentModelTransferMatrix transfer_matrix;
};

struct NERD_EXPORT AncientFloorUnit {
  // 组件宿主层级
  uint8_t level{0};
  // 组件宿主层级高度
  uint16_t level_height{0};
  // 组件跟节点id
  uint32_t component_root_id{0};
  // 组件节点信息
  std::vector<AncientBuildingComponent> ancient_building_component;
};

struct NERD_EXPORT AncientFeature {
  // 关联的POI ID, 0: 无效
  uint64_t poi_id{0};
  // 组件宿主的质心
  nerd::api::Coordinate centroid;
  // 组件关联风格类型（埋点用）
  AncientBuildingStyleType ancient_building_style_type;
  // 组件宿主的层级
  std::vector<AncientFloorUnit> ancient_floors;
};

// L3空间索引
struct NERD_EXPORT L3IndexFeature {
    uint32_t stored_tile_id{DEFINE_32_VALUE};
    std::vector<int64_t> covered_other_tile_list = {};
    Rect index_rect;
};


struct NERD_EXPORT LandUseFeature {
  // ID, 备注: 引擎动态生成, 瓦片内唯一;
  FeatureIDType id{0, 0};
  // 样式ID
  uint32_t style_id{0};
  // 高度(米)
  float height{0.0f};
  // 参考协议：LanduseTypeE
  uint64_t land_use_type{0};
  // 根据不同的类型，提供的详细分类
  uint8_t kind{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 公共边索引, 指明公共边在几何形状中的下标
  std::vector<uint16_t> common_edges;
  // 关联的通模ID
  std::vector<FeatureIDType> model_ids;
};

// SVG：球场、停车位等
struct NERD_EXPORT SVGFeature {
  // ID
  FeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 压盖顺序优先级
  int32_t priority{0};
  // 是否跨瓦片
  bool is_cross_tile{false};
  // 矩形四个顶点的坐标
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 是否用原始数据形点，不用svg的形点，如停车场
  bool use_origin_geometry{false};
};

// 人行天桥面
struct NERD_EXPORT PedestrianOverpassFeature {
  // ID
  FeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 空间点串 (3D)
  std::shared_ptr<std::vector<Coordinate>> geometry3d;
};

// 普通POI, 行政地名, 公交站, 地铁站, 地铁出入口, 红绿灯, 湖泊标注 (Icon & 文字)
struct NERD_EXPORT PointFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 文字方向
  std::vector<uint8_t> text_dir;
  // 避让优先级
  int32_t priority{0};
  // 名称
  NameWithLanguage name;
  // 名称换行位置
  int32_t name_break_pos{0};
  // 原始ID
  uint64_t raw_id{0};
  // 室内ID
  uint64_t indoor_raw_id{0};
  // 用于检索的类型: 0(普通POI), 1(公交), 2(地铁站), 4(地铁出入口), 101(城市名)
  int32_t search_type{0};
  // 是否需要过滤掉(解析时遇到了已知错误,返回时过滤掉)
  bool is_need_filter{false};
  // Rich信息文本(备注:离线数据有效)
  std::string rich_text;
  // Rich信息换行位置(备注:离线数据有效)
  int32_t rich_break_pos{0};
  // Rich样式ID(备注:离线数据有效)
  int32_t rich_style_id{0};
  // 渲染坐标 (3D)
  Coordinate geometry3d{0, 0};
  // 别名(备注:离线数据有效)
  std::string alias;
  // 原始分类编码(备注:离线数据有效)
  int32_t kind_code{0};
  // 原始数据Rank(备注:离线数据有效)
  int32_t raw_rank{0};
  // 地址(备注:离线数据有效)
  NameWithLanguage address;
  // 电话号码(备注:离线数据有效)
  std::string phone_num;
  // 网点类型, 如4S店, 5S店等(备注:离线数据有效)
  std::string sale_type;
  // 品牌(备注:离线数据有效)
  std::string brand;
  // 导航坐标 (3D)(备注:离线数据有效)
  Coordinate nav_point{0, 0};
  // 角标样式ID
  int32_t icon_style_id{0};
  /**
   * 旋转角度, 范围[0,360], 备注: 若值为-1代表没有角度;
   * 场景: 对应底商的文字需要贴墙显示;
   */
  float angle{-1};
  /**
   * POI属性的Mask值,该值会透传给下游，由下游负责解析
   * 默认值为零
   */
  uint32_t poi_type_mask{0};
  int32_t style_id_new{-1};//新地铁线id,默认-1未无效值。
  uint32_t color{0xffffff};//默认为白色,格式rgb
  /**
   * POI的rich文本信息
   *
   * 地铁rich信息格式为: 文字1|文字2@颜色1|颜色2
   *   如：10内环|10外环|西郊@38ADD9|38ADD9|FA5948
   *
   * 楼层rich信息格式如: B3/B2/B1/M/1F
   */
  std::string multi_rich_text;
  /**
   * 具有rich信息的POI类型(标识multi_rich_text字段存储的rich信息类型)，2: 地铁站， 6: 楼层
   */
  int16_t rich_poi_type{0};
  /**
   * 小程序的rich信息，一个POI可能multi_rich_text和app_rich_text同时都有值,分开
   */
  std::string app_rich_text;
  /**
   * POI关联楼块的质心坐标
   */
  Coordinate build_center{0, 0};
  /**
   * 文字内容的语言代码简称, 如: "zh-cn", "zh-tw", "en-us"等
   * 说明: 当前编译控制只有国外数据会填充, 国内为空;
   */
  std::string language_code;
};

/**
 * 通模上的标牌信息
 */
struct NERD_EXPORT ModelSignageInfo {
  /**
   * 标牌的位置信息
   */
  Coordinate brand_geometry_;
  /**
   * 标牌的样式模版信息
   */
  uint16_t brand_template_;
  /**
   * 该出入口Logo资源名称, 如:"bj_0";
   */
  std::string logo_name_;
  /**
   * 该出入口编号资源名称, 如:"A1", "B13"
   */
  std::string station_num_;
  /**
   * 该出入口名称后缀, 如:"exit"
   */
  std::string station_suffix_;
  /**
   * 站点类型资源名称, 如:"exchange"
   * 备注: 若换乘类型, 则使用line_num_
   */
  std::string station_type_;
  /**
   * 该站点对应的线路编号; 若多个采用逗号分割;
   * 如: "8", "10"
   */
  std::string line_num_;
  /**
   * 该站点对应的线路名称号线; 如: “hx”;
   */
  std::string line_suffix_;
  /**
   * 该站点对应的线路颜色; 如: “0xffffffff”(rgba);
   */
  uint32_t line_color_;
};

// 模型点描述: 种树, 加油站等
struct NERD_EXPORT ModelFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  uint32_t style_id{0};
  // 缩放倍数
  float scale{1};
  // 缩放倍数，y方向
  float scale_y{1};
  // 缩放倍数，z方向
  float scale_z{1};
  // 旋转角度
  float angle{0};
  // 通模类型
  uint8_t kind{0};
  // 几何坐标 (3D)
  Coordinate geometry3d;
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 纹理 id
  uint32_t texture_id{0};
  // 模型分类
  DataFeatureType type{DataFeatureType::kBuilding};
  // 是否被精模覆盖
  bool is_covered_by_model{false};
  /**
   * 模型的标牌信息
   * 说明: 不是所有的通模都有标牌信息, 使用需要先判断; 目前地铁出入口有标牌信息
   */
  std::shared_ptr<ModelSignageInfo> signage_info_ptr{nullptr};
  // 通模绑定的poiid
  uint64_t poiid{0};
};

// 标牌: 道路标牌, 地铁标牌 (图标底板随文字长度自适应)
struct NERD_EXPORT SignFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 名称
  NameWithLanguage name;
  // 名称换行位置
  int32_t name_break_pos{0};
  // 几何坐标 (3D)
  Coordinate geometry3d;
  /**
   * 文字内容的语言代码简称, 如: "zh-cn", "zh-tw", "en-us"等
   * 说明: 当前编译控制只有国外数据会填充, 国内为空;
   */
  std::string language_code;
};

// 箭头 (带旋转角度的贴图)
struct NERD_EXPORT ArrowFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 旋转角度
  float angle{0};
  // 几何坐标 (3D)
  Coordinate geometry3d{0, 0};
};

// 沿线文字标注: 道路名, 铁路名
struct NERD_EXPORT TextLabelFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 避让优先级
  int32_t priority{0};
  // 名称
  NameWithLanguage name;
  // 名称换行位置
  int32_t name_break_pos{0};
  // 标注形态类型: 1(直线标注), 2(曲线标注)
  int32_t shape_type{0};
  // 旋转角度[0, 360), 当shape_type=1时有效
  float angle{0};
  // 局部沿线曲线坐标串, 当shape_type=2时有效 */
  std::shared_ptr<std::vector<Coordinate>> curve_geometry;
  // 缩放状态, 三位的十进制数, 百位表示标准, 十位表示放大, 个位表示超大, 数值含义: 0(无效), 1(直线), 2(曲线)
  int32_t zoom_status;
  // 几何坐标 (3D)
  Coordinate geometry3d{0, 0};
  /**
   * 文字内容的语言代码简称, 如: "zh-cn", "zh-tw", "en-us"等
   * 说明: 当前编译控制只有国外数据会填充, 国内为空;
   */
  std::string language_code;
};

// 带标注信息的线: 道路线, 地铁线, 铁路线, 河流中线, ZLevel等
struct NERD_EXPORT LineWithLabelFeature {
  // 标注信息描述
  struct NERD_EXPORT LabelInfo {
    // 文本列表
    NameArrWithLanguage text_list;
    // 样式ID
    int32_t style_id{0};
    /**
     * 文本对应显示比例尺信息
     * 备注: 每个文本对应一个显示比例尺列表, 因此二维数组; 外层数组个数与text_list的个数相同, 部分道路有多个名称;
     */
    std::vector<std::vector<uint8_t>> show_level_list;
    // 避让优先级
    int32_t priority{0};
    int32_t style_id_new{-1};//新地铁线id,默认-1未无效值。
    uint32_t color{0xffffff};//地铁颜色，默认为白色,颜色格式rgb
  };

  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺列表
  std::vector<uint8_t> show_level;
  // 压盖优先级
  int32_t priority{0};
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 沿线文字标注信息
  LabelInfo name_label_info;
  // 沿线标牌标注信息
  LabelInfo sign_label_info;
  // 数据来源: road, subway, railway, river, zlevel
  std::string src;
  int32_t style_id_new{-1};//新地铁线id,默认-1未无效值。
  uint32_t color{0xffffff};//地铁颜色，默认为白色,颜色格式rgb
  /**
  * 文字内容的语言代码简称, 如: "zh-cn", "zh-tw", "en-us"等
  * 说明: 当前编译控制只有国外数据会填充, 国内为空;
  */
  std::string language_code;
};
std::ostream &operator<<(std::ostream &out, const LineWithLabelFeature::LabelInfo &feature);
std::ostream &operator<<(std::ostream &out, const LineWithLabelFeature &feature);

// 普通线: 背景线, 行政线, 球场线, 人行天桥边线
struct NERD_EXPORT LineFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 空间点串
  std::shared_ptr<std::vector<Coordinate>> geometry3d;
};

}  // namespace api
}  // namespace nerd
