//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/7/30.
//

#ifndef NERDAPI_INCLUDE_DTO_IFACE_LINK_ATTRIBUTE_H_
#define NERDAPI_INCLUDE_DTO_IFACE_LINK_ATTRIBUTE_H_

#include "types.h"

namespace nerd {
namespace api {

/**
 * 车道数量变化点信息
 */
struct LaneNumChangePointInfo {
  // 是否从起点到终点
  bool is_from_start_to_end{true};
  // 前继车道数量
  uint8_t previous_lane_number{0};
  // 当前车道数量
  uint8_t  current_lane_number{0};
  // 车道变化位置点
  Coordinate position;
};

/**
 * 分割线类型
 */
enum class RoadSplitLineType : uint8_t {
  /**
   * 空
   */
  NONE,
  /**
   * 虚线
   */
  DASH_LINE,
  /**
   * 实线
   */
  SOLID_LINE,
  /**
   * 左虚线右实线
   */
  LEFT_DASH_LINE_RIGHT_SOLID_LINE,
  /**
   * 右虚线左实线
   */
  RIGHT_DASH_LINE_LEFT_SOLID_LINE,
};

/**
 * 隔离带
 */
enum class SeparatorType : uint8_t {
  NOT_SURVEY    = 0,  /** 未调查 */
  LAW           = 1,  /** 法律隔离 */
  PHYSICS       = 2,  /** 物理隔离-未调查 */
  FENCE         = 3,  /** 物理隔离-护栏 */
  CRUB          = 4,  /** 物理隔离-路牙 */
  WALL          = 5,  /** 物理隔离-墙 */
  GRID          = 6,  /** 物理隔离-防护网 */
  PILLAR        = 7,  /** 物理隔离-隔离柱 */
  NONE          = 8,  /** 无隔离-未调查 */
  NATURAL       = 9,  /** 无隔离-自然边界 */
  VIRTUAL       = 10, /** 无隔离-边缘虚拟线 */
  OTHER         = 11, /** 其他 */
};

/**
 * 属性点对应的置信度登记类型
 */
enum class LaneAttrPointConfidenceLevelType : uint8_t {
  LinkAttributePointConfidenceLevelTypeNone                   = 0,        // 无
  LinkAttributePointConfidenceLevelTypeConfLevelOne           = 1,        // 置信度1级 （5m, 98.5%, tob交付范围内）
  LinkAttributePointConfidenceLevelTypeConfLevelTwo           = 2,        // 置信度2级 （5m, 90%, 10m, toc交付，图像源）
  LinkAttributePointConfidenceLevelTypeConfLevelThree         = 3,        // 置信度3级 （5m, 85%, 10m, 96%）
  LinkAttributePointConfidenceLevelTypeConfLevelFour          = 4,        // 置信度4级 （仅4k降级，同sd精度）
  LinkAttributePointConfidenceLevelTypeConfidenceInvalidation = 15,       // 置信度失效
};

/**
 * 分割线信息
 */
struct RoadSplitLineInfo {
  /**
   * 是否从起点到终点
   */
  bool is_from_start_to_end{true};
  /**
   * 分割线类型
   */
  RoadSplitLineType road_split_line_type;
  /**
   * 左侧隔离带
   */
  SeparatorType left_road_separator_type;
  /**
   * 右侧隔离带类型
   */
  SeparatorType right_road_separator_type;
  /**
   * 车道变化位置点
   */
  Coordinate position;
  /**
   * 对应该位置所有机动车道的位置点;
   * 从左到右依次排列;
   */
  std::vector<Coordinate> lane_border_points;
  /**
   * 对应该车道边界点集合的置信度等级
   */
  LaneAttrPointConfidenceLevelType lane_border_points_confidence_level{LaneAttrPointConfidenceLevelType::LinkAttributePointConfidenceLevelTypeNone};
};

/**
 * 路口对象类型，如斑马线，停止线等
 */
enum class IntersectionObjectType : uint8_t {
  /**
   * 空
   */
  NONE,
  /**
   * 停止线
   */
  STOP_LINE,
  /**
   * 斑马线
   */
  ZEBRA_CROSSING,
};

/**
 * 停止线类型
 */
enum class StopLineType : uint8_t {
  SLT_Unknown = 0,                          // 未分类
  SLT_StopLine,                             // 停止线
  SLT_YieldLine,                            // 停车让行线
  SLT_DecelerationLine,                     // 减速让行线
  SLT_VirtualStopLine,                      // 虚拟停止线
  SLT_SlowLine,                             // 慢行线
  SLT_LeftTurnWaitingAreaStopLine,          // 左转待转区停止线
  SLT_StraightWaitingAreaStopLine,          // 直行待行区停止线
  SLT_Other,                                // 其他
  SLT_TideLaneStopLine,                     // 潮汐车道停止线
  SLT_ReverseStopLine,                      // 逆向停止线
  SLT_OtherWaitingAreaStopLine              // 其他待转区停止线
};

/**
 * 路口对象信息
 */
struct IntersectionObjectInfo {
  // 是否从起点到终点
  bool is_from_start_to_end{true};
  // 是否为入口;
  bool is_entrance{true};
  // 类型
  IntersectionObjectType type{IntersectionObjectType::NONE};
  // 停止线类型(复合类型), 当type为停止线时有效;
  std::vector<StopLineType> stop_line_types;
  // 索引的NodeID
  NodeIDType reference_node_id;
  // 车道变化位置点
  Coordinate position;
};

/**
 * Link经验速度置信度类型
 */
enum class ExperienceSpeedConfidenceType : uint8_t {
  UNKNOWN       = 0,    /** 未知 */
  HIGH          = 1,    /** 高 */
  MEDIUM        = 2,    /** 中 */
  LOW           = 3,    /** 低 */
};
/**
 * Link单方向的经验速度信息
 */
struct ExperienceSpeedInfo {
  /**
   * 该方向的平均经验速度，单位：km/h
   * 0代表无效
   */
  ExperienceSpeedConfidenceType confidence{ExperienceSpeedConfidenceType::UNKNOWN};
  /**
   * 该方向的最大经验速度，单位：km/h
   * 0代表无效
   */
  uint8_t max_experience_speed{0};
  /**
   * 该方向的平均经验速度，单位：km/h
   * 0代表无效
   */
  uint8_t avg_experience_speed{0};
};
/**
 * Link经验速度信息
 */
struct LinkExperienceSpeedInfo {
  /**
   * 起点到终点方向的经验速度信息
   */
  ExperienceSpeedInfo speed_s2e;
  /**
   * 终点到起点方向的经验速度信息
   */
  ExperienceSpeedInfo speed_e2s;
};

/**
 * SD+ 和 HDAir, HD覆盖状态类型
 * 组合类型， bit位设计
 */
union LinkCoverageStatusMaskType {
  struct {
    uint16_t SD_PLUS_LINK_POSITIVE_DIRECTION : 1;             // is SD+ link in positive direction
    uint16_t SD_PLUS_LINK_NEGATIVE_DIRECTION : 1;             // is SD+ link in negative direction
    uint16_t SD_PLUS_LINK_BOTH_DIRECTION : 1;                 // is SD+ link in both direction
    uint16_t HD_AIR_LINK_POSITIVE_DIRECTION : 1;              // is HD air link in positive direction
    uint16_t HD_AIR_LINK_NEGATIVE_DIRECTION : 1;              // is HD air link in negative direction
    uint16_t HD_AIR_LINK_BOTH_DIRECTION : 1;                  // is HD air link in both direction
    uint16_t HD_LINK_POSITIVE_DIRECTION : 1;                  // is HD link in positive direction
    uint16_t HD_LINK_NEGATIVE_DIRECTION : 1;                  // is HD link in negative direction
    uint16_t HD_LINK_BOTH_DIRECTION : 1;                      // is HD link in both direction
    uint8_t reserved : 7;
  } mask;
  uint16_t value;

  explicit LinkCoverageStatusMaskType(uint16_t value) {
    this->value = value;
  }
};

/**
 * SD Plus属性数据定义
 * 这里由于之前对ToB进行了一套定义,ToC的需要格式变化较大, 重新定义了一套避免冲突;
 */
namespace sdPlus {

/**
 * Lane(车道) ID定义
 */
using LaneIDType = FeatureIDType;
/**
 * LaneGroup(车道组) ID定义
 */
using LaneGroupIDType = FeatureIDType;

/**
 * SD Plus属性数据的原始类型
 */
enum class LinkAttrPrimitiveType : uint8_t {
  LINK_ATTRIBUTE_TYPE_NONE                                = 0,                  // 无
  LINK_ATTRIBUTE_TYPE_ROAD_SPLIT_LINE_POINT               = 1,                  // 双通路道路中心分割线
  LINK_ATTRIBUTE_TYPE_ROAD_LANE_NUMBER_CHANGE             = 2,                  // 车道数变化
  LINK_ATTRIBUTE_TYPE_EXCLUSIVE_LANE_START                = 3,                  // 专用车道起点
  LINK_ATTRIBUTE_TYPE_EXCLUSIVE_LANE_END                  = 4,                  // 专用车道终点
  LINK_ATTRIBUTE_TYPE_INTERSECTION_ENTRANCE               = 5,                  // 普通路口进入点
  LINK_ATTRIBUTE_TYPE_INTERSECTION_EXIT                   = 6,                  // 普通路口退出点
  LINK_ATTRIBUTE_TYPE_EXCHANGE_AREA_ENTRANCE              = 7,                  // 交换区起点
  LINK_ATTRIBUTE_TYPE_EXCHANGE_AREA_EXIT                  = 8,                  // 交换区终点
  LINK_ATTRIBUTE_TYPE_UTURN_ENTRANCE                      = 9,                  // 调头口进入点
  LINK_ATTRIBUTE_TYPE_UTURN_EXIT                          = 10,                 // 调头口退出点
  LINK_ATTRIBUTE_TYPE_OPENING_AREA_ENTRANCE               = 11,                 // 豁口进入点
  LINK_ATTRIBUTE_TYPE_OPENING_AREA_EXIT                   = 12,                 // 豁口退出点
  LINK_ATTRIBUTE_TYPE_PEDESTRIAN_CROSSING_ENTRANCE        = 13,                 // 人行路口进入点
  LINK_ATTRIBUTE_TYPE_PEDESTRIAN_CROSSING_EXIT            = 14,                 // 人行路口退出点
  LINK_ATTRIBUTE_TYPE_LANE_MARKING_CHANGE                 = 15,                 // 标线变化点
  LINK_ATTRIBUTE_TYPE_LANE_TYPE_CHANGE                    = 16,                 // 车道类型变化点
  LINK_ATTRIBUTE_TYPE_TOLL_STATION_ENTRANCE               = 17,                 // 收费站进入点
  LINK_ATTRIBUTE_TYPE_TOLL_STATION_EXIT                   = 18,                 // 收费站退出点
  LINK_ATTRIBUTE_TYPE_WIDE_LANE_ENTRANCE                  = 19,                 // 宽车道进入点
  LINK_ATTRIBUTE_TYPE_WIDE_LANE_EXIT                      = 20,                 // 宽车道退出点
  LINK_ATTRIBUTE_TYPE_TRAFFIC_CONDITION_CHANGE            = 21,                 // 车道通行状态变化点
};


/** SD+ LINK_ATTRIBUTE_TYPE_LANE_INFORMATION_CHANGE  **/
/**
 * SD+ 车道类型定义
 */
enum class LaneType : uint8_t {
  NONE                    = 0,  // 无
  REGULAR                 = 1,  // 普通车道
  COMPOUND                = 2,  // 加减速复合车道
  LEFT_ACCELERATION       = 3,  // 左侧加速车道
  LEFT_DECELERATION       = 4,  // 左侧减速车道
  HOV                     = 5,  // HOV
  SLOW                    = 6,  // 慢车道
  SHOULDER                = 7,  // 路肩
  DRIVABLE_SHOULDER       = 8,  // 可行驶路肩
  CONTROL                 = 9,  // 管制车道
  EMERGENCY_PARKING_STRIP = 10, // 紧急停车带
  BUS                     = 11, // 公交车道
  BICYCLE                 = 12, // 非机动车道
  TIDE                    = 13, // 潮汐车道
  DIRECTION_CHANGE        = 14, // 可变车道
  PARKING_ROAD            = 15, // 停车车道
  DRIVABLE_PARKING_ROAD   = 16, // 可行驶停车道
  TRUCK                   = 17, // 货车专用道
  TIME_LIMIT_BUS          = 18, // 限时公交车道
  PASSING_BAY             = 19, // 错车道
  REVERSAL_LEFT_TURN      = 20, // 借道左转车道
  TAXI                    = 21, // 出租车车道
  TURN_LEFT_WAITING       = 22, // 转弯待转区车道
  DIRECTION_LOCKED        = 23, // 定向车道
                                 //
                                 //
                                 //
  STRAIGHT_WAITING        = 27, //
  VEHICLE_BICYCLE_MIX     = 28, // 机非混合车道
  MOTOR                   = 29, // 摩托车道
                                 //
  TOLL                    = 31, // 收费站车道
  CHECK_POINT             = 32, // 检查站车道
  DANGEROUS_ARTICLE       = 33, // 危险品专用车道
  FORBIDDEN_DRIVE         = 34, // 非行驶区域
  THOUGH_LANE_ZONE        = 35, // 借道区
  STREET_RAILWAY          = 36, // 有轨电车车道
  BUS_BAY                 = 37, // 公交港湾车道
  SPECIAL_CAR             = 38, // 特殊车辆专用车道
  PEDESTRIANS             = 39, // 人行道
  EMERGENCY               = 40, // 应急车道
  HEDGING                 = 41, // 避险车道
  BUS_LANE_WITH_TIME      = 42, // 时间段公交车道
  EMPTY                   = 43, // 空车道
  RIGHT_ACCELERATION      = 44, // 右侧加速车道
  RIGHT_DECELERATION      = 45, // 右侧减速车道
  SLOPE                   = 46, // 爬跑车道
  REVERSE_NON_VEHICLE     = 47, // 逆向非机动车道
  EDGE                    = 48, // 边缘车道
  OTHER                   = 99, // 其他车道
};

/**
 * SD+ 车道拓扑变化类型
 */
enum class LaneChangeType : uint8_t {
  LeftTurnExpandingLane   = 1,                            // 左向扩展车道
  RightTurnExpandingLane  = 2,                            // 右向扩展车道
  LeftTurnMergingLane     = 3,                            // 左向汇入车道
  RightTurnMergingLane    = 4,                            // 右向汇入车道
  BothDirectionExpandingLane  = 5,                        // 双向扩展车道
  BothDirectionMergingLane    = 6,                        // 双向汇入车道
  OtherLane                   = 7,                        // 其他
  NotApplicable               = 99,                       // 不适用
};

/**
 * SD+ 车道箭头类型定义
 */
enum class LaneArrowType : uint8_t {
  LaneArrowTypeStraightAhead,                             // 正前方直行
  LaneArrowTypeTurnRightFront30,                          // 右斜前（顺时针正北方向30°）
  LaneArrowTypeTurnRightFront60,                          // 右斜前（顺时针正北方向60°，已废弃）
  LaneArrowTypeTurnRight90,                               // 90度右转
  LaneArrowTypeTurnRightDown30,                           // 右斜下（顺时针正东方向30°，已废弃）
  LaneArrowTypeTurnRightDown60,                           // 右斜下（顺时针正东方向60°，已废弃）
  LaneArrowTypeUTurnLeft,                                 // 左掉头
  LaneArrowTypeTurnLeftDown30,                            // 左斜下（顺时针正南方向30°，已废弃）
  LaneArrowTypeTurnLeftDown60,                            // 左斜下（顺时针正南方向60°，已废弃）
  LaneArrowTypeTurnLeft90,                                // 90度左转
  LaneArrowTypeTurnLeftFront30,                           // 左斜前（顺时针正西方向30°，已废弃）
  LaneArrowTypeTurnLeftFront60,                           // 左斜前（顺时针正西方向60°）
  LaneArrowTypeUTurnRight,                                // 右掉头
  LaneArrowTypeUnknown,                                   // 待确认（已废弃）
  LaneArrowTypeBlank,                                     // 空
  LaneArrowTypeNoLeftTurn,                                // 禁止左转
  LaneArrowTypeNoUTurn,                                   // 禁止掉头
  LaneArrowTypeNoStraightThrough,                         // 禁止直行
  LaneArrowTypeNoRightTurn,                               // 禁止右转
  LaneArrowTypeNoLeftTurnAndNoUTurn,                      // 禁止左转与掉头
  LaneArrowTypeNoStraightOrRightTurn,                     // 禁止直右
  LaneArrowTypeNoStraightOrLeftTurn                       // 禁止直左

};

/**
 * SD+ 车道边类型定义
 */
enum class BorderType : uint8_t {
  LaneBorderTypeUnknown = 0,                              // 未调查
  LaneBorderDashLine,                                     // 普通虚线
  LaneBorderShortThickDashLine,                           // 短粗虚线
  LaneBorderTurnWaitingLine,                              // 待转区标线
  LaneBorderNormalSolidLine,                              // 普通实线
  LaneBorderDiversionZoneLine,                            // 导流区线
  LaneBorderParkingZoneLine,                              // 停车位标线
  LaneBorderThickSolidLine,                               // 粗实线
  LaneBorderLeftDashRightSolidLine,                       // 左虚右实(通过4K数组进行还原，问题：是否0左1右)
  LaneBorderLeftSolidRightDashLine,                       // 左实右虚(通过4K数组进行还原)
  LaneBorderDoubleSolidLine,                              // 双实线(通过4K数组进行还原：双普通实线)
  LaneBorderDoubleDashLine,                               // 双虚线(通过4K数组进行还原：双普通虚线)
  LaneBorderOtherLine,                                    // 其它线
  LaneBorderRoadEdgeLine,                                 // 道路边缘（来源4K border type的道路边缘）
  LaneBorderPhysicalBarrier,                              // 物理隔离 (来源4K border type，如下：道路设施边界、路缘石、隔离桩、护栏、墙、防护网、施工围挡、移动护栏)
  LaneBorderVirtualLine,                                  // 虚拟线
  LaneBorderNone                                          // 无
};
/**
 * SD+ 车道边颜色类型定义
 */
enum class BorderColorType : uint8_t {
  LaneBorderColorUnknown = 0,                             // 未调查
  LaneBorderColorWhite,                                   // 白色
  LaneBorderColorYellow,                                  // 黄色
  LaneBorderColorBlue,                                    // 蓝色
  LaneBorderColorOrange,                                  // 橙色
  LaneBorderColorOther,                                   // 其它
  LaneBorderColorRed,                                     // 红色
  LaneBorderColorGreen,                                   // 绿色
  LaneBorderColorLeftYellowRightWhite,                    // 左黄右白
  LaneBorderColorLeftWhiteRightYellow,                    // 左白右黄
  LaneBorderColorNone                                     // 无
};
typedef std::vector<BorderType> BorderTypeVec;
typedef std::vector<BorderColorType> BorderColorVec;

/**
 * SD+ 车道附属类型定义
 */
enum class AncillaryLineType : uint8_t {
  AncillaryLineTypeNone,                                  // 无附属线
  AncillaryLineTypeLongitudinalDeceleration,              // 纵向减速标线
  AncillaryLineTypeVariableGuidanceLane,                  // 可变导向车道标线
  AncillaryLineTypeBorrowingAreaMarking,                  // 借道区标识线
  AncillaryLineTypeBusLaneMarking,                        // 公交车专用道标识线
  AncillaryLineTypeHOVLaneMarking,                        // HOV车道标识线
  AncillaryLineTypeOther,                                 // 其它附属线
  AncillaryLineTypeUnknown,                               // 未调查
};
typedef std::vector<AncillaryLineType> AncillaryLineTypeVec;
/**
 * SD+ 车道物理分割类型
 */
enum class PhysicalDivideType : uint8_t {
  PhysicalDivideTypeNone,                                         // 无物理隔离
  PhysicalDivideTypeBarrier,                                      // 物理隔离（如：路缘石、隔离桩、护栏、墙、防护网、施工围挡、移动护栏）
  PhysicalDivideTypeVirtual,                                      // 虚拟隔离（如：虚拟分界线、虚拟分界点）
  PhysicalDivideTypeUnknown                                       // 未调查
};
typedef std::vector<PhysicalDivideType> PhysicalDivideTypeVec;

/**
 * SD+ 车道交限车辆类型
 */
enum class VehicleType : uint8_t {
  NONE,                           // 未知
  CAR,                            // 小客车
  LOCAL_TRUCK,                    // 本地货车
  NONLOCAL_TRUCK,                 // 外地货车
  AMBULANCE,                      // 急救车
  TAXI,                           // 出租车
  BUS,                            // 公交车
  WALKER,                         // 步行者
  BICYCLE,                        // 自行车
  ELECTRIC_BICYCLE,               // 电动自行车
  MOTORCYCLE,                     // 摩托车
  MULTI_PASSENGER,                // 多人乘坐车辆
  WITH_TRAILER,                   // 拖挂车
  DANGEROUS,                      // 危险品车辆
  TRANSIT,                        // 过境车辆
  MINIVAN,                        // 小型货车（电动三轮车，拖拉机，三轮汽车）
  ALL,                            // 全部车辆
  LARGE_BUS,                      // 大型客车
  LARGE_TRUCK,                    // 大型货车
  OTHER,                          // 其它机动车辆
  ELECTRIC_CAR,                   // 电动小汽车
  LIGHT_TRUCK,                    // 轻型货车
  MINI_TRUCK,                     // 微型货车
  MEDIUM_TRUCK,                   // 中型货车
  HEAVY_TRUCK,                    // 重型货车
  EXTERNAL,                       // 外部车辆
};

/**
 * @brief 车道限制类型
 */
enum class LaneRestrictionType : uint8_t {
  /**
   * 无
   */
  kNone = 0,
  /**
   * 禁止通行
   */
  kFORBIDDEN,
  /**
   * 公交专用道
   */
  kBUS,
  /**
   * 多成员车道
   */
  kHOV,
  /**
   * 潮汐车道
   */
  kTIDE,
  /**
   * 其他
   */
  kOTHER = 0xFF,
};

/**
 * @brief LaneSemantics接口定义
 * SD地图中的车道语义对象
 * 备注: SD+ 规格数据中有效
 */
struct LaneSemantics {
  /**
   * 该元素对应的ID, 通过索引动态生成;
   */
  LaneIDType id{0, 0};
  /**
   * 车道编号
   * 备注: 从左到右，从1开始
   */
  SeqNumType lane_num{0};
  /**
   * 车道宽度, 单位:米
   */
  float lane_width{0};
  /**
   * 车道通行状态
   */
  ConstructionType construction_type{ConstructionType::kNormal};
  /**
   * 车道的变化类型
   */
  LaneChangeType lane_change_type{LaneChangeType::NotApplicable};
  /**
   * 车道对应的类型列表,如公交车道
   */
  std::vector<LaneType> lane_types;
  /**
   * @brief 车道箭头类型列表,如直行+左转
   */
  std::vector<LaneArrowType> arrow_types;
  /**
   * 车道左侧边类型
   */
  BorderTypeVec left_border_types;
  /**
   * 车道右侧边类型
   */
  BorderTypeVec right_border_types;
  /**
   * 车道左侧边颜色类型
   */
  BorderColorVec left_border_colors;
  /**
   * 车道右侧边颜色类型
   */
  BorderColorVec right_border_colors;
  /**
   * 车道左侧附属线类型列表
   */
  AncillaryLineTypeVec left_ancillary_line_types;
  /**
   * 车道右侧附属线类型列表
   */
  AncillaryLineTypeVec right_ancillary_line_types;
  /**
   * 车道左侧的物理分割类型
   */
  PhysicalDivideTypeVec left_physical_divide_types;
  /**
   * 车道右侧的物理分割类型
   */
  PhysicalDivideTypeVec right_physical_divide_types;
  /**
   * 车道限行条件
   */
  LaneRestrictionType restrict_type{LaneRestrictionType::kNone};
  /**
   * 交通限制条件,车辆类型
   */
  std::vector<VehicleType> restrict_vehicle_types;
  /**
   * 交通限制条件,特殊时间类型, 如: 工作日, 非工作日;
   */
  std::vector<SpecialTimeType> special_time_types;
  /**
   * 该交限起作用的时间段信息
   * 举例:周日周一上午7点~上午8点时间段，标识为"[(h7m0)(h8m0)]*(t7t1)"
   */
  std::string restrict_time;
};

/**
 * SD+ 车道语义组定义
 */
struct LaneSemanticsGroup {
  /**
   * 该元素对应的ID, 通过索引动态生成;
   */
  LaneGroupIDType id;
  /**
   * 获取该车道组下的车道语义列表信息
   */
  std::vector<LaneSemantics> lane_semantics_list;
};

/**
 * 车道组的连接关系
 */
struct LaneSemanticsGroupTopo {
  /**
   * 前序车道组指针指针, 空代表没有;
   */
  std::shared_ptr<LaneSemanticsGroup> prev_group{nullptr};
  /**
   * 后序车道组指针指针, 空代表没有;
   */
  std::shared_ptr<LaneSemanticsGroup> next_group{nullptr};
};

/**
 * 车道语义属性点信息
 */
struct LaneSemanticsAttrPointInfo {
  /**
   * 要素ID, 瓦片内唯一
   */
  FeatureIDType id{0, 0};
  /**
   * 是否从起点到终点
   */
  bool is_from_start_to_end{true};
  /**
   * 属性原始类型
   */
  LinkAttrPrimitiveType attr_type{LinkAttrPrimitiveType::LINK_ATTRIBUTE_TYPE_NONE};
  /**
   * 该车道属性点包含的车道组拓扑信息
   */
  std::vector<LaneSemanticsGroupTopo> lane_group_topo_list;
  /**
   * 位置点
   */
  Coordinate position;
  /**
   * 对应该位置所有机动车道的位置点;
   * 从左到右依次排列;
   */
  std::vector<Coordinate> lane_border_points;
  /**
   * 对应该车道边界点集合的置信度等级
   */
  LaneAttrPointConfidenceLevelType lane_border_points_confidence_level{LaneAttrPointConfidenceLevelType::LinkAttributePointConfidenceLevelTypeNone};
};

/** SD+ LINK_ATTRIBUTE_TYPE_ROAD_LANE_NUMBER_CHANGE  **/
/**
 * 车道合并类型
 */
enum class LaneMergeType : uint8_t {
  LaneMergeTypeUnknown                    = 0,            // 未知
  LaneMergeTypeNone                       = 1,            // 无汇入
  LaneMergeTypeMergeToLeft                = 2,            // 向左汇入
  LaneMergeTypeMergeToRight               = 3,            // 向右汇入
  LaneMergeTypeBothMerge                  = 4,            // 双向汇入（车道合并）
  LaneMergeTypeLaneEnd                    = 5,            // 车道结束
};
/**
 * 车道扩展类型
 */
enum class LaneExtensionType : uint8_t {
  LaneExtensionTypeUnknown                = 0,            // 未知
  LaneExtensionTypeNone                   = 1,            // 无扩展
  LaneExtensionTypeExtendToLeft           = 2,            // 向左扩展
  LaneExtensionTypeExtendToRight          = 3,            // 向右扩展
  LaneExtensionTypeExtendToBoth           = 4,            // 双向扩展
  LaneExtensionTypeLaneStart              = 5,            // 车道开始
};
/**
 * 车道数变化位置类型
 */
enum class LaneNumChangePosType : uint8_t {
  LaneNumberChangePositionTypeUnknown         = 0,        // 未知
  LaneNumberChangePositionTypeLinkStart       = 1,        // 初始点
  LaneNumberChangePositionTypeLinkMiddle      = 2,        // 路中段
  LaneNumberChangePositionTypeLinkStartWithLaneNumberChange   = 3,    // 初始点（含车道数变化）
  LaneNumberChangePositionTypeLinkMiddleWithExchangeArea      = 4,    // 路中段（交换区）
};
/**
 * 车道变化类型
 */
enum class LaneMarkingChangeType : uint8_t {
  LaneMarkingChangeTypeUnknown            = 0,            // 未知
  LaneMarkingChangeTypeSplitOrMerge       = 1,            // 分离或合并
  LaneMarkingChangeTypeAppearOrDisappear  = 2,            // 出现或消失
};
/**
 * 车道数量属性点信息
 */
struct LaneNumAttrPointInfo {
  /**
   * 要素ID, 瓦片内唯一
   */
  FeatureIDType id{0, 0};
  /**
   * 是否从起点到终点
   */
  bool is_from_start_to_end{true};
  /**
   * 属性原始类型
   */
  LinkAttrPrimitiveType attr_type{LinkAttrPrimitiveType::LINK_ATTRIBUTE_TYPE_NONE};
  /**
   * 车道变化位置类型
   */
  LaneNumChangePosType pos_type{LaneNumChangePosType::LaneNumberChangePositionTypeUnknown};
  /**
   * 车道变化位置点
   */
  Coordinate position;
  /**
   * 对应该位置所有机动车道的位置点;
   * 从左到右依次排列;
   */
  std::vector<Coordinate> lane_border_points;
  /**
   * 对应该车道边界点集合的置信度等级
   */
  LaneAttrPointConfidenceLevelType lane_border_points_confidence_level{LaneAttrPointConfidenceLevelType::LinkAttributePointConfidenceLevelTypeNone};
  /**
   * 合并类型
   */
  std::vector<LaneMergeType> merge_types;
  /**
   * 扩展类型
   */
  std::vector<LaneExtensionType> extension_types;
  /**
   * 车道变化类型？？
   */
  LaneMarkingChangeType marking_change_type{LaneMarkingChangeType::LaneMarkingChangeTypeUnknown};
  /**
   * 该属性点总车道数量
   */
  SeqNumType total_lane_num{0};
  /**
   * 该属性点左侧非机动车数量
   */
  SeqNumType left_non_motorized_lane_number{0};
  /**
   * 该属性点右侧非机动车数量
   */
  SeqNumType right_non_motorized_lane_number{0};
  /**
   * 该属性前序车道总数
   */
  SeqNumType prev_total_lane_num{0};
  /**
   * 该属性点前序左侧非机动车数量
   */
  SeqNumType prev_left_non_motorized_lane_number{0};
  /**
   * 该属性点前序右侧非机动车数量
   */
  SeqNumType prev_right_non_motorized_lane_number{0};
};

/** SD+ LINK_ATTRIBUTE_TYPE_INTERSECTION_ENTRANCE_EXIT  **/
/**
 * 路口出入点属性点信息
 */
struct IntersectionAttrPointInfo {
  /**
   * 要素ID, 瓦片内唯一
   */
  FeatureIDType id{0, 0};
  /**
   * 是否从起点到终点
   */
  bool is_from_start_to_end{true};
  /**
   * 属性原始类型
   */
  LinkAttrPrimitiveType attr_type{LinkAttrPrimitiveType::LINK_ATTRIBUTE_TYPE_NONE};
  /**
   * 是否有入口
   */
  bool is_entrance{true};
  /**
   * 路口对象类型
   */
  IntersectionObjectType obj_type{IntersectionObjectType::NONE};
  /**
   * 停止线类型(复合类型), 当obj_type_为停止线时有效;
   */
  std::vector<StopLineType> stop_line_types;
  /**
   * 该车道属性点包含的车道组拓扑信息
   */
  std::vector<LaneSemanticsGroupTopo> lane_semantics_groups;
  /**
   * 位置点
   */
  Coordinate position;
  /**
   * 对应该位置所有机动车道的位置点;
   * 从左到右依次排列;
   */
  std::vector<Coordinate> lane_border_points;
  /**
   * 对应该车道边界点集合的置信度等级
   */
  LaneAttrPointConfidenceLevelType lane_border_points_confidence_level{LaneAttrPointConfidenceLevelType::LinkAttributePointConfidenceLevelTypeNone};
};

/** SD+ LINK_ATTRIBUTE_TYPE_ROAD_MARKING_INFORMATION  **/
/**
 * 道路标记类型
 */
enum class RoadMarkType : uint8_t {
  RoadMarkingTypeNone                                    = 0,            // 空
  RoadMarkingTypeCrosswalk                               = 1,            // 人行横道
  RoadMarkingTypeGridLine                                = 2,            // 网状线
  RoadMarkingTypeSecurityIslandArea                      = 3,            // 安全岛
  RoadMarkingTypeStopLineTypeUnknown                     = 100,          // 未分类
  RoadMarkingTypeStopLineTypeStopLine                    = 101,          // 停止线
  RoadMarkingTypeStopLineTypeYieldLine                   = 102,          // 停车让行线
  RoadMarkingTypeStopLineTypeDecelerationLine            = 103,          // 减速让行线
  RoadMarkingTypeStopLineTypeVirtualStopLine             = 104,          // 虚拟停止线
  RoadMarkingTypeStopLineTypeSlowLine                    = 105,          // 慢行线
  RoadMarkingTypeStopLineTypeLeftTurnWaitingAreaStopLine = 106,          // 左转待转区停止线
  RoadMarkingTypeStopLineTypeStraightWaitingAreaStopLine = 107,          // 直行待行区停止线
  RoadMarkingTypeStopLineTypeOther                       = 108,          // 其他
  RoadMarkingTypeStopLineTypeTideLaneStopLine            = 109,          // 潮汐车道停止线
  RoadMarkingTypeStopLineTypeReverseStopLine             = 110,          // 逆向停止线
  RoadMarkingTypeStopLineTypeOtherWaitingAreaStopLine    = 111,          // 其他待转区停止线
  RoadMarkingTypeStopLineTypeRightTurnWaitingAreaStopLine = 112,          // 右转待转区停止线
  RoadMarkingTypeStopLineUTurnWaitingAreaStopLine        = 113,          // 掉头待转区停止线
  RoadMarkingTypeSafetyIsland                            = 114,          // 安全岛
  RoadMarkingTypeArrow                                   = 115,          // 箭头
  RoadMarkingTypeSafetyBarrier                           = 201,          // 护栏
  RoadMarkingTypeCurbstone                               = 202,          // 路缘石
  RoadMarkingTypeGreenBelt                               = 203,          // 绿化带
  RoadMarkingTypeReservedOne                             = 204,          // 预留1
  RoadMarkingTypeReservedTwo                             = 205,          // 预留2
  RoadMarkingTypeNoIsolation                             = 206          // 无隔离
};

/**
 * 道路标记属性点信息
 */
struct RoadMarkAttrPointInfo {
  /**
   * 要素ID, 瓦片内唯一
   */
  FeatureIDType id{0, 0};
  /**
   * 标记类型
   */
  std::vector<RoadMarkType> road_mark_types;
  /**
   * 总车道数量
   */
  SeqNumType lane_num{0};
  /**
   * 该对象关联的车道编号列表
   * 备注:从左到右, 从1开始;
   */
  std::vector<SeqNumType> lane_nums;
  /**
   * 该对象关联的车道类型列表
   * 备注:第0个元素代表左一车道(机动车道), 然后依此类推;
   */
  std::vector<std::vector<LaneArrowType>> lane_arrows;
  /**
   * 关联的方向信息
   */
  LinkDirection link_direction;
  /**
   * 该对象是否位于道路左侧
   */
  bool is_on_left_side{true};
  /**
   * 作用开始位置
   * 沿道路行驶方向，除10000表示[0,1]，例如30%~80%的限制范围位置区间，开始位置记为3000
   */
  uint16_t range_start{0};
  /**
   * 作用结束位置
   * 沿道路行驶方向，除10000表示[0,1]，例如30%~80%的限制范围位置区间，结束位置记为8000
   */
  uint16_t range_end{0};
  /**
   * 车道变化位置类型
   */
  LaneNumChangePosType pos_type{LaneNumChangePosType::LaneNumberChangePositionTypeUnknown};
  /**
   * 位置点
   */
  Coordinate position;
};

} // namespace sdPlus

/**
 * Link扩展属性
 */
struct LinkExtAttribute {
  /**
   * 车道数属性点信息列表
   */
  std::vector<LaneNumChangePointInfo> lane_change_point_info_list;
  /**
   * 路口出入点属性信息列表
   */
  std::vector<IntersectionObjectInfo> intersection_info_list;
  /**
   * 双通路分割线信息列表
   */
  std::vector<RoadSplitLineInfo> route_split_line_info_list;
  /**
   * Link数据历史经验速度值;
   */
  LinkExperienceSpeedInfo experience_speed_info;
  /**
   * Link数据覆盖状态信息;
   * 组合类型
   */
  LinkCoverageStatusMaskType data_coverage_status{0};
  /**
   * 车道数属性点信息列表
   */
  std::vector<sdPlus::LaneNumAttrPointInfo> lane_num_attr_point_info_list;
  /**
   * 车道语义属性点信息列表
   */
  std::vector<sdPlus::LaneSemanticsAttrPointInfo> lane_semantics_attr_point_info_list;
  /**
   * 路口出入点属性信息列表
   */
  std::vector<sdPlus::IntersectionAttrPointInfo> intersection_attr_point_info_list;
  /**
   * 道路标识属性点信息列表
   */
  std::vector<sdPlus::RoadMarkAttrPointInfo> road_mark_attr_point_info_list;
};

}  // namespace api
}  // namespace nerd

#endif  // NERDAPI_INCLUDE_DTO_IFACE_LINK_ATTRIBUTE_H_
