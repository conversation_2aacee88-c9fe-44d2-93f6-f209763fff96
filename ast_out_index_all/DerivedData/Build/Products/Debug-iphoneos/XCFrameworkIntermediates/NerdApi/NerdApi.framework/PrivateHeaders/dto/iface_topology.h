/*
 * Copyright (c) 2021 Tencent Inc. All rights reserved.
 * @version: 1.0
 * @Author: wallstwang
 * @Date: 2021-03-22 09:31:39
 * @LastEditors: wallstwang
 * @LastEditTime: 2021-03-22 09:47:02
 */

#pragma once

#include <vector>

namespace nerd {
namespace api {

/**
 * @brief 拓扑基类
 * N: 节点类型
 * T: 拓扑相关元素类型，如boundary、lane、lane_group
 * ID: 元素id
 */
template <typename N, typename T, typename ID>
class ITopology {
 public:
  /**
   * @brief 析构函数
   */
  virtual ~ITopology() = default;

  /**
   * @brief 获取起始节点
   * @return 起始节点
   */
  virtual N GetStartNode() const = 0;

  /**
   * @brief 获取终点节点
   * @return 终点节点
   */
  virtual N GetEndNode() const = 0;

  /**
   * @brief 获取后继元素集合
   * @return 后继元素集合
   */
  virtual std::vector<T> GetNext() const = 0;

  /**
   * @brief 获取前驱元素集合
   * @return 前驱元素集合
   */
  virtual std::vector<T> GetPrevious() const = 0;

  /**
   * @brief 获取元素ID
   * @return 元素ID
   */
  virtual ID GetID() const = 0;

  /**
   * 获取长度
   * @return 长度，单位: 米
   */
  virtual double GetLength() const = 0;
};

}  // namespace api
}  // namespace nerd
