//
// Copyright 2024 Tencent Inc. All rights reserved.
// Created by learnjiang on 2024/4/3.
//

#pragma once

#include "common_location.h"

namespace nerd {
namespace api {

/**
 * Nerd全局事件监听
 * 用于给客户端回调交互, 比如获取当前位置
 */
class NerdGlobalEventListener {
 public:
  virtual ~NerdGlobalEventListener() = default;
  /**
   * 获取保留数据范围的中心点，一般为定位点或者屏幕中心点
   * @return
   */
  virtual mapbase::GeoCoordinate OnPreservedDataCenterPos() = 0;
};

}  // namespace api
}  // namespace nerd
