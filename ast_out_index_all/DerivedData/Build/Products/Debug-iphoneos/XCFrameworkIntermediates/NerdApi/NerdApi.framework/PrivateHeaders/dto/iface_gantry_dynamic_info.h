//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/20.
//

#pragma once

#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 一个龙门架电子牌单屏内容
 * 说明:电子牌是滚动屏幕显示, 该信息描述一页内容
 */
struct BoardPartialContent {
  /**
   * 显示内容的组合类型
   * 0: 纯文字; 1: 纯图片; 2: 组合类型一
   */
  uint8_t content_type{0};
  /**
   * 文字类型：0（字数0-4),1:(字数5-9),2:(字数>9)
   */
  uint8_t text_place_type{0};
  /**
   * 文字内容列表
   */
  std::vector<std::string> text_list;
  /**
   * 绘制文字用的颜色列表，RGBA形式
   */
  std::vector<uint32_t> color_list;
  /**
   * 小资源ID 列表
   */
  std::vector<PackedIDType> obj_id_list;
  /**
   * 当前屏内容显示多长时间
   * 单位秒, 0表示一直显示
   */
  uint32_t display_time_s{0};
};

/**
 * @brief 一个龙门架电子牌的完整内容
 */
struct BoardCompleteContent {
  /**
   * 该电子牌的Id+Version
   */
  PackedIDType packed_id;
  /**
   * 该电子牌内容的有效性; true:完整内容,false:构建的空内容;
   */
  bool status{true};
  /**
   * 该电子牌的数据有效期,过期后需要外部主动刷新;
   * 单位秒, 默认值120秒
   */
  uint32_t interval_s{120};
  /**
   * 数据更新的时间
   */
  uint32_t timestamp{0};
  /**
   * 滚屏内容列表
   */
  std::vector<BoardPartialContent> content_list;
};

/**
 * 龙门架电子牌中的模型资源数据
 */
struct BoardResourceData {
  /**
   * 该模型资源的Id+Version
   */
  PackedIDType packed_id;
  /**
   * 该资源的有效性; true:完整内容,false:构建的空内容;
   */
  bool status{true};
  /**
   * data数据内容类型
   * 0:png
   */
  uint8_t type{0};
  /**
   * 该模型资源的内容
   * 注意: 自定义格式;
   */
  std::vector<int8_t> data;
};

}  // namespace api
}  // namespace nerd