// Copyright (c) 2021 Tencent Inc. All rights reserved.
// Created by wallst on 2021/3/17.
//

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dto/types.h"
#include "nerd_export.h"

namespace nerd {
namespace api {

enum class LaneGroupDirection : uint8_t;
union LaneDirectionMask;

class IAccessRestrict;
typedef std::shared_ptr<IAccessRestrict> IAccessRestrictPtr;
typedef std::shared_ptr<const IAccessRestrict> IAccessRestrictConstPtr;

/**
 * @brief 车道限行条件
 */
enum class LaneConditionType : uint8_t {
  /**
   * 无
   */
  kNone = 0,
  /**
   * 禁止通行
   */
  kFORBIDDEN,
  /**
   * 公交专用道
   */
  kBUS,
  /**
   * 多成员车道
   */
  kHOV,
  /**
   * 潮汐车道
   */
  kTIDE,
  /**
   * 直行待转区
   */
  kSTRAIGHT_WAIT,
  /**
   * 左转待转区
   */
  kLEFT_WAIT,
  /**
   * 其他
   */
  kOTHER,
};

/**
 *@brief 车辆所属行政区划类型
 */
enum class VehicleRegion : uint8_t {
  /**
   * 无
   */
  kNONE = 0,
  /**
   * 非本地车辆
   */
  kNOT_LOCAL = 0,
  /**
   * 本地车辆
   */
  kLOCAL,
  /**
   * 本地和外地车辆
   */
  kBOTH
};

/**
 * @brief 限行条件
 */
class NERD_EXPORT IAccessRestrict {
 public:
  virtual ~IAccessRestrict() = default;

  /**
   * @brief 获取该交限时间类型限制
   * @return 时间限制
   */
  virtual std::vector<SpecialTimeType> GetSpecialTimeType() const = 0;
  /**
   * @brief 该交限起作用的时间段信息
   * @return 时间段描述字符
   */
  virtual const std::string& GetRestrictTimePeriod() const = 0;

  /**
   * @brief 最高速度限制
   * @return 单位:千米/小时
   */
  virtual SpeedType GetMaxSpeedKmph() const = 0;

  /**
   * @brief 最低速度限制
   * @return 单位:千米/小时
   */
  virtual SpeedType GetMinSpeedKmph() const = 0;

  /**
   * @brief 条件限速值
   * @return 单位:千米/小时
   */
  virtual ConditionSpeedType GetConditionSpeed() const = 0;

  /**
   * @brief 限制方向
   * @return 顺方向/逆方向
   */
  virtual LaneGroupDirection GetDirection() const = 0;

  /**
   * @brief 禁止车道的行驶方向
   * @return 行驶方向
   */
  virtual LaneDirectionMask GetForbiddenLaneDirection() const = 0;

  /**
   * @brief 限制车辆类型
   * @return 如果没有则为空
   */
  virtual std::vector<VehicleType> GetLimitVehicleType() const = 0;

  /**
   * @brief 车道类型条件
   * @return 车道类型条件
   */
  virtual LaneConditionType GetLaneConditionType() const = 0;

  /**
   * @brief 车辆限制信息
   * @return 车辆限制
   */
  virtual VehicleRegion GetVehicleRegion() const = 0;

  /**
   * @brief 沿道路行驶方向，除10000表示[0,1]，例如30%~80%的限制范围位置区间，开始位置记为3000
   * @return 作用开始位置
   */
  virtual uint16_t GetRangeStart() const = 0;

  /**
   * @brief 沿道路行驶方向，除10000表示[0,1]，例如30%~80%的限制范围位置区间，结束位置记为8000
   * @return 作用结束位置
   */
  virtual uint16_t GetRangeEnd() const = 0;

  /**
   * @brief 获取ID
   * @return 交规ID
   */
  virtual AccessRestrictIDType GetID() const = 0;
};

}  // namespace api
}  // namespace nerd
