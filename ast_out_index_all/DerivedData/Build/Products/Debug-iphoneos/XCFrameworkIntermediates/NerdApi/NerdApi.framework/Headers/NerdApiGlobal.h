//
//  NerdApiGlobal.h
//  NerdApi
//
//  Created by morgansun on 2021/12/31.
//

#ifndef NerdApiGlobal_h
#define NerdApiGlobal_h

#import <Foundation/Foundation.h>
#import <MapBaseNew/MapBaseHttpInterface.h>
#import <MapBaseNew/MapBaseCommonDefine.h>
#import <PLog/PLog-umbrella.h>

NS_ASSUME_NONNULL_BEGIN
CF_EXTERN_C_BEGIN

/**
 * 数据模式
 */
typedef NS_ENUM(NSInteger, NerdApiDataPreference) {
  /**
   * 纯离线模式：只读取本地的离线数据
   */
  NerdApiDataPreferenceOfflineOnly = 0,
  /**
   * 纯在线模式：只读取在线数据（含在线缓存）
   */
  NerdApiDataPreferenceOnlineOnly = 1,
};

/**
 * 语言类型
 */
typedef NS_ENUM(NSInteger, NerdApiLanguageType) {
  /**
   * 默认语言
   */
  NerdApiLanguageTypeDefault = 0,
  /**
   * 简体中文
   */
  NerdApiLanguageTypeSimplifiedChinese = 1,
  /**
   * 繁体中文
   */
  NerdApiLanguageTypeTraditionalChinese = 2,
  /**
   * 英语
   */
  NerdApiLanguageTypeEnglish = 3,
  /**
   * 葡萄牙语
   */
  NerdApiLanguageTypePortuguese = 4,
};

/**
 * 当前网络状态
 */
typedef NS_ENUM(NSInteger, NerdApiNetworkStatus) {
    NerdApiNetworkStatusUnknown              = -1,
    NerdApiNetworkStatusNotReachable         = 0,
    NerdApiNetworkStatusReachableViaWiFi     = 1,
    NerdApiNetworkStatusReachableViaWWAN     = 2,
};

/**
 * 当前数据网络类型
 */
typedef NS_ENUM(NSInteger, NerdApiNetworkWWANType) {
    NerdApiNetworkWWANTypeNotWWAN           = 0,
    NerdApiNetworkWWANTypeUnknown           = 1,
    NerdApiNetworkWWANType2G                = 2,
    NerdApiNetworkWWANType3G                = 3,
    NerdApiNetworkWWANType4G                = 4,
    NerdApiNetworkWWANType5G                = 5,
};

/**
 表明当前网络的情况，是弱网络或者网络情况良好
 - NerdApiNetworkSituationWeakNetwork: 当前是弱网络，延迟和丢包率较高
 - NerdApiNetworkSituationGreatNetork: 当前网络较好，可以适当增加并发数
 */
typedef NS_ENUM(NSInteger, NerdApiNetworkSituation) {
    NerdApiNetworkSituationWeakNetwork = 0,
    NerdApiNetworkSituationGreatNetwork = 1
};

@interface NerdApiGlobalConfig : NSObject
/**
 * 数据根目录，目录格式如下：
 *  db_root_path : 根目录
 *     |_____ cache : 在线数据目录
 *            |____  : bmd_x.nds       SD显示数据
 *            |____  : objects3d_x.nds 模型数据(楼块/小树等)
 *            |____  : ----_x.nds      其他数据
 *    |_____ offline: 离线数据目录(需要外部创建)
 *            |____ XXXXXXX : XXX城市离线数据
 *            |____ YYYYYYY : YYY城市离线数据
 * 配置值为: your_path/data
 */
@property (nonatomic, copy) NSString* dataRootPath;
/**
 * HttpInterface 实例，用于在线数据下载
 */
@property (nonatomic, strong) id<TMapBaseHttpInterface> httpInterface;
/**
 * 数据请求方式：纯在线/纯离线
 * 备注: 已经废弃, 引擎支持加载数据的Api时设置数据加载方式;
 */
@property (nonatomic, assign) NerdApiDataPreference preference;
/**
 * 在线缓存磁盘文件(对应目录cache)大小限制，超过限制时会清除缓存，单位：MB
 * 备注: 1.启动时核查; 2.内部限制范围[200, 5*1024], 用户可以在该范围内调整;
 * 默认值: 600M
 */
@property (nonatomic, assign) NSInteger maxCacheSizeMB;
/**
 * 下发瓦片服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
 */
@property (nonatomic, copy) NSString* tileServerUrl;
/**
 * 模型服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
 */
@property (nonatomic, copy) NSString* modelServerUrl;
/**
 * 路况服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
 */
@property (nonatomic, copy) NSString* trafficServerUrl;
/**
 * 封路服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
 */
@property (nonatomic, copy) NSString* closedRoadServerUrl;
/**
 * 室内图服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
 */
@property (nonatomic, copy) NSString* indoorServerUrl;
/**
 * 在线特征数据拉取服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境; 一般用于测试场景;
 */
@property (nonatomic, copy) NSString* onlineFeatureUrl;
/**
 * 三维地形服务url
 * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
 */
@property (nonatomic, copy) NSString* dtmServerUrl;
/**
 * 百变地图服务url
 */
@property (nonatomic, copy) NSString* agileMapServerUrl;

/**
 * 业务方百变地图服务url
 */
@property (nonatomic, copy) NSString* customAgileMapServerUrl;

/**
 * 客户渠道号
 * 注意: 客户端必须注入,若不清楚渠道联系项目;
 */
@property (nonatomic, copy) NSString* channel;
/**
 * 数据语言类型
 */
@property (nonatomic, assign) NerdApiLanguageType languageType;
/**
 * 是否采用Nerd全局配置开关控制世界图加载; 说明:
 * 若设置true则代表统一控制, 即上层底图不论创建多少个MapView它们均使用Nerd的GlobalConfig世界图开关来控制;
 * 若设置false则代表分别控制, 即上层底图可以创建多个MapView,但它们的世界图开关不共享, 各自独立控制, 开关参考如下:
 *     ApiConfig::support_world_map_for_country 和 ApiConfig::support_world_map_for_city;
 * 默认值: true; 若设置为false, GlobalConfig的开关将失效;
 */
@property (nonatomic, assign) BOOL supportWorldMapByNerdGlobalConfig;
/**
 * 采用Nerd全局配置开关控制模式下, 是否支持全国级别世界图
 * 说明: 1> 默认不支持; 2> 数据级别3/5/7;
*/
@property (nonatomic, assign) BOOL supportWorldMapForCountry;
/**
 * 采用Nerd全局配置开关控制模式下, 是否支持城市级别世界图
 * 说明: 1> 默认不支持; 2> 数据级别10/13/15等;
*/
@property (nonatomic, assign) BOOL supportWorldMapForCity;
/**
 * 离线调试模式, 正常应用时需关闭
 */
@property (nonatomic, assign) BOOL offlineDebug;
/**
 * 数据更新模式, false时全量更新，true时差分更新
 * 备注: 目前支持BMD/OBJ/LANE/ROUTE/LANE4K几种数据;
 * 支持动态开关, 接口:
 */
@property (nonatomic, assign) BOOL supportDiffUpdate;
/**
 * 是否强制使用客户端设置的缓存大小
 */
@property (nonatomic, assign) BOOL useCacheSizeSettingForce;
/**
 * 是否使用nerd在线缓存清理新策略，默认不开启
 */
@property (nonatomic, assign) BOOL useNewCacheClean;

/**
 * 是否支持L3建筑物
 */
@property (nonatomic, assign) BOOL supportL3Building;

@end

/**
 * 获取版本号
 * @return 版本号字符串
 */
FOUNDATION_EXPORT NSString *NerdApiVersionName;
/**
 * 获取版本详细信息，包括版本信息以及仓库hash
 * @return JSON格式版本信息
 */
FOUNDATION_EXPORT NSString *NerdApiVersionDetail;

@interface NerdApiMapCachePath : NSObject
/**
 * 数据路径
 */
@property (nonatomic, copy) NSString* dataPath;
/**
 * 日志路径
 */
@property (nonatomic, copy) NSString* logPath;

@end

/**
 * 清理本地缓存参数
 */
@interface NerdApiClearLocalCacheParam : NSObject
/**
 * 超时回调发送时间, -1表示不发送
 */
@property (nonatomic, assign) int32_t timeoutMs;
/**
 * 是否清理文件缓存
 */
@property (nonatomic, assign) BOOL clearFileCache;
/**
 * 是否清理内存缓存
 */
@property (nonatomic, assign) BOOL clearMemoryCache;

@end

/**
 * 清除本地缓存回调
 * @param jobId  任务id
 * @param status 0: 清理成功, 1: 清理失败, 2: 清理中时重复调用, 3: 清理超时
 */
typedef void (^NerdApiClearLocalCacheCallbackBlock)(int job_id, int status);

/**
 * 初始化数据引擎，全局初始化，只需要执行一次即可
 * @param config 配置对象
 */
FOUNDATION_EXPORT void NerdApiInitGlobalConfig(const NerdApiGlobalConfig *config);

/**
 * 暂停NerdService服务，停止循环检查任务，停止接受数据请求，清空Nerd数据缓存
 */
FOUNDATION_EXPORT void NerdApiPauseNerdService(void);

/**
 * 唤醒NerdService服务，重新注册循环检查任务，接受数据请求
 */
FOUNDATION_EXPORT void NerdApiResumeNerdService(void);

/**
 * 初始化日志
 * @param log_path 日志输出路径
 * @param log_level 日志输出等级，参见 PLog/StructsAndDefine.h 中的定义
 * @param log_dest 日志输出方式，参见 PLog/StructsAndDefine.h 中的定义
 */
FOUNDATION_EXPORT void NerdApiInitLogger(NSString* log_path, PLLogLevel log_level, PLAppenderType log_dest);

/**
 * 设置语言
 * @param language 语言类型，参见dto/types.h中的定义
 * @return 返回成功或失败
 */
FOUNDATION_EXPORT BOOL NerdApiSetGlobalLanguage(int language_type);

/**
 * 删除指定缓存路径下的在线数据缓存，该接口用来多次crash后手图恢复默认设置，将nerd的在线缓存删除，
 * 配置文件都用安装包自带的初始版本，防止app无法启动，不能进行配置更新及热更
 * @param dataPaths nerd数据缓存路径集合，包括nerd在线数据缓存、日志路径缓存
 */
FOUNDATION_EXPORT void NerdApiClearAllCacheBeforeInit(const NerdApiMapCachePath *dataPaths);

/**
  * 异步清理本地缓存, 可以在运行过程中执行
  * @param param 清理本地缓存参数
  * @param block 清理本地缓存事件监听
  * @return 异步任务id, 任务创建失败返回-1
  */
FOUNDATION_EXPORT int32_t NerdApiAsyncClearLocalCache(const NerdApiClearLocalCacheParam *param, NerdApiClearLocalCacheCallbackBlock block);
/**
   * 获取高精数据的pv(product version)版本信息;
   * 备注: 高精数据编译规格的一种定义, 在数据引擎和诱导服务/Tile服务之间生效, 非FV和BBV;
   * 注意: 初始化完Nerd调用, 改值不会在应用使用过程中动态变化; 因此每次启动获取一次即可；
   * @return <=0 无效, 其他有效;
 */
FOUNDATION_EXPORT int32_t NerdApiGetProductVersionForHD(void);

/**
 * @brief 设置是否支持数据差分更新模式
 * @param is_support_diff_update 是否支持差分更新, true:支持; false:不支持;
 * 内部默认值支持, 启动过程也可以通过初始化配置设置;
 */
FOUNDATION_EXPORT void NerdApiSetDataDiffUpdateEnable(BOOL is_support_diff_update);

/**
 * 网络状态变化的时候，将网络状态同步到Nerd中
 * @param networkStatus 当前网络状态
 * @param wwanType 当前数据网络类型
 * @param networkSituation 是否为弱网环境
 */
FOUNDATION_EXPORT void NerdApiNetworkStatusChanged(NerdApiNetworkStatus networkStatus,
                                                   NerdApiNetworkWWANType wwanType,
                                                   NerdApiNetworkSituation networkSituation);
/**
 * 设置事件回调监听器, 用于监听内部的一些错误状态;
 */
typedef void (^NerdApiEventCallbackBlock)(NSArray<NSString *> *debugInfos);
FOUNDATION_EXPORT void NerdApiSetEventCallback(NerdApiEventCallbackBlock callback);
/**
 * 设置数据引擎全局事件回调监听器, 用于nerd与端上进行信息交互，例如保留在线数据中心点;
 */
typedef TMapBaseGeoCoordinate* _Nullable (^NerdApiGlobalEventCallbackBlock)(void);
FOUNDATION_EXPORT void NerdApiSetGlobalEventCallback(NerdApiGlobalEventCallbackBlock callback);

CF_EXTERN_C_END
NS_ASSUME_NONNULL_END



#endif /* NerdApiGlobal_h */
