// Copyright 2021 Tencent Copyright 2020 Tencent. All rights reserved.
//
// Created by morgan<PERSON>(morgansun) on 2020/5/9.
//

#pragma once

#include <cstdint>
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * 空请求
 */
class EmptyRequest {};
/**
 * 空返回
 */
class EmptyResponse {};

/**
 * 异步回调基类定义
 * @tparam Req 请求参数
 * @tparam Rsp 返回参数
 */
template <typename Req, typename Rsp>
class AsyncCallback {
 public:
  /**
   * 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  virtual void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<Req> req, std::unique_ptr<Rsp> rsp) {}
  /**
   * 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  virtual void OnCanceled(JobId job_id, bool success, const RetMessage &ret) {}
  /**
   * 析构函数
   */
  virtual ~AsyncCallback() = default;
};

}  // namespace api
}  // namespace nerd
