// Copyright 2021 Tencent Copyright 2021 Tencent Inc.  All rights reserved.
//
// Author: ni<PERSON><PERSON><PERSON>@tencent.com (Emperor Penguin)
// 数据引擎NERD之NERD数据读取模块

#pragma once

#include <string>
#include <vector>
#include "dto/iface_guidance.h"
#include "dto/iface_lanegroup.h"
#include "dto/iface_restrict.h"
#include "dto/iface_topology.h"
#include "dto/iface_link_attribute.h"
#include "dto/types.h"
#include "linkposition.h"

namespace nerd {
namespace api {

typedef uint8_t FuncClassType;

/**
 * 道路数据中室内楼层信息
 */
struct BuildingFloorInfo {
  /** 楼层号 */
  int16_t floor_seq{0};
  /** 建筑物名称 */
  std::string building_name;
  /** 楼层名称 */
  std::string floor_name;

 public:
  void Print() {}
  void Reset() {
    floor_seq = 0;
    building_name = "";
    floor_name = "";
  }
};

/**
 * @brief 铺设状态
 */
enum class RoadCondType : uint8_t {
  /**
   * 未铺设（不平）
   */
  kUnpavedUnflat = 0,
  /**
   * 已铺设
   */
  kPaved = 1,
  /**
   * 未铺设（平）
   */
  kUnpaved,
  /**
   * 未调查
   */
  kNotSurvey,
  /**
   * 已铺设（严重损坏）
   */
  kPavedDamaged
};

/**
 * 道路通行能力
 */
enum class RoadCompacity : uint8_t {
  /**
   * 普通
   */
  kNormal = 0,
  /**
   * 难走1
   */
  kDiffcult1,
  /**
   * 难走2
   */
  kDiffcult2,
  /**
   * 禁走
   */
  kForbidden,
  /**
   * 禁走&难走
   */
  kForbiddenAndDifficult,
  /**
   * 预留
   */
  kReserve
};

/**
 * 道路收费属性
 * */
enum class LinkTollType : uint8_t {
  /**
   * 未调查
   * */
  kNotSurvey = 0,
  /**
   * 收费
   * */
  kCharge = 1,
  /**
   * 免费
   * */
  kFree = 2,
  /**
   * 部分路段免费
   * */
  kFreeSection = 3,
};

/**
 *  道路速度限制来源
 * */
enum class LinkSpeedSource : uint8_t {
  /**
   * 空
   * */
  KEmpty = 0,
  /**
   * 标牌
   */
  KBoard = 1,
  /**
   * 理论
   */
  KTheory = 2,
  /**
   * 人工
   */
  KManual = 3,
};

/**
 * @brief link的数据源
 */
enum class LinkDataSource : uint8_t {
  /**
   * 无数据
   */
  kNONE = 0,
  /**
   * 高精自采
   */
  kHD_SELF_COLLECTION = 1,
  /**
   * 高精众包
   */
  kHD_CROWDSOURCING = 2,
  /**
   * 低精众包
   */
  kSD_CROWDSOURCING = 3,
  /**
   * 情报
   */
  kINTELLIGENCE = 4,
  /**
   * 影像
   */
  kIMAGE = 5,
  /**
   * 高分影像
   */
  kHIGH_RESOLUTION_IMSGE
};

/**
 * @brief link属性
 */
struct LinkAttribute {
  /**
   * 道路名称
   */
  NameWithLanguage name;
  /**
   * 道路编号，比如 G7
   */
  std::string road_number;
  /**
   * link等级
   */
  RoadClassType road_class_type{0x7F};
  /**
   * link属性
   */
  RoadKindTypeVec road_kind_types;
  /**
   * 功能类别
   */
  FuncClassType func_class{0};
  /**
   * 在建状态
   */
  ConstructionType is_const{ConstructionType::kNormal};
  /**
   * 铺设状态
   */
  RoadCondType road_cond{RoadCondType::kUnpavedUnflat};
  /**
   * link方向
   */
  LinkDirection direction{LinkDirection::kUNKNOWN};
  /**
   * 速度等级
   */
  SpeedClassType speed_class{0};
  /**
   * 行政区代码
   */
  AdminCode admin_code{0};
  /**
   * 总车道数
   */
  uint8_t lane_num_sum{0};
  /**
   * S->E方向的车道数
   */
  uint8_t lane_num_s2e{0};
  /**
   * E->S方向的车道数
   */
  uint8_t lane_num_e2s{0};
  /**
   * 起点到终点方向的限速，单位：km/h
   */
  uint8_t speed_limit_s2e{0};
  /**
   * 终点到起点方向的限速，单位：km/h
   */
  uint8_t speed_limit_e2s{0};
  /**
   * 车辆类型
   */
  std::vector<VehicleType> vehicle_types;
  /**
   * 道路通行能力
   */
  RoadCompacity compacity{RoadCompacity::kNormal};
  /**
   * 特殊交通类型（为了控制进入道路车流量的近似环行的道路）: Unimap规格无数据, 暂时无赋值处理, 维护一段时间后删除;
   */
  bool is_special{false};
  /**
   * 是否可穿行
   */
  bool is_through{false};
  /**
   * 道路收费类型
   */
  LinkTollType toll_type{LinkTollType::kNotSurvey};
  /**
   * 是否高架路
   * 备注:Unimap规格无数据, 暂时无赋值处理, 维护一段时间后删除;
   */
  bool is_elevate{false};
  /**
   * 是否是城市区域
   */
  bool u_flag{false};
  /**
   * 是否跨tile
   * 备注:Unimap规格无数据, 跨瓦片全部打断了, 暂时无赋值处理, 维护一段时间后删除;
   */
  bool is_cross_tile{false};
  /**
   * link宽度 : Unimap规格无数据, 暂时无赋值处理, 维护一段时间后删除;
   * ------------------------
   * | width | 含义          |
   * ------------------------
   * | 15    | <=3.0m       |
   * | 30    | (3.0m, 5.5m) |
   * | 55    | (5.5m, 13.0m)|
   * | 130   | >13m         |
   * | 0     | 未调查        |
   * ------------------------
   */
  int width{0};
  /**
   * link长度，单位：cm
   */
  int length{0};
  /**
   * 起点角度[0,360)，以正北方向顺时针计算，用于算路
   */
  int16_t start_angle{0};
  /**
   * 终点角度[0,360)，以正北方向顺时针计算，用于算路
   */
  int16_t end_angle{0};
  /**
   * 起点限速来源
   */
  LinkSpeedSource speedsrc_ste{LinkSpeedSource::KEmpty};
  /**
   * 终点限速来源
   */
  LinkSpeedSource speedsrc_ets{LinkSpeedSource::KEmpty};
  /**
   * 数据来源 : Unimap规格无数据, 暂时无赋值处理, 维护一段时间后删除;
   */
  LinkDataSource data_source{LinkDataSource::kNONE};

  /**
   * Link数据覆盖状态信息;
   * 组合类型
   */
  LinkCoverageStatusMaskType data_coverage_status{0};

  /**
   * 几何矢量方向归一化标记
   */
  bool geo_direction_normalization{false};
};

/**
 * @brief IndoorLink 属性
 */
struct IndoorLinkAttribute {
  /** 类型，0 步行，1 车行，2 混合 */
  uint8_t type{0};

  /** 坡度类型 */
  LinkSlopeType slope{LinkSlopeType::kNoSurvey};

  /** 路宽,0:<=3.0m;1:(3.0m，5.5m);2:(5.5m，13.0m);3:>13m;7:未调查*/
  uint8_t width{7};

  /**
   * 停车场道路属性,多种属性按位与运算
   * 0 : 非停车场道路
   * 0x1 : 停车场内部道路
   * 0x2 : 停车场楼层间连接道路
   * 0x4 : 停车场连接外部道路
   * 0x8 : 停车场间连接道路
   * 0x10 : 上下线分离
   * 0x20 : 交叉点内部路
   * 0x40 : 掉头口
   * 0x80 : 室内区域内道路
   * 0x100 : 室外区域内道路
   */
  uint16_t indoor_link_type{0};

  /** 道路限高，单位厘米，0表示没有限高信息 */
  uint16_t heigth_limit{0};

  /** 步行方向 */
  LinkDirection direction_walk{LinkDirection::kUNKNOWN};

   /** 建筑物信息 */
   std::shared_ptr<BuildingFloorInfo> build_floor_info_ptr;

  public:
   void Print() {}
   void Reset() {
     type =0 ;
     slope = LinkSlopeType::kNoSurvey;
     width = 7;
     indoor_link_type = 0;
     heigth_limit = 0;
     direction_walk = LinkDirection::kUNKNOWN;
     build_floor_info_ptr.reset();
   }
};

/**
 * 路口标识
 */
enum class CrossFlag : uint8_t {
  /**
   * 不是路口的节点
   */
  kNonIntersection = 0,
  /**
   * 复合路口的子点
   */
  kSubNode,
  /**
   * 单一路口
   */
  kSingleNode,
  /**
   * 复合路口的主点
   */
  kMainNode
};

/**
 * 道路打断标记
 * */
enum class BreakFlag : uint8_t {
  /**
   * 没有SD数据
   * */
  kNO_SD = 1,
  /**
   * 道路结束
   * */
  kLINK_END,
  /**
   * 非制作范围
   * */
  kNO_MAKE_REGION,
  /**
   * 制作范围内未采集
   * */
  kNO_MAKE_IN_REGION
};

class ILink;
typedef std::shared_ptr<ILink> ILinkPtr;
typedef std::shared_ptr<const ILink> ILinkConstPtr;
typedef std::weak_ptr<ILink> ILinkWeakPtr;
typedef std::weak_ptr<const ILink> ILinkConstWeakPtr;

class ILinkNode;
typedef std::shared_ptr<ILinkNode> ILinkNodePtr;
typedef std::shared_ptr<const ILinkNode> ILinkNodeConstPtr;

/**
 * @brief Link Node基类
 */
class ILinkNode {
 public:
  virtual ~ILinkNode() = default;

  /**
   * @brief 获取NodeID
   * @return NodeID
   */
  virtual NodeIDType GetNodeID() const = 0;

  /**
   * @brief 获取NodeTPID
   * @return TPIDType
   */
  virtual TPIDType GetTPID() const = 0;

  /**
   * 获取原始 ID
   * 说明: 只有开启对应宏并且指定了拉去linkNodeRawID数据开关才会拉去对应的数据, 返回有效值
   *  1> 关闭宏: DISABLE_LINKNODE_RAW_ID 2> 设定DataFeatureType: kRouteLinkNodeRawId
   * @return linkNode id
   */
  virtual int64_t GetRawID() const = 0;

  /**
   * @brief 判断当前Node是否是室内Node
   * @return 室内Node返回true，其他Node返回false
   */
  virtual bool isIndoorNode() const = 0;

  /**
   * 获取交叉口路口标识
   * @return 路口标识
   */
  virtual CrossFlag GetCrossFlag() const = 0;

  /**
   * 获取红绿灯标识
   * @return 有红绿灯返回 true，否则返回 false
   */
  virtual bool GetTrafficLightFlag() const = 0;

  /**
   * 获取主Node，主点包括完整的路口拓扑，当 CrossFlag 为 kSubNode 时返回主Node，其他情况下返回nullptr
   * @return 主Node
   */
  virtual ILinkNodeConstPtr GetMainNode() const = 0;

  /**
   * 获取子点ID集合，当 CrossFlag 为 kMainNode 时返回子点集合，其他情况返回空
   * @return
   */
  virtual const std::vector<NodeIDType> &GetSubNodeIds() const = 0;

  /**
   * @brief 获取进入的所有link元素集合
   * @return 进入的link元素集合
   */
  virtual std::vector<ILinkConstPtr> GetEnterElements() const = 0;

  /**
   * @brief 获取出度的所有link元素集合
   * @return 出度的link元素集合
   */
  virtual std::vector<ILinkConstPtr> GetOutElements() const = 0;

  /**
   * @brief 获取所在路口的所有进入link元素集合
   * @return 进入的link元素集合
   */
  virtual std::vector<ILinkConstPtr> GetEnterElementsCross() const = 0;

  /**
   * @brief 获取所在路口的所有出度link元素集合
   * @return 出度的link元素集合
   */
  virtual std::vector<ILinkConstPtr> GetOutElementsCross() const = 0;

  /**
   * @brief Link Node坐标
   * @return Node坐标
   */
  virtual Coordinate GetPosition() const = 0;

  /**
   * @brief 获取进入的所有linkID集合
   * @return 进入的linkID集合
   */
  virtual std::vector<LinkIDType> GetEnterLinkIDs() const = 0;

  /**
   * @brief 获取出度的所有linkID集合
   * @return 出度的linkID集合
   */
  virtual std::vector<LinkIDType> GetOutLinkIDs() const = 0;

  /**
   * @brief 获取所在路口的所有进入linkID集合
   * @return 进入的linkID集合
   */
  virtual std::vector<LinkIDType> GetEnterLinkIDsCross() const = 0;

  /**
   * @brief 获取所在路口的所有出度linkID集合
   * @return 出度的linkID集合
   */
  virtual std::vector<LinkIDType> GetOutLinkIDsCross() const = 0;

  /**
   * @brief 当前LinkNode的Guidance信息
   * @note 限定类型:kBoard,kBranchPic,kGate,kLsl;
   * @return Guidance信息列表
   */
  virtual std::vector<IConditionConstPtr> GetGuidance(GuidanceType type) const = 0;

  /**
   * 获取主Node ID，主点包括完整的路口拓扑，当 CrossFlag 为 kSubNode 时返回主Node ID，其他情况下返回空值
   * @return 主Node ID
   */
  virtual NodeIDType GetMainNodeID() const = 0;
};

/**
 * @brief 带方向的link类(Link with Direction)
 */
class NERD_EXPORT IDLink {
 public:
  virtual ~IDLink() = default;
  /**
   * @brief 获取其中的Link，使用前需判空
   * @return Link指针
   */
  virtual ILinkConstPtr GetLink() const = 0;

  /**
   * @brief 获取Link的行驶方向。方向只可能是顺向或逆向之一，
   *        逆向时要注意个别link属性是反的，比如 node、形点等属性
   * @return 行驶方向
   */
  virtual LinkDirection GetDirection() const = 0;
};
typedef std::shared_ptr<IDLink> IDLinkPtr;
typedef std::shared_ptr<const IDLink> IDLinkConstPtr;
typedef std::weak_ptr<IDLink> IDLinkWeakPtr;
typedef std::weak_ptr<const IDLink> IDLinkConstWeakPtr;

/**
 * Link拓扑接口，只返回ID
 */
class ILinkTopo {
 public:
  virtual ~ILinkTopo() = default;
  /**
   * @brief 获取Link的下游LinkID
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetNextIDs(LinkDirection direction, bool with_indoor = false) const = 0;

  /**
   * @brief 获取Link的上游LinkID
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetPreviousIDs(LinkDirection direction, bool with_indoor = false) const = 0;

  /**
   * @brief 获取Link的下游LinkID，包含交叉点内link
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetNextIDsWithinIntersection(LinkDirection direction,
                                                               bool with_indoor = false) const = 0;

  /**
   * @brief 获取Link的上游LinkID，包含交叉点内link
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<LinkIDType> GetPreviousIDsWithinIntersection(LinkDirection direction,
                                                                   bool with_indoor = false) const = 0;
};

/**
 * @brief link基类
 * SD地图中的link对象
 */
class NERD_EXPORT ILink : public ITopology<ILinkNodeConstPtr, ILinkConstPtr, LinkIDType>, public ILinkTopo {
 public:
  /**
   * @brief 获取Link TPID
   * @return TPIDType
   */
  virtual TPIDType GetTPID() const = 0;
  /**
   * @brief 和Link绑定的SurfaceObject对象
   * @return SurfaceObject列表
   */
  virtual const SurfaceObjectCollect &GetSurfaceObjects() const = 0;

  /**
   * @brief Link的通用属性
   * @return 通用属性
   */
  virtual const LinkAttribute& GetAttribute() const = 0;

  /**
   * @brief 获取Link的扩展属性
   * @return 扩展属性
   */
  virtual const LinkExtAttribute& GetExtAttribute() const = 0;

  /**
   * @brief 判断当前Link是否是室内道路
   * @return 室内道路返回true，其他道路返回false
   */
  virtual bool isIndoorRoad() const = 0;

  /**
   * @brief 获取交通限制信息
   * @return 交限列表
   */
  virtual std::vector<IRestrictionConstPtr> GetRestrictions() const = 0;

  /**
   * @brief 是否存在基于道路的通行限制
   * 挂接在Link本身的限制条件
   * @return 限制条件，如果没有返回空
   */
  virtual std::vector<ILinkRestrictConstPtr> getLinkRestrict() const = 0;

  /**
   * @brief 获取Link的下游列表
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return std::vector<IDLinkConstPtr>
   */
  virtual std::vector<IDLinkConstPtr> GetNext(LinkDirection direction, bool with_indoor = false) const = 0;

  /**
   * @brief 获取Link的上游列表
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetPrevious(LinkDirection direction, bool with_indoor = false) const = 0;

  /**
   * @brief 获取Link的下游列表，包含交叉点内link、室内道路
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetNextWithinIntersection(LinkDirection direction,
                                                                bool with_indoor = false) const = 0;

  /**
   * @brief 获取Link的上游列表，包含交叉点内link、室内道路
   * @param direction 搜索方向,只能是正向或逆向
   * @param with_indoor 是否包含室内Link
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IDLinkConstPtr> GetPreviousWithinIntersection(LinkDirection direction,
                                                                    bool with_indoor = false) const = 0;

  /**
   * @brief IndoorLink的特有属性
   * @return SD Link返回nullptr
   */
  virtual std::shared_ptr<const IndoorLinkAttribute> GetIndoorAttribute() const = 0;

  /**
   * @brief link几何形状
   * @return 数据坐标系
   */
  virtual const std::shared_ptr<std::vector<Coordinate>> &GetGeometry() const = 0;

  /**
   * @brief 判断当前link与rect是否相交
   * @return false: 不想交, true: 相交
   */
  virtual bool IntersectsWithRange(const Rect &range) const = 0;

  /**
   * 获取原始 ID
   * @return link id
   */
  virtual int64_t GetRawID() const = 0;

  /**
   * @brief 判断当前 Link 处于低层路网或高层路网
   * @return true表示link处于低层路网，false表示link处于高层路网
   */
  virtual bool IsInLowerLayer() const = 0;

  /**
   * 获取高层路网Link ID
   * @return 高层道路ID，无时返回null
   */
  virtual std::shared_ptr<LinkIDType> GetUpperLinkID() const = 0;

  /**
   * 获取低层路网Link ID
   * @return 低层道路ID，无时返回null
   */
  virtual std::shared_ptr<LinkIDType> GetLowerLinkID() const = 0;

  /**
   * 获取当前link对应的laneGroupIds
   * @brief 双向Link可能对应不同的LaneGroup
   *
   * @param direction 道路方向
   * @return Lane group Id数组，若方向不匹配返回空数组
   */
  virtual std::vector<LaneGroupIDType> GetLaneGroupIds(LinkDirection direction) const = 0;

  /**
   * 获取link对应的laneGroup的tpid
   */
  virtual std::vector<TPIDType> GetLaneGroupTPIDs(LinkDirection direction) const = 0;

  /**
   * 接口废弃
   * @brief 当前Link的坡度曲率数据
   * @return 如果不存在返回nullptr
   */
  virtual const std::shared_ptr<std::vector<LinkPosition>> &GetLinkPosition() const = 0;

  /**
   * @brief 当前Link的曲率数据
   * @return 如果不存在返回nullptr
   */
  virtual const std::shared_ptr<const std::vector<LinkCurvature>> GetLinkCurvature() const = 0;
  /**
   * @brief 当前Link的横向坡度数据，正数向左倾斜，负数向右倾斜
   * @return 如果不存在返回nullptr
   */
  virtual const std::shared_ptr<const std::vector<LinkSlope>> GetLinkHorizontalSlope() const = 0;
  /**
   * @brief 当前Link的纵向坡度数据，正数为上坡，负数为下坡
   * @return 如果不存在返回nullptr
   */
  virtual const std::shared_ptr<const std::vector<LinkSlope>> GetLinkVerticalSlope() const = 0;
  /**
   * @brief 当前Link的航向数据
   * @return 如果不存在返回nullptr
   */
  virtual const std::shared_ptr<const std::vector<LinkHeading>> GetLinkHeading() const = 0;

  /**
   * @brief 当前Link起点处的坡度类型
   * @return 坡度类型
   */
  virtual LinkSlopeType GetStartNodeSlope() const = 0;

  /**
   * @brief 当前Link终点处的坡度类型
   * @return 坡度类型
   */
  virtual LinkSlopeType GetEndNodeSlope() const = 0;

  /**
   * @brief 当前Link的Guidance信息
   * @note 限定类型:kWarningSign,kCamera,kPointSpeed,kTrafficLightCtrl,kZLevelGroup,kTollStation,kLaneMetaInfo;
   * @return Guidance信息列表
   */
  virtual std::vector<std::shared_ptr<const IGuidanceInfo>> GetGuidance(GuidanceType type) const = 0;

  /**
   * @brief 当前Link起点的NodeID
   * @return NodeID
   */
  virtual NodeIDType GetStartNodeID() const = 0;

  /**
   * @brief 当前Link终点的NodeID
   * @return NodeID
   */
  virtual NodeIDType GetEndNodeID() const = 0;

 protected:
  /**
   * @deprecated
   */
  std::vector<std::shared_ptr<const ILink>> GetNext() const override {
    return {};
  }
  /**
   * @deprecated
   */
  std::vector<std::shared_ptr<const ILink>> GetPrevious() const override {
    return {};
  }
};

}  // namespace api
}  // namespace nerd
