// Copyright (c) 2024 Tencent Inc. All rights reserved.

#pragma once

#include "dto/types.h"
#include "fbgenerator/unimap_close_road_generated.h"

namespace nerd {
namespace api {

class ICloseRoadStatus;
typedef std::shared_ptr<const ICloseRoadStatus> ICloseRoadStatusConstPtr;

/**
 * 封路瓦片数据
 */
struct NERD_EXPORT TileCloseRoadStatus {
  TileCloseRoadStatus() = default;
  /**
   * 瓦片更新到的时间戳，用于计算过期;
   */
  uint32_t last_timestamp{0};
  /**
   * 生命周期
   */
  uint32_t interval{0};
  /**
   * 版本
   */
  uint32_t version{0};
  /**
   * 具体数据, FB协议
   */
  std::shared_ptr<UnimapCloseRoad::TileDataT> tile_data{nullptr};
};

/**
 * 封路瓦块数据接口
 */
class NERD_EXPORT ICloseRoadStatus {
 public:
  virtual ~ICloseRoadStatus() = default;
  /**
   * 获取封路数据的类型, SD封路还是HD封路
   * @return
   */
  virtual CloseType GetCloseType() const = 0;
  /**
   * 获取对应的封路数据
   * @return
   */
  virtual const TileCloseRoadStatus &GetTileCloseRoadStatus() const = 0;
};

/**
 * 封路数据状态实现类
 * 对应一个瓦片
 */
struct NERD_EXPORT CloseRoadStatus : public ICloseRoadStatus {
  CloseRoadStatus() = default;
  CloseType GetCloseType() const override { return type; }
  const TileCloseRoadStatus &GetTileCloseRoadStatus() const override { return close_road_status; }
  /**
   * 封路类型，线/面
   */
  CloseType type{CloseType::kZLevel};
  /**
   * 封路瓦片数据
   */
  TileCloseRoadStatus close_road_status;
};

}  // namespace api
}  // namespace nerd
