// Copyright 2021 Tencent Copyright 2020 Tencent. All rights reserved.
//
// Created by tong<PERSON><PERSON><PERSON> on 2021/6/9.
//

#pragma once

#include <memory>
#include <unordered_map>
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * 车道变化点方向
 */
enum class LaneChangePointDirection : bool {
  /**
   * 正向
   */
  kFORWARD = 0,
  /**
   * 反向
   */
  kBACKFORD = 1,
};

/**
 * @brief 变道点信息
 */
struct ILaneChangePoint {
  virtual ~ILaneChangePoint() = default;
  /**
   * @brief 设置方向
   * @param direction
   */
  void SetDirection(LaneChangePointDirection direction) { this->direction = direction; }
  /**
   * @brief 设置类型
   * @param type
   */
  void SetType(LaneChangePointType type) { this->object_type = type; }
  /**
   * 设置前序索引
   * @param index
   */
  void SetPrevIndex(int index) { this->prev_index = index; }
  /**
   * 设置比例
   * @param ratio
   */
  void SetRatio(double ratio) { this->ratio = ratio; }

 public:
  /**
   * 设置类型
   */
  LaneChangePointType object_type{LaneChangePointType::kNONE};
  /**
   * 设置方向
   */
  LaneChangePointDirection direction{LaneChangePointDirection::kFORWARD};
  /**
   * 索引
   */
  int prev_index{0};
  /**
   * 比例尺
   */
  double ratio{0};
};

typedef std::shared_ptr<ILaneChangePoint> ILaneChangePointPtr;
typedef std::shared_ptr<const ILaneChangePoint> IILaneChangePointConstPtr;

/**
 * @brief 特殊车道类型
 */
struct SpecialLane : public ILaneChangePoint {};
typedef std::shared_ptr<SpecialLane> SpecialLanePtr;

/**
 * @brief 路面对象集合类
 */
class NERD_EXPORT LaneChangePointCollect {
 private:
  std::unordered_map<LaneChangePointType, std::vector<IILaneChangePointConstPtr>> lane_change_points_;

 public:
  /**
   * @brief 设置lane change point
   * @param[in] lane_change_points 对象集合
   */
  void SetLaneChangePoints(
      std::unordered_map<LaneChangePointType, std::vector<IILaneChangePointConstPtr>> &&lane_change_points) {
    this->lane_change_points_ = lane_change_points;
  }

  /**
   * 插入一个lane change point
   * @param type
   * @param obj
   */
  void AddLaneChangePoint(LaneChangePointType type, const IILaneChangePointConstPtr &obj) {
    const auto &iter_objs = this->lane_change_points_.find(type);
    if (iter_objs == this->lane_change_points_.end()) {
      this->lane_change_points_.insert({type, {}});
    }
    this->lane_change_points_.at(type).emplace_back(obj);
  }

  const std::unordered_map<LaneChangePointType, std::vector<IILaneChangePointConstPtr>> &GetLaneChangePointMap() const {
    return lane_change_points_;
  }

  /**
   * 批量插入lane change point
   */
  void AddLaneChangePoints(
      const std::unordered_map<LaneChangePointType, std::vector<IILaneChangePointConstPtr>> &lane_change_points) {
    for (auto const &obj : lane_change_points) {
      for (auto const &o : obj.second) {
        AddLaneChangePoint(obj.first, o);
      }
    }
  }

  /**
   * @brief 是否存在lane change point
   * @return 是否存在lane change point
   */
  bool IsExistLaneChangePoint() const { return !lane_change_points_.empty(); }

  /**
   * @brief 获取指定类型lane change point
   * @return lane change point
   */
  template <class T>
  std::vector<std::shared_ptr<const T>> GetLaneChangePointByType(LaneChangePointType type) const {
    std::vector<std::shared_ptr<const T>> points;
    auto it = lane_change_points_.find(type);
    if (it == lane_change_points_.end()) {
      return points;
    }
    points.reserve(it->second.size());
    for (const auto &obj : it->second) {
      points.push_back(std::static_pointer_cast<const T>(obj));
    }
    return points;
  }

  /**
   * @brief 获取特殊车道
   * @return 特殊车道集合
   */
  std::vector<std::shared_ptr<const SpecialLane>> GetSpecialLanes() const {
    return GetLaneChangePointByType<SpecialLane>(LaneChangePointType::kSPECICAL_LANE);
  }

  std::vector<IILaneChangePointConstPtr> GetAllSurfaceObjects() const {
    std::vector<IILaneChangePointConstPtr> all;
    for (auto const &obj : lane_change_points_) {
      all.insert(all.end(), obj.second.begin(), obj.second.end());
    }
    return all;
  }
};

}  // namespace api
}  // namespace nerd
