/*
 * Copyright (c) 2021 Tencent Inc. All rights reserved.
 * @version: 1.0
 * @LastEditTime: 2021-03-19 09:24:59
 */

#pragma once

#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "common_const.h"

#include "dto/dto.h"
#include "nerd_event_listener.h"
#include "nerd_global_event_listener.h"
#include "nerd_export.h"

namespace mapbase {
class HttpInterface;
}  // namespace mapbase

namespace nerd {
namespace api {

/**
 * 当前的网络状态
 */
enum class NetworkStatus : int32_t {
  Unknown = -2,
  // NotReachable         = -1,  // 网络不可达
  ReachableViaWiFi = 0,      // WI-FI
  ReachableViaETHERNET = 1,  // 以太网
  ReachableViaWWAN2G = 2,    // 数据网络 2G
  ReachableViaWWAN3G = 3,    // 数据网络 3G
  ReachableViaWWAN4G = 4,    // 数据网络 4G
  ReachableViaWWAN5G = 5,    // 数据网络 5G
};

/**
 表明当前网络的情况，是弱网络或者网络情况良好
 */
enum class NetworkCondition : int32_t {
  Unknown = -1,  // 未知状态
  Offline = 0,   // 关闭网络或飞行模式
  Weak = 1,      // 网络差
  Good = 2,      // 网络良好
  PossibleDisconnected = 3,
};

/**
 * Nerd 数据库工作模式
 */
enum class OnlineFileMgrWorkStatus : int8_t {
  kNotInited,                   // 未初始化
  kInitedDB,                    // 锁定数据库，主Nerd实例
  kInitedBinaryCache            // 未锁定数据库，作为Nerd子实例工作
};

/**
 * 数据模式
 */
enum class DataPreference : uint8_t {
  /**
   * 纯离线模式：只读取本地的离线数据
   */
  kOfflineOnly = 0,
  /**
   * 纯在线模式：只读取在线数据（含在线缓存）
   */
  kOnlineOnly = 1,
  /**
   * 在线优先模式:
   *   优先读取在线缓存
   *     若有, 先返回在线缓存数据;
   *     若无, 核查是否有离线数据?
   *         若有, 返回离线数据;
   *   发起网路请求, 若有新数据更新数据;
   */
  kOnlineFirst = 2,
  /**
   * 离线优先模式
   *    优先读取离线数据
   *      若有, 返回离线数据, 终止;
   *      若无, 加载在线缓存数据, 然后网路;
   */
  kOfflineFirst = 3,
  /**
   * 自动模式
   *    nerd内部根据在离线数据状态自动切换模式，用于定位和EHP
   */
  kAuto = 4,
  /**
   * Socket模式，nerd子实例向主实例请求数据
   */
  kSocket = 5,
};
std::ostream& operator<<(std::ostream& out, const DataPreference& preference);

/**
 * @brief 在线数据状态变化回调
 */
class NERD_EXPORT OnlineDataStatusChangeCallback {
 public:
  virtual ~OnlineDataStatusChangeCallback() = default;
  /**
   * @brief 检测到数据版本变化，比如在线数据更新后回调，使用者收到此回调后根据情况清理缓存等
   * @param bb_id building block的id
   * @param version_id 新数据的 version id
   */
  virtual void OnNewData(MapDataBuildingBlockID bb_id, uint32_t version_id) = 0;
};

/**
 * @brief 数据语言类型变化回调
 */
class NERD_EXPORT DataLanguageChangeCallback {
 public:
  virtual ~DataLanguageChangeCallback() = default;
  /**
   * @brief 检测到数据语言类型发生变化，使用者收到此回调后根据情况清理缓存重新获取数据
   * @param bb_id building block的id
   * @param language 新语言类型
   * @param old_language 原语言类型
   */
  virtual void OnLanguageChanged(MapDataBuildingBlockID bb_id, MapDataLanguageType language,
                                 MapDataLanguageType old_language) = 0;
};

/**
 * @brief 配置类
 */
class NERD_EXPORT APIConfig {
 public:
  /**
   * 用于标识使用方
   * 备注: 请根据业务模块负责内容赋值容易区分的名字;
   */
  std::string user_id{"unknown"};
  /**
   * 底图名称, 默认值: 空
   * 备注: 底图名称用于标识使用方使用的底图名称，用于区分不同底图的数据;
   */
  std::string user_subdivision;
  /**
   * 配置数据类型
   */
  MapDataBuildingBlockID build_block_id{MapDataBuildingBlockID::kLANE};
  /**
   * 数据请求方式：纯在线/纯离线/在线优先/离线优先
   * 默认值:仅在线模式
   */
  DataPreference preference{DataPreference::kOnlineOnly};
  /**
   * 查询结果中是否包含空的瓦片，默认会忽略空瓦片，只返回有结果的内容
   */
  bool ignore_emtpy_tile{true};
  /**
   * 是否等待所有数据成功
   * */
  bool wait_all_success{false};
  /**
   * 等待时间(若超过该时间处理不完则会强制返回错误), 单位毫秒
   * 如果每次请求瓦片个数较多，建议设置大点，这里包括等待数据下载时间
   * */
  int32_t wait_ms{8000};
  /**
   * 在线数据状态变化回调，本地有缓存的情况下，会立刻触发回调
   */
  std::weak_ptr<OnlineDataStatusChangeCallback> online_status_callback;
  /**
   * 数据语言类型变化回调
   */
  std::weak_ptr<DataLanguageChangeCallback> language_change_callback;
  /**
   * 语言类型，内部使用，外部设置无效
   */
  MapDataLanguageType language_type{MapDataLanguageType::kLANGUAGE_CN};
  /**
   * 是否支持全国级别世界图
   * 说明: 1> 默认不支持; 2> 数据级别3/5/7;
   */
  bool support_world_map_for_country{false};
  /**
   * 是否支持城市级别世界图
   * 说明: 1> 默认不支持; 2> 数据级别10/13/15等;
   */
  bool support_world_map_for_city{false};
};

/**
 * 全局配置类
 */
class NERD_EXPORT GlobalConfig {
 public:
  /**
   * 数据根目录，目录格式如下：
   *  db_root_path : 根目录
   *     |_____ cache : 在线数据目录
   *            |____  : bmd_x.nds       SD显示数据
   *            |____  : objects3d_x.nds 模型数据(楼块/小树等)
   *            |____  : ----_x.nds      其他数据
   *    |_____ offline: 离线数据目录(需要外部创建)
   *            |____ XXXXXXX : XXX城市离线数据
   *            |____ YYYYYYY : YYY城市离线数据
   * 配置值为: your_path/data
   */
  std::string data_root_path{"data"};
  /**
   * 坐标系统
   * 备注: 默认使用GCJ02坐标系
   */
  mapbase::CoordSysType coord_sys{mapbase::CoordSysType::GCJ02};
  /**
   * HttpInterface 实例，用于在线数据下载
   */
  std::shared_ptr<mapbase::HttpInterface> http;
  /**
   * 是否采用Nerd全局配置开关控制世界图加载; 说明:
   * 若设置true则代表统一控制, 即上层底图不论创建多少个MapView它们均使用Nerd的GlobalConfig世界图开关来控制;
   * 若设置false则代表分别控制, 即上层底图可以创建多个MapView,但它们的世界图开关不共享, 各自独立控制, 开关参考如下:
   *     ApiConfig::support_world_map_for_country 和 ApiConfig::support_world_map_for_city;
   * 默认值: true; 若设置为false, GlobalConfig的开关将失效;
   */
  bool support_world_map_by_nerd_global_config{true};
  /**
   * 采用Nerd全局配置开关控制模式下, 是否支持全国级别世界图;
   * 说明: 1> 默认不支持; 2> 数据级别3/5/7;
   */
  bool support_world_map_for_country{false};
  /**
   * 采用Nerd全局配置开关控制模式下, 是否支持城市级别世界图
   * 说明: 1> 默认不支持; 2> 数据级别10/13/15等;
   */
  bool support_world_map_for_city{false};
  /**
   * 离线调试模式，false时用离线城市包或9级Tile，true时用全国大文件
   */
  bool offline_debug{false};
  /**
   * 离线模式，是否是CityCode模式，false时使用9级Tile模式，true时使用CityCode模式，默认为true
   */
  bool offline_city_code_mode{true};
  /**
   * 离线模式，单个tile的级别，默认为9级
   */
  int32_t offline_nine_square_grid_tile_level{9};
  /**
   * 在线缓存磁盘文件(对应目录cache)大小限制，超过限制时会清除缓存，单位：MB
   * 备注: 1.启动时核查; 2.内部限制范围[200, 5*1024], 用户可以在该范围内调整;
   * 默认值: 600M
   */
  int max_cache_size_mb{600};
  /**
    * 在线缓存磁盘文件(对应目录cache, 仅地形图)大小限制，超过限制时会清除缓存，单位：MB
    * 备注: 1.启动时核查; 2.内部限制范围[500, 5*1024], 用户可以在该范围内调整;
    * 默认值: 1024M
   */
  int dtm_max_cache_size_mb{1024};
  /**
   * 在线文件缓存中，百变地图部分的大小限制， 超出限制会清理百变地图的文件缓存
   * 单位为MB， 默认大小是128MB
   */
  int agile_data_max_cache_size{128};
  /**
   * 下发瓦片服务url
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   * 一般用于测试场景;
   */
  std::string tile_server_url{};
  /**
   * 模型服务url
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   * 一般用于测试场景;
   */
  std::string model_server_url{};
  /**
   * 路况服务下载地址
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   * 一般用于测试场景;
   */
  std::string traffic_server_url{};
  /**
   * 封路服务接口
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   * 一般用于测试场景;
   */
  std::string closed_road_server_url{};
  /**
   * 室内数据服务下载地址
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   * 一般用于测试场景;
   */
  std::string indoor_server_url{};
  /**
   * 在线元素查询服务地址
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   * 一般用于测试场景;
   */
  std::string online_feature_url{};
  /**
   * 三维地形数据服务下载地址
   * 备注: 内部会根据车图/手图平台设置默认正式域名; 当外部指定环境时传入,优先使用外部设置环境;
   */
  std::string dtm_server_url{};
  /**
   * 百变地图数据下载地址
   */
  std::string agile_map_server_url{};
    
  /**
  * 自定义百变地图数据下载地址
  */
  std::string custom_agile_map_server_url{};
  /**
   * 龙门架动态数据下载地址
   */
  std::string gantry_data_server_url{};
  /**
   * 客户渠道号
   * 注意: 客户端必须注入,若不清楚渠道联系项目;
   */
  std::string channel{};
  /**
   * 语言类型
   */
  MapDataLanguageType language_type{MapDataLanguageType::kLANGUAGE_CN};
  /**
   *  调试信息回调
   */
  std::shared_ptr<NerdEventListener> nerd_event_listener_holder_{nullptr};
   /**
   * 全局事件回调，例如保留数据中心点回调
   */
  std::shared_ptr<NerdGlobalEventListener> nerd_global_event_listener_holder_{nullptr};
  /**
   * 数据更新模式, false时全量更新，true时差分更新
   * 备注: 目前支持BMD/OBJ/LANE/ROUTE/LANE4K几种数据;
   * 支持动态开关, 接口:
   */
  bool support_diff_update{true};
  /**
   * 是否优先使用端上配置的在线缓存大小限制
   * 默认采用云控配置的各类业务数据配比动态执行缓存文件清理;
   */
  bool use_cache_size_setting_force{false};
  /**
   * 是否使用新的缓存清理策略
   * 老策略: 启动按照总大小(max_cache_size_mb)判断, 超过改值整个cache目录删除重新删除
   * 新策略: 按照各类业务数据配比动态删除
   */
  bool use_new_cache_clean{false};
  /**
   * 是否检查data_cube持有者情况,以及是否启动内存检查线程
   */
  bool use_nerd_memory_checker{false};
  /**
   * 预留的nerdServer端口，如果有配置nerd以server/client的方式启动，则此配置生效
   * 范围应在 1024 ~ 49151, 且主/从nerd service应配置相同的端口
   */
  int port{0};
    
  /**
   * 是否支持L3建筑物
  */
  bool support_l3_building{false};

  /**
   * 是否开启云控
   */
  bool enable_cloud_control{true};
  /**
   * 是否开启定时版本检查, 仅针对瓦片数据. 对于在线模式，必须打开此选项
   */
  bool enable_data_version_check{true};

  /**
  * 是否开启定时版本检查, 针对非瓦片数据. 目前是：室内、DTM、模型
  */
  bool enable_other_data_version_check{true};

  /**
   * 是否允许瓦片数据落磁盘。云图poc使用
   */
  bool enable_tile_data_cache_disk{true};
};

}  // namespace api
}  // namespace nerd

