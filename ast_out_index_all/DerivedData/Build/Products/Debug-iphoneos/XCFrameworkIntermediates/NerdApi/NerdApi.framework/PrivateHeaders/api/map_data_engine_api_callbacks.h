//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/20.
//

/**
 * 说明: 该文件中内定义map_data_engine_api所有数据加载的接口参数,返回值，回调的定义
 * 避免map_data_engine_api过于庞大;
 */
#pragma once

#include "api/async_callback.h"
#include "dto/iface_gantry_dynamic_info.h"

namespace nerd {
namespace api {

/**
 * @brief 龙门架电子牌动态信息获取参数
 * 支持批量请求
 */
class NERD_EXPORT GetGantryBoardBatchParam {
 public:
  /**
   * 电子牌ID列表
   * 说明: ID中的Version信息首次填充0即可;
   */
  std::vector<nerd::api::PackedIDType> device_ids;
  /**
   * 渲染不适用，做透传给nerd，nerd对数据打上tile的标记用于缓存清理
   */
  TileIDType tile_id{0};
};

/**
 * @brief 龙门架电子牌动态信息获取结果
 */
class NERD_EXPORT GetGantryBoardBatchResult {
 public:
  /**
   * 返回结果描述, 0正常, 非0异常
   */
  RetMessage ret{0, ""};
  /**
   * 对应请求中的电子牌显示内容
   */
  std::vector<BoardCompleteContent> device_contents;
};

/**
 * @brief 异步批量获取指定龙门架电子牌动态信息
 */
class NERD_EXPORT GetGantryBoardCallback : public AsyncCallback<GetGantryBoardBatchParam, GetGantryBoardBatchResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  virtual void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetGantryBoardBatchParam> req,
                        std::unique_ptr<GetGantryBoardBatchResult> rsp) = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  virtual void OnCanceled(JobId job_id, bool success, const RetMessage &ret) = 0;
};

/**
 * 龙门架图片资源获取参数
 */
class NERD_EXPORT GetGantryResourceBatchParam {
 public:
  /**
   * 动态信息中的模型资源ID
   * 说明: ID中的Version信息首次填充0即可;
   */
  std::vector<PackedIDType> resource_ids;
};

/**
 * @brief 龙门架电子牌获取参数
 */
class NERD_EXPORT GetGantryResourceBatchResult {
 public:
  /**
   * 返回结果描述, 0正常, 非0异常
   */
  RetMessage ret{0, ""};
  /**
   * 对应请求中的资源内容
   */
  std::vector<BoardResourceData> resource_contents;
};

/**
 * @brief 异步批量获取指定龙门架电子牌动态信息
 */
class NERD_EXPORT GetGantryResourceCallback : public AsyncCallback<GetGantryResourceBatchParam, GetGantryResourceBatchResult> {
 public:
  /**
   * @brief 通用的回调方法
   * @param ret 错误码和说明
   * @param job_id 任务id，在触发请求时拿到，回调时也携带该值
   * @param req 请求参数
   * @param rsp 返回数据
   */
  virtual void OnResult(const RetMessage &ret, JobId job_id, std::unique_ptr<GetGantryResourceBatchParam> req,
                        std::unique_ptr<GetGantryResourceBatchResult> rsp) = 0;
  /**
   * @brief 任务被取消
   * @param job_id 任务id,如果任务还未创建即被取消，则该值为负数
   * @param success 是否取消成功
   * @param ret 详细信息
   */
  virtual void OnCanceled(JobId job_id, bool success, const RetMessage &ret) = 0;
};


}  // namespace api
}  // namespace nerd