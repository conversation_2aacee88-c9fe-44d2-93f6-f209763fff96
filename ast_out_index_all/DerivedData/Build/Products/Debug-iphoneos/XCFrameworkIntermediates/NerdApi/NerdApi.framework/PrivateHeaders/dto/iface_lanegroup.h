// Copyright 2021 Tencent Copyright 2021 Tencent Inc.  All rights reserved.
//
// Author: ni<PERSON><PERSON><PERSON>@tencent.com (Emperor Penguin)
//
// 数据引擎NERD之NERD数据读取模块

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dto/iface_access_restrict.h"
#include "dto/iface_topology.h"
#include "dto/surfaceobject.h"
#include "dto/iface_boundary.h"
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 道路等级
 * HD|COMMENT
 * --|--
 * 0 |高速路
 * 1 |城市高速路
 * 2 |国道
 * 3 |省道
 * 4 |县道
 * 6 |乡镇村道
 * 8 |其它道路
 * 9 |非引导道路, 又名禁穿辅路
 * 10|轮渡
 * 11|步行道路
 * 12|人渡
 * 13|自行车专用道
 * 14|索道
 */
typedef int8_t RoadClassType;

/**
 * @brief lane group种类
 */
enum class RoadKindType : uint8_t {
  /**
   * 无属性
   */
  kNone = 0x00,
  /**
   * 环岛
   */
  kRoundabout = 0x01,
  /**
   * 上下线分离
   */
  kDirectionSpit = 0x02,
  /**
   * JCT,连接高速道路
   */
  kJCT = 0x03,
  /**
   * 交叉点内Link
   */
  kCrossInner = 0x04,
  /**
   * IC,连接高速和其它不同等级道路
   */
  kIC = 0x05,
  /**
   * 停车区
   */
  kPA = 0x06,
  /**
   * 服务区
   */
  kSA = 0x07,
  /**
   * 固定桥
   */
  kBridge = 0x08,
  /**
   * 步行街
   */
  kWalkStreet = 0x09,
  /**
   * 辅路
   */
  kSecondary = 0x0a,
  /**
   * 匝道
   */
  kRamp = 0x0b,
  /**
   * 全封闭道路
   */
  kEnclosure = 0x0c,
  /**
   * 未定义交通区域
   */
  kUndefined = 0x0d,
  /**
   * POI连接路
   */
  kPoiConnect = 0x0e,
  /**
   * 隧道
   */
  kTunnel = 0x0f,
  /**
   * 公交专用道
   */
  kBus = 0x11,
  /**
   * 提前右转
   */
  kRightTurn = 0x12,
  /**
   * 风景路线
   */
  kView = 0x13,
  /**
   * 区域内道路
   */
  kInnerRegion = 0x14,
  /**
   * 提前左转
   */
  kLeftTurn = 0x15,
  /**
   * 调头口
   */
  kUTurn = 0x16,
  /**
   * 主辅路出入口
   */
  kMainSecondaryEntrance = 0x17,
  /**
   * 停车场出入口连接路
   */
  kParkEntrance = 0x1a,
  /**
   * 移动式桥
   */
  kMovableBridge = 0x1b,
  /**
   * 借道左转
   */
  kReversalLeft = 0x1e,
  /**
   * 主路
   */
  kMainRoad = 0x1f,
  /**
   * 门前路
   */
  kFrontDoor,
  /**
   * 高架路
   */
  kElevated,
  /**
   * 货车专用道
   */
  kTruckLane,
  /**
   * 普通
   */
  kNORMAL,
  /**
   * 收费站
   */
  kTOLL,
  /**
   * 建设中道路
   */
  kCONSTRUCTION,
  /**
   * 十字路口
   */
  kINTERSECTION,
  kHIGHWAY_PORT,              /** 高速出入口 */
  kHIGHWAY_CONNECTION,        /** 高速连接路 */
  kNONSTANDARD_ROUNDABOUT,    /** 非标准环岛 */
  kSPECIAL_CONNECTION,        /** 特殊连接路 */
  kPARKING_OCCUPY_ROAD,       /** 包含占道停车场 */
  kOWNERSHIP,                 /** 私道 */
  kINNER_VIRTUAL_CONNECT,     /** 区域内虚拟连接路 */
  kTAXI,                      /** 区域内虚拟连接路 */
  kTIDE,                      /** 潮汐车道 */
  kSTEP_ROAD,                 /** 台阶路 */
  kINNER_CROSS_ROAD,          /** 路口面道路 */
  kENTRANCE_AND_EXIT_CONNECT, /** 出入口连接路 */
  kAHEAD_TURN_RIGHT,          /** 提前右转 */
  kAHEAD_TURN_LEFT,           /** 提前左转 */
  kCROSS_LINE_OVERPASS,       /** 跨线天桥 */
  kSUNKEN_ROAD,               /** 下沉道路 */
  kRAMP_BOTH_PASS,            /** 匝道双通 */
  kMOUNTAIN_ROAD,             /** 山路 */
  kSUNKEN_ROAD_PORT,          /** 下沉道路出入口 */
  kRAILWAY,                   /** 铁路 */
  kRAILWAY_HIGHSPEED,         /** 高铁 */
  /**
   * 其他
   */
  kOTHER,
  kVIRTUAL_SOLID_CONNECTION, /** 虚实线链接路 */
  kPARKING_INTERNAL_ROAD,    /** 停车场内部道路 */
};

typedef std::vector<RoadKindType> RoadKindTypeVec;

/**
 * @brief 道路隔离类型
 */
enum class RoadIsolationType : uint8_t {
  kFENCE = 1,         /** 护栏 */
  kGREEN_BELT,        /** 绿化带 */
  kSPLIT_POLE,        /** 隔离桩 */
  kSTEP_ROAD,         /** 台阶路 */
  kPHYSICAL_SEPAR,    /** 物理隔离 */
  kLEGAL_SEPAR,       /** 法律隔离 */
  kCURB,              /** 路沿石 */
  kWALL,              /** 墙 */
  kPROTECTIVE_NET,    /** 防护网 */
  kNO_ISOLATION,      /** 无隔离 */
  kNATURAL_BOUNDARY,  /** 自然边界 */
  kEDGE_VIRTUAL_LINE, /** 边缘虚拟线 */
  kOTHER,             /** 其他 */
  kNO_DATA,           /** 无数据 */
};

/**
 * @brief lane group 方向
 */
enum class LaneGroupDirection : uint8_t {
  /**
   * 无数据
   */
  kNO_DATA = 0,
  /**
   * 顺向、逆向
   */
  kBOTH_DIRECTION = 1,
  /**
   * 顺向
   */
  kSTART_TO_END,
  /**
   * 逆向
   */
  kEND_TO_START
};

/**
 * @brief lane group类型
 */
enum class LaneGroupType : bool {
  /**
   * 普通
   */
  kROAD_TYPE_NORMAL = 0,
  /**
   * 十字路口
   */
  kROAD_TYPE_CROSSAREA
};

/**
 * 交换区类型
 */
enum class RoadExchangeType : uint8_t {
  /**
   * 非交换区
   */
  NONE = 1,
  /**
   * 中间型
   */
  MIDDLE,
  /**
   * 侧变型
   */
  SIDE,
  /**
   * 收费站型
   */
  TOLL
};

/**
 * Lane数据分类类型
 */
enum LaneDataSourceType : uint8_t {
  /**
   * 高精数据
   */
  kHD = 0,
  /**
   * SD扩展成的 4K 数据
   */
  kSD_4K = 1,
  /**
   * HDair数据
   */
  kHD_air = 2,

  kMax = 3
};

/**
 * @brief lane group属性
 */
struct LaneGroupAttribute {
  /**
   * 道路名字
   */
  std::string name;
  /**
   * 道路等级
   */
  RoadClassType road_class_type{0x7F};
  /**
   * 道路属性
   */
  RoadKindTypeVec road_kind_types;
  /**
   * 建设状态
   */
  ConstructionType is_const{ConstructionType::kNormal};
  /**
   * 是否跨tile
   */
  bool is_cross_tile{false};
  /**
   * 方向
   */
  LaneGroupDirection direction{LaneGroupDirection::kNO_DATA};
  /**
   * 左侧隔离类型
   */
  RoadIsolationType left_isolation_type{RoadIsolationType::kNO_DATA};
  /**
   * 右侧隔离类型
   */
  RoadIsolationType right_isolation_type{RoadIsolationType::kNO_DATA};
  /**
   * 交换类型
   */
  RoadExchangeType exchange_type{RoadExchangeType::NONE};
  /**
   * 速度等级
   */
  SpeedClassType speed_class{0};
  /**
   * 数据分类信息
   */
  LaneDataSourceType lane_data_source{LaneDataSourceType::kHD};
  /**
   * 是否在路口内
   */
  bool is_in_intersection{false};
  /**
   * 原始数据id, 仅加载调试数据时有效
   */
  std::string raw_id;
};

/**
 * @brief lane group 渲染渐变信息
 * 渲染专用:当从高等级路口向低等级道路时渲染需要渐变;
 */
struct LaneGroupGradientInfo {
  /**
   * LaneGroup关联的路口的道路等级
   */
  RoadClassType cross_area_road_class_type{0x7F};
  /**
   * 在LaneGroup的指定开始位置; 百分比范围:[0.0-1.0]
   */
  float start_pos_ratio{0};
  /**
   * 在LaneGroup的指定结束位置; 百分比范围:[0.0-1.0]
   */
  float end_pos_ratio{1.0};
  /**
   * 在LaneGroup指定开始位置的渐变值; 值范围:[0.0-1.0]
   */
  float start_gradual_value{0};
  /**
   * 在LaneGroup指定结束位置的渐变值; 值范围:[0.0-1.0]
   */
  float end_gradual_value{1.0};
};

enum class CrossAreaType : uint8_t {
  /**
   * 其他
   */
  kOther = 0,
  /**
   * 普通路口
   */
  kNormal = 1,
  /**
   * 环岛
   */
  kRoundabout = 2,
  /**
   * 分合流路口
   */
  kDetach_Join = 3,
  /**
   * 分离路口
   */
  kDetach = 4,
  /**
   * 合并路口
   */
  kJoin = 5,
  /**
   * 主辅路出入口
   */
  kMain_Secondary = 6,
  /**
   * 主路到辅路路口
   */
  kIC_toSecondary = 7,
  /**
   * 辅路到主路路口
   */
  kIC_toMain = 8,
  /**
   * 辅路到主路路口
   */
  kU_TURN = 9,
  /**
   * 辅路到主路路口
   */
  kGate = 10
};

class ILane;
typedef std::shared_ptr<ILane> ILanePtr;
typedef std::shared_ptr<const ILane> ILaneConstPtr;
typedef std::weak_ptr<ILane> ILaneWeakPtr;
typedef std::weak_ptr<const ILane> ILaneConstWeakPtr;
class IBoundary;
typedef std::shared_ptr<IBoundary> IBoundaryPtr;
typedef std::shared_ptr<const IBoundary> IBoundaryConstPtr;

class ILaneGroup;
typedef std::shared_ptr<ILaneGroup> ILaneGroupPtr;
typedef std::shared_ptr<const ILaneGroup> ILaneGroupConstPtr;
typedef std::weak_ptr<ILaneGroup> ILaneGroupWeakPtr;
typedef std::weak_ptr<const ILaneGroup> ILaneGroupConstWeakPtr;

class ILaneGroupNode;
typedef std::shared_ptr<ILaneGroupNode> ILaneGroupNodePtr;
typedef std::shared_ptr<const ILaneGroupNode> ILaneGroupNodeConstPtr;

class ICrossArea;
typedef std::shared_ptr<ICrossArea> ICrossAreaPtr;
typedef std::shared_ptr<const ICrossArea> ICrossAreaConstPtr;
typedef std::weak_ptr<const ICrossArea> ICrossAreaWeakPtr;


/**
 * @brief lane group node基类
 */
class ILaneGroupNode {
 public:
  virtual ~ILaneGroupNode() = default;

  /**
   * @brief 获取当前NodeID
   * @return NodeID
   */
  virtual NodeIDType GetNodeID() const = 0;
  /**
   * @brief 进入LaneGroupNode的元素集合
   * @return 进入集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetEnterElements() const = 0;
  /**
   * @brief 离开LaneGroupNode的元素集合
   * @return 离开集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetOutElements() const = 0;
  /**
   * @brief 进入LaneGroupNode且出度为Id的集合
   * @param id 出度Id
   * @return 集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetEnterElementsByID(LaneGroupIDType id) const = 0;
  /**
   * @brief 离开LaneGroupNode且入度为Id的集合
   * @param id 入度Id
   * @return 集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetOutElementsByID(LaneGroupIDType id) const = 0;

  virtual uint8_t GetNodeMask() const = 0;

  typedef enum : uint8_t {
    DETACH = 1,
    JOINT = 1u << 1u,
    CROSS = 1u << 2u,
  } NodeMask;
};

/**
 * @brief Lane Group基类
 */
class NERD_EXPORT IBaseLaneGroup {
 public:
  virtual ~IBaseLaneGroup() = default;
  virtual LaneGroupType GetBaseLaneGroupType() const = 0;

  /**
   * @brief 和LaneGroup绑定的SurfaceObject对象
   * @return SurfaceObject列表
   */
  virtual const SurfaceObjectCollect &GetSurfaceObjects() const = 0;

  /**
   * 获取控制交通灯灯箱
   * @return 交通灯灯箱列表
   */
  virtual std::vector<std::shared_ptr<const TrafficLightBox>> GetTrafficLightBoxs() const = 0;

  /**
   * 获取道路类型
   * @return 道路类型
   */
  virtual RoadClassType GetRoadClass() const = 0;
};

/**
 * @brief Lane Group类
 */
class NERD_EXPORT ILaneGroup : public IBaseLaneGroup,
                               public ITopology<ILaneGroupNodeConstPtr, ILaneGroupConstPtr, LaneGroupIDType> {
 public:
  /**
   * 获取lanegrou tpid
   */
  virtual TPIDType GetTPID() const = 0;
  /**
   * @brief LaneGroup的通用属性
   * @return 通用属性
   */
  virtual LaneGroupAttribute GetAttribute() const = 0;
  /**
   * @brief 获取LaneGroup的渲染渐变信息
   * @return 渐变信息
   */
  virtual std::vector<LaneGroupGradientInfo> GetGradientInfo() const = 0;
  /**
   * @brief 获取主Boundary
   * @return 主Boundary
   */
  virtual IBoundaryConstPtr GetMainBoundary() const = 0;

  /**
   * @brief 获取Group中的所有车道，按从右到左顺序排列
   * @return 所有车道
   */
  virtual std::vector<ILaneConstPtr> GetLanes() const = 0;

  /**
   * @brief 获取Group中所有的边线/标线信息，从右到左排列
   * @return 所有Boundary
   */
  virtual const std::vector<IBoundaryConstPtr> &GetBoundaries() const = 0;

  /**
   * @brief 是否存在基于道路的通行限制
   * 挂接在LaneGroup本身的限制条件
   * @return 限制条件，如果没有返回空
   */
  virtual std::vector<IAccessRestrictConstPtr> GetAccessRestrict() const = 0;

  /**
   * @brief 到下游指定laneGroup是否存在限行关系
   * 挂接在LaneGroup->LaneGroup之间的限制条件
   * @param next_lane_group 下游LaneGroup
   * @return 如果不存在限行关系返回nullptr
   */
  virtual std::vector<IAccessRestrictConstPtr> GetTraversalRestrict(
      const ILaneGroupConstPtr &next_lane_group) const = 0;

  /**
   * @brief 如果是复杂路口，则获取该复杂路口内所有连接的LaneGroup
   * @return 如果不是复杂路口，返回空
   */
  virtual std::vector<ILaneGroupConstPtr> GetCrossRelatedGroups() const = 0;

  /**
   * @brief 获取前驱LaneGroup集合
   * @return 前驱LaneGroup列表
   */
  std::vector<ILaneGroupConstPtr> GetNext() const override = 0;

  /**
   * @brief 获取后继LaneGroup集合
   * @return 后继LaneGroup列表
   */
  std::vector<ILaneGroupConstPtr> GetPrevious() const override = 0;

  /**
   * @brief 获取前驱LaneGroupID集合
   * @return 前驱LaneGroupID列表
   */
  virtual std::vector<LaneGroupIDType> GetNextIDs() const = 0;

  /**
   * @brief 获取后继LaneGroupID集合
   * @return 后继LaneGroupID列表
   */
  virtual std::vector<LaneGroupIDType> GetPreviousIDs() const = 0;

  /**
   * 获取前驱lanegroup tpid 集合
   */
  virtual std::vector<TPIDType> GetPreviousTPIDs() const = 0;

  /**
   * 获取后继lanegroup tpid 集合
   */
  virtual std::vector<TPIDType> GetNextTPIDs() const = 0;

  /**
   * @brief lane group对应的SD Link集合
   * @return SD Link集合
   */
  virtual std::vector<LinkIDType> GetLinks() const = 0;

  /**
   * 获取对应的SD link的tpid集合
   */
  virtual std::vector<TPIDType> GetLinkTPIDs() const = 0;

  /**
   * @brief 判断range是否与当前Lane Group有交集
   * @param range 矩形框
   * @return true: 有交集, false: 无交集
   */
  virtual bool IntersectsLaneGroupWithRange(const Rect &range) const = 0;

  /**
   * @deprecated
   * @brief 获取当前lane group的近似方向平行lane group，注意该方法无法获取跨瓦片的lane group
   * @param is_left true 获取左侧 false 获取右侧
   * @return 同方向平行关系集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetParallelRelations(bool is_left) const = 0;

  /**
   * @deprecated
   * @brief 获取当前lane group的逆向平行的lane group，注意该方法无法获取跨瓦片的lane group
   * @param is_left true 获取左侧 false 获取右侧
   * @return 逆向关系集合
   */
  virtual std::vector<ILaneGroupConstPtr> GetReverseRelations(bool is_left) const = 0;

  /**
   * @brief 获取当前lane group的近似方向平行lane group id
   * @param is_left true 获取左侧 false 获取右侧
   * @return 同方向平行关系集合
   */
  virtual std::vector<LaneGroupIDType> GetParallelRelationIDs(bool is_left) const = 0;

  /**
   * @brief 获取当前lane group的逆向平行的lane group id
   * @param is_left true 获取左侧 false 获取右侧
   * @return 逆向关系集合
   */
  virtual std::vector<LaneGroupIDType> GetReverseRelationIDs(bool is_left) const = 0;


  /**
   * 获取平行lanegroup tpid 集合
   */
  virtual std::vector<TPIDType> GetParallelRelationTPIDs(bool is_left) const = 0;

  /**
   * 获取逆向lanegroup tpid 集合
   */
  virtual std::vector<TPIDType> GetReverseRelationTPIDs(bool is_left) const = 0;

  /**
   * 获取LaneGroup内打断点数据
   * @param is_left true 获取左侧 false 获取右侧
   * @return 打断信息集合
   */
  virtual std::vector<BreakPointInfo> GetBreakPoint(bool is_left) const = 0;

  /**
   * 获取LaneGroup两侧和平行/逆向路之间的打断点数据
   * @param is_left true 获取左侧 false 获取右侧
   * @return 打断信息集合
   */
  virtual std::vector<BreakPointInfo> GetOuterBreakPoint(bool is_left) const = 0;

  /**
   * @brief 判断当前lane group是否与路口有压盖关系
   * @return true: 有压盖关系，false：无压盖关系
   */
  virtual bool WithinCrossArea() const = 0;

  /**
   *   false                 true
   *  [   ] --------------> [   ]
   *
   * 获取关联的路口
   * @param isForward true-前向路口 false-身后路口
   * @return 路口数据
   */
  virtual ICrossAreaConstPtr GetCrossArea(bool isForward) const = 0;
  /**
   *   false                 true
   *  [   ] --------------> [   ]
   * 获取当前lane group的关联的路口ID
   * @param isForward true-前向路口 false-身后路口
   * @return 路口对象列表
   */
  virtual std::vector<ICrossAreaConstPtr> GetCrossAreas(bool isForward) const = 0;
  /**
   *   false                 true
   *  [   ] --------------> [   ]
   * 获取当前lane group的关联的路口ID
   * @param isForward true-前向路口 false-身后路口
   * @return 路口ID列表
   */
  virtual std::vector<CrossAreaIDType> GetCrossAreaIDs(bool isForward) const = 0;

  /**
   * 获取与当前lanegroup关联的路口tpid
   */
  virtual std::vector<TPIDType> GetCrossAreaTPIDs(bool isForward) const = 0;

  /**
   * @brief LaneGroup是否由SD link扩展生成
   * @return true：是由SD link扩展生成，false：不是由SD link扩展生成
   */
  virtual bool IsSDExtend() const = 0;

  /**
   * @brief LaneGroup与CrossArea重叠标记
   * @return
   */
  virtual bool GetOverlapFlag() const = 0;
};

/**
 * 路口面不可通行边
 */
struct NERD_EXPORT ICrossNoThroughEdge {
  virtual ~ICrossNoThroughEdge() = default;
  /**
   * @brief 获取geometry；内部实时计算，多次调用会有性能损耗
   * 备注: 1> 用于绘制路口的不可通行边, 同时贴边路况也在使用;
   *      2> 该信息通过StartBreakPoint 和 EndBreakPoint计算得出;
   * @return 此不可通行边几何形状
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetGeometry() = 0;
  /**
   * @brief 获取该路口不可通行边相对整个路口完整几何的开始信息
   * @return
   */
  virtual const BreakPointInfo& GetStartBreakPointInfo() const = 0;
  /**
   * 获取该路口不可通行边相对整个路口完整几何的结束信息
   * @return
   */
  virtual const BreakPointInfo& GetEndBreakPointInfo() const = 0;
  /**
   * @brief 边界区间，按照变化点区分
   * 备注: 用于绘制路口的路沿石, 同Boundary中结构
   *      数据中的索引区间是相对整个路口完整几何的;
   * @return 区间列表, 可能空;
   */
  virtual const std::vector<BoundarySetV2>& GetBoundaryChangePointsV2() const = 0;
  /**
   * @brief 获取起点位置关联的lane group
   * @return 关联的lane group
   */
  virtual ILaneGroupConstPtr GetStartLaneGroup() = 0;
  /**
   * @brief 获取终点位置关联的lane group
   * @return 关联的lane group
   */
  virtual ILaneGroupConstPtr GetEndLaneGroup() = 0;
};

/**
 * 路口面
 */
class NERD_EXPORT ICrossArea : public IBaseLaneGroup {
 public:
  /**
   * 获取ID
   * @return ID
   */
  virtual CrossAreaIDType GetID() const = 0;

  /**
   * 获取TpId
   * 说明: 路名面对象暂时没有TpId信息
   */
  virtual TPIDType GetTPID() const = 0;
  /**
   * @brief 获取路口面边界形状点
   * @return 形状点列表
   */
  virtual std::shared_ptr<std::vector<Coordinate>> GetAreaBoundaryGeometry() const = 0;

  /**
   * @brief 获取离开路口面的LaneGroup集合
   * 备注: 该接口无法返回跨瓦片LaneGroup信息, 建议通过GetExitLaneGroupIDs获取
   * @return LaneGroup列表
   */
  virtual std::vector<ILaneGroupConstWeakPtr> GetExits() const = 0;

  /**
   * @brief 获取进入路口面的LaneGroup集合
   * 备注: 该接口无法返回跨瓦片LaneGroup信息, 建议通过GetEntranceLaneGroupIDs获取
   * @return 后继LaneGroup列表
   */
  virtual std::vector<ILaneGroupConstWeakPtr> GetEntrances() const = 0;

  /**
   * @brief 获取离开路口面的LaneGroupID集合
   * @return LaneGroupID列表
   */
  virtual std::vector<LaneGroupIDType> GetExitLaneGroupIDs() const = 0;

  /**
   * @brief 获取进入路口面的LaneGroupID集合
   * @return LaneGroupID列表
   */
  virtual std::vector<LaneGroupIDType> GetEntranceLaneGroupIDs() const = 0;

  /**
   * @brief 获取离开路口面的LaneGroupTPID集合
   * @return LaneGroupID列表
   */
  virtual std::vector<TPIDType> GetExitLaneGroupTPIDs() const = 0;

  /**
   * @brief 获取进入路口面的LaneGroupTPID集合
   * @return LaneGroupID列表
   */
  virtual std::vector<TPIDType> GetEntranceLaneGroupTPIDs() const = 0;

  /**
   * @brief 判断range是否与当前路口有交集
   * @param range 矩形框
   * @return true: 有交集, false: 无交集
   */
  virtual bool IntersectsWithRange(const Rect &range) const = 0;

  /**
   * 获取路口面类型
   * @return 路口面类型
   */
  virtual CrossAreaType GetType() const = 0;

  /**
   * @brief 获取路口面内包含的虚拟车道
   * @return 虚拟车道列表
   */
  virtual std::vector<ILaneConstPtr> GetLanes() const = 0;

  /**
   * @brief 获取路口面内包含的虚拟车道线
   * @return 虚拟车道线列表
   */
  virtual std::vector<IBoundaryConstPtr> GetBoundaries() const = 0;

  /**
   * @brief 获取路口面内真实车道边界
   * @return 真实车道边界列表
   */
  virtual const std::vector<IBoundaryConstPtr> &GetRealBoundaries() const = 0;

  /**
   * 获取路口面不可通行边
   *
   * @return 路口面不可通行边列表
   */
  virtual const std::vector<std::shared_ptr<ICrossNoThroughEdge>> &GetCrossNoThroughEdges() const = 0;

  virtual bool IsSDExtend() const = 0;

  /**
   * @brief 被标识的路口，包括主辅路路口、高快道路汇入路口、高快道路分歧路口
   * @return false:未被标记；true:被标记
   */
  virtual bool GetMainSideRoadFlag() const = 0;

  /**
   * @brief LaneGroup与CrossArea重叠标记
   * @return
   */
  virtual bool GetOverlapFlag() const = 0;

  /**
   * @brief 返回该路口面是否重复存储的
   * @return false: 非重复存储; true: 重复存储, 在所属的瓦片中关联的额外瓦片中也有存储;
   */
  virtual bool GetRepeatFlag() const = 0;
};

struct TunnelNoThroughEdge {
    
   /**
    * 不可通行边起始位置
    * 高程单位是米;
    */
    BreakPointInfo start_pos;
    
   /**
    * 不可通行边终止位置
    * 高程单位是米;
    */
    BreakPointInfo end_pos;
};

class NERD_EXPORT TunnelCrossArea {
public:
    void SetId(const CrossAreaIDType &id);
    CrossAreaIDType GetId();
    
    std::shared_ptr<std::vector<Coordinate>> GetGeometry();
    void SetGeometry(std::shared_ptr<std::vector<Coordinate>> geo);
    
    std::vector<std::shared_ptr<TunnelNoThroughEdge>> GetNoThroughEdge();
    void SetNoThroughEdge(std::vector<std::shared_ptr<TunnelNoThroughEdge>>& edges);
    
    std::shared_ptr<std::vector<Coordinate>> GetNoThroughEdgeGeometry(std::shared_ptr<TunnelNoThroughEdge>);
    
    std::vector<LaneGroupIDType> GetRelatedLaneGroupIds();
    std::vector<TPIDType> GetRelatedLaneGroupTPIds();
    void SetRelatedLaneGroupIds(std::vector<LaneGroupIDType>& ids);
    
    std::vector<CrossAreaIDType> GetRelatedCrossAreaIds();
    std::vector<TPIDType> GetRelatedCrossAreaTPIds();
    void SetRelatedCrossAreaIds(std::vector<CrossAreaIDType>& ids);

public:
    CrossAreaIDType id_;
    std::shared_ptr<std::vector<Coordinate>> tunnel_geometries_;
    std::vector<std::shared_ptr<TunnelNoThroughEdge>> no_through_edges_;
    std::vector<LaneGroupIDType> lane_group_ids_;
    std::vector<TPIDType> lane_group_tp_ids_;
    std::vector<CrossAreaIDType> cross_area_ids_;
};

}  // namespace api
}  // namespace nerd
