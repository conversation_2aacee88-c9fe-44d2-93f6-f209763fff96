//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/6/22.
//

#pragma once

#include <string>
#include "nerd_export.h"

namespace nerd {

/**
 * 获取版本号
 * 备注: 因为后续手图/车图/开平的版本体系无法统一, 为了能形成唯一标识用于服务监控统计, 实际内容同GetBranchName;
 * @return 版本号字符串
 */
NERD_EXPORT std::string GetVersionName();
/**
 * 获取代码分支信息(信息来自流水线, 有Tag优先用Tag, 没有Tag用Branch);
 * 正式版本理论上都应该是Tag信息;
 * @return 分支字符串
 */
NERD_EXPORT std::string GetBranchName();
/**
 * 获取代码Commit信息
 * @return hash字符串
 */
NERD_EXPORT std::string GetCommitHash();
/**
 * 获取版本详细信息，包括版本信息以及仓库hash
 * @param pretty 是否缩进
 * @return JSON格式版本信息
 */
NERD_EXPORT std::string GetVersionDetail(bool pretty = false);

}  // namespace nerd
