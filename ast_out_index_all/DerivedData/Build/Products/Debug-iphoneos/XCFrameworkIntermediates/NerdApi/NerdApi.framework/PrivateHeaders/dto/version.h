// Copyright 2021 Tencent Inc.  All rights reserved.

#pragma once

#include <string>
#include "types.h"

namespace nerd {
namespace api {

/**
 * 格式版本号：格式为X.Y，主版本号值变化表示格式不兼容
 */
class NERD_EXPORT FormatVersion {
 public:
  /**
   * 主版本号
   */
  uint32_t main_version{0};
  /**
   * 子版本号
   */
  uint32_t sub_version{0};

  std::string ToString() const { return std::to_string(main_version) + "." + std::to_string(sub_version); }

  /**
   * 判断版本号是否和当前代码的格式版本号兼容
   * @return true表示兼容，false表示不兼容
   */
  bool IsCompatible(nerd::api::MapDataBuildingBlockID bb_id) const;

  /**
   * 将字符串解析为格式版本号
   * @param version_str 格式字符串
   * @param version 解析后的版本号
   * @return 是否解析成功
   */
  static bool Parse(const std::string &version_str, nerd::api::FormatVersion &version);
};

/**
 * 版本信息
 */
class VersionInfo {
 public:
  /**
   * 版本 ID
   */
  uint32_t version_id{0};
  /**
   * 格式版本号
   */
  FormatVersion format_version;
  /**
   * 数据规格版本号
   */
  std::string data_spec_version;
  /**
   * 数据版本号
   */
  std::string data_version;
  /**
   * 编译器版本号
   */
  std::string compiler_version;
  /**
   * 创建时间
   */
  std::string creation_time;
  /**
   * 渠道信息
   */
  std::string channel;
};

/**
 * Nds文件元信息
 */
class NERD_EXPORT NdsMetadata {
 public:
  /**
   * 数据版本信息，含格式版本等
   */
  VersionInfo data_version;
  /**
   * 大版本号，一般是月度版本号，用于数据更新
   */
  uint32_t major_version{0};
  /**
   * 小版本号，一般是周版本号，用于数据更新
   */
  uint32_t minor_version{0};
};

}  // namespace api
}  // namespace nerd
