//
// Created by learnjiang on 2024/1/16.
//

#pragma once

#include <vector>

namespace nerd {
namespace api {

/**
 * 扩展方向
 */
enum DtmTileExtendDirectEnum : uint8_t {
  Extend_North = 1 << 0,
  Extend_South = 1 << 1,
  Extend_West = 1 << 2,
  Extend_East = 1 << 3,
};

/**
 * 一个Tile的地形数据
 * 一个tile下切分成多个格子数据配置。一般一个tile分成256*256；横纵扩展后为258*258
 * 一个地形瓦片内网格的排布表达:
 * -------------------------------------
 * [000]                           [255]
 * [256]                           [511]
 *             .....
 * [x]                             [x+256]
 * [65279]                         [65535]
 * -------------------------------------
 */
struct DtmTileData {
  /**
   * tile方向扩展信息
   */
  uint8_t extend_direct_info{0};
  /**
   * 基准高程,单位0.1m
   */
  int32_t base_height{0};
  /**
   * 横向单元格数目
   */
  uint16_t row_num{0};
  /**
   * 纵向单元格数据
   */
  uint16_t col_num{0};
  /**
   * 所有单元格地貌的相对高程信息
   * 一般: size= row_num * col_num; 当带有extend_direct_info时 size > row_num * col_num;
   * 还原方法:
   *   第0个格子绝对高度为:  base_height + delta_heights[0];
   *   第x个格子绝对高度为: x-1格子的绝对高度 + delta_heights[x]; (x>=1)
   */
  std::vector<int32_t> quad_delta_heights;
  /**
   * 所有单元格地貌类型列表
   * 一般: size= row_num * col_num; 当带有extend_direct_info时 size > row_num * col_num;
   */
  std::vector<uint8_t> quad_land_types;
};

}  // namespace api
}  // namespace nerd
