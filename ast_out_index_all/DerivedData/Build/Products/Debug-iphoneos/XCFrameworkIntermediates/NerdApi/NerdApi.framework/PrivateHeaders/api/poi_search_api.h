//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON> on 2023/2/21.
//

#pragma once

#include "map_data_engine_api.h"

namespace nerd {
namespace api {

typedef std::shared_ptr<const PointFeature> PoiDataPtr;
typedef std::vector<PoiDataPtr> PoiDataVec;
typedef std::shared_ptr<PoiDataVec> PoiDataVecPtr;

/**
 * @brief poi离线搜索数据瓦块
 */
class IPoiSearchDataCube {
 public:
  virtual ~IPoiSearchDataCube() = default;
  /**
   * 返回该瓦片内的所有POI数据
   * @return
   */
  virtual PoiDataVecPtr GetPOIs() = 0;
};

/**
 * @brief 获取poi离线搜索数据接口返回结果
 */
struct GetPoiSearchByRectResult {
 public:
  RetMessage ret{0, ""};
  std::unique_ptr<IPoiSearchDataCube> data_cube;
};

/**
 * @brief 获取poi离线搜索数据接口
 */
class IPoiSearchDataApi {
 public:
  virtual ~IPoiSearchDataApi() = default;

  /**
   * 拉取指定区域内的数据
   * @param area_code 城市码
   * @param param 区域参数
   * @return
   */
  virtual GetPoiSearchByRectResult GetDataByRect(int32_t area_code, const GetMapDataByRectParam &param) = 0;

  /**
   * @brief 拉取指定poi id的数据
   * @param area_code 城市码
   * @param poi_id POI ID
   * @return
   */
  virtual PoiDataPtr GetDataByPoiId(int32_t area_code, const PoiIDType &poi_id) = 0;

  /**
   * @brief 拉取指定poi id列表的数据
   * @param area_code 区域编码
   * @param poi_ids poi id列表
   * @return 查询结果
   */
  virtual PoiDataVecPtr GetDataByPoiIds(int32_t area_code, const std::vector<PoiIDType> &poi_ids) = 0;

  /**
   * @brief 获取poi索引辅助数据
   * @return poi索引辅助数据
   */
  virtual IPOIGlobalIndexConstPtr GetPoiGlobalIndexData() = 0;

  /**
   * @brief 获取poi多语言索引辅助数据
   * @return 获取poi多语言索引辅助数据
   */
  virtual IPOIGlobalMultilingualIndexConstPtr GetPoiGlobalMultilingualIndexData() = 0;

  /**
   * @brief 获取poi英文单词表
   * @param area_code 区域编码
   * @return 指定城市poi英文单词表数据
   */
  virtual const std::shared_ptr<const std::vector<MultilingualWord>> GetPoiMultilingualWords(int32_t area_code) = 0;

  /**
   * @brief 查找指定城市的poi字符索引数据
   * @param area_code 区域编码
   * @param index_code 索引编码
   * @return
   */
  virtual std::shared_ptr<std::vector<std::vector<uint32_t>>> GetCharIndexData(int32_t area_code,
                                                                               int16_t index_code) = 0;

  /**
   * @brief 查找指定城市的poi拼音索引数据
   * @param area_code 区域编码
   * @param index_code 索引编码
   * @return 检索结果
   */
  virtual std::shared_ptr<std::vector<uint32_t>> GetPinyinIndexData(int32_t area_code, int16_t index_code) = 0;

  /**
   * @brief 查找指定城市的poi类型索引数据
   * @param area_code 区域编码
   * @param index_code 索引编码
   * @return 检索结果
   */
  virtual std::shared_ptr<std::vector<uint32_t>> GetKindIndexData(int32_t area_code, int16_t index_code) = 0;

  /**
   * @brief 查找指定城市的poi英文索引数据
   * @param area_code 区域编码
   * @param index_code 索引编码
   * @return 检索结果
   */
  virtual std::shared_ptr<std::vector<std::vector<uint32_t>>> GetEnglishIndexData(int32_t area_code,
                                                                                  int16_t index_code) = 0;

  /**
   * @brief 查找坐标是否在城市的tile范围内
   * @param area_code 区域编码
   * @param point 点坐标
   * @return 检索结果
   */
  virtual bool CheckPointInCity(int32_t area_code, const nerd::api::GeoCoordinate &point) = 0;

  /**
   * @brief 查找城市检索所需文件是否存在
   * @param area_code 区域编码
   * @return 检索结果
   */
  virtual bool CheckCityFileExist(int32_t area_code) = 0;

  /**
   * @brief 设置检索语言
   * @param language_type 语言类型
   * @return
   */
  virtual void SetPoiSearchLanguageType(nerd::api::MapDataLanguageType language_type) = 0;
  /**
   * @brief 获取指定城市的
   * @param area_code 区域编码
   * @return
   */
  virtual std::shared_ptr<nerd::api::FtsCityHeader> GetFtsCityHeader(int32_t area_code) = 0;
  /**
   * @brief 获取BGPOI.NDS及对应的FTS.NDS的version ID(两个文件关联性强)
   * @param area_code 区域编码
   * @return metaData
   */
  virtual std::string GetPOICityDataVersion(int32_t area_code) = 0;
};

/**
 * @brief 创建poi搜索数据接口
 * 备注需要先初始化Nerd, 否则会创建失败
 * @return api指针指针
 */
NERD_EXPORT std::unique_ptr<IPoiSearchDataApi> CreatePoiSearchApi();

}  // namespace api
}  // namespace nerd
