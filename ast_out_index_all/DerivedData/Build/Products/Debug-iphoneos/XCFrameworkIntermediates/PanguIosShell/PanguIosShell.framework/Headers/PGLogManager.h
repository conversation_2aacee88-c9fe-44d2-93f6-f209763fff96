//
//  PGLogManager.h
//  PanguIosShell
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/10/25.
//

#import <Foundation/Foundation.h>
#import <PLog/PLog-umbrella.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, PGModuleId) {
    PGModuleIdNerd              = 1,    // 数据引擎
    PGModuleIdMapSDK            = 2,    // 底图
    PGModuleIdMapBiz            = 3,    // MapBiz
    PGModuleIdRouteGuidance     = 4,    // 诱导
    PGModuleIdLocationVDR       = 6,    // 定位 
    PGModuleIdOlRoute           = 7,    // 离线算路
    PGModuleIdPoiEngine         = 8,    // 离线检索
    PGModuleIdAgileMap          = 9,    // 百变地图
};

// MARK: - PGLoggerConfig
@interface PGLoggerConfig : NSObject
@property (nonatomic, assign) PLLogLevel logLevel;       // 通过设置该属性，可以修改对应模块的日志级别
@property (nonatomic, assign) PLAppenderType logDest;    // 通过设置该属性，可以修改对应模块的日志输出方式
@end

// MARK: - PGLogManager
@interface PGLogManager : NSObject
/**
 * @brief 日志初始化统一接口。通过该接口可以一次初始化所有的引擎日志
 * @param path      传入存储日志的一个根目录。接口内部会给各个模块分配子目录
 * @param logLevel  传入初始的日志级别
 * @param logDest   传入初始的日志输出方式
 */
+ (void)initLoggerWithPath:(NSString *)path logLevel:(PLLogLevel)logLevel logDest:(PLAppenderType)logDest;

/**
 * @brief 通过模块id来获取到PGLoggerConfig实例。通过PGLoggerConfig实例可以查询和修改各模块的日志级别和日志输出方式
 * @param moduleId  传入要查询的模块id
 * @return 返回 PGLoggerConfig 实例，如果为nil，表示未初始化日志。
 */
+ (nullable PGLoggerConfig *)getLoggerConfigWithModuleId:(PGModuleId)moduleId;

/**
 * @brief 启动自动存储分配策略 (该方法只能在启动的第一次设置有效)
 * @param sizeLimit plog管理的所有日志的容量(每个模块至少需要64M的容量, 单位：byte)
 * @param timeLimit plog管理的所有日志的时间(如果为 0， 这表示不设置时间策略，单位：小时)
 * @note 该接口用于在测试期间增加日志清理的大小。不调用该接口也会执行默认清理策略（每个模块保留最近64M，7天的日志）
 * 建议只在Debug版本调用该接口
 */
+ (void)startAutoStoragingManagerWithSizeLimit:(NSUInteger)sizeLimit timeLimit:(NSUInteger)timeLimit;

@end

NS_ASSUME_NONNULL_END
