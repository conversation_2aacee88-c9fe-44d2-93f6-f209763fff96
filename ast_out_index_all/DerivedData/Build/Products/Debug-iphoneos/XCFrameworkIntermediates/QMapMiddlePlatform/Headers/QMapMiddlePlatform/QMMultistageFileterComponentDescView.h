//
//  QMMultistageFileterComponentDescView.h
//  XMFilter
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/27.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


@interface QMMultistageFileterComponentDescViewConfig : NSObject

/// 数据源
@property (nonatomic, copy  ) NSArray <NSString *> *descList;

/// 字体
@property (nonatomic, strong) UIFont *font;

/// 颜色
@property (nonatomic, strong) UIColor *color;

///
@property (nonatomic) NSInteger numberOfLines;

@property (nonatomic) NSAttributedString *attributedDesc;

@end


/// 描述信息  把 descList 用 | 拼接然后显示 如 [A, B, C] 则最终显示为 A | B | C
@interface QMMultistageFileterComponentDescView : UIView

/// 初始化描述信息
/// @param config 文字 配置信息
- (instancetype)initWithDescConfig:(QMMultistageFileterComponentDescViewConfig *)config;

+ (CGFloat)heightForConfig:(QMMultistageFileterComponentDescViewConfig *)config inWidth:(CGFloat)width;

@property (nonatomic) BOOL isNightStyle;

@end

NS_ASSUME_NONNULL_END
