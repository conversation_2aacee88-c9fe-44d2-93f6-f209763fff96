//
//  QMUnlockCityModel.h
//  QMapBaseline
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/4/22.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMUnlockCityButtonBGGradientlayerModel : NSObject

@property (nonatomic) NSString *startColor;
@property (nonatomic) NSString *endColor;

@end

@interface QMUnlockCityButtonBGColorModel : NSObject

@property (nonatomic) QMUnlockCityButtonBGGradientlayerModel *light;
@property (nonatomic) QMUnlockCityButtonBGGradientlayerModel *dark;

@end

@interface QMUnlockCityButtonDayNightModel : NSObject

@property (nonatomic) NSString *light;
@property (nonatomic) NSString *dark;

@end

@interface QMUnlockCityButtonTextModel : NSObject

@property (nonatomic) NSString *textContent;
@property (nonatomic) QMUnlockCityButtonDayNightModel *color;

@end


@interface QMUnlockCityButtonsModel : NSObject

@property (nonatomic) QMUnlockCityButtonTextModel *buttonText;
@property (nonatomic) QMUnlockCityButtonDayNightModel *leftIcon;
@property (nonatomic) QMUnlockCityButtonBGColorModel *bgColor;
@property (nonatomic) NSString *jumpUrl;

@end

@interface QMUnlockCityConfigModel : NSObject

@property (nonatomic) NSInteger autoCloseSeconds;
@property (nonatomic) NSArray<QMUnlockCityButtonsModel *> *bottomButtons;

@end

@interface QMUnlockCityModel : NSObject

/// 城市名称
@property (nonatomic) NSString *cityName;
/// 城市区域图
@property (nonatomic) NSString *region;

@property (nonatomic) QMUnlockCityConfigModel *unlockCityConfig;
/// 城市区域图
@property (nonatomic) NSString *arrivalTime;

/// 城市区域图
@property (nonatomic) NSString *background;

@property (nonatomic) NSString *interactionReportId;

@property (nonatomic) NSUInteger unlockIndex;

/// 用户设置的背景图url
@property (nonatomic) NSString *userBackground;
@property (nonatomic, copy) NSString *iconUrl;
@property (nonatomic, copy) NSString *nickName;

@property (nonatomic) UIImage *backgroundImg;

// 是否圆角
@property (nonatomic) BOOL isCorner;

// 是否展示新UI
@property (nonatomic) BOOL showNewUI;

@property (nonatomic) NSInteger localTouchTag;

// 是否接入互动的 quick
@property (nonatomic) BOOL isQuick;
// quick场景的埋点上报
@property (nonatomic) NSString *sceneName;

@end

NS_ASSUME_NONNULL_END
