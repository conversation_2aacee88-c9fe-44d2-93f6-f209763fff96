//
//  QMCustomAccessoryView.h
//  QMapMiddlePlatform
//
//  Created by 狄弘辉 on 2022/5/4.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapFoundation/QMModel.h>
#import <DragonMapKit/DragonMapKit.h>
#import "QMAccessoryViewDelegate.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMCustomAccessoryViewDisplayType) {
    QMCustomAccessoryViewDisplayTypeOneLine = 0,
    QMCustomAccessoryViewDisplayTypeTwoLines,
};

@interface QMCustomAccessoryViewDataSource : QMModel

/// 图标url
@property (nonatomic, copy) NSString *iconUrl;
/// 标题
@property (nonatomic, copy) NSString *title;
/// 标题颜色
@property (nonatomic, copy) NSString *titleColor;
/// 子标题
@property (nonatomic, copy) NSString *subTitle;
/// 子标题颜色
@property (nonatomic, copy) NSString *subTitleColor;
/// 是否展示关闭按钮
@property (nonatomic, assign) BOOL supportClose;
/// 跳转链接
@property (nonatomic, copy) NSString *actionUrl;
/// 展示类型(模板)
@property (nonatomic, assign, readonly) QMCustomAccessoryViewDisplayType displayType;
/// 是否展示左侧图标
@property (nonatomic, assign, readonly) BOOL supportShowIcon;

@end

@class QMCustomAccessoryView;

@protocol QMCustomAccessoryViewDelegate <NSObject>

@optional
- (void)accessoryViewClick:(QMCustomAccessoryView *)view;
- (void)accessoryViewClose:(QMCustomAccessoryView *)view;

@end

@interface QMCustomAccessoryView : DMMarkerAccessoryView

@property (nonatomic, strong, readonly) QMCustomAccessoryViewDataSource *dataSource;

- (instancetype)init __unavailable;
- (instancetype)initWithDelegate:(id<QMAccessoryViewDelegate>)delegate
                      dataSource:(QMCustomAccessoryViewDataSource *)dataSource;

@end

NS_ASSUME_NONNULL_END
