//
//  QMPopupAlertWithRemindView.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/20.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMPopupAlertWithRemindView : UIView

/// 点击蒙版是否关闭弹框
@property (nonatomic) BOOL dismissOnClick;
+ (instancetype)alertWithTitle:(NSString *)title;
+ (instancetype)alertWithTitle:(NSString *)title subTitle:(nullable NSString *)subTitle;
- (void)addConfirmActionWithTitle:(NSString *)confirmTitle action:(nullable void(^)(void))confirmAction;
- (void)addCancelActionWithTitle:(NSString *)cancelTitle action:(nullable void(^)(void))cancelAction;

//colors为添加按钮的渐变颜色值
- (void)addConfirmActionWithTitle:(NSString *)confirmTitle
                           colors:(NSArray *)colors
                           action:(void (^)(void))confirmAction;

// 添加key
- (void)addNotRemindKey:(NSString *)notRemindKey
           showCountKey:(NSString *)showCountKey
            showDateKey:(NSString *)showDateKey;
- (void)show;
- (void)dismiss;

+ (void)dismissAlertView;

@end

NS_ASSUME_NONNULL_END
