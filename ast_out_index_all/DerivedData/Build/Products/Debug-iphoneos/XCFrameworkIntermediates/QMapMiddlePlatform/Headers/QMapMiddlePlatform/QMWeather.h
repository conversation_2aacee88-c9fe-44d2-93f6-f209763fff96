//
//  QMWeather.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 2018/1/17.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMWeatherModel.h"

@interface QMWeather : NSObject


-(instancetype)initWithJCEWeatherInfo:(QMJCE_MapSSO_WeatherInfo *)info;

@property(nonatomic, readonly)NSInteger maxTemperature;
@property(nonatomic, readonly)NSInteger minTemperature;
@property(nonatomic, readonly)NSInteger realTimeTemperature;
@property(nonatomic, readonly)NSString* city;
@property(nonatomic, readonly)NSString* weatherState;
@property(nonatomic, readonly)NSInteger statCode;
@property(nonatomic, readonly)NSString* pictureUrl;
@property(nonatomic, readonly)NSString* backgroundUrl;
@property (nonatomic) BOOL unusual;  //恶劣天气为true - 新接口(json)

@property (nonatomic) BOOL selected; 

@property(nonatomic, readonly)NSInteger adCode;
@property(nonatomic, readonly)NSString *icon;
@property(nonatomic, readonly)NSString *tipsBgDay;
@property(nonatomic, readonly)NSString *tipsBgNight;
@property(nonatomic, readonly)NSInteger unusualWeather;

/// 位置
@property (nonatomic, assign) CLLocationCoordinate2D coor;


-(NSDictionary* )weatherDic;

- (instancetype)initWithModel:(QMWeatherDataModel *)model;

- (instancetype)initWithAlongDic:(NSDictionary *)dic;

@end
