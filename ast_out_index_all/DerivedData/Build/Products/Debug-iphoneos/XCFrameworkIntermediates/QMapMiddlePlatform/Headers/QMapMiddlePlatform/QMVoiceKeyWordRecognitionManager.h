//
//  QMVoiceKeyWordRecognitionManager.h
//  QMapMiddlePlatform
//
//  Created by 张敬民 on 2024/5/26.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, QMDingDangKeyWordRecognitionSceneType) {
    QMDingDangKeyWordRecognitionSceneTypeCarNavi,  // 驾车导航
    QMDingDangKeyWordRecognitionSceneTypeTruckNavi,  // 货车导航
    QMDingDangKeyWordRecognitionSceneTypeWalkCycleNavi,  // 步骑行
};

/// compaired 是否匹配上
typedef void(^QMDingDangKeyWordRecognitionCompletionBlock)(BOOL compaired, NSString *keyWord);


@interface QMKeyWordRecognitionModel : NSObject <YYModel>

@property (nonatomic) NSArray *keywords;

@property (nonatomic) QMDingDangKeyWordRecognitionSceneType scene;

@end

@interface QMVoiceKeyWordRecognitionManager : NSObject

- (void)keyWordRecognizeWithContent:(NSString *)keyWord
                              scene:(QMDingDangKeyWordRecognitionSceneType)sceneType
                          completed:(QMDingDangKeyWordRecognitionCompletionBlock)completedBlock;

@end

NS_ASSUME_NONNULL_END
