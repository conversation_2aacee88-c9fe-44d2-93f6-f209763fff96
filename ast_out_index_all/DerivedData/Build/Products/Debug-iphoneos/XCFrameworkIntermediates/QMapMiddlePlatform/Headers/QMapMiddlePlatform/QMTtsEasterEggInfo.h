//
//  QMTtsEasterEggInfo.h
//  QMapMiddlePlatform
//
//  Created by 江航 on 2022/4/14.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

//彩蛋类型
typedef NS_ENUM(NSInteger, QMTtsEasterEggType) {
    QMTtsEasterEggTypeBeforeNavi,       //导航前彩蛋
    QMTtsEasterEggTypeNavi,             //导航中彩蛋
};

//单个彩蛋包配置内容
@interface QMTtsEasterEggInfo : NSObject
//开始导航mp3
@property (nonatomic, strong) NSString *startVoiceUrl;
//结束导航mp3
@property (nonatomic, strong) NSString *endNavVoiceUrl;
/**结束导航mp3数组*/
@property (nonatomic, copy) NSArray<NSString *> *endNavVoiceUrls;
//空闲时段mp3
@property (nonatomic, strong) NSString *freeTimeVoiceUrl;
/**空闲时段mp3数组*/
@property (nonatomic, copy) NSArray<NSString *> *freeTimeVoiceUrls;
//开始导航前mp3
@property (nonatomic, strong) NSString *navBeforeVoiceEggUrl;
/**开始导航前mp3数组*/
@property (nonatomic, copy) NSArray<NSString *> *navBeforeVoiceEggUrls;
//空闲距离
@property (nonatomic) NSInteger freeTimeDistance;
//单条彩蛋疲劳度控制，天频
@property (nonatomic) NSInteger dayFrequency;
//标识
@property (nonatomic, strong) NSString *identifier;
//支持的语音ID数组，行中开始导航后彩蛋只对填了的生效，开始导航前彩蛋不填全部生效
@property (nonatomic, copy) NSArray<NSString *> *enableVoiceIds;
/**支持的语音ID*/
@property (nonatomic) NSInteger voiceId;
//支持的主题ID，行中开始导航后彩蛋配置
@property (nonatomic, assign) NSInteger themeId;
//支持的主题ID数组，开始导航前彩蛋配置，不填对所有主题都生效
@property (nonatomic, copy) NSArray<NSString *> *enableThemeIds;
/***/
@property (nonatomic) NSString *playText;

//初始化方法
- (instancetype)initWithDic:(NSDictionary *)dic type:(QMTtsEasterEggType)type
               dayFrequency:(NSInteger)dayFrequency freeTimeDistance:(NSInteger)freeTimeDistance;
//彩蛋当前是否生效，判断内容包括当前主题/语音
- (BOOL)isValidForCurThemeAndcurVoice;
//下载mp3压缩包
- (void)downloadMp3IfNeed;
//对外暴露开始导航前彩蛋音频路径
- (NSString *)beforeNaviEggPath;
//对外暴露开始导航语音彩蛋路径
- (NSString *)startNaviEggPath;
//对外暴露空闲语音彩蛋路径
- (NSString *)freeTimeEggPath;
//对外暴露导航结束语音彩蛋路径
- (NSString *)stopNaviEggPath;
@end

NS_ASSUME_NONNULL_END
