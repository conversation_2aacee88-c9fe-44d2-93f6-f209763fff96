//
//  DMPOIMarker.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/4/14.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DMPOIMarker : NSObject

/// 中心 Marker
@property (nonatomic, strong) DMCommonMarker *centerMarker;

/// 四周文字
@property (nonatomic, strong) DMGroupMarker *groupMarker;

@property (nonatomic, strong) DMGroupMarker *callOutGroupMarker;

// 关联id
@property (nonatomic, copy) NSString *relationId;

@property (nonatomic, copy) NSString *themeType;

// 隐藏
@property (nonatomic, assign) BOOL hidden;

+ (instancetype)poiMarkerInPOIMarkers:(NSArray<DMPOIMarker *> *)markers
                                     withJsonPOIInfo:(JsonPOIInfo *)jsonInfo;

+ (JsonPOIInfo *)jsonPOIInfoInPOIMarkers:(NSArray<DMPOIMarker *> *)markers
                             withMarker:(DMMarkerElement *)marker;

@end

@interface DMMapView (POIMarker)

- (void)addPOIMarker:(DMPOIMarker *)poiMarker;

- (void)addPOIMarkers:(NSArray<DMPOIMarker *> *)poiMarkers;

- (void)removePOIMarker:(DMPOIMarker *)poiMarker;

- (void)removePOIMarkers:(NSArray<DMPOIMarker *> *)poiMarkers;



@end

NS_ASSUME_NONNULL_END
