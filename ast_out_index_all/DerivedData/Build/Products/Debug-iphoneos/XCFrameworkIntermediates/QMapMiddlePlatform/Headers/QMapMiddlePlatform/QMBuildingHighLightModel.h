//
//  QMBuildingHighLightModel.h
//  QMapMiddlePlatform
//
//  Created by re<PERSON><PERSON><PERSON><PERSON> on 2024/5/9.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMBuildingHighLightModel : NSObject

@property (nonatomic) DMapColor spotColor;
@property (nonatomic) DMapColor darkSpotColor;
@property (nonatomic) DMapColor blurColor;
@property (nonatomic) DMapColor spotEdgeLineColor;
@property (nonatomic) DMapColor nightSpotEdgeLineColor;
@property (nonatomic) DMapInt32 spotLightAngle;
@property (nonatomic) DMapFloat spotEdgeLineWidth;
@property (nonatomic) DMapFloat nightSpotEdgeLineWidth;
@property (nonatomic) DMapFloat lightHeight;
@property (nonatomic) DMapColor roofColor;
@property (nonatomic) DMapColor darkRoofColor;
@property (nonatomic) DMapColor wallColor;
@property (nonatomic) DMapColor darkWallColor;
@property (nonatomic) DMapInt32 mixMode;

@end

NS_ASSUME_NONNULL_END
