//
//  QMMotorUtils.h
//  QMapMiddlePlatform
//
//  Created by botaomao on 2025/3/17.
//  Copyright © 2025 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import <QMapMiddlePlatform/QMMotorLicensePlateInfo.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMMotorUtils : NSObject

// 根据当前用户摩托车信息更新摩托车算路参数
+ (void)updateSearchParamByMotorInfo:(QMotorRouteReqParam *)motorParam;
+ (void)updateSearchParamWithMotorPlate:(QMMotorLicensePlateInfo *)plate motorParam:(QMotorRouteReqParam *)motorParam;

@end

NS_ASSUME_NONNULL_END
