//
//  QMCommonTableSectionModel.h
//  SOSOMap
//
//  Created by 刘宁 on 2017/6/26.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
typedef UIView * (^QMCommonSectionViewRenderBlock)(NSInteger section, UITableView *tableView);

@interface QMCommonTableSectionModel : NSObject
@property (nonatomic, strong)NSMutableArray* mCellArray;
@property (nonatomic, copy)NSString* headerTitle;
@property (nonatomic, copy)NSString* footTitle;
@property (nonatomic, assign) CGFloat headerHeight;
@property (nonatomic, assign) CGFloat footHeight;
@property (nonatomic, strong) UIView *headerView;  // section header view
@property (nonatomic, strong) UIView *footerView;  // section footer view
@property (nonatomic, copy)QMCommonSectionViewRenderBlock headerViewBlock;
@property (nonatomic, copy)QMCommonSectionViewRenderBlock footViewBlock;
@end
