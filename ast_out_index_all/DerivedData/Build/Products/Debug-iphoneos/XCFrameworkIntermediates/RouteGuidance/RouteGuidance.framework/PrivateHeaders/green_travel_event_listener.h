//
// Created by davishuang on 2020/11/9.
//

#ifndef ROUTEGUIDANCE_INCLUDE_GREEN_TRAVEL_EVENT_LISTENER_H_
#define ROUTEGUIDANCE_INCLUDE_GREEN_TRAVEL_EVENT_LISTENER_H_

#include "guidance_interface_structure.h"
#include "green_travel_interface_structure.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

/**
 * 绿色出行事件监听器，步骑行公交事件监听器的基类
 */
class GreenTravelEventListener {
 public:
  virtual ~GreenTravelEventListener() = default;
  /**
   * @brief 播报语音
   * @param tts 语音内容
   * @return 是否播报成功，ture-成功，false-失败
   */
  virtual bool OnTTSPlay(const PlayTtsInfo& tts) { return true; };
  /**
   * @brief 偏航
   * @note 步骑偏航时不需要回流偏航信息，诱导只透传吸附偏航状态
   */
  virtual void OnOffCourse() {};
  /**
   * @brief 到达目的地
   */
  virtual void OnArrivalDestination() {};
  /**
   * @brief  诱导状态更新
   * @note 透出了SetMatchedPoint Out数据
   * @param basic_guidance_list
   */
  virtual void OnSegmentUpdate(const GreenTravelUpdateInfo &basic_guidance_list){};

  /**
   * @brief 吸附失败
   * @param match_failed_info 吸附失败信息
   */
  virtual void OnMatchFailed(const MatchFailedInfo& match_failed_info){};

  /**
   * @brief 吸附失败恢复
   */
  virtual void OnHideMatchFailed(){};

  /**
   * 速度异常
   */
  virtual void OnVelocityAnomaly(){};

  /**
   * 诱导透出信息，公交在每次OnPlayTTS后回调用于透传埋点key
   * @param info 透出信息
   */
  virtual void OnRGOutputInfo(const RGOutputInfo& info){};

  /**
   * 关键poi前显示特效
   * @param info 关键poi信息
   */
  virtual void OnKeyPoiShow(const KeyPoiInfo& info){};
  /**
   * 关键poi后隐藏特效
   */
  virtual void OnKeyPoiHide(){};
  /**
   * 里程碑marker前显示动画
   * @param info 里程碑信息
   */
  virtual void OnMileStoneShow(const MileStoneInfo& info){};
  /**
   * 里程碑marker后隐藏动画
   */
  virtual void OnMileStoneHide(){};
  /**
   * 到达途径点
   * @param via 途径点
   */
  virtual void OnArrivalVia(const ViaArrivalInfo& via) {};
  /**
   * 主路线途经点剩余信息秒级更新；<br>
   * 注意：主路线没有途经点或最后一个途经点判达后，会每秒回调空list。客户端可借助这个停止更新eta
   * @param viaList 主路线所有没判达的途经点剩余信息，若非空，get(0)获取下个途经点
   */
  virtual  void OnViaRemainInfoUpdate(const std::vector<mapbase::RGViaRemainInfo> &via_list) {};

    /**
     * 红绿灯倒计时请求时机（in-开始轮询请求，out-结束轮询），用于客户端请求变灯倒计时（LightStatus）或入队（LightQueue）数据
     * @param info  红绿灯倒计时请求依赖信息
     */
  virtual void OnRedLightCountDown(const mapbase::RedLightInfo& info) {};
};

ROUTE_GUIDANCE_NAMESPACE_END

#endif //ROUTEGUIDANCE_INCLUDE_GREEN_TRAVEL_EVENT_LISTENER_H_