/////////////////////////////////////////////////////////////////////////////////
/// Copyright (C), 2015-2025.
/// \file  
/// \brief 公交车导航头文件
/// \author paldinzhang
/// \version 1.0
/// \date    2015.11
/// \warning 
///		1,回调命令复用QRouteGuidanceApi.h中的
///		2,VoiceParam复用
///		3,语音优先级复用
///		4,routeforwhat复用
/// \ClassList:
/// 1. ....
/// \History: 修改历史记录列表，每条修改记录应包括修改日期、修改者及修改内容简述
///                
///		paldinzhang  2015/11/24     1.0     build this moudle 
////////////////////////////////////////////////////////////////////////////////////

#ifndef _QBUSGUIDANCEAPI_H
#define _QBUSGUIDANCEAPI_H
#include "guidance_data_def.h"

ROUTE_GUIDANCE_FUNCTION_BEGIN

	typedef RouteGuidanceMapPoint BusGuidanceMapPoint;
	typedef RouteGuidanceGPSPoint BusGuidanceGPSPoint;
	
	//线路信息
	typedef struct RG_EXPORT  
	{
		unsigned short name[256];		// IN 线路名称
		unsigned short from[256];		// IN 线路起点站方向名称
		unsigned short end[256];		// IN 线路终点站方向名称
		int preTransIndex;				// IN 关联的前一换乘信息在数组中的索引
		int postTransIndex;				// IN 关联的后一换乘信息在数组中的索引
		int lineproperty;				// IN 0-公交 1-地址
	}BusGuidanceLineInfo;
	
	//换乘信息
	typedef struct RG_EXPORT  
	{
		int walkDistance;					// IN 换乘站步行距离
		unsigned short getoffExitName[256];	// IN 下车出口名称
		unsigned short getonExitName[256];	// IN 上车站出口名称
	}BusGuidanceTransInfo;
	/*
	intersection
	0,上车站
	1,下车站
	2,下车前一站
	*/
	typedef struct RG_EXPORT 
	{
		int segmentIndex;					// IN 事件所在索引
		unsigned short curStationName[256];	// IN 当前车站名
		BusGuidanceMapPoint mapPoint;		// IN
		int lineIndex;						// IN 对应line数组中索引

		int innerState;						// RESERVED
		unsigned short nextStationName[256];// RESERVED 下一站车站名
		int intersection;					// RESERVED 事件类型

		int distance;						// OUT
		int totalDistanceLeft;				// OUT
	}BusGuidanceEventPoint;

	typedef int(*QBusGuidanceCallback)(void *context, int action, void *textParam, int textParamSize, void *voiceParam, int voiceParamSize);

	RG_EXPORT void *QBusGuidanceCreate();
	RG_EXPORT void QBusGuidanceDestroy(void *rgHandle);
	RG_EXPORT void QBusGuidanceSetCallback(void *rgHandle, QBusGuidanceCallback callback, void *callbackContext);
	RG_EXPORT int QBusGuidanceSetMapPoints(void *rgHandle, unsigned short *route_id, BusGuidanceMapPoint *mapPoints, int mapPointsCount
		, BusGuidanceEventPoint *eventPoints, int eventPointsCount
		, BusGuidanceLineInfo *lineInfo, int lineInfoCount
		, BusGuidanceTransInfo *transInfo, int transInfoCount
		, int routeForWhat
		, int totalDistance
		, int totalTime);

	RG_EXPORT void QBusGuidanceSetGPSPoint(void *rgHandle, BusGuidanceGPSPoint *gpsPoint, BusGuidanceGPSPoint *matchedPoint, BusGuidanceEventPoint *nextEventPoint, enum VOICE_SETTING voiceSetting);
	RG_EXPORT int QBusGuidanceDistanceToPoint(void *rgHandle, const BusGuidanceGPSPoint *gpsPoint, int segmentIndex, BusGuidanceMapPoint mapPoint);
	RG_EXPORT int QBusGuidanceDistanceToBeginPoint(void *rgHandle);
	RG_EXPORT int QBusGuidanceDistanceToRouteEnd(void *rgHandle, int pointIndex, RGMapPoint mapPoint);

	RG_EXPORT void QBusGuidanceSetGPSTolerantRadius(void *rgHandle, int radius);

	RG_EXPORT void QBusGuidanceClear(void *rgHandle);

ROUTE_GUIDANCE_FUNCTION_END

#endif
