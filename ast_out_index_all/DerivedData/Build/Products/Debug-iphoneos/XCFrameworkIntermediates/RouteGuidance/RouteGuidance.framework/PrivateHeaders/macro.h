#ifndef ROUTEGUIDANCE_BASE_MACRO_H_
#define ROUT<PERSON>UIDANCE_BASE_MACRO_H_

#if defined(ROUTE_GUIDANCE_PLAN_B)
#define ROUTE_GUIDANCE_NAMESPACE route_guidance_b
#define ROUTE_GUIDANCE_NO_C_FUNCTION
#endif

#if !defined(ROUTE_GUIDANCE_NAMESPACE) && !defined(NO_ROUTE_GUIDANCE_NAMESPACE)
#define ROUTE_GUIDANCE_NAMESPACE route_guidance
#endif

#if defined(__cplusplus) && defined(ROUTE_GUIDANCE_NAMESPACE)
#define USING_ROUTE_GUIDANCE_NAMESPACE using namespace ROUTE_GUIDANCE_NAMESPACE;
#define ROUTE_GUIDANCE_NAMESPACE_BEGIN namespace ROUTE_GUIDANCE_NAMESPACE {
#define ROUTE_GUIDANCE_NAMESPACE_END }
#define USING_ROUTE_GUIDANCE_NAMESPACE_VALUE(x) using ROUTE_GUIDANCE_NAMESPACE::x;
#else // defined(ROUTE_GUIDANCE_NAMESPACE)
#define USING_ROUTE_GUIDANCE_NAMESPACE
#define ROUTE_GUIDANCE_NAMESPACE_BEGIN
#define ROUTE_GUIDANCE_NAMESPACE_END
#define USING_ROUTE_GUIDANCE_NAMESPACE_VALUE(x)
#endif // defined(ROUTE_GUIDANCE_NAMESPACE)

#if defined(__cplusplus)
#if defined(ROUTE_GUIDANCE_NO_C_FUNCTION) && defined(ROUTE_GUIDANCE_NAMESPACE)
#define ROUTE_GUIDANCE_FUNCTION_BEGIN ROUTE_GUIDANCE_NAMESPACE_BEGIN
#define ROUTE_GUIDANCE_FUNCTION_END ROUTE_GUIDANCE_NAMESPACE_END
#define ROUTE_GUIDANCE_FUNCTION_NAMESPACE ROUTE_GUIDANCE_NAMESPACE
#else // defined(ROUTE_GUIDANCE_NO_C_FUNCTION)
#define ROUTE_GUIDANCE_FUNCTION_BEGIN extern "C" {
#define ROUTE_GUIDANCE_FUNCTION_END }
#define ROUTE_GUIDANCE_FUNCTION_NAMESPACE
#endif // defined(ROUTE_GUIDANCE_NO_C_FUNCTION)
#else // __cplusplus
#define ROUTE_GUIDANCE_FUNCTION_BEGIN
#define ROUTE_GUIDANCE_FUNCTION_END
#define ROUTE_GUIDANCE_FUNCTION_NAMESPACE
#endif // __cplusplus

ROUTE_GUIDANCE_NAMESPACE_BEGIN
ROUTE_GUIDANCE_NAMESPACE_END
USING_ROUTE_GUIDANCE_NAMESPACE

#if defined(_WIN32)

#if defined(routeguidance_EXPORTS)
#define RG_EXPORT __declspec(dllexport)
#else
#define RG_EXPORT __declspec(dllimport)
#endif  // defined(routeguiance_EXPORTS)

#else  // defined(WIN32)
#if defined(routeguidance_EXPORTS)
#define RG_EXPORT __attribute__((visibility("default")))
#else
#define RG_EXPORT
#endif  // defined(routeguidance_EXPORTS)
#endif

#endif // ROUTEGUIDANCE_BASE_MACRO_H_
