#ifndef ROUTEGUIDANCE_CARENGINE_GUIDANCE_DEF_H
#define ROUTEGUIDANCE_CARENGINE_GUIDANCE_DEF_H

#include <stdint.h>

#include "macro.h"

ROUTE_GUIDANCE_FUNCTION_BEGIN

// 引擎发版时，诱导同学请务必确认云播版本号已更新
static const int RG_CLOUD_VER = 26;

#define NAVI_MODE_CAR 1
#define NAVI_MODE_BICYCLE 2
#define NAVI_MODE_PEDESTRIAN 3
#define NAVI_MODE_CRUISE 4

#define SPEED_FACTOR 1.03 //引擎内部将大定位GPS速度统一乘以系数，协调均方差

#define GETMAXWHEN0(x) (((x) == 0) ? (INT_MAX) : (x))

//typedef int(*QRouteGuidanceCallback)(void *context, int action, void *textParam, int textParamSize, void *voiceParam, int voiceParamSize);
typedef int(*QRouteGuidanceCallbackNative)(void *context, int action, void *textParam, int textParamSize, void *voiceParam, int voiceParamSize);

typedef enum {
    kRouteGuidanceEventTypeNone = 0,
    kRouteGuidanceEventTypeIntersection = 1,
    kRouteGuidanceEventTypeSpecialguidance = 20,

    kRouteGuidanceEventTypeMax = 0xFFFFFFFF
} RouteGuidanceEventPointType;

typedef enum {
    kRouteGuidanceAccessoryTypeNone = 0,
    kRouteGuidanceAccessoryTypeGasStation = 1,
    kRouteGuidanceAccessoryTypeSeatingArea = 2,
    kRouteGuidanceAccessoryTypeCamera = 4,
    kRouteGuidanceAccessoryTypeTollStation = 5,
    kRouteGuidanceAccessoryTypeJunctionPoint = 6,
    kRouteGuidanceAccessoryTypeTunnelEntrance = 7,
    kRouteGuidanceAccessoryTypeForceGuidance = 8,
    kRouteGuidanceAccessoryTypeRoundaboutExit = 9,
    kRouteGuidanceAccessoryTypeWarningSigns = 10,
    kRouteGuidanceAccessoryTypeStraight = 11,
    kRouteGuidanceAccessoryTypeICJCT = 12,
    kRouteGuidanceAccessoryTypeInnerRoad = 13,
    kRouteGuidanceAccessoryTypeSpecialGuidance = kRouteGuidanceAccessoryTypeWarningSigns + 10,

    kRouteGuidanceAccessoryTypeMax = 0xFFFFFFFF
} RouteGuidanceAccessoryType;

typedef enum {
    kAccessoryCameraRedLight = 1,               //闯红灯照相
    kAccessoryCameraElectronicMonitoring = 2,   //电子监控
    kAccessoryCameraFixedSpeedTraps = 3,        //固定测速点
    kAccessoryCameraMobileSpeedZone = 4,        //移动测速区
    kAccessoryCameraBusOnlyWay = 5, // 公交
    kAccessoryCameraOneWay = 6, // 单行
    kAccessoryCameraEmergencyWay = 7, // 应急
    kAccessoryCameraNoneMotorWay = 8, // 非机动车
    KAccessoryCameraQujianEnter = 9,//区间测速进入点
    KAccessoryCameraQujianExit = 10,//区间测速退出点
    KAccessoryCameraHOV = 16, //多乘员专用道监控.
    KAccessoryCameraLaLian = 17, //拉链通行道路监控.


    // show but not broadcast
    KAccessoryCameraTailNumber = 21,//尾号限行，
    KAccessoryCameraGoToBeijing = 22,//外地车进京监控,
    KAccessoryCameraIllegalBlow = 23, //违法鸣笛摄像头
    KAccessoryCameraBusStation = 24, //公交车站摄像头
    KAccessoryCameraForbiddenTure = 30, //禁止左右转
    KAccessoryCameraForbiddenLine = 31, //违反禁止标线
    KAccessoryCameraForbiddenParking = 32, //违章停车
    KAccessoryCameraLowestSpeed = 33, //超低速
    KAccessoryCameraAlterableSpeed = 34, //可变限速
    KAccessoryCameraLaneSpeed = 35, //分车道限速
    KAccessoryCameraVehicelTypeSpeed = 36, //分车种限速
    KAccessoryCameraLaneOccupy = 37, //违章占车道
    KAccessoryCameraCrossing = 38, //违章过路口
    KAccessoryCameraForbiddenSign = 39, //违反禁令标志    
    KAccessoryCameraForbiddenLight = 40, //违规用灯   
    KAccessoryCameraLifeBelt = 41, //不系安全带 
    KAccessoryCameraForbiddenCall = 42, //开车打手机
    KAccessoryCameraLimitLine = 43, //环保限行
    KAccessoryCameraPedestrainFirst = 44, //礼让行人
    KAccessoryCameraAnnualInpection = 45, //车辆未按规定年检
    KAccessoryCameraVehicelExhaust = 46, //车辆尾气超标
    KAccessoryCameraTraffic = 47, //路况监控
    KAccessoryCameraEntrance = 48, //出入口摄像头
    KAccessoryCameraForbiddenUTurn = 49, //禁止掉头摄像头
    KAccessoryCameraVideo = 52      //视频摄像头
} ConcreteCameraType;


enum INTERSECTION_TYPE {
    INTERSECTION_NONE = 0,
    INTERSECTION_STRAIGHT = 1,
    INTERSECTION_TURN_LEFT = 2,
    INTERSECTION_TURN_RIGHT = 3,
    INTERSECTION_TURN_AROUND = 4,
    INTERSECTION_ENTER_THE_ROUNDABOUT = 5, // 环岛入口

    INTERSECTION_DRIVE_IN_LEFT_SIDE = 6,
    INTERSECTION_DRIVE_IN_RIGHT_SIDE = 7,
    INTERSECTION_STRAIGHT_IN_MIDDLE = 8,

    INTERSECTION_BEND_LEFT = 10,
    INTERSECTION_ENTER_MAIN_ROAD = 11,
    INTERSECTION_KEEP_LEFT = 12,
    INTERSECTION_KEEP_LEFT2 = 13,

    INTERSECTION_ENTER_LEFT_FORK = 14,
    INTERSECTION_ENTER_MIDDLE_FORK = 15,
    INTERSECTION_FOURWAY_ROAD_LEFT = 16, //四岔路口最左侧岔路
    INTERSECTION_FOURWAY_ROAD_LEFT2 = 17, //四岔路口左边第二岔路
    INTERSECTION_STRAIGHT_LEFT_SIDE = 18,

    INTERSECTION_BEND_RIGHT = 20,
    INTERSECTION_ENTER_SIDE_ROAD = 21,
    INTERSECTION_KEEP_RIGHT = 22,
    INTERSECTION_KEEP_RIGHT2 = 23,

    INTERSECTION_ENTER_RIGHT_FORK = 24,
    INTERSECTION_ENTER_MIDDLE_FORK2 = 25,
    INTERSECTION_FOURWAY_ROAD_RIGHT2 = 26, //四岔路口右边第二岔路
    INTERSECTION_FOURWAY_ROAD_RIGHT = 27, //四岔路口最右侧岔路
    INTERSECTION_STRAIGHT_RIGHT_SIDE = 28,

    INTERSECTION_SHARP_LEFT = 30,
    INTERSECTION_SHARP_LEFT2 = 31,

    INTERSECTION_TURN_LEFTLEFT = 32,
    INTERSECTION_TURN_LEFTRIGHT = 33,
    INTERSECTION_TURN_LEFTPSILEFT = 34,
    INTERSECTION_TURN_LEFTPSICENTER = 35,
    INTERSECTION_TURN_LEFTPSIRIGHT = 36,

    INTERSECTION_STRAIGHT_BEARLEFT = 37,

    INTERSECTION_ENTER_LEFTWITHLEFTBACK = 38,

    INTERSECTION_SHARP_RIGHT = 40,
    INTERSECTION_SHARP_RIGHT2 = 41,

    INTERSECTION_TURN_RIGHTLEFT = 42,
    INTERSECTION_TURN_RIGHTRIGHT = 43,
    INTERSECTION_TURN_RIGHTPSILEFT = 44,
    INTERSECTION_TURN_RIGHTPSICENTER = 45,
    INTERSECTION_TURN_RIGHTPSIRIGHT = 46,

    INTERSECTION_STRAIGHT_BEARRIGHT = 47,

    INTERSECTION_ENTER_RIGHTWITHRIGHTBACK = 48,

    INTERSECTION_TAKE_THE_FIRST_EXIT = 51,   //环岛出口
    INTERSECTION_TAKE_THE_SECOND_EXIT = 52,
    INTERSECTION_TAKE_THE_THIRD_EXIT = 53,
    INTERSECTION_TAKE_THE_FOURTH_EXIT = 54,
    INTERSECTION_TAKE_THE_FIFTH_EXIT = 55,
    INTERSECTION_TAKE_THE_SIXTH_EXIT = 56,
    INTERSECTION_TAKE_THE_SEVENTH_EXIT = 57,
    INTERSECTION_TAKE_THE_EIGHTH_EXIT = 58,
    INTERSECTION_TAKE_THE_NINTH_EXIT = 59,

    INTERSECTION_YOU_HAVE_REACHED_DESTINATION = 60,
    INTERSECTION_YOU_HAVE_REACHED_DESTINATION_LEFT = 61,
    INTERSECTION_YOU_HAVE_REACHED_DESTINATION_RIGHT = 62,
    INTERSECTION_VIA_POINT = 63,

    INTERSECTION_ENTER_TUNNEL = 64,
    INTERSECTION_UNDERPASS = 65,
    INTERSECTION_TOLL_GATE = 66,

    INTERSECTION_KEEP_STRAIGHT_ON_LEFT = 81,
    INTERSECTION_KEEP_STRAIGHT_ON_RIGHT = 82,
    INTERSECTION_TRIPLE_PATH_RIGHT_RIGHT = 83,
    INTERSECTION_TRIPLE_PATH_LEFT_LEFT = 84,


    INTERSECTION_LEFT_UTURN_ENTER_LEFTROAD = 85, //左掉头进入左侧道路
    INTERSECTION_LEFT_UTURN_ENTER_RIGHTROAD = 86, //左掉头进入右侧道路

    INTERSECTION_RIGHT_UTURN = 87, //右调头

    INTERSECTION_RIGHT_UTURN_ENTER_LEFTROAD = 88, //右掉头进入左侧道路
    INTERSECTION_RIGHT_UTURN_ENTER_RIGHTROAD = 89, //右掉头进入右侧道路
    INTERSECTION_LONG_DASHLINE_STRAIGHT = 90, //长距离虚直箭头

    //公交车导航相关
    INTERSECTION_BUSUP = 999,
    INTERSECTION_BUSDOWN,
    INTERSECTION_SPECIALGUIDANCE
};

typedef enum RouteGuidanceAccessoryInfoType{
    kRouteGuidanceAccessoryInfoTypeNone = 0,
    kRouteGuidanceAccessoryInfoTypeRingRoadEntrance = 1,    //进入匝道
    kRouteGuidanceAccessoryInfoTypeTurnSlotLeft = 2,        //进入左转专用道
    kRouteGuidanceAccessoryInfoTypeTurnSlotRight = 3,       //进入右转专用道
    kRouteGuidanceAccessoryInfoTypeMainRoadEntrance = 4,    //进入主路
    kRouteGuidanceAccessoryInfoTypeExitArrive = 5,          //到达出口
    kRouteGuidanceAccessoryInfoTypeMainRoadExit = 6,        //出主路
    kRouteGuidanceAccessoryInfoTypeFreewayEntrance = 7,     //进高速
    kRouteGuidanceAccessoryInfoTypeEnterSideRoad = 8		//进入辅路
} RouteGuidanceAccessoryInfoType;

enum VOICE_SETTING {
    AUTO_VOICE = 0,
    FORCE_NO_VOICE,
    FORCE_VOICE
};

typedef enum NAV_MODE_ {
    DEFAULT_NAV = 0, //导航
    LIGHT_NAV,  //路线探测
}NAV_MODE;

typedef enum TRAFFIC_UPDTATE_STATUS_{
    TUPDATE_UNKNOWN = 0,
    TUPDATE_SUCCESS, 
    TUPDATE_FAIL
}TRAFFIC_UPDTATE_STATUS;

typedef enum {
    SpTypeNone = 0,		// 无
    SpTypeTowards = 1,	// 方向
    SpTypeExit = 2,		// 出口
    SpTypeEntrance = 3,	// 入口
    SpTypeHighWay = 4,	// 高速设施
    SpTypeVia = 98,     // 途经点
    SpTypeDest = 99     // 终点
} RouteGuidanceSpType;

enum  seg_hint_e {
    SEG_HINT_NONE = 0, //无提示按钮
    SEG_HINT_ELEVATED = 1, //提示按钮：在桥上
    SEG_HINT_DOWNSTAIRS = 2, //提示按钮：在桥下
    SEG_HINT_MAIN_ROAD = 3, //提示按钮：在主路
    SEG_HINT_SERVING_ROAD = 4, //提示按钮：在辅路
    SEG_HINT_2_DIRECTION_ROAD = 5,//提示按钮：在对面
    SEG_HINT_DOWNSTAIRS_MAIN_ROAD = 6,//提示按钮：桥下主路
    SEG_HINT_DOWNSTAIRS_SERVING_ROAD = 7//提示按钮：桥下辅路
};

//同nav_OverSpeedStatus枚举值一一对应
enum CAMERA_OVER_SPEED_KIND                                  // 超速显示类型
{
    CAMERA_OVER_SPEED_KIND_DEFAULT = 0,                      // 默认值
    CAMERA_OVER_SPEED_KIND_NOT_OVER_SPEED = 1,               // 实时测速电子眼未超速
    CAMERA_OVER_SPEED_KIND_OVER_SPEED_TIPS = 2,              // 实时测速电子眼超速提示
    CAMERA_OVER_SPEED_KIND_DANGEROUS_SPEED = 3,              // 实时测速电子眼危险驾驶
    CAMERA_OVER_SPEED_KIND_INTERVAL_AVERAGE_OVER_SPEED = 4,  // 区间测速平均速度超速
    CAMERA_OVER_SPEED_KIND_NULL = 5,                         // 不返回速度 --
    CAMERA_OVER_SPEED_KIND_HIDE = 6,                         // 不显示超速小红圈
};

enum OVER_SPEED_TYPE
{
    OVER_SPEED_TYPE_NONE = -1,
    OVER_SPEED_TYPE_CAMERA = 0,      // 电子眼超速
    OVER_SPEED_TYPE_ROAD = 1,        // 道路限速超速
};

#define ROUTE_FOR_UNKOWN		            0	// 模拟导航
#define ROUTE_FOR_NAVIGATION	            1	// 导航下发的路径
#define ROUTE_FOR_DEVIATION		            2	// 偏航下发的路径
#define ROUTE_FOR_SERVERCHANGE	            3	// 后台换路
#define ROUTE_FOR_TINGCHECHANG	            4	// 停车场算路
#define ROUTE_FOR_ADD			            5	// 动态换路调用的setmappoints+这个
#define ROUTE_FOR_ZHUFULU		            6	// 主辅路切换后的算路
#define ROUTE_FOR_PASSDIVERGENCEWITHCUR		7	// 伴随路线经过分歧点走的选中路线
#define ROUTE_FOR_PASSDIVERGENCEWITHCAN		8	// 伴随路线经过分歧点走的侯选路线
#define ROUTE_FOR_REFRESH					9	// 手动刷新伴随路线
#define ROUTE_FOR_ADDVIAPOINT               10  // 添加途经点
#define ROUTE_FOR_DELVIAPOINT               11  // 删除途经点
#define ROUTE_FOR_PREFCHANGED               12  // 改变路线偏好
#define ROUTE_FOR_SILENT_ADD                13  // 静默添加伴随路线
#define ROUTE_FOR_RECOMMENDEDPARKING        14  // 选择推荐停车场后刷新路线
#define ROUTE_FOR_CLOUD_SCENE_DEFAULT		10000	// 路线变更场景云控默认值
#define ROUTE_FOR_MAX						10001	// 最大值（无效值）

#define ENLARGE_TYPE_4K    130100       // 4K放大图

#define kRouteGuidanceEventActionNone							0
#define kRouteGuidanceEventActionPlayTTS						1
#define kRouteGuidanceEventActionOutWay							2
#define kRouteGuidanceEventActionShowMapEnlargement				5
#define kRouteGuidanceEventActionHideMapEnlargement				6
#define kRouteGuidanceEventActionArrivingDestination			7
#define kRouteGuidanceEventActionUpdateMapEnlargement			8	//刷新动态连续路口放大图
//#define kRouteGuidanceEventActionOverviewNavigation             8 // Not Use
#define kRouteGuidanceEventActionShowLaneInfo					9
#define kRouteGuidanceEventActionHideLaneInfo					10
//#define kRouteGuidanceEventActionShowCamera						11
#define kRouteGuidanceEventActionHideCamera						12
#define kRouteGuidanceEventActionFinished						13 // Not Use
#define kRouteGuidanceEventActionNewAction						14 // Not Use
//#define kRouteGuidanceEventActionShowCameraLive					15
//#define kRouteGuidanceEventActionHideCameraLive					16
#define kRouteGuidanceEventActionHideCameras                    17 //隐藏自车点后方电子眼
#define kRouteGuidanceEventActionEnlargeCamera                  18 //放大电子眼
//#define kRouteGuidanceEventActionCameraPassby					19
#define kRouteGuidanceEventActionTrafficEventVerify				20 //触发点事件众验
#define kRouteGuidanceEventActionArrivingViaPoint				21
#define kRouteGuidanceEventActionShowWarningSchool				22
#define kRouteGuidanceEventActionHideWarningSchool				23
#define kRouteGuidanceEventActionOverRoadSpeedLimitBegin		25
#define kRouteGuidanceEventActionOverRoadSpeedLimitEnd			26
#define kRouteGuidanceEventActionOpenTingchechangBar			27
#define kRouteGuidanceEventActionHideCameraPair					30
#define kRouteGuidanceEventActionRefuseRoute					31 // Not Use
#define kRouteGuidanceEventActionInFenceNotMatch				32
#define kRouteGuidanceEventActionOverCameraSpeedLimit			33 // 超速小红圈
#define kRouteGuidanceEventActionSpeedLimitChange				44
#define kRouteGuidanceEventActionArrivingTunnelEntrance			45 // Not Use
#define kRouteGuidanceEventActionPianhangtongji					46 //偏航统计回调 Not Use
#define kRouteGuidanceEventActionShowBubble				    	47
#define kRouteGuidanceEventActionHideBubble			     		48
#define kRouteGuidanceEventActionHideBubbleTEST			     	50 // Not Use
#define kRouteGuidanceEventActionSpecialGuidanceMaidian			51 //特殊诱导埋点
#define kRouteGuidanceEventActionGetOffTheCar        			52 //下车手势
//#define kRouteGuidanceEventActionArrivingFreeWay				53 //进入高速前150m给一个回调，用于滴滴播报系安全带
#define kRouteGuidanceEventActionOutWayTime						54 //偏航用时统计回调
#define kRouteGuidanceEventActionGetDayOrNight					55 //从客户端获取是白天还是黑夜,白天为0，夜晚非0
#define kRouteGuidanceEventActionGetWeather					    56 //获取天气回调
#define kRouteGuidanceEventActionRoadName					    57 //道路名回调
#define kRouteGuidanceEventActionRequestRoutesEta               58 //请求多条路线的ETA请求, Not Use
#define kRouteGuidanceEventActionPassServerDivergencePoint      59 //走过服务端返回的分歧点
#define kRouteGuidanceEventActionVelocityAnomaly                60 //速度异常回调
#define kRouteGuidanceEventActionBeforeRedLight                 61 //走到红绿灯前
#define kRouteGuidanceEventActionAfterRedLight                  62 //走过红绿灯后
#define kRouteGuidanceEventActionShowIconBeforeTollStation      63 //在收费站前打开图标和弹窗，现在小于600米
#define kRouteGuidanceEventActionBroadcastBeforeTollStation     64 //在收费站前让客户端播报语音
#define kRouteGuidanceEventActionPassTollStation                65 //过收费站，过了收费站50米内认过了
#define kRouteGuidanceEventActionShowCompanionBubble            66 //伴随3.0需求，离分歧点300米内满一定条件，发送打开气泡回调
#define kRouteGuidanceEventActionIntervalSpeedMonitoringInfo    67 //区间测速信息（位置、限速值等）
#define kRouteGuidanceEventActionIntervalSpeedMonitoringStatus  68 //区间测速状态（平均车速、剩余距离等）
#define kRouteGuidanceEventActionCamerasOnRoute                 69 //路线上电子眼序列
#define kRouteGuidanceEventActionCamerasID                      70 //触发播报的电子眼序号序列
#define kRouteGuidanceEventActionRequestTollFee                 71 //让客户端请求收费费用
#define kRouteGuidanceEventActionShowTollFeeTips                72 //让客户端显示收费tips
#define kRouteGuidanceEventActionShowIntersection               73 //让客户端显示箭头信息
#define kRouteGuidanceEventActionHideIntersection               74 //让客户端隐藏箭头信息
#define kRouteGuidanceEventActionCallCompanionReason            75 //让客户端唤起丁当，进行一些拥堵展示的操作，调用一遍
#define kRouteGuidanceEventActionSilentChangeMainRoute			76  //让客户端静默替换主路线
#define kRouteGuidanceEventActionSendRedLights					77 //给客户端发送红绿灯信息，对应回调内容jce：QRouteGuidanceGetRemainRedLightsOutParam
#define kRouteGuidanceEventActionHideDisplayItemsOnFishbone     78 //让客户端隐藏鱼骨上的元素
#define kRouteGuidanceEventActionRequestCruiserRoute            79 //让客户端请求新巡航路线
//#define kRouteGuidanceEventActionDisplayItemsOnFishbone         80 //回调鱼骨上的所有的显示事件
//#define kRouteGuidanceEventActionDisplayTrafficEventPoints      81 //回调所有点事件给盘古
#define kRouteGuidanceEventActionHideTrafficEventPoint          82 //经过某个点事件
#define kRouteGuidanceEventActionHideAllDisplayItemsOnFishbone  83 //巡航态偏航重新算路后，隐藏原路线鱼骨路上所有显示元素
#define kRouteGuidanceEventActionEnterIdleSection               84 //进入空闲区间
#define kRouteGuidanceEventActionUpdateCommonEnlargement        85 //刷新放大图
#define kRouteGuidanceEventActionNextBrInfo                     86 //透出后续放大图信息
#define kRouteGuidanceEventActionHighwayInstructionUpdate       100 // 高速设施状态更新
#define kRouteGuidanceEventActionHighwayInstructionHide         101 // 高速设施状态隐藏
#define kRouteGuidanceEventActionShowLightNavCamera             102 //显示路线探测电子眼
#define kRouteGuidanceEventActionHideLightNavCameras            103 //隐藏路线探测电子眼
#define kRouteGuidanceEventActionPassDivergencePoint			501 //经过分歧点，引擎通过偏航自行算出的分歧点
#define KRouteGuidanceEventActionWillEnterIntersection          601 //即将进入路口事件（第四轮播报或距离路口100以内）
#define KRouteGuidanceEventActionShowMarker                     602 //显示图钉
#define KRouteGuidanceEventActionLeaveIntersection              603 //已经离开路口事件
#define kRouteGuidanceEventActionShowExitInfo                   604 //显示出口信息
#define kRouteGuidanceEventActionHideExitInfo                   605 //隐藏出口信息
#define kRouteGuidanceEventActionDingdangQueryParking           606 //唤起叮当推荐停车场
#define kRouteGuidanceEventActionFluxRefluxData				    888 //回流数据刷新，这个回调比较特殊，确保放入最后 3Q
#define kRouteGuidanceEventActionMockedGps  				    999 // 隧道导航推算出来的GPS，此消息来自底层其他线程，需要端上转发到上层线程中
#define kRouteGuidanceEventActionSendMatchedPointOfCurRoute     1000 //发送当前吸附点的回调，目前公供spec，诱导回流平台使用
#define kRouteGuidanceActionMatchFailed							1001
#define kRouteGuidanceEventActionHideMatchFailed				1002
#define kRouteGuidanceEventActionSmartLocTrigger                1003  //开启关闭智能定位事件
#define kRouteGuidanceEventActionSmartLocStart                  10001 //启动智能定位标志
#define kRouteGuidanceEventActionSmartLocStop                   10002 //关闭智能定位标志
#define kRouteGuidanceEventActionTipsArriveStatus               10003 //AR步导，回调设施通过状态

#define kRouteGuidanceEventActionShortTermGPSWeak               2001  //普通GPS信号弱 信号弱持续5s
#define kRouteGuidanceEventActionShortTermGPSNormal             2002  //普通GPS信号弱恢复正常
#define kRouteGuidanceEventActionLongTermGPSWeak                2003  //GPS信号弱 信号弱持续60s
#define kRouteGuidanceEventActionLongTermGPSNormal              2004  //GPS信号弱持续超过60s后恢复正常

#define kRouteGuidanceEventActionShowTollAisles                 2101 //显示收费站通道,收费站前500m
#define kRouteGuidanceEventActionHideTollAisles                 2102 //隐藏收费站通道,过收费站或切换路线后

#define kRouteGuidanceEventActionOutPutInfo                     3000 //诱导透出信息回调
#define kRouteGuidanceEventActionRoadFeatureDetect              3001 //道路特征信息识别，包含智能倾角、路口时机、连续弯道场景类型信息
#define kRouteGuidanceEventActionShowSpEnhance                  3002 //显示方向看板增益信息
#define kRouteGuidanceEventActionHideSpEnhance                  3003 //隐藏方向看板增益信息
#define kRouteGuidanceEventActionEnterFamiliarSection           3004 //进入熟路区间
#define kRouteGuidanceEventActionLeaveFamiliarSection           3005 //离开熟路区间
#define kRouteGuidanceEventActionShowLightWaitBubble			3006
#define kRouteGuidanceEventActionHideLightWaitBubble			3007

#define PRIORITY_P0	0
#define PRIORITY_P1	1
#define PRIORITY_P2	2
#define PRIORITY_P3	3 // 只播报明星播报，不牵扯拼接语音
#define PRIORITY_P88 88 // 区域面结束标识

//禁止事件列表
#define FORBIDDENOUTWAY 0X0001
#define FORBIDDENARRIVINGDESTINATION 0x0002

enum
{
    VOICE_NONE = 0,
    VOICE_BEEP,
    VOICE_DENGDENG,
    VOICE_VIBRATION,
    VOICE_BUSALERTNORMAL,
    VOICE_BUSALERTLAST,
    VOICE_PASS_SPEED_CAMER,
    VOICE_DING_LING
};

enum
{
    SPECIALGUIDANCEMAI_TURN = 0,
    SPECIALGUIDANCEMAI_SINGLEPOINT,
    SPECIALGUIDANCEMAI_LANEINFO,
    SPECIALGUIDANCEMAI_ALLTEXT
};


enum TTSType
{
    TTSType_NONE = -1,
    FORCE_VOICE_TTS = 0,//强制播报
    OUTWAY = 1,//偏航
    REMIND_3RD_4TH_5TH_TIMES = 3,//转向 3、4或5轮播报
    CAMERA = 4,//电子眼播报
    REMIND_0TH_1ST_2ND_TIMES = 5,//转向 0、1或2轮播报
    TRAFFIC = 6,//拥堵播报
    WARNING_SIGN = 7,//警示牌播报
    SEATING_AREA = 8,//服务区播报
    STRAIGHT = 9,//小直行播报
    REDLIGHT_WAITING = 10,//红绿灯等待播报
    HEART_BREAKING = 11,//心跳播报
    ROUNDABOUTEXIT = 12,//退出环岛播报
    JUNCTIONPOINT = 13,//走过分歧点播报
    SPEEDLIMIT = 14,//道路限速播报
    LANE = 15, //车道线播报
    WARNINGSCHOOL = 16,//学校警示播报
    SPECIALGUIDANCE = 17,//特殊诱导播报
    FCROSS = 18,//F路口播报
    TUNNEL_INS = 19,//启动惯导播报
    INNERROAD = 20,//内部路播报
#ifdef __AUXILIARY_INFORMATION_FOR_CHECK__
    BEFORE_TUNNEL = 21,// 隧道前，仅诱导sdk调试使用
    AFTER_TUNNEL = 22,// 隧道后，仅诱导sdk调试使用
#endif
    READYGO = 23,//准备出发播报
    LONGTRIP = 24,//长途播报
    REFERENCIA = 25,//参照物播报
    FORCEGUIDANCE = 26,//暂无使用
    CITYANDWEATHER = 27,//城市边界和天气播报
    SPEEDWILLCHANGE = 28,//限速变化播报
    PASS_CAMERA = 29,//经过测速电子眼提示音
    INTERVAL_CAMERA = 30,//区间测速电子眼播报
    LSLINE_TIP= 31,//长白实线播报
    TRAFFIC_EVENT = 32,//交通点事件播报
    POINT_SPEED_LIMIT = 33,//限速电子眼播报
    OPERATION_DATA = 34,// 运营播报
    ADDITON_LANE = 35,// 针对车道线的额外的播报
    UPTONW_GATE = 36,// 区域内小区门
    ADDITIONAL_LANE_EVENT = 37,// 附加车道播报
    EXPLAIN_EVENT = 38,// 行中解释性播报
    CONTI_CURVE = 39,//连续弯道播报

    //以下为车道级事件类型，50-100
    ADAS_LANE = 50,// 车道级车道线播报
    HAD_SPECIAL_LANE = 51,//车道级特殊车道播报（公交/应急/非机动车道）
    HAD_CAMERA = 52,//车道级电子眼播报
    TTS_TP_CHANGE_LANE = 100,//车道级变道点播报，依赖tp变道点

    //以下为非云播播报，新类型请递减
    TTS_DRIVE_REVERSE = 8871,// 行驶方向与规划路线方向相反
    TTS_START_DRIVER_OFF_CAR = 8872, //起点人车分离
    TTS_IN_ROUTE_AREA = 8873,  // 请行驶回xx路(行中)
    TTS_START_AREA_GATE = 8874,  // 请行驶到xx门（大门）
    TTS_START_AREA_EXIT = 8875,  // 请行驶到（xx）出口
    TTS_START_NO_AREA   = 8876,  // 请行驶到xx路（起点）
    TTS_SMART_LOC = 8877,          // 智能定位
    TTS_DIALOG = 8878,             // 切骑行、步行提醒 dialog播报
    TTS_START_PARKING = 8879,      // 起点在地库
    TTS_BOUND_ARRIVE_DEST = 8880, // boundary终点判达
    TTS_VIA_FORCE_ARRIVE = 8881, // 强制途经点判达
    TTS_FORCE_ARRIVE_DEST = 8882,//吸附到路线最后一个形状点终点判达
    TTS_LOC_ARRIVE_DEST = 8883,//定位下车手势终点判达
    TTS_BI_ARRIVE_DEST = 8884, //行为事件终点判达
    TTS_BI_ARRIVE_DEST_PARKING = 8885, // 特殊行为事件终点判达，终点poi在室外，但脱离路线驶入地库场景
    TTS_SDK_YAW_ARRIVIAL = 8886, // 偏航驳回判达播报
    TTS_REMINDER = 8887, //播报模式切换(以为您切换详细播报、已为您切换简洁播报)、启动播报提示音（已为您切换熟路模式、已为您切换至导航）
    TTS_TYPE_NONE = 8888 // 纯走诱导sdk不走云播的播报：推荐路线、驶入伴随、手动切伴随、正为您提供离线播报、gps状态
};

enum RefluxType
{
    RTNONE = 0,
    RT0 = 1,
    RT1 = 2,
    RT2 = 3,
    RT3 = 4,
    RT4 = 5,
    RT5 = 6,
    RTBEFORETUNNEL = 7,
    RTAFTERTUNNEL = 8,
    RTWARNINGSIGN = 9,
    RTSA = 10,
    RTSTRAIGHT = 11,
    RTJUNCTION = 12,
    RTTUNNELINS = 13,
    RTINNERROAD = 14,
    RTDEFAULT = 15
};

enum TipsStraightType
{
    TIPSSTRAIGHTTYPENONE = 0,
    TIPSSTRAIGHTTYPERIGHT2IC = 1,
    TIPSSTRAIGHTTYPELEFT2IC = 2,
    TIPSSTRAIGHTTYPERIGHTIC = 3,
    TIPSSTRAIGHTTYPELEFTIC = 4,
    TIPSSTRAIGHTTYPERIGHTICSTAYMAIN = 5,
    TIPSSTRAIGHTTYPERIGHTICSTAYFRONTAGE = 6,
    TIPSSTRAIGHTTYPELEFTICSTAYMAIN = 7,
    TIPSSTRAIGHTTYPELEFTICSTAYFRONTAGE = 8,
    TIPSSTRAIGHTTYPESTAYMAIN = 9,
    TIPSSTRAIGHTTYPESTAYFRONTAGE = 10,
    TIPSSTRAIGHTTYPEBIGRIGHT = 11,
    TIPSSTRAIGHTTYPEBIGLEFT = 12,
    TIPSSTRAIGHTTYPEBIGCROSS = 13,
    TIPSSTRAIGHTTYPEDEFAULT = 14
};
enum NotMatchType
{
    UNKOWN_NOT_MATCH = 0x1,
    ANGLE_NOT_MATCH = 0x2,
    DISTANCE_NOT_MATCH = 0x4,
    START_NOT_SAME_NOT_MATCH = 0x8,
    START_ANGLE_NOT_MATCH = 0x10,
    HMM_NOT_MATCH = 0x20,
    TREND_NOT_MATCH = 0x40
};
/*  segment index value in different match state
 * >= 0 MS_OnRoute = 1,
 * < 0  MS_NotOnRoute_NotOutWay = -1,
 * < 0  MS_NotOnRoute_OutWay = -2,
 * >= 0 MS_IdleCounterInServiceArea = -3,
 * >= 0 MS_IdleCounterNotInServiceArea = -4,
 * <0   MS_NotOnRoute_InStartCycle = -5,
 * <0   MS_In_The_District = -6,
 */
enum MatchState
{
    MS_OnRoute = 1,
    MS_NotOnRoute_NotOutWay = -1,
    MS_NotOnRoute_OutWay = -2,
    MS_InServiceArea = -3,
    MS_NotOnRoute_InStartCycle = -5,//在起点圈内偏航有特殊处理
    MS_In_The_District = -6,//在小区内
    MS_OUTWAY_BUT_ARRVING_DESTINATION = -7,//在终点附近偏航但命中了结束的逻辑
    MS_Error = -888
};
//LongTrip
typedef enum ReturnType
{
    RETURNTYPE_TIME_REMINDER = 0x01, // 时间提醒
    RETURNTYPE_FATIGUE_DRIVING_2_HOURS = 0x02, // 疲劳驾驶2小时
    RETURNTYPE_FATIGUE_DRIVING_4_HOURS = 0x04, // 疲劳驾驶4小时
    RETURNTYPE_MILESTONE_MOST = 0x08, // 里程碑提醒（过大半）
    RETURNTYPE_MILESTONE_HALF = 0x10, // 里程碑提醒（过半）
    RETURNTYPE_BORDER_AND_WEATHER = 0x20, //地界天气
    RETURNTYPE_ERROR = 0x8000000 // 错误的返回类型
} ReturnType;

enum RouteGuidanceReturnTypes
{
    RETURN_SEARCH_ERROR = -8,//没有查询结果或查询类型非法
    RETURN_LOC_ERROR = -7, //智能定位一期专用，用于告诉客户端不使用智能定位结果
    RETURN_TRY_CATCH_ERROR = -6, //try-catch错误
    RETURN_EVENTPOINTS_ERROR = -5, //没有拥堵数据
    RETURN_MAPPOINTS_ERROR = -4, //没有点串
    RETURN_NOT_MATCHED = -3,//gps点未吸附，导致没办法进行计算
    RETURN_JCE_ERROR = -2,
    RETURN_COMMON_ERROR = -1,
    RETURN_SUCCESS = 0
};

typedef enum LocSrcType
{
    kLocSrcTypeNtIntel = 10,
    kLocSrcTypeNtDrIntel = 11,
    kLocSrcTypeGpsDrIntel = 12
} LocSrcType;

enum RouteGuidanceIntelligentVoiceReturnTypes
{
    INTELLIGENT_VOICE_RETURN_NOSATISFIEDDATA = -2,//没有满足条件的数据
    INTELLIGENT_VOICE_RETURN_NOTRAFFICDATA = -1,//没有路况数据
    INTELLIGENT_VOICE_RETURN_SUCCESS = 0
};

enum ENUMPASSDIVERGENCE
{
    PASSDIVERGENCEWITHBRIGHTLINE = 0,
    PASSDIVERGENCEWITHDARKLINE
};

enum TtsSubType
{
    TST_NONE = -1,
    TST_Destnation = 1,
    TST_Intersection,
    TST_IntersectionStraight,
    TST_RoundAbout,
    TST_SpecialGuidance,
    TST_Tollgate,
    TST_Tunnel,
    TST_ViaPoint
};

enum WalkTipsArriveStatus
{
    WALK_TIPS_ARRIVE_STATUS_NULL = 0,
    WALK_TIPS_ARRIVE_STATUS_ARRIVED = 1,
    WALK_TIPS_ARRIVE_STATUS_CROSSING = 2,
    WALK_TIPS_ARRIVE_STATUS_PASSED = 3
};

// 辅助动作类型
typedef enum _AccActionType
{
  ACC_ACTION_TYPE_NONE = 0,  // 默认值
  ACC_ACTION_TYPE_UP_OVERPASS = 1,     // 上天桥
  ACC_ACTION_TYPE_DOWN_OVERPASS = 2,   // 下天桥
  ACC_ACTION_TYPE_IN_UNDERPASS = 3,     // 进地下通道
  ACC_ACTION_TYPE_OUT_UNDERPASS = 4     // 出地下通道
}AccActionType;

typedef enum RecommandLevel_ {
  RECOMMAND_NONEEDCHANGE = 0,  //不需要替换
  RECOMMAND_BROADCAST,         // 1 播报
  RECOMMAND_PUSHANDBROADCAST,  // 2 强推并播报
  RECOMMAND_SILENT_CHANGE,     // 3.静默替换
}RecommandLevel;

typedef enum RecommandAttitude_
{
  //2 推荐 1中性 0 不推荐
  ATTITUDE_NO = 0, // 0 不推荐
  ATTITUDE_NEUTRAL, //1中性
  ATTITUDE_RECOMMAND,//2 推荐
}RecommandAttitude;

//左侧傍山险路	eWTAlongMountain	component_alongmountain	18
//右侧傍山险路 eWTAlongMountainEx component_alongmountainex 19
//路面不平	eWTUnevenRoad	component_rough_road	24
//左右绕行	eWTDetour	component_detour	29
//左侧绕行 eWTDetourLeft component_detourleft 30
//右侧绕行 eWTDetourRight component_detourright 31
//注意危险 eWTCaution component_caution 32
//禁止超车 eWTNoOvertake component_noovertake 33
//注意警示牌	eWTTextWarning	component_textwarning	37
//隧道开灯	eWTTurnonLightInTunnel	component_turnonlightintunnel	43
enum WarningSignSubtype
{
	WARNING_SIGN_SUBTYPE_ZUOCEBANGSHANXIANLU = 18,
    WARNING_SIGN_SUBTYPE_YOUCEBANGSHANXIANLU = 19,
    WARNING_SIGN_SUBTYPE_LUMIANBUPING = 24, 
    WARNING_SIGN_SUBTYPE_ZUOYOURAOXING = 29,
    WARNING_SIGN_SUBTYPE_ZUOCERAOXING = 30,
    WARNING_SIGN_SUBTYPE_YOUCERAOXING = 31,
    WARNING_SIGN_SUBTYPE_ZHUYIWEIXIAN = 32,
    WARNING_SIGN_SUBTYPE_JINZHICHAOCHE = 33,
    WARNING_SIGN_SUBTYPE_ZHUYIJINGSHIPAI = 37,
    WARNING_SIGN_SUBTYPE_SUIDAOKAIDENG = 43,
    WARNING_SIGN_SUBTYPE_LaneSlideSlop = 49, // 滑坡
    WARNING_SIGN_SUBTYPE_DirtRoad = 50, // 泥石路
    WARNING_SIGN_SUBTYPE_GroundSink = 51, // 地面塌陷
    WARNING_SIGN_SUBTYPE_Collapse = 52, // 塌方
};

enum DynamicMapEnlargedCallType {
    DYNAMIC_ENLARGE_MAP_INVALID,
    DYNAMIC_ENLARGE_MAP_ABOUT_TO_PASS_INNER_1,  //即将经过第一个路口
    DYNAMIC_ENLARGE_MAP_PASS_INNER_1            //已经过第一个路口
};

enum AccessorySearchType
{
  ACCESSORY_SEARCH_TYPE_SEATING_AREA  =   1<<1, //服务区
  ACCESSORY_SEARCH_TYPE_TOLL_STATION  =   1<<2, //收费站
  ACCESSORY_SEARCH_TYPE_GAS_STATION   =   1<<3  //独立加油站
};

enum AccessoryResultType
{
  /**
   * 剩余全程，按从近到远排序
   */
  ACCESSORY_RESULT_TYPE_DEFAULT = 0,
  /**
   * 最近一个
   */
  ACCESSORY_RESULT_TYPE_NEXT_ONE = 1,
  /**
   * 最远一个
   */
  ACCESSORY_RESULT_TYPE_LAST_ONE = 2,
};

// 播报模式
enum TTSModeEnum
{
  TTSMODE_NULL = -1,      // 仅last_tts_mode_会用到，用于区分非行程中状态
  TTSMODE_DEFAULT = 0,    // 详细播报
  TTSMODE_SHORT,          // 简洁播报
  TTSMODE_DING,           // 只有ding,用于路线探测
  TTSMODE_FAMILIAR,       // 熟路模式
  TTSMODE_SILENT,         // 静音模式，原来由客户端实现, 2021.1.15 下沉诱导
};

enum GuideModeEnum
{
    GUIDE_MODE_ONLINE = 0, //在线诱导
    GUIDE_MODE_OFFLINE = 1, // 离线诱导
};

enum RGIdleSectionTypeEnum
{
    IDLE_SECTION_NONE = -1,
    IDLE_SECTION_HIGH = 0,//高级
    IDLE_SECTION_LOW = 1,//低级
};

// 白天黑夜状态
enum DayToNightEnum
{
  DAYTONIGHT_DEFAULT = -1, // 默认初值
  DAYTONIGHT_DAY = 0,      // 白天
  DAYTONIGHT_NIGHT = 1,    // 黑夜
  DAYTONIGHT_TO_NIGHT = 2, // 白天切黑夜
  DAYTONIGHT_TO_DAY = 3,   // 黑夜切白天
};

//下面的状态用于CQRouteMatchItemr的setGPSPoint的返回的子状态
/***************************************************/
#define MS_STATE_PASSSERVERDIVERGENCEPOINT          0x01
#define MS_STATE_HEARTBEAT                          0x02

// gps source real 0 simulate 1 first start point 2
#define kRouteGuidanceGpsSourceReal         0
#define kRouteGuidanceGpsSourceSimulate     1
#define kRouteGuidanceGpsSourceStart        2

# define INTERVAL_SLIGHTLY_YAW_TTS 60 // 步骑轻微偏航tts时间间隔
# define ACCMULATE_TIMES 3 // 步骑从轻微偏航恢复后吸附成功累计次数阈值

ROUTE_GUIDANCE_FUNCTION_END

#endif
