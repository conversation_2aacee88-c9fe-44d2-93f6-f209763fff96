//
// Created by da<PERSON><PERSON><PERSON> on 2020/11/2.
// 步骑公交接口参数独有数据结构
//

#ifndef GUIDANCE_ENGINE_INCLUDE_WALK_LISTENER_STRUCTURE_H_
#define GUIDANCE_ENGINE_INCLUDE_WALK_LISTENER_STRUCTURE_H_

#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/route_structure.h>
#include "guidance_def.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

/**
 * SetRoute参数结构,步骑复用
 * @see {@link walk::WalkApi::SetRoute}
 */ 
struct RG_EXPORT GreenTravelSetRouteParam {
  /**
   * 导航原因，对应ROUTE_FOR系列
   */
  mapbase::RouteForWhat route_for_what;
  /**
   * 指定主路线id，默认第0条
   */
  std::string selected_routeid;
  /**
   * 主路线剩余时间，用于定制开始导航播报，默认值:-1，单位：分钟
   * 当为0时，不播报开始导航中的时间和距离信息
   * 当-1时，时间会直接从主路线数据中获取
   */
  int remain_time_minute{-1};
  /**
   * 主路线剩余距离，用于定制开始导航播报，单位：米
   * 当<15时，不播报开始导航中的时间和距离信息
   * 当为-1时，距离会直接从主路线数据中获取
   */
  int remain_distance{-1};
};

/**
 * AR步导独用，设施通过状态类型信息
 */
struct RG_EXPORT WalkTipsArriveStatusInfo {
  /**
   * 设施通过状态
   */ 
  WalkTipsArriveStatus walk_status;
  /**
   * 设施类型:o天桥,u地下通道,c人行横道
   */ 
  std::string tips_type;
};

/**
 * 步骑公交EventPoint通用数据封装
 */
struct RG_EXPORT GreenTravelEventPointItem {
  /**
   * 当前转向的类型
   * <br>公交复用,表示当前段类型:
   * <br>0-NONE
   * <br>1-INTERVAL
   * <br>2-TRAN
   */
  INTERSECTION_TYPE intersection_type;
  /**
   * 当前转向动作，举例：左转；公交复用，用于表示interval_uid 或tran_uid
   */ 
  std::string action;
  /**
   * 下个导航段第一个形状点索引
   */ 
  int segment_index;
  /**
   * 当前导航段长度，单位m
   */ 
  int road_length;
  /**
   * 当前导航段道路名
   */ 
  std::string road_name;
  /**
   * 下一个导航段路名/方向名
   */
  std::string next_road_name;
  /**
   * AR导航方向角
   */ 
  float out_angle;
  /**
   * 辅助动作:上/下天桥 进入/离开地下通道,for AR WalkGuider
   */
  AccActionType acc_action_type;
  /**
   * 定位点到当前转向距离，单位m；公交复用，用于表示当前子段（Tran or Interval）剩余距离
   */
  int distance;

  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const GreenTravelEventPointItem& info) {
    out << " EventPoint{inter_type:" << info.intersection_type
        << ",dis:" << info.distance
        << ",action:" << info.action
        << ",seg_index:" << info.segment_index
        << ",acc_type:" << info.acc_action_type
        << ",out_angle:" << info.out_angle
        << ",road_name:" << info.road_name
        << ",road_len:" << info.road_length
        << ",next_road_name:" << info.next_road_name
        << "}";
    return out;
  }
};

/**
 * 步骑公交单一路线当前诱导信息
 */
struct RG_EXPORT GreenTravelUpdateInfoItem {
  /**
   * 路线id；公交复用
   */
  std::string route_id;
  /**
   * 定位吸附结果；公交复用
   */
  mapbase::MatchResult match_result;
  /**
   * 定位点到终点距离，单位m；公交目前无效
   */ 
  int total_distance_left;
  /**
   * 定位点到前方第一个形状点距离，单位m；公交目前无效
   */ 
  int distance_to_shape_point;
  /**
   * 连续EventPoint通用数据；<br>
   * 若步骑定位点在路线最后一个导航段上，size为1，否则size为2；<br>
   * event_points[0]表示next_event_point，<br>
   * event_points[1]表示next_next_event_point；<br>
   * 公交复用，size为1
   */ 
  std::vector<GreenTravelEventPointItem> event_points;

  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const GreenTravelUpdateInfoItem& info) {
    out << " UpdateInfoItem{route_id:" << info.route_id
        << ",dis_to_shape:" << info.distance_to_shape_point
        << ",total_dis_left:" << info.total_distance_left
        << ",event_points:[";
    for (const auto& event : info.event_points) {
      out << event;
    }
    out << "],match_result:" << info.match_result;
    return out;
  }
};

/**
 * 步骑公交诱导状态更新信息；公交复用用于透出子段剩余距离
 */
struct RG_EXPORT GreenTravelUpdateInfo {
 /**
  * 当前吸附场景；公交复用
  */  
 mapbase::MultiRouteMatchStatus match_status;
 /**
  * 当前诱导信息，步骑行size为1；公交复用，size为1
  */
 std::vector<GreenTravelUpdateInfoItem> guidance_info;

 RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const GreenTravelUpdateInfo& info) {
    out << " UpdateInfo{match_status:" << static_cast<int>(info.match_status) << ",guidance_info:[";
    for (const auto& item : info.guidance_info) {
      out << item;
    }
    out << "]}";
    return out;
  }
};

/**
 * 步骑吸附失败信息
 */
struct RG_EXPORT MatchFailedInfo{
  /**
   * 吸附失败文案
   */
  std::string text;
  /**
   * 透传定位引擎的吸附失败（微偏）原因：0x4表示距离不匹配微偏，0x10表示起点逆行微偏
   */
  int reason;
  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const MatchFailedInfo& info) {
    out << " MatchFailedInfo{text:" << info.text << ", reason:" << info.reason << "}";
    return out;
  }
};
/**
 * 步骑关键poi信息
 */
struct RG_EXPORT KeyPoiInfo{
  /**
   * poi id
   */
  std::string poi_id;
  friend std::ostream& operator<<(std::ostream& out, const KeyPoiInfo& info) {
    out << info.poi_id;
    return out;
  }
};
/**
 * 步骑里程碑信息</br>
 * 映射表：<a href="https://doc.weixin.qq.com/sheet/e3_AaAAPAZaADkaU4tTQO9TDWN7CimKU?scode=AJEAIQdfAAogskjMECAaAAPAZaADk&tab=BB08J2">...</a>
 */
struct RG_EXPORT MileStoneInfo{
  /**
   * 里程碑里程，单位m
   */
  int meter;
  /**
   * 消耗热量等价的食物枚举，具体枚举值见表格
   */
  int food_enum;
  /**
   * 里程marker坐标
   */
  mapbase::RoutePos marker_pos;
  friend std::ostream& operator<<(std::ostream& out, const MileStoneInfo& info) {
    out << info.meter << "m,food:" << info.food_enum << "," << info.marker_pos;
    return out;
  }
};

/**
 * Bus SetRoute参数结构
 * @see {@link bus::BusApi::SetRoute}
 */
struct RG_EXPORT BusEngineSetRouteParam {
  /**
   * 当前路线(方案)id，对应BusRoute
   */
  std::string selected_routeid;
  /**
   * 当前路线的选中 line id 集合，line id 映射 Interval 的 interval_uid;
   * 必须按照从上到下的顺序填充数据，否则诱导索引转换过程会乱
   */
  std::vector<std::string> line_id_list;
};

/**
 * 公交显示事件类型
 */
enum class RG_EXPORT BusDIType {
  START           = 10000,  //路线起点
  END             = 10001,  //路线终点
  STATION         = 10002,  //站点
  STATION_BETWEEN = 10003,  //站间
  TRAN            = 10004,  //步行段
  INTERVAL        = 10005,  //段中显示，用于骑行或打车段
  INTERVAL_BEGIN  = 10006,  //段首显示，用于骑行或打车段
  INTERVAL_END    = 10007,  //段尾显示，用于骑行或打车段
  LEAVE_START     = 10008,  //离开起点
  LEAVE_END       = 10009,  //离开终点
};

/**
 * 公交显示目标信息
 */
struct RG_EXPORT TargetInfo {
    /**
     * 透传显示目标位置，其 coor_start 相对路线起点
     */
    mapbase::RoutePos route_pos;
    /**
     * 透传显示目标uid，站间是上一站点uid
     */
    std::string target_uid;
    /**
     * 透传公交显示事件类型，手图说要用
     * @see BusDIType
     */
    BusDIType di_type;
    /**
      * 目标所在interval或tran的uid
      */
    std::string segment_uid;

    RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const TargetInfo& info) {
      out << " TargetInfo{route_pos:" << info.route_pos
          << ",seg_uid:" << info.segment_uid
          << ",di_type:" << static_cast<int>(info.di_type)
          << ",target_uid:" << info.target_uid
          << "}";
      return out;
    }
};
ROUTE_GUIDANCE_NAMESPACE_END


#endif //GUIDANCE_ENGINE_INCLUDE_WALK_LISTENER_STRUCTURE_H_