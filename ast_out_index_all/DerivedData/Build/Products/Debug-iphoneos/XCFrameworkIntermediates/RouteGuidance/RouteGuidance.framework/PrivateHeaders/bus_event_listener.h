//
// Created by davishua<PERSON> on 2020/10/31.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_BUSENGINE_API_BUSEVENTLISTENER_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_BUSENGINE_API_BUSEVENTLISTENER_H_


#include "green_travel_interface_structure.h"
#include "green_travel_event_listener.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

class BusEventListener : public GreenTravelEventListener {
public:
   /**
   * 到达目标，用于显示
   * @param target_info 目标信息
   */
    virtual void OnApproachingTarget(const TargetInfo &target_info){};
};

ROUTE_GUIDANCE_NAMESPACE_END

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKEVENTLISTENER_H_