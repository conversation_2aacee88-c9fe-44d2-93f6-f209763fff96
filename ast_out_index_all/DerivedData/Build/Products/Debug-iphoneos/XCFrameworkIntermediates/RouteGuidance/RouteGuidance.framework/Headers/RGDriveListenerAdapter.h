/**
 * Copyright © 2021 Tencent. All rights reserved.
 */
#import "RGListenerAdapter.h"
#import "RGModels.h"

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - DriveBehaviorEvent

@protocol TRGDriveBehaviorEventListenerProtocol <NSObject>

/**
* 准备请求下一个城市的天气情况
* @param city_name 城市名称
*/
-(void)onRequestWeather:(NSString *)city_name;

/**
 * 准备更新指定路线的收费信息
 * @param route_id 路线id
 */
-(void)onRequestTollStationFee:(NSString *) route_id;

/**
 * 准备请求停车场信息
 */
-(void)onRequestParking;

/**
 * 推荐上层进行路线跟换，获取该回调后，如果决定进行更换可以通过SetMainRoute接口进行变更
 * @param info 当前路线和推荐路线的id
 */
-(void)onRecommendRouteShow:(TRGRecommendRouteInfo *)info;

/**
 * 缺少云播数据
 * @param route_ids 需要的云播数据列表
 */
-(void)onLackOfGuidanceData:(NSArray<NSString*> *)route_ids;

@end


/// 导航行为时间中间适配层
@interface TRGDriveBehaviorEventListenerAdapter : TRGListenerAdapter

//listener代理对象，代理实现在RGDriveProvider
@property (nonatomic,weak) id <TRGDriveBehaviorEventListenerProtocol> delegate;

@end

#pragma mark - DriveEvent


/// 导航事件回调方法
@protocol TRGDriveEventListenerProtocol <NSObject>

@optional

/**
 * @brief 播报语音
 * @param tts 语音内容
 * @return 是否播报成功
 */
- (BOOL)onTTSPlay:(TRGPlayTtsInfo *)tts;

/**
 * 偏航
 * @param off_course_info 偏航状态
 */
- (void)onOffCourse:(TRGOffCourseInfo *)off_course_info;

/**
 * 显示放大图
 * kRouteGuidanceEventActionShowMapEnlargement 5
 * @param enlarge_map_info 放大图信息
 * @return true-成功 false-失败
 */
- (BOOL)onEnlargeMapShow:(TRGShowEnlargeMapInfo *)enlarge_map_info;

/**
 * 路口放大图距离更新
 * @param info  距离数据
 */
- (void)onUpdateCommonEnlargedMap:(TRGUpdateCommonEnlargedMapInfo *)info;

/**
 * 刷新动态连续路口放大图
 * @param enlarge_map_info 放大图的信息
 * @return true-成功 false-失败
 */
- (BOOL)onDynamicEnlargedMapUpdate:(TRGUpdateDynamicEnlargedMapInfo *)enlarge_map_info;

/**
 * 隐藏放大图
 * @return true-成功 false-失败
 */
- (BOOL)onEnlargeMapHide;

/**
 * 显示高速出口
 * @param exit_info
 */
-(void)onExitInfoShow:(TRGExitInfo *)exit_info;

/**
 * 隐藏高速出口
 */
-(void)onExitInfoHide;

/**
 * 拥堵事件显示更新
 * @param jam_info
 * @return true-成功 false-失败
 */
-(BOOL)onTrafficEventUpdate:(TMapBaseTrafficJamInfo *)jam_info;

/**
 * 拥堵事件隐藏
 */
-(void)onTrafficEventHide;

/**
 * 沿途电子眼列表
 * @param camera_list 摄像头列表
 */
-(void)onResetRouteCameraList:(TMapBaseRouteCameraRefreshInfo *)camera_list;

/**
 * 即将经过的电子眼列表(正在播报)
 * @param camera_id_list 摄像头id列表
 */
-(void)onApproachingCamera:(TRGCamerasListInfo *)camera_id_list;

/**
 * 放大摄像头
 * @param cameras_in_range 从中pick用于放大,产品定的pick规则:按distance,最多同时放大两位置;
 *                         同一位置按priority最多显示两个icon
 */
-(void)onCameraEnlarge:(NSArray<TRGRouteCameraInRange *> *)cameras_in_range;

/**
 * 隐藏指定的摄像头
 * @param hide_cameras 需要隐藏的摄像头index列表
 */
-(void)onCameraHide:(NSArray<NSNumber *> *)hide_cameras;

/**
 * 测速区间开始显示
 * @param limit_zone_info 测速区间信息
 */
-(void)onSpeedZoneCameraShow:(TRGSpeedLimitZoneInfo *) limit_zone_info;

/**
 * 进入区间测速路段
 * @param enter_camera 经过的进入测速摄像头
 */
-(void)onEnterSpeedZone:(TMapBaseRouteCameraInfo *)enter_camera;

/**
 * 区间测速内状态
 * @param info 区间测速状态
 */
-(void)onSpeedZoneUpdate:(TRGSpeedLimitZoneUpdateInfo *)info;

/**
 * 离开区间测速路段
 * @param leave_camera 经过的离开区间测速摄像头
 */
-(void)onLeaveSpeedZone:(TMapBaseRouteCameraInfo *)leave_camera;

/**
 * 显示车道线
 * @param lane_info 车道线信息
 * @return true-成功 false-失败
 */
-(BOOL)onLaneGuideShow:(TMapBaseLaneInfo *)lane;

/**
 * 隐藏车道线
 */
-(void)onLaneGuideHide;

/**
 * 警示牌显示
 * @param warning_sign 警示牌
 */
-(void)onWarningTipShow:(TRGWarningSignInfo*)info;

/**
 * 警示牌消失
 */
-(void)onWarningTipHide;

/**
 * 当前路名更新
 * @param road_name 当前路名
 */
-(void)onCurrentRoadNameUpdate:(NSString*)road_name;

/**
 * 接近分歧点
 * @param fork_point 分歧点
 */
-(void)onApproachingDivergencePoint:(TMapBaseRoutePos*)fork_point ;

/**
 * 通过分歧点（注意，通过并不一定构成偏航，此时主路线无法准确判断）
 * @param divergence_point 分歧点位置
 */
-(void)onPassedDivergencePoint:(TRGPassDivergencePointInfo *)divergence_point;

/**
 * 伴随路线偏航
 * @param off_course_info 通过分歧点且有至少一条路线偏航
 */
-(void)onCompanionRouteOffCourse:(TRGCompanionRouteOffCourseInfo *)off_course_info;

/**
 * 迷惑路口位置显示，只回调一次
 * @param cross_position 路口位置
 */
-(void)onConfuseCrossShow:(NSArray<TRGConfuseCrossInfo *> *)cross_position;

/**
 * 在红绿灯前(目前不推荐使用) iOS 端上没有逻辑，安卓有显示悬浮窗的逻辑
 */
-(void)onBeforeRedLight;

/**
 * 通过红绿灯
 */
-(void)onPassRedLight;

/**
 * 路线上剩余红绿灯发生变化时，回调最新的
 * @param remain_redlight_list
 */
-(void)onRemainRedLightUpdate:(TRGRemainRedLightInfo *) remain_redlight_list;

/**
 * 到达目的地
 */
-(void)onArrivalDestination;

/**
 * 到达途径点
 * @param via 途径点
 */
-(void)onArrivalVia:(TRGViaArrivalInfo *)via;

/**
 * 即将到达隧道
 * @param tunnel 隧道
 */
-(void)onArrivalTunnel:(TRGTunnelInfo *)tunnel;

/**
 * 接近收费站
 */
-(void)onBeforeTollStationShow;

/**
 * 接近收费站播报
 */
-(void)onBeforeTollStationSpeak;

/**
 * 通过收费站
 */
-(void)onPassTollStation;

/**
 * 显示收费站收费信息
 * @param toll_name 收费站名称
 */
-(void)onShowTollStationFee:(NSString *)toll_name;

/**
 * 显示收费站通道
 * @param info 高速设施(收费站)信息，包含收费站通道数据
 */
-(void)onShowTollAisles:(TRGHighwayInstructionInfo *)info;

/**
 * 隐藏收费站通道
 */
-(void)onHideTollAisles;

/**
 * 接近转向位置
 * @param info 进入路口
 */
-(void)onApproachingTurnIntersection:(TRGApproachingTurnInfo *)info;

/**
 * 离开上一个转向位置
 * @param last_segment_index 离开路口的导航段下标
 */
-(void)onLeftTurnIntersection:(NSInteger)last_segment_index;

/**
 * 高速/国道上的服务区
 * @param instructions 高速设施
 */
-(void)onHighwayInstructionUpdate:(NSArray<TRGHighwayInstructionInfo*>*)instructions;

/**
 * 隐藏高速/国道上目前的服务区
 */
-(void)onHighwayInstructionHide;

/**
 * 智能定位状态更新
 * @param loc_state 只能定位
 */
-(void)onSmartLocStatusUpdate:(TMapBaseSmartLocStatus)loc_state;

/**
 * 已超过前方测速摄像限速
 * @param overspeed
 */
-(void)onCameraOverSpeed:(TRGOverSpeedInfo *)overspeed;

/**
 * 巡航态偏航重新算路后，隐藏原路线鱼骨路上所有显示元素
 */
-(void)onHideAllDisplayItemsOnFishbone;

/**
 * 重新请求巡航路线
 */
-(void)onRequestCruiserRoute;

/**
 * 取得主路上所有动态点事件
 * @param vec_event_points 动态点事件数组
 */
-(void)onTrafficEventPointsUpdate:(NSArray<TMapBaseTrafficEventInfo*>*)vec_event_points;

/**
 * 隐藏经过的动态点事件信息
 * @param event_point 动态点事件数组
 */
-(void)onHideTrafficEventPoint:(TMapBaseTrafficEventInfo*)event_point;

/**
 * 主路线即将发生静默替换时回调
 * @param info 替换路线前后的routeid信息
 */
-(void)onSilentChangeMainRoute:(TRGRecommendRouteInfo *)info;

/**
 * 定位信号弱状态信息
 * @param status_info 状态信息
 */
-(void)onGpsStatusUpdate:(TRGGpsStatusInfo*)status_info;

/**
 * 大定位结果 match_location_info 传入 诱导引擎 setmapoint 之后给的返回值 核心接口
 * @param status_info 状态信息
 */
-(void)onSegmentUpdate:(TRGUpdateInfo *)updateModel;

/**
 * 手图路线探测显示电子眼
 * @param cameras_show 要显示的电子眼序列
 */
- (void)onShowLightNavCameras:(NSArray<TRGRouteCameraInRange *> *)cameras_show;
    
/**
 * 手图路线探测隐藏电子眼
 */
- (void)onHideLightNavCameras;

/**
* 诱导透出信息
* @param info 透出信息
*/
-(void)onRGOutputInfo:(NSString *)out_info;

/**
 * 进入空闲区间
 * @param info 空闲区间信息
 */
-(void)onEnterIdleSection:(TRGIdleSectionInfo *)info;

/**
 * 预下载放大图
 * @param info 空闲区间信息
 */
-(void)onUpdateNextEnlargedMap:(TRGNextBrInfos *)info;

/**
 * 触发点事件众验
 * kRouteGuidanceEventActionTrafficEventValidator 20
 * @param traffic_event_info 交通事件
 */
- (void)onTrafficEventVerify:(TMapBaseTrafficEventInfo *)traffic_event_info;

/**
 * 即将离开当前导航段，距离机动点a点小于200米
 * @param info 目标车道相关信息
 */
 - (void)willLeaveIntersection;
/**
 * 已离开当前导航段，以过当前导航段b点为判定原则，进入到下个导航段
 * @param info 目标车道相关信息
 */
- (void)onExitIntersection;

/**
 * 进入隧道
 */
- (void)onEnterTunnel;

/**
 * 离开隧道
 */
- (void)onLeaveTunnel;

@end


/// 导航事件中间适配层
@interface TRGDriveEventListenerAdapter : TRGListenerAdapter

/// listener代理对象，代理实现在RGDriveProvider
@property (nonatomic,weak) id<TRGDriveEventListenerProtocol> delegate;

@end

#pragma mark - DriveStatistics

@protocol TRGDriveStatisticsListenerProtocol <NSObject>

@optional
/**
 * 特殊诱导类型回调
 * @param info_type 类型
 */
-(void)onSpecialGuidanceInfo:(NSInteger)info_type;

/**
 * 偏航用时统计
 * @param outway_count 偏航用时
 */
-(void)onOutwayCountUpdate:(NSInteger)outway_count;

@end


/// 中间适配层
@interface TRGDriveStatisticsListenerAdapter : TRGListenerAdapter

@property (nonatomic,weak) id<TRGDriveStatisticsListenerProtocol> delegate; /// listener代理对象，代理实现在RGDriveProvider

@end



#pragma mark - DriveHDEvent
/// 导航HD事件回调方法
@protocol TRGDriveHDEventListenerProtocol <NSObject>
@optional
/**
 * 在高精覆盖区域内 HAD->SD边界点为参考点
 * @param length 距离HAD覆盖边界的沿路线距离
 * @param carPos 当前自车吸附位置
 * @param hdEndPos HD覆盖终点位置
 */
- (void)onUpdateDisToHDEnd:(NSInteger)length: (TMapBaseRoutePos *)carPos: (TMapBaseRoutePos *)hdEndPos;
/**
 * 针对变化点，停止线，分离点引导区域内提醒
 * @param info 期望变道位置以及目标车道信息
 * 当行驶在推荐车道时，change_points_首元素对应的lane_id_中的值均为0
 * 当未行驶在推荐车道时，change_points_首元素对应的lane_id_中的值不全为0
 */
- (void)onNeedChangeToOtherLane:(TMapBaseChangeLaneGuideInfo *)info;
/**
 * 驶离变道点
 */
- (void)onLeaveChangePos;
@end
/// 中间适配层
@interface TRGDriveHDEventListenerAdapter : TRGListenerAdapter
/// listener代理对象，代理实现在RGDriveProvider
@property (nonatomic,weak) id<TRGDriveHDEventListenerProtocol> delegate;
@end


NS_ASSUME_NONNULL_END
