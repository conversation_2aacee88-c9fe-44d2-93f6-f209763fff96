/**
 * 该头文件目前只用来支持公交
 */
#ifndef ROUTEGUIDANCE_CARENGINE_GUIDANCE_DATA_DEF_H
#define ROUTEGUIDANCE_CARENGINE_GUIDANCE_DATA_DEF_H

#include <stdint.h>
#include "guidance_def.h"

ROUTE_GUIDANCE_FUNCTION_BEGIN

typedef struct RG_EXPORT tagRouteGuidanceMapPoint{
  int x;
  int y;
  int z;
}RouteGuidanceMapPoint;
//ToDo:
typedef struct RG_EXPORT _RouteGuidanceGPSPoint
{
  int coorIdx;				    // matched_index
  RouteGuidanceMapPoint mapPoint;   // IN OUT
  float heading;					// IN OUT 360, Negative if heading is invalid.
  int locationAccuracy;			    // IN, Negative if locationAccuracy is invalid.
  float velocity;				    // IN, m/s Negative if velocity is invalid.
  int64_t timestamp;			    // IN, s Negative if timestamp is invalid.
  int source;					    // IN, gps source real 0 simulate 1 first start point 2
  int motion;                       // 运动状态,特别注意！！！目前该字段暂不要使用，仅供信号处理模块使用，当该字段是7777时为非法信号，需要匹配特殊处理
  float mainConfidence;             // [0,1]运动状态置信度，结合运动状态本身使用
  float quality;                    // 范围[0,100]，越接近100，置信度越高
  int indoor;					    // 室内外 0室外，1室内
  int disToEvent;                   //自车点到转向距离,大图使用
  int smartLocState;                // 智能定位类型
  int smartLocType;                 // 智能定位状态
  int posType;                      // 定位信号类型
  int pos_sub_type;                 // 定位信号子类型
  int match_status;                 // 综合匹配状态
  int match_scene;                  // 导航匹配场景

  //以下是车道级吸附信息
  int loc_work_mode;                // 定位吸附模式 -1 无效 2 车道级吸附 3 中心线吸附
  uint32_t tile_id;                 // HAD::LaneID tile_id，loc_work_mode为2时HAD::LaneID才有效
  uint32_t lane_id;                 // HAD::LaneID lane_id
  int speed_type;                   // 车速类型： 0为其他速度，1：仪表速度
} RouteGuidanceGPSPoint;

ROUTE_GUIDANCE_FUNCTION_END
#endif
