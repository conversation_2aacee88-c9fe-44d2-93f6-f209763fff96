//
// Created by morgan<PERSON>(morgansun) on 2020/4/10.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCEEVENTLISTENER_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCEEVENTLISTENER_H_

#include "guidance_def.h"
#include "guidance_interface_structure.h"

namespace route_guidance {

class GuidanceBehaviorEventListener {
 public:
  /**
  * 准备请求下一个城市的天气情况
  * @param city_name 城市名称
  */
  virtual void OnRequestWeather(const std::string& city_name) {}
  /**
   * 准备更新指定路线的收费信息
   * @param route_id 路线id
   */
  virtual void OnRequestTollStationFee(const std::string& route_id) {}
  /**
   * 准备请求停车场信息
   */
  virtual void OnRequestParking() {}
  /**
   * 推荐上层进行路线跟换，获取该回调后，如果决定进行更换可以通过SetMainRoute接口进行变更
   * @param info 当前路线和推荐路线的id
   */
  virtual void OnRecommendRouteShow(const RecommendRouteInfo& info) {}
  /**
   * 缺少云播数据
   * @param route_ids 需要的云播数据列表
   */
  virtual void OnLackOfGuidanceData(const std::vector<std::string>& route_ids) {}
};

class GuidanceEventListener {
 public:
  /**
   * 播报语音
   * @param tts 语音内容
   * @return 是否播报成功
   *
   */
  virtual bool OnTTSPlay(const PlayTtsInfo& tts) { return true; };
  /**
   * 偏航，诱导透传，触发在定位
   * @param off_course_info 偏航状态
   */
  virtual void OnOffCourse(const OffCourseInfo& off_course_info) {};
  /**
   * 显示放大图
   * @param enlarge_map_info 放大图信息
   * @return true-成功 false-失败
   */
  virtual bool OnEnlargeMapShow(const ShowEnlargeMapInfo& enlarge_map_info) { return true; }
  /**
   * 终点门脸大图显示，诱导仅提供显示时机
   */
  virtual void OnDestEnlargeMapShow() {};
  /**
   * 刷新动态连续路口放大图
   * @param enlarge_map_info 放大图的信息
   * @return true-成功 false-失败
   */
  virtual bool OnDynamicEnlargedMapUpdate(const UpdateDynamicEnlargedMapInfo& enlarge_map_info) {
    return true;
  }
  /**
   * 隐藏放大图
   * @param target 大图目标位置
   * @return true-成功 false-失败
   */
  virtual bool OnEnlargeMapHide() { return true; }
  /**
   * 显示高速出口
   * @param exit_info
   */
  virtual void OnExitInfoShow(const ExitInfo& exit_info) {};
  /**
   * 隐藏高速出口
   */
  virtual void OnExitInfoHide() {};
  /**
   * 拥堵事件显示更新
   * @param jam_info
   * @return true-成功 false-失败
   */
  virtual bool OnTrafficEventUpdate(const TrafficJamInfo& jam_info) { return true; };
  /**
   * 拥堵事件隐藏
   */
  virtual void OnTrafficEventHide() {};
  /**
   * 等灯气泡显示
   * @param info 等灯气泡信息
   */
  virtual void OnLightWaitBubbleShow(const mapbase::TrafficLightWaitInfo& info) {};
  /**
   * 等灯气泡隐藏
   */
  virtual void OnLightWaitBubbleHide(){};

  /**
   * 沿途电子眼列表
   * @param camera_list 摄像头列表
   */
  virtual void OnResetRouteCameraList(const RouteCameraRefreshInfo& camera_list) {}
  /**
   * 即将经过的电子眼列表(正在播报)
   * @param showing_cameras 摄像头index列表
   */
  virtual void OnApproachingCamera(const std::vector<int>& showing_cameras) {}
  /**
   *  巡航放⼤主路线⾃⻋前⽅（⾼速or城快700/其他300）的摄像头
   * @param cameras_in_range 从中pick用于放大,产品定的pick规则:按distance,最多同时放大两位置;
   *                         同一位置按priority最多显示两个icon
   */
  virtual void OnCameraEnlarge(const std::vector<RouteCameraInRange>& cameras_in_range) {}
  /**
   * 摄像头距离更新
   * @param update_cameras 更新距离的摄像头列表，camera.distance获取自车点到该摄像头的距离
   */
  // virtual void OnCameraUpdate(const std::vector<RouteCameraUpdateInfo>& update_cameras) {}
  /**
   * 巡航隐藏主路线⾃⻋后⽅摄像头
   * @param hide_cameras 需要隐藏的摄像头index列表
   */
  virtual void OnCameraHide(const std::vector<int>& hide_cameras) {}

  /**
   * 手图路线探测显示电子眼
   * @param cameras_show 要显示的电子眼序列
   */
  virtual void OnShowLightNavCameras(const std::vector<RouteCameraInRange>& cameras_show){}

  /**
   * 手图路线探测隐藏电子眼
   */
  virtual void OnHideLightNavCameras(){}
  /**
   * 测速区间开始显示
   * @param limit_zone_info 测速区间信息
   */
  virtual void OnSpeedZoneCameraShow(const SpeedLimitZoneInfo& limit_zone_info) {}
  /**
   * 进入区间测速路段
   * @param enter_camera 经过的进入测速摄像头
   */
  virtual void OnEnterSpeedZone(const RouteCameraInfo& enter_camera) {}
  /**
   * 区间测速内状态
   * @param info 区间测速状态
   */
  virtual void OnSpeedZoneUpdate(const SpeedLimitZoneUpdateInfo& info) {}
  /**
   * 离开区间测速路段
   * @param leave_camera 经过的离开区间测速摄像头
   */
  virtual void OnLeaveSpeedZone(const RouteCameraInfo& leave_camera) {}
  /**
   * 显示车道线
   * <br> LaneInfo 的d istance_to_route_end 目前为0，如需请调用 GetRemainDistance 接口
   * @param lane_info 车道线信息
   * @return true-成功 false-失败
   */
  virtual bool OnLaneGuideShow(const LaneInfo& lane_info) { return true; }
  /**
   * 隐藏车道线
   */
  virtual void OnLaneGuideHide() {}
  /**
   * 警示牌显示
   * @param warning_sign 警示牌
   */
  virtual void OnWarningTipShow(const WarningSignInfo& warning_sign) {}
  /**
   * 警示牌消失
   */
  virtual void OnWarningTipHide() {}
  /**
   * 当前路名更新
   * @param road_name 当前路名
   */
  virtual void OnCurrentRoadNameUpdate(const std::string& road_name) {}
  /**
   * 接近分歧点
   * @deprecated 目前不触发
   * @param fork_point 分歧点
   */
  virtual void OnApproachingDivergencePoint(const mapbase::RoutePos& fork_point) {}
  /**
   * 过服务端分歧点，分歧点后150m或200m触发
   * @apiNote 定位判断不准兜底：
   * 1、普通分歧点会触发onCompanionRouteOffCourse，该分歧点不再回调onPassedDivergencePoint；
   * 2、平行路分歧点可能不/晚触发onCompanionRouteOffCourse，分歧点后150m先回调一次；
   * 若后面回调onCompanionRouteOffCourse或更新伴随了不再回调onPassedDivergencePoint，否则200m再回调一次
   * @param divergence_point 分歧点信息
   */
  virtual void OnPassedDivergencePoint(const PassDivergencePointInfo& divergence_point) {}
  /**
   * 定位判断过分歧点(伴随偏航或主路偏航)
   * @apiNote 平行路分歧点可能不会触发
   * @param routeOffCourseInfo type 0-走主路，伴随偏航 1-驶入伴随，主路偏航
   */
  virtual void OnCompanionRouteOffCourse(const CompanionRouteOffCourseInfo& off_course_info) {}
  /**
   * 迷惑路口位置显示，只回调一次
   * @param cross_position 路口位置
   */
  virtual void OnConfuseCrossShow(const std::vector<ConfuseCrossInfo>& cross_position) {}
  /**
   * 在红绿灯前(目前不推荐使用)
   */
  virtual void OnBeforeRedLight() {}
  /**
   * 通过红绿灯
   */
  virtual void OnPassRedLight() {}
  /**
   * 路线上剩余红绿灯发生变化时，回调最新的
   * @param remain_redlight_list
   */
  virtual void OnRemainRedLightUpdate(const std::vector<RemainRedLightInfo>& remain_redlight_list) {}
  /**
   * 到达目的地
   */
  virtual void OnArrivalDestination() {}
  /**
   * 到达途径点
   * @param via 途径点
   */
  virtual void OnArrivalVia(const ViaArrivalInfo& via) {}
  /**
   * 即将到达隧道
   * @param tunnel 隧道
   */
  virtual void OnArrivalTunnel(const TunnelInfo& tunnel) {}
  /**
   * 吸附服务偏航驳回时途经点或终点判达，诱导SetRoute时回调
   * @param arrival_info 判达信息(诱导透传，若数据不对找吸附服务)
   */
  virtual void OnArrivalAfterYaw(const mapbase::YawArrivalInfo& arrival_info) {}

  /**
   * 接近收费站
   */
  virtual void OnBeforeTollStationShow() {}
  /**
   * 接近收费站播报
   */
  virtual void OnBeforeTollStationSpeak() {}
  /**
   * 通过收费站
   */
  virtual void OnPassTollStation() {}
  /**
   * 显示收费站信息
   * @param toll_name 收费站名
   */
  virtual void OnShowTollStationFee(const std::string& toll_name) {}
  /**
   * 显示收费站通道
   * @param info 高速设施(收费站)信息，包含收费站通道数据
   */
  virtual void OnShowTollAisles(const HighwayInstructionInfo& info) {}
  /**
   * 隐藏收费站通道
   */
  virtual void OnHideTollAisles() {}
  /**
   * 接近转向位置
   * @param info 进入路口
   */
  virtual void OnApproachingTurnIntersection(const ApproachingTurnInfo& info) {}
  /**
   * 离开上一个转向位置
   * @param last_segment_index 离开路口的导航段下标
   */
  virtual void OnLeftTurnIntersection(int last_segment_index) {}
  /**
   *  高速/国道上的收费站、服务区、独立加油站信息更新
   * @param instructions 高速设施列表：自车点由近及远最多透出3个高速设施，最多2个服务区，最多2个收费站，最多2个独立加油站（注意要求自车点在高速或国道上）；
   */
  virtual void OnHighwayInstructionUpdate(const std::vector<HighwayInstructionInfo>& instructions) {}
  /**
   * 隐藏高速/国道上的收费站、服务区、独立加油站看板
   */
  virtual void OnHighwayInstructionHide() {};
  /**
   * 智能定位状态更新
   * @param loc_state 只能定位
   */
  virtual void OnSmartLocStatusUpdate(mapbase::SmartLocStatus loc_state) {}
  /**
   * 已超过前方测速摄像限速
   * @param overspeed
   */
  virtual void OnCameraOverSpeed(const OverSpeedInfo& overspeed) {}
  /**
   * 鱼骨路上所有显示元素
   * @param vec_display_items 鱼骨路上所有显示元素
   * 定义参考rg::nav_Event.jce
   * type = 7，警示牌信息
   * type = 8，电子眼信息，电子眼sub_type=3，有speed信息
   * type = 12，点事件
   * type = 16，拥堵信息， traffic_length，traffic_passTimeMin有值
   */
  virtual void OnFishBoneItemsUpdate(const std::vector<CruiseFishBoneDisplayInfo> &vec_display_items) {};
  /**
   * 经过鱼骨上元素
   * @param vec_items 隐藏鱼骨上的元素索引
   */
  virtual void OnHideDisplayItemsOnFishbone(const CruiseFishBoneHideInfo& item) {};
  /**
   * 巡航态偏航重新算路后，隐藏原路线鱼骨路上所有显示元素
   */
  virtual void OnHideAllDisplayItemsOnFishbone() {};
  /**
   * 行中请求巡航算路<br>
   * 触发条件为（满足其一即可）：1、偏航  2、到达目的地（诱导或吸附判达） 3、到达云播下发的pos_refresh  4、 离上次算路间隔 interval_refresh （60s,来自云播）也会触发
   */
  virtual void OnRequestCruiserRoute() {};
  /**
   * 取得主路上所有动态点事件，端给诱导设置路线时解析主路线全部点事件，直接解析Rtt。仅巡航
   * @param vec_event_points 动态点事件数组
   */
  virtual void OnTrafficEventPointsUpdate(const std::vector<TrafficEventInfo> &vec_event_points) {};
  /**
   * 隐藏经过的动态点事件信息，走云播事件了
   * @param event_point 动态点事件数组
   */
  virtual void OnHideTrafficEventPoint(const TrafficEventInfo &event_point) {};
  /*
   * 诱导机动点信息更新
   */
  virtual void OnSegmentUpdate(const GuidanceUpdateInfo& basic_guidance_list) {};
  /**
   * bmw 定制接口：获取自车前方最多连续3个机动点
   */
  virtual void OnIntersectionListUpdate(const mapbase::RGIntersectionInfo& inter_info) {};
  /**
   * 主路线即将发生静默替换时回调
   * @param info 替换路线前后的routeid信息
   */
  virtual void OnSilentChangeMainRoute(const RecommendRouteInfo &info) {}
 /**
  * 定位信号弱状态信息
  * @param status_info 状态信息
  */
  virtual void OnGpsStatusUpdate(const GpsStatusInfo& status_info){};

  /**
  * 诱导透出信息
  * @param info 透出信息
  */
  virtual void OnRGOutputInfo(const RGOutputInfo& info){};
  /**
   * 进入空闲区间
   * @deprecated 即将废弃，空闲时间请使用OnTTSPlay中idle_time字段（诱导根据语音池事件间隔与速度进行计算，可信度较高）原空闲区间已不准确
   * @param info 空闲区间信息
   */
   virtual void OnEnterIdleSection(const IdleSectionInfo& info){};
  /**
   * 放大图更新
   * @param info 放大图信息
   */
  virtual void OnUpdateCommonEnlargedMap(const UpdateCommonEnlargedMapInfo& info){};
  /**
   * 透出后续的放大图信息
   * @param info 后续放大图信息
   */
  virtual void OnUpdateNextEnlargedMap(const NextBrInfos& info){};
  /**
   * 触发点事件众验
   * @param info 点事件信息
   */
  virtual void OnTrafficEventVerify(const TrafficEventInfo &info){};
  /**
   * 回调下载分片信息
   * @param info 分片信息
   */
  virtual void OnRequestGuidanceSliceInfo(const GuidanceSliceInfo& info) {};
  /**
   * 道路特征识别<br>
   * 目前包含智能倾角、连续弯道、路口时机场景
   * @param info 道路特征信息，map-biz 会参考该信息调整比例尺
   */
  virtual void OnRoadFeatureDetect(const RoadFeatureInfo &info){};
  /**
   * 隧道进度条显示
   * @param info 增益信息
   */
   virtual void OnTunnelProgressUpdate(const TunnelProgressInfo &info){};
  /**
   * 方向看板增益信息显示
   * @param info 增益信息
   */
  virtual void OnSpEnhanceShow(const SpEnhanceInfo &info){};

  /**
   * 方向看板增益信息隐藏
   * @param type 隐藏增益信息类型：参考SpEnhanceInfoType
   */
  virtual void OnSpEnhanceHide(int type){};
  /**
   * 1.进入熟路区间，一个熟路区间可能跨多个导航段；<br>
   * 2.无论熟路开关是否打开，都会这对回调；<br>
   * 3.从进入熟路区间开始会一直回调enter，直到离开熟路区间回调leave；<br>
   * 4.一直回调enter的原因：解决切伴随或驶入伴随后熟路区间可能会丢失一段距离的问题
   */
  virtual void OnEnterFamiliarSeg(){};
  /**
   * 退出熟路区间
   */
  virtual void OnLeaveFamiliarSeg(){};
  /**
   * 即将离开当前导航段，距离机动点a点小于200米
   */
  virtual void WillLeaveIntersection(){};
  /**
   * 已离开当前导航段，以过当前导航段b点为判定原则，进入到下个导航段
   */
  virtual void OnExitIntersection(){};

  /**
   * 进入隧道
   */
  virtual void OnEnterTunnel(const mapbase::EnterTunnelInfo& info){};

  /**
   * 离开隧道
   */
  virtual void OnLeaveTunnel(){};

  /**
   * 请求推荐车位
   */
  virtual void OnRequestParkingPlace(){}

  /**
   * 进入室内停车场
   */
  virtual void OnEnterIndoorParking(){}

  /**
   * 离开室内停车场
   */
  virtual void OnLeaveIndoorParking(){}

  /**
   * 红绿灯倒计时请求时机（in-开始轮询请求，out-结束轮询），用于客户端请求变灯倒计时（LightStatus）或入队（LightQueue）数据
   * @param info  红绿灯倒计时请求依赖信息
   */
  virtual void OnRedLightCountDown(const mapbase::RedLightInfo &info) {};

  /**
   * 区域面进入退出类事件接口统一
   * @param scene_type_info   区域面事件场景类型；高精区域面接近和退出（视觉开关）// 收费站进入和退出
   */
  virtual void OnPassingEvent(const mapbase::SceneTypeInfo& info) {};

  /**
   * 通用气泡显示信息回调（目前有长白实线气泡显示）
   * @param callback 回调类型，显示，隐藏
   * @param info 气泡数据
   */
  virtual void OnCommonBubble(const mapbase::CallbackType& callback, const mapbase::CommonBubbleInfo& info) {};
  /**
   * 主路线途经点剩余信息秒级更新；<br>
   * 注意：主路线没有途经点或最后一个途经点判达后，会每秒回调空list。客户端可借助这个停止更新eta
   * @param via_list 主路线所有没判达的途经点剩余信息，若非空，get(0)获取下个途经点
   */
  virtual void OnViaRemainInfoUpdate(const std::vector<mapbase::RGViaRemainInfo> &via_list){};
  /**
   * 请求主路线服务区充电站信息
   * @param req_info 请求信息
   */
  virtual void OnRequestSaChargingStationInfo(const mapbase::SaChargingStationReqInfo& req_info){}
  /**
   * 定位平行路切换提醒<br>
   * 播报控频：每次导航只提醒一次<br>
   * 显示控频(即回调时机)：1、累计3个点loc_hint_type有效且相同时； 2、主路线发生变化时重置；3、每个hint区间最多提醒1次，即吸附到新hint区间时重置；4、0-0 低置信度默认值，直接放弃，计数重置
   * @param info 平行路切换提醒信息
   */
  virtual void OnRemindLocHint(const LocHintInfo& info){}

  /**
   * 首次请求或更新请求绿波段
   * @param req 请求信息
   */
  virtual void OnReqGreenWave(const mapbase::GreenWaveReq& req){}
  /**
   * 进入绿波段，显示绿波面板；驶过绿波段的某灯，更新绿波面板
   * @param info 绿波段信息
   */
  virtual void OnGreenWaveShow(const mapbase::GreenWaveSegInfo& info){}
  /**
   * 退出绿波段，隐藏绿波面板
   */
  virtual void OnGreenWaveHide(){}
  /**
   * 显示dialog，隐藏端自己处理
   */
  virtual void OnDialogShow(const DialogInfo& info){}
  /**
   * 定位触发重新算路，吸附状态为ReRoute时触发
   * @param reroute_sub_scene_state 从定位透传子场景，枚举参考 ReRouteSubSceneState
   */
  virtual void OnLocReRoute(int reroute_sub_scene_state){}
  /**
   * 定位触发显示文案，定位输出ids（id列表），诱导依据id列表匹配服务下发的文案配置表，回调ids对应的文案信息
   */
  virtual void OnShowLocDisplayInfo(const LocDisplayInfo& info){}
  /**
   * @deprecated 已废弃
   * 诱导收到onHttpResponse成功后将rsp参数回传给端，端在该回调调用setHttpResponse将下述参数全部纯透传给诱导，方便自动从网络线程切回诱导线程
   */
  virtual void OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data, int32_t size){}

  /*
  * 准备请求异源匹配信息
  */
  virtual void OnRequestNextMMRoute() {}
};

class GuidanceStatisticsListener {
 public:
  /**
   * 特殊诱导类型回调
   * @param info_type 类型
   */
  virtual void OnSpecialGuidanceInfo(int info_type) = 0;
  /**
   * 偏航用时统计
   * @param outway_count 偏航用时
   */
  virtual void OnOutwayCountUpdate(int outway_count) = 0;
};

/**
 * 车道级导航消息监听器
 */
class GuidanceHDEventListener {
 public:
  /**
   * @deprecated 请使用接口 OnUpdateDistanceToTarget
   * 在高精覆盖区域内 HAD->SD边界点为参考点
   * @param length 距离HAD覆盖边界的沿路线距离
   * @param car_pos 自车路线吸附结果
   * @param hd_end_pos HD区域结束位置
   */
  virtual void OnUpdateDisToHDEnd(const int distance, const mapbase::RoutePos& car_pos, const mapbase::RoutePos& hd_end_pos){};

  /**
   * 到目标点剩余距离回调
   * @param type       目标点类型
   * @param distance   距离目标点的沿路线距离
   * @param car_pos    自车在路线上吸附位置
   * @param target_pos 目标点位置
   */
  virtual void OnUpdateDistanceToTarget(DistanceTargetType type, int distance, const mapbase::RoutePos& car_pos, const mapbase::RoutePos& target_pos) {}

  /**
   * 针对变化点，停止线，分离点引导区域内提醒
   * @param info 期望变道位置以及目标车道信息
   * 当行驶在推荐车道时，change_points_首元素对应的lane_id_中的值均为0
   * 当未行驶在推荐车道时，change_points_首元素对应的lane_id_中的值不全为0
   */
  virtual void OnNeedChangeToOtherLane(const mapbase::ChangeLaneGuideInfo& info){};

  /**
   * 驶离变道点
   */
  virtual void OnLeaveChangePos(){};

  /**
   * @deprecated 该接口即将于2025.1.1废弃，请尽快切到OnUpdateGuideAreas
   * 更新引导面回调接口
   * @param route_id 路线id
   * @param guide_area_info 引导面详细信息
   * @param guidearea_update_kind 引导面更新类型，show，append，update
   */
  virtual void OnUpdateGuideArea(const mapbase::DIHDAreaOperation& guide_area_operation){};
  virtual void OnUpdateGuideAreas(const std::vector<mapbase::DIHDAreaOperation>& guide_areas){};
  /**
   * 进入一片引导面，仅GuideAreaUpdateKind_Update有效
   */
  virtual void OnEnterGuideArea(const mapbase::DIHDAreaOperation& guide_area_operation){};
  /**
   * 离开一片引导面，仅GuideAreaUpdateKind_Update有效
   */
  virtual void OnLeaveGuideArea(const mapbase::DIHDAreaOperation& guide_area_operation){};

  /**
   * hd特殊车道驶入状态更新
   */
  virtual void OnSpecialLaneStateUpdate(const mapbase::SpecialLaneStateInfo& info){};

  /**
   * 进入最佳变道区
   */
  virtual void OnEnterSwitchLaneArea(const mapbase::SwitchLaneAreaInfo& info){};
  /**
   * 离开最佳变道区
   */
  virtual void OnLeaveSwitchLaneArea(const mapbase::SwitchLaneAreaInfo& info){};
};

}

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCEEVENTLISTENER_H_
