/**
 * Copyright © 2021 Tencent. All rights reserved.
 */
#import <MapBaseNew/MapBaseGuidanceBaseStructure.h>
#import <MapBaseNew/MapBaseRouteStructure.h>

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Enums

typedef NS_ENUM(NSInteger, TRGBeepType) {
    RGBeepTypeNone = 0,
    RGBeepTypeBeep = 1,
    RGBeepTypeDengDeng = 2,
    RGBeepTypeVibration = 3,
    RGBeepTypePassSpeedCamera = 6,
};

typedef NS_ENUM(NSInteger, TRGTTSType) {
    RGTTSTypeForceVoiceTTS = 0,
    RGTTSTypeOutway = 1,
    RGTTSTypeRemind3rd4th5thTimes = 3,
    RGTTSTypeCamera = 4,
    RGTTSTypeRemind0th1st2ndTimes = 5,
    RGTTSTypeTraffic = 6,
    RGTTSTypeWarningSign = 7,
    RGTTSTypeSeatingArea = 8,
    RGTTSTypeStraight = 9,
    RGTTSTypeRedlightWaiting = 10,
    RGTTSTypeHeartBreaking = 11,
    RGTTSTypeRoundAboutExit = 12,
    RGTTSTypeJunctionPoint = 13,
    RGTTSTypeSpeedLimit = 14,
    RGTTSTypeLane = 15,
    RGTTSTypeWarningSchool = 16,
    RGTTSTypeSpecialGuidance = 17,
    RGTTSTypeFCross = 18,
    RGTTSTypeTunnelIns = 19,
    RGTTSTypeInnerRoad= 20,
#ifdef __AUXILIARY_INFORMATION_FOR_CHECK__
    RGTTSTypeBeforeTunnel = 21,
    RGTTSTypeAfterTunnel = 22,
#endif
    RGTTSTypeReadyGo = 23,
    RGTTSTypeLongTrip = 24,
    RGTTSTypeReferencia = 25,
    RGTTSTypeForceGuidance = 26,
    RGTTSTypeCityAndWeather = 27,
    RGTTSTypeSpeedWillChange = 28,
    RGTTSTypePassCamera = 29,
    RGTTSTypeIntervalCamera= 30,
    RGTTSTypeLslineTip= 31,
    RGTTSTypeTrafficEvent = 32,
    RGTTSTypePointSpeedLimit = 33,
    RGTTSTypeTTSTypeNone = 8888,
};

typedef NS_ENUM(NSInteger, TRGTTSSubType) {
    RGTTSSubTypeDestination = 1,
    RGTTSSubTypeIntersection,
    RGTTSSubTypeIntersectionStraight,
    RGTTSSubTypeRoundAbout,
    RGTTSSubTypeSpecialGuidancee,
    RGTTSSubTypeTollgate,
    RGTTSSubTypeTunnel,
    RGTTSSubTypeViaPoint,
};

typedef NS_ENUM(NSInteger, TRGIntersectionType) {
    RGIntersectionTypeNone = 0,
    RGIntersectionTypeStraight = 1,
    RGIntersectionTypeTurnLeft = 2,
    RGIntersectionTypeTurnRight = 3,
    RGIntersectionTypeTurnAround = 4,
    RGIntersectionTypeEnterTheRoundabout = 5,

    RGIntersectionTypeDriveInLeftSide = 6,
    RGIntersectionTypeDriveInRightSide = 7,
    RGIntersectionTypeStraightInMiddle = 8,

    RGIntersectionTypeBendLeft= 10,
    RGIntersectionTypeEnterMainRoad = 11,
    RGIntersectionTypeKeepLeft = 12,
    RGIntersectionTypeKeepLeft2 = 13,

    RGIntersectionTypeEnterLeftFork = 14,
    RGIntersectionTypeEnterMiddleFork = 15,
    RGIntersectionTypeFourwayRoadLeft = 16, //四岔路口最左侧岔路
    RGIntersectionTypeFourwayRoadLeft2 = 17, //四岔路口左边第二岔路
    RGIntersectionTypeStraightLeftSide= 18,

    RGIntersectionTypeBendRight = 20,
    RGIntersectionTypeEnterSideRoad = 21,
    RGIntersectionTypeKeepRight = 22,
    RGIntersectionTypeKeepRight2 = 23,

    RGIntersectionTypeEnterRightFork = 24,
    RGIntersectionTypeEnterMiddleFork2 = 25,
    RGIntersectionTypeFourwayRoadRight2 = 26, //四岔路口右边第二岔路
    RGIntersectionTypeFourwayRoadRight = 27, //四岔路口最右侧岔路
    RGIntersectionTypeStraightRightSide = 28,

    RGIntersectionTypeSharpLeft = 30,
    RGIntersectionTypeSharpLeft2 = 31,

    RGIntersectionTypeTurnLeftLeft = 32,
    RGIntersectionTypeTurnLeftRight = 33,
    RGIntersectionTypeTurnLeftPSILeft = 34,
    RGIntersectionTypeTurnLeftPSICenter = 35,
    RGIntersectionTypeTurnLeftPSIRight = 36,

    RGIntersectionTypeStraightBearLeft = 37,

    RGIntersectionTypeEnterLeftWithLeftBack = 38,

    RGIntersectionTypeSharpRight = 40,
    RGIntersectionTypeSharpRight2 = 41,

    RGIntersectionTypeTurnRightLeft = 42,
    RGIntersectionTypeTurnRightRight = 43,
    RGIntersectionTypeTurnRightPSILeft = 44,
    RGIntersectionTypeTurnRightPSICenter = 45,
    RGIntersectionTypeTurnRightPSIRight = 46,

    RGIntersectionTypeStaightBearRight = 47,

    RGIntersectionTypeEnterRightWithRightBack = 48,

    RGIntersectionTypeTakeTheFirstExit = 51,
    RGIntersectionTypeTakeTheSecondExit = 52,
    RGIntersectionTypeTakeTheThirdExit = 53,
    RGIntersectionTypeTakeTheFourthExit = 54,
    RGIntersectionTypeTakeTheFifthExit = 55,
    RGIntersectionTypeTakeTheSixthExit = 56,
    RGIntersectionTypeTakeTheSeventh = 57,
    RGIntersectionTypeTakeTheEighthExit = 58,
    RGIntersectionTypeTakeTheNinthExit = 59,

    RGIntersectionTypeYouHaveReachedDestination = 60,
    RGIntersectionTypeYouHaveReachedDestinationLeft = 61,
    RGIntersectionTypeYouHaveReachedDestinationRight = 62,
    RGIntersectionTypeViaPoint = 63,

    RGIntersectionTypeEnterTunnel = 64,
    RGIntersectionTypeUnderpass = 65,
    RGIntersectionTypeTollGate = 66,

    RGIntersectionTypeKeepStaightOnLeft = 81,
    RGIntersectionTypeStaightOnRight = 82,
    RGIntersectionTypeTriplePathRightRight = 83,
    RGIntersectionTypeTriplePathLeftLeft = 84,


    RGIntersectionTypeLeftUTurnEnterLeftRoad = 85, //左掉头进入左侧道路
    RGIntersectionTypeLeftUTurnEnterRightRoad = 86, //左掉头进入右侧道路

    RGIntersectionTypeRightUTurn = 87, //右调头

    RGIntersectionTypeUTurnEnterLeftRoad = 88, //右掉头进入左侧道路
    RGIntersectionTypeUTurnEnterRightRoad = 89, //右掉头进入右侧道路
    RGIntersectionTypeLongDashlineStraight = 90, //长距离虚直箭头

    //公交车导航相关
    RGIntersectionTypeBusUp = 999,
    RGIntersectionTypeBusDown,
    RGIntersectionTypeSpecialGuidance
};

typedef NS_ENUM(NSInteger, TRGAccActionType) {
    RGAccActionTypeNone = 0,            /// 默认值
    RGAccActionTypeUpOverpass = 1,      /// 上天桥
    RGAccActionTypeDownOverpass = 2,    /// 下天桥
    RGAccActionTypeInUnderpass = 3,     /// 进地下通道
    RGAccActionTypeOutUnderpass = 4     /// 出地下通道
};

typedef NS_ENUM(NSInteger, TRGMMultiRouteMatchStatus) {
    RGMMultiRouteMatchStatusNull = -1,
    RGMMultiRouteMatchStatusAllOnRoute,       /**< 全部吸附成功 */
    RGMMultiRouteMatchStatusAllYaw,           /**< 所有路线偏航 */
    RGMMultiRouteMatchStatusMainYaw,          /**< 主路线偏航  */
    RGMMultiRouteMatchStatusCompanionYaw,     /**< 伴随路线偏航 */
    RGMMultiRouteMatchStatusMainSlightlyYaw   /**< 主路线微偏  */
};

typedef NS_ENUM(NSInteger, TRGMRouteForWhat) {
    RGMRouteForWhatUnknown = 0,                    /// 模拟导航
    RGMRouteForWhatNavigation,                     /// 导航下发的路径
    RGMRouteForWhatDeviation,                      /// 偏航下发的路径
    //RGMRouteForWhatSERVERCHANGE                  /// 后台换路
    RGMRouteForWhatParking = 4,                    /// 停车场算路
    RGMRouteForWhatAdd,                            /// 动态换路调用的setmappoints+这个
    RGMRouteForWhatParallel,                       /// 主辅路切换后的算路
    RGMRouteForWhatPassDivergenceWithCur,          /// 伴随路线经过分歧点走的选中路线
    RGMRouteForWhatassDivergenceWithComponent,     /// 伴随路线经过分歧点走的侯选路线
    RGMRouteForWhatRefresh,                        /// 手动刷新伴随路线
    RGMRouteForWhatAddViaPoint,                    /// 添加途经点
    RGMRouteForWhatDelViaPoint,                    /// 删除途经点
    RGMRouteForWhatPreferenceChanged,              /// 改变路线偏好
    RGMRouteForWhatSilentAdd,                      /// 静默添加伴随路线
    RGMRouteForWhatRecommendParking                /// 选择推荐停车场后刷新路线
};

typedef NS_ENUM(NSInteger, TRGWalkTipsArriveStatus) {
    RGWalkTipsArriveStatusNull = 0,
    RGWalkTipsArriveStatusArrived = 1,
    RGWalkTipsArriveStatusCrossing = 2,
    RGWalkTipsArriveStatusPassed = 3,
};

/// 超速显示类型
typedef NS_ENUM(NSInteger, TRGCameraOverSpeedKind) {
    RGCameraOverSpeedKind_DEFAULT = 0,                      /// 默认值
    RGCameraOverSpeedKind_NOT_OVER_SPEED = 1,               /// 实时测速电子眼未超速
    RGCameraOverSpeedKind_OVER_SPEED_TIPS = 2,              /// 实时测速电子眼超速提示
    RGCameraOverSpeedKind_DANGEROUS_SPEED = 3,              /// 实时测速电子眼危险驾驶
    RGCameraOverSpeedKind_INTERVAL_AVERAGE_OVER_SPEED = 4,  /// 区间测速平均速度超速
    RGCameraOverSpeedKind_NULL = 5,                         /// 不返回速度 --
};

/// 超速显示类型
typedef NS_ENUM(NSInteger, TRGOverSpeedType) {
    RGOverSpeedType_CAMERA = 0,      /// 电子眼超速
    RGOverSpeedType_ROAD = 1,        /// 道路超速
};

typedef NS_ENUM(NSInteger, TRGCompanionRouteSwitchType) {
    RGCompanionRouteSwitchType_OriginRoute = 0,         /**< 切换后是原路线 */
    RGCompanionRouteSwitchType_CompanionRoute = 1       /**< 切换后是伴随路线 */
};

typedef NS_ENUM(NSInteger, TRGConfuseCrossType) {
    RGConfuseCrossType_None = 0,       /**< 无 */
    RGConfuseCrossType_FCross = 1,     /**< F路口 */
    RGConfuseCrossType_Exit = 2,       /**< 高速出口 */
    RGConfuseCrossType_RoundaboutExit = 3  /**< 环岛出口 */
};

/// 高速设施类型
typedef NS_ENUM(NSInteger, TRGHighwayInstructionType) {
    RGHighwayInstructionType_SeatingArea = 1,  /**< 服务区 */
    RGHighwayInstructionType_TollStation = 2   /**< 收费站 */
};

/// 信号弱状态
typedef NS_ENUM(NSInteger, TRGGpsWeakStatus) {
    RGGpsWeakStatus_ShortTermGPSWeak = 2001,  //普通GPS信号弱 信号弱持续5s
    RGGpsWeakStatus_ShortTermGPSNormal = 2002,  //普通GPS信号弱恢复正常
    RGGpsWeakStatus_LongTermGPSWeak = 2003, //GPS信号弱 信号弱持续60s
    RGGpsWeakStatus_LongTermGPSNormal = 2004 //GPS信号弱持续超过60s后恢复正常
};

//辅助设施(tips)类型
typedef NS_ENUM(NSInteger, TRGRouteGuidanceAccessoryType) {
    RGRouteGuidanceAccessoryType_None = 0,
    RGRouteGuidanceAccessoryType_GasStation = 1,//加油站
    RGRouteGuidanceAccessoryType_SeatingArea = 2,//服务区
    RGRouteGuidanceAccessoryType_Camera = 4,//电子眼
    RGRouteGuidanceAccessoryType_TollStation = 5,//收费站
    RGRouteGuidanceAccessoryType_JunctionPoint = 6,//车辆交汇点
    RGRouteGuidanceAccessoryType_TunnelEntrance = 7,//隧道入口
    RGRouteGuidanceAccessoryType_ForceGuidance = 8, //强制诱导tips
    RGRouteGuidanceAccessoryType_RoundaboutExit = 9,//环岛出口
    RGRouteGuidanceAccessoryType_WarningSigns = 10,//警示牌
    RGRouteGuidanceAccessoryType_Straight = 11,//直行提示
    RGRouteGuidanceAccessoryType_ICJCT = 12, //高速和高速之间连接路，匝道
    RGRouteGuidanceAccessoryType_InnerRoad = 13, //内部路
    RGRouteGuidanceAccessoryType_SpecialGuidance = RGRouteGuidanceAccessoryType_WarningSigns+10,//特殊诱导
    RGRouteGuidanceAccessoryType_TypeMax = (NSInteger)0xFFFFFFFF
};

/// 服务区支持服务类型
// CATERING-1, SHOPPING-2, WC-3, CARREPAIRING-4, PARKING-5, GASSTATION-6, CHARGINGPILE-7, ACCOMMODATION-8, SHOWER-9
typedef NS_ENUM(NSInteger, TRGSAPOIInfoType){
    RGSAPOIInfoType_Parking = 0,        /// 停车场
    RGSAPOIInfoType_GasStation,         /// 加油站
    RGSAPOIInfoType_Dining,             /// 餐厅
    RGSAPOIInfoType_WC,                 /// 厕所
    RGSAPOIInfoType_ChargeStation,      /// 充电站
    RGSAPOIInfoType_GroceryStore,       /// 购物
    RGSAPOIInfoType_Build,              /// 维修
    RGSAPOIInfoType_Hotel,              /// 住宿
    RGSAPOIInfoType_Shower,             /// 洗澡
};

typedef NS_ENUM(NSInteger, TRGIdleSectionType) {
    RGIdleSectionType_HIGH = 0,         /// 高级
    RGIdleSectionType_LOW = 1           /// 低级
};

#pragma mark - Models

/// routeguidance 基础 model
@interface TRGModel : NSObject

@end


/**
 * SetRoute参数结构,步骑复用
 * @see {@link walk::WalkApi::SetRoute}
 */
@interface TRGGreenTravelSetRouteParam : TRGModel
@property (nonatomic, assign) TRGMRouteForWhat routeForWhat;    /// 导航原因，对应ROUTE_FOR系列
@property (nonatomic, copy) NSString *selectedRouteID;          /// 指定主路线id，默认第0条
///
/**
 * 主路线剩余时间，用于定制开始导航播报，默认值:-1，单位：分钟
 * 当为0时，不播报开始导航中的时间和距离信息
 * 当-1时，时间会直接从主路线数据中获取
 */
@property (nonatomic, assign) NSInteger remainTimeMinute;

/**
 * 主路线剩余距离，用于定制开始导航播报，单位：米
 * 当<15时，不播报开始导航中的时间和距离信息
 * 当为-1时，距离会直接从主路线数据中获取
 */
@property (nonatomic, assign) NSInteger remainDistance;

@end


/**
 * AR步导，设施通过状态
 */
@interface TRGWalkTipsArriveStatusInfo : TRGModel

@property (nonatomic, assign) TRGWalkTipsArriveStatus walkStatus; /**< 设施通过状态 */
@property (nonatomic, copy) NSString *tipsType; /**< 设施类型 */

@end


/**
 * 步骑EventPoint通用数据封装
 */
@interface TRGGreenTravelEventPointItem : TRGModel

@property (nonatomic, assign) TRGIntersectionType intersectionType; /**< 当前转向的类型 */
@property (nonatomic,   copy) NSString *action; /**< 当前转向动作，举例：左转 */
@property (nonatomic, assign) NSInteger segmentIndex; /**< 导航段index */
@property (nonatomic, assign) NSInteger roadLength; /**< 当前导航段长度，单位m */
@property (nonatomic,   copy) NSString *roadName; /**< 当前导航段道路名 */
@property (nonatomic,   copy) NSString *nextRoadName;/**<下一个导航段路名/方向名 */
@property (nonatomic, assign) float outAngle; /**<AR导航方向角 */
@property (nonatomic, assign) TRGAccActionType accActionType;/**<辅助动作:上/下天桥 进入/离开地下通道,for AR WalkGuider */
@property (nonatomic, assign) NSInteger distance;   ///定位点到当前转向距离，单位m

@end


@interface TRGGreenTravelUpdateInfoItem : TRGModel

@property (nonatomic, copy) NSString *routeID;  /// 路线id
@property (nonatomic) void *matchResult;        ///定位吸附结果（mapbase数据结构转指针）
@property (nonatomic, assign) NSInteger totalDistanceLeft;      ///当前转向到终点距离，单位m
@property (nonatomic, assign) NSInteger distanceToShapePoint;   ///定位点到前方第一个形状点距离，单位m
/**
 * 连续EventPoint通用数据；步行size为2，
 * event_points[0]表示next_event_point，
 * event_points[1]表示next_next_event_point；
 * 骑行size为1，只有next_event_point。
 */
@property (nonatomic, copy) NSArray<TRGGreenTravelEventPointItem *> *eventPoints;

@end


/**
 * 步骑诱导状态更新信息
 */
@interface TRGGreenTravelUpdateInfo : TRGModel

@property (nonatomic, assign) TRGMMultiRouteMatchStatus matchStatus;    /// 当前吸附场景
@property (nonatomic, copy) NSArray<TRGGreenTravelUpdateInfoItem *> *guidanceInfo;   /// 当前诱导信息，步骑行size为1

@end


/**
 * 语音播报信息
 */
@interface TRGPlayTtsInfo : TRGModel

@property (nonatomic,   copy) NSString *text;           /// 语音文本信息
@property (nonatomic, assign) TRGBeepType beepType;      /// 语音提示音
@property (nonatomic, assign) TRGTTSType ttsType;        /// 语音类型
@property (nonatomic, assign) TRGTTSSubType ttsSubType;  /// 语音子类型
@property (nonatomic, assign) NSInteger estrellaNum;    /// 明星语音编号
@property (nonatomic, assign) NSInteger priority;       /// 优先级

#ifdef __AUXILIARY_INFORMATION_FOR_CHECK__
@property (nonatomic, assign) NSInteger checkType;
@property (nonatomic, assign) NSInteger intersectionLat;
@property (nonatomic, assign) NSInteger reasonOfEndOfNavigation;
@property (nonatomic, assign) NSInteger round;
@property (nonatomic, assign) NSInteger intersection;
@property (nonatomic,   copy) NSString *subType;
@property (nonatomic, assign) NSInteger subtypeDistance;
@property (nonatomic, assign) NSInteger fcrossNumbers;
@property (nonatomic, assign) NSInteger enterFCrossDistanceBeforeFirstFCross;
@property (nonatomic, assign) NSInteger enterFCrossBasicDistanceBeforeFirstFCrossMin;
@property (nonatomic, assign) NSInteger enterFCrossBasicDistanceBeforeFirstFCrossMax;
@property (nonatomic, assign) NSInteger broadcastFirstFCrossDistances;
@property (nonatomic,   copy) NSString *flag;
@property (nonatomic,   copy) NSString *arrow;
@property (nonatomic, assign) NSInteger lanePointX;
@property (nonatomic, assign) NSInteger lanePointY;
@property (nonatomic, assign) NSInteger tunnelType;
@property (nonatomic, assign) NSInteger intersectionLon;
@property (nonatomic, assign) NSInteger totalDistanceLeft;
@property (nonatomic, assign) NSInteger distanceToEvent;
#endif //#ifdef __AUXILIARY_INFORMATION_FOR_CHECK__

@end


/**
 * 步骑吸附失败信息
 */
@interface TRGMatchFailedInfo : TRGModel

@property (nonatomic, copy) NSString *text;     /// 吸附失败文案
@property (nonatomic, assign) NSInteger reason; /// 失败原因

@end


@interface TRGOffCourseInfo : TRGModel

@property (nonatomic, assign) NSInteger yaw_type; /**< 偏航原因 */
@property (nonatomic,   copy) NSString *yaw_message; /**< 偏航说明 */

@end


@interface TRGShowEnlargeMapInfo : TRGModel

@property (nonatomic, assign) NSInteger segment_index;  /**< 导航段索引下标 */
@property (nonatomic, assign) NSInteger segment_inner_index;      /**< 导航段内索引下标，如导航段内有多个放大图，第2个放大图的段内下标为1 */
@property (nonatomic, assign) NSInteger cross_inner_distance;     /**< 路口内距离 */
@property (nonatomic, assign) NSInteger dis_to_map;      /**< 到放大图的距离 */
@property (nonatomic,   copy) NSString  *pattern;  /**< 放大图下载地址 */
@property (nonatomic,   copy) NSString  *display_text;/**< 放大图所在路口显示文案，进入隧道 通过桥梁 通过路口等 */


@end


@interface TRGUpdateCommonEnlargedMapInfo : TRGModel

@property (nonatomic, assign) NSInteger dis_to_map;    /**< 到放大图的距离 */

@end


@interface TRGUpdateDynamicEnlargedMapInfo : TRGModel

@property (nonatomic, assign) NSInteger type;                 /**< 刷新的类型 */
@property (nonatomic, assign) NSInteger dis_to_first_inner;   /**< 与第一个路口的距离 */
@property (nonatomic, strong) NSData *jce_data; /**< jce data 将C++对象序列化得出 透传给底图使用 */

@end


@interface TRGExitInfo : TRGModel

@property (nonatomic, assign) NSInteger exitID;              /**< 出口id */
@property (nonatomic, strong) TMapBaseRoutePos *route_pos; /**< 出口位置 */
@property (nonatomic,   copy) NSString *name_info;          /** 出口名称 */

@end


/**
 * 放大范围内(高/快道路700,普300)摄像头
 */
@interface TRGRouteCameraInRange : TMapBaseRouteCameraInRange

@property (nonatomic,assign) NSInteger sub_type; // 电子眼子类型，仅手图使用
@property (nonatomic,assign) NSInteger speed;  // 电子眼限速值，单位km/h；限速电子眼为正值，非限速电子眼为0；仅手图使用

@end


@interface TRGCamerasListInfo : TRGModel

@property (nonatomic,  copy) NSArray<NSNumber *> *list; /**<摄像头ID列表 */
@property (nonatomic,strong) NSData *jceData; //jce数据

@end


/**
 * 区间限速信息
 */
@interface TRGSpeedLimitZoneInfo : TRGModel

@property (nonatomic, strong) TMapBaseRoutePos *begin;  /**< 区间测速起点 */
@property (nonatomic, strong) TMapBaseRoutePos *end;   /**< 区间测速终点 */
@property (nonatomic, assign) NSInteger speed_limit_kmph;   /**< 区间测速限速值，单位：kmph */
@property (nonatomic, assign) NSInteger length;   /**< 区间测速长度 */

@end


/**
 * 在区间测速内状态更新
 */
@interface TRGSpeedLimitZoneUpdateInfo : TRGModel

@property (nonatomic, assign) NSInteger average_speed_kmph;       /**< 当前平均车速，单位：kmph */
@property (nonatomic, assign) NSInteger remain_length;            /**< 区间测速区间剩余距离，单位: m */
@property (nonatomic, assign) NSInteger speed_limit;              /**< 区间测速限速值，单位: kmph */
@property (nonatomic, assign) TRGCameraOverSpeedKind average_speed_status; /**< 当前超速状态 */
@property (nonatomic, assign) CLLocationCoordinate2D coordinate;  /**< 区间测速摄像头终点位置 */

@end


@class TMapBaseWarningSignInfo;
/**
 * 警示牌信息
 */
@interface TRGWarningSignInfo : TRGModel

@property (nonatomic, assign) TMapBaseRouteGuidanceWarningType warning_type; /**< 警示牌类型 */
@property (nonatomic, strong) TMapBaseRoutePos *route_pos;    /**< 警示牌位置 */

- (TMapBaseWarningSignInfo*) toMapBaseWarningSignInfo;
@end


/**
 * 摄像头超速信息
 */
@interface TRGOverSpeedInfo : TRGModel

@property (nonatomic, assign) TRGOverSpeedType over_speed_type;   /**< 超速类型 */
@property (nonatomic, assign) NSInteger speed_kmph; /**< 当前速度，单位: kmph */
@property (nonatomic, assign) NSInteger limit_speed_kmph; /**< 当前限速，单位：kmph */
@property (nonatomic, assign) TRGCameraOverSpeedKind over_speed_kind; /**< 超速状态 */
@property (nonatomic, assign) CLLocationCoordinate2D coordinate;    /**< 如果有摄像头限速，这里给出摄像头位置 */
@property (nonatomic, assign) TMapBaseGuidanceCameraType camera_type; /**< 摄像头超速类型 */

@end


@interface TRGRecommendRouteInfo : TRGModel

@property (nonatomic,   copy) NSString *current_routeid;    /// 当前路线id
@property (nonatomic,   copy) NSString *recommend_routeid;  ///  即将要替换成的路线id

@end


/**
 * 通过多路线分歧点信息
 */
@interface TRGPassDivergencePointInfo : TRGModel

@property (nonatomic,   copy) NSString *current_route_id;     /**< 当前路线id */
@property (nonatomic,   copy) NSArray<NSString *> *companion_route_ids; /**< 伴随路线列表 */

@end


/**
 * 伴随路线偏航回调信息
 */
@interface TRGCompanionRouteOffCourseInfo : TRGModel

@property (nonatomic, assign) TRGCompanionRouteSwitchType type; /**< 伴随路线切换类型，用于判断主路线是否变化 */
@property (nonatomic,   copy) NSArray<NSString *> *deleted_route_ids; /**< 删除的路线清单 */
@property (nonatomic,   copy) NSString * current_route_id; /**< 当前保留的主路线id */
@property (nonatomic,   copy) NSString * yaw_message; /**< 偏航描述，用于埋点和问题分析 */

@end


/**
 * 迷惑路口信息
 * 迷惑路口是和用户转向位置相近，且形态相似的路口
 */
@interface TRGConfuseCrossInfo : TRGModel

@property (nonatomic, assign) TRGConfuseCrossType type;        /**< 迷惑路口类型 */
@property (nonatomic, assign) NSInteger index; /**< 迷惑路口序号 */
@property (nonatomic, strong) TMapBaseRoutePos *route_pos;    /**< 迷惑路口路线位置 */

@end


@interface TRGRemainRedLightItemInfo : TRGModel

@property (nonatomic, assign) NSInteger state;  /**<0-正常吸附 -1-当前未吸附，之前吸附上过 -404-从来没吸附上 */
@property (nonatomic,   copy) NSString *route_id;  /**< 路线id */
@property (nonatomic, assign) NSInteger remain_red_light_count;/**< 剩余红绿灯个数 */
@property (nonatomic, assign) NSInteger distance_to_next_red_light;/**< 到下一个红绿灯距离 */
@property (nonatomic, strong) TMapBaseRoutePos *pos;/**< 下一个红绿灯位置 */

@end


@interface TRGRemainRedLightInfo : TRGModel
@property (nonatomic, copy) NSArray <TRGRemainRedLightItemInfo *> *redLights; /**< 剩余红绿灯 */
@end


/**
 * 途经点信息
 */
@interface TRGViaArrivalInfo : TRGModel

@property (nonatomic, assign) NSInteger segment_index; /**< 导航段下标 */
@property (nonatomic, strong) TMapBaseRoutePos *route_pos;/**< 途径点路线位置 */

@end


/**
 * 隧道展示信息
 */
@interface TRGTunnelInfo : TRGModel
@property (nonatomic, assign) NSInteger length; /**< 隧道长度 */
@property (nonatomic, strong) TMapBaseRoutePos *tunnel_entrance_pos; /**< 隧道入口位置 */
@end


@interface TRGSAPOIInfo : TRGModel

@property (nonatomic, assign) TRGSAPOIInfoType type; /**< poi类型*/
@property (nonatomic,   copy) NSString *POIDesc; /**<poi说明 */

@end


/**
 * 高速设施信息
 * <br> vec_aisle[i]指左i+1收费通道数据,取值范围[1,254];
 * <br> 每个元素从低位到高位(Bit0~Bit5)依次标识是/否(1/0)支持该支付方式：
 * <br> Bit0:现金
 * <br> Bit1:ETC
 * <br> Bit2:微信
 * <br> Bit3:支付宝
 * <br> Bit4:其他支付
 * <br> Bit5:预留
 * <br> 样例:000010代表该通道仅支持ETC支付
 */

@interface TRGHighwayInstructionInfo : TRGModel

@property (nonatomic, assign) TRGHighwayInstructionType type;  /**< 高速设施类型 */
@property (nonatomic, assign) NSInteger distance;  /**< 自车点到设施距离 单位米 */
@property (nonatomic,   copy) NSString *name;/**< 高速设施中文名称 */
@property (nonatomic,   copy) NSString *rawID;
@property (nonatomic,   copy) NSString *saDesc;
@property (nonatomic, strong) TMapBaseRoutePos *route_pos; /**< 高速设施所在路线位置 */
@property (nonatomic,   copy) NSArray<NSNumber *> *vec_aisle;/**< 收费站通道数据  std::vector<int> vec_aisle;      < 收费站通道数据 */
@property (nonatomic,   copy) NSArray<TRGSAPOIInfo *> *saPoiVec;/**< 收费站通道数据 */

@end


/**
 * GPS信号弱信息
 */
@interface TRGGpsStatusInfo : TRGModel

@property (nonatomic, assign) TRGGpsWeakStatus status;/**< gps信号状态 */
@property (nonatomic,   copy) NSString *tips;/**< 信号弱提示文案*/

@end


@interface TRGIdleSectionInfo : TRGModel

@property (nonatomic, assign) int  distance; /**< 空闲区间长度 */
@property (nonatomic, assign) TRGIdleSectionType  type; /**< 空闲区间等级 */

@end

/// 路口放大图
@interface TRGBrInfo : TRGModel

@property (nonatomic,   copy) NSString *pattern;  /**< 样式*/
@property (nonatomic,   copy) NSString *arrow; /**< 箭头*/
@property (nonatomic, assign) int  type;

@end

/// 预下载
@interface TRGNextBrInfos : TRGModel

@property (nonatomic,   copy) NSArray <TRGBrInfo*> *next_br_infos;  /**< 样式*/

@end


@interface TRGGuidanceAccessoryInfo : TRGModel

@property (nonatomic,assign) NSInteger distance_to;/**< 自车点到该数据剩余距离 */
@property (nonatomic,assign) TRGRouteGuidanceAccessoryType type;/**<辅助数据类型 */
@property (nonatomic,strong) TMapBaseRoutePos *route_pos; /**<路线坐标 */

@end


@class TRGUpdateItemInfo;
/**
 * 大定位 match_location_info 对象传入 诱导引擎setMapPoint 接口的回调
 */
@interface TRGUpdateInfo : TMapBaseUpdateInfo

@property (nonatomic, strong) TMapBasePosPoint *origin_pos;//原始定位位置
@property (nonatomic,   copy) NSArray<TRGUpdateItemInfo *> *guidance_info;   /**< 当前诱导详情 */

@end


//非诱导数据对象，端上自己构造对象
@interface TRGHintInfo : TRGModel

@property (nonatomic, assign) TMapBaseRoadHintType hint_type; /**< 当前的平行路状态 */
@property (nonatomic,   copy) NSArray<NSNumber *> *show_hint_type;  /**< 展示用的可切换平行路类型 */

@end


//非诱导数据对象，端上自己构造对象
@interface TRGRouteMatchInfo : TRGModel

@property (nonatomic, strong) CLLocation *matchedLocation;/**< 吸附点 端上自己构造 */
@property (nonatomic, assign) NSInteger segmentIndex;  /**< 导航段下标 */
@property (nonatomic, assign) NSInteger matchedIndex;  /**< 吸附位置索引 */
@property (nonatomic, assign) TMapBaseMercatorCentimeterPos  *mercatorCmPos; /**< 墨卡托横坐标系 */
@property (nonatomic, assign) CGPoint matchedMapPoint;/**< 像素坐标 */
@property (nonatomic, assign) TMapBaseLocationSource source;/**<  定位类型 */

@end


@interface TRGRouteNextEventPointInfo : TRGModel

@property (nonatomic, assign) NSInteger userTag;  /**< 导航段下标 */
@property (nonatomic, assign) NSInteger segmentIndex;  /**< 吸附下标 */
@property (nonatomic, assign) NSInteger intersection;   /**< 当前机动点类型, 参见诱导定义：IntersectionType */
@property (nonatomic, assign) NSInteger intersection_type;   /**< 当前机动点特殊诱导类型，如：长直行 */
@property (nonatomic, assign) NSInteger limSpeed;   /**< 当前机动点特殊诱导类型，如：长直行 */
@property (nonatomic, assign) NSInteger distance;  /**< 车辆到当前机动点的剩余距离 */
@property (nonatomic, assign) NSInteger type; /**< 标记当前诱导段特征  0 - 无效导航段  1 - 正常导航段  20 - 特殊导航段 */
@property (nonatomic, assign) NSInteger spType; /**< type:1 2 3 4 表示:方向型路牌,出口路牌,入口路牌,高速设施*/
@property (nonatomic,   copy) NSString  *nextRoadName;/**< 当前机动点的路名/方向指示 */
@property (nonatomic,   copy) NSString  *roadName; /**< 当前导航段主路名 */
@property (nonatomic, assign) NSInteger distance_of_ab;
@property (nonatomic, assign) NSInteger totalDistanceLeft;  /**< 全程剩余距离 */
@property (nonatomic, assign) NSInteger actionLength; /**< 机动点路口内长度 */
@property (nonatomic,   copy) NSString *nextRoadNo; /**< 道路编号 */
@property (nonatomic, assign) NSInteger segmentIndexA;
@property (nonatomic, assign) NSInteger segmentIndexB;
@property (nonatomic, assign) NSInteger buildingLength; /**< 当前导航段前段隧道内长度 */
@property (nonatomic, assign) NSInteger connection_len;/**< 当前导航段前段连接路长度，如：匝道，JCT等 */

@end


@interface TRGRouteNextNextEventPointInfo : TRGRouteNextEventPointInfo

@property (nonatomic, assign) NSInteger intersection;/**< 连续机动点类型，参见诱导定义：IntersectionType  */
@property (nonatomic, assign) NSInteger actionLength; /**< 连续转向路口的路口内距离，当has_close_turn==true时有效 */
@property (nonatomic,   copy) NSString  *nextRoadName;/**< 连续机动点路名/方向指示 */
@property (nonatomic, assign) NSInteger segmentIndex;  /**< 吸附下标 是的我没写错 就是吸附下标 */
@property (nonatomic, assign) NSInteger distance;  /**< 连续导航段总长度 */

@end


/**
 * 接近机动点信息
 */
@interface TRGApproachingTurnInfo : TMapBaseApproachingTurnInfo

@property (nonatomic, assign) NSInteger distance_2nd;/**< 若存在连续机动点，车辆到第二个机动点距离 */

@end


// * 基础诱导更新信息 RouteGuidanceUpdateInfo
@interface TRGUpdateItemInfo : TMapBaseUpdateItemInfo

@property (nonatomic, strong) TRGGuidanceAccessoryInfo *nextAcc; /**< 临近的隧道/收费站/服务区*/
@property (nonatomic, assign) NSInteger  limitSpeedKmph; /**< 导航段的通用道路限速*/
@property (nonatomic, assign) NSInteger  spType; /**< type:1 2 3 4 表示:方向型路牌,出口路牌,入口路牌,高速设施*/
@property (nonatomic, assign) NSInteger  type; /**< 标记当前诱导段特征  0 - 无效导航段  1 - 正常导航段  20 - 特殊导航段 */
@property (nonatomic, strong, readonly) TRGHintInfo *hint;/**< 吸附点 主辅路对象 端上自己构造的 */
@property (nonatomic, strong, readonly) TRGRouteMatchInfo *matchRouteInfo;/**< 路线吸附信息，非数据传入,端上自己构造*/
@property (nonatomic, strong, readonly) TRGRouteNextEventPointInfo *nextEventPoint;/**< 下一个转向点*/
@property (nonatomic, strong, readonly) TRGRouteNextNextEventPointInfo *nextNextEventPoint;/**< 下下一个转向点*/

@end


NS_ASSUME_NONNULL_END
