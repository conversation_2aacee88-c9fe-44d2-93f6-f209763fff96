
//
// Created by morgansun(morgansun) on 2020/8/26.
//

#ifndef GUIDANCE_ENGINE_INCLUDE_GUIDANCE_INTERFACE_STRUCTURE_H_
#define GUIDANCE_ENGINE_INCLUDE_GUIDANCE_INTERFACE_STRUCTURE_H_

#include <MapBaseNew/http_interface.h>
#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/common_define.h>
#include <MapBaseNew/route_structure.h>
#include <MapBaseNew/guidance_base_structure.h>
#include "guidance_def.h"

namespace route_guidance {

enum class RG_EXPORT NaviType {
  Navi = 0,     // 默认导航
  Cruise = 1    // 巡航
};

// SetMatchPoint 返回值
enum class RG_EXPORT SMPRet {
  OK = 0,                    // 处理正常，会回调OnSegmentUpdate，其他返回值不回调
  RouteIdNotMatchErr = 1,    // 定位main_route_id和诱导路线集不匹配，可能定位驶入伴随同时更新伴随会导致车标卡住，客户端可重新算路解决车标卡住问题
  RouteResultEmptyErr = 2,   // 定位吸附结果为空
  OtherErr = 3,              // 诱导内部错误，目前不返回，后续扩展使用
  RGIdNotFoundInLocErr = 4,  // 诱导main_route_id在定位路线集找不到，可能是诱导更新伴随后用户手动切伴随，但定位没及时收到伴随
  MainYawOrCompanionYaw = 5, // 这两种吸附状态时SetMatchPoint先不回调OnSegmentUpdate，等客户端确定路线变更后调用诱导SetMainRoute或clearCompanionRoutes再回调
  JniNullErr = 99            // jni null
};

enum class RG_EXPORT BeepType {
  None = 0,
  Beep = 1,
  DengDeng = 2,
  Vibration = 3,
  PassSpeedCamera = 6,
  DingLing= 7,
  DynamicLight = 8
};

enum class RG_EXPORT CompanionRouteSwitchType {
  None = -1,
  OriginRoute = 0,  /**< 切换后是原路线 */
  CompanionRoute = 1 /**< 切换后是伴随路线 */
};

enum class RG_EXPORT ConfuseCrossType {
  None = 0,       /**< 无 */
  FCross = 1,     /**< F路口 */
  Exit = 2,       /**< 高速出口 */
  RoundaboutExit = 3  /**< 环岛出口 */
};

enum class RG_EXPORT HighwayInstructionType {
  None = 0,         //无效
  SeatingArea = 1,  /**< 服务区 */
  TollStation = 2,  /**< 收费站 */
  GasStation = 3    /**< 独立加油站 */
};

/**
 * OnUpdateDistanceToTarget 回调类型
 */
enum class DistanceTargetType {
  NONE = 0,              // 无效
  HD_DATA_BORDER = 1,    // HD数据边界
  HD_4K_DATA_BORDER = 2  // HD和4K数据边界
};

enum class RG_EXPORT GpsWeakStatus {
   None = 0,
   ShortTermGPSWeak = kRouteGuidanceEventActionShortTermGPSWeak, /**< 短期信号弱 */
   ShortTermGPSNormal = kRouteGuidanceEventActionShortTermGPSNormal, /**< 短期信号弱恢复 */
   LongTermGPSWeak = kRouteGuidanceEventActionLongTermGPSWeak, /**< 长期信号弱 */
   LongTermGPSNormal = kRouteGuidanceEventActionLongTermGPSNormal /**< 长期信号弱恢复 */
};

enum class RG_EXPORT CruiseEventType {
  None = 0,
  WarningSign = 7,
  Camera = 8,
  Event = 12,
  TrafficBubble = 16
};

struct RG_EXPORT Push {
  bool need {false};
};
struct RG_EXPORT Toast {
  bool need {false};
};
struct RG_EXPORT Alert {
  bool need {false};
};
enum class RG_EXPORT VibrateLevel {
  NONE = 0,
  LIGHT = 10,
  HEAVY = 20
};
/**
 * 公交到站花式提醒
 */
struct RG_EXPORT BusReminder {
  Push push;
  Toast toast;
  Alert alert;
  VibrateLevel vibrate_level {VibrateLevel::NONE};
  /**
  * 目标所在interval或tran的uid
  */
  std::string segment_uid;

  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const BusReminder& info) {
    if (info.segment_uid.empty()) {
      return out;
    }
    out << ",BusReminder:{seg_uid:" << info.segment_uid
    << ",vibrate:" << static_cast<int>(info.vibrate_level)
    << ",push:" << info.push.need
    << ",toast:" << info.toast.need
    << ",alert:" << info.alert.need
    << "}";
    return out;
  }
};

/**
 * 彩蛋格式类型
 */
enum class RG_EXPORT AudioType {
  NONE = -1,    // 非彩蛋播报，即普通播报
  TXT = 0,      // 文本文案通过 PlayTtsInfo-text 给出，客户端调用 tts 引擎播报
  MP3,          // mp3格式, url通过 audio_url 透出，客户端调用 Media 播报，此时 text 是 ","
  WAV,          // wav格式，其他同mp3
};
struct RG_EXPORT IPController {
  int idx{0};                                       // 索引，便于问题追踪
  int voice_packet_id{0};                           // 语音包id
  int vehicle_log_id{0};                            // 车标/主题id
};
/**
 * 透传阿波罗的运营彩蛋信息
 */
struct RG_EXPORT OperationInfo {
  AudioType audio_type{AudioType::NONE};            // 彩蛋格式类型
  std::string audio_url;                            // 播报音频链接 md5
  std::vector<IPController> ip_list;                // ip id list
  std::string active_time;                          // 生效时间，eg:"07:00-09:00|17:00-19:00"
  int max_per_session{0};                           // 疲劳度配置，单次导航最多播多少次
  int max_per_day{0};                               // 疲劳度配置，每天单用户最多播多少次
  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const OperationInfo& info) {
      out << "{audio_type:" << static_cast<int>(info.audio_type) << "ip_list:[";
      for (auto ip : info.ip_list) {
        out << "(idx:" << ip.idx << ",vp_id:" << ip.voice_packet_id << ",vl_id:" << ip.vehicle_log_id << "),";
      }
      out << "],(" << info.max_per_day << "," << info.max_per_session << "," << info.active_time << ")," << info.audio_url << "}";
      return out;
  }
};

/**
 * 语音播报信息
 */
struct RG_EXPORT PlayTtsInfo {
  std::string text; /**< 语音文本内容 */
  std::string u_key; // 用户级别关联key，用于端非单次导航控频的唯一key，纯透传自云播。注意可能为空串。
  std::string u_freq; // u_key对应的频率控制，字符串形式，｜分隔: 天次数｜周次数｜月次数｜年次数｜总次数
  BeepType beep_type{BeepType::None}; /**< 语音前缀提示音 */
  TTSType tts_type{TTSType::TTSType_NONE}; /**< 语音类型 仅用于驾车导航*/
  TtsSubType tts_sub_type{TtsSubType::TST_NONE}; /**< 语音子类型 仅用于驾车导航，对应云播的infoKind*/
  int idle_time{0};  ///空闲时间（2事件间空闲时间透出，端可使用播报其他东西）
  int easter_egg_type{0};  ///彩蛋场景值
  int easter_egg_way{0};   ///彩蛋替换方式 1：整句替换；2：后面拼接；3：前面拼接
  int source{0};           // 0:云播 非0:动态服务请求req_type
  /**
   * 明星语音编号：当 SetEstrella 1时，准备出发文案透出1，其他情况都是100
   */
  int estrella_num{0};
  /**
   * 优先级:值越小优先级越高，高优先级可打断低优
   */
  int priority{1};
  /**
   * 透传阿波罗的运营彩蛋信息
   */
  OperationInfo oper_info;
  /**
   * 透传公交到站花式提醒,手图使用
   */
  BusReminder bus_reminder;

#ifdef __AUXILIARY_INFORMATION_FOR_CHECK__
  int checkType;
  int intersection_lat;
  int reason_of_end_of_navigation;
  int round;
  int intersection;
  std::string subtype;
  int subtypeDistance;
  int fcrossDistance;
  int fcrossNumbers;
  int enterFCrossDistanceBeforeFirstFCross;
  int enterFCrossBasicDistanceBeforeFirstFCrossMin;
  int enterFCrossBasicDistanceBeforeFirstFCrossMax;
  int broadcastFirstFCrossDistances;
  std::string flag;
  std::string arrow;
  int lanePointX;
  int lanePointY;
  int tunneltype;
  int intersection_lon;
  int total_distance_left;
  int distance_to_event;
#endif

  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const PlayTtsInfo& info) {
    out << " tts:{text:" << info.text
        << ",beep:" << static_cast<int>(info.beep_type)
        << ",tts_type:" << info.tts_type
        << ",sub_type:" << info.tts_sub_type
        << ",idle_time:" << info.idle_time
        << ",egg_type:" << info.easter_egg_type
        << ",egg_way:" << info.easter_egg_way
        << ",priority:" << info.priority
        << ",src:" << info.source
        << ",key:" << info.u_key
        << ",freq" << info.u_freq
        << ",estrella:" << info.estrella_num;
    if (info.oper_info.audio_type != AudioType::NONE) {
      out << ",oper_info:" << info.oper_info;
    }
    if (!info.bus_reminder.segment_uid.empty()) {
      out << info.bus_reminder;
    }
    out << "}";
    return out;
  }
};

/**
 * 偏航信息，用于上报服务器
 * @note 内部状态
 */
struct RG_EXPORT OffCourseInfo {
  int yaw_type{0}; /**< 偏航原因 */
  std::string yaw_message; /**< 偏航说明 */
  std::string main_route_id; // 透传MatchLocationInfo的main_route_id
  mapbase::GeoCoordinate match_pos;  /**< 当前吸附点位置 */
  int match_index; /**< 当前吸附点所在形点索引 */
  int next_intersection_remain_distance; /**< 当前位置到下个机动点的剩余距离 */
  // 自定义格式化函数，防止修改stringstream的默认输出规则影响全局
  std::string formatDouble(double value, int precision) const {
    std::stringstream ss;
    ss << std::fixed << std::setprecision(precision) << value;
    return ss.str();
  }
  friend std::ostream& operator<<(std::ostream& out, const OffCourseInfo& obj) {
    out << " info:{" << obj.main_route_id
        << ",type:" << obj.yaw_type
        << ",msg:" << obj.yaw_message
        << ",pos:" << obj.formatDouble(obj.match_pos.Lng(), 7) << " " << obj.formatDouble(obj.match_pos.Lat(), 7)
        << ",idx:" << obj.match_index
        << ",remain_distance:" << obj.next_intersection_remain_distance
        << "}";
    return out;
  }
};

/**
 * 路口放大图用于显示的相关信息
 */
struct RG_EXPORT ShowEnlargeMapInfo {
  int segment_index{0};            /**< 导航段索引下标 */
  int segment_inner_index{0};      /**< 导航段内索引下标，如导航段内有多个放大图，第2个放大图的段内下标为1 */
  int cross_inner_distance{0};     /**< 路口内距离 */
  int dis_to_map{0};               /**< 到放大图的距离 */
  int intersection{0};             // 转向大图转向点模型类型IntersectionType，直行路口大图为0
  bool is_turn{false};                  // true-转向大图，false-直行路口大图
  std::string next_road_name;    // 转向大图转向点方向名/下个导航段路名，直行路口大图为空
  std::string display_text;     /**< 放大图所在路口显示文案，进入隧道 通过桥梁 通过路口等 */
  std::string pattern;          /**< 样式url */
  mapbase::RoutePos target;    /**< 大图目标点 */
  friend std::ostream& operator<<(std::ostream& out, const ShowEnlargeMapInfo& obj) {
    out << " br:{"
        << "seg_idx:" << obj.segment_index
        << ",seg_inner_idx:" << obj.segment_inner_index
        << ",inner_dis:" << obj.cross_inner_distance
        << ",dis_to_map:" << obj.dis_to_map
        << ",dis_text:" << obj.display_text
        << ",target:" << obj.target
        << ",is_turn:" << obj.is_turn
        << ",inter:" << obj.intersection
        << ",next_road_name:" << obj.next_road_name
        << ",pattern:" << obj.pattern
        << " }";
    return out;
  }
};

/*
 * 动态连续路口放大图用于刷新与第一个路口距离时的相关信息
 * */
struct RG_EXPORT UpdateDynamicEnlargedMapInfo {
    int type{0};                 /**< 刷新的类型 */
    int dis_to_first_inner{0};   /**< 与第一个路口的距离 */
    friend std::ostream& operator<<(std::ostream& out, const UpdateDynamicEnlargedMapInfo& obj) {
      out << " DyMap:{"
          << "type:" << obj.type
          << ",dis:" << obj.dis_to_first_inner
          << "}";
      return out;
    }
};

/**
 * 高速出口信息
 */
struct RG_EXPORT ExitInfo : public mapbase::ExitInfo {
};

/**
 * 拥堵视频摄像头
 */
struct RG_EXPORT VideoCameraInfo : public mapbase::VideoCameraInfo {
};

/**
 * 路况拥堵信息
 */
struct RG_EXPORT TrafficJamInfo : public mapbase::TrafficJamInfo {
};

/**
 * 路线上电子眼信息
 */
struct RG_EXPORT RouteCameraInfo : public mapbase::RouteCameraInfo {
};

/**
 * 放大范围内(高/快道路700,普300)摄像头
 */
struct RG_EXPORT RouteCameraInRange : public mapbase::RouteCameraInRange {
  int sub_type{0}; // 电子眼子类型，仅手图使用
  int speed{0};    // 电子眼限速值，单位km/h；限速电子眼为正值，非限速电子眼为0；仅手图使用
  friend std::ostream& operator<<(std::ostream& out, const RouteCameraInRange& obj) {
      out << " camera:{"
          << "sub_type:" << obj.sub_type
          << ",speed:" << obj.speed << ","
          << static_cast<mapbase::RouteCameraInRange>(obj)
          << "}";
      return out;
  }
};

/**
 * 摄像头距离更新信息
 */
// struct RG_EXPORT RouteCameraUpdateInfo {
//   int id;                                  /**< 摄像头编号 */
//   int distance;                            /**< 该摄像头距离车辆位置 */
// };

/**
 * 路线上全部电子眼信息
 */
struct RG_EXPORT RouteCameraRefreshInfo : public mapbase::RouteCameraRefreshInfo {
};

/**
 * 车道线信息
 */
class RG_EXPORT LaneInfo : public mapbase::LaneInfo {
};

/**
 * 警示牌信息
 */
struct RG_EXPORT WarningSignInfo : public mapbase::WarningSignInfo {
};

/**
 * 隧道展示信息
 */
struct RG_EXPORT TunnelInfo {
  mapbase::RoutePos tunnel_entrance_pos;  /**< 隧道入口位置 */
  int length{0}; /**< 隧道长度 */
  friend std::ostream& operator<<(std::ostream& out, const TunnelInfo& obj) {
      out << " Tunnel:{"
          << "pos:" << obj.tunnel_entrance_pos
          << ",len:" << obj.length
          << "}";
      return out;
  }
};

/**
 * 摄像头超速信息
 */
struct RG_EXPORT OverSpeedInfo {
  OVER_SPEED_TYPE over_speed_type{OVER_SPEED_TYPE::OVER_SPEED_TYPE_NONE};              /**< 超速类型 */
  int speed_kmph{0};                               /**< 当前速度，单位: kmph */
  int limit_speed_kmph{0};                         /**< 当前限速，单位：kmph */
  mapbase::GeoCoordinate coordinate;            /**< 如果有摄像头限速，这里给出摄像头位置 */
  mapbase::GuidanceCameraType camera_type{mapbase::GuidanceCameraType::None};      /**< 摄像头超速类型 */
  CAMERA_OVER_SPEED_KIND over_speed_kind{CAMERA_OVER_SPEED_KIND::CAMERA_OVER_SPEED_KIND_DEFAULT};       /**< 超速状态 */
  friend std::ostream& operator<<(std::ostream& out, const OverSpeedInfo& obj) {
      out << " info:{"
          << "over_type:" << obj.over_speed_type
          << ",over_kind:" << obj.over_speed_kind
          << ",camera_type:" << static_cast<int>(obj.camera_type)
          << ",speed:" << obj.speed_kmph
          << ",limit:" << obj.limit_speed_kmph
          << ",geo:[" << obj.coordinate
          << "]}";
      return out;
  }
  void reset() {
    over_speed_type = OVER_SPEED_TYPE::OVER_SPEED_TYPE_NONE;
    speed_kmph = 0;
    limit_speed_kmph = 0;
    coordinate.SetLng(0.0);
    coordinate.SetLat(0.0);
    camera_type = mapbase::GuidanceCameraType::None;
    over_speed_kind = CAMERA_OVER_SPEED_KIND::CAMERA_OVER_SPEED_KIND_DEFAULT;
  }
};

/**
 * 通过多路线分歧点信息
 */
struct RG_EXPORT PassDivergencePointInfo {
  std::string current_route_id;     /**< 当前路线id */
  std::vector<std::string> companion_route_ids; /**< 伴随路线列表 */
  friend std::ostream& operator<<(std::ostream& out, const PassDivergencePointInfo& obj) {
      out << " info:{"
          << "current_route_id:" << obj.current_route_id << ",companion_route_ids:[";
      for (auto routeId : obj.companion_route_ids) {
        out << routeId << ",";
      }
      out << "]}";
      return out;
  }
};

/**
 * 伴随路线偏航回调信息
 */
struct RG_EXPORT CompanionRouteOffCourseInfo {
  CompanionRouteSwitchType type;              // 0-走主路，伴随偏航 1-驶入伴随，主路偏航
  std::vector<std::string> deleted_route_ids; // 要删除的无用路线，type 0 等端调用clearCompanionRoutes才删，type 1 等端调用setMainRoute才删
  std::string current_route_id;               // 保留的主路线
  std::string yaw_message;                    // 偏航描述，用于埋点和问题分析
  friend std::ostream& operator<<(std::ostream& out, const CompanionRouteOffCourseInfo& obj) {
      out << " info:{"
          << "type:" << static_cast<int>(obj.type)
          << ",cur_id:" << obj.current_route_id
          << ",del_ids:[";
      for (auto routeId : obj.deleted_route_ids) {
        out << routeId << ",";
      }
      out << "]}";
      return out;
  }
};

/**
 * 迷惑路口信息
 * 迷惑路口是和用户转向位置相近，且形态相似的路口
 */
struct RG_EXPORT ConfuseCrossInfo {
  ConfuseCrossType type{ConfuseCrossType::None};        /**< 迷惑路口类型 */
  int index{0};                    /**< 迷惑路口序号 */
  mapbase::RoutePos route_pos;  /**< 迷惑路口路线位置 */
  friend std::ostream& operator<<(std::ostream& out, const ConfuseCrossInfo& obj) {
      out << " cross:{"
          << "type:" << static_cast<int>(obj.type)
          << ",index:" << obj.index
          << ",pos:" << obj.route_pos
          << "}";
      return out;
  }
};

enum class ViaArriveType {
  None = 0,
  ViaArrive = 3,    // 途经点前 65m (若跨导航段上个机动点前10m) 判达，对应播报"前方到达途经点"
  ViaLeave = 4,     // 途经点后 250m 判达，因为存在ViaPassed策略，除非跳点一般不触发，对应播报"前方到达途经点"
  ViaForce = 6,     // 途经点~途经点后第一个机动点+50m范围内发生偏航，对应播报"已为您删除途经点"
  ViaThreeYaw = 7,  // 途经点前连续4个偏航点到途经点的距离（小于5km）越来越大，对应播报"已为您删除途经点"
  ViaPassed = 8,    // 吸附点过了途经点，即吸附点索引大于途经点索引且吸附点距离途经点小于500m，对应播报"您已通过途经点"
};
/**
 * 途径点到达信息
 */
struct RG_EXPORT ViaArrivalInfo {
  int idx{-1};                              // 途经点在服务下发途经点列表中的索引，注意做越界异常处理，可能返回异常值-1
  int segment_index{-1};                    // 导航段下标
  ViaArriveType type{ViaArriveType::None};  // 途经点判达类型
  mapbase::RoutePos route_pos;              // 途径点路线位置
  std::string via_point_id;                 // 途经点id，用于途经点判达互斥
  std::string route_id;                     // 主路线id
  friend std::ostream& operator<<(std::ostream& out, const ViaArrivalInfo& obj) {
      out << " Via:{idx:" << obj.idx
          << ",point_id:" << obj.via_point_id
          << ",seg_idx:" << obj.segment_index
          << ",pos:" << obj.route_pos
          << ",type:" << static_cast<int>(obj.type)
          << "," << obj.route_id
          << "}";
      return out;
  }
};

/**
 * 接近机动点信息
 */
struct RG_EXPORT ApproachingTurnInfo : public mapbase::ApproachingTurnInfo {
  int distance_2nd{-1}; /**< 若存在连续机动点，车辆到第二个机动点距离 */
  friend std::ostream& operator<<(std::ostream& out, const ApproachingTurnInfo& obj) {
    out << " info:{"
        << static_cast<mapbase::ApproachingTurnInfo>(obj)
        << ",dis_2nd_cross:" << obj.distance_2nd
        << "}";
    return out;
  }
};

struct RG_EXPORT SAPoiInfo {
  int type{0};              //类型
  std::string poiDesc;      //@deprecated 描述-已废弃，改用poi_info
  std::string poi_info;     //描述:目前充电桩poiid用该字段
  std::string brand_name;	//品牌名
  std::string brand_id;		//品牌id
  std::string detail; 		//品牌详细信息；若type为6加油站eg: #92|#95
  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const SAPoiInfo& info) {
    out << " poi:{type:" << info.type
        << ",poi_info:" << info.poi_info
        << ",brand_name:" << info.brand_name
        << ",brand_id:" << info.brand_id
        << ",detail:" << info.detail
        << "}";
    return out;
  }
};

/**
 * 高速设施信息
 * <br> vec_aisle[i]指左i+1收费通道数据,取值范围[1,254];
 * <br> 每个元素从低位到高位(Bit0~Bit5)依次标识是/否(1/0)支持该支付方式：
 * <br> Bit0:现金
 * <br> Bit1:ETC
 * <br> Bit2:微信
 * <br> Bit3:支付宝
 * <br> Bit4:其他支付
 * <br> Bit5:预留
 * <br> 样例:000010代表该通道仅支持ETC支付
 */
struct RG_EXPORT HighwayInstructionInfo {
  HighwayInstructionType type{HighwayInstructionType::None};  /**< 高速设施类型 */
  int distance{0};                 /**< 自车点到设施距离 */
  /**
   * 高速设施所在导航段道路等级
   * <br>OnHighwayInstructionUpdate已做过滤只透出高速/国道上的设施，
   * <br>GetHighwayInstructions透出所有道路等级的设施，上层可依据自行过滤
   */
  mapbase::OnlineRoadFuncClass road_class{mapbase::OnlineRoadFuncClass::Null};
  std::string name;             /**< 高速设施中文名称 */
  mapbase::RoutePos route_pos;  /**< 高速设施所在路线位置 */
  std::vector<int> vec_aisle;   /**< 收费站通道数据 */
  std::string rawID;            /**< 服务区uid */
  std::string saDesc;           /**< 服务区描述 */
  std::vector<SAPoiInfo> saPoiVec; /**< 服务区poi */

  friend bool operator<(const HighwayInstructionInfo& a, const HighwayInstructionInfo& b) {return a.distance < b.distance;}
  friend bool operator>(const HighwayInstructionInfo& a, const HighwayInstructionInfo& b) {return a.distance > b.distance;}
  friend std::ostream& operator<<(std::ostream& out, const HighwayInstructionInfo& info) {
    out << " ins:{type:" << static_cast<int>(info.type)
        << ",name:" << info.name << ",road_class:" << static_cast<int>(info.road_class)
        << ",dis:" << info.distance
        << ",pos:" << info.route_pos << ",";
    if (HighwayInstructionType::TollStation == info.type) {
      out << "vec_aisle:[";
      for (auto aisle : info.vec_aisle) {
        out << aisle << ",";
      }
    } else if (HighwayInstructionType::SeatingArea == info.type){
      out << "rawID:" << info.rawID
          << ",saDesc:" << info.saDesc
          << ",saPoiVec:[";
      for (const auto& poi : info.saPoiVec) {
        out << poi;
      }
    }
    out << "]}";
    return out;
  }
};

/**
 * 巡航鱼骨展示信息
 */
class CruiseFishBoneDisplayInfo : public mapbase::CruiseFishBoneDisplayInfo {
};

/**
 * 巡航鱼骨隐藏索引信息
 */
struct RG_EXPORT CruiseFishBoneHideInfo : public mapbase::CruiseFishBoneHideInfo {
};

/**
 * 交通点事件
 */
struct RG_EXPORT TrafficEventInfo : public mapbase::TrafficEventInfo {
};

struct RG_EXPORT GuidanceAccessoryInfo {
  /**
   * 自车点到该数据剩余距离
   */
  int distance_to{0};
  /**
   * 辅助数据类型
   */
  RouteGuidanceAccessoryType type{kRouteGuidanceAccessoryTypeNone};
  /**
   * 路线坐标
   */
  mapbase::RoutePos route_pos;

  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const GuidanceAccessoryInfo& info) {
    out << " acc:{dis:" << info.distance_to
        << ",type:" << static_cast<int>(info.type)
        << ",pos:" << info.route_pos
        << "}";
    return out;
  }
};

/**
 * 基础诱导更新信息
 */
struct RG_EXPORT RouteGuidanceUpdateInfo : public mapbase::RouteGuidanceUpdateInfo {
  /**
   * 临近的隧道/收费站/服务区/
   */
  GuidanceAccessoryInfo next_acc;
  /**
   * 导航段的通用道路限速
   */
  int limit_speed_kmph{0};
  /**
   * 下一机动点（前方转向）处方向路牌(SignPost)的类型，用于标记 next_intersection_road_name 是方向名（SignPost name）还是路名 (下个导航段路名)
   * type:1 2 分别表示各种SignPost类型对应 方向名:方向型路牌,出口路牌； 0 表示无SignPost对应 路名
   */
  int sp_type{0};
  /**
   * 标记当前诱导段特征
   * 0 - 无效导航段
   * 1 - 正常导航段
   * 20 - 特殊导航段
   */
  int type{1};

  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const RouteGuidanceUpdateInfo& info) {
    out << " RGInfo:{" << static_cast<mapbase::RouteGuidanceUpdateInfo>(info)
        << ",type:" << info.type
        << ",sp_type:" << info.sp_type
        << ",limit:" << info.limit_speed_kmph
        << ",next_acc:" << info.next_acc
        << "},";
    return out;
  }
};

/**
 * 诱导状态更新信息
 */
struct RG_EXPORT GuidanceUpdateInfo : public mapbase::GuidanceUpdateInfo {
    RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const GuidanceUpdateInfo& info) {
    out << " UpdateInfo:{status:" << static_cast<int>(info.match_status) << ",ts:" << info.origin_pos.timestamp
        << ",list:[";
    for (auto item : info.guidance_info) {
      if (item.get() != nullptr) {
        auto derived = std::static_pointer_cast<RouteGuidanceUpdateInfo>(item);
        out << *derived;
      }
    // NOCA:UNINIT.STACK.MIGHT（忽视静扫警告：没问题）
    }
    out << "]}";
    return out;
  }
};

/**
 * 停车场定义
 */
struct RG_EXPORT Parking {
  /**
   * 停车场poi中心点
   */
  mapbase::GeoCoordinate pos;
  /**
   * 是否收费
   */
  bool free{false};
  /**
   * 类型 0 地下停车场 1 地上停车场
   */
  int type{0};
  /**
   * 停车场名称
   */
  std::string name;
  friend std::ostream& operator<<(std::ostream& out, const Parking& obj) {
    out << " Parking:{"
        << "type:" << obj.type
        << ",free:" << obj.free
        << ",pos[:" << obj.pos
        << "]}";
    return out;
  }
};

/**
 * 路线eta对
 */
struct RG_EXPORT RouteEtaPair {
  /**
   * 路线id
   */
  std::string route_id;
  /**
   * 剩余时间，单位：秒
   */
  int eta_seconds{0};
  friend std::ostream& operator<<(std::ostream& out, const RouteEtaPair& obj) {
    out << " Eta:{"
        << "route_id:" << obj.route_id
        << "," << obj.eta_seconds << "s}";
    return out;
  }
};

////////////////////////////////// 接口参数定义 ////////////////////////////////
/**
 * 引擎功能配置开关
 */
struct RG_EXPORT FunctionSwitchParam {
  /**
   * 高速、国道上允许展示加油站提示牌(默认：手图true,车机false)
   */
  bool allow_gas_station_board {true};
  /**
   * 将额外的区间测速信息抛给调用者（默认：手图false,车机true）
   */
  bool more_interval_speed_camera_info {false};
  /**
   * 记录关键点日志，并抛给调用者，用以记录日志到文件中（默认：false）
   */
  bool enable_log_to_file {false};
  friend std::ostream& operator<<(std::ostream& out, const FunctionSwitchParam& obj) {
    out << " Param:{"
        << "allow_gas:" << obj.allow_gas_station_board
        << ",more_interval:" << obj.more_interval_speed_camera_info
        << ",enable_log:" << obj.enable_log_to_file
        << "}";
    return out;
  }
};

struct RG_EXPORT SetRouteParam {
  /**
   * 算路场景，目前只有俩值：1-行前算路，10000-行中算路
   */
  mapbase::RouteForWhat route_for_what {mapbase::RouteForWhat::Navigation};
  /**
   * 恢复老的算路route_for_what，建议在离线都设置，目前诱导仅离线时使用该字段区分不同算路原因匹配不同"开始导航类播报"
   */
  mapbase::RouteForWhat reason {mapbase::RouteForWhat::Navigation};
  /**
   * 指定主路线id，默认第0条
   */
  std::string selected_routeid;
  /**
   * 终点名称
   */
  std::string poi_name;
  /**
   * 限行信息
   */
  std::string forbid;
  /**
   * 停车场名称
   */
  std::string park_name;
  /**
   * 主路线剩余时间，用于定制开始导航播报，默认值:-1，单位：分钟
   * 当为0时，不播报开始导航中的时间和距离信息
   * 当0时，时间会直接从主路线数据中获取
   */
  int remain_time_minute{-1};
  /**
   * 主路线剩余距离，用于定制开始导航播报，单位：米
   * 当<15时，不播报开始导航中的时间和距离信息
   * 当为0时，距离会直接从主路线数据中获取
   */
  int remain_distance{-1};
  /**
   * 禁用路线id列表，被禁用的路线将不被加载到诱导引擎中
   */
  std::vector<std::string> forbidden_routeid_list;
  /**
   * 使用哪个引擎
   * 0-全用
   * 1-只用云播
   * 2-只用老SDK
   *
   * 默认：0
   */
  int use_which_engine {0};
  friend std::ostream& operator<<(std::ostream& out, const SetRouteParam& obj) {
    out << " Param:{"
        << "rfw:" << static_cast<int>(obj.route_for_what) << ",reason:" << static_cast<int>(obj.reason)
        << ",uwe:" << obj.use_which_engine
        << ",selected_routeid:" << obj.selected_routeid
        << ",remain_time:" << obj.remain_time_minute
        << ",remain_dis:" << obj.remain_distance
        << ",poi_name:" << obj.poi_name
        << ",forbid:" << obj.forbid
        << ",park_name:" << obj.park_name
        << ",fr_list:[";
    for (auto routeId : obj.forbidden_routeid_list) {
      out << routeId << ",";
    }
    out << "]}";
    return out;
  }
};

/**
 * 城市天气
 */
struct RG_EXPORT CityWeather {
  /**
   * 城市名称
   */
  std::string city_name;
  /**
   * 天气类型
   */
  int weather_state{0};
  /**
   * 最高温度，单位：摄氏度
   */
  int max_temperature{0};
  /**
   * 最低温度，单位：摄氏度
   */
  int min_temperature{0};
  /**
   * 当前温度, 单位：摄氏度
   */
  int real_temperature{0};
  friend std::ostream& operator<<(std::ostream& out, const CityWeather& obj) {
    out << " Weather:{"
        << "city:" << obj.city_name
        << ",weather_state:" << obj.weather_state
        << ",temperature:" << obj.real_temperature
        << ",max_temp:" << obj.max_temperature
        << ",min_temp" << obj.min_temperature
        << "}";
    return out;
  }
};

struct RG_EXPORT RemainRedLightInfo {
  /**
   * 0-正常吸附 -1-当前未吸附，之前吸附上过 -404-从来没吸附上
   */
  int state{0};
  /**
   * 路线id
   */
  std::string route_id;
  /**
   * 剩余红绿灯个数
   */
  int remain_red_light_count {0};
  /**
   * 到下一个红绿灯距离
   */
  int distance_to_next_red_light {0};
  /**
   * 下一个红绿灯位置
   */
  mapbase::RoutePos pos;
  friend std::ostream& operator<<(std::ostream& out, const RemainRedLightInfo& obj) {
    out << " RemainLight:{"
        << "route_id:" << obj.route_id
        << ",state:" << obj.state
        << ",remain_cnt:" << obj.remain_red_light_count
        << ",dis_to_next:" << obj.distance_to_next_red_light
        << ",pos:" << obj.pos
        << "}";
    return out;
  }
};

struct RG_EXPORT RecommendRouteInfo {
  /**
   * 当前路线id
   */
  std::string current_routeid;
  /**
   * 即将要替换成的路线id
   */
  std::string recommend_routeid;
  /**
   * 透传路线服务下发的推荐原因文案 broadcast_reason，可用于播报；目前手图未使用，盘古上层可用作文案播报
   */
  std::string broadcast_reason;
  /**
   * 透传路线服务下发的推荐原因文案 recommend_reason，可用于显示
   */
  std::string recommend_reason;
  /**
   * 透传路线服务下发的推荐类型 recommend_type; 0-更快， 1-封路， 2-限行
   */
   int recommend_type{0};
   friend std::ostream& operator<<(std::ostream& out, const RecommendRouteInfo& obj) {
      out << " info:{"
          << "current_routeid:" << obj.current_routeid
          << ",recommend_routeid:" << obj.recommend_routeid
          << ",broadcast_reason:" << obj.broadcast_reason
          << ",recommend_reason:" << obj.recommend_reason
          << ",recommend_type" << obj.recommend_type
          << "}";
      return out;
   }
};

/**
 * GPS信号弱信息
 */
struct RG_EXPORT GpsStatusInfo {
  /**
   * gps信号状态
   */
  GpsWeakStatus status{GpsWeakStatus::None};
  /**
   * 信号弱提示文案
   */
   std::string tips;
   friend std::ostream& operator<<(std::ostream& out, const GpsStatusInfo& obj) {
      out << " StatusInfo:{"
          << "status:" << static_cast<int>(obj.status)
          << ",tips:" << obj.tips
          << "}";
      return out;
    }
};

enum class RG_EXPORT OutputInfoType {
    RG_NONE = -1,
    RG_ERROR = 0,
};

/**
 * 诱导透出给端上的统计信息
 */
struct RG_EXPORT RGOutputInfo {
    /**
     * 输出信息类型，  参考 OutputInfoType 定义
     */
    OutputInfoType type{OutputInfoType::RG_NONE};
    /**
     * 输出信息（公交用于透传手图埋点key）
     */
    std::string content;
    friend std::ostream& operator<<(std::ostream& out, const RGOutputInfo& info) {
      out << " info:{content:" << info.content
          << ",type:" << static_cast<int>(info.type)
          << "}";
      return out;
    }
};

/**
 * 空闲区间信息
 */
struct RG_EXPORT IdleSectionInfo {
 /**
  * 空闲区间长度
  */
  int distance{0};
  /**
   * 空闲区间等级
   */
  RGIdleSectionTypeEnum type{RGIdleSectionTypeEnum::IDLE_SECTION_NONE};
  friend std::ostream& operator<<(std::ostream& out, const IdleSectionInfo& info) {
      out << " info:{dis:" << info.distance
          << ",type:" << static_cast<int>(info.type)
          << "}";
      return out;
  }
};

/**
 * 刷新放大图信息信息
 */
struct RG_EXPORT UpdateCommonEnlargedMapInfo {
 /**
  * 到放大图的距离
  */
  int dis_to_map{-1};
  friend std::ostream& operator<<(std::ostream& out, const UpdateCommonEnlargedMapInfo& info) {
      out << ",dis:" << info.dis_to_map;
      return out;
  }
};

/**
 * 放大图信息
 */
struct RG_EXPORT BrInfo {
 /**
  * 样式
  */
  std::string pattern;
 /**
  * 箭头
  */
  std::string arrow;
  /**
   * 类型
   */
   int type{0};
   friend std::ostream& operator<<(std::ostream& out, const BrInfo& obj) {
      out << " Br:{"
          << "pattern:" << obj.pattern
          << ",arrow:" << obj.arrow
          << ",type:" << obj.type
          << "}";
      return out;
   }
};

/**
 * 透出的后续放大图信息
 */
struct RG_EXPORT NextBrInfos {
 /**
  * 后续的放大图信息
  */
  std::vector<BrInfo> next_br_infos;
  friend std::ostream& operator<<(std::ostream& out, const NextBrInfos& obj) {
    out << " preload_brs:[";
    for (auto br : obj.next_br_infos) {
      out << br;
    }
    out << "]";
    return out;
  }
};

//分片请求信息
struct RG_EXPORT GuidanceSliceInfo {
  int index{0};//分片的索引
  int count{0};//分片数量，目前只会是1
  std::string route_id;//路线id
  friend std::ostream& operator<<(std::ostream& out, const GuidanceSliceInfo& obj) {
      out << " SliceInfo:{"
          << "idx:" << obj.index
          << ",cnt:" << obj.count
          << ",route_id:" << obj.route_id
          << "}";
      return out;
   }
};

// 请求返回的GuidanceSliceData的jce结构体转换
struct RG_EXPORT UgsSliceInfo {
  int slice_id;
  std::vector<char> *guidance_data;
  friend std::ostream& operator<<(std::ostream& out, const UgsSliceInfo& obj) {
    out << " UgsSliceInfo:{"
        << "slice_id:" << obj.slice_id;
    if (obj.guidance_data != nullptr) {
      out << ",guidance_data size:" << obj.guidance_data->size();
    }
    out << "}";
    return out;
  }
};
/**
 * 道路特征信息，map-biz 会参考该信息调整比例尺
 */
struct RG_EXPORT RoadFeatureInfo : public mapbase::RoadFeatureInfo {
};

enum class SpEnhanceInfoType {
  NONE = 0,   // 无效
  GATE = 1,   // 小区门，被START_AREA_GATE替代（服务实现，端无需care）
  EXIT = 2,   // 长距离直行高速出口
  PARKING = 4,// 起点在地库，高置信度
  PARKING_MIDDLE = 5, // 起点在地库，中置信度
  START_AREA_GATE = 42,    // 起点区域面，行驶到xx门（大门）
  START_AREA_EXIT = 43,    // 起点区域面，行驶到（xx）出口
  START_NO_AREA = 44,      // 起点附近无区域面，行驶到xx路（起点）
  IN_ROUTE_AREA = 45,      // 行中行驶回规划路线
  START_DRIVER_OFF_CAR = 47,    // 起点人车分离
  IN_ROUTE_DRIVER_OFF_CAR = 48, // 行中人车分离
  DRIVE_REVERSE = 49,           // 行驶方向与规划路线方向相反
};

/**
 * 隧道内模型信息
 */
struct RG_EXPORT TunnelInter {
    INTERSECTION_TYPE intersection_type {INTERSECTION_NONE};      // 隧道内模型
    int inter_pos_remain_dis {};                                  // 模型与出口距离
};
/**
 * 隧道进度条控制信息
 */
struct RG_EXPORT TunnelProgressInfo {
    bool show {false};            // 是否显示进度条，true 显示 false 隐藏
    int tunnel_len {0};           // 隧道长度
    std::string tunnel_name;      // 隧道名称
    int remain_dis {0};           // 当前自车点与出口距离
    std::vector<TunnelInter> inters {};    // 隧道内模型值
    friend std::ostream& operator<<(std::ostream& out, const TunnelProgressInfo& obj) {
        out << ",info:{show:" << obj.show << ",len:" << obj.tunnel_len << ",name:" << obj.tunnel_name
            << ",remain_dis:" << obj.remain_dis << ",inters:";
        for (const auto& inter : obj.inters) {
            out << "[" << inter.intersection_type << "," << inter.inter_pos_remain_dis << "],";
        }
        out  << "}";
        return out;
    }
};

/**
 * 方向看板增强信息
 */
class RG_EXPORT SpEnhanceInfo {
 public:
  SpEnhanceInfo() : type(SpEnhanceInfoType::NONE), intersection_enhance(false), display_text(""){}
  friend std::ostream& operator<<(std::ostream& out, const SpEnhanceInfo& obj) {
    out << " SpEnhance:{"
        << "type:" << static_cast<int>(obj.type)
        << ",enhance:" << obj.intersection_enhance
        << ",dis_text:" << obj.display_text
        << "}";
    return out;
  }
  SpEnhanceInfoType type;       // 增强信息类型：几种type云播保证区间不重叠（回调对不交叉），优先级高于OnSegmentUpdate
  bool intersection_enhance;    // 云播控制方向看板模型箭头是否使用增强样式 true:增强样式 false:使用OnSegmentUpdate参数next_intersection_type匹配样式
  std::string display_text;     // 显示文案，为空时不显示文案
};

/**
 * 区间测速段信息
 */
class RG_EXPORT SpeedLimitZoneInfo {
 public:
  SpeedLimitZoneInfo() : speed_limit_kmph(0), length(0){}
  friend std::ostream& operator<<(std::ostream& out, const SpeedLimitZoneInfo& obj) {
    out << " info:{"
        << "len:" << obj.length
        << ",limit:" << obj.speed_limit_kmph
        << ",begin:" << obj.begin
        << ",end:" << obj.end
        << "}";
    return out;
  }
  mapbase::RoutePos begin;  /**< 区间测速起点 */
  mapbase::RoutePos end;    /**< 区间测速终点 */
  int speed_limit_kmph;     /**< 区间测速限速值，单位：kmph */
  int length;               /**< 区间测速长度 */
};

/**
 * 在区间测速内状态更新信息
 */
class RG_EXPORT SpeedLimitZoneUpdateInfo {
 public:
  SpeedLimitZoneUpdateInfo() : average_speed_kmph(0), remain_length(-1),
    speed_limit(0), average_speed_status(CAMERA_OVER_SPEED_KIND::CAMERA_OVER_SPEED_KIND_DEFAULT){}
  RG_EXPORT friend std::ostream& operator<<(std::ostream& out, const SpeedLimitZoneUpdateInfo& obj) {
    out << "info:{"
        << "aver_speed:" << obj.average_speed_kmph
        << ",remain_len:" << obj.remain_length
        << ",limit:" << obj.speed_limit
        << ",status:" << obj.average_speed_status
        << ",pos:" << obj.coordinate
        << "}";
    return out;
  }
  int average_speed_kmph;       /**< 当前平均车速，单位：kmph */
  int remain_length;            /**< 区间测速区间剩余距离，单位: m */
  int speed_limit;              /**< 区间测速限速值，单位: kmph */
  CAMERA_OVER_SPEED_KIND average_speed_status; /**< 当前超速状态 */
  mapbase::GeoCoordinate coordinate;  /**< 区间测速摄像头终点位置 */
};

/**
 * 定位平行路信息
 */
struct RG_EXPORT LocHintInfo {
  int loc_hint_type{0}; // 建议切路类型，来自定位的match_location_info.extra_info.pos_interactive_type，范围 1-7，定义参考RoadHintType
  std::string tts_text; // 对应播报文案：一次导航只播一次，赋值非空串；后面再回调时赋值空串
  friend std::ostream& operator<<(std::ostream& out, const LocHintInfo& info) {
      out << "info:{type:" << info.loc_hint_type
          << ",tts:" << info.tts_text
          << "}";
      return out;
    }
};

struct RG_EXPORT DialogInfo {
  int type{0};        // Dialog 类型：1-步行提醒，2-骑行提醒，3-步行强切，4-骑行强切，eg：if(type == 1) {步行} else if (type == 2) {骑行}
  std::string text;   // Dialog 文案
  friend std::ostream& operator<<(std::ostream& out, const DialogInfo& info) {
    out << "info:{type:" << info.type
        << ",txt:" << info.text
        << "}";
    return out;
  }
};

struct RG_EXPORT LocDisplayItem {
  std::string id;         // 定位给的文案id，唯一id，来自定位的MatchLocationInfo-PosGuardianInfo-ids
  std::string text;       // id匹配的显示文案
  friend std::ostream& operator<<(std::ostream& out, const LocDisplayItem& item) {
    out << "(" << item.id << "," << item.text << ")";
    return out;
  }
};
/**
 * 通过定位MatchLocationInfo-PosGuardianInfo-ids匹配的id-text映射表
 */
struct RG_EXPORT LocDisplayInfo {
  std::vector<LocDisplayItem> loc_dis_list;   // 通过定位MatchLocationInfo-PosGuardianInfo-ids匹配的id-text映射表，size和顺序同ids
};


enum class RG_EXPORT SmartLocConfType {
    SMART_LOC_NONE = 0,     // 非智能定位
    SMART_LOC_HIGH = 1,     // 智能定位+高置信
    SMART_LOC_MID = 2,      // 智能定位+中置信
    SMART_LOC_LOW = 3,      // 智能定位+低置信
};


/**
 * 诱导网络配置
 */
class RG_EXPORT NetConfig {
  public:
  /**
   * HttpInterface 实例，用于在线请求
   */
  std::shared_ptr<mapbase::HttpInterface> http;
  /**
   * 诱导动态服务测试环境url
   * 备注: 当外部需要设置测试环境时，将测试环境url(eg:https://unimaptest1.map.qq.com)赋值给它即可；若使用正式环境，赋值空串，诱导内部会填正式url。
   */
  std::string dynamic_cloud_url{};
  /**
   * 客户渠道号
   * 注意: 客户端必须注入,若不清楚渠道联系项目;
   */
//  std::string channel{};
};

}

#endif //GUIDANCE_ENGINE_INCLUDE_GUIDANCE_LISTENER_STRUCTURE_H_
