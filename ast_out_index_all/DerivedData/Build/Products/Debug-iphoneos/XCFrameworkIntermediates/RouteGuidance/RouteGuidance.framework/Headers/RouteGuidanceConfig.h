//
//  RouteGuidanceConfig.h
//  RouteGuidance
//
//  Created by morgansun on 2021/12/29.
//

#ifndef RouteGuidanceConfig_h
#define RouteGuidanceConfig_h

#import <Foundation/Foundation.h>
#import <RouteGuidance/RGCycleAPI.h>
#import <RouteGuidance/RGDriveProvider.h>
#import <RouteGuidance/RGDriveListenerAdapter.h>
#import <RouteGuidance/RGModels.h>
#import <RouteGuidance/RGWalkAPI.h>
#import <RouteGuidance/RGListenerAdapter.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * mark where should logs writes
 */
//typedef NS_OPTIONS(NSUInteger, TRGLoggingTarget) {
//    LoggingTargetNone           = 0,
//    LoggingTargetFile           = 1 << 0,
//    LoggingTargetSysDebugLog    = 1 << 1,
//    LoggingTargetStderr         = 1 << 2,
//
//    LoggingTargetAll = LoggingTargetFile | LoggingTargetSysDebugLog | LoggingTargetStderr,
//};

@interface TRouteGuidance : NSObject

//@property(nonatomic, readonly, getter=logPath) NSString *logPath;
//@property(nonatomic, readonly, getter=logTarget) TRGLoggingTarget logTarget;

+ (unsigned long long)getVersionCode;
+ (NSString *)getVersionName;
//+ (void)initLogger:(NSString *)logPath: (int)level: (bool)console;

/**
 * get logging destination path.
 * if called before initLogger, would got empty result
 */
//+ (NSString *)logPath;

/**
 * get log target options.
 * if called before initLogger, might got garbage
 */
//+ (TRGLoggingTarget)logTarget;

@end

NS_ASSUME_NONNULL_END


#endif /* RouteGuidanceConfig_h */
