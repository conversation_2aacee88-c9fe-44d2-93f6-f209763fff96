//
// Created by morgan<PERSON>(morgansun) on 2020/7/22.
// 获取诱导引擎版本号，驾车、步、骑、公交通用
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCE_VERSION_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCE_VERSION_H_
#include "macro.h"
#ifdef __cplusplus
extern "C" {
#endif
/// \brief 获取引擎版本号
/// \return uint64_t 64位整型版本号
RG_EXPORT unsigned long long QRouteGuidanceGetVersionCode(void);

/// \brief 获取引擎版本号
/// \return const char* 引擎版本号字符串
RG_EXPORT const char *QRouteGuidanceGetVersionName(void);

/**
 * 获取诱导云播版本号（仅驾车云播版本号），算路请求和路况更新请求都需使用该版本号
 * @apiNote 为保持灵活性，算路请求云播版本推荐优先使用云控，拉取云控失败时可以使用本接口
 * @return 云播版本号
 */
RG_EXPORT int QRouteGuidanceGetCloudVer(void);

#ifdef __cplusplus
};
#endif
#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCE_VERSION_H_
