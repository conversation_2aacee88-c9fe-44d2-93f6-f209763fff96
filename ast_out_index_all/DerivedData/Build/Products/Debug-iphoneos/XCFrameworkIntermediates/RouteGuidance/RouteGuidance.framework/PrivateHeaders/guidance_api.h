//
// Created by morgan<PERSON>(morgansun) on 2020/4/10.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCEAPI_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCEAPI_H_

#include <vector>
#include <memory>

#include "macro.h"
#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/route_plan_visitor.h>
#include "guidance_event_listener.h"

namespace route_guidance {

class RG_EXPORT GetterApi {
 public:
  /**
   * 获取指定位置主路线剩余距离
   * @param pos 指定位置，支持吸附点和带索引的形状点
   * @return 主路线剩余距离剩余距离，单位：米
   */
  virtual int GetRemainDistance(const mapbase::RoutePos& pos) = 0;
  /**
   * 获取当前吸附点到路线起点的距离
   * @return 到起点的距离，单位：米
   */
  virtual int GetDistanceToBegin() = 0;
  /**
    * 获取主路线上两个点的路线距离
    * @param begin 起点位置, 需要填充经纬度、coor_start数据，支持吸附点和带索引的形状点
    * @param end 终点位置, 需要填充经纬度、coor_start数据，支持吸附点和带索引的形状点
    * @return 到指定点的距离，单位：米
    */
  virtual int GetDistanceToPoint(const mapbase::RoutePos& begin, const mapbase::RoutePos& end) = 0;
  /**
   * 获取范围内的路况信息
   * @param range 范围
   * @param traffic_events 输入路况信息
   * @return 范围内的路况信息
   */
  virtual std::vector<mapbase::RouteEvent> GetTrafficInRange(int range, const std::vector<mapbase::RouteEvent>& traffic_events) = 0;
  /**
   * 获取路线上剩余红绿灯信息
   * return 剩余红绿灯信息
   */
  virtual std::vector<RemainRedLightInfo> GetRemainLightCount() = 0;
  /**
   * 获取主路线上服务区、收费站、加油站信息
   * <br> example：查询主路线上自车到终点剩余所有服务区、收费站、加油站 GetHighwayInstructionsInfo(
   * <br>ACCESSORY_SEARCH_TYPE_SEATING_AREA|ACCESSORY_SEARCH_TYPE_TOLL_STATION|ACCESSORY_SEARCH_TYPE_GAS_STATION, ACCESSORY_RESULT_TYPE_DEFAULT)
   * @param type 查询设施类型：
   * <br>服务区: ACCESSORY_SEARCH_TYPE_SEATING_AREA = 2
   * <br>收费站: ACCESSORY_SEARCH_TYPE_TOLL_STATION = 4
   * <br>独立加油站：ACCESSORY_SEARCH_TYPE_GAS_STATION  = 8
   * <br>服务区+收费站+独立加油站: ACCESSORY_SEARCH_TYPE_SEATING_AREA|ACCESSORY_SEARCH_TYPE_TOLL_STATION|ACCESSORY_SEARCH_TYPE_GAS_STATION = 14
   * @param searchType 查询选项:
   * <br>剩余全程，按从近到远排序: ACCESSORY_RESULT_TYPE_DEFAULT = 0
   * <br>下一个设施信息: ACCESSORY_RESULT_TYPE_NEXT_ONE = 1
   * <br>最后一个设施信息: ACCESSORY_RESULT_TYPE_LAST_ONE = 2
   * @return 查询结果，已按自车点距离由近及远排序
   */
  virtual std::vector<HighwayInstructionInfo> GetHighwayInstructionsInfo(int type, AccessoryResultType searchType) = 0;
};

class RG_EXPORT GuidanceApi {
 public:
  virtual ~GuidanceApi() {}
  /**
   * 设置测试模式，默认非测试模式
   * 仅供诱导UT和AT使用，外部禁止调用
   * @param flag 是否测试模式
   */
  virtual void SetTestMode(bool flag) = 0;
  /**
   * 设置诱导引擎开关
   * @param in_param 诱导功能开关
   */
  virtual void SetSwitches(const FunctionSwitchParam& in_param) = 0;
  /**
   * 设置吸附引擎句柄，用于向吸附引擎同步路线变更
   * @param pos_service 吸附引擎
   */
  virtual void SetMatchService(void *pos_service) = 0;
  /**
   * 诱导事件监听
   * @param listener 诱导监听回调
   */
  virtual void SetListener(GuidanceEventListener* listener) = 0;
  /**
   * 设置统计监听，统计埋点信息
   * @param statistics_listener 诱导统计回调
   */
  virtual void SetStatistics(GuidanceStatisticsListener* statistics_listener) = 0;
  /**
   * 设置行为监听
   * @param listener 诱导行为回调
   */
  virtual void SetBehaviorListener(GuidanceBehaviorEventListener* listener) = 0;
  /**
   * 设置车道级HD监听器；不调用该接口或传参为null时诱导车道级功能失效
   * @param listener  诱导车道级HD监听器
   */
  virtual void SetHDListener(GuidanceHDEventListener* listener) = 0;
  /**
   * @brief 设置诱导配置，在set上面几个listener后(setRoute前)调用
   * @param config 诱导网络配置
   */
  virtual void SetNetProvider(const NetConfig &config) = 0;
  /**
   * 获取云播版本
   * @deprecated 已废弃改用 QRouteGuidanceGetCloudVer
   * @return 云播版本 获取云播版本
   */
  virtual int GetCloudVer() const = 0;
  /**
   * 设置最新的匹配位置, 频率1Hz
   * @param match_location_info 吸附位置
   * @param voice_mode 语音播报模式: 0-智能播报(默认), 2-强制播报，其他值(包括值为1的强制不播报)无效
   * @return @see SMPRet
   */
  virtual int SetMatchPoint(const mapbase::MatchLocationInfo& match_location_info, int voice_mode) = 0;

  /**
   * 设置高频定位信息，频率10HZ，来自定位的 OnHighFreqLocInfoUpdate 回调
   * @param hd_pos 高频定位信息
   */
  virtual void SetHighFreqLocInfo(const mapbase::HDPosPoint& hd_pos) = 0;

  /**
   * 设置规划的路线方案
   * @param route_plan 解析后的路线
   * @param param 初始化参数
   * @return 是否设置成功
   */
  virtual bool SetRoute(std::weak_ptr<mapbase::RoutePlanVisitor> route_plan, SetRouteParam &param) = 0;

  /**
   * 设置路线更新结果，包含：路况、eta、伴随路线、主路线解释性
   * @param guidance_route_update 路线更新结果
   * @return 0-全部更新成功   非0-更新失败(十进制个位、十位、百位分别表示UpdateTraffic、UpdateMainRouteExplain、UpdateCompanionRoute失败具体原因)
   */
  virtual int UpdateGuidance(const mapbase::RouteUpdateVisitor *guidance_route_update) = 0;
  /**
   * 更新异步下发的云播数据
   * @param route_ids 云播数据对应的routeid列表
   * @param cloud_data_list 云播数据
   * @brief route_ids和cloud_data_list长度一致，按下标对应，该接口会接管
   * @return 是否更新成功
   */
  virtual bool UpdateGuidanceCloudData(std::vector<std::string> &&route_ids,
                                       std::vector<std::vector<char>>&& cloud_data_list) = 0;
  /**
   * 手切伴随或驶入伴随
   * @param mainRouteId 目标主路线id
   * @param isSilent 是否静音切换：驶入伴随时强制false，手切伴随时若是静默替换true其他false
   * @param type 0-手切伴随，1-驶入伴随
   */
  virtual void SetMainRoute(const std::string& route_id, bool is_silent, int type) = 0;
  /**
   * 清除导航引擎数据
   */
  virtual void Clear() = 0;
  /**
   * 设置城市天气
   * @param param 天气设置参数
   * @return 是否设置成功
   */
  virtual bool SetWeather(const std::vector<CityWeather>& weather_list) = 0;
  /**
   * 设置推荐停车场数据
   * @param parking_list 推荐停车场数据
   * @return 是否设置成功
   */
  virtual bool SetRecommendParking(const std::vector<Parking>& parking_list) = 0;
  /**
   * 清除推荐停车场
   */
  virtual void ClearRecommendParking() = 0;
  /**
   * 设置语音播报冲突原因
   * @param reason 冲突原因
   */
  virtual void SetConflictReason(int reason) = 0;
  /**
   * 设置零流量模式
   * @param is_zero_network true - 开启
   */
  virtual void SetZeroNetwork(bool is_zero_network) = 0;
  /**
   * 设置导航模式(首次导航、日夜间模式)
   * @param is_night 是否夜间导航
   * @param is_first_time 是否首次导航
   */
  virtual void SetMode(bool is_night, bool is_first_time) = 0;
  /**
   * 设置明星语音
   * @param in_param 明星语音
   */
  virtual void SetEstrella(int estrella_num) = 0;
  /**
   * 设置路线收费信息
   * @param in_param 路线收费信息
   */
  virtual void SetTollFee(const std::string &route_id, float fee) = 0;
  /**
   * 清空伴随路线
   * @param type 0-智能定位场景 1-onCompanionRouteOffCourse过分歧点走主路场景
   */
  virtual void ClearCompanionRoutes(int type) = 0;
  /**
   * 设置路线的剩余时间
   * @param route_eta_list 剩余时间参数
   */
  virtual void SetRoutesEta(const std::vector<RouteEtaPair>& route_eta_list) = 0;
  /**
   * 设置当前路况是否更新成功，如果路况刷新后调用
   * 如果发现长时间未能成功更新时间路况信息，则引擎会停用时间、路况相关事件
   * @param is_success 是否成功状态 true-成功 false-失败
   */
  virtual void SetTrafficUpdateResult(bool is_success) = 0;
  /**
   * 手图接口：设置是否是路线探测。初始化诱导实例后首次SetRoute前调用，或行中切换时调用
   * @param nav_mode 导航模式
   */
  virtual void SetNaviMode(NAV_MODE nav_mode) = 0;
  /**
   * 车机接口：设置导航类型，初始化诱导实例后，首次SetRoute前调用
   * @param navi_type 导航or巡航
   */
  virtual void SetNaviType(NaviType navi_type) = 0;
  /**
   * 设置播报模式，SetNaviMode 后调用
   * @param tts_mode 播报模式
   * @return 传入的tts_mode是否为有效值，本期仅0、1、4返回true，其他返回false
   * <br>0 标准模式（默认）
   * <br>1 简洁播报模式
   * <br>2 只有ding,用于路线探测（本期无效）
   * <br>3 熟路模式（本期无效）
   * <br>4 静音模式
   */
  virtual bool SetTTSMode(int tts_mode) = 0;
  /**
   * 设置播报语音包 id
   * @param packet 语音包 id
   * @param when 设置时机
   * <br>0 客户端行前设置语音包 id
   * <br>1 客户端行中设置语音包 id
   */
   virtual void SetTTSPacketId(int packet, int when) = 0;

  /**
   * 设置语言类型
   * @param nLanguageType 语言类型:
   * <br>LANGUAGE_CN = 1, // 中文简体
   * <br>LANGUAGE_TC = 2, // 中文繁体
   * <br>LANGUAGE_EN = 3, // 英文
   * <br>LANGUAGE_PT = 4, // 葡萄牙文
   */
  virtual void SetLanguageType(int nLanguageType) = 0;
  /**
   * 设置地区，请在SetRoute前调用
   * @param region 地区 参见mapbase::RegionType
   */
  virtual void SetRegion(int region) = 0;

  /**
   * 导航状态获取接口
   * @return 导航功能API
   */
  virtual GetterApi* Get() = 0;

  /**
   * @param infoList 车道吸附信息列表，字符串格式：timestamp,laneNum,laneCount 用 "," 分割
   */
  virtual void SetCorrectLaneNum(std::vector<std::string>& infoList) = 0;

   /**
    * 设置TP的变道点
    * @deprecated
    */
  virtual void SetTPChangePoint(mapbase::TPChangePoint& change_point) = 0;
  /**
   * 设置分片数据
   */
  virtual void SetGuidanceSliceData(const std::string& route_id, const std::vector<UgsSliceInfo>& slice_data) = 0;

  /**
    * 设置总包分片数据,对应jce结构体ugs_guidance_slice_res_t的二进制流
    */
  virtual bool SetUgsGuidanceSlice(const char *data, int len) = 0;

  /**
   * 更新红绿灯倒计时数据（rsp_type 区分变灯倒计时/入队）<br>
   * 客户端请求频率和给诱导update频率：<br>
   * 1、变灯：onRedLightCountDown LightStatus in-out范围内，请求每5s（建议apollo云控）一次，给诱导update数据每1s一次（remain_time来自客户端的1s的定时器,rsp_type=LightStatus,其他数据来自服务返回）；<br>
   * 2、入队：onRedLightCountDown LightQueue in-out范围内，请求每5s（建议apollo云控）一次，给诱导update数据每5s一次（和请求时机一致，若请求成功直接组装rsp数据[rsp_type=LightQueue,其他属性来自服务返回]同步诱导；请求失败也需要给诱导同步无效数据[rsp_type=LightQueue,其他属性填默认值0]）；<br>
   * @param rsp 变灯倒计时/入队请求返回的数据
   */
  virtual void UpdateTrafficLightData(const mapbase::TrafficLightRsp& rsp) = 0;
  /**
   * 设置服务区充电站信息，调用时机：OnRequestSaChargingStationInfo 请求 或 定时请求（有就调，没有就不用调） 成功后
   * @param info 充电站信息
   */
  virtual void SetChargingStationInfo(const mapbase::ChargingStationInfo& info) = 0;
  /**
   * 设置绿波数据，OnReqGreenWave中调用，无论是否请求成功都需要调用
   * @param rsp 绿波响应数据
   */
  virtual void SetGreenWaveRsp(const mapbase::GreenWaveRsp& rsp) = 0;
  /**
   * @deprecated 已废弃
   * 诱导收到onHttpResponse成功后将rsp参数回传给端，端在该回调调用setHttpResponse将下述参数全部纯透传给诱导，方便自动从网络线程切回诱导线程
   */
  virtual void SetHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data, int32_t size) = 0;
  /**
   * 同步长链接数据，同mapbiz接口
   * @param data jce 二进制流，对应RealTimeCommWrap.jce的CommStreamData结构
   */
  virtual void UpdateRealTimeCommData(std::unique_ptr<std::vector<uint8_t>> data) = 0;
};
/**
 * 创建一个新的诱导句柄
 * @return
 */
RG_EXPORT GuidanceApi* CreateGuidance();

/**
 * 销毁驾车诱导引擎
 * @param api 句柄
 */
RG_EXPORT void DestroyGuidance(GuidanceApi* api);

/**
 * 获取版本信息
 * @return 版本信息
 */
RG_EXPORT std::string GetVersionInfo();

}

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_CARENGINE_API_GUIDANCEAPI_H_
