//
// Created by da<PERSON><PERSON><PERSON> on 1/25/21.
// Todo：临时供手图透传给底图的俩结构序列化，手图接map-biz后下掉
//

#ifndef ROUTEGUIDANCE_ROUTEGUIDANCE_CARENGINE_JCE_UTIL_TEMP_H_
#define ROUTEGUIDANCE_ROUTEGUIDANCE_CARENGINE_JCE_UTIL_TEMP_H_

#include "guidance_interface_structure.h"

namespace route_guidance {
// from 先转老 jce 结构再序列化，在 OnDynamicEnlargedMapUpdate 中调用
RG_EXPORT bool JceSerialize(const UpdateDynamicEnlargedMapInfo& from, std::vector<char>& to);
// from 先转老 jce 结构再序列化，在 OnResetRouteCameraList 中调用
RG_EXPORT bool JceSerialize(const RouteCameraRefreshInfo& from, std::vector<char>& to);
// from 先转老 jce 结构再序列化，在 OnApproachingCamera 中调用
RG_EXPORT bool JceSerialize(const std::vector<int>& from, std::vector<char>& to);
}
#endif //ROUTEGUIDANCE_ROUTEGUIDANCE_CARENGINE_JCE_UTIL_TEMP_H_