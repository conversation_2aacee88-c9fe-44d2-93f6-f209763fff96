#ifndef _RG_OC_INTERFACE_STRUCTURE_H_
#define _RG_OC_INTERFACE_STRUCTURE_H_

#import <MapBaseNew/MapBase.h>

@interface TRGTrafficEventInfo : TMapBaseTrafficEventInfo
@end

@interface TRGCruiseFishBoneDisplayInfo : TMapBaseCruiseFishBoneDisplayInfo
@end

@interface TRGCruiseFishBoneHideInfo : TMapBaseCruiseFishBoneHideInfo
@end


@interface TRGRouteCameraRefreshInfo : TMapBaseRouteCameraRefreshInfo
@end


@interface TRGRouteCameraInfo : TMapBaseRouteCameraInfo
@end

@interface TRGTrafficJamInfo : TMapBaseTrafficJamInfo
@end

@interface TRGLaneInfo : TMapBaseLaneInfo
@end

@interface TRGRoadFeatureInfo : TMapBaseRoadFeatureInfo
@end

@interface TRGGuidanceUpdateInfo : TMapBaseUpdateInfo
@end


#endif
