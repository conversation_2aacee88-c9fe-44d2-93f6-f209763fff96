#ifndef _QWALKGUIDANCEAPI_H
#define _QWALKGUIDANCEAPI_H
#include "guidance_data_def.h"

ROUTE_GUIDANCE_FUNCTION_BEGIN

typedef RouteGuidanceMapPoint WalkGuidanceMapPoint;
typedef RouteGuidanceGPSPoint WalkGuidanceGPSPoint;

RG_EXPORT void *QWalkGuidanceCreate();
RG_EXPORT void QWalkGuidanceDestroy(void *rgHandle);
RG_EXPORT int QWalkGuidanceSetMapPoints(void *rgHandle, WalkGuidanceMapPoint *mapPoints, int mapPointsCount);
RG_EXPORT void QWalkGuidanceSetFencePoints(void *rgHandle, WalkGuidanceMapPoint *fencePoints, int fencePointsCount);
RG_EXPORT void QWalkGuidanceSetGPSPoint(void *rgHandle, WalkGuidanceGPSPoint *gpsPoint, WalkGuidanceGPSPoint *matchedPoint, bool *outway);
RG_EXPORT void QWalkGuidanceSetGPSTolerantRadius(void *rgHandle, int radius);
RG_EXPORT void QWalkGuidanceSetUsingHeading(void *rgHandle, bool usingHeading);
RG_EXPORT int QWalkGuidanceDistanceToPoint(void *rgHandle, const WalkGuidanceGPSPoint *gpsPoint, int segmentIndex, WalkGuidanceMapPoint mapPoint);
RG_EXPORT void QWalkGuidanceClear(void *rgHandle);
RG_EXPORT int QWalkGuidanceDistanceOfA2BInRoute(void *rgHandle, WalkGuidanceMapPoint p1, int index1, WalkGuidanceMapPoint p2, int index2);//A在路线上比B靠近起点为负，否则为正

ROUTE_GUIDANCE_FUNCTION_END

#endif
