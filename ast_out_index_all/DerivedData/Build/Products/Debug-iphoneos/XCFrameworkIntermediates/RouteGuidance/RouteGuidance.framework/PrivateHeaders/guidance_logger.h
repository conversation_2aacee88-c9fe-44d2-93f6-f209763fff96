//
// Created by da<PERSON><PERSON><PERSON> on 2020/11/10.
//

#ifndef ROUTEGUIDANCE_INCLUDE_GUIDANCE_LOGGER_H_
#define ROUTEGUIDANCE_INCLUDE_GUIDANCE_LOGGER_H_

#include "macro.h"
#include "plog/logger.h"

namespace route_guidance {

/**
 * 初始化日志，建议在初始化诱导实例前调用
 * @param path 日志目录路径
 * @param level 日志等级{@link plog::LogLevel}
 * @param dest 日志输出方式：控制台或文件 {@link plog::LogDest}
 */
RG_EXPORT void InitLogger(const std::string &path, plog::LogLevel level, plog::LogDest dest);

/**
 * 获取 Plog的LoggerConfig对象指针，用于设置日志级别，日志输出方式
 * @return
 */
RG_EXPORT std::shared_ptr<plog::LoggerConfig> GetLoggerConfig();

}

#endif //ROUTEGUIDANCE_INCLUDE_GUIDANCE_LOGGER_H_
