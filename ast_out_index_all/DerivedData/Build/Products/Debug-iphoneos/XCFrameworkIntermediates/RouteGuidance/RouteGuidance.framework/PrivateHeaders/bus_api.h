//
// Created by da<PERSON><PERSON><PERSON> on 2020/10/31.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_BUSENGINE_API_BUSAPI_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_BUSENGINE_API_BUSAPI_H_

#include <vector>
#include <memory>

#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/route_plan_visitor.h>
#include "bus_event_listener.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

class RG_EXPORT BusApi {
 public:
  virtual ~BusApi() {}
  /**
   * 设置吸附引擎句柄，用于向吸附引擎同步路线变更
   * @param pos_service 吸附引擎
   */
  virtual void SetMatchService(void *pos_service) = 0;
  /**
   * 诱导事件监听
   * @param listener 诱导监听回调
   */
  virtual void SetListener(BusEventListener* listener) = 0;

  /**
   * 设置最新的匹配位置, 频率1Hz
   * @param match_location_info 吸附位置
   */
  virtual void SetMatchPoint(const mapbase::MatchLocationInfo& match_location_info) = 0;
  /**
   * 设置规划的路线方案
   * @param route_plan 路线方案数据
   * @param param 初始化参数
   * @return true-成功 false-失败
  */
  virtual bool SetRoute(std::shared_ptr<mapbase::BaseRoutePlanVisitor> route_plan_visitor, BusEngineSetRouteParam& param) = 0;

  /**
   * 清除引擎路线数据
   */
  virtual void Clear() = 0;
};

namespace bus {
/**
 * 创建一个公交句柄
 * @return
 */
RG_EXPORT BusApi *CreateGuidance();

/**
 * 销毁公交引擎
 * @param api
 */
RG_EXPORT void DestroyGuidance(BusApi* api);

}

ROUTE_GUIDANCE_NAMESPACE_END

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKAPI_H_
