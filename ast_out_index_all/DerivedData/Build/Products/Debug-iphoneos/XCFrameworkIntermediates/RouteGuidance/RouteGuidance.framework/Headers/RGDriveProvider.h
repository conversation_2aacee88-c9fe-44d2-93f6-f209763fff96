/**
 * Copyright © 2021 Tencent. All rights reserved.
 */
#import "RGModels.h"
#import <MapBaseNew/MapBaseRouteStructure.h>

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TRGDriveProvider : NSObject

@property (nonatomic,weak) id delegate;

/**
 * 设置回调
 */
- (void)setListener;

/**
 * 设置明星语音
 * @param in_param 明星语音
 */
- (void)setEstrella:(int)estrella_num;


/**
 * 销毁
 */
- (void)destory;

/**
 * 清除推荐停车场
 */
- (void)clearRecommendParking;

/**
 * 设置路线更新结果，包含：路况、eta、伴随路线
 * @param guidance_route_update 路线更新信息
 * @return 是否更新成功
 */
- (int)updateGuidance:(NSData *)data  reason:(int)route_for_what;

/**
  * 获取主路线上两个点的路线距离
  * @param begin 起点位置, 需要填充经纬度、coor_start数据，支持吸附点和带索引的形状点
  * @param end 终点位置, 需要填充经纬度、coor_start数据，支持吸附点和带索引的形状点
  * @return 到指定点的距离，单位：米
  */
- (int)distanceToPoint:(TMapBaseRoutePos*)beginPos endPos:(TMapBaseRoutePos *)endPos;

/**
 * 设置路线收费信息
 * @param in_param 路线收费信息
 */

- (void)setEtcAccountTollFee:(float)account routeId:(NSString *)routeId;

/**
 * 设置当前路况是否更新成功，如果路况刷新后调用
 * 如果发现长时间未能成功更新时间路况信息，则引擎会停用时间、路况相关事件
 * @param is_success 是否成功状态 true-成功 false-失败
 */
- (void)setTrafficUpdateStatus:(BOOL)status;

/**
 * 设置主路线
 * @param route_id 主路线id
 * @param is_silent 是否静音切换
 */
- (void)setMainRoute:(NSString*)route_id silentMode:(BOOL)is_silent switchType:(int)type;

/**
 * 设置零流量模式
 * @param is_zero_network true - 开启
 */
- (void)setZeroNetwork:(BOOL)is_zero_network;

/**
 * 设置播报模式，SetNaviMode 后调用
 * @param tts_mode 播报模式
 * @return 传入的tts_mode是否为有效值，本期仅0、1、4返回true，其他返回false
 * <br>0 标准模式（默认）
 * <br>1 简洁播报模式
 * <br>2 只有ding,用于路线探测（本期无效）
 * <br>3 老司机模式（本期无效）
 * <br>4 静音模式
 */
- (void)setTTSMode:(int)tts_mode;

/**
 * 设置是否是路线探测，SetRoute 后，SetMatchPoint 前调用
 * @param nav_mode 导航模式
 */
- (void)setNaviMode:(NSInteger)naviMode;

/**
 * 设置路线的剩余时间
 * @param route_eta_list 剩余时间参数
 */
- (void)updateTrafficETA:(NSArray<TRGUpdateItemInfo *> *)list;

/**
 * 设置导航模式(首次导航、日夜间模式)
 * @param is_night 是否夜间导航
 * @param is_first_time 是否首次导航
 */
- (void)setIsNightMode:(BOOL)isNightMode isFirst:(BOOL)bFirst;

/**
 * 设置语音播报冲突原因
 * @param reason 冲突原因
 */
- (void)setConflictReason:(int)reason;

/**
 * 设置城市天气
 * @param param 天气设置参数
 * @return 是否设置成功
 */
- (void)writeWeatherInfo:(NSDictionary*)dic;

/**
 * 获取主路线上服务区、收费站的信息
 * <br> example：GetHighwayInstructionsInfo(ACCESSORY_SEARCH_TYPE_SEATING_AREA|ACCESSORY_SEARCH_TYPE_TOLL_STATION, ACCESSORY_RESULT_TYPE_DEFAULT)
 * @param type 查询设施类型：
 * <br>服务区: ACCESSORY_SEARCH_TYPE_SEATING_AREA
 * <br>收费站: ACCESSORY_SEARCH_TYPE_TOLL_STATION
 * <br>服务区+收费站: ACCESSORY_SEARCH_TYPE_SEATING_AREA|ACCESSORY_SEARCH_TYPE_TOLL_STATION
 * @param searchType 查询选项:
 * <br>剩余全程，按从近到远排序: ACCESSORY_RESULT_TYPE_DEFAULT
 * <br>下一个设施信息: ACCESSORY_RESULT_TYPE_NEXT_ONE
 * <br>最后一个设施信息: ACCESSORY_RESULT_TYPE_LAST_ONE
 * @return 查询结果，已按离自车点距离排序
 */
- (NSArray<TRGHighwayInstructionInfo*>*)getAccessoryInfo:(NSInteger)searchType  responseType:(NSInteger)ressponseType;

/**
 * 获取指定位置主路线剩余距离
 * @param pos 指定位置，支持吸附点和带索引的形状点
 * @return 主路线剩余距离剩余距离，单位：米
 */
- (int)remainDistance:(TMapBaseRoutePos*)pos;

/**
 * 获取路线上剩余红绿灯信息
 * return 剩余红绿灯信息
 */
- (NSArray<TRGRemainRedLightItemInfo*>*)remainLightInfo;

/**
 * 设置最新的匹配位置, 频率1Hz
 * @param match_location_info 吸附位置
 * @param voice_mode 语音播报模式: 0-智能播报(默认), 2-强制播报，其他值(包括值为1的强制不播报)无效
 */
- (void)setMatchPoint:(TMapBaseMatchLocationInfo *)matchPointInfo voiceSetting:(NSInteger)voice;

/**
 * 设置吸附引擎句柄，用于向吸附引擎同步路线变更
 * @param pos_service 吸附引擎
 */
- (void)setMatchService:(void *)fusion;

/**
 * 获取范围内的路况信息
 * @param range 范围
 * @param traffic_events 输入路况信息
 * @return 范围内的路况信息
 */
- (NSArray<TMapBaseRouteEvent*> *)trafficInRange:(int)range range:(NSArray<TMapBaseRouteEvent*> *)traffic_events;

/**
 * 清空伴随路线
 */
- (void)clearCompanionRoutes:(int)type;

@end

NS_ASSUME_NONNULL_END
