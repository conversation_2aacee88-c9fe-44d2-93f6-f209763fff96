/**
 * Copyright © 2021 Tencent. All rights reserved.
 */
#import "RGModels.h"
#import <MapBaseNew/MapBase.h>

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol TRGWalkAPIDelegate <NSObject>

/**
 * @brief 播报语音
 * @param tts 语音内容
 * @return 是否播报成功
 */
- (BOOL)onTTSPlay:(TRGPlayTtsInfo *)tts;

/**
 * AR步导，设施通过状态
 * @param statusInfo 设施通过状态
 */
- (void)onTipsArrive:(TRGWalkTipsArriveStatusInfo *)statusInfo;

/**
 * @brief 偏航
 * @note 步骑偏航时不需要回流偏航信息，诱导只透传吸附偏航状态
 */
- (void)onOffCourse;

/**
 * @brief 到达目的地
 */
- (void)onArrivalDestination;

/**
 * @brief  诱导状态更新
 * @note 透出了SetMatchedPoint Out数据
 * @param basicGuidanceList
 */
- (void)onSegmentUpdate:(TRGGreenTravelUpdateInfo *)basicGuidanceList;


/**
 * @brief 吸附失败
 * @param matchFailedInfo 吸附失败信息
 */
- (void)onMatchFailed:(TRGMatchFailedInfo *)matchFailedInfo;

/**
 * @brief 吸附失败恢复
 */
- (void)onHideMatchFailed;

/**
 * 速度异常
 */
- (void)onVelocityAnomaly;

@end


/// 步骑大定位walk_api的OC封装
@interface TRGWalkAPI : NSObject

@property (nonatomic, weak, nullable) id<TRGWalkAPIDelegate> delegate;

/// 调用单例前，务必先调用此方法创建引擎API
- (void)createEngine;

/**
 * 设置MatchService
 * @param fusion_engine::FusionLocationEngineGuide* pos_service实例
 */
- (void)setMatchService:(void *)posService;

/**
 * 设置最新的匹配位置, 频率1Hz
 * @param matchLocationInfo 吸附位置
 */
- (void)setMatchPoint:(TMapBaseMatchLocationInfo*)matchLocationInfo;

/**
 * 设置规划的路线方案
 *
 * 解析步骑行路线，对应的jce流为TmapWalkRouteRsp(is_online = true) 或 WalkRouteRsp(is_online = false)
 * @param data 二进制流
 * @param length 流长度
 * @param isOnline 是否有Tmap外壳
 * @param vehicleMode 车辆类型
 * @param param SetRoute参数结构
*/
- (void)setRoute:(NSData *)data2
          length:(int)length
        isOnline:(BOOL)isOnline
            mode:(TMapBaseVehicleMode)vehicleMode
           param:(TRGGreenTravelSetRouteParam *)param;
/**
 * 清除导航引擎数据
 */
- (void)clear;

/**
 * 设置语音播报模式：
 * <p>
 *     0  智能播报(默认)
 *     1  无语音
 *     2  强制透出所有语音
 * </p>
 * @param voice_mode 语音播报模式
 */
- (void)setVoiceMode:(int)voiceMode;

/**
 * @brief 设置步行禁止列表
 * @details actions由一系列宏定义确定，按位与的方式组合出来，
 * 目前有:
 * FORBIDDENOUTWAY              0X0001
 * FORBIDDENARRIVINGDESTINATION 0x0002
 * @param actions 禁止开关列表，从中取值[0,1,2,3]；举例：3 = 0x0003,到达终点和偏航均禁止
 */
- (void)setForbiddenActions:(int)actions;

/**
 * 最新吸附位置到路线起点距离（耗时操作）
 * @return 距离，单位：米
 */
- (int)distanceToBegin;

/**
 * 修改里程碑播报文案
 * @param path 配置文件路径
 * @return YES-成功 no-失败
 */
- (BOOL)changeWalkMileStoneTextFromFile:(NSString *)path;

/// 销毁步导引擎
- (void)destroy;

@end

NS_ASSUME_NONNULL_END
