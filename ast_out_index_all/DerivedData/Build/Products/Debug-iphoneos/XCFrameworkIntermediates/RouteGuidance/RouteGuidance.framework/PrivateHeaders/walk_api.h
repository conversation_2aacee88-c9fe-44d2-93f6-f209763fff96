//
// Created by davishua<PERSON> on 2020/10/31.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKAPI_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKAPI_H_

#include <vector>
#include <memory>

#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/route_plan_visitor.h>
#include <MapBaseNew/common_location.h>
#include "walk_event_listener.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

class RG_EXPORT WalkApi {
 public:
  virtual ~WalkApi() {}
  /**
   * 设置吸附引擎句柄，用于向吸附引擎同步路线变更
   * @param pos_service 吸附引擎
   */
  virtual void SetMatchService(void *pos_service) = 0;
  /**
   * 诱导事件监听
   * @param listener 诱导监听回调
   */
  virtual void SetListener(WalkEventListener* listener) = 0;

  /**
   * 设置最新的匹配位置, 频率1Hz
   * @param match_location_info 吸附位置
   */
  virtual void SetMatchPoint(const mapbase::MatchLocationInfo& match_location_info) = 0;
  /**
   * 设置规划的路线方案
   * @param route_plan 路线方案数据
   * @param param 初始化参数
   * @return true-成功 false-失败
  */
  virtual bool SetRoute(std::shared_ptr<mapbase::BaseRoutePlanVisitor> route_plan_visitor, GreenTravelSetRouteParam& param) = 0;

  /**
   * 清除导航引擎数据
   */
  virtual void Clear() = 0;

  /**
   * 设置语音播报模式：
   * <br>0  智能播报(默认)
   * <br>1  无语音
   * <br>2  强制播报
   * @param voice_mode 语音播报模式
   */
  virtual void SetVoiceMode(int voice_mode) = 0;

  /**
   * @brief 设置步行禁止列表
   * @details actions由一系列宏定义确定，按位与的方式组合出来，
   * 目前有:
   * FORBIDDENOUTWAY              0X0001
   * FORBIDDENARRIVINGDESTINATION 0x0002
   * @param actions 禁止开关列表，从中取值[0,1,2,3]；举例：3 = 0x0003,到达终点和偏航均禁止
   */
  virtual void SetForbiddenActions(int actions) = 0;

  /**
   * 最新吸附位置到路线起点距离（耗时操作）
   * @return 距离，单位：米
   */
  virtual int DistanceToBegin() = 0;

  /**
   * 路线上两个点之间的距离
   * @param begin 起点
   * @param end   终点
   * @return 距离，单位：米
   */
  virtual int DistanceOnRoute(const mapbase::RoutePos& begin, const mapbase::RoutePos& end) = 0;

  /**
   * 修改里程碑播报文案
   * @param path 配置文件路径
   * @return true-成功 false-失败
   */
  virtual bool ChangeWalkMileStoneTextFromFile(const std::string& path) = 0;
    /**
     * 更新红绿灯倒计时数据（rsp_type 区分变灯倒计时/入队）<br>
     * 客户端请求频率和给诱导update频率：<br>
     * 1、变灯：onRedLightCountDown LightStatus in-out范围内，请求每5s（建议apollo云控）一次，给诱导update数据每1s一次（remain_time来自客户端的1s的定时器,rsp_type=LightStatus,其他数据来自服务返回）；<br><br>
     * @param rsp 变灯倒计时请求返回的数据
     */
    virtual void UpdateTrafficLightData(const mapbase::TrafficLightRsp& rsp) = 0;
};

namespace walk {
/**
 * 创建一个步导句柄
 * @return
 */
RG_EXPORT WalkApi *CreateGuidance();

/**
 * 销毁步行引擎
 * @param api
 */
RG_EXPORT void DestroyGuidance(WalkApi* api);

}

ROUTE_GUIDANCE_NAMESPACE_END

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKAPI_H_
