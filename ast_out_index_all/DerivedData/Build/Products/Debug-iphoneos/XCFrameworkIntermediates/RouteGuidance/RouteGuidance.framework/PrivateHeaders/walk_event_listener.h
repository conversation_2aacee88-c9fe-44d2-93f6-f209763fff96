//
// Created by davishua<PERSON> on 2020/10/31.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKEVENTLISTENER_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKEVENTLISTENER_H_


#include "green_travel_interface_structure.h"
#include "green_travel_event_listener.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

class WalkEventListener : public GreenTravelEventListener {
 public:
  /**
   * AR步导，设施通过状态
   * @param status_info 设施通过状态
   */
  virtual void OnTipsArrive(const WalkTipsArriveStatusInfo &status_info){};
};

ROUTE_GUIDANCE_NAMESPACE_END

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_WALKENGINE_API_WALKEVENTLISTENER_H_