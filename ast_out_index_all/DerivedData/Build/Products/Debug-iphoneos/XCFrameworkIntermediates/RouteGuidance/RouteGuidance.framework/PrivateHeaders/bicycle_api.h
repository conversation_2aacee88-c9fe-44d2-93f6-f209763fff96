//
// Created by da<PERSON>hua<PERSON> on 2020/10/31.
//

#ifndef GUIDANCE_ENGINE_ROUTEGUIDANCE_BICYCLEENGINE_API_BICYCLEAPI_H_
#define GUIDANCE_ENGINE_ROUTEGUIDANCE_BICYCLEENGINE_API_BICYCLEAPI_H_

#include <vector>
#include <memory>

#include <MapBaseNew/pos_result_define.h>
#include <MapBaseNew/route_structure.h>
#include <MapBaseNew/route_plan_visitor.h>
#include "bicycle_event_listener.h"

ROUTE_GUIDANCE_NAMESPACE_BEGIN

/**
 * 诱导骑行对外接口
 */
class RG_EXPORT BicycleApi {
public:
   virtual ~BicycleApi() {}

  /**
   * 设置吸附引擎句柄，用于向吸附引擎同步路线变更
   * @param pos_service 吸附引擎
   */
  virtual void SetMatchService(void *pos_service) = 0;

  /**
   * 设置诱导事件监听器
   * @param listener 诱导监听回调
   */
  virtual void SetListener(BicycleEventListener* listener) = 0;

  /**
   * 设置最新的匹配位置, 频率1Hz
   * @param match_location_info 吸附位置
   */
  virtual void SetMatchPoint(const mapbase::MatchLocationInfo& match_location_info) = 0;

  /**
  * 设置规划的路线方案
  * @param base_route_plan 路线方案数据
  * @param param 初始化参数
  */
  virtual bool SetRoute(std::shared_ptr<mapbase::BaseRoutePlanVisitor> route_plan_visitor, GreenTravelSetRouteParam& param) = 0;

  /**
   * 清除导航引擎数据
   */
  virtual void Clear() = 0;

  /**
   * 设置语音播报模式：
   * <br>0  智能播报(默认)
   * <br>1  无语音
   * <br>2  强制播报
   * @param voice_mode 语音播报模式
   */
  virtual void SetVoiceMode(int voice_mode) = 0;

  /**
   * 最新吸附位置到路线起点距离（耗时操作）
   * @return 距离，单位：米
   */
  virtual int DistanceToBegin() = 0;

  /**
   * 路线上两个点之间的距离
   * @param begin 起点
   * @param end   终点
   * @return 距离，单位：米
   */
  virtual int DistanceOnRoute(const mapbase::RoutePos& begin, const mapbase::RoutePos& end) = 0;

  /**
    * 更新红绿灯倒计时数据（rsp_type 区分变灯倒计时/入队）<br>
    * 客户端请求频率和给诱导update频率：<br>
    * 1、变灯：onRedLightCountDown LightStatus in-out范围内，请求每5s（建议apollo云控）一次，给诱导update数据每1s一次（remain_time来自客户端的1s的定时器,rsp_type=LightStatus,其他数据来自服务返回）；<br><br>
    * @param rsp 变灯倒计时请求返回的数据
    */
  virtual void UpdateTrafficLightData(const mapbase::TrafficLightRsp& rsp) = 0;
};

namespace bicycle {

/**
 * 创建一个骑行诱导句柄
 * @param true - 自行车，false - 电动车
 * @return 骑行诱导句柄
 */
RG_EXPORT BicycleApi *CreateGuidance(bool is_bicycle);

/**
 * 销毁骑行诱导引擎
 * @param api
 */
RG_EXPORT void DestroyGuidance(BicycleApi *api);
}

ROUTE_GUIDANCE_NAMESPACE_END

#endif //GUIDANCE_ENGINE_ROUTEGUIDANCE_BICYCLEENGINE_API_BICYCLEAPI_H_