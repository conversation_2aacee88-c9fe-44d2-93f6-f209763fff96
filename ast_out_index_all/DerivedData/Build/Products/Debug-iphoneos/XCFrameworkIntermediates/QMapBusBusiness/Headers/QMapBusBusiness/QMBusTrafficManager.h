//
//  QMBusTrafficManager.h
//  QMapBusiness
//
//  Created by q<PERSON><PERSON><PERSON>(卢祺宙) on 2021/5/26.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class QMBusPlanTrafficModel, BusRoute;
@protocol QMBusTrafficDelegate;

@interface QMBusTrafficManager : NSObject

@property (nonatomic, assign) BOOL polling; // 是否开始轮询
@property (nonatomic, assign) NSInteger index; // 当前算路方案索引
@property (nonatomic, copy) NSArray <BusRoute *> *routes; // 所有的算路方案
@property (nonatomic, weak) id<QMBusTrafficDelegate> delegate;

@property (nonatomic, readonly, strong) BusRoute *route; // 当前算路方案
@property (nonatomic, readonly, strong) QMBusPlanTrafficModel *trafficModel; // 当前算路方案路况信息


@end

@protocol QMBusTrafficDelegate <NSObject>
@optional
- (void)didUpdateBusTraffic:(QMBusTrafficManager *)manager traffic:(QMBusPlanTrafficModel *)traffic; // 当前算路方案路况信息更新

@end

NS_ASSUME_NONNULL_END
