//
//  QMBusETAManager.h
//  SOSOMap
//
//  Created by admin on 2020/6/8.
//  Copyright © 2020 Tencent. All rights reserved.
//


#import <Foundation/Foundation.h>

#import <QMapMiddlePlatform/JsonBuslineInfo.h>
#import <QMapMiddlePlatform/QMBusStopInfo.h>
#import <QMapMiddlePlatform/QMBusETAModel.h>

NS_ASSUME_NONNULL_BEGIN
@class RouteBusSegment;
extern NSString *const kNotifyQMBusETADataKey;
extern NSString *const kNotifyQMSubwayETADataKey;
extern NSString *const kNotifyQMBusETADataChange;
extern NSString *const kNotifyQMBusETADataChangeFaild;

extern NSString *const QMNotifyTrajinfoForBusSegmentChange;

@interface QMBusETAManager : NSObject

+ (QMBusETAManager *)sharedInstance;

// 设置请求的TraceId
- (void)setRequestFromTraceId:(QMBusRequestFromTraceId *)traceId;
- (void)removeRequestFromTraceId:(QMBusRequestFromTraceId *)traceId;
// 设置请求的来源
- (void)setRequestFromSource:(QMBusRequestFromSource *)source;
- (void)removeRequestFromSource:(QMBusRequestFromSource *)source;

// 注册相关
- (void)registerEtaCheckItems:(NSArray<QMBusETAItem *> *)items;
- (void)ungisterEtaCheckItems:(NSArray<QMBusETAItem *> *)items;
- (void)ungisterEtaCheckItems:(NSArray<QMBusETAItem *> *)items clearCache:(BOOL)clear;
- (void)cancelRegisterAllEtaCheck;
- (void)cancelRegisterAllEtaCheckAndClearCache:(BOOL)clear;

// 获取相关
- (QMBusETAModel *)getRealtimeLineByEtaItem:(QMBusETAItem *)item;
- (QMBusETAModel *)getRealtimeLineByBusLineInfo:(QMBusLineInfo *)line;
- (QMBusETAModel *)getFirstRealtimeLineByEtaItemList:(NSArray<QMBusETAItem *> *)etaItemList;

- (void)forceRunTask;
@property (nonatomic) BOOL isWalkETA;
@property (nonatomic) RouteBusSegment *busSegment;

@end
 
NS_ASSUME_NONNULL_END
