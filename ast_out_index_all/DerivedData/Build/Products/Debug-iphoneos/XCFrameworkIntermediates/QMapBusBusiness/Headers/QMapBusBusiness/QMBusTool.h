//
//  QMBusTool.h
//  SOSOMap
//
//  Created by 胡朔溢 on 2019/3/14.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/RouteSearcherConstant.h>

@class RouteBusSegment;
@class QMRealtimeBusInfo;
@class BusRouteResult, BusRoute;
@class QMapNoticeModel;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMBusAlongwayStationType) {
    QMBusAlongwayStationTypeTop,
    QMBusAlongwayStationTypeLeft,
    QMBusAlongwayStationTypeRight,
    QMBusAlongwayStationTypeBottom,
};

@interface QMBusTool : NSObject

+ (NSMutableAttributedString *)getLineTimeAttributeWithSegment:(RouteBusSegment *)segment textColor:(UIColor *)textColor;
+ (NSString *)getLineStartEndTime:(RouteBusSegment *)segment;
+ (NSMutableAttributedString *)getLineStartEndTimeAttributeWithSegment:(RouteBusSegment *)segment textColor:(UIColor *)textColor;
+ (NSMutableAttributedString *)getLineStateAttributeWithSegment:(RouteBusSegment *)segment;
+ (NSMutableAttributedString *)attributeStringWithRealtimeBusInfo:(QMRealtimeBusInfo *)busInfo;
+ (NSTextAttachment *)attributedStrInCityColorTrainImageWith:(RouteBusSegment *)segment hasArrow:(BOOL)hasArrow maxWidth:(CGFloat)maxWidth;
+ (NSTextAttachment *)attributedStrInCityColorCycleImageWith:(RouteBusSegment *)segment hasArrow:(BOOL)hasArrow maxWidth:(CGFloat)maxWidth;
+ (NSTextAttachment *)attributedStrInCityColorCarImageWith:(RouteBusSegment *)segment hasArrow:(BOOL)hasArrow maxWidth:(CGFloat)maxWidth;
+ (NSTextAttachment *)attributedStrInSameStationImageWith:(RouteBusSegment *)segment;

// 公交算路 及算路详情 方案展示
+ (NSTextAttachment *)attributedStrInCityColorSubwayImageWith:(RouteBusSegment *)segment hasArrow:(BOOL)hasArrow maxWidth:(CGFloat)maxWidth;

+ (NSTextAttachment *)attributedStrInCityColorBusImageWithStr:(NSString *)segmentStr hasArrow:(BOOL)hasArrow maxWidth:(CGFloat)maxWidth;
// 绘制最后一段元素+小人
+ (NSTextAttachment *)attributedStrInLastImageWithStr:(RouteBusSegment *)segment lastSeg:(RouteBusSegment *)lastSeg maxWidth:(CGFloat)maxWidth;

+ (NSMutableArray *)busOrSubSegmentListWithList:(NSArray <RouteBusSegment *>*)list;

// 隐藏步行
+ (NSMutableArray *)hideWalkSegmentListWithList:(NSArray <RouteBusSegment *>*)list;


+ (UIColor *)lineColorWith:(RouteBusSegment *)segment;

//行中埋点
+ (void)doBusOnPassageRecordWithRouteResult:(BusRouteResult *)result;
+ (void)doBusOnPassageRecordWithRoute:(BusRoute *)busRoute traceId:(NSString *)traceId;

//生成站点文字的Image 用于底图Overlay
+ (UIImage *)generateImageWithText:(NSString *)text type:(QMBusAlongwayStationType)alongwayStationType;

+ (BOOL)isValidBusCoordinate:(CLLocationCoordinate2D)coordinate;

+ (CLLocationCoordinate2D)getValidBusCoordinateWithPointx:(double)pointx  pointy:(double)pointy;

+ (NSString *)getCityNameFromCoordinate:(CLLocationCoordinate2D)coordinatePoint;
+ (NSMutableAttributedString *)busAttriStringForList:(NSArray *)list
                                           textColor:(UIColor *)textColor
                                     attachMentArrow:(NSTextAttachment *)attachMentArrow;
/// 连接两个元素之间的箭头
+ (NSTextAttachment *)attributedStrInCityArrowImageWith:(RouteBusSegment *)segment;
+ (NSTextAttachment *)attributedStrInCityColorWalkImageWith:(RouteBusSegment *)segment;
/// 步行时间和间隔icon
+ (NSTextAttachment *)attributedStrInCityColorTimeArrowImageWith:(RouteBusSegment *)segment isFirst:(BOOL)isFrist;
//安全的string和number类型
+ (NSString *)safeString:(NSString *)str;
+ (NSNumber *)safeNumber:(NSNumber *)number;

+ (QMapNoticeModel *)convertAnnouncement:(QMJCE_MapRoute_DestAnnouncementResp *)rsp;

@end

NS_ASSUME_NONNULL_END
