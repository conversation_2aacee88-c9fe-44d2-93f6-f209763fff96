//
//  BusRoute.h
//  SOSOMap
//
//  Created by s<PERSON><PERSON><PERSON><PERSON> on 2020/4/23.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/Route.h>
#import <QMapMiddlePlatform/ConstantDefine.h>
#import <QMapMiddlePlatform/RouteSearcherConstant.h>
#import <QMapProto/QMJCE_MapRoute_BusRoute.h>
#import <QMapProto/QMJCE_text_BusRouteAnnouncementInfo.h>
#import <QMapMiddlePlatform/RouteBusSegment.h>


@class RoutePoint;
@class QMDataArchive;
@class RouteBusStation;
@class WalkRoute;

//code0代表是其他换乘
static const NSInteger QMTransferNormalCode = 0;
//code16代表是公交换乘段，此时不使用步行信息
static const NSInteger QMTransferBusCode = 16;
//code32代表是地铁换乘段，此时在换乘文案中使用tips信息
static const NSInteger QMTransferSubwayCode = 32;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, BusRouteRTagType) {
    BusRouteRTagTypeGreen = 1,
    BusRouteRTagTypeBlue = 2,
};

typedef NS_OPTIONS(NSInteger, QMBusRouteMixTagType) {
    QMBusRouteMixTagTypeBus = 1 << 0,
    QMBusRouteMixTagTypeWalk = 1 << 1,
    QMBusRouteMixTagTypeCycle = 1 << 2,
    QMBusRouteMixTagTypeCar = 1 << 3,
    QMBusRouteMixTagTypeSubway = 1 << 4,
    QMBusRouteMixTagTypeCycleBus = QMBusRouteMixTagTypeCycle | QMBusRouteMixTagTypeBus,
    QMBusRouteMixTagTypeCycleSubway = QMBusRouteMixTagTypeCycle | QMBusRouteMixTagTypeSubway,
    QMBusRouteMixTagTypeCarSubway = QMBusRouteMixTagTypeCar | QMBusRouteMixTagTypeSubway,
    QMBusRouteMixTagTypeTaxi
};

typedef NS_ENUM(NSInteger, QMBusRouteCycleWalkMixType) {
    QMBusRouteMixTypeNone                       = 0,
    QMBusRouteMixTypeStartCycleWalkEndWalk      = 1,
    QMBusRouteMixTypeStartCycleWalkEndCycle     = 2,
    QMBusRouteMixTypeEndCycleWalkStartWalk      = 3,
    QMBusRouteMixTypeEndCycleWalkStartCycle     = 4,
    QMBusRouteMixTypeStartEndCycleWalk          = 5
};

typedef NS_ENUM(NSInteger, QMBusRouteStartEndTranDisplayType) {
    QMBusRouteTranDisplayTypeStartEndTranNone   = 0,
    QMBusRouteTranDisplayTypeStartWalkEndWalk   = 1,
    QMBusRouteTranDisplayTypeStartWalkEndCycle  = 2,
    QMBusRouteTranDisplayTypeStartCycleEndWalk  = 3,
    QMBusRouteTranDisplayTypeStartCycleEndCycle = 4,
};

typedef NS_ENUM(NSInteger, QMBusRouteStartEndTranSwitchType) {
    QMBusRouteStartEndTranSwitchTypeNone        = 0,
    QMBusRouteStartEndTranSwitchTypeStart       = 1,
    QMBusRouteStartEndTranSwitchTypeEnd         = 2,
    QMBusRouteStartEndTranSwitchTypeAll         = 3,
};

@class QMBusLocationManager;
@interface BusRoute : Route<NSCopying>

@property (nonatomic, strong) QMPointInfo *startPoint;
@property (nonatomic, strong) QMPointInfo *endPoint;
@property (nonatomic, strong) NSMutableArray *selectLineUids;
@property (nonatomic,   copy) NSArray<RouteBusSegment *> *segmentsTest;

/**
 换乘
 */
@property (nonatomic,   copy) NSArray *trans;

@property (nonatomic,   copy) NSArray<RoutePoint *> *points;
@property (nonatomic, assign) NSInteger time;
@property (nonatomic, assign) NSInteger distance;
@property (nonatomic, assign) NSInteger walkDistance;
@property (nonatomic, assign) BusTimeLine busRunningState;
@property (nonatomic, strong) NSString *routeID;  /// 索引
@property (nonatomic, assign) NSInteger routeDescTime;
@property (nonatomic, assign) NSInteger tag;
@property (nonatomic,   copy) NSString *routedDesc;
@property (nonatomic, assign) NSInteger price;
@property (nonatomic, strong) NSArray * gonggaosArray;
@property (nonatomic, assign) BOOL hasRtBus;
@property (nonatomic, assign) BOOL hasSubway;
@property (nonatomic, assign) NSInteger type;
@property (nonatomic, assign) NSInteger order;
@property (nonatomic, assign) int transferCount;

@property (nonatomic, assign) BOOL isCrossCity;
@property (nonatomic,   copy) NSString *crossRouteTitle;
@property (nonatomic,   copy) NSString *crossCityStartStation;
@property (nonatomic, assign) QMJCE_MapRoute_CrossCityType crossCityType;
@property (nonatomic, strong) QMJCE_text_BusRouteAnnouncementInfo *announcement;

@property (nonatomic, assign) BusRouteRTagType rtag;
@property (nonatomic,   copy) NSString *strTag;
@property (nonatomic,   copy) NSString *strRich;
@property (nonatomic, assign) int stationCount;
@property (nonatomic) RouteBusSegment *firstBusSegment;
@property (nonatomic) RouteBusSegment *lastBusSegment;
@property (nonatomic, nullable) NSString *busRouteId;

// 为了实时信息 添加的
// 多方案
@property (nonatomic, copy) NSArray *firstEtaCheckItems;
// 多方案详情
@property (nonatomic, copy) NSArray *allEtaCheckItems;

@property (nonatomic, copy) NSString *routeTraceId;
@property (nonatomic, copy) NSString *cityCode;
@property (nonatomic, assign) int deptime;
@property (nonatomic, strong) NSDictionary *feedsBusSegmentMap;
@property (nonatomic, strong) NSDictionary *feedsWalkSegmentMap;

@property (nonatomic, assign) int busOrSubwayCount;

//公交混合算路协议更新加入的字段

@property (nonatomic, strong) NSArray <QMJCE_routesearch_TransitMixTag*> *transitMixTags; //多方案页标签
@property (nonatomic, strong) QMJCE_routesearch_WalkRoute *walk;//纯步行
@property (nonatomic, strong) QMJCE_routesearch_WalkRoute *cycle;//纯骑行
@property (nonatomic, assign) QMBusRouteMixTagType mixTagType;//混合算路type
@property (nonatomic, assign) NSInteger mixType;//JCE返回的混合算路type

@property (nonatomic, strong) QMJCE_MapRoute_TaxiInfo *taxiRoute;
@property (nonatomic, assign) BOOL hasReported;
@property (nonatomic) NSArray *crossTaxiArray;

@property (nonatomic, copy) NSString *nickName;

@property (nonatomic) BOOL isFakeRoute;

// 综合导航-骑行组合扩召回 *********  新增
@property (nonatomic) QMJCE_MapRoute_Tran *startTran;
@property (nonatomic) QMJCE_MapRoute_Tran *endTran;
@property (nonatomic) QMBusRouteCycleWalkMixType cycleWalkType;
@property (nonatomic) QMJCE_MapRoute_CycleToWalkDelta *startWalkDelta;
@property (nonatomic) QMJCE_MapRoute_CycleToWalkDelta *endWalkDelta;
@property (nonatomic) QMBusRouteStartEndTranDisplayType curDisplayStartEndTranType; // 当前用户选择的类型
@property (nonatomic) NSString *routeId; // 具体的id值

- (QMBusRouteStartEndTranSwitchType)tranSwitchType;
- (NSArray<RouteBusSegment *> *)tranSwitchedSegmentsTest;
- (NSString *)tranTypeSelectStr;
+ (NSString *)tranTypeStrWithType:(QMBusRouteStartEndTranDisplayType)type;

//根据最新jce创建model
- (id)initWithJceContent:(QMJCE_MapRoute_BusRoute *)jceRoute;
//离线检索使用，根据dic创建model
- (id)initWithDicContent:(NSDictionary *)dictionary;

- (id)content;

- (BusRoute *)selectLinesNewBusRoute;

 
- (NSDictionary *)getReportBusLinesWithLocationManager:(QMBusLocationManager *)busLocationManager;

- (NSDictionary *)getReportBusStops;

- (NSDictionary *)getReportDestInfo;

- (NSArray *)busLineIdList;

- (RouteBusStation *)lastBusStation;



@end

NS_ASSUME_NONNULL_END
