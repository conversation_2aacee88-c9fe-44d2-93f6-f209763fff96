//
//  QMBusRouteJce+MJExtension.h
//  QMapBusiness
//
//  Created by admin on 2021/6/1.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <MJExtension/MJExtension.h>

NS_ASSUME_NONNULL_BEGIN
@interface QMBusRouteJceMJExtension : NSObject
+ (NSMutableDictionary *)toDictionaryWithBusRouteJce:(QMJCE_MapRoute_BusRoute *)busRouteJce
                          replacePropertyName:(BOOL)replace;
@end
 
@interface QMJCE_text_BusRouteAnnouncementInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_CrossCityInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_IrregularTime(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_BusRouteLineExt(MJExtension)<MJKeyValue>
@end

@interface QMJCE_text_TextSeg(MJExtension)<MJKeyValue>
@end

@interface QMJCE_text_OperationInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_text_RichInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_common_Point(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_Station(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_BusRouteLine(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_BusRouteLineContainer(MJExtension)<MJKeyValue>
@end

// BusTran
@interface QMJCE_MapRoute_Exit(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_GetOnOff(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_Walk(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_TranDrive(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_Tran(MJExtension)<MJKeyValue>
@end

// BusRoute
@interface QMJCE_MapRoute_BusRoute(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_TaxiInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_WalkCycleLight(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_XPInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_WalkCycleRouteSegment(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_WalkCycleRoute(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_Cycle(MJExtension)<MJKeyValue>
@end

@interface QMJCE_routesearch_PlaneDepartureArrival(MJExtension)<MJKeyValue>
@end

@interface QMJCE_routesearch_PlaneStopInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_routesearch_PlaneMidInfo(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_CycleToWalkDelta(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_CustomsCrossing(MJExtension)<MJKeyValue>
@end

@interface QMJCE_MapRoute_WayPoint(MJExtension)<MJKeyValue>
@end

NS_ASSUME_NONNULL_END
