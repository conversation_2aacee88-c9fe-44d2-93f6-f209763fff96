//
//  QMBusRouteDetailModel.h
//  SOSOMap
//
//  Created by admin on 2020/4/20.
//  Copyright © 2020 Tencent. All rights reserved.
//
 
#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/RouteBusSegment.h>

NS_ASSUME_NONNULL_BEGIN


@class BusSegmentModel, BusRoute, QMPointInfo;

typedef NS_ENUM(NSUInteger, BusDetailItemType) {
    BusDetailItemTypeStart,
    BusDetailItemTypeEnd,
    BusDetailItemTypeWalk,
    BusDetailItemTypeBusLineInfo,
    BusDetailItemTypeSubwayLineInfo,
    BusDetailItemTypeBusItem,
    BusDetailItemTypeSubwayItem,
    BusDetailItemTypeBusModelItem,
    BusDetailItemTypeBusStartItem,
    BusDetailItemTypeSubwayStartItem,
    BusDetailItemTypeBusEndItem,
    BusDetailItemTypeSubwayEndItem,
    BusDetailItemTypeStationNum,
};

@interface BusDetailItem : NSObject

@property (nonatomic, strong)BusSegmentModel *segmentModel;
@property (nonatomic, copy)NSString *name;
@property (nonatomic, copy)NSString *subName;
@property (nonatomic, assign)NSInteger dayNum;
@property (nonatomic, assign)BusDetailItemType type;

+ (BusDetailItem *)busDetailItem;

@end

typedef NS_ENUM(NSUInteger, BusSegmentModelType) {
    BusSegmentModelTypeDefault,
    BusSegmentModelTypeStart,
    BusSegmentModelTypeEnd,
    BusSegmentModelTypeWalk,
    BusSegmentModelTypeBus,
    BusSegmentModelTypeSubWay,
    BusSegmentModelTypeTrain,
    BusSegmentModelTypeCycle,
    BusSegmentModelTypeCar,
    
    BusSegmentModelTypeInSideCity, // 市内路线梗概
    BusSegmentModelTypeSpace, // 占位
};
 

@interface BusSegmentModel : NSObject

@property (nonatomic, strong) RouteBusSegment *segment;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) BusSegmentModelType type;
@property (nonatomic, assign) BusSegmentTrainType trainType;
@property (nonatomic, copy) NSString *transContent;    //换乘的详细信息，用来区分展示
@property (nonatomic, copy) NSString *nextTransContent;

@property (nonatomic, assign) BOOL allOpen; // 截图的时候用

// BusSegmentModelTypeInSideCity 这种类型才有数据
@property (nonatomic, assign) BOOL open;

/// 每条路线中的每一段比如换乘段、公交段、步行段
@property (nonatomic, copy) NSArray<BusSegmentModel *> *detailSegments;
@property (nonatomic, weak) BusSegmentModel *nextSegmentModel;
@property (nonatomic, weak) BusSegmentModel *prepSegmentModel;

// 市内段折叠类型 只有驾车方案 标识
@property (nonatomic, assign) BOOL isInsideCityAndDrive;
 
+ (BusSegmentModel *)busSegmentBusModelForData:( RouteBusSegment *_Nullable)segment;

@end

@class QMJCE_text_BusRouteAnnouncementInfo;
@interface BusModel : NSObject
@property (nonatomic, copy) NSArray <BusSegmentModel *>*extendSubModels;
@property (nonatomic, copy) NSArray <BusSegmentModel *>*subModels;
@property (nonatomic, copy) NSArray <RouteBusSegment *>*segments;
@property (nonatomic, copy) NSArray <BusDetailItem *>*detailItemList;
@property (nonatomic, assign) BOOL allOpen;
@property (nonatomic, copy) NSString *startName;
@property (nonatomic, strong) QMPointInfo *startPoint;
@property (nonatomic, copy) NSString *endName;
@property (nonatomic, strong) QMPointInfo *endPoint;

@property (nonatomic, copy) NSArray <BusSegmentModel *>*crossCityModels;

+ (BusModel *)busModelForData:(NSArray <RouteBusSegment *>*)content;

+ (BusModel *)modelWithRoute:(BusRoute *)route;

+ (BusModel *)modelExtendWithRoute:(BusRoute *)route; // 跨城折叠后用的

- (void)updateModel;

- (NSDictionary *)getReportWalkSteps;

@end

NS_ASSUME_NONNULL_END
