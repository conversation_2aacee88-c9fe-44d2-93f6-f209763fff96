dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/QMIntentRouteUtils.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/LiteRouteSearch-iOS14.0/LiteRouteSearch-iOS14.0-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/QMIntentRouteUtils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/QMLRouteTraffic.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/NavCommuting.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/Jce.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/JceType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/JceDisplayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/Common.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/RouteSearch.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/RttGroupEvent.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/sosomap.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/LiteRouteSearch/LiteRouteSearch/Classes/Support/JCE/QMJceTools.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_sosomap_Header.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMWUP/JceObjectV2.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMWUP/JceObject.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMWUP/JceEnumHelper.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_sosomap_CmdResult.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteReq.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_ApplloReqInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRoute.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteLineContainer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteLine.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteLineExt.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_CrossCityInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_CrossCityType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_IrregularTime.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_Station.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_common_Point.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteLineType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_RunState.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_PlaneDepartureArrival.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_PlaneMidInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_PlaneStopInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_TransitCar.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_CarRoute.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_common_SupplementRouteNavInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_navsns_RttGroupEventInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_CarRouteSegment.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_BR.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_CarRouteSegmentRoadNames.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_CityBorder.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_ExitInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Inter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_IntersectionInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_KP.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_LaneInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_AdditionalLaneInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Light.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_LinkInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_LsLine.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Park.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Roundabout.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_RoundaboutExit.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SP.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SegHints.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SegmentToll.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SpecialGuidance.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SpeedInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Tip.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_common_HDPoint.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SapaInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SapaPoiSubType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_WhiteBound.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_CarSegmentLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_CurveSkipInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_DerouteReason.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_ElectricVehicleRsp.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_ForbiddenInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_HighRiskInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_LinkAttribInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_PassPointInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_RenderSegment.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_RouteLimitInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_RouteTips.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_StartEndInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SimpleXPInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Taxi.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Fee.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Traffic.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_TrafficSegment.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_TrafficCloseInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_TrafficCloseSegment.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_TransitCycle.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Walk.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_WalkRoute.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_RouteLink.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SubwayEntranceAndExit.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_WalkMarker.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_WalkRouteSegment.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_IndoorDoorInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_IndoorLandMarkers.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_WalkTip.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_ws_sign_poi_t.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_OperationInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_OperationType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_RichInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_TextSeg.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_TextBKGStyleType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_TextFuncType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_TextStyleType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteTag.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_CycleToWalkDelta.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_CycleWalkMixType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_StartEndTranType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_TaxiInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_TransitMixTag.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_Tran.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_CustomsCrossing.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_WayPoint.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_Cycle.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_WalkCycleRoute.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_WalkCycleRouteSegment.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_WalkCycleLight.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_XPInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_GetOnOff.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_Exit.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_Media.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_TranDrive.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_TranType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_Walk.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_BusRouteAnnouncementInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_AnnouncementInfoType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_BusLineAnnouncementInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRoutePreference.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_POIReqInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_LocationType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_BusRouteResp.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_CoachInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_DestInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_TransitGuideRsp.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_MapRoute_WalkRouteInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_Info.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_ForkPoint.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_PassPtInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_routesearch_SimplePOIResultInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_BusRouteNoPlanAnnouncement.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_BusRouteNoPlanAnnouncementDetail.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_BusExitAnnouncementInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_text_BusStopAnnouncementInfo.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_sosomap_Package.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_sosomap_CMD.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapProto/QMJCE_sosomap_Tag.h
