#import <Foundation/Foundation.h>

#if __has_attribute(swift_private)
#define AC_SWIFT_PRIVATE __attribute__((swift_private))
#else
#define AC_SWIFT_PRIVATE
#endif

/// The "Launch_icon" asset catalog image resource.
static NSString * const ACImageNameLaunchIcon AC_SWIFT_PRIVATE = @"Launch_icon";

/// The "feedback_ex_bus" asset catalog image resource.
static NSString * const ACImageNameFeedbackExBus AC_SWIFT_PRIVATE = @"feedback_ex_bus";

/// The "feedback_ex_road" asset catalog image resource.
static NSString * const ACImageNameFeedbackExRoad AC_SWIFT_PRIVATE = @"feedback_ex_road";

/// The "ic_overpass" asset catalog image resource.
static NSString * const ACImageNameIcOverpass AC_SWIFT_PRIVATE = @"ic_overpass";

/// The "ic_souterrain" asset catalog image resource.
static NSString * const ACImageNameIcSouterrain AC_SWIFT_PRIVATE = @"ic_souterrain";

/// The "ic_step" asset catalog image resource.
static NSString * const ACImageNameIcStep AC_SWIFT_PRIVATE = @"ic_step";

/// The "ic_ugc_around_pic_sample" asset catalog image resource.
static NSString * const ACImageNameIcUgcAroundPicSample AC_SWIFT_PRIVATE = @"ic_ugc_around_pic_sample";

/// The "ic_ugc_card_back_pic_sample" asset catalog image resource.
static NSString * const ACImageNameIcUgcCardBackPicSample AC_SWIFT_PRIVATE = @"ic_ugc_card_back_pic_sample";

/// The "ic_ugc_card_front_pic_sample" asset catalog image resource.
static NSString * const ACImageNameIcUgcCardFrontPicSample AC_SWIFT_PRIVATE = @"ic_ugc_card_front_pic_sample";

/// The "ic_ugc_hold_card_pic_sample" asset catalog image resource.
static NSString * const ACImageNameIcUgcHoldCardPicSample AC_SWIFT_PRIVATE = @"ic_ugc_hold_card_pic_sample";

/// The "ic_ugc_licence_sample" asset catalog image resource.
static NSString * const ACImageNameIcUgcLicenceSample AC_SWIFT_PRIVATE = @"ic_ugc_licence_sample";

/// The "ic_ugc_place_pic_sample" asset catalog image resource.
static NSString * const ACImageNameIcUgcPlacePicSample AC_SWIFT_PRIVATE = @"ic_ugc_place_pic_sample";

/// The "ic_zebra_crossing" asset catalog image resource.
static NSString * const ACImageNameIcZebraCrossing AC_SWIFT_PRIVATE = @"ic_zebra_crossing";

/// The "launch_logo" asset catalog image resource.
static NSString * const ACImageNameLaunchLogo AC_SWIFT_PRIVATE = @"launch_logo";

/// The "screenEye_placeholder" asset catalog image resource.
static NSString * const ACImageNameScreenEyePlaceholder AC_SWIFT_PRIVATE = @"screenEye_placeholder";

#undef AC_SWIFT_PRIVATE
