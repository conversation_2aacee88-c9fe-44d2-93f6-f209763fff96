import Foundation
#if canImport(AppKit)
import AppKit
#endif
#if canImport(UIKit)
import UIKit
#endif
#if canImport(SwiftUI)
import Swift<PERSON>
#endif
#if canImport(DeveloperToolsSupport)
import DeveloperToolsSupport
#endif

#if SWIFT_PACKAGE
private let resourceBundle = Foundation.Bundle.module
#else
private class ResourceBundleClass {}
private let resourceBundle = Foundation.Bundle(for: ResourceBundleClass.self)
#endif

// MARK: - Color Symbols -

@available(iOS 11.0, macOS 10.13, tvOS 11.0, *)
extension ColorResource {

}

// MARK: - Image Symbols -

@available(iOS 11.0, macOS 10.7, tvOS 11.0, *)
extension ImageResource {

    /// The "Launch_icon" asset catalog image resource.
    static let launchIcon = ImageResource(name: "Launch_icon", bundle: resourceBundle)

    /// The "feedback_ex_bus" asset catalog image resource.
    static let feedbackExBus = ImageResource(name: "feedback_ex_bus", bundle: resourceBundle)

    /// The "feedback_ex_road" asset catalog image resource.
    static let feedbackExRoad = ImageResource(name: "feedback_ex_road", bundle: resourceBundle)

    /// The "ic_overpass" asset catalog image resource.
    static let icOverpass = ImageResource(name: "ic_overpass", bundle: resourceBundle)

    /// The "ic_souterrain" asset catalog image resource.
    static let icSouterrain = ImageResource(name: "ic_souterrain", bundle: resourceBundle)

    /// The "ic_step" asset catalog image resource.
    static let icStep = ImageResource(name: "ic_step", bundle: resourceBundle)

    /// The "ic_ugc_around_pic_sample" asset catalog image resource.
    static let icUgcAroundPicSample = ImageResource(name: "ic_ugc_around_pic_sample", bundle: resourceBundle)

    /// The "ic_ugc_card_back_pic_sample" asset catalog image resource.
    static let icUgcCardBackPicSample = ImageResource(name: "ic_ugc_card_back_pic_sample", bundle: resourceBundle)

    /// The "ic_ugc_card_front_pic_sample" asset catalog image resource.
    static let icUgcCardFrontPicSample = ImageResource(name: "ic_ugc_card_front_pic_sample", bundle: resourceBundle)

    /// The "ic_ugc_hold_card_pic_sample" asset catalog image resource.
    static let icUgcHoldCardPicSample = ImageResource(name: "ic_ugc_hold_card_pic_sample", bundle: resourceBundle)

    /// The "ic_ugc_licence_sample" asset catalog image resource.
    static let icUgcLicenceSample = ImageResource(name: "ic_ugc_licence_sample", bundle: resourceBundle)

    /// The "ic_ugc_place_pic_sample" asset catalog image resource.
    static let icUgcPlacePicSample = ImageResource(name: "ic_ugc_place_pic_sample", bundle: resourceBundle)

    /// The "ic_zebra_crossing" asset catalog image resource.
    static let icZebraCrossing = ImageResource(name: "ic_zebra_crossing", bundle: resourceBundle)

    /// The "launch_logo" asset catalog image resource.
    static let launchLogo = ImageResource(name: "launch_logo", bundle: resourceBundle)

    /// The "screenEye_placeholder" asset catalog image resource.
    static let screenEyePlaceholder = ImageResource(name: "screenEye_placeholder", bundle: resourceBundle)

}

// MARK: - Backwards Deployment Support -

/// A color resource.
struct ColorResource: Swift.Hashable, Swift.Sendable {

    /// An asset catalog color resource name.
    fileprivate let name: Swift.String

    /// An asset catalog color resource bundle.
    fileprivate let bundle: Foundation.Bundle

    /// Initialize a `ColorResource` with `name` and `bundle`.
    init(name: Swift.String, bundle: Foundation.Bundle) {
        self.name = name
        self.bundle = bundle
    }

}

/// An image resource.
struct ImageResource: Swift.Hashable, Swift.Sendable {

    /// An asset catalog image resource name.
    fileprivate let name: Swift.String

    /// An asset catalog image resource bundle.
    fileprivate let bundle: Foundation.Bundle

    /// Initialize an `ImageResource` with `name` and `bundle`.
    init(name: Swift.String, bundle: Foundation.Bundle) {
        self.name = name
        self.bundle = bundle
    }

}

#if canImport(AppKit)
@available(macOS 10.13, *)
@available(macCatalyst, unavailable)
extension AppKit.NSColor {

    /// Initialize a `NSColor` with a color resource.
    convenience init(resource: ColorResource) {
        self.init(named: NSColor.Name(resource.name), bundle: resource.bundle)!
    }

}

protocol _ACResourceInitProtocol {}
extension AppKit.NSImage: _ACResourceInitProtocol {}

@available(macOS 10.7, *)
@available(macCatalyst, unavailable)
extension _ACResourceInitProtocol {

    /// Initialize a `NSImage` with an image resource.
    init(resource: ImageResource) {
        self = resource.bundle.image(forResource: NSImage.Name(resource.name))! as! Self
    }

}
#endif

#if canImport(UIKit)
@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

    /// Initialize a `UIColor` with a color resource.
    convenience init(resource: ColorResource) {
#if !os(watchOS)
        self.init(named: resource.name, in: resource.bundle, compatibleWith: nil)!
#else
        self.init()
#endif
    }

}

@available(iOS 11.0, tvOS 11.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    /// Initialize a `UIImage` with an image resource.
    convenience init(resource: ImageResource) {
#if !os(watchOS)
        self.init(named: resource.name, in: resource.bundle, compatibleWith: nil)!
#else
        self.init()
#endif
    }

}
#endif

#if canImport(SwiftUI)
@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Color {

    /// Initialize a `Color` with a color resource.
    init(_ resource: ColorResource) {
        self.init(resource.name, bundle: resource.bundle)
    }

}

@available(iOS 13.0, macOS 10.15, tvOS 13.0, watchOS 6.0, *)
extension SwiftUI.Image {

    /// Initialize an `Image` with an image resource.
    init(_ resource: ImageResource) {
        self.init(resource.name, bundle: resource.bundle)
    }

}
#endif