//
// QMConfigurationIntent.m
//
// This file was automatically generated and should not be edited.
//

#import "QMConfigurationIntent.h"

#if __has_include(<Intents/Intents.h>) && !TARGET_OS_TV

@implementation QMConfigurationIntent

@end

@interface QMConfigurationIntentResponse ()

@property (readwrite, NS_NONATOMIC_IOSONLY) QMConfigurationIntentResponseCode code;

@end

@implementation QMConfigurationIntentResponse

@synthesize code = _code;

- (instancetype)initWithCode:(QMConfigurationIntentResponseCode)code userActivity:(nullable NSUserActivity *)userActivity {
    self = [super init];
    if (self) {
        _code = code;
        self.userActivity = userActivity;
    }
    return self;
}

@end

#endif
