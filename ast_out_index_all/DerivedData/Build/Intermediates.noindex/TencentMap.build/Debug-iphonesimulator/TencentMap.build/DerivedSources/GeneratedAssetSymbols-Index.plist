<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>colors</key>
	<array/>
	<key>images</key>
	<array>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/IconsAndLaunchImages.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameLaunchIcon</string>
			<key>relativePath</key>
			<string>./Launch_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>launchIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameFeedbackExBus</string>
			<key>relativePath</key>
			<string>./ugc/feedback_ex_bus.imageset</string>
			<key>swiftSymbol</key>
			<string>feedbackExBus</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameFeedbackExRoad</string>
			<key>relativePath</key>
			<string>./ugc/feedback_ex_road.imageset</string>
			<key>swiftSymbol</key>
			<string>feedbackExRoad</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcOverpass</string>
			<key>relativePath</key>
			<string>./ic_overpass.imageset</string>
			<key>swiftSymbol</key>
			<string>icOverpass</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcSouterrain</string>
			<key>relativePath</key>
			<string>./ic_souterrain.imageset</string>
			<key>swiftSymbol</key>
			<string>icSouterrain</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcStep</string>
			<key>relativePath</key>
			<string>./ic_step.imageset</string>
			<key>swiftSymbol</key>
			<string>icStep</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcUgcAroundPicSample</string>
			<key>relativePath</key>
			<string>./ugc/ic_ugc_around_pic_sample.imageset</string>
			<key>swiftSymbol</key>
			<string>icUgcAroundPicSample</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcUgcCardBackPicSample</string>
			<key>relativePath</key>
			<string>./ugc/ic_ugc_card_back_pic_sample.imageset</string>
			<key>swiftSymbol</key>
			<string>icUgcCardBackPicSample</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcUgcCardFrontPicSample</string>
			<key>relativePath</key>
			<string>./ugc/ic_ugc_card_front_pic_sample.imageset</string>
			<key>swiftSymbol</key>
			<string>icUgcCardFrontPicSample</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcUgcHoldCardPicSample</string>
			<key>relativePath</key>
			<string>./ugc/ic_ugc_hold_card_pic_sample.imageset</string>
			<key>swiftSymbol</key>
			<string>icUgcHoldCardPicSample</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcUgcLicenceSample</string>
			<key>relativePath</key>
			<string>./ugc/ic_ugc_licence_sample.imageset</string>
			<key>swiftSymbol</key>
			<string>icUgcLicenceSample</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcUgcPlacePicSample</string>
			<key>relativePath</key>
			<string>./ugc/ic_ugc_place_pic_sample.imageset</string>
			<key>swiftSymbol</key>
			<string>icUgcPlacePicSample</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameIcZebraCrossing</string>
			<key>relativePath</key>
			<string>./ic_zebra_crossing.imageset</string>
			<key>swiftSymbol</key>
			<string>icZebraCrossing</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/IconsAndLaunchImages.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameLaunchLogo</string>
			<key>relativePath</key>
			<string>./launch_logo.imageset</string>
			<key>swiftSymbol</key>
			<string>launchLogo</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/Resources/images.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameScreenEyePlaceholder</string>
			<key>relativePath</key>
			<string>./screenEye_placeholder.imageset</string>
			<key>swiftSymbol</key>
			<string>screenEyePlaceholder</string>
		</dict>
	</array>
	<key>symbols</key>
	<array/>
</dict>
</plist>
