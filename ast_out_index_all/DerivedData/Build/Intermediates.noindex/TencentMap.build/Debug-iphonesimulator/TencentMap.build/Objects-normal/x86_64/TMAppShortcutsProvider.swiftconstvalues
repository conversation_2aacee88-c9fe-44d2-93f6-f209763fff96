[{"typeName": "TencentMap.TMAppShortcutsProvider", "mangledTypeName": "10TencentMap22TMAppShortcutsProviderC", "kind": "class", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/TMAppShortcutsProvider.swift", "line": 14, "conformances": ["AppIntents.AppShortcutsProvider"], "associatedTypeAliases": [], "properties": [{"label": "shortcutTileColor", "type": "AppIntents.ShortcutTileColor", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/TMAppShortcutsProvider.swift", "line": 16, "valueKind": "Enum", "value": {"name": "lightBlue"}}, {"label": "appShortcuts", "type": "Swift.Array<AppIntents.AppShortcut>", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/TMAppShortcutsProvider.swift", "line": 18, "valueKind": "Runtime", "resultBuilder": {"type": "AppIntents.AppShortcutsBuilder"}}], "availabilityAttributes": [{"platform": "iOS", "introducedVersion": "16", "isUnavailable": false, "isDeprecated": false}]}]