dependencies: \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0/QMapSwiftBridge.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/QMPrefixHeader.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/../Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/TargetConditionals.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/main.m \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CarPlay.framework/Modules/module.modulemap
