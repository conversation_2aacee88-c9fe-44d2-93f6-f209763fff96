[{"typeName": "TencentMap.Destination", "mangledTypeName": "10TencentMap11DestinationO", "kind": "enum", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/Destination.swift", "line": 15, "conformances": ["Swift.Equatable", "<PERSON><PERSON>", "Swift.RawRepresentable", "Swift.Decodable", "Swift.Encodable", "Swift.Sendable", "Swift.BitwiseCopyable", "AppIntents.AppEnum", "AppIntents.AppValue", "AppIntents.StaticDisplayRepresentable", "AppIntents.PersistentlyIdentifiable", "AppIntents.TypeDisplayRepresentable", "AppIntents._IntentValue", "AppIntents.CaseDisplayRepresentable", "Foundation.CustomLocalizedStringResourceConvertible", "Swift.CaseIterable"], "associatedTypeAliases": [{"typeAliasName": "RawValue", "substitutedTypeName": "Swift.String", "substitutedMangledTypeName": "SS"}, {"typeAliasName": "ValueType", "substitutedTypeName": "TencentMap.Destination", "substitutedMangledTypeName": "10TencentMap11DestinationO"}, {"typeAliasName": "UnwrappedType", "substitutedTypeName": "TencentMap.Destination", "substitutedMangledTypeName": "10TencentMap11DestinationO"}, {"typeAliasName": "Specification", "substitutedTypeName": "some AppIntents.ResolverSpecification", "substitutedMangledTypeName": "10AppIntents0A4EnumPAAE28defaultResolverSpecificationQrvpZQOy10TencentMap11DestinationO_Qo_", "opaqueTypeProtocolRequirements": ["AppIntents.ResolverSpecification"], "opaqueTypeSameTypeRequirements": []}, {"typeAliasName": "AllCases", "substitutedTypeName": "Swift.Array<TencentMap.Destination>", "substitutedMangledTypeName": "Say10TencentMap11DestinationOG"}], "properties": [{"label": "symbol", "type": "Swift.String", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/Destination.swift", "line": 21, "valueKind": "Runtime"}, {"label": "title", "type": "Swift.String", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/Destination.swift", "line": 30, "valueKind": "Runtime"}, {"label": "rawValue", "type": "Swift.String", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "valueKind": "Runtime"}, {"label": "typeDisplayRepresentation", "type": "AppIntents.TypeDisplayRepresentation", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/Destination.swift", "line": 43, "valueKind": "InitCall", "value": {"type": "AppIntents.TypeDisplayRepresentation", "arguments": [{"label": "name", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "目的地"}, {"label": "numericFormat", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "nil"}]}}, {"label": "caseDisplayRepresentations", "type": "Swift.Dictionary<TencentMap.Destination, AppIntents.DisplayRepresentation>", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/Destination.swift", "line": 45, "valueKind": "Dictionary", "value": [{"key": {"valueKind": "Enum", "value": {"name": "home"}}, "value": {"valueKind": "InitCall", "value": {"type": "AppIntents.DisplayRepresentation", "arguments": [{"label": "title", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "回家"}, {"label": "subtitle", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "image", "type": "Swift.Optional<AppIntents.DisplayRepresentation.Image>", "valueKind": "InitCall", "value": {"type": "Swift.Optional<AppIntents.DisplayRepresentation.Image>", "arguments": [{"label": "systemName", "type": "Swift.String", "valueKind": "RawLiteral", "value": "house"}, {"label": "tintColor", "type": "Swift.Optional<UIKit.UIColor>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "symbolConfiguration", "type": "Swift.Optional<UIKit.UIImage.SymbolConfiguration>", "valueKind": "RawLiteral", "value": "nil"}]}}]}}}, {"key": {"valueKind": "Enum", "value": {"name": "work"}}, "value": {"valueKind": "InitCall", "value": {"type": "AppIntents.DisplayRepresentation", "arguments": [{"label": "title", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "去公司"}, {"label": "subtitle", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "image", "type": "Swift.Optional<AppIntents.DisplayRepresentation.Image>", "valueKind": "InitCall", "value": {"type": "Swift.Optional<AppIntents.DisplayRepresentation.Image>", "arguments": [{"label": "systemName", "type": "Swift.String", "valueKind": "RawLiteral", "value": "building.2"}, {"label": "tintColor", "type": "Swift.Optional<UIKit.UIColor>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "symbolConfiguration", "type": "Swift.Optional<UIKit.UIImage.SymbolConfiguration>", "valueKind": "RawLiteral", "value": "nil"}]}}]}}}]}, {"label": "allCases", "type": "Swift.Array<TencentMap.Destination>", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "true", "valueKind": "Array", "value": [{"valueKind": "Enum", "value": {"name": "home"}}, {"valueKind": "Enum", "value": {"name": "work"}}]}], "cases": [{"name": "home", "rawValue": "home"}, {"name": "work", "rawValue": "work"}], "availabilityAttributes": [{"platform": "iOS", "introducedVersion": "16", "isUnavailable": false, "isDeprecated": false}]}]