dependencies: \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0/QMapSwiftBridge.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/QMPrefixHeader.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/../Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/TargetConditionals.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/QMBasicSchemer.m \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/QMBasicSchemer.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/os.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UserNotifications.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CarPlay.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/CoreLocation.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMapFoundation.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMapFoundationDefine.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/NSArray+QMAdditions.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QDevice.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMDeviceObject.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMAnnotationLabelUtils.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMProtocolProxy.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/NSString+URL.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMWeakObjectContainer.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/NSMutableArray+QMThreadSafe.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/NSMutableDictionary+QMThreadSafe.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMThreadSafeMap.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMGCDTimer.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLogManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMXlogManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMTimeCostLogger.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMGeneralTimeCostLogger.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMGeneralTimeCostInfo.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMGeneralPerformanceTool.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMSosoAuthHandler.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QCommonDefine.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMDebugManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMIdentifierManager.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/AppTrackingTransparency.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMServerUrlManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMDelegateManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMFileDirectoryManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/SoftwareVersionManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMKVCache.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkRequest.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkRequestParameters.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMHttpToLongLinkProtocol.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkEncryptor.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkReqParamContext.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkManager+JCE.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMNetworkManager+JSON.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMMQTT.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMMQTTDefine.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMMQTTManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMAliveMessageManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLocationDefine.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLocationManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TencentLBS/TencentLBS.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLocationManagerDebugDelegate.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/CLLocation+Features.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMIndoorLocation.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMMulticastDelegateNode.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLocatingStrategyParam.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLocatingStrategy.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLocationManager+AuthorizationStatus.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMModel.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMHippyDebuggingModel.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCloudSyncBaseItem.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCS.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCSSDK.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCSExtServiceProtocol.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCSSDKItem.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCSServiceConfiguration.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCSBaseBPO.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCSSDKBasicConfiguration.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMStatManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMAlarmMonitor.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCOSManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMCOSErrorCode.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMFileDownloader.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMQAPMManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMQAPMConstants.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMSystemPrivacyManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLifecycleManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMSafeModeManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMKeyValueDataManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMOpenGLTools.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMMapBaseManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMLaunchTimeLogger.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/WeixinSDKManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMLoginFailureContext.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMWeChatPayParam.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapMiddlePlatform/Headers/QMapMiddlePlatform/QMOpenMiniProgramOption.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMUniversalLinksManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QQSDKManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMUserActivityManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMIntentManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMIntentConfirmView.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/WeiboSDKManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMDouyinSDKManager.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapBusiness/Headers/QMapBusiness/QMXHSOpenSDKManager.h
