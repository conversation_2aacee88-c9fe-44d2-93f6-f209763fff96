dependencies: \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ExplicitPrecompiledModules/TargetConditionals-4UXLLMN2XO77O2Y52P918NOGK.pcm \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0/QMapSwiftBridge.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/Darwin.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/mach-o/module.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/QMPrefixHeader.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/../Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/TargetConditionals.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/RaftKit/Plugins/Beacon/QMRaftBeaconViewController.m \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/RaftKit/Plugins/Beacon/QMRaftBeaconViewController.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/RaftKit/Plugins/Beacon/QMRaftBeaconDataProvider.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers/QMapFoundation/QMapFoundationDefine.h \
  /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapMiddlePlatform/Headers/QMapMiddlePlatform/UserOpDataManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/RaftKit/Plugins/Beacon/QMRaftBeaconListViewController.h
