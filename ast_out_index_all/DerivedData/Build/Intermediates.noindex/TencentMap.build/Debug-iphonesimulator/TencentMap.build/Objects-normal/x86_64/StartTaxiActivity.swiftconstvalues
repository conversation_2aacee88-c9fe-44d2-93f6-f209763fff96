[{"typeName": "TencentMap.StartTaxiActivity", "mangledTypeName": "10TencentMap17StartTaxiActivityV", "kind": "struct", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 16, "conformances": ["AppIntents.AppIntent", "TencentMap.TaxiIntentNavigable", "AppIntents.PersistentlyIdentifiable", "AppIntents._SupportsAppDependencies", "Swift.Sendable", "TencentMap.AppIntentQQMapNavigable", "TencentMap.RunOnce", "Swift.BitwiseCopyable"], "associatedTypeAliases": [{"typeAliasName": "PerformResult", "substitutedTypeName": "some AppIntents.OpensIntent", "substitutedMangledTypeName": "10TencentMap17StartTaxiActivityV7performQryYaKFQOy_Qo_", "opaqueTypeProtocolRequirements": ["AppIntents.OpensIntent"], "opaqueTypeSameTypeRequirements": []}, {"typeAliasName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "substitutedTypeName": "some AppIntents.ParameterSummary", "substitutedMangledTypeName": "10TencentMap17StartTaxiActivityV16parameterSummaryQrvpZQOy_Qo_", "opaqueTypeProtocolRequirements": ["AppIntents.ParameterSummary"], "opaqueTypeSameTypeRequirements": []}], "properties": [{"label": "isRunning", "type": "Swift.Bool", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 18, "valueKind": "RawLiteral", "value": "false"}, {"label": "title", "type": "Foundation.LocalizedStringResource", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 20, "valueKind": "RawLiteral", "value": "打车"}, {"label": "description", "type": "AppIntents.IntentDescription", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 22, "valueKind": "InitCall", "value": {"type": "AppIntents.IntentDescription", "arguments": [{"label": "", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "使用打车服务"}, {"label": "categoryName", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "出行服务"}, {"label": "searchKeywords", "type": "Swift.Array<Foundation.LocalizedStringResource>", "valueKind": "Array", "value": [{"valueKind": "RawLiteral", "value": "打车"}, {"valueKind": "RawLiteral", "value": "taxi"}]}]}}, {"label": "parameterSummary", "type": "some AppIntents.ParameterSummary", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 26, "valueKind": "InitCall", "value": {"type": "AppIntents.IntentParameterSummary<TencentMap.StartTaxiActivity>", "arguments": [{"label": "", "type": "AppIntents.ParameterSummaryString<TencentMap.StartTaxiActivity>", "valueKind": "RawLiteral", "value": "使用打车服务"}, {"label": "table", "type": "Swift.Optional<Swift.String>", "valueKind": "RawLiteral", "value": "nil"}]}}, {"label": "openAppWhenRun", "type": "Swift.Bool", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 30, "valueKind": "RawLiteral", "value": "true"}, {"label": "displayRepresentation", "type": "AppIntents.DisplayRepresentation", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/StartTaxiActivity.swift", "line": 32, "valueKind": "InitCall", "value": {"type": "AppIntents.DisplayRepresentation", "arguments": [{"label": "title", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "打车"}, {"label": "subtitle", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "image", "type": "Swift.Optional<AppIntents.DisplayRepresentation.Image>", "valueKind": "InitCall", "value": {"type": "Swift.Optional<AppIntents.DisplayRepresentation.Image>", "arguments": [{"label": "systemName", "type": "Swift.String", "valueKind": "RawLiteral", "value": "figure.wave"}, {"label": "tintColor", "type": "Swift.Optional<UIKit.UIColor>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "symbolConfiguration", "type": "Swift.Optional<UIKit.UIImage.SymbolConfiguration>", "valueKind": "RawLiteral", "value": "nil"}]}}]}}], "availabilityAttributes": [{"platform": "iOS", "introducedVersion": "16", "isUnavailable": false, "isDeprecated": false}]}]