[{"typeName": "TencentMap.GotoDestinationActivity", "mangledTypeName": "10TencentMap23GotoDestinationActivityV", "kind": "struct", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 16, "conformances": ["AppIntents.AppIntent", "TencentMap.GoHomeOrWorkIntentNavigable", "AppIntents.PersistentlyIdentifiable", "AppIntents._SupportsAppDependencies", "Swift.Sendable", "TencentMap.AppIntentQQMapNavigable", "TencentMap.RunOnce"], "associatedTypeAliases": [{"typeAliasName": "PerformResult", "substitutedTypeName": "some AppIntents.IntentResult", "substitutedMangledTypeName": "10TencentMap23GotoDestinationActivityV7performQryYaKFQOy_Qo_", "opaqueTypeProtocolRequirements": ["AppIntents.IntentResult"], "opaqueTypeSameTypeRequirements": []}, {"typeAliasName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "substitutedTypeName": "some AppIntents.ParameterSummary", "substitutedMangledTypeName": "10TencentMap23GotoDestinationActivityV16parameterSummaryQrvpZQOy_Qo_", "opaqueTypeProtocolRequirements": ["AppIntents.ParameterSummary"], "opaqueTypeSameTypeRequirements": []}], "properties": [{"label": "_destination", "type": "AppIntents.IntentParameter<TencentMap.Destination>", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 37, "valueKind": "InitCall", "value": {"type": "AppIntents.IntentParameter<TencentMap.Destination>", "arguments": [{"label": "title", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "目的地"}, {"label": "description", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "导航目的地"}, {"label": "default", "type": "Swift.Optional<TencentMap.Destination>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "requestValueDialog", "type": "Swift.Optional<AppIntents.IntentDialog>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "requestDisambiguationDialog", "type": "Swift.Optional<AppIntents.IntentDialog>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "inputConnectionBehavior", "type": "AppIntents.InputConnectionBehavior", "valueKind": "Runtime"}, {"label": "supportedValues", "type": "Swift.Array<TencentMap.Destination>", "valueKind": "Runtime"}]}}, {"label": "isRunning", "type": "Swift.Bool", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 18, "valueKind": "RawLiteral", "value": "false"}, {"label": "title", "type": "Foundation.LocalizedStringResource", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 20, "valueKind": "RawLiteral", "value": "回家或者去公司"}, {"label": "description", "type": "AppIntents.IntentDescription", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 22, "valueKind": "InitCall", "value": {"type": "AppIntents.IntentDescription", "arguments": [{"label": "", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "导航回家或者去公司"}, {"label": "categoryName", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "导航"}, {"label": "searchKeywords", "type": "Swift.Array<Foundation.LocalizedStringResource>", "valueKind": "Array", "value": [{"valueKind": "RawLiteral", "value": "回家"}, {"valueKind": "RawLiteral", "value": "去公司"}, {"valueKind": "RawLiteral", "value": "公司"}]}]}}, {"label": "displayRepresentation", "type": "AppIntents.DisplayRepresentation", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 26, "valueKind": "Runtime"}, {"label": "parameterSummary", "type": "some AppIntents.ParameterSummary", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 30, "valueKind": "InitCall", "value": {"type": "AppIntents.IntentParameterSummary<TencentMap.GotoDestinationActivity>", "arguments": [{"label": "", "type": "AppIntents.ParameterSummaryString<TencentMap.GotoDestinationActivity>", "valueKind": "Runtime"}, {"label": "table", "type": "Swift.Optional<Swift.String>", "valueKind": "RawLiteral", "value": "nil"}]}}, {"label": "openAppWhenRun", "type": "Swift.Bool", "mangledTypeName": "n/a - deprecated", "isStatic": "true", "isComputed": "false", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 34, "valueKind": "RawLiteral", "value": "true"}, {"label": "destination", "type": "TencentMap.Destination", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 37, "valueKind": "InitCall", "value": {"type": "AppIntents.IntentParameter<TencentMap.Destination>", "arguments": [{"label": "title", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "目的地"}, {"label": "description", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "导航目的地"}, {"label": "default", "type": "Swift.Optional<TencentMap.Destination>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "requestValueDialog", "type": "Swift.Optional<AppIntents.IntentDialog>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "requestDisambiguationDialog", "type": "Swift.Optional<AppIntents.IntentDialog>", "valueKind": "RawLiteral", "value": "nil"}, {"label": "inputConnectionBehavior", "type": "AppIntents.InputConnectionBehavior", "valueKind": "Runtime"}, {"label": "supportedValues", "type": "Swift.Array<TencentMap.Destination>", "valueKind": "Runtime"}]}, "propertyWrappers": [{"type": "AppIntents.IntentParameter", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 36, "arguments": [{"label": "title", "type": "Foundation.LocalizedStringResource", "valueKind": "RawLiteral", "value": "目的地"}, {"label": "description", "type": "Swift.Optional<Foundation.LocalizedStringResource>", "valueKind": "RawLiteral", "value": "导航目的地"}]}]}, {"label": "$destination", "type": "AppIntents.IntentParameter<TencentMap.Destination>", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 37, "valueKind": "Runtime"}, {"label": "delay", "type": "Swift.Double", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 39, "valueKind": "RawLiteral", "value": "2.0"}, {"label": "sourceType", "type": "Swift.String", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 41, "valueKind": "Runtime"}, {"label": "intentSource", "type": "Swift.String", "mangledTypeName": "n/a - deprecated", "isStatic": "false", "isComputed": "true", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/GotoDestinationActivity.swift", "line": 50, "valueKind": "Runtime"}], "availabilityAttributes": [{"platform": "iOS", "introducedVersion": "16", "isUnavailable": false, "isDeprecated": false}]}]