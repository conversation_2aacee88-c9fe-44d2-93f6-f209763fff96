dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJClass.m \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/MJExtension/MJExtension-prefix.pch \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/include/ObjectiveC.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJClass.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJCoding.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJExtensionConst.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJKeyValue.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/CoreData.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJProperty.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJPropertyType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJPropertyKey.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJFoundation.h
