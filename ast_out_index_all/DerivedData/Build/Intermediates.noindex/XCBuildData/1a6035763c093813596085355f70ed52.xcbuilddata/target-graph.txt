Target dependency graph (244 targets)
Target 'TencentMap' in project 'TencentMap'
➜ Explicit dependency on target 'BuildInfo' in project 'TencentMap'
➜ Explicit dependency on target 'QMNotificationService' in project 'TencentMap'
➜ Explicit dependency on target 'QMIntentsUI' in project 'TencentMap'
➜ Explicit dependency on target 'QMIntents' in project 'TencentMap'
➜ Explicit dependency on target 'QMWidgetExtension' in project 'TencentMap'
➜ Implicit dependency on target 'Pods-TencentMap' in project 'Pods' via file 'libPods-TencentMap.a' in build phase 'Link Binary'
➜ Implicit dependency on target 'ApolloSDK' in project 'ApolloSDK' via options '-lApolloSDK' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'Aspects' in project 'Aspects' via options '-lAspects' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'AutoCoding' in project 'AutoCoding' via options '-lAutoCoding' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket' via options '-lCocoaAsyncSocket' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'CocoaHotReload' in project 'CocoaHotReload' via options '-lCocoaHotReload' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'CocoaLumberjack' in project 'CocoaLumberjack' via options '-lCocoaLumberjack' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'ComponentKit' in project 'ComponentKit' via options '-lComponentKit' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'DHSmartScreenshot' in project 'DHSmartScreenshot' via options '-lDHSmartScreenshot' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'DKMobile' in project 'DKMobile' via options '-lDKMobile' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool' via options '-lDKProtocolsPool' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'DragonMapKit' in project 'DragonMapKit' via options '-lDragonMapKit' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'DragonMapKitC2OC' in project 'DragonMapKitC2OC' via options '-lDragonMapKitC2OC' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet' via options '-lEDSunriseSet' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'FBRetainCycleDetector' in project 'FBRetainCycleDetector' via options '-lFBRetainCycleDetector' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'FMDB' in project 'FMDB' via options '-lFMDB' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'GCDWebServer' in project 'GCDWebServer' via options '-lGCDWebServer' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'GT' in project 'GT' via options '-lGT' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'GZIP' in project 'GZIP' via options '-lGZIP' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'KVOController' in project 'KVOController' via options '-lKVOController' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'LongLinkSDK' in project 'LongLinkSDK' via options '-lLongLinkSDK' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'LookinServer' in project 'LookinServer' via options '-lLookinServer' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'MJExtension' in project 'MJExtension' via options '-lMJExtension' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'MMKV' in project 'MMKV' via options '-lMMKV' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'MMKVCore' in project 'MMKVCore' via options '-lMMKVCore' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'MQTTClient' in project 'MQTTClient' via options '-lMQTTClient' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel' via options '-lMapBaseOCModel' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware' via options '-lMapBizOCMiddleware' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'Masonry-iOS12.0' in project 'Masonry' via options '-lMasonry-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'PerfSight' in project 'PerfSight' via options '-lPerfSight' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'Protobuf' in project 'Protobuf' via options '-lProtobuf' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QCloudCOSXML' in project 'QCloudCOSXML' via options '-lQCloudCOSXML' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QCloudCore' in project 'QCloudCore' via options '-lQCloudCore' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMEncrypt' in project 'QMEncrypt' via options '-lQMEncrypt' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap' via options '-lQMExtraOrdinaryMap' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMResDownloader' in project 'QMResDownloader' via options '-lQMResDownloader' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMSpiderManDebugTool' in project 'QMSpiderManDebugTool' via options '-lQMSpiderManDebugTool' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper' via options '-lQMSpiderManWrapper' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP' via options '-lQMWUP-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics' via options '-lQMapBasics-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools' via options '-lQMapBuildTools-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode' via options '-lQMapDarkMode' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension' via options '-lQMapIntentsExtension' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapPerformanceTest' in project 'QMapPerformanceTest' via options '-lQMapPerformanceTest' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapSwiftBridge-iOS12.0' in project 'QMapSwiftBridge' via options '-lQMapSwiftBridge-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapVectorLayout' in project 'QMapVectorLayout' via options '-lQMapVectorLayout' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets' via options '-lQMapWidgets-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QPlayAutoSDK' in project 'QPlayAutoSDK' via options '-lQPlayAutoSDK' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QuickJS' in project 'QuickJS' via options '-lQuickJS' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'RaftKit' in project 'RaftKit' via options '-lRaftKit' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'RenderCore' in project 'RenderCore' via options '-lRenderCore' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware' via options '-lRouteGuidanceOCMiddleware' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage' via options '-lSDWebImage-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SDWebImageWebPCoder' in project 'SDWebImageWebPCoder' via options '-lSDWebImageWebPCoder' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SSZipArchive' in project 'SSZipArchive' via options '-lSSZipArchive' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SVProgressHUD' in project 'SVProgressHUD' via options '-lSVProgressHUD' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SocketRocket' in project 'SocketRocket' via options '-lSocketRocket' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'TAM_IOS_SDK' in project 'TAM_IOS_SDK' via options '-lTAM_IOS_SDK' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'TDFDebugBox' in project 'TDFDebugBox' via options '-lTDFDebugBox' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'TMAISound' in project 'TMAISound' via options '-lTMAISound' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper' via options '-lTMDylibLazyLoadWrapper' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'TMWeAppClient' in project 'TMWeAppClient' via options '-lTMWeAppClient' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'VectorLayout' in project 'VectorLayout' via options '-lVectorLayout' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'VectorLayoutExpression' in project 'VectorLayoutExpression' via options '-lVectorLayoutExpression' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'VectorLayoutReactivity' in project 'VectorLayoutReactivity' via options '-lVectorLayoutReactivity' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'YYCache' in project 'YYCache' via options '-lYYCache' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'YYImage' in project 'YYImage' via options '-lYYImage' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'YYModel' in project 'YYModel' via options '-lYYModel' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'YYText' in project 'YYText' via options '-lYYText' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'YYWebImage' in project 'YYWebImage' via options '-lYYWebImage' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'Yoga' in project 'Yoga' via options '-lYoga' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'hippy' in project 'hippy' via options '-lhippy' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'libwebp' in project 'libwebp' via options '-llibwebp' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'lottie-ios' in project 'lottie-ios' via options '-llottie-ios' in build setting 'OTHER_LDFLAGS'
Target 'Pods-TencentMap' in project 'Pods'
➜ Explicit dependency on target 'A4LoadMeasure' in project 'A4LoadMeasure'
➜ Explicit dependency on target 'A4QMStartTimeMonitor' in project 'A4QMStartTimeMonitor'
➜ Explicit dependency on target 'AISDK' in project 'AISDK'
➜ Explicit dependency on target 'AISound' in project 'AISound'
➜ Explicit dependency on target 'AgileMap' in project 'AgileMap'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'Aspects' in project 'Aspects'
➜ Explicit dependency on target 'AutoCoding' in project 'AutoCoding'
➜ Explicit dependency on target 'Beacon' in project 'Beacon'
➜ Explicit dependency on target 'Bugly' in project 'Bugly'
➜ Explicit dependency on target 'CharacterTextReplace' in project 'CharacterTextReplace'
➜ Explicit dependency on target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket'
➜ Explicit dependency on target 'CocoaHotReload' in project 'CocoaHotReload'
➜ Explicit dependency on target 'CocoaLumberjack' in project 'CocoaLumberjack'
➜ Explicit dependency on target 'ComponentKit' in project 'ComponentKit'
➜ Explicit dependency on target 'CrBase' in project 'CrBase'
➜ Explicit dependency on target 'DHSmartScreenshot' in project 'DHSmartScreenshot'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'DouyinOpenSDK' in project 'DouyinOpenSDK'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'DragonMapKitC2OC' in project 'DragonMapKitC2OC'
➜ Explicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet'
➜ Explicit dependency on target 'FBRetainCycleDetector' in project 'FBRetainCycleDetector'
➜ Explicit dependency on target 'FMDB' in project 'FMDB'
➜ Explicit dependency on target 'GCDWebServer' in project 'GCDWebServer'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'GLMapLibBusiness' in project 'GLMapLibBusiness'
➜ Explicit dependency on target 'GLMapLibCore' in project 'GLMapLibCore'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'GLMapLibThirdParty' in project 'GLMapLibThirdParty'
➜ Explicit dependency on target 'GT' in project 'GT'
➜ Explicit dependency on target 'GZIP' in project 'GZIP'
➜ Explicit dependency on target 'ITLogin' in project 'ITLogin'
➜ Explicit dependency on target 'KVOController' in project 'KVOController'
➜ Explicit dependency on target 'LarkLite' in project 'LarkLite'
➜ Explicit dependency on target 'LongLinkSDK' in project 'LongLinkSDK'
➜ Explicit dependency on target 'LookinServer' in project 'LookinServer'
➜ Explicit dependency on target 'MJExtension' in project 'MJExtension'
➜ Explicit dependency on target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'MMKVCore' in project 'MMKVCore'
➜ Explicit dependency on target 'MQQKCardKit' in project 'MQQKCardKit'
➜ Explicit dependency on target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MapBizNew' in project 'MapBizNew'
➜ Explicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'MapReflux' in project 'MapReflux'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'OlRoute' in project 'OlRoute'
➜ Explicit dependency on target 'PLog' in project 'PLog'
➜ Explicit dependency on target 'PMonitor' in project 'PMonitor'
➜ Explicit dependency on target 'PandoraEx' in project 'PandoraEx'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'PerfSight' in project 'PerfSight'
➜ Explicit dependency on target 'PoiEngine' in project 'PoiEngine'
➜ Explicit dependency on target 'PosEngine' in project 'PosEngine'
➜ Explicit dependency on target 'Protobuf' in project 'Protobuf'
➜ Explicit dependency on target 'QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QCloudCore' in project 'QCloudCore'
➜ Explicit dependency on target 'QMEncrypt' in project 'QMEncrypt'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMLibffi' in project 'QMLibffi'
➜ Explicit dependency on target 'QMResDownloader' in project 'QMResDownloader'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMSpiderManDebugTool' in project 'QMSpiderManDebugTool'
➜ Explicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
➜ Explicit dependency on target 'QMWeChatSDK' in project 'QMWeChatSDK'
➜ Explicit dependency on target 'QMapARNaviKit' in project 'QMapARNaviKit'
➜ Explicit dependency on target 'QMapBaseline' in project 'QMapBaseline'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapBusBusiness' in project 'QMapBusBusiness'
➜ Explicit dependency on target 'QMapBusKit' in project 'QMapBusKit'
➜ Explicit dependency on target 'QMapBusiness' in project 'QMapBusiness'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapInfoServiceKit' in project 'QMapInfoServiceKit'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapNaviKit' in project 'QMapNaviKit'
➜ Explicit dependency on target 'QMapPerformanceTest' in project 'QMapPerformanceTest'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapSwiftBridge-iOS12.0' in project 'QMapSwiftBridge'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapVectorLayout' in project 'QMapVectorLayout'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'QPlayAutoSDK' in project 'QPlayAutoSDK'
➜ Explicit dependency on target 'QRouteEnlargeLib' in project 'QRouteEnlargeLib'
➜ Explicit dependency on target 'QStreetView' in project 'QStreetView'
➜ Explicit dependency on target 'QimeiSDK' in project 'QimeiSDK'
➜ Explicit dependency on target 'QuickJS' in project 'QuickJS'
➜ Explicit dependency on target 'RAFTMeasure' in project 'RAFTMeasure'
➜ Explicit dependency on target 'RUM' in project 'RUM'
➜ Explicit dependency on target 'RaftKit' in project 'RaftKit'
➜ Explicit dependency on target 'RenderCore' in project 'RenderCore'
➜ Explicit dependency on target 'RouteGuidance' in project 'RouteGuidance'
➜ Explicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SDWebImageWebPCoder' in project 'SDWebImageWebPCoder'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
➜ Explicit dependency on target 'SVProgressHUD' in project 'SVProgressHUD'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
➜ Explicit dependency on target 'TAM_IOS_SDK' in project 'TAM_IOS_SDK'
➜ Explicit dependency on target 'TDFDebugBox' in project 'TDFDebugBox'
➜ Explicit dependency on target 'TMAISound' in project 'TMAISound'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'TMWeAppClient' in project 'TMWeAppClient'
➜ Explicit dependency on target 'TPNS-iOS' in project 'TPNS-iOS'
➜ Explicit dependency on target 'TXMapComLibForiOS' in project 'TXMapComLibForiOS'
➜ Explicit dependency on target 'TXMapView' in project 'TXMapView'
➜ Explicit dependency on target 'Tangram' in project 'Tangram'
➜ Explicit dependency on target 'TencentLBS' in project 'TencentLBS'
➜ Explicit dependency on target 'TencentOpenAPI' in project 'TencentOpenAPI'
➜ Explicit dependency on target 'TencentTravelService' in project 'TencentTravelService'
➜ Explicit dependency on target 'TuringShield' in project 'TuringShield'
➜ Explicit dependency on target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'VectorLayoutExpression' in project 'VectorLayoutExpression'
➜ Explicit dependency on target 'VectorLayoutReactivity' in project 'VectorLayoutReactivity'
➜ Explicit dependency on target 'WeAppSDK' in project 'WeAppSDK'
➜ Explicit dependency on target 'Weibo_SDK' in project 'Weibo_SDK'
➜ Explicit dependency on target 'XiaoHongShuOpenSDK' in project 'XiaoHongShuOpenSDK'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
➜ Explicit dependency on target 'YYText' in project 'YYText'
➜ Explicit dependency on target 'YYWebImage' in project 'YYWebImage'
➜ Explicit dependency on target 'Yoga' in project 'Yoga'
➜ Explicit dependency on target 'h5_poi' in project 'h5_poi'
➜ Explicit dependency on target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'hippy_accountManagerment' in project 'hippy_accountManagerment'
➜ Explicit dependency on target 'hippy_busTab' in project 'hippy_busTab'
➜ Explicit dependency on target 'hippy_cardList' in project 'hippy_cardList'
➜ Explicit dependency on target 'hippy_chauffeur' in project 'hippy_chauffeur'
➜ Explicit dependency on target 'hippy_customMap' in project 'hippy_customMap'
➜ Explicit dependency on target 'hippy_demo' in project 'hippy_demo'
➜ Explicit dependency on target 'hippy_devSetting' in project 'hippy_devSetting'
➜ Explicit dependency on target 'hippy_drive4Stop4' in project 'hippy_drive4Stop4'
➜ Explicit dependency on target 'hippy_favorite' in project 'hippy_favorite'
➜ Explicit dependency on target 'hippy_footprintMap' in project 'hippy_footprintMap'
➜ Explicit dependency on target 'hippy_futureEta' in project 'hippy_futureEta'
➜ Explicit dependency on target 'hippy_hermes' in project 'hippy_hermes'
➜ Explicit dependency on target 'hippy_hermes_full' in project 'hippy_hermes_full'
➜ Explicit dependency on target 'hippy_myContributions' in project 'hippy_myContributions'
➜ Explicit dependency on target 'hippy_myWallet' in project 'hippy_myWallet'
➜ Explicit dependency on target 'hippy_newsCenter' in project 'hippy_newsCenter'
➜ Explicit dependency on target 'hippy_order' in project 'hippy_order'
➜ Explicit dependency on target 'hippy_pandora' in project 'hippy_pandora'
➜ Explicit dependency on target 'hippy_parkingRecord' in project 'hippy_parkingRecord'
➜ Explicit dependency on target 'hippy_passCertificate' in project 'hippy_passCertificate'
➜ Explicit dependency on target 'hippy_personalCenter' in project 'hippy_personalCenter'
➜ Explicit dependency on target 'hippy_personalPoints' in project 'hippy_personalPoints'
➜ Explicit dependency on target 'hippy_poi' in project 'hippy_poi'
➜ Explicit dependency on target 'hippy_privacySetting' in project 'hippy_privacySetting'
➜ Explicit dependency on target 'hippy_realtimebus' in project 'hippy_realtimebus'
➜ Explicit dependency on target 'hippy_scenic' in project 'hippy_scenic'
➜ Explicit dependency on target 'hippy_sharePosition' in project 'hippy_sharePosition'
➜ Explicit dependency on target 'hippy_summary' in project 'hippy_summary'
➜ Explicit dependency on target 'hippy_taxi' in project 'hippy_taxi'
➜ Explicit dependency on target 'hippy_themeGround' in project 'hippy_themeGround'
➜ Explicit dependency on target 'hippy_threeDParticle' in project 'hippy_threeDParticle'
➜ Explicit dependency on target 'hippy_tm' in project 'hippy_tm'
➜ Explicit dependency on target 'hippy_tm_hbc' in project 'hippy_tm_hbc'
➜ Explicit dependency on target 'hippy_transformMap' in project 'hippy_transformMap'
➜ Explicit dependency on target 'hippy_travelMap' in project 'hippy_travelMap'
➜ Explicit dependency on target 'hippy_userPhoneBind' in project 'hippy_userPhoneBind'
➜ Explicit dependency on target 'lib-CoordTransfer' in project 'lib-CoordTransfer'
➜ Explicit dependency on target 'lib-TLBSiOSAr' in project 'lib-TLBSiOSAr'
➜ Explicit dependency on target 'lib-TencentLBSZip' in project 'lib-TencentLBSZip'
➜ Explicit dependency on target 'libpag' in project 'libpag'
➜ Explicit dependency on target 'libwebp' in project 'libwebp'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'mars' in project 'mars'
Target 'hippy_userPhoneBind' in project 'hippy_userPhoneBind'
➜ Explicit dependency on target 'hippy_userPhoneBind-hippy_userPhoneBind' in project 'hippy_userPhoneBind'
Target 'hippy_userPhoneBind-hippy_userPhoneBind' in project 'hippy_userPhoneBind' (no dependencies)
Target 'hippy_travelMap' in project 'hippy_travelMap'
➜ Explicit dependency on target 'hippy_travelMap-hippy_travelMap' in project 'hippy_travelMap'
Target 'hippy_travelMap-hippy_travelMap' in project 'hippy_travelMap' (no dependencies)
Target 'hippy_transformMap' in project 'hippy_transformMap'
➜ Explicit dependency on target 'hippy_transformMap-hippy_transformMap' in project 'hippy_transformMap'
Target 'hippy_transformMap-hippy_transformMap' in project 'hippy_transformMap' (no dependencies)
Target 'hippy_tm_hbc' in project 'hippy_tm_hbc'
➜ Explicit dependency on target 'hippy_tm_hbc-hippy_tm_hbc' in project 'hippy_tm_hbc'
Target 'hippy_tm_hbc-hippy_tm_hbc' in project 'hippy_tm_hbc' (no dependencies)
Target 'hippy_tm' in project 'hippy_tm'
➜ Explicit dependency on target 'hippy_tm-hippy_tm' in project 'hippy_tm'
Target 'hippy_tm-hippy_tm' in project 'hippy_tm' (no dependencies)
Target 'hippy_threeDParticle' in project 'hippy_threeDParticle'
➜ Explicit dependency on target 'hippy_threeDParticle-hippy_threeDParticle' in project 'hippy_threeDParticle'
Target 'hippy_threeDParticle-hippy_threeDParticle' in project 'hippy_threeDParticle' (no dependencies)
Target 'hippy_themeGround' in project 'hippy_themeGround'
➜ Explicit dependency on target 'hippy_themeGround-hippy_themeGround' in project 'hippy_themeGround'
Target 'hippy_themeGround-hippy_themeGround' in project 'hippy_themeGround' (no dependencies)
Target 'hippy_taxi' in project 'hippy_taxi'
➜ Explicit dependency on target 'hippy_taxi-hippy_taxi' in project 'hippy_taxi'
Target 'hippy_taxi-hippy_taxi' in project 'hippy_taxi' (no dependencies)
Target 'hippy_summary' in project 'hippy_summary'
➜ Explicit dependency on target 'hippy_summary-hippy_summary' in project 'hippy_summary'
Target 'hippy_summary-hippy_summary' in project 'hippy_summary' (no dependencies)
Target 'hippy_sharePosition' in project 'hippy_sharePosition'
➜ Explicit dependency on target 'hippy_sharePosition-hippy_sharePosition' in project 'hippy_sharePosition'
Target 'hippy_sharePosition-hippy_sharePosition' in project 'hippy_sharePosition' (no dependencies)
Target 'hippy_scenic' in project 'hippy_scenic'
➜ Explicit dependency on target 'hippy_scenic-hippy_scenic' in project 'hippy_scenic'
Target 'hippy_scenic-hippy_scenic' in project 'hippy_scenic' (no dependencies)
Target 'hippy_realtimebus' in project 'hippy_realtimebus'
➜ Explicit dependency on target 'hippy_realtimebus-hippy_realtimebus' in project 'hippy_realtimebus'
Target 'hippy_realtimebus-hippy_realtimebus' in project 'hippy_realtimebus' (no dependencies)
Target 'hippy_privacySetting' in project 'hippy_privacySetting'
➜ Explicit dependency on target 'hippy_privacySetting-hippy_privacySetting' in project 'hippy_privacySetting'
Target 'hippy_privacySetting-hippy_privacySetting' in project 'hippy_privacySetting' (no dependencies)
Target 'hippy_poi' in project 'hippy_poi'
➜ Explicit dependency on target 'hippy_poi-hippy_poi' in project 'hippy_poi'
Target 'hippy_poi-hippy_poi' in project 'hippy_poi' (no dependencies)
Target 'hippy_personalPoints' in project 'hippy_personalPoints'
➜ Explicit dependency on target 'hippy_personalPoints-hippy_personalPoints' in project 'hippy_personalPoints'
Target 'hippy_personalPoints-hippy_personalPoints' in project 'hippy_personalPoints' (no dependencies)
Target 'hippy_personalCenter' in project 'hippy_personalCenter'
➜ Explicit dependency on target 'hippy_personalCenter-hippy_personalCenter' in project 'hippy_personalCenter'
Target 'hippy_personalCenter-hippy_personalCenter' in project 'hippy_personalCenter' (no dependencies)
Target 'hippy_passCertificate' in project 'hippy_passCertificate'
➜ Explicit dependency on target 'hippy_passCertificate-hippy_passCertificate' in project 'hippy_passCertificate'
Target 'hippy_passCertificate-hippy_passCertificate' in project 'hippy_passCertificate' (no dependencies)
Target 'hippy_parkingRecord' in project 'hippy_parkingRecord'
➜ Explicit dependency on target 'hippy_parkingRecord-hippy_parkingRecord' in project 'hippy_parkingRecord'
Target 'hippy_parkingRecord-hippy_parkingRecord' in project 'hippy_parkingRecord' (no dependencies)
Target 'hippy_pandora' in project 'hippy_pandora'
➜ Explicit dependency on target 'hippy_pandora-hippy_pandora' in project 'hippy_pandora'
Target 'hippy_pandora-hippy_pandora' in project 'hippy_pandora' (no dependencies)
Target 'hippy_order' in project 'hippy_order'
➜ Explicit dependency on target 'hippy_order-hippy_order' in project 'hippy_order'
Target 'hippy_order-hippy_order' in project 'hippy_order' (no dependencies)
Target 'hippy_newsCenter' in project 'hippy_newsCenter'
➜ Explicit dependency on target 'hippy_newsCenter-hippy_newsCenter' in project 'hippy_newsCenter'
Target 'hippy_newsCenter-hippy_newsCenter' in project 'hippy_newsCenter' (no dependencies)
Target 'hippy_myWallet' in project 'hippy_myWallet'
➜ Explicit dependency on target 'hippy_myWallet-hippy_myWallet' in project 'hippy_myWallet'
Target 'hippy_myWallet-hippy_myWallet' in project 'hippy_myWallet' (no dependencies)
Target 'hippy_myContributions' in project 'hippy_myContributions'
➜ Explicit dependency on target 'hippy_myContributions-hippy_myContributions' in project 'hippy_myContributions'
Target 'hippy_myContributions-hippy_myContributions' in project 'hippy_myContributions' (no dependencies)
Target 'hippy_hermes' in project 'hippy_hermes' (no dependencies)
Target 'hippy_futureEta' in project 'hippy_futureEta'
➜ Explicit dependency on target 'hippy_futureEta-hippy_futureEta' in project 'hippy_futureEta'
Target 'hippy_futureEta-hippy_futureEta' in project 'hippy_futureEta' (no dependencies)
Target 'hippy_footprintMap' in project 'hippy_footprintMap'
➜ Explicit dependency on target 'hippy_footprintMap-hippy_footprintMap' in project 'hippy_footprintMap'
Target 'hippy_footprintMap-hippy_footprintMap' in project 'hippy_footprintMap' (no dependencies)
Target 'hippy_favorite' in project 'hippy_favorite'
➜ Explicit dependency on target 'hippy_favorite-hippy_favorite' in project 'hippy_favorite'
Target 'hippy_favorite-hippy_favorite' in project 'hippy_favorite' (no dependencies)
Target 'hippy_drive4Stop4' in project 'hippy_drive4Stop4'
➜ Explicit dependency on target 'hippy_drive4Stop4-hippy_drive4Stop4' in project 'hippy_drive4Stop4'
Target 'hippy_drive4Stop4-hippy_drive4Stop4' in project 'hippy_drive4Stop4' (no dependencies)
Target 'hippy_devSetting' in project 'hippy_devSetting'
➜ Explicit dependency on target 'hippy_devSetting-hippy_devSetting' in project 'hippy_devSetting'
Target 'hippy_devSetting-hippy_devSetting' in project 'hippy_devSetting' (no dependencies)
Target 'hippy_demo' in project 'hippy_demo'
➜ Explicit dependency on target 'hippy_demo-hippy_demo' in project 'hippy_demo'
Target 'hippy_demo-hippy_demo' in project 'hippy_demo' (no dependencies)
Target 'hippy_customMap' in project 'hippy_customMap'
➜ Explicit dependency on target 'hippy_customMap-hippy_customMap' in project 'hippy_customMap'
Target 'hippy_customMap-hippy_customMap' in project 'hippy_customMap' (no dependencies)
Target 'hippy_chauffeur' in project 'hippy_chauffeur'
➜ Explicit dependency on target 'hippy_chauffeur-hippy_chauffeur' in project 'hippy_chauffeur'
Target 'hippy_chauffeur-hippy_chauffeur' in project 'hippy_chauffeur' (no dependencies)
Target 'hippy_cardList' in project 'hippy_cardList'
➜ Explicit dependency on target 'hippy_cardList-hippy_cardList' in project 'hippy_cardList'
Target 'hippy_cardList-hippy_cardList' in project 'hippy_cardList' (no dependencies)
Target 'hippy_busTab' in project 'hippy_busTab'
➜ Explicit dependency on target 'hippy_busTab-hippy_busTab' in project 'hippy_busTab'
Target 'hippy_busTab-hippy_busTab' in project 'hippy_busTab' (no dependencies)
Target 'hippy_accountManagerment' in project 'hippy_accountManagerment'
➜ Explicit dependency on target 'hippy_accountManagerment-hippy_accountManagerment' in project 'hippy_accountManagerment'
Target 'hippy_accountManagerment-hippy_accountManagerment' in project 'hippy_accountManagerment' (no dependencies)
Target 'h5_poi' in project 'h5_poi' (no dependencies)
Target 'TDFDebugBox' in project 'TDFDebugBox' (no dependencies)
Target 'RaftKit' in project 'RaftKit'
➜ Explicit dependency on target 'Bugly' in project 'Bugly'
➜ Explicit dependency on target 'FBRetainCycleDetector' in project 'FBRetainCycleDetector'
➜ Explicit dependency on target 'hippy' in project 'hippy'
Target 'QMapPerformanceTest' in project 'QMapPerformanceTest'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'GT' in project 'GT'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapPerformanceTest-QMapPerformanceTestResources' in project 'QMapPerformanceTest'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
Target 'QMapPerformanceTest-QMapPerformanceTestResources' in project 'QMapPerformanceTest' (no dependencies)
Target 'QMapBusiness' in project 'QMapBusiness'
➜ Explicit dependency on target 'A4QMStartTimeMonitor' in project 'A4QMStartTimeMonitor'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'Aspects' in project 'Aspects'
➜ Explicit dependency on target 'AutoCoding' in project 'AutoCoding'
➜ Explicit dependency on target 'CharacterTextReplace' in project 'CharacterTextReplace'
➜ Explicit dependency on target 'DHSmartScreenshot' in project 'DHSmartScreenshot'
➜ Explicit dependency on target 'DouyinOpenSDK' in project 'DouyinOpenSDK'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MQQKCardKit' in project 'MQQKCardKit'
➜ Explicit dependency on target 'PerfSight' in project 'PerfSight'
➜ Explicit dependency on target 'QMapBaseline' in project 'QMapBaseline'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapBusBusiness' in project 'QMapBusBusiness'
➜ Explicit dependency on target 'QMapBusKit' in project 'QMapBusKit'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapInfoServiceKit' in project 'QMapInfoServiceKit'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapNaviKit' in project 'QMapNaviKit'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapVectorLayout' in project 'QMapVectorLayout'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QStreetView' in project 'QStreetView'
➜ Explicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'SVProgressHUD' in project 'SVProgressHUD'
➜ Explicit dependency on target 'TencentTravelService' in project 'TencentTravelService'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'TMWeAppClient' in project 'TMWeAppClient'
➜ Explicit dependency on target 'TPNS-iOS' in project 'TPNS-iOS'
➜ Explicit dependency on target 'Weibo_SDK' in project 'Weibo_SDK'
➜ Explicit dependency on target 'XiaoHongShuOpenSDK' in project 'XiaoHongShuOpenSDK'
Target 'XiaoHongShuOpenSDK' in project 'XiaoHongShuOpenSDK' (no dependencies)
Target 'Weibo_SDK' in project 'Weibo_SDK' (no dependencies)
Target 'TPNS-iOS' in project 'TPNS-iOS' (no dependencies)
Target 'QMapVectorLayout' in project 'QMapVectorLayout'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
➜ Explicit dependency on target 'libpag' in project 'libpag'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
Target 'QMapBusBusiness' in project 'QMapBusBusiness'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'CharacterTextReplace' in project 'CharacterTextReplace'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'QMapBusKit' in project 'QMapBusKit'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
➜ Explicit dependency on target 'QStreetView' in project 'QStreetView'
➜ Explicit dependency on target 'TencentTravelService' in project 'TencentTravelService'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
Target 'QMapBusKit' in project 'QMapBusKit'
➜ Explicit dependency on target 'AutoCoding' in project 'AutoCoding'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
Target 'QMapBaseline' in project 'QMapBaseline'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'AutoCoding' in project 'AutoCoding'
➜ Explicit dependency on target 'CharacterTextReplace' in project 'CharacterTextReplace'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapInfoServiceKit' in project 'QMapInfoServiceKit'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QStreetView' in project 'QStreetView'
➜ Explicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'SVProgressHUD' in project 'SVProgressHUD'
➜ Explicit dependency on target 'TencentTravelService' in project 'TencentTravelService'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
Target 'TencentTravelService' in project 'TencentTravelService'
➜ Explicit dependency on target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'QMapNaviKit' in project 'QMapNaviKit'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
Target 'QMapNaviKit' in project 'QMapNaviKit'
➜ Explicit dependency on target 'Aspects' in project 'Aspects'
➜ Explicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'KVOController' in project 'KVOController'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'QPlayAutoSDK' in project 'QPlayAutoSDK'
➜ Explicit dependency on target 'QRouteEnlargeLib' in project 'QRouteEnlargeLib'
➜ Explicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
➜ Explicit dependency on target 'YYText' in project 'YYText'
Target 'QRouteEnlargeLib' in project 'QRouteEnlargeLib' (no dependencies)
Target 'QPlayAutoSDK' in project 'QPlayAutoSDK'
➜ Explicit dependency on target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket'
Target 'QStreetView' in project 'QStreetView' (no dependencies)
Target 'QMapInfoServiceKit' in project 'QMapInfoServiceKit'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
Target 'QMapHippy' in project 'QMapHippy'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SVProgressHUD' in project 'SVProgressHUD'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'TuringShield' in project 'TuringShield'
➜ Explicit dependency on target 'YYText' in project 'YYText'
Target 'YYText' in project 'YYText' (no dependencies)
Target 'TuringShield' in project 'TuringShield' (no dependencies)
Target 'SVProgressHUD' in project 'SVProgressHUD' (no dependencies)
Target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket'
➜ Explicit dependency on target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet'
➜ Explicit dependency on target 'GCDWebServer' in project 'GCDWebServer'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'libpag' in project 'libpag'
➜ Explicit dependency on target 'LongLinkSDK' in project 'LongLinkSDK'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'MQQKCardKit' in project 'MQQKCardKit'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapSwiftBridge-iOS12.0' in project 'QMapSwiftBridge'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMSpiderManDebugTool' in project 'QMSpiderManDebugTool'
➜ Explicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
➜ Explicit dependency on target 'QMWeChatSDK' in project 'QMWeChatSDK'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
➜ Explicit dependency on target 'Tangram' in project 'Tangram'
➜ Explicit dependency on target 'TencentOpenAPI' in project 'TencentOpenAPI'
➜ Explicit dependency on target 'TMAISound' in project 'TMAISound'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'TMWeAppClient' in project 'TMWeAppClient'
➜ Explicit dependency on target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
➜ Explicit dependency on target 'YYWebImage' in project 'YYWebImage'
Target 'YYWebImage' in project 'YYWebImage'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
Target 'YYImage' in project 'YYImage' (no dependencies)
Target 'TMWeAppClient' in project 'TMWeAppClient'
➜ Explicit dependency on target 'WeAppSDK' in project 'WeAppSDK'
Target 'WeAppSDK' in project 'WeAppSDK' (no dependencies)
Target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'AISDK' in project 'AISDK'
➜ Explicit dependency on target 'LarkLite' in project 'LarkLite'
➜ Explicit dependency on target 'QMapARNaviKit' in project 'QMapARNaviKit'
Target 'TMAISound' in project 'TMAISound'
➜ Explicit dependency on target 'AISound' in project 'AISound'
Target 'TencentOpenAPI' in project 'TencentOpenAPI' (no dependencies)
Target 'Tangram' in project 'Tangram' (no dependencies)
Target 'QMapSwiftBridge-iOS12.0' in project 'QMapSwiftBridge' (no dependencies)
Target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
Target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
Target 'hippy' in project 'hippy'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'hippy-HippyBundle' in project 'hippy'
➜ Explicit dependency on target 'hippy_hermes_full' in project 'hippy_hermes_full'
Target 'hippy_hermes_full' in project 'hippy_hermes_full' (no dependencies)
Target 'hippy-HippyBundle' in project 'hippy' (no dependencies)
Target 'QMapARNaviKit' in project 'QMapARNaviKit' (no dependencies)
Target 'QMWeChatSDK' in project 'QMWeChatSDK' (no dependencies)
Target 'QMSpiderManDebugTool' in project 'QMSpiderManDebugTool'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
Target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
Target 'QMSpiderMan' in project 'QMSpiderMan' (no dependencies)
Target 'QMResDownloader' in project 'QMResDownloader' (no dependencies)
Target 'PerfSight' in project 'PerfSight' (no dependencies)
Target 'PMonitor' in project 'PMonitor'
➜ Explicit dependency on target 'PandoraEx' in project 'PandoraEx'
➜ Explicit dependency on target 'RAFTMeasure' in project 'RAFTMeasure'
Target 'RAFTMeasure' in project 'RAFTMeasure' (no dependencies)
Target 'PandoraEx' in project 'PandoraEx' (no dependencies)
Target 'MQQKCardKit' in project 'MQQKCardKit' (no dependencies)
Target 'LookinServer' in project 'LookinServer' (no dependencies)
Target 'LongLinkSDK' in project 'LongLinkSDK'
➜ Explicit dependency on target 'GZIP' in project 'GZIP'
➜ Explicit dependency on target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'Protobuf' in project 'Protobuf'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
Target 'LarkLite' in project 'LarkLite' (no dependencies)
Target 'ITLogin' in project 'ITLogin' (no dependencies)
Target 'GT' in project 'GT' (no dependencies)
Target 'GCDWebServer' in project 'GCDWebServer' (no dependencies)
Target 'FBRetainCycleDetector' in project 'FBRetainCycleDetector' (no dependencies)
Target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'DragonMapKit-DragonMapKitBundle' in project 'DragonMapKit'
➜ Explicit dependency on target 'DragonMapKit-QQMapRes' in project 'DragonMapKit'
➜ Explicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet'
➜ Explicit dependency on target 'FMDB' in project 'FMDB'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMLibffi' in project 'QMLibffi'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
➜ Explicit dependency on target 'TXMapComLibForiOS' in project 'TXMapComLibForiOS'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'libpag' in project 'libpag'
Target 'YYCache' in project 'YYCache' (no dependencies)
Target 'TXMapComLibForiOS' in project 'TXMapComLibForiOS' (no dependencies)
Target 'QMLibffi' in project 'QMLibffi' (no dependencies)
Target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMExtraOrdinaryMap-QMExtraOrdinaryMapResources' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
➜ Explicit dependency on target 'libpag' in project 'libpag'
Target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'ComponentKit' in project 'ComponentKit'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QuickJS' in project 'QuickJS'
➜ Explicit dependency on target 'RenderCore' in project 'RenderCore'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'VectorLayoutExpression' in project 'VectorLayoutExpression'
➜ Explicit dependency on target 'VectorLayoutReactivity' in project 'VectorLayoutReactivity'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
➜ Explicit dependency on target 'libpag' in project 'libpag'
Target 'VectorLayoutReactivity' in project 'VectorLayoutReactivity' (no dependencies)
Target 'VectorLayoutExpression' in project 'VectorLayoutExpression' (no dependencies)
Target 'QuickJS' in project 'QuickJS' (no dependencies)
Target 'QMExtraOrdinaryMap-QMExtraOrdinaryMapResources' in project 'QMExtraOrdinaryMap' (no dependencies)
Target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MapBizOCMiddleware-MapBizOCMiddlewareRes' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
Target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'Beacon' in project 'Beacon'
➜ Explicit dependency on target 'CocoaLumberjack' in project 'CocoaLumberjack'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'FMDB' in project 'FMDB'
➜ Explicit dependency on target 'GZIP' in project 'GZIP'
➜ Explicit dependency on target 'KVOController' in project 'KVOController'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'mars' in project 'mars'
➜ Explicit dependency on target 'MJExtension' in project 'MJExtension'
➜ Explicit dependency on target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'QMEncrypt' in project 'QMEncrypt'
➜ Explicit dependency on target 'RUM' in project 'RUM'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
➜ Explicit dependency on target 'TAM_IOS_SDK' in project 'TAM_IOS_SDK'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
Target 'TAM_IOS_SDK' in project 'TAM_IOS_SDK' (no dependencies)
Target 'RUM' in project 'RUM'
➜ Explicit dependency on target 'RUM-QAPM_cer' in project 'RUM'
Target 'RUM-QAPM_cer' in project 'RUM' (no dependencies)
Target 'QMEncrypt' in project 'QMEncrypt' (no dependencies)
Target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'libpag' in project 'libpag'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
Target 'lottie-ios' in project 'lottie-ios' (no dependencies)
Target 'libpag' in project 'libpag' (no dependencies)
Target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
Target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
Target 'QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QCloudCOSXML-QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QCloudCore' in project 'QCloudCore'
Target 'QCloudCore' in project 'QCloudCore' (no dependencies)
Target 'QCloudCOSXML-QCloudCOSXML' in project 'QCloudCOSXML' (no dependencies)
Target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
Target 'SocketRocket' in project 'SocketRocket' (no dependencies)
Target 'MJExtension' in project 'MJExtension' (no dependencies)
Target 'mars' in project 'mars' (no dependencies)
Target 'KVOController' in project 'KVOController' (no dependencies)
Target 'GZIP' in project 'GZIP' (no dependencies)
Target 'MapBizOCMiddleware-MapBizOCMiddlewareRes' in project 'MapBizOCMiddleware' (no dependencies)
Target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
Target 'YYModel' in project 'YYModel' (no dependencies)
Target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'AgileMap' in project 'AgileMap'
➜ Explicit dependency on target 'DragonMapKitC2OC' in project 'DragonMapKitC2OC'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'GLMapLibBusiness' in project 'GLMapLibBusiness'
➜ Explicit dependency on target 'GLMapLibCore' in project 'GLMapLibCore'
➜ Explicit dependency on target 'GLMapLibThirdParty' in project 'GLMapLibThirdParty'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'MapBizNew' in project 'MapBizNew'
➜ Explicit dependency on target 'MapReflux' in project 'MapReflux'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'OlRoute' in project 'OlRoute'
➜ Explicit dependency on target 'PLog' in project 'PLog'
➜ Explicit dependency on target 'PoiEngine' in project 'PoiEngine'
➜ Explicit dependency on target 'PosEngine' in project 'PosEngine'
➜ Explicit dependency on target 'RouteGuidance' in project 'RouteGuidance'
➜ Explicit dependency on target 'TencentLBS' in project 'TencentLBS'
➜ Explicit dependency on target 'TXMapView' in project 'TXMapView'
Target 'TencentLBS' in project 'TencentLBS'
➜ Explicit dependency on target 'CrBase' in project 'CrBase'
➜ Explicit dependency on target 'lib-CoordTransfer' in project 'lib-CoordTransfer'
➜ Explicit dependency on target 'lib-TencentLBSZip' in project 'lib-TencentLBSZip'
➜ Explicit dependency on target 'lib-TLBSiOSAr' in project 'lib-TLBSiOSAr'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'OlRoute' in project 'OlRoute'
➜ Explicit dependency on target 'PosEngine' in project 'PosEngine'
Target 'lib-TLBSiOSAr' in project 'lib-TLBSiOSAr' (no dependencies)
Target 'lib-TencentLBSZip' in project 'lib-TencentLBSZip' (no dependencies)
Target 'lib-CoordTransfer' in project 'lib-CoordTransfer' (no dependencies)
Target 'RouteGuidance' in project 'RouteGuidance'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'PosEngine' in project 'PosEngine'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
Target 'PoiEngine' in project 'PoiEngine' (no dependencies)
Target 'OlRoute' in project 'OlRoute' (no dependencies)
Target 'MapReflux' in project 'MapReflux'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'MapBizNew' in project 'MapBizNew'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'GLMapLibThirdParty' in project 'GLMapLibThirdParty' (no dependencies)
Target 'GLMapLibCore' in project 'GLMapLibCore' (no dependencies)
Target 'GLMapLibBusiness' in project 'GLMapLibBusiness' (no dependencies)
Target 'DragonMapKitC2OC' in project 'DragonMapKitC2OC'
➜ Explicit dependency on target 'TXMapView' in project 'TXMapView'
Target 'TXMapView' in project 'TXMapView'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'PLog' in project 'PLog'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SDWebImageWebPCoder' in project 'SDWebImageWebPCoder'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
Target 'SSZipArchive' in project 'SSZipArchive' (no dependencies)
Target 'SDWebImageWebPCoder' in project 'SDWebImageWebPCoder'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'libwebp' in project 'libwebp'
Target 'libwebp' in project 'libwebp' (no dependencies)
Target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'GLMapLibHeaders' in project 'GLMapLibHeaders' (no dependencies)
Target 'FMDB' in project 'FMDB' (no dependencies)
Target 'EDSunriseSet' in project 'EDSunriseSet' (no dependencies)
Target 'DragonMapKit-QQMapRes' in project 'DragonMapKit' (no dependencies)
Target 'DragonMapKit-DragonMapKitBundle' in project 'DragonMapKit' (no dependencies)
Target 'DouyinOpenSDK' in project 'DouyinOpenSDK' (no dependencies)
Target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
Target 'DKMobile' in project 'DKMobile' (no dependencies)
Target 'DHSmartScreenshot' in project 'DHSmartScreenshot' (no dependencies)
Target 'CrBase' in project 'CrBase' (no dependencies)
Target 'ComponentKit' in project 'ComponentKit'
➜ Explicit dependency on target 'RenderCore' in project 'RenderCore'
➜ Explicit dependency on target 'Yoga' in project 'Yoga'
Target 'Yoga' in project 'Yoga' (no dependencies)
Target 'RenderCore' in project 'RenderCore' (no dependencies)
Target 'CocoaLumberjack' in project 'CocoaLumberjack' (no dependencies)
Target 'CocoaHotReload' in project 'CocoaHotReload' (no dependencies)
Target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket' (no dependencies)
Target 'CharacterTextReplace' in project 'CharacterTextReplace' (no dependencies)
Target 'Bugly' in project 'Bugly' (no dependencies)
Target 'Beacon' in project 'Beacon'
➜ Explicit dependency on target 'QimeiSDK' in project 'QimeiSDK'
Target 'QimeiSDK' in project 'QimeiSDK' (no dependencies)
Target 'AutoCoding' in project 'AutoCoding' (no dependencies)
Target 'Aspects' in project 'Aspects' (no dependencies)
Target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'Protobuf' in project 'Protobuf'
Target 'Protobuf' in project 'Protobuf' (no dependencies)
Target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'MMKVCore' in project 'MMKVCore'
Target 'MMKVCore' in project 'MMKVCore' (no dependencies)
Target 'AgileMap' in project 'AgileMap'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'PLog' in project 'PLog' (no dependencies)
Target 'MapBaseNew' in project 'MapBaseNew' (no dependencies)
Target 'AISound' in project 'AISound' (no dependencies)
Target 'AISDK' in project 'AISDK' (no dependencies)
Target 'A4QMStartTimeMonitor' in project 'A4QMStartTimeMonitor' (no dependencies)
Target 'A4LoadMeasure' in project 'A4LoadMeasure' (no dependencies)
Target 'QMWidgetExtension' in project 'TencentMap'
➜ Implicit dependency on target 'Pods-QMWidgetExtension' in project 'Pods' via file 'libPods-QMWidgetExtension.a' in build phase 'Link Binary'
➜ Implicit dependency on target 'LiteRouteSearch-iOS14.0' in project 'LiteRouteSearch' via options '-lLiteRouteSearch-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'Masonry-iOS14.0' in project 'Masonry' via options '-lMasonry-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMWUP-iOS14.0' in project 'QMWUP' via options '-lQMWUP-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMWidgetKit' in project 'QMWidgetKit' via options '-lQMWidgetKit' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapBasics-iOS14.0' in project 'QMapBasics' via options '-lQMapBasics-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapBuildTools-iOS14.0' in project 'QMapBuildTools' via options '-lQMapBuildTools-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapSwiftBridge-iOS14.0' in project 'QMapSwiftBridge' via options '-lQMapSwiftBridge-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapWidgets-iOS14.0' in project 'QMapWidgets' via options '-lQMapWidgets-iOS14.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SDWebImage-iOS14.0' in project 'SDWebImage' via options '-lSDWebImage-iOS14.0' in build setting 'OTHER_LDFLAGS'
Target 'Pods-QMWidgetExtension' in project 'Pods'
➜ Explicit dependency on target 'LiteRouteSearch-iOS14.0' in project 'LiteRouteSearch'
➜ Explicit dependency on target 'Masonry-iOS14.0' in project 'Masonry'
➜ Explicit dependency on target 'QMWUP-iOS14.0' in project 'QMWUP'
➜ Explicit dependency on target 'QMWidgetKit' in project 'QMWidgetKit'
➜ Explicit dependency on target 'QMapBasics-iOS14.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS14.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapProto-iOS14.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapSwiftBridge-iOS14.0' in project 'QMapSwiftBridge'
➜ Explicit dependency on target 'QMapWidgets-iOS14.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS14.0' in project 'SDWebImage'
Target 'QMWidgetKit' in project 'QMWidgetKit'
➜ Explicit dependency on target 'LiteRouteSearch-iOS14.0' in project 'LiteRouteSearch'
➜ Explicit dependency on target 'QMapSwiftBridge-iOS14.0' in project 'QMapSwiftBridge'
Target 'QMapSwiftBridge-iOS14.0' in project 'QMapSwiftBridge' (no dependencies)
Target 'LiteRouteSearch-iOS14.0' in project 'LiteRouteSearch'
➜ Explicit dependency on target 'QMWUP-iOS14.0' in project 'QMWUP'
➜ Explicit dependency on target 'QMapBasics-iOS14.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS14.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapProto-iOS14.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapWidgets-iOS14.0' in project 'QMapWidgets'
Target 'QMapWidgets-iOS14.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'Masonry-iOS14.0' in project 'Masonry'
➜ Explicit dependency on target 'QMapBasics-iOS14.0' in project 'QMapBasics'
➜ Explicit dependency on target 'SDWebImage-iOS14.0' in project 'SDWebImage'
Target 'SDWebImage-iOS14.0' in project 'SDWebImage' (no dependencies)
Target 'Masonry-iOS14.0' in project 'Masonry' (no dependencies)
Target 'QMapProto-iOS14.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMWUP-iOS14.0' in project 'QMWUP'
Target 'QMapBuildTools-iOS14.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapBasics-iOS14.0' in project 'QMapBasics'
Target 'QMapBasics-iOS14.0' in project 'QMapBasics' (no dependencies)
Target 'QMWUP-iOS14.0' in project 'QMWUP' (no dependencies)
Target 'QMIntents' in project 'TencentMap'
➜ Explicit dependency on target 'BuildInfo' in project 'TencentMap'
➜ Implicit dependency on target 'Pods-QMIntents' in project 'Pods' via file 'libPods-QMIntents.a' in build phase 'Link Binary'
➜ Implicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension' via options '-lQMapIntentsExtension' in build setting 'OTHER_LDFLAGS'
Target 'Pods-QMIntents' in project 'Pods'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
Target 'QMIntentsUI' in project 'TencentMap'
➜ Explicit dependency on target 'BuildInfo' in project 'TencentMap'
➜ Implicit dependency on target 'Pods-QMIntents-QMIntentsUI' in project 'Pods' via file 'libPods-QMIntents-QMIntentsUI.a' in build phase 'Link Binary'
➜ Implicit dependency on target 'LiteRouteSearch-iOS12.0' in project 'LiteRouteSearch' via options '-lLiteRouteSearch-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'Masonry-iOS12.0' in project 'Masonry' via options '-lMasonry-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP' via options '-lQMWUP-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics' via options '-lQMapBasics-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools' via options '-lQMapBuildTools-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension' via options '-lQMapIntentsExtension' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapIntentsUIExtension' in project 'QMapIntentsUIExtension' via options '-lQMapIntentsUIExtension' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets' via options '-lQMapWidgets-iOS12.0' in build setting 'OTHER_LDFLAGS'
➜ Implicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage' via options '-lSDWebImage-iOS12.0' in build setting 'OTHER_LDFLAGS'
Target 'Pods-QMIntents-QMIntentsUI' in project 'Pods'
➜ Explicit dependency on target 'LiteRouteSearch-iOS12.0' in project 'LiteRouteSearch'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
➜ Explicit dependency on target 'QMapIntentsUIExtension' in project 'QMapIntentsUIExtension'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
Target 'QMapIntentsUIExtension' in project 'QMapIntentsUIExtension'
➜ Explicit dependency on target 'LiteRouteSearch-iOS12.0' in project 'LiteRouteSearch'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapIntentsExtension' in project 'QMapIntentsExtension'
Target 'QMapIntentsExtension' in project 'QMapIntentsExtension' (no dependencies)
Target 'LiteRouteSearch-iOS12.0' in project 'LiteRouteSearch'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
Target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
Target 'SDWebImage-iOS12.0' in project 'SDWebImage' (no dependencies)
Target 'Masonry-iOS12.0' in project 'Masonry' (no dependencies)
Target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
Target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
Target 'QMapBasics-iOS12.0' in project 'QMapBasics' (no dependencies)
Target 'QMWUP-iOS12.0' in project 'QMWUP' (no dependencies)
Target 'QMNotificationService' in project 'TencentMap'
➜ Explicit dependency on target 'BuildInfo' in project 'TencentMap'
➜ Implicit dependency on target 'Pods-QMNotificationService' in project 'Pods' via file 'libPods-QMNotificationService.a' in build phase 'Link Binary'
➜ Implicit dependency on target 'QMapNotificationServiceExtension' in project 'QMapNotificationServiceExtension' via options '-lQMapNotificationServiceExtension' in build setting 'OTHER_LDFLAGS'
Target 'Pods-QMNotificationService' in project 'Pods'
➜ Explicit dependency on target 'QMapNotificationServiceExtension' in project 'QMapNotificationServiceExtension'
Target 'QMapNotificationServiceExtension' in project 'QMapNotificationServiceExtension' (no dependencies)
Target 'BuildInfo' in project 'TencentMap' (no dependencies)