{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "a859d1d342b9df48666caf0b74a56c376d7cf4880c121de07bb887af928e1687"}, {"guid": "a859d1d342b9df48666caf0b74a56c37390d912abbe3e7e4b5872ac7f6610ab8"}], "containerPath": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap.xcworkspace", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "undefined_arch", "activeRunDestination": {"disableOnlyActiveArch": true, "platform": "iphoneos", "sdk": "iphoneos18.1", "sdkVariant": "iphoneos", "supportedArchitectures": ["armv4t", "armv5", "armv6", "armv7", "armv7f", "armv7s", "armv7k", "arm64", "arm64e"], "targetArchitecture": "undefined_arch"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {"CODE_SIGN_IDENTITY": "", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_REQUIRED": "NO", "PROVISIONING_PROFILE": "", "SDKROOT": "iphoneos18.1"}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}