-target arm64-apple-ios12.0 '-std=c++17' '-stdlib=libc++' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DHIPPY_VERSION=*******' '-DJS_HERMES=1' '-DJS_JSC=1' '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DLAYOUT_ENGINE_TAITANK=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/hippy-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/hippy-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/hippy-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy-ddc9e178894d483363db4ecabaed93c3-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/hippy-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/hippy/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/dom/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/footstone -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/footstone/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/driver/js/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/vfs/native/include -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/hippy.build/Debug-iphoneos/hippy.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/hippy -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy_hermes_full/Full/destroot/Library/Frameworks/universal -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/hippy_hermes_full