-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/ApolloSDK-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/ApolloSDK-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/ApolloSDK-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK-77a71738d4b72a2137e4ce491fe89876-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/ApolloSDK-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/ApolloSDK/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/ApolloSDK.build/Debug-iphoneos/ApolloSDK.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/ApolloSDK