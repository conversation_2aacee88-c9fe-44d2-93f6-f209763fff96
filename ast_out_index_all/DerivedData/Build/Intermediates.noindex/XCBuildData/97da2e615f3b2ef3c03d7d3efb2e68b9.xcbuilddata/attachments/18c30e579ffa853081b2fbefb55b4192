-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fobjc-weak -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fapplication-extension -fpascal-strings -O0 -fno-common '-DDEBUG=1' '-DCHANNEL_DEBUG=1' '-DDEBUG_TOOLS_ENABLED=1' '-DCOCOAPODS=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/QMNotificationService-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/QMNotificationService-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/QMNotificationService-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/TencentMap-a859d1d342b9df48666caf0b74a56c37-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/QMNotificationService-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/A4LoadMeasure -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Aspects -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/AutoCoding -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CharacterTextReplace -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaAsyncSocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaHotReload -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaLumberjack -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DHSmartScreenshot -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/EDSunriseSet -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FBRetainCycleDetector -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FMDB -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GCDWebServer -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GLMapLibHeaders -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GT -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GZIP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/KVOController -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LiteRouteSearch -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LongLinkSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LookinServer -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MJExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MQTTClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBaseOCModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBizOCMiddleware -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/PerfSight -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCOSXML -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMExtraOrdinaryMap -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMLibffi -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMResDownloader -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManDebugTool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWeChatSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWidgetKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapFoundation -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsUIExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapJCE -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapNotificationServiceExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapPerformanceTest -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapVectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QPlayAutoSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QRouteEnlargeLib -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QStreetView -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QuickJS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RaftKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RouteGuidanceOCMiddleware -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SVProgressHUD -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SocketRocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TDFDebugBox -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMAISound -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMDylibLazyLoadWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMWeAppClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TPNS-iOS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Tangram -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TuringShield -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutExpression -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutReactivity -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Weibo_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYCache -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYText -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TLBSiOSAr -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TencentLBSZip -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lottie-ios -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMNotificationService.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/../Pods/Headers/Public -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/../Pods/Headers/Public/QMapNotificationServiceExtension