-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/QMapPerformanceTest-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/QMapPerformanceTest-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/QMapPerformanceTest-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest-264ac95330d91fdf92895d9e0d624b37-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/QMapPerformanceTest-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapPerformanceTest/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapPerformanceTest -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaAsyncSocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaLumberjack -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/EDSunriseSet -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FMDB -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GCDWebServer -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GLMapLibHeaders -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GT -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GZIP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/KVOController -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LongLinkSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MJExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MQTTClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBaseOCModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBizOCMiddleware -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCOSXML -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMExtraOrdinaryMap -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMLibffi -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManDebugTool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWeChatSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapFoundation -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapJCE -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapPerformanceTest -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QuickJS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RouteGuidanceOCMiddleware -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SocketRocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMAISound -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMDylibLazyLoadWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMWeAppClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Tangram -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutExpression -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutReactivity -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYCache -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TLBSiOSAr -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TencentLBSZip -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lottie-ios -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMExtraOrdinaryMap/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapFoundation/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapMiddlePlatform/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapRouteSearchKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapUIKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapPerformanceTest.build/Debug-iphoneos/QMapPerformanceTest.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapPerformanceTest -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/AgileMap -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Beacon/beacon-frameworks/BeaconAPI_Base -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CrBase -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibCore -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GT/GT/Library -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQQKCardKit/MQQKCardKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBizNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapReflux -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/OlRoute -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PanguIosShell -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PoiEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PosEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/RenderEngine/lib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMSpiderMan -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapFoundation/QMapFoundation -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapMiddlePlatform/QMapMiddlePlatform -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapRouteSearchKit/QMapRouteSearchKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapUIKit/QMapUIKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QimeiSDK -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RUM -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidance -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentLBS -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentOpenAPI/TencentOpenAPI -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libpag/framework -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/mars/mars -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/AgileMap -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/CrBase -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLib -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibCore -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBaseNew -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBizNew -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapReflux -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/NerdApi -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/OlRoute -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PLog -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PanguIosShell -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PoiEngine -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PosEngine -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QimeiSDK -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/RouteGuidance -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TXMapView -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TencentLBS -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/libpag '-fmodule-map-file=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapSwiftBridge-iOS12.0/QMapSwiftBridge.modulemap' '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap'