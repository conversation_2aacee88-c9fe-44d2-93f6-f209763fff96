-target arm64-apple-ios12.0 '-std=gnu++14' '-stdlib=libc++' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DVL_DEBUGGER=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/VectorLayout-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/VectorLayout-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/VectorLayout-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout-88d9aabc30d7e402167e768359325c67-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/VectorLayout-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/VectorLayout/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QuickJS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutExpression -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutReactivity -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/VectorLayout.build/Debug-iphoneos/VectorLayout.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/VectorLayout -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libpag/framework -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/libpag