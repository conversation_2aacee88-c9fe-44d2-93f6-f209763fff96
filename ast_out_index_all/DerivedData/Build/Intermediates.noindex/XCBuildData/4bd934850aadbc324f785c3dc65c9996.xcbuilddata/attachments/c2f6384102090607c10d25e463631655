-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' -DUSE_ZLIB '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DGPB_USE_PROTOBUF_FRAMEWORK_IMPORTS=1' '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DSD_WEBP=1' '-DVL_DEBUGGER=1' '-DVL_DEBUGGER=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/Pods-TencentMap-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/Pods-TencentMap-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/Pods-TencentMap-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-8699adb1dd336b26511df848a716bd42-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/Pods-TencentMap-project-headers.hmap -iquote /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libwebp/src -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/A4LoadMeasure -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Aspects -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/AutoCoding -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CharacterTextReplace -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaAsyncSocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaHotReload -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaLumberjack -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DHSmartScreenshot -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/EDSunriseSet -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FBRetainCycleDetector -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FMDB -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GCDWebServer -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GLMapLibHeaders -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GT -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GZIP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/KVOController -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LiteRouteSearch -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LongLinkSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LookinServer -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MJExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MQTTClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBaseOCModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBizOCMiddleware -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/PerfSight -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCOSXML -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMExtraOrdinaryMap -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMLibffi -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMResDownloader -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManDebugTool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWeChatSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWidgetKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapFoundation -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsUIExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapJCE -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapNotificationServiceExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapPerformanceTest -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapVectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QPlayAutoSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QRouteEnlargeLib -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QStreetView -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QuickJS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RaftKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RouteGuidanceOCMiddleware -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SVProgressHUD -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SocketRocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TDFDebugBox -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMAISound -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMDylibLazyLoadWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMWeAppClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TPNS-iOS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Tangram -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TuringShield -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutExpression -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutReactivity -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Weibo_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYCache -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYText -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TLBSiOSAr -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TencentLBSZip -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lottie-ios -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMExtraOrdinaryMap/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapBaseline/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapBusBusiness/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapBusKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapBusiness/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapFoundation/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapHippy/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapInfoServiceKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapMiddlePlatform/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapNaviKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapRouteSearchKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QMapUIKit/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/RaftKit/Core/Headers -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TencentTravelService/Headers -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Pods/PLog/PLog.xcframework/ios-arm64_arm64e/PLog.framework/PrivateHeaders -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/Pods.build/Debug-iphoneos/Pods-TencentMap.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/A4LoadMeasure -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/A4QMStartTimeMonitor -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/AgileMap -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Beacon/beacon-frameworks/BeaconAPI_Base -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CrBase -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DouyinOpenSDK -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibCore -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GT/GT/Library -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ITLogin/ITLogin -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQQKCardKit/MQQKCardKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBizNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapReflux -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/OlRoute -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PMonitor/PMonitor/framework_PMonitor -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PandoraEx/PandoraEx/framework_PandoraEx -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PanguIosShell -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PoiEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PosEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/RenderEngine/lib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMSpiderMan -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBaseline/QMapBaseline -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBusBusiness/QMapBusBusiness -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBusKit/QMapBusKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBusiness/QMapBusiness -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapFoundation/QMapFoundation -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapHippy/QMapHippy -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapInfoServiceKit/QMapInfoServiceKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapMiddlePlatform/QMapMiddlePlatform -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapNaviKit/QMapNaviKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapRouteSearchKit/QMapRouteSearchKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapUIKit/QMapUIKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QimeiSDK -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RAFTMeasure/RAFTMeasure/framework_RAFTMeasure -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RUM -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RaftKit/RaftKit/Core -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidance -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TPNS-iOS/XGVIPPush -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TPNS-iOS/XGVIPPush/InAppMessage -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentLBS -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentOpenAPI/TencentOpenAPI -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentTravelService/TencentTravelService -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/XiaoHongShuOpenSDK/XiaoHongShuOpenSDK -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy_hermes_full/Full/destroot/Library/Frameworks/universal -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libpag/framework -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/mars/mars -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/A4QMStartTimeMonitor -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/AgileMap -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/Bugly -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/CrBase -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLib -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibCore -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/ITLogin -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBaseNew -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBizNew -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapReflux -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/NerdApi -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/OlRoute -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PLog -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PMonitor -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PandoraEx -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PanguIosShell -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PoiEngine -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PosEngine -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QimeiSDK -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/RouteGuidance -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TXMapView -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TencentLBS -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/XiaoHongShuOpenSDK -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/hippy_hermes_full -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/libpag '-fmodule-map-file=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapSwiftBridge-iOS12.0/QMapSwiftBridge.modulemap' '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap' -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsExtension -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/A4LoadMeasure -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Aspects -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/AutoCoding -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CharacterTextReplace -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaAsyncSocket -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaHotReload -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaLumberjack -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DHSmartScreenshot -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/EDSunriseSet -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FBRetainCycleDetector -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FMDB -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GCDWebServer -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GLMapLibHeaders -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GT -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GZIP -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/KVOController -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LongLinkSDK -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MQTTClient -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SocketRocket -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LookinServer -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MJExtension -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TLBSiOSAr -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TencentLBSZip -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/PerfSight -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCOSXML -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCore -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBaseOCModel -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMExtraOrdinaryMap -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapFoundation -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapJCE -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QuickJS -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayout -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutExpression -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutReactivity -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lottie-ios -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMLibffi -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMResDownloader -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManDebugTool -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManWrapper -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWeChatSDK -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKit -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBizOCMiddleware -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QPlayAutoSDK -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QRouteEnlargeLib -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QStreetView -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RouteGuidanceOCMiddleware -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SVProgressHUD -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMAISound -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMDylibLazyLoadWrapper -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TMWeAppClient -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Tangram -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TuringShield -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYCache -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYImage -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYText -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYWebImage -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapVectorLayout -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TPNS-iOS -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Weibo_SDK -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapPerformanceTest -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RaftKit -isystem /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TDFDebugBox -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/A4LoadMeasure -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/A4QMStartTimeMonitor -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/A4QMStartTimeMonitor -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/AgileMap -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/AgileMap -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Beacon/beacon-frameworks/BeaconAPI_Base -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/Bugly -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CrBase -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/CrBase -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DouyinOpenSDK -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLib -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibBusiness -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibBusiness -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibCore -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibCore -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibThirdParty -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLibThirdParty -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GT/GT/Library -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ITLogin/ITLogin -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/ITLogin -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQQKCardKit/MQQKCardKit -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBaseNew -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBizNew -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBizNew -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapReflux -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapReflux -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/NerdApi -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/OlRoute -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/OlRoute -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PLog -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PMonitor/PMonitor/framework_PMonitor -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PMonitor -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PandoraEx/PandoraEx/framework_PandoraEx -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PandoraEx -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PanguIosShell -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PanguIosShell -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PoiEngine -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PoiEngine -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PosEngine -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PosEngine -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/RenderEngine/lib -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMSpiderMan -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBaseline/QMapBaseline -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBusBusiness/QMapBusBusiness -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBusKit/QMapBusKit -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBusiness/QMapBusiness -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapFoundation/QMapFoundation -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapHippy/QMapHippy -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapInfoServiceKit/QMapInfoServiceKit -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapMiddlePlatform/QMapMiddlePlatform -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapNaviKit/QMapNaviKit -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapRouteSearchKit/QMapRouteSearchKit -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapUIKit/QMapUIKit -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QimeiSDK -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/QimeiSDK -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RAFTMeasure/RAFTMeasure/framework_RAFTMeasure -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RUM -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RaftKit/RaftKit/Core -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidance -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/RouteGuidance -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TPNS-iOS/XGVIPPush -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TPNS-iOS/XGVIPPush/InAppMessage -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TXMapView -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentLBS -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TencentLBS -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentOpenAPI/TencentOpenAPI -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentTravelService/TencentTravelService -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/XiaoHongShuOpenSDK/XiaoHongShuOpenSDK -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/XiaoHongShuOpenSDK -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy_hermes_full/Full/destroot/Library/Frameworks/universal -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/hippy_hermes_full -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libpag/framework -iframework /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/libpag -iframework /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/mars/mars -I /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TXMapView/TXMapView.framework/PrivateHeaders/ '-DCONFIG_VERSION="2021-03-27"' '-DCONFIG_BIGNUM=1'