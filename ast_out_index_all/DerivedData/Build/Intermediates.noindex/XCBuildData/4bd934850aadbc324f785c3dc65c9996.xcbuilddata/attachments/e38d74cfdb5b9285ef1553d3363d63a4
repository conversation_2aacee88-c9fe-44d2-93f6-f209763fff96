{"": {"diagnostics": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/QMWidgetExtension-master.dia", "emit-module-dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/QMWidgetExtension-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/QMWidgetExtension-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/QMWidgetExtension-master.swiftdeps"}, "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/QMWidget/TMWidgetBundle.swift": {"const-values": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.swiftconstvalues", "dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.d", "diagnostics": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.dia", "index-unit-output-path": "/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.o", "llvm-bc": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.bc", "object": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.o", "swift-dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle.swiftdeps", "swiftmodule": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/TMWidgetBundle~partial.swiftmodule"}, "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/ControlWidgetAppIntents.swift": {"const-values": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.swiftconstvalues", "dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.d", "diagnostics": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.dia", "index-unit-output-path": "/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.o", "llvm-bc": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.bc", "object": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.o", "swift-dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents.swiftdeps", "swiftmodule": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/ControlWidgetAppIntents~partial.swiftmodule"}, "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap/AppIntents/Destination.swift": {"const-values": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.swiftconstvalues", "dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.d", "diagnostics": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.dia", "index-unit-output-path": "/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.o", "llvm-bc": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.bc", "object": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.o", "swift-dependencies": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination.swiftdeps", "swiftmodule": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/QMWidgetExtension.build/Objects-normal/arm64/Destination~partial.swiftmodule"}}