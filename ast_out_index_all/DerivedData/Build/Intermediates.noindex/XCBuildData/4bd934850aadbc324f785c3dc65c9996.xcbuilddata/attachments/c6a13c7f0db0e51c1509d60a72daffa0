-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fobjc-weak -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/TAM_IOS_SDK-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/TAM_IOS_SDK-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/TAM_IOS_SDK-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK-5c24a7189d0b6802e2b7ff2920b5335c-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/TAM_IOS_SDK-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/TAM_IOS_SDK/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TAM_IOS_SDK.build/Debug-iphoneos/TAM_IOS_SDK.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/TAM_IOS_SDK