-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DragonMapKitC2OC-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DragonMapKitC2OC-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DragonMapKitC2OC-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC-2ef015221f2f36037f3d49cf871f6ef5-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DragonMapKitC2OC-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DragonMapKitC2OC/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphoneos/DragonMapKitC2OC.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DragonMapKitC2OC -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/GLMapLib -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/MapBaseNew -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/NerdApi -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/PLog -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/TXMapView '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap'