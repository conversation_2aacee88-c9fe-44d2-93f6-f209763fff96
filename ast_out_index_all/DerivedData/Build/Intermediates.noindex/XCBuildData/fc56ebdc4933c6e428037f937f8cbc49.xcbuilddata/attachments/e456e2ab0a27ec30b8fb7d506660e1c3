-target arm64-apple-ios14.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fapplication-extension -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/QMWidgetKit-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/QMWidgetKit-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/QMWidgetKit-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit-3811a0692ef6068b3304fe49d9c81196-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/QMWidgetKit-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMWidgetKit/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMWidgetKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMWidgetKit.build/Debug-iphoneos/QMWidgetKit.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMWidgetKit '-fmodule-map-file=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapSwiftBridge-iOS14.0/QMapSwiftBridge.modulemap' '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LiteRouteSearch/LiteRouteSearch-iOS14.0.modulemap'