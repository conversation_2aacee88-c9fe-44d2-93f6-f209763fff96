-target arm64-apple-ios12.0 '-std=gnu++14' '-stdlib=libc++' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DRFKT_VERSION=1.5.8' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/RaftKit-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/RaftKit-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/RaftKit-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit-71c2411c81c97d1010d2a2f0d269c3a0-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/RaftKit-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/RaftKit/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RaftKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FBRetainCycleDetector -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RaftKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/RaftKit/Core/Headers -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static/Bugly.xcframework/ios-arm64_armv7/Bugly.framework/Headers -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static/Bugly.xcframework/ios-arm64_armv7/Bugly.framework/PrivateHeaders -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static/Bugly.xcframework/ios-arm64/Bugly.framework/Headers -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static/Bugly.xcframework/ios-arm64/Bugly.framework/PrivateHeaders -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RaftKit.build/Debug-iphoneos/RaftKit.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/RaftKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Bugly/static -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RaftKit/RaftKit/Core -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy_hermes_full/Full/destroot/Library/Frameworks/universal -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/Bugly -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/XCFrameworkIntermediates/hippy_hermes_full