-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fapplication-extension -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/QMapIntentsExtension-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/QMapIntentsExtension-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/QMapIntentsExtension-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension-7de4140b7a22f205f5fee8171e9e8016-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/QMapIntentsExtension-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapIntentsExtension/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapIntentsExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsExtension -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsExtension.build/Debug-iphoneos/QMapIntentsExtension.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapIntentsExtension