-target arm64-apple-ios12.0 '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex' -fapplication-extension -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk -g -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/QMapIntentsUIExtension-generated-files.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/QMapIntentsUIExtension-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/QMapIntentsUIExtension-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension-6c59a91a0fd2fb40c2b1554b84f8e262-VFS-iphoneos/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/QMapIntentsUIExtension-project-headers.hmap -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapIntentsUIExtension/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMapIntentsUIExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/LiteRouteSearch -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapIntentsUIExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/DerivedSources-normal/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/DerivedSources/arm64 -I/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/QMapIntentsUIExtension.build/Debug-iphoneos/QMapIntentsUIExtension.build/DerivedSources -F/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QMapIntentsUIExtension