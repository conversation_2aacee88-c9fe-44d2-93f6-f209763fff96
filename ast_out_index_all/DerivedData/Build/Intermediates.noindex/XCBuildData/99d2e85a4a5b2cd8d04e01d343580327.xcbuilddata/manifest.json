{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos": {"is-mutated": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<WorkspaceHeaderMapVFSFilesWritten>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-linking>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-scanning>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--end>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--linker-inputs-ready>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--modules-ready>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--will-sign>"], "outputs": ["<all>"]}, "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh"], "roots": ["/tmp/TencentMap.dst", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": [], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--fused-phase0-merge-info-plist": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--start>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>", "<execute-shell-script-a859d1d342b9df48666caf0b74a56c37854df39322b3c88a858d03fc1530799d-target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2->", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--fused-phase0-merge-info-plist>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-linking": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-linking>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-scanning": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-scanning>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--end": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--entry>", "<execute-shell-script-a859d1d342b9df48666caf0b74a56c37854df39322b3c88a858d03fc1530799d-target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2->", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--fused-phase0-merge-info-plist>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--end>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--entry": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--entry>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--immediate": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-Debug-iphoneos--arm64-build-headers-stale-file-removal>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--immediate>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--linker-inputs-ready>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--modules-ready": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>", "<execute-shell-script-a859d1d342b9df48666caf0b74a56c37854df39322b3c88a858d03fc1530799d-target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2->", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--modules-ready>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--begin-compiling>", "<execute-shell-script-a859d1d342b9df48666caf0b74a56c37854df39322b3c88a858d03fc1530799d-target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2->", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--unsigned-product-ready>"]}, "P0:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:Gate target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--will-sign": {"tool": "phony", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--unsigned-product-ready>"], "outputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--will-sign>"]}, "P2:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:PhaseScriptExecution Merge Info.plist /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh": {"tool": "shell", "description": "PhaseScriptExecution Merge Info.plist /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--start>", "<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--entry>"], "outputs": ["<execute-shell-script-a859d1d342b9df48666caf0b74a56c37854df39322b3c88a858d03fc1530799d-target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2->"], "args": ["/bin/sh", "-c", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh"], "env": {"ACTION": "build", "AD_HOC_CODE_SIGNING_ALLOWED": "NO", "ALLOW_BUILD_REQUEST_OVERRIDES": "NO", "ALLOW_TARGET_PLATFORM_SPECIALIZATION": "NO", "ALTERNATE_GROUP": "staff", "ALTERNATE_MODE": "u+w,go-w,a+rX", "ALTERNATE_OWNER": "qy", "ALTERNATIVE_DISTRIBUTION_WEB": "NO", "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "ALWAYS_SEARCH_USER_PATHS": "NO", "ALWAYS_USE_SEPARATE_HEADERMAPS": "NO", "APPLE_INTERNAL_DEVELOPER_DIR": "/AppleInternal/Developer", "APPLE_INTERNAL_DIR": "/AppleInternal", "APPLE_INTERNAL_DOCUMENTATION_DIR": "/AppleInternal/Documentation", "APPLE_INTERNAL_LIBRARY_DIR": "/AppleInternal/Library", "APPLE_INTERNAL_TOOLS": "/AppleInternal/Developer/Tools", "APPLICATION_EXTENSION_API_ONLY": "NO", "APPLY_RULES_IN_COPY_FILES": "NO", "APPLY_RULES_IN_COPY_HEADERS": "NO", "APP_SHORTCUTS_ENABLE_FLEXIBLE_MATCHING": "YES", "ARCHS": "arm64", "ARCHS_STANDARD": "arm64", "ARCHS_STANDARD_32_64_BIT": "armv7 arm64", "ARCHS_STANDARD_32_BIT": "armv7", "ARCHS_STANDARD_64_BIT": "arm64", "ARCHS_STANDARD_INCLUDING_64_BIT": "arm64", "ARCHS_UNIVERSAL_IPHONE_OS": "armv7 arm64", "ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOLS": "YES", "AUTOMATICALLY_MERGE_DEPENDENCIES": "NO", "AVAILABLE_PLATFORMS": "appletvos appletvsimulator driverkit iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator", "BITCODE_GENERATION_MODE": "marker", "BUILD_ACTIVE_RESOURCES_ONLY": "NO", "BUILD_COMPONENTS": "headers build", "BUILD_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products", "BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "BUILD_ROOT": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products", "BUILD_STYLE": "", "BUILD_VARIANTS": "normal", "BUILT_PRODUCTS_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos", "BUNDLE_CONTENTS_FOLDER_PATH_deep": "Contents/", "BUNDLE_EXECUTABLE_FOLDER_NAME_deep": "MacOS", "BUNDLE_EXTENSIONS_FOLDER_PATH": "Extensions", "BUNDLE_FORMAT": "shallow", "BUNDLE_FRAMEWORKS_FOLDER_PATH": "Frameworks", "BUNDLE_PLUGINS_FOLDER_PATH": "PlugIns", "BUNDLE_PRIVATE_HEADERS_FOLDER_PATH": "PrivateHeaders", "BUNDLE_PUBLIC_HEADERS_FOLDER_PATH": "Headers", "CACHE_ROOT": "/var/folders/8_/0tvfq7fn43j534cqbz0xyccw0000gn/C/com.apple.DeveloperTools/16.1-16B40/Xcode", "CCHROOT": "/var/folders/8_/0tvfq7fn43j534cqbz0xyccw0000gn/C/com.apple.DeveloperTools/16.1-16B40/Xcode", "CHMOD": "/bin/chmod", "CHOWN": "/usr/sbin/chown", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CACHE_FINE_GRAINED_OUTPUTS": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_CPP_STATIC_DESTRUCTORS": "NO", "CLANG_ENABLE_EXPLICIT_MODULES": "YES", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_MODULES_BUILD_SESSION_FILE": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex/Session.modulevalidation", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "CLASS_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/JavaClasses", "CLEAN_PRECOMPS": "YES", "CLONE_HEADERS": "NO", "CODESIGNING_FOLDER_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_CONTEXT_CLASS": "XCiPhoneOSCodeSignContext", "CODE_SIGN_INJECT_BASE_ENTITLEMENTS": "YES", "CODE_SIGN_STYLE": "Automatic", "COLOR_DIAGNOSTICS": "NO", "COMBINE_HIDPI_IMAGES": "NO", "COMPILATION_CACHE_CAS_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/CompilationCache.noindex", "COMPILATION_CACHE_KEEP_CAS_DIRECTORY": "YES", "COMPILER_INDEX_STORE_ENABLE": "<PERSON><PERSON><PERSON>", "COMPOSITE_SDK_DIRS": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/CompositeSDKs", "COMPRESS_PNG_FILES": "YES", "CONFIGURATION": "Debug", "CONFIGURATION_BUILD_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos", "CONFIGURATION_TEMP_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos", "COPYING_PRESERVES_HFS_DATA": "NO", "COPY_HEADERS_RUN_UNIFDEF": "NO", "COPY_PHASE_STRIP": "NO", "CORRESPONDING_SIMULATOR_PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform", "CORRESPONDING_SIMULATOR_PLATFORM_NAME": "iphonesimulator", "CORRESPONDING_SIMULATOR_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk", "CORRESPONDING_SIMULATOR_SDK_NAME": "iphonesimulator18.1", "CP": "/bin/cp", "CREATE_INFOPLIST_SECTION_IN_BINARY": "NO", "CURRENT_ARCH": "undefined_arch", "CURRENT_VARIANT": "normal", "DEAD_CODE_STRIPPING": "YES", "DEBUGGING_SYMBOLS": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEBUG_INFORMATION_VERSION": "compiler-default", "DEFAULT_COMPILER": "com.apple.compilers.llvm.clang.1_0", "DEFAULT_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "DEFAULT_KEXT_INSTALL_PATH": "/System/Library/Extensions", "DEFINES_MODULE": "NO", "DEPLOYMENT_LOCATION": "NO", "DEPLOYMENT_POSTPROCESSING": "NO", "DEPLOYMENT_TARGET_SETTING_NAME": "IPHONEOS_DEPLOYMENT_TARGET", "DEPLOYMENT_TARGET_SUGGESTED_VALUES": "12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2 14.3 14.4 14.5 14.6 14.7 15.0 15.1 15.2 15.3 15.4 15.5 15.6 16.0 16.1 16.2 16.3 16.4 16.5 16.6 17.0 17.1 17.2 17.3 17.4 17.5 17.6 18.0 18.1", "DERIVED_FILES_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/DerivedSources", "DERIVED_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/DerivedSources", "DERIVED_SOURCES_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/DerivedSources", "DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER": "NO", "DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "DEVELOPER_FRAMEWORKS_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_FRAMEWORKS_DIR_QUOTED": "/Applications/Xcode.app/Contents/Developer/Library/Frameworks", "DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Library", "DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs", "DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Tools", "DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "DEVELOPMENT_LANGUAGE": "en", "DEVELOPMENT_TEAM": "B59Q5DZ8GY", "DIFF": "/usr/bin/diff", "DONT_GENERATE_INFOPLIST_FILE": "NO", "DSTROOT": "/tmp/TencentMap.dst", "DT_TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "DWARF_DSYM_FILE_NAME": ".dSYM", "DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT": "NO", "DWARF_DSYM_FOLDER_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos", "DYNAMIC_LIBRARY_EXTENSION": "dylib", "EAGER_COMPILATION_ALLOW_SCRIPTS": "NO", "EAGER_LINKING": "NO", "EFFECTIVE_PLATFORM_NAME": "-iphoneos", "EMBEDDED_CONTENT_CONTAINS_SWIFT": "NO", "EMBEDDED_PROFILE_NAME": "embedded.mobileprovision", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_APP_SANDBOX": "NO", "ENABLE_BITCODE": "NO", "ENABLE_CODE_COVERAGE": "YES", "ENABLE_DEBUG_DYLIB": "NO", "ENABLE_DEFAULT_HEADER_SEARCH_PATHS": "YES", "ENABLE_DEFAULT_SEARCH_PATHS": "YES", "ENABLE_HARDENED_RUNTIME": "NO", "ENABLE_HEADER_DEPENDENCIES": "YES", "ENABLE_ON_DEMAND_RESOURCES": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "ENABLE_XOJIT_PREVIEWS": "NO", "ENTITLEMENTS_DESTINATION": "Signature", "ENTITLEMENTS_REQUIRED": "YES", "EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS": ".DS_Store .svn .git .hg CVS", "EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES": "*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj", "FILE_LIST": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects/LinkFileList", "FIXED_FILES_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/FixedFiles", "FRAMEWORK_VERSION": "A", "FUSE_BUILD_PHASES": "YES", "FUSE_BUILD_SCRIPT_PHASES": "NO", "GCC3_VERSION": "3.3", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PFE_FILE_C_DIALECTS": "c objective-c c++ objective-c++", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1  CHANNEL_DEBUG=1 DEBUG_TOOLS_ENABLED=1", "GCC_THUMB_SUPPORT": "YES", "GCC_TREAT_WARNINGS_AS_ERRORS": "YES", "GCC_VERSION": "com.apple.compilers.llvm.clang.1_0", "GCC_VERSION_IDENTIFIER": "com_apple_compilers_llvm_clang_1_0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "GENERATED_MODULEMAP_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphoneos", "GENERATE_INFOPLIST_FILE": "NO", "GENERATE_INTERMEDIATE_TEXT_BASED_STUBS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_PKGINFO_FILE": "NO", "GENERATE_PROFILING_CODE": "NO", "GENERATE_TEXT_BASED_STUBS": "NO", "GID": "20", "GROUP": "staff", "HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES": "YES", "HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_TARGETS_NOT_BEING_BUILT": "YES", "HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS": "YES", "HEADERMAP_INCLUDES_PROJECT_HEADERS": "YES", "HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES": "YES", "HEADERMAP_USES_VFS": "NO", "HIDE_BITCODE_SYMBOLS": "YES", "HOME": "/Users/<USER>", "HOST_ARCH": "arm64", "HOST_PLATFORM": "macosx", "ICONV": "/usr/bin/iconv", "IMPLICIT_DEPENDENCY_DOMAIN": "default", "INFOPLIST_ENABLE_CFBUNDLEICONS_MERGE": "YES", "INFOPLIST_EXPAND_BUILD_SETTINGS": "YES", "INFOPLIST_KEY_NSSupportsLiveActivities": "YES", "INFOPLIST_KEY_NSSupportsLiveActivitiesFrequentUpdates": "YES", "INFOPLIST_OUTPUT_FORMAT": "binary", "INFOPLIST_PREPROCESS": "NO", "INLINE_PRIVATE_FRAMEWORKS": "NO", "INSTALLHDRS_COPY_PHASE": "NO", "INSTALLHDRS_SCRIPT_PHASE": "NO", "INSTALL_DIR": "/tmp/TencentMap.dst", "INSTALL_GROUP": "staff", "INSTALL_MODE_FLAG": "u+w,go-w,a+rX", "INSTALL_OWNER": "qy", "INSTALL_ROOT": "/tmp/TencentMap.dst", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IS_UNOPTIMIZED_BUILD": "YES", "JAVAC_DEFAULT_FLAGS": "-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8", "JAVA_APP_STUB": "/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub", "JAVA_ARCHIVE_CLASSES": "YES", "JAVA_ARCHIVE_TYPE": "JAR", "JAVA_COMPILER": "/usr/bin/javac", "JAVA_FRAMEWORK_RESOURCES_DIRS": "Resources", "JAVA_JAR_FLAGS": "cv", "JAVA_SOURCE_SUBDIR": ".", "JAVA_USE_DEPENDENCIES": "YES", "JAVA_ZIP_FLAGS": "-urg", "JIKES_DEFAULT_FLAGS": "+E +OLDCSO", "KASAN_CFLAGS_CLASSIC": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KASAN_CFLAGS_TBI": "-DKASAN=1 -DKASAN_TBI=1 -fsanitize=kernel-hwaddress -mllvm -hwasan-recover=0 -mllvm -hwasan-instrument-atomics=0 -mllvm -hwasan-instrument-stack=1 -mllvm -hwasan-generate-tags-with-calls=1 -mllvm -hwasan-instrument-with-calls=1 -mllvm -hwasan-use-short-granules=0 -mllvm -hwasan-memory-access-callback-prefix=__asan_", "KASAN_DEFAULT_CFLAGS": "-DKASAN=1 -DKASAN_CLASSIC=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow", "KEEP_PRIVATE_EXTERNS": "NO", "LD_DEPENDENCY_INFO_FILE": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/undefined_arch/BuildInfo_dependency_info.dat", "LD_EXPORT_SYMBOLS": "YES", "LD_GENERATE_MAP_FILE": "NO", "LD_MAP_FILE_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/BuildInfo-LinkMap-normal-undefined_arch.txt", "LD_NO_PIE": "NO", "LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER": "YES", "LD_SHARED_CACHE_ELIGIBLE": "Automatic", "LD_WARN_DUPLICATE_LIBRARIES": "NO", "LD_WARN_UNUSED_DYLIBS": "NO", "LEGACY_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer", "LEX": "lex", "LIBRARY_DEXT_INSTALL_PATH": "/Library/DriverExtensions", "LIBRARY_FLAG_NOSPACE": "YES", "LIBRARY_KEXT_INSTALL_PATH": "/Library/Extensions", "LINKER_DISPLAYS_MANGLED_NAMES": "NO", "LINK_FILE_LIST_normal_arm64": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/arm64/BuildInfo.LinkFileList", "LINK_OBJC_RUNTIME": "YES", "LINK_WITH_STANDARD_LIBRARIES": "YES", "LLVM_TARGET_TRIPLE_OS_VERSION": "ios12.0", "LLVM_TARGET_TRIPLE_VENDOR": "apple", "LM_AUX_CONST_METADATA_LIST_PATH_normal_arm64": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/arm64/BuildInfo.SwiftConstValuesFileList", "LOCALIZATION_EXPORT_SUPPORTED": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "NO", "LOCALIZED_STRING_MACRO_NAMES": "NSLocalizedString CFCopyLocalizedString", "LOCALIZED_STRING_SWIFTUI_SUPPORT": "YES", "LOCAL_ADMIN_APPS_DIR": "/Applications/Utilities", "LOCAL_APPS_DIR": "/Applications", "LOCAL_DEVELOPER_DIR": "/Library/Developer", "LOCAL_LIBRARY_DIR": "/Library", "LOCROOT": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap", "LOCSYMROOT": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap", "MAC_OS_X_PRODUCT_BUILD_VERSION": "24F74", "MAC_OS_X_VERSION_ACTUAL": "150500", "MAC_OS_X_VERSION_MAJOR": "150000", "MAC_OS_X_VERSION_MINOR": "150500", "MAKE_MERGEABLE": "NO", "MERGEABLE_LIBRARY": "NO", "MERGED_BINARY_TYPE": "none", "MERGE_LINKED_LIBRARIES": "NO", "METAL_LIBRARY_FILE_BASE": "default", "METAL_LIBRARY_OUTPUT_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/", "MODULE_CACHE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/ModuleCache.noindex", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "NATIVE_ARCH": "arm64", "NATIVE_ARCH_32_BIT": "arm", "NATIVE_ARCH_64_BIT": "arm64", "NATIVE_ARCH_ACTUAL": "arm64", "NO_COMMON": "YES", "OBJECT_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects", "OBJECT_FILE_DIR_normal": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal", "OBJROOT": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "ONLY_ACTIVE_ARCH": "NO", "OS": "MACOS", "OSAC": "/usr/bin/osacompile", "PASCAL_STRINGS": "YES", "PATH": "/Applications/Xcode.app/Contents/SharedFrameworks/XCBuild.framework/Versions/A/PlugIns/XCBBuildService.bundle/Contents/PlugIns/XCBSpecifications.ideplugin/Contents/Resources:/Applications/Xcode.app/Contents/SharedFrameworks/XCBuild.framework/Versions/A/PlugIns/XCBBuildService.bundle/Contents/PlugIns/XCBSpecifications.ideplugin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/appleinternal/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/appleinternal/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/Users/<USER>/.vscode/extensions/ms-python.python-2025.8.0-darwin-arm64/python_files/deactivate/zsh:/Users/<USER>/GRAG_iOS/.venv/bin:/Users/<USER>/.rvm/gems/ruby-3.1.4/bin:/Users/<USER>/.rvm/gems/ruby-3.1.4@global/bin:/Users/<USER>/.rvm/rubies/ruby-3.1.4/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/opt/homebrew/bin:/Users/<USER>/.gem/ruby/3.3.0/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.nvm/versions/node/v22.17.0/bin:/Users/<USER>/.rbenv/shims:/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/homebrew/sbin:/Users/<USER>/.lmstudio/bin:/Users/<USER>/.rvm/bin", "PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES": "/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms", "PER_ARCH_OBJECT_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/undefined_arch", "PER_VARIANT_OBJECT_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal", "PKGINFO_FILE_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/PkgInfo", "PLATFORM_DEVELOPER_APPLICATIONS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications", "PLATFORM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin", "PLATFORM_DEVELOPER_LIBRARY_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library", "PLATFORM_DEVELOPER_SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs", "PLATFORM_DEVELOPER_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools", "PLATFORM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr", "PLATFORM_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform", "PLATFORM_DISPLAY_NAME": "iOS", "PLATFORM_FAMILY_NAME": "iOS", "PLATFORM_NAME": "iphoneos", "PLATFORM_PREFERRED_ARCH": "arm64", "PLATFORM_PRODUCT_BUILD_VERSION": "22B74", "PLIST_FILE_OUTPUT_FORMAT": "binary", "PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR": "YES", "PRECOMP_DESTINATION_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/PrefixHeaders", "PROCESSED_INFOPLIST_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/undefined_arch/Processed-Info.plist", "PRODUCT_MODULE_NAME": "BuildInfo", "PRODUCT_NAME": "BuildInfo", "PRODUCT_SETTINGS_PATH": "", "PROFILING_CODE": "NO", "PROJECT": "TencentMap", "PROJECT_CLASS_PREFIX": "QM", "PROJECT_DERIVED_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/DerivedSources", "PROJECT_DIR": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap", "PROJECT_FILE_PATH": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap/TencentMap.xcodeproj", "PROJECT_GUID": "a859d1d342b9df48666caf0b74a56c37", "PROJECT_NAME": "TencentMap", "PROJECT_TEMP_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build", "PROJECT_TEMP_ROOT": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "QM_CUSTOM_SCHEME": "qqmapdebug", "QM_ITLOGIN_SCHEME": "itlogin-tencentmap", "QM_WECHAT_SCHEME": "wx36174d3a5f72f64a", "QM_XHS_SCHEME": "xhs40340c91f507ae57ba9234a48a26377c", "RECOMMENDED_IPHONEOS_DEPLOYMENT_TARGET": "15.0", "RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS": "YES", "REMOVE_CVS_FROM_RESOURCES": "YES", "REMOVE_GIT_FROM_RESOURCES": "YES", "REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_HG_FROM_RESOURCES": "YES", "REMOVE_STATIC_EXECUTABLES_FROM_EMBEDDED_BUNDLES": "YES", "REMOVE_SVN_FROM_RESOURCES": "YES", "RESCHEDULE_INDEPENDENT_HEADERS_PHASES": "YES", "REZ_COLLECTOR_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/ResourceManagerResources", "REZ_OBJECTS_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/ResourceManagerResources/Objects", "SCAN_ALL_SOURCE_FILES_FOR_INCLUDES": "NO", "SCRIPT_INPUT_FILE_COUNT": "0", "SCRIPT_INPUT_FILE_LIST_COUNT": "0", "SCRIPT_OUTPUT_FILE_COUNT": "0", "SCRIPT_OUTPUT_FILE_LIST_COUNT": "0", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "SDK_DIR": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "SDK_DIR_iphoneos": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "SDK_DIR_iphoneos18_1": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "SDK_NAME": "iphoneos18.1", "SDK_NAMES": "iphoneos18.1", "SDK_PRODUCT_BUILD_VERSION": "22B74", "SDK_STAT_CACHE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData", "SDK_STAT_CACHE_ENABLE": "YES", "SDK_STAT_CACHE_PATH": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache", "SDK_VERSION": "18.1", "SDK_VERSION_ACTUAL": "180100", "SDK_VERSION_MAJOR": "180000", "SDK_VERSION_MINOR": "180100", "SED": "/usr/bin/sed", "SEPARATE_STRIP": "NO", "SEPARATE_SYMBOL_EDIT": "NO", "SET_DIR_MODE_OWNER_GROUP": "YES", "SET_FILE_MODE_OWNER_GROUP": "NO", "SHALLOW_BUNDLE": "NO", "SHARED_DERIVED_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DerivedSources", "SHARED_PRECOMPS_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders", "SKIP_INSTALL": "YES", "SOURCE_ROOT": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap", "SRCROOT": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap", "STRINGSDATA_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/undefined_arch", "STRINGSDATA_ROOT": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build", "STRINGS_FILE_INFOPLIST_RENAME": "YES", "STRINGS_FILE_OUTPUT_ENCODING": "binary", "STRIP_BITCODE_FROM_COPIED_FILES": "YES", "STRIP_INSTALLED_PRODUCT": "NO", "STRIP_STYLE": "all", "STRIP_SWIFT_SYMBOLS": "YES", "SUPPORTED_DEVICE_FAMILIES": "1,2", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator", "SUPPORTS_MACCATALYST": "YES", "SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD": "YES", "SUPPORTS_TEXT_BASED_API": "NO", "SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD": "YES", "SUPPRESS_WARNINGS": "NO", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_PLATFORM_TARGET_PREFIX": "ios", "SWIFT_RESPONSE_FILE_PATH_normal_arm64": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Objects-normal/arm64/BuildInfo.SwiftFileList", "SYMROOT": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products", "SYSTEM_ADMIN_APPS_DIR": "/Applications/Utilities", "SYSTEM_APPS_DIR": "/Applications", "SYSTEM_CORE_SERVICES_DIR": "/System/Library/CoreServices", "SYSTEM_DEMOS_DIR": "/Applications/Extras", "SYSTEM_DEVELOPER_APPS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications", "SYSTEM_DEVELOPER_BIN_DIR": "/Applications/Xcode.app/Contents/Developer/usr/bin", "SYSTEM_DEVELOPER_DEMOS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples", "SYSTEM_DEVELOPER_DIR": "/Applications/Xcode.app/Contents/Developer", "SYSTEM_DEVELOPER_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library", "SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools", "SYSTEM_DEVELOPER_JAVA_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Java Tools", "SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools", "SYSTEM_DEVELOPER_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes", "SYSTEM_DEVELOPER_TOOLS": "/Applications/Xcode.app/Contents/Developer/Tools", "SYSTEM_DEVELOPER_TOOLS_DOC_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools", "SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR": "/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools", "SYSTEM_DEVELOPER_USR_DIR": "/Applications/Xcode.app/Contents/Developer/usr", "SYSTEM_DEVELOPER_UTILITIES_DIR": "/Applications/Xcode.app/Contents/Developer/Applications/Utilities", "SYSTEM_DEXT_INSTALL_PATH": "/System/Library/DriverExtensions", "SYSTEM_DOCUMENTATION_DIR": "/Library/Documentation", "SYSTEM_KEXT_INSTALL_PATH": "/System/Library/Extensions", "SYSTEM_LIBRARY_DIR": "/System/Library", "TAPI_DEMANGLE": "YES", "TAPI_ENABLE_PROJECT_HEADERS": "NO", "TAPI_LANGUAGE": "objective-c", "TAPI_LANGUAGE_STANDARD": "compiler-default", "TAPI_USE_SRCROOT": "YES", "TAPI_VERIFY_MODE": "Pedantic", "TARGETED_DEVICE_FAMILY": "1", "TARGETNAME": "BuildInfo", "TARGET_BUILD_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos", "TARGET_NAME": "BuildInfo", "TARGET_TEMP_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build", "TEMP_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build", "TEMP_FILES_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build", "TEMP_FILE_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build", "TEMP_ROOT": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "TEST_FRAMEWORK_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk/Developer/Library/Frameworks", "TEST_LIBRARY_SEARCH_PATHS": " /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "TOOLCHAINS": "com.apple.dt.toolchain.XcodeDefault", "TOOLCHAIN_DIR": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "TREAT_MISSING_BASELINES_AS_TEST_FAILURES": "NO", "TREAT_MISSING_SCRIPT_PHASE_OUTPUTS_AS_ERRORS": "NO", "UID": "501", "UNINSTALLED_PRODUCTS_DIR": "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/UninstalledProducts", "UNSTRIPPED_PRODUCT": "NO", "USER": "qy", "USER_APPS_DIR": "/Users/<USER>/Applications", "USER_LIBRARY_DIR": "/Users/<USER>/Library", "USE_DYNAMIC_NO_PIC": "YES", "USE_HEADERMAP": "YES", "USE_HEADER_SYMLINKS": "NO", "VALIDATE_DEVELOPMENT_ASSET_PATHS": "YES_ERROR", "VALIDATE_PRODUCT": "NO", "VALID_ARCHS": "arm64 arm64e armv7 armv7s", "VERBOSE_PBXCP": "NO", "VERSION_INFO_BUILDER": "qy", "VERSION_INFO_FILE": "BuildInfo_vers.c", "VERSION_INFO_STRING": "\"@(#)PROGRAM:BuildInfo  PROJECT:TencentMap-\"", "WORKSPACE_DIR": "/Users/<USER>/GRAG_iOS/hammmer-workspace", "WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES": "NO", "XCODE_APP_SUPPORT_DIR": "/Applications/Xcode.app/Contents/Developer/Library/Xcode", "XCODE_PRODUCT_BUILD_VERSION": "16B40", "XCODE_VERSION_ACTUAL": "1610", "XCODE_VERSION_MAJOR": "1600", "XCODE_VERSION_MINOR": "1610", "XPCSERVICES_FOLDER_PATH": "/XPCServices", "YACC": "yacc", "arch": "undefined_arch", "variant": "normal"}, "allow-missing-inputs": true, "always-out-of-date": true, "working-directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap", "control-enabled": false, "repair-via-ownership-analysis": true, "signature": "8ae98819b54208f47a2f506dd167fd2d"}, "P2:target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh", "inputs": ["<target-BuildInfo-a859d1d342b9df48666caf0b74a56c3795da3af0a1fa6eb46a1710dadeb3aea2--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/TencentMap.build/Debug-iphoneos/BuildInfo.build/Script-D6C8CA6A258B511E001C4B2F.sh"]}}}