{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile": {"is-mutated": true}, "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool": {"is-mutated": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache", "<Linked Binary /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a>", "<Linked Binary /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a>", "<target-DKMobile-****************************************************************--begin-scanning>", "<target-DKMobile-****************************************************************--linker-inputs-ready>", "<target-DKProtocolsPool-****************************************************************--begin-scanning>", "<target-DKProtocolsPool-****************************************************************--end>", "<target-DKProtocolsPool-****************************************************************--linker-inputs-ready>", "<target-DKProtocolsPool-****************************************************************--modules-ready>", "<workspace-Debug-iphoneos18.1-iphoneos--stale-file-removal>"], "outputs": ["<all>"]}, "<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-non-framework-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-generated-files.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-own-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-project-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "roots": ["/tmp/DKMobile.dst", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"], "outputs": ["<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-non-framework-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-generated-files.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-own-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-project-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "roots": ["/tmp/DKProtocolsPool.dst", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"], "outputs": ["<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphoneos18.1-iphoneos--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile-76bc415940689dcc40784cc56bc9d871-VFS-iphoneos/all-product-headers.yaml", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool-7109707696ad219963127d35c0c5378a-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphoneos18.1-iphoneos--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "-o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace", "signature": "aa52836c20da5ebf079de16b9dc975e9"}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile"]}, "P0:::CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile-76bc415940689dcc40784cc56bc9d871-VFS-iphoneos/all-product-headers.yaml", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool-7109707696ad219963127d35c0c5378a-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-DKMobile-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKMobile-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList"], "outputs": ["<target-DKMobile-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-ChangePermissions>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-StripSymbols>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-GenerateStubAPI>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ProductPostprocessingTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-CodeSign>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-Validate>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-CopyAside>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-DKMobile-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-DKMobile-****************************************************************--will-sign>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-DKMobile-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--GeneratedFilesTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ProductStructureTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--RealityAssetsTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-non-framework-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-generated-files.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-own-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-project-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.hmap"], "outputs": ["<target-DKMobile-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--HeadermapTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleMapTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKMobile-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-DKMobile-****************************************************************--InfoPlistTaskProducer>", "<target-DKMobile-****************************************************************--VersionPlistTaskProducer>", "<target-DKMobile-****************************************************************--SanitizerTaskProducer>", "<target-DKMobile-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-DKMobile-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-DKMobile-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-DKMobile-****************************************************************--StubBinaryTaskProducer>", "<target-DKMobile-****************************************************************--TestTargetTaskProducer>", "<target-DKMobile-****************************************************************--TestHostTaskProducer>", "<target-DKMobile-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-DKMobile-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-DKMobile-****************************************************************--DocumentationTaskProducer>", "<target-DKMobile-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--start>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ProductPostprocessingTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-DKMobile-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-DKMobile-****************************************************************--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--fused-phase0-copy-headers>"]}, "P0:::Gate target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>"]}, "P0:::Gate target-DKMobile-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--generated-headers>"]}, "P0:::Gate target-DKMobile-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--swift-generated-headers>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList"], "outputs": ["<target-DKProtocolsPool-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-ChangePermissions>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-StripSymbols>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-GenerateStubAPI>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ProductPostprocessingTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-CodeSign>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-Validate>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-CopyAside>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-DKProtocolsPool-****************************************************************--will-sign>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--GeneratedFilesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ProductStructureTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--RealityAssetsTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-non-framework-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-generated-files.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-own-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-project-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.hmap"], "outputs": ["<target-DKProtocolsPool-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--HeadermapTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleMapTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKProtocolsPool-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--InfoPlistTaskProducer>", "<target-DKProtocolsPool-****************************************************************--VersionPlistTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SanitizerTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-DKProtocolsPool-****************************************************************--StubBinaryTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TestTargetTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TestHostTaskProducer>", "<target-DKProtocolsPool-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-DKProtocolsPool-****************************************************************--DocumentationTaskProducer>", "<target-DKProtocolsPool-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--start>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ProductPostprocessingTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"], "outputs": ["<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--generated-headers>"]}, "P0:::Gate target-DKProtocolsPool-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--swift-generated-headers>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKMobile.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-DKMobile-****************************************************************--begin-compiling>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKMobile.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-DKMobile-****************************************************************--begin-linking>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKMobile.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--begin-scanning>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--end": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--entry>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-non-framework-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-generated-files.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-own-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-project-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "<target-DKMobile-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-DKMobile-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-DKMobile-****************************************************************--Barrier-ChangePermissions>", "<target-DKMobile-****************************************************************--Barrier-CodeSign>", "<target-DKMobile-****************************************************************--Barrier-CopyAside>", "<target-DKMobile-****************************************************************--Barrier-GenerateStubAPI>", "<target-DKMobile-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-DKMobile-****************************************************************--Barrier-RegisterProduct>", "<target-DKMobile-****************************************************************--Barrier-StripSymbols>", "<target-DKMobile-****************************************************************--Barrier-Validate>", "<target-DKMobile-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-DKMobile-****************************************************************--DocumentationTaskProducer>", "<target-DKMobile-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-DKMobile-****************************************************************--GeneratedFilesTaskProducer>", "<target-DKMobile-****************************************************************--HeadermapTaskProducer>", "<target-DKMobile-****************************************************************--InfoPlistTaskProducer>", "<target-DKMobile-****************************************************************--ModuleMapTaskProducer>", "<target-DKMobile-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKMobile-****************************************************************--ProductPostprocessingTaskProducer>", "<target-DKMobile-****************************************************************--ProductStructureTaskProducer>", "<target-DKMobile-****************************************************************--RealityAssetsTaskProducer>", "<target-DKMobile-****************************************************************--SanitizerTaskProducer>", "<target-DKMobile-****************************************************************--StubBinaryTaskProducer>", "<target-DKMobile-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-DKMobile-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-DKMobile-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-DKMobile-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-DKMobile-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-DKMobile-****************************************************************--TestHostTaskProducer>", "<target-DKMobile-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-DKMobile-****************************************************************--TestTargetTaskProducer>", "<target-DKMobile-****************************************************************--VersionPlistTaskProducer>", "<target-DKMobile-****************************************************************--copy-headers-completion>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>"], "outputs": ["<target-DKMobile-****************************************************************--end>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKMobile.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--entry>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKMobile.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-DKMobile-****************************************************************--immediate>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList"], "outputs": ["<target-DKMobile-****************************************************************--linker-inputs-ready>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--begin-compiling>"], "outputs": ["<target-DKMobile-****************************************************************--modules-ready>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "<target-DKMobile-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-DKMobile-****************************************************************--unsigned-product-ready>"]}, "P0:target-DKMobile-****************************************************************-:Debug:Gate target-DKMobile-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-DKMobile-****************************************************************--will-sign>"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o.scan"]}, "P0:target-DKMobile-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o.scan"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--modules-ready>", "<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKProtocolsPool.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--end>", "<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKProtocolsPool.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--begin-linking>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--scan-inputs-ready>", "<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKProtocolsPool.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--begin-scanning>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--end": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--entry>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-non-framework-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-generated-files.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-own-target-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-project-headers.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.hmap", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "<target-DKProtocolsPool-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-DKProtocolsPool-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-DKProtocolsPool-****************************************************************--Barrier-ChangePermissions>", "<target-DKProtocolsPool-****************************************************************--Barrier-CodeSign>", "<target-DKProtocolsPool-****************************************************************--Barrier-CopyAside>", "<target-DKProtocolsPool-****************************************************************--Barrier-GenerateStubAPI>", "<target-DKProtocolsPool-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-DKProtocolsPool-****************************************************************--Barrier-RegisterProduct>", "<target-DKProtocolsPool-****************************************************************--Barrier-StripSymbols>", "<target-DKProtocolsPool-****************************************************************--Barrier-Validate>", "<target-DKProtocolsPool-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--DocumentationTaskProducer>", "<target-DKProtocolsPool-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-DKProtocolsPool-****************************************************************--GeneratedFilesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--HeadermapTaskProducer>", "<target-DKProtocolsPool-****************************************************************--InfoPlistTaskProducer>", "<target-DKProtocolsPool-****************************************************************--ModuleMapTaskProducer>", "<target-DKProtocolsPool-****************************************************************--ModuleVerifierTaskProducer>", "<target-DKProtocolsPool-****************************************************************--ProductPostprocessingTaskProducer>", "<target-DKProtocolsPool-****************************************************************--ProductStructureTaskProducer>", "<target-DKProtocolsPool-****************************************************************--RealityAssetsTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SanitizerTaskProducer>", "<target-DKProtocolsPool-****************************************************************--StubBinaryTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TestHostTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-DKProtocolsPool-****************************************************************--TestTargetTaskProducer>", "<target-DKProtocolsPool-****************************************************************--VersionPlistTaskProducer>", "<target-DKProtocolsPool-****************************************************************--copy-headers-completion>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase1-compile-sources&link-binary>", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--end>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-DKMobile-****************************************************************--end>", "<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKProtocolsPool.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--entry>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************-Debug-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/DKProtocolsPool.dst>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool>", "<CreateBuildDirectory-/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphoneos>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList"], "outputs": ["<target-DKProtocolsPool-****************************************************************--linker-inputs-ready>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--modules-ready>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--begin-compiling>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o.scan", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "<target-DKProtocolsPool-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--unsigned-product-ready>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:Gate target-DKProtocolsPool-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-DKProtocolsPool-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-DKProtocolsPool-****************************************************************--will-sign>"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o.scan"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o.scan"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o.scan"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o.scan"]}, "P0:target-DKProtocolsPool-****************************************************************-:Debug:ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o.scan"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o"]}, "P1:target-DKMobile-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o.scan", "<target-DKMobile-****************************************************************--generated-headers>", "<target-DKMobile-****************************************************************--swift-generated-headers>", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o"]}, "P1:target-DKProtocolsPool-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o.scan", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o"]}, "P1:target-DKProtocolsPool-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o.scan", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o"]}, "P1:target-DKProtocolsPool-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o.scan", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o"]}, "P1:target-DKProtocolsPool-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o.scan", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o"]}, "P1:target-DKProtocolsPool-****************************************************************-:Debug:CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-prefix.pch", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m", "<ClangStatCache /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/SDKStatCaches.noindex/iphoneos18.1-22B74-456b5073a84ca8a40bffd5133c40ea2b.sdkstatcache>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o.scan", "<target-DKProtocolsPool-****************************************************************--generated-headers>", "<target-DKProtocolsPool-****************************************************************--swift-generated-headers>", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile-76bc415940689dcc40784cc56bc9d871-VFS-iphoneos/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile-76bc415940689dcc40784cc56bc9d871-VFS-iphoneos/all-product-headers.yaml", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile-76bc415940689dcc40784cc56bc9d871-VFS-iphoneos/all-product-headers.yaml"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool-7109707696ad219963127d35c0c5378a-VFS-iphoneos/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool-7109707696ad219963127d35c0c5378a-VFS-iphoneos/all-product-headers.yaml", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool-7109707696ad219963127d35c0c5378a-VFS-iphoneos/all-product-headers.yaml"]}, "P2:target-DKMobile-****************************************************************-:Debug:Libtool /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a normal": {"tool": "shell", "description": "Libtool /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a normal", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKApplicationDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKAppLifeDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKCoreDelegate+CarPlay.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKLog.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMachORegistry.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobileCore.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRegistryCenter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Application.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+Service.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouter+URLRouter.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRouterContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKRulesChecker.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKSchemeHandler.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceContext.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceDigest.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKServiceStartMirror.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UINavigationController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/UIViewController+DKApplication.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "<target-DKMobile-****************************************************************--fused-phase0-copy-headers>", "<target-DKMobile-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a", "<Linked Binary /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool", "-static", "-arch_only", "arm64", "-D", "-s<PERSON><PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "-L/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile", "-filelist", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "-dependency_info", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat", "-o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKMobile/libDKMobile.a"], "env": {"IPHONEOS_DEPLOYMENT_TARGET": "12.0"}, "working-directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "deps": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile_libtool_dependency_info.dat"], "deps-style": "dependency-info", "signature": "c38b885e6095e6c992f9495dc285ba8c"}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-non-framework-target-headers.hmap", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-non-framework-target-headers.hmap"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-target-headers.hmap", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-all-target-headers.hmap"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-generated-files.hmap", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-generated-files.hmap"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-own-target-headers.hmap", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-own-target-headers.hmap"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-project-headers.hmap", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile-project-headers.hmap"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.DependencyMetadataFileList"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.hmap", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/DKMobile.hmap"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/DKMobile.LinkFileList"]}, "P2:target-DKMobile-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-DKMobile-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKMobile.build/Debug-iphoneos/DKMobile.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:Libtool /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a normal": {"tool": "shell", "description": "Libtool /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a normal", "inputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPApplications.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPPerformanceStatService.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPQQMaps.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool-dummy.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKPServices.o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "<target-DKProtocolsPool-****************************************************************--fused-phase0-copy-headers>", "<target-DKProtocolsPool-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a", "<Linked Binary /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a>", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool", "-static", "-arch_only", "arm64", "-D", "-s<PERSON><PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.1.sdk", "-L/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool", "-filelist", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "-dependency_info", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat", "-o", "/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/DKProtocolsPool/libDKProtocolsPool.a"], "env": {"IPHONEOS_DEPLOYMENT_TARGET": "12.0"}, "working-directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "deps": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool_libtool_dependency_info.dat"], "deps-style": "dependency-info", "signature": "dfd63412ad0c2fba0490f85207d27506"}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-non-framework-target-headers.hmap", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-non-framework-target-headers.hmap"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-target-headers.hmap", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-all-target-headers.hmap"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-generated-files.hmap", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-generated-files.hmap"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-own-target-headers.hmap", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-own-target-headers.hmap"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-project-headers.hmap", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool-project-headers.hmap"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.DependencyMetadataFileList"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.hmap", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/DKProtocolsPool.hmap"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/DKProtocolsPool.LinkFileList"]}, "P2:target-DKProtocolsPool-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp", "inputs": ["<target-DKProtocolsPool-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/DKProtocolsPool.build/Debug-iphoneos/DKProtocolsPool.build/Objects-normal/arm64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp"]}}}