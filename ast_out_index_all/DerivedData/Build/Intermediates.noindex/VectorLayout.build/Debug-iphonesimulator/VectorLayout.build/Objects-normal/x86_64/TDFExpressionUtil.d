dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/TDFExpressionUtil.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/TDFExpressionUtil.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSASTProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSASTEnumDefinitions.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Serialization/FB/tdf_interface_expression_generated.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/flatbuffers.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/array.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/base.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/stl_emulation.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/vector.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/buffer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/buffer_ref.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/verifier.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/util.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/detached_buffer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/allocator.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/default_allocator.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/flatbuffer_builder.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/fbstring.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/struct.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/table.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/flatbuffer/source/vector_downward.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/imp/VLFBUtils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/vector_layout_interface_generated.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Serialization/FB/tdf_interface_card_generated.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Serialization/FB/tdf_interface_node_generated.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/interfaces/IVLFBInitialization.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/NSArray+VLJSON.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFLiteral.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSASTNode.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSLiteralProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFIdentifier.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSIdentifierProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/StatementImp/TDFIfStatement.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSIfStatementProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSStatement.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/StatementImp/TDFBlockStatement.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSBlockStatementProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFCallExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSCallExpressionProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFUnaryExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSUnaryOrUpdateExpressionProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSOperationType.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFMemberExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSMemberExpressionProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFBinaryExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSBinaryOrLogicalExpressionProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFUpdateExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFLogicalExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/StatementImp/TDFExpressionStatement.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSExpressionStatementProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFConditionalExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSConditionalExpressionProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/Declaraction/TDFVariableDeclaraction.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSVariableDeclarationProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Expression/ExpressionImp/TDFAssignmentExpression.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/VectorLayoutExpression/JSAssignmentExpressionProtocol.h
