dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutJSEngine/JSDebug/VLJSDebugSessionManager.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutJSEngine/JSDebug/VLJSDebugSessionManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutJSEngine/JSEngineImpl/QuickJS/Features/QuickJS_iOS.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QuickJS/quickjs.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QuickJS/quickjs-libc.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QuickJS/quickjs-debugger.h
