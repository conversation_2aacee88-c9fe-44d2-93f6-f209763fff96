dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/View/VLLayer/VLLayer.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/View/VLLayer/VLLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/View/VLLayer/VLInnerLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Interface/Private/IVLBorderData.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/CSS/Value/TDFAttributeValue.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/CSS/Value/TDFExcuting.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutSerializations/interfaces/IVLFBInitialization.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/CSS/Value/ITDFExcuteContext.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayout/Card/VLCardConfigurations.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/CSS/Value/TDFLengthUnitValue.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/CSS/Value/Struct/TDFColorValue.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/View/VLLayer/VLRoundedRect.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/View/VLLayer/UIBezierPath+VL.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/View/VLBorderDrawing.h
