dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/Text/VLTextComponentLayerHighlighter.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/Text/VLTextComponentLayerHighlighter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextComponentLayerHighlighter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/RCAssert.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKHighlightOverlayLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitRenderer+Positioning.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitRenderer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitAttributes.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextComponentLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKAsyncLayer.h
