dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/HTML/VLHTMLAttributedStringFactory.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/HTML/VLHTMLAttributedStringFactory.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/HTML/VLHTMLNode.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/NSString+VectorLayout.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/HTML/NSString+HTMLString.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Imp/Utils/VLFundationUtils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCSS/CssValueParsers/VLEnumMapTabs.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutCommon/Category/HTML/VLHTMLTagEnumHandler.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/Text/VLTextAttributesStringKeys.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitAttributes.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitEntityAttribute.h
