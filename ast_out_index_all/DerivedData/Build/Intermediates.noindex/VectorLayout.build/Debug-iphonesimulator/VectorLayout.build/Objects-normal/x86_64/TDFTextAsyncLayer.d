dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Utility/TDFTextAsyncLayer.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Utility/TDFTextAsyncLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFTextKitRenderer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitAttributes.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Card/Utils/TDFTextSizeCache.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Component/TDFTextLayout.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Component/TDFTextDebugOption.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Component/TDFTextLine.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Component/TDFTextAttribute.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/TDF/Node/Text/TDFText/Component/TDFTextInput.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextKitRendererCache.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKCacheImpl.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKFunctor.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/RCAssert.h
