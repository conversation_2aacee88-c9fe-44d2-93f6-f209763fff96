dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/Text/VLTextComponentLayer.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/VectorLayout/VectorLayout-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/Text/VLTextComponentLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextComponentLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKDefines.h \
  /Users/<USER>/GRA<PERSON>_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/CKMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKAsyncLayer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayout/VectorLayout/Classes/Impl/Internal/VectorLayoutComponent/Text/VLTextComponentLayerHighlighter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit/CKTextComponentLayerHighlighter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/RenderCore/RCAssert.h
