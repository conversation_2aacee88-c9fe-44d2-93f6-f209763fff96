dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/vfs/native/src/uri_loader.cc \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/uri_loader.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/handler/uri_handler.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/request_job.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/worker_manager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/task_runner.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/idle_task.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/time_delta.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/time_point.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/macros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/task.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/worker.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/driver.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/job_response.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view_utils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/logging.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/log_level.h
