dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/viewPager/HippyViewPager.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/viewPager/HippyViewPager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/HippyComponent.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/viewPager/HippyViewPagerItem.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/NativeRenderTouchesView.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/NativeRenderTouchesProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/utils/HippyConvert+NativeRender.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyConvert.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/UIView+Hippy.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/HippyViewEventProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/UIView+DirectionalLayout.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/UIView+MountEvent.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/utils/HippyRenderUtils.h
