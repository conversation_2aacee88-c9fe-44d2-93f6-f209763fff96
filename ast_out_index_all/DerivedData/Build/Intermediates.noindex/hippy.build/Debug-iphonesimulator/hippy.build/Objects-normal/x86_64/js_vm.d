dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/driver/js/src/vm/js_vm.cc \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/js_vm.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_ctx.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/logging.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/log_level.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/macros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/common.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/js_value_wrapper.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/callback_info.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_class_definition.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_ctx_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/native_source_code.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_event.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/hippy_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_try_catch.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view_utils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/jsc/jsc_vm.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/hermes/hermes_vm.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/hermes.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/HermesExport.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/RuntimeConfig.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/CrashManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/CtorConfig.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/GCConfig.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/GCTripwireContext.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/jsi/jsi.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/jsi/jsi-inl.h
