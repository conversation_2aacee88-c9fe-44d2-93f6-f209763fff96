dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/UIView+DirectionalLayout.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/UIView+DirectionalLayout.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/UIView+Hippy.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/HippyComponent.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/HippyViewEventProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/NativeRenderTouchesProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyI18nUtils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/layout_node.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/hippy_value.h
