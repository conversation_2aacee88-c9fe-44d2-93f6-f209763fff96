dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/driver/js/src/scope.cc \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/scope.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/animation/animation_manager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/macros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/task.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/animation/animation.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/animation/cubic_bezier_animation.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/animation/animation_math.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/animation/animation_set.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_action_interceptor.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_manager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_argument.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/hippy_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_event.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_listener.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/layout_node.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/scene.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/logging.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/log_level.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/task_runner.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/idle_task.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/time_delta.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/time_point.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/worker.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/driver.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/base_timer.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_node.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/check.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/render_manager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/root_node.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/diff_utils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/persistent_object_map.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/scene_builder.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/common.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/engine.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/js_vm.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_ctx.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/js_value_wrapper.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/callback_info.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_class_definition.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_ctx_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/native_source_code.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/uri_loader.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/handler/uri_handler.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/request_job.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/worker_manager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/vfs/job_response.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_entry.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_resource_timing.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_navigation_timing.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_paint_timing.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/js_convert_utils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/module_register.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/animation_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/contextify_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/module_base.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view_utils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/console_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/event_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/animation_frame_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_entry_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_frame_timing_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_frame_timing.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_mark_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_mark.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_measure_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/performance/performance_measure.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_navigation_timing_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_paint_timing_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/performance/performance_resource_timing_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/scene_builder_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/timer_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/ui_manager_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/modules/ui_layout_module.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/hermes/hermes_vm.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/hermes/hermes_ctx.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/hermes/hermes_class_definition.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/jsi/jsi.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/jsi/jsi-inl.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/hermes/hermes_ctx_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/hermes.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/HermesExport.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/RuntimeConfig.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/CrashManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/CtorConfig.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/GCConfig.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full/hermes/Public/GCTripwireContext.h
