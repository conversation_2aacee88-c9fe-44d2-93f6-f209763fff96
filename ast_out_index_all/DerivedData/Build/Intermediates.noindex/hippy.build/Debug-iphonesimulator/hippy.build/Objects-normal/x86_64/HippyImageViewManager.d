dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/image/HippyImageViewManager.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyAssert.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyJSStackFrame.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyUtils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/image/HippyImageViewManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/HippyViewManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyConvert.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridgeModule.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/image/HippyImageView.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/HippyComponent.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/image/HippyAnimatedImageView.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/image/HippyImageProviderProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridgeDelegate.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyInvalidating.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/HippyMethodInterceptorProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/modules/HippyModulesSetup.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/module/imageloader/HippyImageViewCustomLoader.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge+BundleLoad.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge+ModuleManage.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/HippyUIManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/touch_handler/HippyCustomTouchHandlerProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/TypeConverter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge+VFS.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/vfs/ios/HippyVFSDefines.h
