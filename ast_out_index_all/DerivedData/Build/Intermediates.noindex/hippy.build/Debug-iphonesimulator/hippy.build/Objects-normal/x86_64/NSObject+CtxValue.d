dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/utils/NSObject+CtxValue.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/utils/NSObject+CtxValue.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyAssert.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyJSStackFrame.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyUtils.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_ctx.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/logging.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/log_level.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/macros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/common.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/base/js_value_wrapper.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/callback_info.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_class_definition.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/napi/js_ctx_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/driver/vm/native_source_code.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/dom/dom_event.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/hippy_value.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy/footstone/string_view_utils.h
