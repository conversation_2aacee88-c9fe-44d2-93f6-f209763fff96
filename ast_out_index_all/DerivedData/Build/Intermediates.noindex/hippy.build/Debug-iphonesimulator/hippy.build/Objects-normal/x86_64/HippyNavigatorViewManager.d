dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/navigator/HippyNavigatorViewManager.mm \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/hippy/hippy-prefix.pch \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/HippyUIManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridgeDelegate.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyInvalidating.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridgeModule.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyDefines.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/HippyMethodInterceptorProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/modules/HippyModulesSetup.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/image/HippyImageProviderProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/module/imageloader/HippyImageViewCustomLoader.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge+BundleLoad.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/framework/ios/base/bridge/HippyBridge+ModuleManage.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/view/HippyViewManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyConvert.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/ios/base/HippyLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/touch_handler/HippyCustomTouchHandlerProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/navigator/HippyNavigatorViewManager.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/renderer/native/ios/renderer/component/navigator/HippyNavigatorHostView.h
