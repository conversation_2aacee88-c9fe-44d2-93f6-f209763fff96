dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Extensions/DDContextFilterLogFormatter.m \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/CocoaLumberjack/CocoaLumberjack-prefix.pch \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/include/DarwinBasic.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDContextFilterLogFormatter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Supporting\ Files/DDLegacyMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDLoggerNames.h
