dependencies: \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/include/Darwin.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDASLLogCapture.m \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/CocoaLumberjack/CocoaLumberjack-prefix.pch \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/include/TargetConditionals.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/include/asl.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/usr/include/notify.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDASLLogCapture.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDASLLogger.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Supporting\ Files/DDLegacyMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/include/CocoaLumberjack/DDLoggerNames.h
