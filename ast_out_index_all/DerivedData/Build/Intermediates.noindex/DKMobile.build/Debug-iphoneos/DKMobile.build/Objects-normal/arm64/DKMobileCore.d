dependencies: \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/DKMobile/DKMobile-prefix.pch \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterCommons.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKService.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDelegate.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKPluginsProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMacros.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore+Private.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobile.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS.sdk/System/Library/Frameworks/CarPlay.framework/Modules/module.modulemap \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKURLRouteProtocol.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.h \
  /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.h
