<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>0A7B048F-EE2F-451E-B93D-325C80B52CE8</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>0A7B048F-EE2F-451E-B93D-325C80B52CE8.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>1</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>TencentMap project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>BuildInfo</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building workspace TencentMap with scheme BuildInfo and configuration Debug</string>
			<key>timeStartedRecording</key>
			<real>773824469.82440698</real>
			<key>timeStoppedRecording</key>
			<real>773824476.73003995</real>
			<key>title</key>
			<string>Building workspace TencentMap with scheme BuildInfo and configuration Debug</string>
			<key>uniqueIdentifier</key>
			<string>0A7B048F-EE2F-451E-B93D-325C80B52CE8</string>
		</dict>
		<key>6AB16B30-E969-48E8-A2EC-B4E9FA265165</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>6AB16B30-E969-48E8-A2EC-B4E9FA265165.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>1</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>2</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>QMapBusiness project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>QMapBusiness</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>0</integer>
			<key>signature</key>
			<string>Building workspace TencentMap with scheme QMapBusiness and configuration Debug</string>
			<key>timeStartedRecording</key>
			<real>773824452.75168395</real>
			<key>timeStoppedRecording</key>
			<real>773824465.50030994</real>
			<key>title</key>
			<string>Building workspace TencentMap with scheme QMapBusiness and configuration Debug</string>
			<key>uniqueIdentifier</key>
			<string>6AB16B30-E969-48E8-A2EC-B4E9FA265165</string>
		</dict>
	</dict>
</dict>
</plist>
