<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>318B1684-0900-41D9-AC83-CE26027A838E</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>318B1684-0900-41D9-AC83-CE26027A838E.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>14</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>2</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>QMapBusiness project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>QMapBusiness</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>0</integer>
			<key>signature</key>
			<string>Building workspace TencentMap with scheme QMapBusiness and configuration Debug</string>
			<key>timeStartedRecording</key>
			<real>773692151.79516804</real>
			<key>timeStoppedRecording</key>
			<real>773692160.16613901</real>
			<key>title</key>
			<string>Building workspace TencentMap with scheme QMapBusiness and configuration Debug</string>
			<key>uniqueIdentifier</key>
			<string>318B1684-0900-41D9-AC83-CE26027A838E</string>
		</dict>
		<key>F65EC9B1-17A5-41C4-9E6E-CCEF1B6EE7AC</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>F65EC9B1-17A5-41C4-9E6E-CCEF1B6EE7AC.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>1</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>DKProtocolsPool project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>DKProtocolsPool</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>0</integer>
			<key>signature</key>
			<string>Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug</string>
			<key>timeStartedRecording</key>
			<real>773692165.73562205</real>
			<key>timeStoppedRecording</key>
			<real>773692175.83089399</real>
			<key>title</key>
			<string>Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug</string>
			<key>uniqueIdentifier</key>
			<string>F65EC9B1-17A5-41C4-9E6E-CCEF1B6EE7AC</string>
		</dict>
	</dict>
</dict>
</plist>
