<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>08C0A349-AE32-495C-B711-2ECA4ED979AF</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>08C0A349-AE32-495C-B711-2ECA4ED979AF.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>1</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>TencentMap project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>BuildInfo</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building workspace TencentMap with scheme BuildInfo and configuration Debug</string>
			<key>timeStartedRecording</key>
			<real>773896454.97685897</real>
			<key>timeStoppedRecording</key>
			<real>773896461.55960798</real>
			<key>title</key>
			<string>Building workspace TencentMap with scheme BuildInfo and configuration Debug</string>
			<key>uniqueIdentifier</key>
			<string>08C0A349-AE32-495C-B711-2ECA4ED979AF</string>
		</dict>
		<key>0A5C4AB4-5987-4899-99DF-B3CEA8EBC405</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>0A5C4AB4-5987-4899-99DF-B3CEA8EBC405.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>1</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>DKProtocolsPool project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>DKProtocolsPool</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>0</integer>
			<key>signature</key>
			<string>Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug</string>
			<key>timeStartedRecording</key>
			<real>773896467.46999395</real>
			<key>timeStoppedRecording</key>
			<real>773896474.57091606</real>
			<key>title</key>
			<string>Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug</string>
			<key>uniqueIdentifier</key>
			<string>0A5C4AB4-5987-4899-99DF-B3CEA8EBC405</string>
		</dict>
	</dict>
</dict>
</plist>
