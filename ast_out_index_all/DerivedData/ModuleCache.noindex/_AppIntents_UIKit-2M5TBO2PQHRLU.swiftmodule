---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.1/_AppIntents_UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1728603093000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.1/_AppIntents_UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
    size:            32568
  - mtime:           1727589760000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            2084165
    sdk_relative:    true
  - mtime:           1727590511000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1727588729000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1727586528000000000
    path:            'usr/include/dispatch/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1728091643000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1727667416000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1727590736000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3711
    sdk_relative:    true
  - mtime:           1727590756000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            996
    sdk_relative:    true
  - mtime:           1727590768000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            997
    sdk_relative:    true
  - mtime:           1727590756000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            960
    sdk_relative:    true
  - mtime:           1727590756000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1422
    sdk_relative:    true
  - mtime:           1727590773000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            692
    sdk_relative:    true
  - mtime:           1727590736000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            23965
    sdk_relative:    true
  - mtime:           1727589821000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            5810
    sdk_relative:    true
  - mtime:           1727590785000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            19774
    sdk_relative:    true
  - mtime:           1727591125000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            249276
    sdk_relative:    true
  - mtime:           1727591541000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22665
    sdk_relative:    true
  - mtime:           1727592190000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            172314
    sdk_relative:    true
  - mtime:           1727592129000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            6429
    sdk_relative:    true
  - mtime:           1727592652000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            56622
    sdk_relative:    true
  - mtime:           1727592876000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22797
    sdk_relative:    true
  - mtime:           1727592975000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            33153
    sdk_relative:    true
  - mtime:           1727591152000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3741
    sdk_relative:    true
  - mtime:           1727592197000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            94364
    sdk_relative:    true
  - mtime:           1727593274000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            998185
    sdk_relative:    true
  - mtime:           1727586576000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1727593580000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            52569
    sdk_relative:    true
  - mtime:           1728360933000000000
    path:            'System/Library/Frameworks/CoreLocation.framework/Headers/CoreLocation.apinotes'
    size:            1557
    sdk_relative:    true
  - mtime:           1727593045000000000
    path:            'usr/lib/swift/os.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            107939
    sdk_relative:    true
  - mtime:           1727595370000000000
    path:            'usr/lib/swift/CoreLocation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            19135
    sdk_relative:    true
  - mtime:           1727594254000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1728530774000000000
    path:            'System/Library/Frameworks/CoreSpotlight.framework/Headers/CoreSpotlight.apinotes'
    size:            703
    sdk_relative:    true
  - mtime:           1727593424000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            20301
    sdk_relative:    true
  - mtime:           1728530834000000000
    path:            'System/Library/Frameworks/CoreSpotlight.framework/Modules/CoreSpotlight.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            10628
    sdk_relative:    true
  - mtime:           1727594644000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21072
    sdk_relative:    true
  - mtime:           1727591458000000000
    path:            'usr/lib/swift/Distributed.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            26911
    sdk_relative:    true
  - mtime:           1728360740000000000
    path:            'System/Library/Frameworks/Network.framework/Headers/Network.apinotes'
    size:            213
    sdk_relative:    true
  - mtime:           1728360804000000000
    path:            'System/Library/Frameworks/Network.framework/Modules/Network.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            79208
    sdk_relative:    true
  - mtime:           1727594676000000000
    path:            'System/Library/Frameworks/ExtensionFoundation.framework/Modules/ExtensionFoundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            5451
    sdk_relative:    true
  - mtime:           1728015057000000000
    path:            'System/Library/Frameworks/AppIntents.framework/Modules/AppIntents.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            724737
    sdk_relative:    true
  - mtime:           1728088719000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1728091632000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            42479
    sdk_relative:    true
  - mtime:           1727593787000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            10764
    sdk_relative:    true
  - mtime:           1727593616000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1142
    sdk_relative:    true
  - mtime:           1727592178000000000
    path:            'usr/lib/swift/simd.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            218603
    sdk_relative:    true
  - mtime:           1727592713000000000
    path:            'usr/lib/swift/Spatial.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            99059
    sdk_relative:    true
  - mtime:           1727593818000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1725741256000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1727600530000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1727594184000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1727589356000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1728091594000000000
    path:            'System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes'
    size:            326
    sdk_relative:    true
  - mtime:           1728531296000000000
    path:            'System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes'
    size:            161991
    sdk_relative:    true
  - mtime:           1728092090000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21117
    sdk_relative:    true
  - mtime:           1727594412000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1425
    sdk_relative:    true
  - mtime:           1727839879000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            580
    sdk_relative:    true
  - mtime:           1727593638000000000
    path:            'usr/lib/swift/Metal.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            25853
    sdk_relative:    true
  - mtime:           1727594058000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1653
    sdk_relative:    true
  - mtime:           1727594334000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21434
    sdk_relative:    true
  - mtime:           1727593763000000000
    path:            'usr/lib/swift/FileProvider.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1371
    sdk_relative:    true
  - mtime:           1727594047000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            584
    sdk_relative:    true
  - mtime:           1728531467000000000
    path:            'System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            219038
    sdk_relative:    true
  - mtime:           1728360881000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            970781
    sdk_relative:    true
  - mtime:           1728362271000000000
    path:            'System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1345787
    sdk_relative:    true
  - mtime:           1728015121000000000
    path:            'System/Library/Frameworks/_AppIntents_SwiftUI.framework/Modules/_AppIntents_SwiftUI.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            15661
    sdk_relative:    true
  - mtime:           1728015797000000000
    path:            'System/Library/Frameworks/_AppIntents_UIKit.framework/Modules/_AppIntents_UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            2819
    sdk_relative:    true
version:         1
...
