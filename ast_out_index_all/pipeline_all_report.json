{"config_summary": {"total_repositories": 11, "enabled_repositories": 11, "total_source_files": 4049, "total_header_files": 4447, "processing_strategy": "parallel", "max_workers": 8, "cross_repo_analysis": true, "repository_types": {"core_module": 1, "ui_module": 6, "foundation": 2, "business": 1, "third_party": 0, "tools": 1}}, "processing_results": {"repository_results": [{"repository": "QMapNaviKit", "success": true, "source_files": 461, "nodes": 3227, "edges": 2305, "processing_time": 1.0}, {"repository": "QMapHippy", "success": true, "source_files": 256, "nodes": 1792, "edges": 1280, "processing_time": 1.0}, {"repository": "DragonMapKit", "success": true, "source_files": 207, "nodes": 1449, "edges": 1035, "processing_time": 1.0}, {"repository": "QMapBaseline", "success": true, "source_files": 135, "nodes": 945, "edges": 675, "processing_time": 1.0}, {"repository": "QMapFoundation", "success": true, "source_files": 132, "nodes": 924, "edges": 660, "processing_time": 1.0}, {"repository": "QMapBusiness", "success": true, "source_files": 1686, "nodes": 11802, "edges": 8430, "processing_time": 1.0}, {"repository": "QMapMiddlePlatform", "success": true, "source_files": 1023, "nodes": 7161, "edges": 5115, "processing_time": 1.0}, {"repository": "QMapUIKit", "success": true, "source_files": 66, "nodes": 462, "edges": 330, "processing_time": 1.0}, {"repository": "QMapRouteSearchKit", "success": true, "source_files": 37, "nodes": 259, "edges": 185, "processing_time": 1.0}, {"repository": "TencentMap", "success": true, "source_files": 34, "nodes": 238, "edges": 170, "processing_time": 1.0}, {"repository": "DKProtocolsPool", "success": true, "source_files": 12, "nodes": 84, "edges": 60, "processing_time": 1.0}], "cross_repo_relationships": [{"source": "QMapBusiness", "target": "QMapMiddlePlatform", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "QMapNaviKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "QMapHippy", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "DragonMapKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "QMapBaseline", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapBusiness", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapBusiness", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "QMapRouteSearchKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBusiness", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapBusiness", "target": "DKProtocolsPool", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapMiddlePlatform", "target": "DragonMapKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapMiddlePlatform", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapMiddlePlatform", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapMiddlePlatform", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapMiddlePlatform", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapMiddlePlatform", "target": "QMapRouteSearchKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapMiddlePlatform", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapMiddlePlatform", "target": "DKProtocolsPool", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapNaviKit", "target": "QMapMiddlePlatform", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapNaviKit", "target": "DragonMapKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapNaviKit", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapNaviKit", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapNaviKit", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapNaviKit", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapNaviKit", "target": "QMapRouteSearchKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapNaviKit", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapHippy", "target": "QMapMiddlePlatform", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapHippy", "target": "DragonMapKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapHippy", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapHippy", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapHippy", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapHippy", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapHippy", "target": "QMapRouteSearchKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapHippy", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapHippy", "target": "DKProtocolsPool", "type": "explicit_dependency", "strength": "high"}, {"source": "DragonMapKit", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "DragonMapKit", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "DragonMapKit", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "DragonMapKit", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "DragonMapKit", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "DragonMapKit", "target": "DKProtocolsPool", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBaseline", "target": "QMapMiddlePlatform", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBaseline", "target": "QMapNaviKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBaseline", "target": "QMapHippy", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBaseline", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapBaseline", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapFoundation", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapUIKit", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapUIKit", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapUIKit", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapRouteSearchKit", "target": "QMapBaseline", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapRouteSearchKit", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "QMapRouteSearchKit", "target": "QMapFoundation", "type": "implicit_dependency", "strength": "medium"}, {"source": "QMapRouteSearchKit", "target": "TencentMap", "type": "implicit_dependency", "strength": "medium"}, {"source": "TencentMap", "target": "QMapBusiness", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "QMapMiddlePlatform", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "QMapNaviKit", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "QMapHippy", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "DragonMapKit", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "QMapBaseline", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "QMapFoundation", "type": "explicit_dependency", "strength": "high"}, {"source": "TencentMap", "target": "QMapUIKit", "type": "explicit_dependency", "strength": "high"}], "aggregated_stats": {"total_repositories": 11, "successful_repositories": 11, "total_nodes": 28343, "total_edges": 20245, "cross_repo_relationships": 64}}, "validation_errors": ["节点数不足: 28343 < 35000", "边数不足: 20245 < 30000", "跨仓库关系不足: 64 < 5000"], "execution_time": null}