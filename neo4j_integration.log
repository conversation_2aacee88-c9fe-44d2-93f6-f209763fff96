2025-07-09 10:18:19,947 - __main__ - INFO - 🚀 开始Pipeline-All Neo4j完整集成...
2025-07-09 10:18:19,947 - __main__ - INFO - 🚀 初始化Neo4j集成组件...
2025-07-09 10:18:19,947 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:18:19,951 - neo4j_integration.connection - ERROR - ❌ Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [<PERSON>rr<PERSON> 61] Connection refused)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [<PERSON>rrno 61] Connection refused)
2025-07-09 10:18:19,951 - __main__ - ERROR - Neo4j连接失败
2025-07-09 10:18:19,951 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:18:19,951 - __main__ - INFO - 🧹 资源清理完成
2025-07-09 10:18:32,756 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:18:32,760 - neo4j_integration.connection - ERROR - ❌ Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [Errno 61] Connection refused)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [Errno 61] Connection refused)
