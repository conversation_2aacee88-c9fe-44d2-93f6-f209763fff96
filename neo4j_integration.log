2025-07-09 10:18:19,947 - __main__ - INFO - 🚀 开始Pipeline-All Neo4j完整集成...
2025-07-09 10:18:19,947 - __main__ - INFO - 🚀 初始化Neo4j集成组件...
2025-07-09 10:18:19,947 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:18:19,951 - neo4j_integration.connection - ERROR - ❌ Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [<PERSON>rr<PERSON> 61] Connection refused)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [<PERSON>rrno 61] Connection refused)
2025-07-09 10:18:19,951 - __main__ - ERROR - Neo4j连接失败
2025-07-09 10:18:19,951 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:18:19,951 - __main__ - INFO - 🧹 资源清理完成
2025-07-09 10:18:32,756 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:18:32,760 - neo4j_integration.connection - ERROR - ❌ Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [Errno 61] Connection refused)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [Errno 61] Connection refused)
2025-07-09 10:26:27,341 - __main__ - INFO - 🚀 开始Pipeline-All Neo4j完整集成...
2025-07-09 10:26:27,342 - __main__ - INFO - 🚀 初始化Neo4j集成组件...
2025-07-09 10:26:27,342 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:26:27,351 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 10:26:27,351 - __main__ - INFO - ✅ 组件初始化完成
2025-07-09 10:26:27,351 - __main__ - INFO - 🛠️ 准备Neo4j数据库...
2025-07-09 10:26:27,351 - neo4j_integration.schema - INFO - 🚀 开始准备Neo4j数据库...
2025-07-09 10:26:27,352 - neo4j_integration.schema - INFO - 🗑️ 删除现有约束和索引...
2025-07-09 10:26:27,753 - neo4j_integration.schema - INFO - ✅ 约束和索引删除完成
2025-07-09 10:26:27,753 - neo4j_integration.schema - INFO - 🧹 开始清理Neo4j数据库...
2025-07-09 10:26:27,753 - neo4j_integration.schema - INFO - 删除所有关系...
2025-07-09 10:26:28,102 - neo4j_integration.schema - INFO - 删除所有节点...
2025-07-09 10:26:28,451 - neo4j_integration.schema - INFO - ✅ 数据库清理完成
2025-07-09 10:26:28,452 - neo4j_integration.schema - INFO - 🏗️ 创建Neo4j数据库模式...
2025-07-09 10:26:28,812 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 52 (offset: 51))
"CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)"
                                                    ^}
2025-07-09 10:26:28,816 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_source_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_source_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.source_repo)"
                                                           ^}
2025-07-09 10:26:28,819 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_target_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_target_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.target_repo)"
                                                           ^}
2025-07-09 10:26:28,822 - neo4j_integration.schema - INFO - ✅ 模式创建完成: 10/13 成功
2025-07-09 10:26:28,822 - neo4j_integration.schema - WARNING - 模式创建部分失败，但继续执行
2025-07-09 10:26:28,822 - neo4j_integration.schema - INFO - 🔍 验证数据库模式...
2025-07-09 10:26:28,896 - neo4j_integration.schema - INFO - 📊 模式验证完成:
2025-07-09 10:26:28,896 - neo4j_integration.schema - INFO -    约束数量: 1
2025-07-09 10:26:28,897 - neo4j_integration.schema - INFO -    索引数量: 10
2025-07-09 10:26:28,897 - neo4j_integration.schema - INFO -    节点标签: 5
2025-07-09 10:26:28,897 - neo4j_integration.schema - INFO -    关系类型: 0
2025-07-09 10:26:28,897 - neo4j_integration.schema - INFO - ✅ 数据库准备完成
2025-07-09 10:26:28,897 - __main__ - INFO - 📥 开始数据导入...
2025-07-09 10:26:28,897 - __main__ - INFO - 节点文件: ast_out_index_all/nodes_all.jsonl (24.8 MB)
2025-07-09 10:26:28,897 - __main__ - INFO - 边文件: ast_out_index_all/edges_all.jsonl (95.4 MB)
2025-07-09 10:26:28,897 - neo4j_integration.importer - INFO - 🚀 开始完整数据导入...
2025-07-09 10:26:28,897 - neo4j_integration.importer - INFO - 📥 开始导入节点数据...
2025-07-09 10:26:28,897 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/nodes_all.jsonl
2025-07-09 10:26:29,201 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,290 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,364 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,434 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,500 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,565 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,626 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,684 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,742 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,797 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,797 - neo4j_integration.importer - INFO - 已处理 10 批节点，导入 0 个节点
2025-07-09 10:26:29,857 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,908 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:29,958 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,006 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,054 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,104 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,150 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,195 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,240 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,285 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,285 - neo4j_integration.importer - INFO - 已处理 20 批节点，导入 0 个节点
2025-07-09 10:26:30,327 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,369 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,418 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,461 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,504 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,543 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,582 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,623 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,662 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,701 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,701 - neo4j_integration.importer - INFO - 已处理 30 批节点，导入 0 个节点
2025-07-09 10:26:30,740 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,778 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,819 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,861 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,899 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,937 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:30,974 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,019 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,059 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,103 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,103 - neo4j_integration.importer - INFO - 已处理 40 批节点，导入 0 个节点
2025-07-09 10:26:31,159 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,200 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,241 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,279 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,317 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,355 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,396 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,434 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,472 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,512 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,512 - neo4j_integration.importer - INFO - 已处理 50 批节点，导入 0 个节点
2025-07-09 10:26:31,550 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,596 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,635 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,673 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,711 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,753 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,794 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,837 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,878 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,919 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:31,919 - neo4j_integration.importer - INFO - 已处理 60 批节点，导入 0 个节点
2025-07-09 10:26:31,974 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,016 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,055 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,095 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,133 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,176 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,218 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,256 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,294 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,331 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,331 - neo4j_integration.importer - INFO - 已处理 70 批节点，导入 0 个节点
2025-07-09 10:26:32,371 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,409 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,449 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,491 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,530 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,570 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,609 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,649 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,689 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,727 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,728 - neo4j_integration.importer - INFO - 已处理 80 批节点，导入 0 个节点
2025-07-09 10:26:32,752 - neo4j_integration.importer - ERROR - 批量导入节点失败: The result is out of scope. The associated transaction has been closed. Results can only be used while the transaction is open.
2025-07-09 10:26:32,752 - neo4j_integration.importer - INFO - ✅ 节点导入完成: 0 成功, 80549 失败
2025-07-09 10:26:32,752 - neo4j_integration.importer - ERROR - 节点导入失败，停止导入
2025-07-09 10:26:32,752 - __main__ - ERROR - 数据导入失败
2025-07-09 10:26:32,752 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:26:32,752 - __main__ - INFO - 🧹 资源清理完成
2025-07-09 10:27:12,714 - __main__ - INFO - 🚀 开始Pipeline-All Neo4j完整集成...
2025-07-09 10:27:12,715 - __main__ - INFO - 🚀 初始化Neo4j集成组件...
2025-07-09 10:27:12,715 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:27:12,748 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 10:27:12,748 - __main__ - INFO - ✅ 组件初始化完成
2025-07-09 10:27:12,748 - __main__ - INFO - 🛠️ 准备Neo4j数据库...
2025-07-09 10:27:12,748 - neo4j_integration.schema - INFO - 🚀 开始准备Neo4j数据库...
2025-07-09 10:27:12,748 - neo4j_integration.schema - INFO - 🗑️ 删除现有约束和索引...
2025-07-09 10:27:12,905 - neo4j_integration.schema - INFO - ✅ 约束和索引删除完成
2025-07-09 10:27:12,905 - neo4j_integration.schema - INFO - 🧹 开始清理Neo4j数据库...
2025-07-09 10:27:12,905 - neo4j_integration.schema - INFO - 删除所有关系...
2025-07-09 10:27:12,928 - neo4j_integration.schema - INFO - 删除所有节点...
2025-07-09 10:27:13,776 - neo4j_integration.schema - INFO - ✅ 数据库清理完成
2025-07-09 10:27:13,776 - neo4j_integration.schema - INFO - 🏗️ 创建Neo4j数据库模式...
2025-07-09 10:27:14,047 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 52 (offset: 51))
"CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)"
                                                    ^}
2025-07-09 10:27:14,048 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_source_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_source_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.source_repo)"
                                                           ^}
2025-07-09 10:27:14,052 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_target_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_target_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.target_repo)"
                                                           ^}
2025-07-09 10:27:14,056 - neo4j_integration.schema - INFO - ✅ 模式创建完成: 10/13 成功
2025-07-09 10:27:14,056 - neo4j_integration.schema - WARNING - 模式创建部分失败，但继续执行
2025-07-09 10:27:14,056 - neo4j_integration.schema - INFO - 🔍 验证数据库模式...
2025-07-09 10:27:14,122 - neo4j_integration.schema - INFO - 📊 模式验证完成:
2025-07-09 10:27:14,122 - neo4j_integration.schema - INFO -    约束数量: 1
2025-07-09 10:27:14,122 - neo4j_integration.schema - INFO -    索引数量: 10
2025-07-09 10:27:14,122 - neo4j_integration.schema - INFO -    节点标签: 5
2025-07-09 10:27:14,122 - neo4j_integration.schema - INFO -    关系类型: 0
2025-07-09 10:27:14,122 - neo4j_integration.schema - INFO - ✅ 数据库准备完成
2025-07-09 10:27:14,122 - __main__ - INFO - 📥 开始数据导入...
2025-07-09 10:27:14,123 - __main__ - INFO - 节点文件: ast_out_index_all/nodes_all.jsonl (24.8 MB)
2025-07-09 10:27:14,123 - __main__ - INFO - 边文件: ast_out_index_all/edges_all.jsonl (95.4 MB)
2025-07-09 10:27:14,123 - neo4j_integration.importer - INFO - 🚀 开始完整数据导入...
2025-07-09 10:27:14,123 - neo4j_integration.importer - INFO - 📥 开始导入节点数据...
2025-07-09 10:27:14,123 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/nodes_all.jsonl
2025-07-09 10:27:14,562 - neo4j_integration.importer - INFO - 已处理 10 批节点，导入 10000 个节点
2025-07-09 10:27:14,971 - neo4j_integration.importer - INFO - 已处理 20 批节点，导入 20000 个节点
2025-07-09 10:27:15,358 - neo4j_integration.importer - INFO - 已处理 30 批节点，导入 30000 个节点
2025-07-09 10:27:15,749 - neo4j_integration.importer - INFO - 已处理 40 批节点，导入 40000 个节点
2025-07-09 10:27:16,180 - neo4j_integration.importer - INFO - 已处理 50 批节点，导入 50000 个节点
2025-07-09 10:27:16,572 - neo4j_integration.importer - INFO - 已处理 60 批节点，导入 60000 个节点
2025-07-09 10:27:16,972 - neo4j_integration.importer - INFO - 已处理 70 批节点，导入 70000 个节点
2025-07-09 10:27:17,366 - neo4j_integration.importer - INFO - 已处理 80 批节点，导入 80000 个节点
2025-07-09 10:27:17,390 - neo4j_integration.importer - INFO - ✅ 节点导入完成: 80549 成功, 0 失败
2025-07-09 10:27:17,390 - neo4j_integration.importer - INFO - 🔗 开始导入关系数据...
2025-07-09 10:27:17,390 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/edges_all.jsonl
2025-07-09 10:27:17,881 - neo4j_integration.importer - INFO - 已处理 10 批关系，导入 1770 个关系
2025-07-09 10:27:18,131 - neo4j_integration.importer - INFO - 已处理 20 批关系，导入 3247 个关系
2025-07-09 10:27:18,356 - neo4j_integration.importer - INFO - 已处理 30 批关系，导入 4580 个关系
2025-07-09 10:27:18,606 - neo4j_integration.importer - INFO - 已处理 40 批关系，导入 6202 个关系
2025-07-09 10:27:18,840 - neo4j_integration.importer - INFO - 已处理 50 批关系，导入 7558 个关系
2025-07-09 10:27:19,111 - neo4j_integration.importer - INFO - 已处理 60 批关系，导入 8861 个关系
2025-07-09 10:27:19,343 - neo4j_integration.importer - INFO - 已处理 70 批关系，导入 10535 个关系
2025-07-09 10:27:19,557 - neo4j_integration.importer - INFO - 已处理 80 批关系，导入 11552 个关系
2025-07-09 10:27:19,794 - neo4j_integration.importer - INFO - 已处理 90 批关系，导入 12774 个关系
2025-07-09 10:27:20,032 - neo4j_integration.importer - INFO - 已处理 100 批关系，导入 14360 个关系
2025-07-09 10:27:20,264 - neo4j_integration.importer - INFO - 已处理 110 批关系，导入 16048 个关系
2025-07-09 10:27:20,486 - neo4j_integration.importer - INFO - 已处理 120 批关系，导入 17529 个关系
2025-07-09 10:27:20,709 - neo4j_integration.importer - INFO - 已处理 130 批关系，导入 19185 个关系
2025-07-09 10:27:20,930 - neo4j_integration.importer - INFO - 已处理 140 批关系，导入 20759 个关系
2025-07-09 10:27:21,127 - neo4j_integration.importer - INFO - 已处理 150 批关系，导入 22198 个关系
2025-07-09 10:27:21,350 - neo4j_integration.importer - INFO - 已处理 160 批关系，导入 23647 个关系
2025-07-09 10:27:21,574 - neo4j_integration.importer - INFO - 已处理 170 批关系，导入 25004 个关系
2025-07-09 10:27:21,801 - neo4j_integration.importer - INFO - 已处理 180 批关系，导入 26359 个关系
2025-07-09 10:27:22,032 - neo4j_integration.importer - INFO - 已处理 190 批关系，导入 27971 个关系
2025-07-09 10:27:22,259 - neo4j_integration.importer - INFO - 已处理 200 批关系，导入 29469 个关系
2025-07-09 10:27:22,485 - neo4j_integration.importer - INFO - 已处理 210 批关系，导入 30957 个关系
2025-07-09 10:27:22,702 - neo4j_integration.importer - INFO - 已处理 220 批关系，导入 31902 个关系
2025-07-09 10:27:22,935 - neo4j_integration.importer - INFO - 已处理 230 批关系，导入 33265 个关系
2025-07-09 10:27:23,160 - neo4j_integration.importer - INFO - 已处理 240 批关系，导入 34649 个关系
2025-07-09 10:27:23,384 - neo4j_integration.importer - INFO - 已处理 250 批关系，导入 36060 个关系
2025-07-09 10:27:23,596 - neo4j_integration.importer - INFO - 已处理 260 批关系，导入 37024 个关系
2025-07-09 10:27:23,820 - neo4j_integration.importer - INFO - 已处理 270 批关系，导入 38749 个关系
2025-07-09 10:27:24,042 - neo4j_integration.importer - INFO - 已处理 280 批关系，导入 40596 个关系
2025-07-09 10:27:24,272 - neo4j_integration.importer - INFO - 已处理 290 批关系，导入 42330 个关系
2025-07-09 10:27:24,499 - neo4j_integration.importer - INFO - 已处理 300 批关系，导入 43966 个关系
2025-07-09 10:27:24,714 - neo4j_integration.importer - INFO - 已处理 310 批关系，导入 45225 个关系
2025-07-09 10:27:24,934 - neo4j_integration.importer - INFO - 已处理 320 批关系，导入 46537 个关系
2025-07-09 10:27:25,161 - neo4j_integration.importer - INFO - 已处理 330 批关系，导入 48009 个关系
2025-07-09 10:27:25,383 - neo4j_integration.importer - INFO - 已处理 340 批关系，导入 49330 个关系
2025-07-09 10:27:25,603 - neo4j_integration.importer - INFO - 已处理 350 批关系，导入 50730 个关系
2025-07-09 10:27:25,827 - neo4j_integration.importer - INFO - 已处理 360 批关系，导入 52099 个关系
2025-07-09 10:27:26,036 - neo4j_integration.importer - INFO - 已处理 370 批关系，导入 53403 个关系
2025-07-09 10:27:26,252 - neo4j_integration.importer - INFO - 已处理 380 批关系，导入 54961 个关系
2025-07-09 10:27:26,462 - neo4j_integration.importer - INFO - 已处理 390 批关系，导入 56391 个关系
2025-07-09 10:27:26,675 - neo4j_integration.importer - INFO - 已处理 400 批关系，导入 57719 个关系
2025-07-09 10:27:26,891 - neo4j_integration.importer - INFO - 已处理 410 批关系，导入 59155 个关系
2025-07-09 10:27:27,109 - neo4j_integration.importer - INFO - 已处理 420 批关系，导入 60439 个关系
2025-07-09 10:27:27,322 - neo4j_integration.importer - INFO - 已处理 430 批关系，导入 61931 个关系
2025-07-09 10:27:27,634 - neo4j_integration.importer - INFO - 已处理 440 批关系，导入 63428 个关系
2025-07-09 10:27:27,854 - neo4j_integration.importer - INFO - 已处理 450 批关系，导入 64823 个关系
2025-07-09 10:27:28,063 - neo4j_integration.importer - INFO - 已处理 460 批关系，导入 66065 个关系
2025-07-09 10:27:28,286 - neo4j_integration.importer - INFO - 已处理 470 批关系，导入 67891 个关系
2025-07-09 10:27:28,519 - neo4j_integration.importer - INFO - 已处理 480 批关系，导入 69661 个关系
2025-07-09 10:27:28,739 - neo4j_integration.importer - INFO - 已处理 490 批关系，导入 71622 个关系
2025-07-09 10:27:28,960 - neo4j_integration.importer - INFO - 已处理 500 批关系，导入 73457 个关系
2025-07-09 10:27:29,193 - neo4j_integration.importer - INFO - 已处理 510 批关系，导入 75101 个关系
2025-07-09 10:27:29,380 - neo4j_integration.importer - INFO - ✅ 关系导入完成: 76079 成功, 0 失败
2025-07-09 10:27:29,380 - neo4j_integration.importer - INFO - 🎉 数据导入完成!
2025-07-09 10:27:29,381 - neo4j_integration.importer - INFO - 📊 导入统计:
2025-07-09 10:27:29,381 - neo4j_integration.importer - INFO -    节点: 80549 成功, 0 失败
2025-07-09 10:27:29,381 - neo4j_integration.importer - INFO -    关系: 76079 成功, 0 失败
2025-07-09 10:27:29,381 - neo4j_integration.importer - INFO -    耗时: 15.26 秒
2025-07-09 10:27:29,381 - __main__ - INFO - 🔍 验证导入数据...
2025-07-09 10:27:29,381 - neo4j_integration.validator - INFO - 🔍 开始数据完整性验证...
2025-07-09 10:27:29,381 - neo4j_integration.validator - INFO - 📊 收集Neo4j统计数据...
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - ✅ Neo4j统计数据收集完成
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - 🔍 比较统计数据...
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - ✅ 统计比较完成: 不匹配
2025-07-09 10:27:29,830 - neo4j_integration.validator - WARNING - ⚠️ 数据完整性验证未通过: 4 个问题
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - 📋 数据完整性验证报告
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - ============================================================
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - Neo4j统计:
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   节点总数: 80,549
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   关系总数: 76,079
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   跨仓库关系: 0
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - 
匹配度:
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   nodes: 0.0%
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   relationships: 0.0%
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   node_types: 0.0%
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO -   repositories: 0.0%
2025-07-09 10:27:29,830 - neo4j_integration.validator - INFO - 
发现的问题:
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   1. 节点数量不匹配
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   2. 关系数量不匹配
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   3. 节点类型分布不匹配
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   4. 仓库分布不匹配
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO - 
建议:
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   1. 检查节点导入过程是否有错误
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   2. 检查关系导入过程是否有错误
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   3. 检查节点类型映射是否正确
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO -   4. 检查仓库识别逻辑是否正确
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO - 
验证结果: ❌ 未通过
2025-07-09 10:27:29,831 - neo4j_integration.validator - INFO - ============================================================
2025-07-09 10:27:29,831 - __main__ - WARNING - 数据验证未完全通过，但继续执行
2025-07-09 10:27:29,831 - __main__ - INFO - 🔗 分析跨仓库关系...
2025-07-09 10:27:29,831 - neo4j_integration.cross_repo_analyzer - INFO - 🔍 分析跨仓库关系...
2025-07-09 10:27:30,173 - neo4j_integration.cross_repo_analyzer - INFO - ✅ 跨仓库关系分析完成: 0 个关系
2025-07-09 10:27:30,173 - neo4j_integration.cross_repo_analyzer - INFO - 📊 计算仓库耦合度指标...
2025-07-09 10:27:30,378 - neo4j_integration.cross_repo_analyzer - INFO - ✅ 仓库耦合度指标计算完成
2025-07-09 10:27:30,378 - neo4j_integration.cross_repo_analyzer - INFO - 🔍 验证跨仓库数据完整性...
2025-07-09 10:27:30,616 - neo4j_integration.cross_repo_analyzer - INFO - ✅ 跨仓库数据验证完成: 未通过
2025-07-09 10:27:30,616 - __main__ - INFO - 📊 运行示例查询...
2025-07-09 10:27:30,616 - neo4j_integration.queries - INFO - 🚀 运行完整的代码知识图谱分析...
2025-07-09 10:27:30,616 - neo4j_integration.queries - INFO - 📊 执行基础统计查询...
2025-07-09 10:27:30,693 - neo4j_integration.queries - INFO - 🔗 执行跨仓库分析查询...
2025-07-09 10:27:30,887 - neo4j_integration.queries - INFO - 🏗️ 执行代码结构分析查询...
2025-07-09 10:27:31,184 - neo4j_integration.queries - INFO - 🔍 执行依赖分析查询...
2025-07-09 10:27:31,345 - neo4j_integration.queries - INFO - 🏛️ 执行架构洞察查询...
2025-07-09 10:27:31,501 - neo4j_integration.queries - ERROR - 查询 hub_nodes 失败: {code: Neo.ClientError.Statement.SyntaxError} {message: A pattern expression should only be used in order to test the existence of a pattern. It can no longer be used inside the function size(), an alternative is to replace size() with COUNT {}. (line 5, column 27 (offset: 164))
"                     size((n)-[:RELATED_TO]->()) as outgoing_degree,"
                           ^}
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - ✅ 所有分析查询完成
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - 📋 代码知识图谱分析摘要
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - ============================================================
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - 总节点数: 80,549
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - 总关系数: 76,079
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - 仓库数量: 11
2025-07-09 10:27:31,560 - neo4j_integration.queries - INFO - ============================================================
2025-07-09 10:27:31,560 - __main__ - INFO - 🎉 Neo4j集成完成!
2025-07-09 10:27:31,560 - __main__ - INFO - ⏱️ 总耗时: 18.85 秒
2025-07-09 10:27:31,560 - __main__ - INFO - 📋 Neo4j集成最终摘要
2025-07-09 10:27:31,561 - __main__ - INFO - ============================================================
2025-07-09 10:27:31,582 - __main__ - INFO - 数据库统计:
2025-07-09 10:27:31,582 - __main__ - INFO -   节点数量: 80,549
2025-07-09 10:27:31,582 - __main__ - INFO -   关系数量: 76,079
2025-07-09 10:27:31,582 - __main__ - INFO -   节点标签: 5
2025-07-09 10:27:31,582 - __main__ - INFO -   关系类型: 1
2025-07-09 10:27:31,582 - __main__ - INFO - 
连接信息:
2025-07-09 10:27:31,582 - __main__ - INFO -   URI: bolt://localhost:7687
2025-07-09 10:27:31,582 - __main__ - INFO -   数据库: neo4j
2025-07-09 10:27:31,582 - __main__ - INFO - 
数据文件:
2025-07-09 10:27:31,582 - __main__ - INFO -   节点文件: ast_out_index_all/nodes_all.jsonl
2025-07-09 10:27:31,582 - __main__ - INFO -   边文件: ast_out_index_all/edges_all.jsonl
2025-07-09 10:27:31,582 - __main__ - INFO - ============================================================
2025-07-09 10:27:31,582 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:27:31,582 - __main__ - INFO - 🧹 资源清理完成
2025-07-09 10:36:52,892 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:36:52,904 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 10:36:52,921 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:36:57,288 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:36:57,292 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 10:36:57,292 - neo4j_integration.schema - INFO - 🧹 开始清理Neo4j数据库...
2025-07-09 10:36:57,292 - neo4j_integration.schema - INFO - 删除所有关系...
2025-07-09 10:36:59,023 - neo4j_integration.schema - INFO - 删除所有节点...
2025-07-09 10:37:00,109 - neo4j_integration.schema - INFO - ✅ 数据库清理完成
2025-07-09 10:37:00,109 - neo4j_integration.schema - INFO - 🏗️ 创建Neo4j数据库模式...
2025-07-09 10:37:00,131 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE CONSTRAINT node_id_unique IF NOT EXISTS FOR (e:CodeNode) REQUIRE (e.id) IS UNIQUE` has no effect.} {description: `CONSTRAINT node_id_unique FOR (e:CodeNode) REQUIRE (e.id) IS UNIQUE` already exists.} {position: None} for query: 'CREATE CONSTRAINT node_id_unique IF NOT EXISTS FOR (n:CodeNode) REQUIRE n.id IS UNIQUE'
2025-07-09 10:37:00,138 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX node_name_index IF NOT EXISTS FOR (e:CodeNode) ON (e.name)` has no effect.} {description: `RANGE INDEX node_name_index FOR (e:CodeNode) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX node_name_index IF NOT EXISTS FOR (n:CodeNode) ON (n.name)'
2025-07-09 10:37:00,144 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX node_file_index IF NOT EXISTS FOR (e:CodeNode) ON (e.file)` has no effect.} {description: `RANGE INDEX node_file_index FOR (e:CodeNode) ON (e.file)` already exists.} {position: None} for query: 'CREATE INDEX node_file_index IF NOT EXISTS FOR (n:CodeNode) ON (n.file)'
2025-07-09 10:37:00,148 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX node_repo_index IF NOT EXISTS FOR (e:CodeNode) ON (e.repository)` has no effect.} {description: `RANGE INDEX node_repo_index FOR (e:CodeNode) ON (e.repository)` already exists.} {position: None} for query: 'CREATE INDEX node_repo_index IF NOT EXISTS FOR (n:CodeNode) ON (n.repository)'
2025-07-09 10:37:00,153 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX node_type_index IF NOT EXISTS FOR (e:CodeNode) ON (e.node_type)` has no effect.} {description: `RANGE INDEX node_type_index FOR (e:CodeNode) ON (e.node_type)` already exists.} {position: None} for query: 'CREATE INDEX node_type_index IF NOT EXISTS FOR (n:CodeNode) ON (n.node_type)'
2025-07-09 10:37:00,158 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX file_path_index IF NOT EXISTS FOR (e:File) ON (e.file_path)` has no effect.} {description: `RANGE INDEX file_path_index FOR (e:File) ON (e.file_path)` already exists.} {position: None} for query: 'CREATE INDEX file_path_index IF NOT EXISTS FOR (f:File) ON (f.file_path)'
2025-07-09 10:37:00,163 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX class_name_index IF NOT EXISTS FOR (e:Class) ON (e.name)` has no effect.} {description: `RANGE INDEX class_name_index FOR (e:Class) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX class_name_index IF NOT EXISTS FOR (c:Class) ON (c.name)'
2025-07-09 10:37:00,167 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX method_name_index IF NOT EXISTS FOR (e:Method) ON (e.name)` has no effect.} {description: `RANGE INDEX method_name_index FOR (e:Method) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX method_name_index IF NOT EXISTS FOR (m:Method) ON (m.name)'
2025-07-09 10:37:00,170 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX method_signature_index IF NOT EXISTS FOR (e:Method) ON (e.signature)` has no effect.} {description: `RANGE INDEX method_signature_index FOR (e:Method) ON (e.signature)` already exists.} {position: None} for query: 'CREATE INDEX method_signature_index IF NOT EXISTS FOR (m:Method) ON (m.signature)'
2025-07-09 10:37:00,174 - neo4j.notifications - INFO - Received notification from DBMS server: {severity: INFORMATION} {code: Neo.ClientNotification.Schema.IndexOrConstraintAlreadyExists} {category: SCHEMA} {title: `CREATE RANGE INDEX property_name_index IF NOT EXISTS FOR (e:Property) ON (e.name)` has no effect.} {description: `RANGE INDEX property_name_index FOR (e:Property) ON (e.name)` already exists.} {position: None} for query: 'CREATE INDEX property_name_index IF NOT EXISTS FOR (p:Property) ON (p.name)'
2025-07-09 10:37:00,178 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 52 (offset: 51))
"CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)"
                                                    ^}
2025-07-09 10:37:00,180 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_source_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_source_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.source_repo)"
                                                           ^}
2025-07-09 10:37:00,182 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_target_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_target_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.target_repo)"
                                                           ^}
2025-07-09 10:37:00,183 - neo4j_integration.schema - INFO - ✅ 模式创建完成: 10/13 成功
2025-07-09 10:37:00,183 - neo4j_integration.importer - INFO - 🚀 开始完整数据导入...
2025-07-09 10:37:00,183 - neo4j_integration.importer - INFO - 📥 开始导入节点数据...
2025-07-09 10:37:00,183 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/nodes_all.jsonl
2025-07-09 10:37:09,604 - neo4j_integration.importer - INFO - 已处理 10 批节点，导入 10000 个节点
2025-07-09 10:37:38,988 - neo4j_integration.importer - INFO - 已处理 20 批节点，导入 20000 个节点
2025-07-09 10:38:29,753 - neo4j_integration.importer - INFO - 已处理 30 批节点，导入 30000 个节点
2025-07-09 10:39:40,545 - neo4j_integration.importer - INFO - 已处理 40 批节点，导入 40000 个节点
2025-07-09 10:41:13,665 - neo4j_integration.importer - INFO - 已处理 50 批节点，导入 50000 个节点
2025-07-09 10:43:07,528 - neo4j_integration.importer - INFO - 已处理 60 批节点，导入 60000 个节点
2025-07-09 10:45:21,143 - neo4j_integration.importer - INFO - 已处理 70 批节点，导入 70000 个节点
2025-07-09 10:47:59,130 - neo4j_integration.importer - INFO - 已处理 80 批节点，导入 80000 个节点
2025-07-09 10:48:06,236 - neo4j_integration.importer - INFO - ✅ 节点导入完成: 80549 成功, 0 失败
2025-07-09 10:48:06,236 - neo4j_integration.importer - INFO - 🔗 开始导入关系数据...
2025-07-09 10:48:06,236 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/edges_all.jsonl
2025-07-09 10:48:06,844 - neo4j_integration.importer - INFO - 已处理 10 批关系，导入 1770 个关系
2025-07-09 10:48:07,110 - neo4j_integration.importer - INFO - 已处理 20 批关系，导入 3247 个关系
2025-07-09 10:48:07,331 - neo4j_integration.importer - INFO - 已处理 30 批关系，导入 4580 个关系
2025-07-09 10:48:07,559 - neo4j_integration.importer - INFO - 已处理 40 批关系，导入 6202 个关系
2025-07-09 10:48:07,794 - neo4j_integration.importer - INFO - 已处理 50 批关系，导入 7558 个关系
2025-07-09 10:48:08,015 - neo4j_integration.importer - INFO - 已处理 60 批关系，导入 8861 个关系
2025-07-09 10:48:08,238 - neo4j_integration.importer - INFO - 已处理 70 批关系，导入 10535 个关系
2025-07-09 10:48:08,443 - neo4j_integration.importer - INFO - 已处理 80 批关系，导入 11552 个关系
2025-07-09 10:48:08,671 - neo4j_integration.importer - INFO - 已处理 90 批关系，导入 12774 个关系
2025-07-09 10:48:08,895 - neo4j_integration.importer - INFO - 已处理 100 批关系，导入 14360 个关系
2025-07-09 10:48:09,117 - neo4j_integration.importer - INFO - 已处理 110 批关系，导入 16048 个关系
2025-07-09 10:48:09,334 - neo4j_integration.importer - INFO - 已处理 120 批关系，导入 17529 个关系
2025-07-09 10:48:09,575 - neo4j_integration.importer - INFO - 已处理 130 批关系，导入 19185 个关系
2025-07-09 10:48:09,798 - neo4j_integration.importer - INFO - 已处理 140 批关系，导入 20759 个关系
2025-07-09 10:48:09,991 - neo4j_integration.importer - INFO - 已处理 150 批关系，导入 22198 个关系
2025-07-09 10:48:10,206 - neo4j_integration.importer - INFO - 已处理 160 批关系，导入 23647 个关系
2025-07-09 10:48:10,421 - neo4j_integration.importer - INFO - 已处理 170 批关系，导入 25004 个关系
2025-07-09 10:48:10,637 - neo4j_integration.importer - INFO - 已处理 180 批关系，导入 26359 个关系
2025-07-09 10:48:10,862 - neo4j_integration.importer - INFO - 已处理 190 批关系，导入 27971 个关系
2025-07-09 10:48:11,081 - neo4j_integration.importer - INFO - 已处理 200 批关系，导入 29469 个关系
2025-07-09 10:48:11,296 - neo4j_integration.importer - INFO - 已处理 210 批关系，导入 30957 个关系
2025-07-09 10:48:11,509 - neo4j_integration.importer - INFO - 已处理 220 批关系，导入 31902 个关系
2025-07-09 10:48:11,784 - neo4j_integration.importer - INFO - 已处理 230 批关系，导入 33265 个关系
2025-07-09 10:48:12,001 - neo4j_integration.importer - INFO - 已处理 240 批关系，导入 34649 个关系
2025-07-09 10:48:12,220 - neo4j_integration.importer - INFO - 已处理 250 批关系，导入 36060 个关系
2025-07-09 10:48:12,425 - neo4j_integration.importer - INFO - 已处理 260 批关系，导入 37024 个关系
2025-07-09 10:48:12,675 - neo4j_integration.importer - INFO - 已处理 270 批关系，导入 38749 个关系
2025-07-09 10:48:12,931 - neo4j_integration.importer - INFO - 已处理 280 批关系，导入 40596 个关系
2025-07-09 10:48:13,156 - neo4j_integration.importer - INFO - 已处理 290 批关系，导入 42330 个关系
2025-07-09 10:48:13,389 - neo4j_integration.importer - INFO - 已处理 300 批关系，导入 43966 个关系
2025-07-09 10:48:13,608 - neo4j_integration.importer - INFO - 已处理 310 批关系，导入 45225 个关系
2025-07-09 10:48:13,858 - neo4j_integration.importer - INFO - 已处理 320 批关系，导入 46537 个关系
2025-07-09 10:48:14,083 - neo4j_integration.importer - INFO - 已处理 330 批关系，导入 48009 个关系
2025-07-09 10:48:14,310 - neo4j_integration.importer - INFO - 已处理 340 批关系，导入 49330 个关系
2025-07-09 10:48:14,534 - neo4j_integration.importer - INFO - 已处理 350 批关系，导入 50730 个关系
2025-07-09 10:48:14,755 - neo4j_integration.importer - INFO - 已处理 360 批关系，导入 52099 个关系
2025-07-09 10:48:14,969 - neo4j_integration.importer - INFO - 已处理 370 批关系，导入 53403 个关系
2025-07-09 10:48:15,192 - neo4j_integration.importer - INFO - 已处理 380 批关系，导入 54961 个关系
2025-07-09 10:48:15,424 - neo4j_integration.importer - INFO - 已处理 390 批关系，导入 56391 个关系
2025-07-09 10:48:15,652 - neo4j_integration.importer - INFO - 已处理 400 批关系，导入 57719 个关系
2025-07-09 10:48:15,872 - neo4j_integration.importer - INFO - 已处理 410 批关系，导入 59155 个关系
2025-07-09 10:48:16,089 - neo4j_integration.importer - INFO - 已处理 420 批关系，导入 60439 个关系
2025-07-09 10:48:16,312 - neo4j_integration.importer - INFO - 已处理 430 批关系，导入 61931 个关系
2025-07-09 10:48:16,533 - neo4j_integration.importer - INFO - 已处理 440 批关系，导入 63428 个关系
2025-07-09 10:48:16,751 - neo4j_integration.importer - INFO - 已处理 450 批关系，导入 64823 个关系
2025-07-09 10:48:16,963 - neo4j_integration.importer - INFO - 已处理 460 批关系，导入 66065 个关系
2025-07-09 10:48:17,186 - neo4j_integration.importer - INFO - 已处理 470 批关系，导入 67891 个关系
2025-07-09 10:48:17,410 - neo4j_integration.importer - INFO - 已处理 480 批关系，导入 69661 个关系
2025-07-09 10:48:17,634 - neo4j_integration.importer - INFO - 已处理 490 批关系，导入 71622 个关系
2025-07-09 10:48:17,860 - neo4j_integration.importer - INFO - 已处理 500 批关系，导入 73457 个关系
2025-07-09 10:48:18,090 - neo4j_integration.importer - INFO - 已处理 510 批关系，导入 75101 个关系
2025-07-09 10:48:18,302 - neo4j_integration.importer - INFO - ✅ 关系导入完成: 76079 成功, 0 失败
2025-07-09 10:48:18,302 - neo4j_integration.importer - INFO - 🎉 数据导入完成!
2025-07-09 10:48:18,302 - neo4j_integration.importer - INFO - 📊 导入统计:
2025-07-09 10:48:18,302 - neo4j_integration.importer - INFO -    节点: 80549 成功, 0 失败
2025-07-09 10:48:18,302 - neo4j_integration.importer - INFO -    关系: 76079 成功, 0 失败
2025-07-09 10:48:18,302 - neo4j_integration.importer - INFO -    耗时: 678.12 秒
2025-07-09 10:48:18,383 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:50:10,709 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:50:10,717 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 10:50:10,720 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 10:50:33,983 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 10:50:33,990 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 10:50:35,001 - neo4j_integration.importer - INFO - 🔗 开始导入关系数据...
2025-07-09 10:50:35,001 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/edges_all.jsonl
2025-07-09 10:50:35,874 - neo4j_integration.importer - INFO - 已处理 10 批关系，导入 10000 个关系
2025-07-09 10:50:36,355 - neo4j_integration.importer - INFO - 已处理 20 批关系，导入 20000 个关系
2025-07-09 10:50:36,821 - neo4j_integration.importer - INFO - 已处理 30 批关系，导入 30000 个关系
2025-07-09 10:50:37,287 - neo4j_integration.importer - INFO - 已处理 40 批关系，导入 40000 个关系
2025-07-09 10:50:37,739 - neo4j_integration.importer - INFO - 已处理 50 批关系，导入 50000 个关系
2025-07-09 10:50:38,185 - neo4j_integration.importer - INFO - 已处理 60 批关系，导入 60000 个关系
2025-07-09 10:50:38,636 - neo4j_integration.importer - INFO - 已处理 70 批关系，导入 70000 个关系
2025-07-09 10:50:39,083 - neo4j_integration.importer - INFO - 已处理 80 批关系，导入 80000 个关系
2025-07-09 10:50:39,565 - neo4j_integration.importer - INFO - 已处理 90 批关系，导入 90000 个关系
2025-07-09 10:50:40,062 - neo4j_integration.importer - INFO - 已处理 100 批关系，导入 100000 个关系
2025-07-09 10:50:40,531 - neo4j_integration.importer - INFO - 已处理 110 批关系，导入 110000 个关系
2025-07-09 10:50:41,010 - neo4j_integration.importer - INFO - 已处理 120 批关系，导入 120000 个关系
2025-07-09 10:50:41,480 - neo4j_integration.importer - INFO - 已处理 130 批关系，导入 130000 个关系
2025-07-09 10:50:41,968 - neo4j_integration.importer - INFO - 已处理 140 批关系，导入 140000 个关系
2025-07-09 10:50:42,386 - neo4j_integration.importer - INFO - 已处理 150 批关系，导入 150000 个关系
2025-07-09 10:50:42,821 - neo4j_integration.importer - INFO - 已处理 160 批关系，导入 160000 个关系
2025-07-09 10:50:43,305 - neo4j_integration.importer - INFO - 已处理 170 批关系，导入 170000 个关系
2025-07-09 10:50:43,774 - neo4j_integration.importer - INFO - 已处理 180 批关系，导入 180000 个关系
2025-07-09 10:50:44,267 - neo4j_integration.importer - INFO - 已处理 190 批关系，导入 190000 个关系
2025-07-09 10:50:44,719 - neo4j_integration.importer - INFO - 已处理 200 批关系，导入 200000 个关系
2025-07-09 10:50:45,189 - neo4j_integration.importer - INFO - 已处理 210 批关系，导入 210000 个关系
2025-07-09 10:50:45,631 - neo4j_integration.importer - INFO - 已处理 220 批关系，导入 220000 个关系
2025-07-09 10:50:46,230 - neo4j_integration.importer - INFO - 已处理 230 批关系，导入 230000 个关系
2025-07-09 10:50:46,701 - neo4j_integration.importer - INFO - 已处理 240 批关系，导入 240000 个关系
2025-07-09 10:50:47,173 - neo4j_integration.importer - INFO - 已处理 250 批关系，导入 250000 个关系
2025-07-09 10:50:47,635 - neo4j_integration.importer - INFO - 已处理 260 批关系，导入 260000 个关系
2025-07-09 10:50:48,197 - neo4j_integration.importer - INFO - 已处理 270 批关系，导入 270000 个关系
2025-07-09 10:50:48,784 - neo4j_integration.importer - INFO - 已处理 280 批关系，导入 280000 个关系
2025-07-09 10:50:49,314 - neo4j_integration.importer - INFO - 已处理 290 批关系，导入 290000 个关系
2025-07-09 10:50:49,829 - neo4j_integration.importer - INFO - 已处理 300 批关系，导入 300000 个关系
2025-07-09 10:50:50,341 - neo4j_integration.importer - INFO - 已处理 310 批关系，导入 310000 个关系
2025-07-09 10:50:50,926 - neo4j_integration.importer - INFO - 已处理 320 批关系，导入 320000 个关系
2025-07-09 10:50:51,472 - neo4j_integration.importer - INFO - 已处理 330 批关系，导入 330000 个关系
2025-07-09 10:50:51,973 - neo4j_integration.importer - INFO - 已处理 340 批关系，导入 340000 个关系
2025-07-09 10:50:52,494 - neo4j_integration.importer - INFO - 已处理 350 批关系，导入 350000 个关系
2025-07-09 10:50:53,033 - neo4j_integration.importer - INFO - 已处理 360 批关系，导入 360000 个关系
2025-07-09 10:50:53,536 - neo4j_integration.importer - INFO - 已处理 370 批关系，导入 370000 个关系
2025-07-09 10:50:54,066 - neo4j_integration.importer - INFO - 已处理 380 批关系，导入 380000 个关系
2025-07-09 10:50:54,654 - neo4j_integration.importer - INFO - 已处理 390 批关系，导入 390000 个关系
2025-07-09 10:50:55,216 - neo4j_integration.importer - INFO - 已处理 400 批关系，导入 400000 个关系
2025-07-09 10:50:55,772 - neo4j_integration.importer - INFO - 已处理 410 批关系，导入 410000 个关系
2025-07-09 10:50:56,296 - neo4j_integration.importer - INFO - 已处理 420 批关系，导入 420000 个关系
2025-07-09 10:50:56,847 - neo4j_integration.importer - INFO - 已处理 430 批关系，导入 430000 个关系
2025-07-09 10:50:57,377 - neo4j_integration.importer - INFO - 已处理 440 批关系，导入 440000 个关系
2025-07-09 10:50:57,931 - neo4j_integration.importer - INFO - 已处理 450 批关系，导入 450000 个关系
2025-07-09 10:50:58,470 - neo4j_integration.importer - INFO - 已处理 460 批关系，导入 460000 个关系
2025-07-09 10:50:59,021 - neo4j_integration.importer - INFO - 已处理 470 批关系，导入 470000 个关系
2025-07-09 10:50:59,540 - neo4j_integration.importer - INFO - 已处理 480 批关系，导入 480000 个关系
2025-07-09 10:51:00,050 - neo4j_integration.importer - INFO - 已处理 490 批关系，导入 490000 个关系
2025-07-09 10:51:00,624 - neo4j_integration.importer - INFO - 已处理 500 批关系，导入 500000 个关系
2025-07-09 10:51:01,166 - neo4j_integration.importer - INFO - 已处理 510 批关系，导入 510000 个关系
2025-07-09 10:51:01,719 - neo4j_integration.importer - INFO - ✅ 关系导入完成: 518692 成功, 0 失败
2025-07-09 10:51:01,727 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-09 11:00:56,828 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-09 11:00:56,836 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-09 11:00:56,888 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-10 15:43:50,500 - __main__ - INFO - 🚀 开始Pipeline-All Neo4j完整集成...
2025-07-10 15:43:50,500 - __main__ - INFO - 🚀 初始化Neo4j集成组件...
2025-07-10 15:43:50,501 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-10 15:43:50,505 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-10 15:43:50,506 - __main__ - INFO - ✅ 组件初始化完成
2025-07-10 15:43:50,506 - __main__ - INFO - 🛠️ 准备Neo4j数据库...
2025-07-10 15:43:50,506 - neo4j_integration.schema - INFO - 🚀 开始准备Neo4j数据库...
2025-07-10 15:43:50,506 - neo4j_integration.schema - INFO - 🗑️ 删除现有约束和索引...
2025-07-10 15:43:50,612 - neo4j_integration.schema - INFO - ✅ 约束和索引删除完成
2025-07-10 15:43:50,612 - neo4j_integration.schema - INFO - 🧹 开始清理Neo4j数据库...
2025-07-10 15:43:50,612 - neo4j_integration.schema - INFO - 删除所有关系...
2025-07-10 15:43:57,423 - neo4j_integration.schema - INFO - 删除所有节点...
2025-07-10 15:43:58,601 - neo4j_integration.schema - INFO - ✅ 数据库清理完成
2025-07-10 15:43:58,601 - neo4j_integration.schema - INFO - 🏗️ 创建Neo4j数据库模式...
2025-07-10 15:43:58,921 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 52 (offset: 51))
"CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)"
                                                    ^}
2025-07-10 15:43:58,924 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_source_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_source_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.source_repo)"
                                                           ^}
2025-07-10 15:43:58,929 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_target_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_target_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.target_repo)"
                                                           ^}
2025-07-10 15:43:58,930 - neo4j_integration.schema - INFO - ✅ 模式创建完成: 10/13 成功
2025-07-10 15:43:58,930 - neo4j_integration.schema - WARNING - 模式创建部分失败，但继续执行
2025-07-10 15:43:58,930 - neo4j_integration.schema - INFO - 🔍 验证数据库模式...
2025-07-10 15:43:59,002 - neo4j_integration.schema - INFO - 📊 模式验证完成:
2025-07-10 15:43:59,002 - neo4j_integration.schema - INFO -    约束数量: 1
2025-07-10 15:43:59,002 - neo4j_integration.schema - INFO -    索引数量: 10
2025-07-10 15:43:59,002 - neo4j_integration.schema - INFO -    节点标签: 5
2025-07-10 15:43:59,002 - neo4j_integration.schema - INFO -    关系类型: 0
2025-07-10 15:43:59,002 - neo4j_integration.schema - INFO - ✅ 数据库准备完成
2025-07-10 15:43:59,002 - __main__ - INFO - 📥 开始数据导入...
2025-07-10 15:43:59,002 - __main__ - INFO - 节点文件: ast_out_index_all/nodes_all.jsonl (24.9 MB)
2025-07-10 15:43:59,002 - __main__ - INFO - 边文件: ast_out_index_all/edges_all.jsonl (95.5 MB)
2025-07-10 15:43:59,002 - neo4j_integration.importer - INFO - 🚀 开始完整数据导入...
2025-07-10 15:43:59,002 - neo4j_integration.importer - INFO - 📥 开始导入节点数据...
2025-07-10 15:43:59,002 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/nodes_all.jsonl
2025-07-10 15:44:07,893 - neo4j_integration.importer - INFO - 已处理 10 批节点，导入 10000 个节点
2025-07-10 15:44:37,395 - neo4j_integration.importer - INFO - 已处理 20 批节点，导入 20000 个节点
2025-07-10 15:45:28,901 - neo4j_integration.importer - INFO - 已处理 30 批节点，导入 30000 个节点
2025-07-10 15:46:40,815 - neo4j_integration.importer - INFO - 已处理 40 批节点，导入 40000 个节点
2025-07-10 15:48:15,620 - neo4j_integration.importer - INFO - 已处理 50 批节点，导入 50000 个节点
2025-07-10 15:54:05,819 - __main__ - INFO - 🚀 开始Pipeline-All Neo4j完整集成...
2025-07-10 15:54:05,820 - __main__ - INFO - 🚀 初始化Neo4j集成组件...
2025-07-10 15:54:05,820 - neo4j_integration.connection - INFO - 连接到Neo4j: bolt://localhost:7687
2025-07-10 15:54:05,829 - neo4j_integration.connection - INFO - ✅ Neo4j连接成功
2025-07-10 15:54:05,829 - __main__ - INFO - ✅ 组件初始化完成
2025-07-10 15:54:05,829 - __main__ - INFO - 🛠️ 准备Neo4j数据库...
2025-07-10 15:54:05,829 - neo4j_integration.schema - INFO - 🚀 开始准备Neo4j数据库...
2025-07-10 15:54:05,829 - neo4j_integration.schema - INFO - 🗑️ 删除现有约束和索引...
2025-07-10 15:54:05,913 - neo4j_integration.schema - INFO - ✅ 约束和索引删除完成
2025-07-10 15:54:05,913 - neo4j_integration.schema - INFO - 🧹 开始清理Neo4j数据库...
2025-07-10 15:54:05,914 - neo4j_integration.schema - INFO - 删除所有关系...
2025-07-10 15:54:05,923 - neo4j_integration.schema - INFO - 删除所有节点...
2025-07-10 15:54:05,947 - neo4j_integration.schema - INFO - ✅ 数据库清理完成
2025-07-10 15:54:05,947 - neo4j_integration.schema - INFO - 🏗️ 创建Neo4j数据库模式...
2025-07-10 15:54:06,190 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 52 (offset: 51))
"CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)"
                                                    ^}
2025-07-10 15:54:06,193 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_source_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_source_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.source_repo)"
                                                           ^}
2025-07-10 15:54:06,197 - neo4j_integration.schema - WARNING - 创建失败: CREATE INDEX rel_target_repo_index IF NOT EXISTS F... - {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ']': expected ':' (line 1, column 59 (offset: 58))
"CREATE INDEX rel_target_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.target_repo)"
                                                           ^}
2025-07-10 15:54:06,200 - neo4j_integration.schema - INFO - ✅ 模式创建完成: 10/13 成功
2025-07-10 15:54:06,201 - neo4j_integration.schema - WARNING - 模式创建部分失败，但继续执行
2025-07-10 15:54:06,201 - neo4j_integration.schema - INFO - 🔍 验证数据库模式...
2025-07-10 15:54:06,303 - neo4j_integration.schema - INFO - 📊 模式验证完成:
2025-07-10 15:54:06,303 - neo4j_integration.schema - INFO -    约束数量: 1
2025-07-10 15:54:06,303 - neo4j_integration.schema - INFO -    索引数量: 10
2025-07-10 15:54:06,303 - neo4j_integration.schema - INFO -    节点标签: 5
2025-07-10 15:54:06,303 - neo4j_integration.schema - INFO -    关系类型: 0
2025-07-10 15:54:06,303 - neo4j_integration.schema - INFO - ✅ 数据库准备完成
2025-07-10 15:54:06,303 - __main__ - INFO - 📥 开始数据导入...
2025-07-10 15:54:06,303 - __main__ - INFO - 节点文件: ast_out_index_all/nodes_all.jsonl (24.9 MB)
2025-07-10 15:54:06,304 - __main__ - INFO - 边文件: ast_out_index_all/edges_all.jsonl (95.5 MB)
2025-07-10 15:54:06,304 - neo4j_integration.importer - INFO - 🚀 开始完整数据导入...
2025-07-10 15:54:06,304 - neo4j_integration.importer - INFO - 📥 开始导入节点数据...
2025-07-10 15:54:06,304 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/nodes_all.jsonl
2025-07-10 15:54:14,699 - neo4j_integration.importer - INFO - 已处理 10 批节点，导入 10000 个节点
2025-07-10 15:54:43,921 - neo4j_integration.importer - INFO - 已处理 20 批节点，导入 20000 个节点
2025-07-10 15:55:35,697 - neo4j_integration.importer - INFO - 已处理 30 批节点，导入 30000 个节点
2025-07-10 15:56:47,514 - neo4j_integration.importer - INFO - 已处理 40 批节点，导入 40000 个节点
2025-07-10 15:58:22,904 - neo4j_integration.importer - INFO - 已处理 50 批节点，导入 50000 个节点
2025-07-10 16:00:16,510 - neo4j_integration.importer - INFO - 已处理 60 批节点，导入 60000 个节点
2025-07-10 16:02:33,965 - neo4j_integration.importer - INFO - 已处理 70 批节点，导入 70000 个节点
2025-07-10 16:05:18,576 - neo4j_integration.importer - INFO - 已处理 80 批节点，导入 80000 个节点
2025-07-10 16:05:33,174 - neo4j_integration.importer - INFO - ✅ 节点导入完成: 81268 成功, 0 失败
2025-07-10 16:05:33,174 - neo4j_integration.importer - INFO - 🔗 开始导入关系数据...
2025-07-10 16:05:33,174 - neo4j_integration.importer - INFO - 📖 读取文件: ast_out_index_all/edges_all.jsonl
2025-07-10 16:05:33,930 - neo4j_integration.importer - INFO - 已处理 10 批关系，导入 10000 个关系
2025-07-10 16:05:34,501 - neo4j_integration.importer - INFO - 已处理 20 批关系，导入 20000 个关系
2025-07-10 16:05:34,955 - neo4j_integration.importer - INFO - 已处理 30 批关系，导入 30000 个关系
2025-07-10 16:05:35,423 - neo4j_integration.importer - INFO - 已处理 40 批关系，导入 40000 个关系
2025-07-10 16:05:35,868 - neo4j_integration.importer - INFO - 已处理 50 批关系，导入 50000 个关系
2025-07-10 16:05:36,324 - neo4j_integration.importer - INFO - 已处理 60 批关系，导入 60000 个关系
2025-07-10 16:05:36,785 - neo4j_integration.importer - INFO - 已处理 70 批关系，导入 70000 个关系
2025-07-10 16:05:37,277 - neo4j_integration.importer - INFO - 已处理 80 批关系，导入 80000 个关系
2025-07-10 16:05:37,771 - neo4j_integration.importer - INFO - 已处理 90 批关系，导入 90000 个关系
2025-07-10 16:05:38,252 - neo4j_integration.importer - INFO - 已处理 100 批关系，导入 100000 个关系
2025-07-10 16:05:38,742 - neo4j_integration.importer - INFO - 已处理 110 批关系，导入 110000 个关系
2025-07-10 16:05:39,238 - neo4j_integration.importer - INFO - 已处理 120 批关系，导入 120000 个关系
2025-07-10 16:05:39,722 - neo4j_integration.importer - INFO - 已处理 130 批关系，导入 130000 个关系
2025-07-10 16:05:40,205 - neo4j_integration.importer - INFO - 已处理 140 批关系，导入 140000 个关系
2025-07-10 16:05:40,640 - neo4j_integration.importer - INFO - 已处理 150 批关系，导入 150000 个关系
2025-07-10 16:05:41,083 - neo4j_integration.importer - INFO - 已处理 160 批关系，导入 160000 个关系
2025-07-10 16:05:41,569 - neo4j_integration.importer - INFO - 已处理 170 批关系，导入 170000 个关系
2025-07-10 16:05:42,038 - neo4j_integration.importer - INFO - 已处理 180 批关系，导入 180000 个关系
2025-07-10 16:05:42,520 - neo4j_integration.importer - INFO - 已处理 190 批关系，导入 190000 个关系
2025-07-10 16:05:42,992 - neo4j_integration.importer - INFO - 已处理 200 批关系，导入 200000 个关系
2025-07-10 16:05:43,503 - neo4j_integration.importer - INFO - 已处理 210 批关系，导入 210000 个关系
2025-07-10 16:05:44,011 - neo4j_integration.importer - INFO - 已处理 220 批关系，导入 220000 个关系
2025-07-10 16:05:44,477 - neo4j_integration.importer - INFO - 已处理 230 批关系，导入 230000 个关系
2025-07-10 16:05:44,988 - neo4j_integration.importer - INFO - 已处理 240 批关系，导入 240000 个关系
2025-07-10 16:05:45,475 - neo4j_integration.importer - INFO - 已处理 250 批关系，导入 250000 个关系
2025-07-10 16:05:45,953 - neo4j_integration.importer - INFO - 已处理 260 批关系，导入 260000 个关系
2025-07-10 16:05:46,500 - neo4j_integration.importer - INFO - 已处理 270 批关系，导入 270000 个关系
2025-07-10 16:05:47,073 - neo4j_integration.importer - INFO - 已处理 280 批关系，导入 280000 个关系
2025-07-10 16:05:47,618 - neo4j_integration.importer - INFO - 已处理 290 批关系，导入 290000 个关系
2025-07-10 16:05:48,196 - neo4j_integration.importer - INFO - 已处理 300 批关系，导入 300000 个关系
2025-07-10 16:05:48,730 - neo4j_integration.importer - INFO - 已处理 310 批关系，导入 310000 个关系
2025-07-10 16:05:49,271 - neo4j_integration.importer - INFO - 已处理 320 批关系，导入 320000 个关系
2025-07-10 16:05:49,850 - neo4j_integration.importer - INFO - 已处理 330 批关系，导入 330000 个关系
2025-07-10 16:05:50,391 - neo4j_integration.importer - INFO - 已处理 340 批关系，导入 340000 个关系
2025-07-10 16:05:50,951 - neo4j_integration.importer - INFO - 已处理 350 批关系，导入 350000 个关系
2025-07-10 16:05:51,481 - neo4j_integration.importer - INFO - 已处理 360 批关系，导入 360000 个关系
2025-07-10 16:05:51,982 - neo4j_integration.importer - INFO - 已处理 370 批关系，导入 370000 个关系
2025-07-10 16:05:52,546 - neo4j_integration.importer - INFO - 已处理 380 批关系，导入 380000 个关系
2025-07-10 16:05:53,144 - neo4j_integration.importer - INFO - 已处理 390 批关系，导入 390000 个关系
2025-07-10 16:05:53,712 - neo4j_integration.importer - INFO - 已处理 400 批关系，导入 400000 个关系
2025-07-10 16:05:54,231 - neo4j_integration.importer - INFO - 已处理 410 批关系，导入 410000 个关系
2025-07-10 16:05:54,788 - neo4j_integration.importer - INFO - 已处理 420 批关系，导入 420000 个关系
2025-07-10 16:05:55,351 - neo4j_integration.importer - INFO - 已处理 430 批关系，导入 430000 个关系
2025-07-10 16:05:55,909 - neo4j_integration.importer - INFO - 已处理 440 批关系，导入 440000 个关系
2025-07-10 16:05:56,474 - neo4j_integration.importer - INFO - 已处理 450 批关系，导入 450000 个关系
2025-07-10 16:05:57,062 - neo4j_integration.importer - INFO - 已处理 460 批关系，导入 460000 个关系
2025-07-10 16:05:57,622 - neo4j_integration.importer - INFO - 已处理 470 批关系，导入 470000 个关系
2025-07-10 16:05:58,158 - neo4j_integration.importer - INFO - 已处理 480 批关系，导入 480000 个关系
2025-07-10 16:05:58,720 - neo4j_integration.importer - INFO - 已处理 490 批关系，导入 490000 个关系
2025-07-10 16:05:59,250 - neo4j_integration.importer - INFO - 已处理 500 批关系，导入 500000 个关系
2025-07-10 16:05:59,768 - neo4j_integration.importer - INFO - 已处理 510 批关系，导入 510000 个关系
2025-07-10 16:06:00,313 - neo4j_integration.importer - INFO - ✅ 关系导入完成: 518910 成功, 0 失败
2025-07-10 16:06:00,313 - neo4j_integration.importer - INFO - 🎉 数据导入完成!
2025-07-10 16:06:00,313 - neo4j_integration.importer - INFO - 📊 导入统计:
2025-07-10 16:06:00,313 - neo4j_integration.importer - INFO -    节点: 81268 成功, 0 失败
2025-07-10 16:06:00,313 - neo4j_integration.importer - INFO -    关系: 518910 成功, 0 失败
2025-07-10 16:06:00,313 - neo4j_integration.importer - INFO -    耗时: 714.01 秒
2025-07-10 16:06:00,313 - __main__ - INFO - 🔍 验证导入数据...
2025-07-10 16:06:00,313 - neo4j_integration.validator - INFO - 🔍 开始数据完整性验证...
2025-07-10 16:06:00,314 - neo4j_integration.validator - INFO - 📊 收集Neo4j统计数据...
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO - ✅ Neo4j统计数据收集完成
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO - 🔍 比较统计数据...
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO - ✅ 统计比较完成: 不匹配
2025-07-10 16:06:00,401 - neo4j_integration.validator - WARNING - ⚠️ 数据完整性验证未通过: 4 个问题
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO - 📋 数据完整性验证报告
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO - ============================================================
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO - Neo4j统计:
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO -   节点总数: 158,338
2025-07-10 16:06:00,401 - neo4j_integration.validator - INFO -   关系总数: 0
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   跨仓库关系: 0
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO - 
匹配度:
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   nodes: 0.0%
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   relationships: 0.0%
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   node_types: 0.0%
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   repositories: 0.0%
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO - 
发现的问题:
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   1. 节点数量不匹配
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   2. 关系数量不匹配
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   3. 节点类型分布不匹配
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   4. 仓库分布不匹配
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO - 
建议:
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   1. 检查节点导入过程是否有错误
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   2. 检查关系导入过程是否有错误
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   3. 检查节点类型映射是否正确
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO -   4. 检查仓库识别逻辑是否正确
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO - 
验证结果: ❌ 未通过
2025-07-10 16:06:00,402 - neo4j_integration.validator - INFO - ============================================================
2025-07-10 16:06:00,402 - __main__ - WARNING - 数据验证未完全通过，但继续执行
2025-07-10 16:06:00,402 - __main__ - INFO - 🔗 分析跨仓库关系...
2025-07-10 16:06:00,402 - neo4j_integration.cross_repo_analyzer - INFO - 🔍 分析跨仓库关系...
2025-07-10 16:06:00,466 - neo4j_integration.cross_repo_analyzer - INFO - ✅ 跨仓库关系分析完成: 0 个关系
2025-07-10 16:06:00,466 - neo4j_integration.cross_repo_analyzer - INFO - 📊 计算仓库耦合度指标...
2025-07-10 16:06:00,546 - neo4j_integration.cross_repo_analyzer - INFO - ✅ 仓库耦合度指标计算完成
2025-07-10 16:06:00,546 - neo4j_integration.cross_repo_analyzer - INFO - 🔍 验证跨仓库数据完整性...
2025-07-10 16:06:00,646 - neo4j_integration.cross_repo_analyzer - INFO - ✅ 跨仓库数据验证完成: 未通过
2025-07-10 16:06:00,646 - __main__ - INFO - 📊 运行示例查询...
2025-07-10 16:06:00,646 - neo4j_integration.queries - INFO - 🚀 运行完整的代码知识图谱分析...
2025-07-10 16:06:00,646 - neo4j_integration.queries - INFO - 📊 执行基础统计查询...
2025-07-10 16:06:00,710 - neo4j_integration.queries - INFO - 🔗 执行跨仓库分析查询...
2025-07-10 16:06:00,751 - neo4j_integration.queries - INFO - 🏗️ 执行代码结构分析查询...
2025-07-10 16:06:00,883 - neo4j_integration.queries - INFO - 🔍 执行依赖分析查询...
2025-07-10 16:06:00,920 - neo4j_integration.queries - INFO - 🏛️ 执行架构洞察查询...
2025-07-10 16:06:00,998 - neo4j_integration.queries - ERROR - 查询 hub_nodes 失败: {code: Neo.ClientError.Statement.SyntaxError} {message: A pattern expression should only be used in order to test the existence of a pattern. It can no longer be used inside the function size(), an alternative is to replace size() with COUNT {}. (line 5, column 27 (offset: 164))
"                     size((n)-[:RELATED_TO]->()) as outgoing_degree,"
                           ^}
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - ✅ 所有分析查询完成
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - 📋 代码知识图谱分析摘要
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - ============================================================
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - 总节点数: 158,338
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - 总关系数: 0
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - 仓库数量: 12
2025-07-10 16:06:01,065 - neo4j_integration.queries - INFO - ============================================================
2025-07-10 16:06:01,065 - __main__ - INFO - 🎉 Neo4j集成完成!
2025-07-10 16:06:01,065 - __main__ - INFO - ⏱️ 总耗时: 715.25 秒
2025-07-10 16:06:01,065 - __main__ - INFO - 📋 Neo4j集成最终摘要
2025-07-10 16:06:01,065 - __main__ - INFO - ============================================================
2025-07-10 16:06:01,083 - __main__ - INFO - 数据库统计:
2025-07-10 16:06:01,083 - __main__ - INFO -   节点数量: 158,338
2025-07-10 16:06:01,083 - __main__ - INFO -   关系数量: 518,910
2025-07-10 16:06:01,083 - __main__ - INFO -   节点标签: 8
2025-07-10 16:06:01,083 - __main__ - INFO -   关系类型: 8
2025-07-10 16:06:01,083 - __main__ - INFO - 
连接信息:
2025-07-10 16:06:01,083 - __main__ - INFO -   URI: bolt://localhost:7687
2025-07-10 16:06:01,083 - __main__ - INFO -   数据库: neo4j
2025-07-10 16:06:01,083 - __main__ - INFO - 
数据文件:
2025-07-10 16:06:01,083 - __main__ - INFO -   节点文件: ast_out_index_all/nodes_all.jsonl
2025-07-10 16:06:01,083 - __main__ - INFO -   边文件: ast_out_index_all/edges_all.jsonl
2025-07-10 16:06:01,083 - __main__ - INFO - ============================================================
2025-07-10 16:06:01,083 - neo4j_integration.connection - INFO - Neo4j连接已关闭
2025-07-10 16:06:01,083 - __main__ - INFO - 🧹 资源清理完成
