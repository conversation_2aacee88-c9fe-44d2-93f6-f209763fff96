"""
Neo4j数据导入器
高效处理大规模代码知识图谱数据导入
"""

import json
import logging
import time
from typing import Dict, List, Any, Iterator, Tuple
from pathlib import Path

from .connection import Neo4jConnection
from .config import Neo4jConfig, NODE_LABELS, RELATIONSHIP_TYPES

logger = logging.getLogger(__name__)


class Neo4jImporter:
    """Neo4j数据导入器"""
    
    def __init__(self, connection: Neo4jConnection, config: Neo4jConfig):
        self.connection = connection
        self.config = config
        self.import_stats = {
            'nodes_imported': 0,
            'relationships_imported': 0,
            'nodes_failed': 0,
            'relationships_failed': 0,
            'start_time': None,
            'end_time': None
        }
    
    def read_jsonl_file(self, file_path: str) -> Iterator[Dict[str, Any]]:
        """读取JSONL文件的生成器"""
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        logger.info(f"📖 读取文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            line_num = 0
            for line in f:
                line_num += 1
                line = line.strip()
                if not line:
                    continue
                
                try:
                    yield json.loads(line)
                except json.JSONDecodeError as e:
                    logger.warning(f"跳过无效JSON行 {line_num}: {e}")
                    continue
    
    def batch_iterator(self, items: Iterator[Dict[str, Any]], batch_size: int) -> Iterator[List[Dict[str, Any]]]:
        """将数据分批的生成器"""
        batch = []
        for item in items:
            batch.append(item)
            if len(batch) >= batch_size:
                yield batch
                batch = []
        
        if batch:  # 处理最后一批
            yield batch
    
    def prepare_node_data(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """准备节点数据用于Neo4j导入"""
        # 获取节点标签
        node_label = node.get('label', 'Unknown')
        neo4j_label = NODE_LABELS.get(node_label, 'CodeNode')
        
        # 准备节点属性
        attrs = node.get('attrs', {})
        
        # 识别仓库
        repository = self.identify_repository(attrs.get('file', ''))
        
        # 构建Neo4j节点数据
        node_data = {
            'id': node.get('id', ''),
            'name': attrs.get('name', ''),
            'node_type': node_label,
            'repository': repository,
            'labels': [neo4j_label, 'CodeNode']  # 多标签支持
        }
        
        # 添加所有原始属性
        for key, value in attrs.items():
            if key not in node_data:
                node_data[key] = value
        
        return node_data
    
    def prepare_relationship_data(self, edge: Dict[str, Any]) -> Dict[str, Any]:
        """准备关系数据用于Neo4j导入"""
        edge_label = edge.get('label', 'UNKNOWN')
        neo4j_rel_type = RELATIONSHIP_TYPES.get(edge_label, 'RELATED_TO')

        attrs = edge.get('attrs', {})
        target_id = edge.get('target', '')

        # 构建Neo4j关系数据
        rel_data = {
            'source_id': edge.get('source', ''),
            'target_id': target_id,
            'relationship_type': neo4j_rel_type,
            'edge_type': edge_label
        }

        # 为虚拟目标节点推断信息
        if target_id.startswith(('property_', 'method_', 'type_', 'class_', 'protocol_', 'module_')):
            # 解析虚拟节点信息
            if target_id.startswith('property_'):
                rel_data['target_type'] = 'Property'
                rel_data['target_name'] = target_id.replace('property_', '').replace('_', '.')
            elif target_id.startswith('method_'):
                rel_data['target_type'] = 'Method'
                rel_data['target_name'] = target_id.replace('method_', '').replace('_', '.')
            elif target_id.startswith('type_'):
                rel_data['target_type'] = 'Type'
                rel_data['target_name'] = target_id.replace('type_', '')
            elif target_id.startswith('class_'):
                rel_data['target_type'] = 'Class'
                rel_data['target_name'] = target_id.replace('class_', '')
            elif target_id.startswith('protocol_'):
                rel_data['target_type'] = 'Protocol'
                rel_data['target_name'] = target_id.replace('protocol_', '')
            elif target_id.startswith('module_'):
                rel_data['target_type'] = 'Module'
                rel_data['target_name'] = target_id.replace('module_', '').replace('_', '/')

            # 尝试从target_id推断仓库
            rel_data['target_repo'] = self.identify_virtual_target_repository(target_id, attrs)

        # 添加所有原始属性
        for key, value in attrs.items():
            rel_data[key] = value

        return rel_data

    def identify_virtual_target_repository(self, target_id: str, attrs: Dict[str, Any]) -> str:
        """识别虚拟目标节点的仓库"""
        # 从属性中获取仓库信息
        if 'target_repo' in attrs:
            return attrs['target_repo']

        # 从target_id中推断
        target_lower = target_id.lower()
        repo_patterns = {
            'QMapBusiness': ['qmapbusiness', 'business'],
            'QMapMiddlePlatform': ['qmapmiddleplatform', 'middleplatform'],
            'QMapNaviKit': ['qmapnavikit', 'navikit'],
            'DragonMapKit': ['dragonmapkit', 'dragon'],
            'QMapUIKit': ['qmapuikit', 'uikit'],
            'QMapFoundation': ['qmapfoundation', 'foundation'],
            'QMapBaseline': ['qmapbaseline', 'baseline'],
            'QMapHippy': ['qmaphippy', 'hippy'],
            'TencentMap': ['tencentmap', 'tencent']
        }

        for repo_name, patterns in repo_patterns.items():
            if any(pattern in target_lower for pattern in patterns):
                return repo_name

        return 'external'
    
    def identify_repository(self, file_path: str) -> str:
        """从文件路径识别仓库"""
        if not file_path:
            return 'unknown'
        
        # 检查文件路径中的仓库名称
        for repo in ['QMapBusiness', 'QMapMiddlePlatform', 'QMapNaviKit', 'DragonMapKit',
                     'QMapHippy', 'QMapFoundation', 'QMapBaseline', 'QMapUIKit',
                     'TencentMap', 'QMapRouteSearchKit', 'DKProtocolsPool']:
            if repo in file_path:
                return repo
        
        return 'unknown'
    
    def import_nodes_batch(self, nodes_batch: List[Dict[str, Any]]) -> Tuple[int, int]:
        """批量导入节点"""
        if not nodes_batch:
            return 0, 0

        try:
            prepared_nodes = [self.prepare_node_data(node) for node in nodes_batch]
            imported_count = 0

            # 按节点类型分组导入，为每种类型创建特定标签
            nodes_by_type = {}
            for node in prepared_nodes:
                node_type = node.get('node_type', 'Unknown')
                if node_type not in nodes_by_type:
                    nodes_by_type[node_type] = []
                nodes_by_type[node_type].append(node)

            with self.connection.session() as session:
                for node_type, type_nodes in nodes_by_type.items():
                    # 为每种节点类型创建特定的Cypher查询
                    neo4j_label = NODE_LABELS.get(node_type, 'CodeNode')

                    # 创建带有特定标签的查询
                    query = f"""
                    UNWIND $nodes as node
                    MERGE (n:{neo4j_label} {{id: node.id}})
                    SET n:CodeNode,
                        n.name = COALESCE(node.name, ''),
                        n.node_type = COALESCE(node.node_type, 'Unknown'),
                        n.repository = COALESCE(node.repository, 'unknown'),
                        n.file = COALESCE(node.file, ''),
                        n.line = COALESCE(node.line, 0),
                        n.type = COALESCE(node.type, ''),
                        n.signature = COALESCE(node.signature, ''),
                        n.declaration = COALESCE(node.declaration, '')
                    RETURN count(*) as imported_count
                    """

                    def _import_type_nodes(tx):
                        result = tx.run(query, {'nodes': type_nodes})
                        return result.single()['imported_count']

                    type_imported = session.execute_write(_import_type_nodes)
                    imported_count += type_imported
                    logger.debug(f"导入 {node_type} 节点: {type_imported} 个")

            return imported_count, 0
        except Exception as e:
            logger.error(f"批量导入节点失败: {e}")
            return 0, len(nodes_batch)
    
    def import_relationships_batch(self, relationships_batch: List[Dict[str, Any]]) -> Tuple[int, int]:
        """批量导入关系"""
        if not relationships_batch:
            return 0, 0

        try:
            prepared_rels = [self.prepare_relationship_data(rel) for rel in relationships_batch]
            imported_count = 0

            # 按关系类型分组导入，为每种类型创建特定关系
            rels_by_type = {}
            for rel in prepared_rels:
                rel_type = rel.get('relationship_type', 'RELATED_TO')
                if rel_type not in rels_by_type:
                    rels_by_type[rel_type] = []
                rels_by_type[rel_type].append(rel)

            with self.connection.session() as session:
                for rel_type, type_rels in rels_by_type.items():
                    # 为每种关系类型创建特定的Cypher查询
                    neo4j_rel_type = RELATIONSHIP_TYPES.get(rel_type, 'RELATED_TO')

                    # 创建带有虚拟节点支持的查询
                    query = f"""
                    UNWIND $relationships as rel
                    MATCH (source:CodeNode {{id: rel.source_id}})
                    MERGE (target:CodeNode {{id: rel.target_id}})
                    ON CREATE SET target.name = COALESCE(rel.target_name, rel.target_id),
                                  target.node_type = COALESCE(rel.target_type, 'Virtual'),
                                  target.repository = COALESCE(rel.target_repo, 'virtual'),
                                  target.is_virtual = true
                    CREATE (source)-[r:{neo4j_rel_type}]->(target)
                    SET r.relationship_type = COALESCE(rel.relationship_type, 'RELATED_TO'),
                        r.edge_type = COALESCE(rel.edge_type, ''),
                        r.type = COALESCE(rel.type, ''),
                        r.source_repo = COALESCE(rel.source_repo, ''),
                        r.target_repo = COALESCE(rel.target_repo, ''),
                        r.imported_module = COALESCE(rel.imported_module, ''),
                        r.target_object = COALESCE(rel.target_object, ''),
                        r.method = COALESCE(rel.method, ''),
                        r.protocol = COALESCE(rel.protocol, ''),
                        r.subclass = COALESCE(rel.subclass, ''),
                        r.superclass = COALESCE(rel.superclass, ''),
                        r.category = COALESCE(rel.category, ''),
                        r.object = COALESCE(rel.object, ''),
                        r.property = COALESCE(rel.property, ''),
                        r.variable = COALESCE(rel.variable, ''),
                        r.variable_type = COALESCE(rel.variable_type, ''),
                        r.return_type = COALESCE(rel.return_type, '')
                    RETURN count(*) as imported_count
                    """

                    def _import_type_relationships(tx):
                        result = tx.run(query, {'relationships': type_rels})
                        return result.single()['imported_count']

                    type_imported = session.execute_write(_import_type_relationships)
                    imported_count += type_imported
                    logger.debug(f"导入 {rel_type} 关系: {type_imported} 个")

            return imported_count, 0
        except Exception as e:
            logger.error(f"批量导入关系失败: {e}")
            return 0, len(relationships_batch)

    def import_nodes(self) -> bool:
        """导入所有节点"""
        logger.info("📥 开始导入节点数据...")
        self.import_stats['start_time'] = time.time()

        try:
            nodes_iter = self.read_jsonl_file(self.config.nodes_file)
            batch_count = 0

            for batch in self.batch_iterator(nodes_iter, self.config.batch_size):
                batch_count += 1
                imported, failed = self.import_nodes_batch(batch)

                self.import_stats['nodes_imported'] += imported
                self.import_stats['nodes_failed'] += failed

                if batch_count % 10 == 0:
                    logger.info(f"已处理 {batch_count} 批节点，导入 {self.import_stats['nodes_imported']} 个节点")

            logger.info(f"✅ 节点导入完成: {self.import_stats['nodes_imported']} 成功, {self.import_stats['nodes_failed']} 失败")
            return self.import_stats['nodes_failed'] == 0

        except Exception as e:
            logger.error(f"❌ 节点导入失败: {e}")
            return False

    def import_relationships(self) -> bool:
        """导入所有关系"""
        logger.info("🔗 开始导入关系数据...")

        try:
            edges_iter = self.read_jsonl_file(self.config.edges_file)
            batch_count = 0

            for batch in self.batch_iterator(edges_iter, self.config.batch_size):
                batch_count += 1
                imported, failed = self.import_relationships_batch(batch)

                self.import_stats['relationships_imported'] += imported
                self.import_stats['relationships_failed'] += failed

                if batch_count % 10 == 0:
                    logger.info(f"已处理 {batch_count} 批关系，导入 {self.import_stats['relationships_imported']} 个关系")

            logger.info(f"✅ 关系导入完成: {self.import_stats['relationships_imported']} 成功, {self.import_stats['relationships_failed']} 失败")
            return self.import_stats['relationships_failed'] == 0

        except Exception as e:
            logger.error(f"❌ 关系导入失败: {e}")
            return False

    def import_all(self) -> bool:
        """导入所有数据"""
        logger.info("🚀 开始完整数据导入...")

        # 导入节点
        if not self.import_nodes():
            logger.error("节点导入失败，停止导入")
            return False

        # 导入关系
        if not self.import_relationships():
            logger.error("关系导入失败")
            return False

        self.import_stats['end_time'] = time.time()
        duration = self.import_stats['end_time'] - self.import_stats['start_time']

        logger.info("🎉 数据导入完成!")
        logger.info(f"📊 导入统计:")
        logger.info(f"   节点: {self.import_stats['nodes_imported']} 成功, {self.import_stats['nodes_failed']} 失败")
        logger.info(f"   关系: {self.import_stats['relationships_imported']} 成功, {self.import_stats['relationships_failed']} 失败")
        logger.info(f"   耗时: {duration:.2f} 秒")

        return True

    def get_import_stats(self) -> Dict[str, Any]:
        """获取导入统计信息"""
        return self.import_stats.copy()
