"""
Neo4j集成主程序
Pipeline-All代码知识图谱的Neo4j导入和分析
"""

import logging
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from neo4j_integration.config import Neo4jConfig
from neo4j_integration.connection import Neo4jConnection
from neo4j_integration.schema import Neo4jSchemaManager
from neo4j_integration.importer import Neo4jImporter
from neo4j_integration.validator import Neo4jValidator
from neo4j_integration.cross_repo_analyzer import CrossRepoAnalyzer
from neo4j_integration.queries import CypherQueries

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('neo4j_integration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class Neo4jIntegrationManager:
    """Neo4j集成管理器"""
    
    def __init__(self):
        self.config = Neo4jConfig.from_env()
        self.connection = Neo4jConnection(self.config)
        self.schema_manager = None
        self.importer = None
        self.validator = None
        self.cross_repo_analyzer = None
        self.queries = None
    
    def initialize(self) -> bool:
        """初始化所有组件"""
        logger.info("🚀 初始化Neo4j集成组件...")
        
        # 建立连接
        if not self.connection.connect():
            logger.error("Neo4j连接失败")
            return False
        
        # 初始化组件
        self.schema_manager = Neo4jSchemaManager(self.connection)
        self.importer = Neo4jImporter(self.connection, self.config)
        self.validator = Neo4jValidator(self.connection, self.config)
        self.cross_repo_analyzer = CrossRepoAnalyzer(self.connection)
        self.queries = CypherQueries(self.connection)
        
        logger.info("✅ 组件初始化完成")
        return True
    
    def prepare_database(self) -> bool:
        """准备数据库"""
        logger.info("🛠️ 准备Neo4j数据库...")
        
        if not self.schema_manager:
            logger.error("Schema管理器未初始化")
            return False
        
        return self.schema_manager.prepare_database()
    
    def import_data(self) -> bool:
        """导入数据"""
        logger.info("📥 开始数据导入...")
        
        if not self.importer:
            logger.error("导入器未初始化")
            return False
        
        # 检查数据文件是否存在
        nodes_file = Path(self.config.nodes_file)
        edges_file = Path(self.config.edges_file)
        
        if not nodes_file.exists():
            logger.error(f"节点文件不存在: {nodes_file}")
            return False
        
        if not edges_file.exists():
            logger.error(f"边文件不存在: {edges_file}")
            return False
        
        logger.info(f"节点文件: {nodes_file} ({nodes_file.stat().st_size / 1024 / 1024:.1f} MB)")
        logger.info(f"边文件: {edges_file} ({edges_file.stat().st_size / 1024 / 1024:.1f} MB)")
        
        return self.importer.import_all()
    
    def validate_data(self) -> bool:
        """验证数据"""
        logger.info("🔍 验证导入数据...")
        
        if not self.validator:
            logger.error("验证器未初始化")
            return False
        
        validation_result = self.validator.validate_data_integrity()
        self.validator.print_validation_report(validation_result)
        
        return validation_result.get('validation_passed', False)
    
    def analyze_cross_repo_relationships(self):
        """分析跨仓库关系"""
        logger.info("🔗 分析跨仓库关系...")
        
        if not self.cross_repo_analyzer:
            logger.error("跨仓库分析器未初始化")
            return {}
        
        analysis = self.cross_repo_analyzer.analyze_cross_repo_relationships()
        coupling_metrics = self.cross_repo_analyzer.get_repository_coupling_metrics()
        validation = self.cross_repo_analyzer.validate_cross_repo_data()
        
        return {
            'analysis': analysis,
            'coupling_metrics': coupling_metrics,
            'validation': validation
        }
    
    def run_sample_queries(self):
        """运行示例查询"""
        logger.info("📊 运行示例查询...")
        
        if not self.queries:
            logger.error("查询器未初始化")
            return {}
        
        results = self.queries.run_all_analyses()
        self.queries.print_analysis_summary(results)
        
        return results
    
    def cleanup(self):
        """清理资源"""
        if self.connection:
            self.connection.disconnect()
        logger.info("🧹 资源清理完成")
    
    def run_full_integration(self) -> bool:
        """运行完整的集成流程"""
        logger.info("🚀 开始Pipeline-All Neo4j完整集成...")
        start_time = time.time()
        
        try:
            # 1. 初始化
            if not self.initialize():
                return False
            
            # 2. 准备数据库
            if not self.prepare_database():
                logger.error("数据库准备失败")
                return False
            
            # 3. 导入数据
            if not self.import_data():
                logger.error("数据导入失败")
                return False
            
            # 4. 验证数据
            if not self.validate_data():
                logger.warning("数据验证未完全通过，但继续执行")
            
            # 5. 分析跨仓库关系
            cross_repo_results = self.analyze_cross_repo_relationships()
            
            # 6. 运行示例查询
            query_results = self.run_sample_queries()
            
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info("🎉 Neo4j集成完成!")
            logger.info(f"⏱️ 总耗时: {duration:.2f} 秒")
            
            # 打印最终统计
            self.print_final_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 集成过程失败: {e}")
            return False
        finally:
            self.cleanup()
    
    def print_final_summary(self):
        """打印最终摘要"""
        logger.info("📋 Neo4j集成最终摘要")
        logger.info("=" * 60)
        
        try:
            db_info = self.connection.get_database_info()
            
            logger.info(f"数据库统计:")
            logger.info(f"  节点数量: {db_info.get('node_count', 0):,}")
            logger.info(f"  关系数量: {db_info.get('relationship_count', 0):,}")
            logger.info(f"  节点标签: {len(db_info.get('node_labels', []))}")
            logger.info(f"  关系类型: {len(db_info.get('relationship_types', []))}")
            
            logger.info(f"\n连接信息:")
            logger.info(f"  URI: {self.config.uri}")
            logger.info(f"  数据库: {self.config.database}")
            
            logger.info(f"\n数据文件:")
            logger.info(f"  节点文件: {self.config.nodes_file}")
            logger.info(f"  边文件: {self.config.edges_file}")
            
        except Exception as e:
            logger.error(f"获取摘要信息失败: {e}")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    print("🚀 启动Pipeline-All Neo4j集成...")
    print("这将清理现有数据并导入完整的代码知识图谱")
    print("预计耗时: 10-20分钟")
    print()
    
    # 确认执行
    confirm = input("确认执行? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消执行")
        return
    
    # 运行集成
    manager = Neo4jIntegrationManager()
    success = manager.run_full_integration()
    
    if success:
        print("\n🎉 Neo4j集成成功完成!")
        print("现在可以使用Neo4j Browser或其他工具查询代码知识图谱")
        print(f"连接地址: {manager.config.uri}")
    else:
        print("\n❌ Neo4j集成失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
