#!/usr/bin/env python3
"""
Neo4j集成演示脚本
展示如何使用Pipeline-All的Neo4j集成功能
"""

import sys
import json
from pathlib import Path

def show_data_overview():
    """显示数据概览"""
    print("📊 Pipeline-All代码知识图谱数据概览")
    print("=" * 60)
    
    # 检查数据文件
    nodes_file = Path("ast_out_index_all/nodes_all.jsonl")
    edges_file = Path("ast_out_index_all/edges_all.jsonl")
    stats_file = Path("ast_out_index_all/stats_all.json")
    
    if not nodes_file.exists():
        print("❌ 节点文件不存在，请先运行Pipeline-All生成数据")
        return False
    
    if not edges_file.exists():
        print("❌ 边文件不存在，请先运行Pipeline-All生成数据")
        return False
    
    # 显示文件信息
    print(f"📁 数据文件:")
    print(f"  节点文件: {nodes_file} ({nodes_file.stat().st_size / 1024 / 1024:.1f} MB)")
    print(f"  边文件: {edges_file} ({edges_file.stat().st_size / 1024 / 1024:.1f} MB)")
    
    # 显示统计信息
    if stats_file.exists():
        try:
            with open(stats_file, 'r') as f:
                stats = json.load(f)
            
            pipeline_stats = stats.get('stats', {})
            print(f"\n📈 数据统计:")
            print(f"  总节点数: {pipeline_stats.get('total_nodes', 0):,}")
            print(f"  总边数: {pipeline_stats.get('total_edges', 0):,}")
            print(f"  跨仓库关系: {pipeline_stats.get('cross_repo_relationships', 0):,}")
            
            # 节点类型分布
            node_types = pipeline_stats.get('node_types', {})
            if node_types:
                print(f"\n🏗️ 节点类型分布:")
                for node_type, count in sorted(node_types.items(), key=lambda x: x[1], reverse=True):
                    print(f"  {node_type}: {count:,}")
            
            # 仓库分布
            repositories = pipeline_stats.get('repositories', {})
            if repositories:
                print(f"\n🏢 仓库分布:")
                for repo, data in sorted(repositories.items(), key=lambda x: x[1].get('nodes', 0), reverse=True):
                    nodes = data.get('nodes', 0)
                    files = data.get('files', 0)
                    print(f"  {repo}: {nodes:,} 节点, {files} 文件")
                    
        except Exception as e:
            print(f"⚠️ 读取统计文件失败: {e}")
    
    return True

def show_sample_queries():
    """显示示例查询"""
    print("\n🔍 Neo4j Cypher查询示例")
    print("=" * 60)
    
    queries = {
        "基础统计": [
            "// 总节点数\nMATCH (n:CodeNode) RETURN count(n) as total_nodes",
            "// 总关系数\nMATCH ()-[r:RELATED_TO]->() RETURN count(r) as total_relationships",
            "// 节点类型分布\nMATCH (n:CodeNode)\nRETURN n.node_type as type, count(n) as count\nORDER BY count DESC"
        ],
        "跨仓库分析": [
            "// 跨仓库依赖矩阵\nMATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)\nWHERE source.repository <> target.repository\nRETURN source.repository as source_repo,\n       target.repository as target_repo,\n       count(r) as dependency_count\nORDER BY dependency_count DESC",
            "// 最依赖其他仓库的仓库\nMATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)\nWHERE source.repository <> target.repository\nRETURN source.repository as repository,\n       count(r) as outgoing_dependencies\nORDER BY outgoing_dependencies DESC"
        ],
        "代码结构分析": [
            "// 最大的类（按方法数）\nMATCH (c:CodeNode {node_type: 'Class'})-[r:RELATED_TO]->(m:CodeNode {node_type: 'Method'})\nWHERE r.relationship_type = 'DEFINES'\nRETURN c.name as class_name,\n       c.repository as repository,\n       count(m) as method_count\nORDER BY method_count DESC\nLIMIT 20",
            "// 继承链分析\nMATCH path = (child:CodeNode)-[:RELATED_TO*1..5]->(parent:CodeNode)\nWHERE ALL(r IN relationships(path) WHERE r.relationship_type = 'INHERITS')\nRETURN child.name as child_class,\n       parent.name as parent_class,\n       length(path) as inheritance_depth\nORDER BY inheritance_depth DESC"
        ],
        "依赖分析": [
            "// 最常访问的属性\nMATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode {node_type: 'Property'})\nWHERE r.relationship_type = 'ACCESSES'\nRETURN target.name as property_name,\n       target.repository as repository,\n       count(r) as access_count\nORDER BY access_count DESC\nLIMIT 20",
            "// 导入依赖分析\nMATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)\nWHERE r.relationship_type = 'IMPORTS'\nRETURN source.repository as source_repo,\n       target.repository as target_repo,\n       count(r) as import_count\nORDER BY import_count DESC"
        ]
    }
    
    for category, query_list in queries.items():
        print(f"\n📋 {category}:")
        for i, query in enumerate(query_list, 1):
            print(f"\n{i}. {query}")

def show_setup_instructions():
    """显示设置说明"""
    print("\n🛠️ Neo4j设置说明")
    print("=" * 60)
    
    print("1. 安装Neo4j:")
    print("   方法1 - Neo4j Desktop (推荐):")
    print("     • 下载: https://neo4j.com/download/")
    print("     • 创建数据库实例")
    print("     • 设置密码: 6536772a")
    print("     • 启动数据库")
    print()
    print("   方法2 - Homebrew:")
    print("     brew install neo4j")
    print("     neo4j-admin set-initial-password 6536772a")
    print("     neo4j start")
    print()
    print("   方法3 - Docker:")
    print("     docker run --name neo4j-pipeline \\")
    print("       -p 7474:7474 -p 7687:7687 \\")
    print("       -e NEO4J_AUTH=neo4j/6536772a \\")
    print("       neo4j:latest")
    print()
    print("2. 测试连接:")
    print("   python test_neo4j_connection.py")
    print()
    print("3. 导入数据:")
    print("   python neo4j_integration/main.py")
    print()
    print("4. 访问Neo4j Browser:")
    print("   http://localhost:7474")
    print("   用户名: neo4j")
    print("   密码: 6536772a")

def show_expected_results():
    """显示预期结果"""
    print("\n🎯 预期导入结果")
    print("=" * 60)
    
    print("成功导入后，Neo4j数据库将包含:")
    print("• 80,549个节点 (7种类型)")
    print("  - Method: 57,582个 (71.5%)")
    print("  - Property: 14,144个 (17.6%)")
    print("  - File: 4,465个 (5.5%)")
    print("  - Class: 4,353个 (5.4%)")
    print("  - Protocol: 3个")
    print("  - Struct: 1个")
    print("  - Enum: 1个")
    print()
    print("• 518,692条关系 (8种类型)")
    print("  - ACCESSES: 325,934条 (62.8%)")
    print("  - DEPENDS_ON: 107,377条 (20.7%)")
    print("  - DEFINES: 76,079条 (14.7%)")
    print("  - IMPORTS: 8,859条 (1.7%)")
    print("  - INHERITS: 195条")
    print("  - CALLS: 159条")
    print("  - EXTENDS: 64条")
    print("  - IMPLEMENTS: 25条")
    print()
    print("• 15,754个跨仓库关系")
    print("• 11个仓库的完整覆盖")

def main():
    """主函数"""
    print("🚀 Pipeline-All Neo4j集成演示")
    print("=" * 60)
    
    # 显示数据概览
    if not show_data_overview():
        return
    
    # 显示示例查询
    show_sample_queries()
    
    # 显示设置说明
    show_setup_instructions()
    
    # 显示预期结果
    show_expected_results()
    
    print("\n🎉 演示完成!")
    print("请按照设置说明配置Neo4j，然后运行数据导入。")

if __name__ == "__main__":
    main()
