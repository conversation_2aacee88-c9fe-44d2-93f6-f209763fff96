"""
跨仓库关系分析器
专门处理和验证跨仓库依赖关系
"""

import logging
from typing import Dict, List, Any, Tuple

from .connection import Neo4jConnection

logger = logging.getLogger(__name__)


class CrossRepoAnalyzer:
    """跨仓库关系分析器"""
    
    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
    
    def analyze_cross_repo_relationships(self) -> Dict[str, Any]:
        """分析跨仓库关系"""
        logger.info("🔍 分析跨仓库关系...")
        
        analysis = {
            'total_cross_repo_relationships': 0,
            'by_relationship_type': {},
            'by_source_repository': {},
            'by_target_repository': {},
            'repository_dependency_matrix': {},
            'top_cross_repo_dependencies': []
        }
        
        try:
            # 1. 统计总的跨仓库关系数量
            total_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN count(r) as total_count
            """
            
            result = self.connection.execute_query(total_query)
            analysis['total_cross_repo_relationships'] = result[0]['total_count'] if result else 0
            
            # 2. 按关系类型统计
            by_type_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN r.relationship_type as rel_type, count(r) as count
            ORDER BY count DESC
            """
            
            type_results = self.connection.execute_query(by_type_query)
            analysis['by_relationship_type'] = {r['rel_type']: r['count'] for r in type_results}
            
            # 3. 按源仓库统计
            by_source_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN source.repository as source_repo, count(r) as count
            ORDER BY count DESC
            """
            
            source_results = self.connection.execute_query(by_source_query)
            analysis['by_source_repository'] = {r['source_repo']: r['count'] for r in source_results}
            
            # 4. 按目标仓库统计
            by_target_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN target.repository as target_repo, count(r) as count
            ORDER BY count DESC
            """
            
            target_results = self.connection.execute_query(by_target_query)
            analysis['by_target_repository'] = {r['target_repo']: r['count'] for r in target_results}
            
            # 5. 仓库依赖矩阵
            matrix_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN source.repository as source_repo, target.repository as target_repo, count(r) as count
            ORDER BY count DESC
            """
            
            matrix_results = self.connection.execute_query(matrix_query)
            for result in matrix_results:
                source_repo = result['source_repo']
                target_repo = result['target_repo']
                count = result['count']
                
                if source_repo not in analysis['repository_dependency_matrix']:
                    analysis['repository_dependency_matrix'][source_repo] = {}
                analysis['repository_dependency_matrix'][source_repo][target_repo] = count
            
            # 6. 顶级跨仓库依赖
            analysis['top_cross_repo_dependencies'] = matrix_results[:20]  # 前20个
            
            logger.info(f"✅ 跨仓库关系分析完成: {analysis['total_cross_repo_relationships']} 个关系")
            
        except Exception as e:
            logger.error(f"❌ 跨仓库关系分析失败: {e}")
        
        return analysis
    
    def get_repository_coupling_metrics(self) -> Dict[str, Any]:
        """计算仓库耦合度指标"""
        logger.info("📊 计算仓库耦合度指标...")
        
        metrics = {
            'afferent_coupling': {},  # 传入耦合 (Ca)
            'efferent_coupling': {},  # 传出耦合 (Ce)
            'instability': {},        # 不稳定性 (I = Ce / (Ca + Ce))
            'coupling_matrix': {}
        }
        
        try:
            # 获取所有仓库
            repos_query = """
            MATCH (n:CodeNode)
            WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
            RETURN DISTINCT n.repository as repository
            ORDER BY repository
            """
            
            repos_result = self.connection.execute_query(repos_query)
            repositories = [r['repository'] for r in repos_result]
            
            for repo in repositories:
                # 计算传入耦合 (其他仓库依赖此仓库的数量)
                ca_query = """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE target.repository = $repo 
                AND source.repository <> $repo
                AND source.repository IS NOT NULL 
                AND source.repository <> 'unknown'
                RETURN count(DISTINCT source.repository) as ca
                """
                
                ca_result = self.connection.execute_query(ca_query, {'repo': repo})
                ca = ca_result[0]['ca'] if ca_result else 0
                
                # 计算传出耦合 (此仓库依赖其他仓库的数量)
                ce_query = """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE source.repository = $repo 
                AND target.repository <> $repo
                AND target.repository IS NOT NULL 
                AND target.repository <> 'unknown'
                RETURN count(DISTINCT target.repository) as ce
                """
                
                ce_result = self.connection.execute_query(ce_query, {'repo': repo})
                ce = ce_result[0]['ce'] if ce_result else 0
                
                # 计算不稳定性
                instability = ce / (ca + ce) if (ca + ce) > 0 else 0
                
                metrics['afferent_coupling'][repo] = ca
                metrics['efferent_coupling'][repo] = ce
                metrics['instability'][repo] = instability
            
            logger.info("✅ 仓库耦合度指标计算完成")
            
        except Exception as e:
            logger.error(f"❌ 仓库耦合度指标计算失败: {e}")
        
        return metrics
    
    def validate_cross_repo_data(self) -> Dict[str, Any]:
        """验证跨仓库数据的完整性"""
        logger.info("🔍 验证跨仓库数据完整性...")
        
        validation = {
            'total_nodes_with_repo': 0,
            'nodes_without_repo': 0,
            'cross_repo_relationships': 0,
            'same_repo_relationships': 0,
            'repository_distribution': {},
            'relationship_type_distribution': {},
            'validation_passed': False
        }
        
        try:
            # 1. 统计有仓库信息的节点
            nodes_with_repo_query = """
            MATCH (n:CodeNode)
            WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
            RETURN count(n) as count
            """
            
            result = self.connection.execute_query(nodes_with_repo_query)
            validation['total_nodes_with_repo'] = result[0]['count'] if result else 0
            
            # 2. 统计没有仓库信息的节点
            nodes_without_repo_query = """
            MATCH (n:CodeNode)
            WHERE n.repository IS NULL OR n.repository = 'unknown'
            RETURN count(n) as count
            """
            
            result = self.connection.execute_query(nodes_without_repo_query)
            validation['nodes_without_repo'] = result[0]['count'] if result else 0
            
            # 3. 统计跨仓库关系
            cross_repo_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN count(r) as count
            """
            
            result = self.connection.execute_query(cross_repo_query)
            validation['cross_repo_relationships'] = result[0]['count'] if result else 0
            
            # 4. 统计同仓库关系
            same_repo_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository = target.repository 
            AND source.repository IS NOT NULL 
            AND source.repository <> 'unknown'
            RETURN count(r) as count
            """
            
            result = self.connection.execute_query(same_repo_query)
            validation['same_repo_relationships'] = result[0]['count'] if result else 0
            
            # 5. 仓库分布
            repo_dist_query = """
            MATCH (n:CodeNode)
            WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
            RETURN n.repository as repository, count(n) as count
            ORDER BY count DESC
            """
            
            repo_results = self.connection.execute_query(repo_dist_query)
            validation['repository_distribution'] = {r['repository']: r['count'] for r in repo_results}
            
            # 6. 关系类型分布
            rel_type_query = """
            MATCH ()-[r:RELATED_TO]->()
            RETURN r.relationship_type as rel_type, count(r) as count
            ORDER BY count DESC
            """
            
            rel_results = self.connection.execute_query(rel_type_query)
            validation['relationship_type_distribution'] = {r['rel_type']: r['count'] for r in rel_results}
            
            # 验证通过条件
            validation['validation_passed'] = (
                validation['total_nodes_with_repo'] > 70000 and  # 大部分节点有仓库信息
                validation['cross_repo_relationships'] > 10000 and  # 有足够的跨仓库关系
                len(validation['repository_distribution']) >= 10  # 覆盖大部分仓库
            )
            
            logger.info(f"✅ 跨仓库数据验证完成: {'通过' if validation['validation_passed'] else '未通过'}")
            
        except Exception as e:
            logger.error(f"❌ 跨仓库数据验证失败: {e}")
        
        return validation
