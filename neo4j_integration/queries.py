"""
Neo4j Cypher查询示例
提供探索代码知识图谱的常用查询
"""

import logging
from typing import Dict, List, Any

from .connection import Neo4jConnection

logger = logging.getLogger(__name__)


class CypherQueries:
    """Cypher查询示例集合"""
    
    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
    
    def basic_statistics(self) -> Dict[str, Any]:
        """基础统计查询"""
        logger.info("📊 执行基础统计查询...")
        
        queries = {
            "total_nodes": "MATCH (n:CodeNode) RETURN count(n) as count",
            "total_relationships": "MATCH ()-[r:RELATED_TO]->() RETURN count(r) as count",
            "node_types": """
                MATCH (n:CodeNode)
                RETURN n.node_type as type, count(n) as count
                ORDER BY count DESC
            """,
            "relationship_types": """
                MATCH ()-[r:RELATED_TO]->()
                RETURN r.relationship_type as type, count(r) as count
                ORDER BY count DESC
            """,
            "repositories": """
                MATCH (n:CodeNode)
                WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
                RETURN n.repository as repository, count(n) as count
                ORDER BY count DESC
            """
        }
        
        results = {}
        for name, query in queries.items():
            try:
                result = self.connection.execute_query(query)
                results[name] = result
            except Exception as e:
                logger.error(f"查询 {name} 失败: {e}")
                results[name] = []
        
        return results
    
    def cross_repository_analysis(self) -> Dict[str, Any]:
        """跨仓库分析查询"""
        logger.info("🔗 执行跨仓库分析查询...")
        
        queries = {
            "cross_repo_dependencies": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE source.repository <> target.repository 
                AND source.repository IS NOT NULL 
                AND target.repository IS NOT NULL
                AND source.repository <> 'unknown'
                AND target.repository <> 'unknown'
                RETURN source.repository as source_repo, 
                       target.repository as target_repo, 
                       r.relationship_type as rel_type,
                       count(r) as count
                ORDER BY count DESC
                LIMIT 20
            """,
            "most_dependent_repositories": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE source.repository <> target.repository 
                AND source.repository IS NOT NULL 
                AND target.repository IS NOT NULL
                AND source.repository <> 'unknown'
                AND target.repository <> 'unknown'
                RETURN source.repository as repository, count(r) as outgoing_dependencies
                ORDER BY outgoing_dependencies DESC
            """,
            "most_depended_on_repositories": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE source.repository <> target.repository 
                AND source.repository IS NOT NULL 
                AND target.repository IS NOT NULL
                AND source.repository <> 'unknown'
                AND target.repository <> 'unknown'
                RETURN target.repository as repository, count(r) as incoming_dependencies
                ORDER BY incoming_dependencies DESC
            """
        }
        
        results = {}
        for name, query in queries.items():
            try:
                result = self.connection.execute_query(query)
                results[name] = result
            except Exception as e:
                logger.error(f"查询 {name} 失败: {e}")
                results[name] = []
        
        return results
    
    def code_structure_analysis(self) -> Dict[str, Any]:
        """代码结构分析查询"""
        logger.info("🏗️ 执行代码结构分析查询...")
        
        queries = {
            "largest_classes": """
                MATCH (c:CodeNode {node_type: 'Class'})-[r:RELATED_TO]->(m:CodeNode {node_type: 'Method'})
                WHERE r.relationship_type = 'DEFINES'
                RETURN c.name as class_name, c.repository as repository, count(m) as method_count
                ORDER BY method_count DESC
                LIMIT 20
            """,
            "most_complex_files": """
                MATCH (f:CodeNode {node_type: 'File'})-[r:RELATED_TO]->(n:CodeNode)
                WHERE r.relationship_type = 'DEFINES'
                RETURN f.name as file_name, f.repository as repository, count(n) as defined_elements
                ORDER BY defined_elements DESC
                LIMIT 20
            """,
            "inheritance_chains": """
                MATCH path = (child:CodeNode)-[:RELATED_TO*1..5]->(parent:CodeNode)
                WHERE ALL(r IN relationships(path) WHERE r.relationship_type = 'INHERITS')
                RETURN child.name as child_class, parent.name as parent_class, 
                       length(path) as inheritance_depth, child.repository as repository
                ORDER BY inheritance_depth DESC
                LIMIT 10
            """,
            "protocol_implementations": """
                MATCH (c:CodeNode)-[r:RELATED_TO]->(p:CodeNode {node_type: 'Protocol'})
                WHERE r.relationship_type = 'IMPLEMENTS'
                RETURN p.name as protocol_name, count(c) as implementation_count
                ORDER BY implementation_count DESC
                LIMIT 10
            """
        }
        
        results = {}
        for name, query in queries.items():
            try:
                result = self.connection.execute_query(query)
                results[name] = result
            except Exception as e:
                logger.error(f"查询 {name} 失败: {e}")
                results[name] = []
        
        return results
    
    def dependency_analysis(self) -> Dict[str, Any]:
        """依赖分析查询"""
        logger.info("🔍 执行依赖分析查询...")
        
        queries = {
            "most_accessed_properties": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode {node_type: 'Property'})
                WHERE r.relationship_type = 'ACCESSES'
                RETURN target.name as property_name, target.repository as repository, count(r) as access_count
                ORDER BY access_count DESC
                LIMIT 20
            """,
            "most_called_methods": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode {node_type: 'Method'})
                WHERE r.relationship_type = 'CALLS'
                RETURN target.name as method_name, target.repository as repository, count(r) as call_count
                ORDER BY call_count DESC
                LIMIT 20
            """,
            "import_dependencies": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE r.relationship_type = 'IMPORTS'
                RETURN source.repository as source_repo, target.repository as target_repo, count(r) as import_count
                ORDER BY import_count DESC
                LIMIT 20
            """,
            "type_dependencies": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE r.relationship_type = 'DEPENDS_ON'
                RETURN source.repository as source_repo, r.variable_type as type_name, count(r) as usage_count
                ORDER BY usage_count DESC
                LIMIT 20
            """
        }
        
        results = {}
        for name, query in queries.items():
            try:
                result = self.connection.execute_query(query)
                results[name] = result
            except Exception as e:
                logger.error(f"查询 {name} 失败: {e}")
                results[name] = []
        
        return results
    
    def architectural_insights(self) -> Dict[str, Any]:
        """架构洞察查询"""
        logger.info("🏛️ 执行架构洞察查询...")
        
        queries = {
            "repository_coupling": """
                MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
                WHERE source.repository <> target.repository 
                AND source.repository IS NOT NULL 
                AND target.repository IS NOT NULL
                AND source.repository <> 'unknown'
                AND target.repository <> 'unknown'
                WITH source.repository as source_repo, target.repository as target_repo, count(r) as coupling_strength
                RETURN source_repo, target_repo, coupling_strength
                ORDER BY coupling_strength DESC
                LIMIT 30
            """,
            "circular_dependencies": """
                MATCH path = (a:CodeNode)-[:RELATED_TO*2..4]->(a)
                WHERE a.repository IS NOT NULL AND a.repository <> 'unknown'
                AND ALL(r IN relationships(path) WHERE r.relationship_type IN ['DEPENDS_ON', 'IMPORTS'])
                RETURN a.name as node_name, a.repository as repository, length(path) as cycle_length
                LIMIT 10
            """,
            "hub_nodes": """
                MATCH (n:CodeNode)
                WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
                WITH n, 
                     size((n)-[:RELATED_TO]->()) as outgoing_degree,
                     size(()-[:RELATED_TO]->(n)) as incoming_degree
                RETURN n.name as node_name, n.node_type as type, n.repository as repository,
                       outgoing_degree, incoming_degree, 
                       (outgoing_degree + incoming_degree) as total_degree
                ORDER BY total_degree DESC
                LIMIT 20
            """,
            "isolated_components": """
                MATCH (n:CodeNode)
                WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
                AND NOT (n)-[:RELATED_TO]-() AND NOT ()-[:RELATED_TO]-(n)
                RETURN n.repository as repository, count(n) as isolated_nodes
                ORDER BY isolated_nodes DESC
            """
        }
        
        results = {}
        for name, query in queries.items():
            try:
                result = self.connection.execute_query(query)
                results[name] = result
            except Exception as e:
                logger.error(f"查询 {name} 失败: {e}")
                results[name] = []
        
        return results
    
    def run_all_analyses(self) -> Dict[str, Any]:
        """运行所有分析查询"""
        logger.info("🚀 运行完整的代码知识图谱分析...")
        
        all_results = {
            'basic_statistics': self.basic_statistics(),
            'cross_repository_analysis': self.cross_repository_analysis(),
            'code_structure_analysis': self.code_structure_analysis(),
            'dependency_analysis': self.dependency_analysis(),
            'architectural_insights': self.architectural_insights()
        }
        
        logger.info("✅ 所有分析查询完成")
        return all_results
    
    def print_analysis_summary(self, results: Dict[str, Any]):
        """打印分析摘要"""
        logger.info("📋 代码知识图谱分析摘要")
        logger.info("=" * 60)
        
        # 基础统计
        basic_stats = results.get('basic_statistics', {})
        if 'total_nodes' in basic_stats and basic_stats['total_nodes']:
            total_nodes = basic_stats['total_nodes'][0]['count']
            logger.info(f"总节点数: {total_nodes:,}")
        
        if 'total_relationships' in basic_stats and basic_stats['total_relationships']:
            total_rels = basic_stats['total_relationships'][0]['count']
            logger.info(f"总关系数: {total_rels:,}")
        
        # 跨仓库分析
        cross_repo = results.get('cross_repository_analysis', {})
        if 'cross_repo_dependencies' in cross_repo and cross_repo['cross_repo_dependencies']:
            cross_repo_count = sum(dep['count'] for dep in cross_repo['cross_repo_dependencies'])
            logger.info(f"跨仓库关系: {cross_repo_count:,}")
        
        # 仓库分布
        if 'repositories' in basic_stats and basic_stats['repositories']:
            repo_count = len(basic_stats['repositories'])
            logger.info(f"仓库数量: {repo_count}")
        
        logger.info("=" * 60)
