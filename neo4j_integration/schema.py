"""
Neo4j数据库模式管理
处理数据库清理、索引创建和约束设置
"""

import logging
from typing import List, Dict, Any

from .connection import Neo4jConnection
from .config import Neo4jConfig, INDEXES_AND_CONSTRAINTS, NODE_LABELS, RELATIONSHIP_TYPES

logger = logging.getLogger(__name__)


class Neo4jSchemaManager:
    """Neo4j模式管理器"""
    
    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
    
    def clear_database(self) -> bool:
        """清理数据库中的所有数据"""
        logger.info("🧹 开始清理Neo4j数据库...")
        
        try:
            # 删除所有关系
            logger.info("删除所有关系...")
            self.connection.execute_write_transaction("MATCH ()-[r]->() DELETE r")
            
            # 删除所有节点
            logger.info("删除所有节点...")
            self.connection.execute_write_transaction("MATCH (n) DELETE n")
            
            # 验证清理结果
            info = self.connection.get_database_info()
            node_count = info.get('node_count', 0)
            rel_count = info.get('relationship_count', 0)
            
            if node_count == 0 and rel_count == 0:
                logger.info("✅ 数据库清理完成")
                return True
            else:
                logger.warning(f"⚠️ 数据库清理不完整: {node_count}个节点, {rel_count}个关系")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据库清理失败: {e}")
            return False
    
    def drop_constraints_and_indexes(self) -> bool:
        """删除所有约束和索引"""
        logger.info("🗑️ 删除现有约束和索引...")
        
        try:
            # 获取所有约束
            constraints_query = "SHOW CONSTRAINTS YIELD name"
            constraints = self.connection.execute_query(constraints_query)
            
            for constraint in constraints:
                constraint_name = constraint['name']
                drop_query = f"DROP CONSTRAINT {constraint_name} IF EXISTS"
                try:
                    self.connection.execute_write_transaction(drop_query)
                    logger.debug(f"删除约束: {constraint_name}")
                except Exception as e:
                    logger.warning(f"删除约束{constraint_name}失败: {e}")
            
            # 获取所有索引
            indexes_query = "SHOW INDEXES YIELD name, type WHERE type <> 'LOOKUP'"
            indexes = self.connection.execute_query(indexes_query)
            
            for index in indexes:
                index_name = index['name']
                drop_query = f"DROP INDEX {index_name} IF EXISTS"
                try:
                    self.connection.execute_write_transaction(drop_query)
                    logger.debug(f"删除索引: {index_name}")
                except Exception as e:
                    logger.warning(f"删除索引{index_name}失败: {e}")
            
            logger.info("✅ 约束和索引删除完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 删除约束和索引失败: {e}")
            return False
    
    def create_schema(self) -> bool:
        """创建数据库模式（索引和约束）"""
        logger.info("🏗️ 创建Neo4j数据库模式...")
        
        success_count = 0
        total_count = len(INDEXES_AND_CONSTRAINTS)
        
        for constraint_or_index in INDEXES_AND_CONSTRAINTS:
            try:
                self.connection.execute_write_transaction(constraint_or_index)
                success_count += 1
                logger.debug(f"创建成功: {constraint_or_index[:50]}...")
            except Exception as e:
                logger.warning(f"创建失败: {constraint_or_index[:50]}... - {e}")
        
        logger.info(f"✅ 模式创建完成: {success_count}/{total_count} 成功")
        return success_count == total_count
    
    def verify_schema(self) -> Dict[str, Any]:
        """验证数据库模式"""
        logger.info("🔍 验证数据库模式...")
        
        schema_info = {
            'constraints': [],
            'indexes': [],
            'node_labels': [],
            'relationship_types': []
        }
        
        try:
            # 获取约束信息
            constraints = self.connection.execute_query("SHOW CONSTRAINTS YIELD name, type")
            schema_info['constraints'] = [c['name'] for c in constraints]
            
            # 获取索引信息
            indexes = self.connection.execute_query("SHOW INDEXES YIELD name, type WHERE type <> 'LOOKUP'")
            schema_info['indexes'] = [i['name'] for i in indexes]
            
            # 获取节点标签
            labels = self.connection.execute_query("CALL db.labels() YIELD label RETURN label")
            schema_info['node_labels'] = [l['label'] for l in labels]
            
            # 获取关系类型
            rel_types = self.connection.execute_query("CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType")
            schema_info['relationship_types'] = [r['relationshipType'] for r in rel_types]
            
            logger.info(f"📊 模式验证完成:")
            logger.info(f"   约束数量: {len(schema_info['constraints'])}")
            logger.info(f"   索引数量: {len(schema_info['indexes'])}")
            logger.info(f"   节点标签: {len(schema_info['node_labels'])}")
            logger.info(f"   关系类型: {len(schema_info['relationship_types'])}")
            
        except Exception as e:
            logger.error(f"❌ 模式验证失败: {e}")
        
        return schema_info
    
    def prepare_database(self) -> bool:
        """准备数据库（完整的清理和模式创建流程）"""
        logger.info("🚀 开始准备Neo4j数据库...")
        
        # 步骤1: 删除约束和索引
        if not self.drop_constraints_and_indexes():
            logger.error("删除约束和索引失败")
            return False
        
        # 步骤2: 清理数据
        if not self.clear_database():
            logger.error("数据库清理失败")
            return False
        
        # 步骤3: 创建模式
        if not self.create_schema():
            logger.warning("模式创建部分失败，但继续执行")
        
        # 步骤4: 验证模式
        schema_info = self.verify_schema()
        
        logger.info("✅ 数据库准备完成")
        return True
    
    def get_expected_schema(self) -> Dict[str, List[str]]:
        """获取期望的模式配置"""
        return {
            'node_labels': list(NODE_LABELS.values()),
            'relationship_types': list(RELATIONSHIP_TYPES.values()),
            'repositories': [
                'QMapBusiness', 'QMapMiddlePlatform', 'QMapNaviKit', 'DragonMapKit',
                'QMapHippy', 'QMapFoundation', 'QMapBaseline', 'QMapUIKit',
                'TencentMap', 'QMapRouteSearchKit', 'DKProtocolsPool'
            ]
        }
