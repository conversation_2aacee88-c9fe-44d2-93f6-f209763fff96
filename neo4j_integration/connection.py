"""
Neo4j连接管理器
处理与Neo4j数据库的连接和会话管理
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Generator

try:
    from neo4j import GraphDatabase, Driver, Session
    from neo4j.exceptions import ServiceUnavailable, TransientError
except ImportError:
    raise ImportError("请安装neo4j驱动: pip install neo4j")

from .config import Neo4jConfig

logger = logging.getLogger(__name__)


class Neo4jConnection:
    """Neo4j连接管理器"""
    
    def __init__(self, config: Neo4jConfig):
        self.config = config
        self.driver: Optional[Driver] = None
        
    def connect(self) -> bool:
        """建立Neo4j连接"""
        try:
            logger.info(f"连接到Neo4j: {self.config.uri}")
            self.driver = GraphDatabase.driver(
                self.config.uri,
                auth=(self.config.username, self.config.password),
                connection_timeout=self.config.timeout,
                max_connection_lifetime=3600  # 1小时
            )
            
            # 测试连接
            with self.driver.session(database=self.config.database) as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    logger.info("✅ Neo4j连接成功")
                    return True
                    
        except ServiceUnavailable as e:
            logger.error(f"❌ Neo4j服务不可用: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Neo4j连接失败: {e}")
            return False
            
        return False
    
    def disconnect(self):
        """关闭Neo4j连接"""
        if self.driver:
            self.driver.close()
            self.driver = None
            logger.info("Neo4j连接已关闭")
    
    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        """获取Neo4j会话的上下文管理器"""
        if not self.driver:
            raise RuntimeError("Neo4j连接未建立，请先调用connect()")
            
        session = self.driver.session(database=self.config.database)
        try:
            yield session
        finally:
            session.close()
    
    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行单个查询"""
        with self.session() as session:
            result = session.run(query, parameters or {})
            return [record.data() for record in result]
    
    def execute_write_transaction(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> Any:
        """执行写事务"""
        def _execute_write(tx):
            return tx.run(query, parameters or {})
        
        with self.session() as session:
            return session.execute_write(_execute_write)
    
    def execute_batch_write(self, queries: List[str], parameters_list: List[Dict[str, Any]] = None) -> bool:
        """批量执行写操作"""
        if not queries:
            return True
            
        if parameters_list and len(queries) != len(parameters_list):
            raise ValueError("查询数量与参数数量不匹配")
        
        def _batch_write(tx):
            for i, query in enumerate(queries):
                params = parameters_list[i] if parameters_list else {}
                tx.run(query, params)
        
        try:
            with self.session() as session:
                session.execute_write(_batch_write)
            return True
        except Exception as e:
            logger.error(f"批量写操作失败: {e}")
            return False
    
    def execute_with_retry(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> Any:
        """带重试机制的查询执行"""
        for attempt in range(self.config.max_retries):
            try:
                return self.execute_write_transaction(query, parameters)
            except TransientError as e:
                if attempt < self.config.max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"查询失败，{wait_time}秒后重试 (尝试 {attempt + 1}/{self.config.max_retries}): {e}")
                    time.sleep(wait_time)
                else:
                    logger.error(f"查询在{self.config.max_retries}次尝试后仍然失败: {e}")
                    raise
            except Exception as e:
                logger.error(f"查询执行失败: {e}")
                raise
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        queries = {
            "node_count": "MATCH (n) RETURN count(n) as count",
            "relationship_count": "MATCH ()-[r]->() RETURN count(r) as count", 
            "node_labels": "CALL db.labels() YIELD label RETURN collect(label) as labels",
            "relationship_types": "CALL db.relationshipTypes() YIELD relationshipType RETURN collect(relationshipType) as types"
        }
        
        info = {}
        for key, query in queries.items():
            try:
                result = self.execute_query(query)
                if key in ["node_labels", "relationship_types"]:
                    info[key] = result[0][key.split('_')[1]] if result else []
                else:
                    info[key] = result[0]["count"] if result else 0
            except Exception as e:
                logger.warning(f"获取{key}失败: {e}")
                info[key] = None
        
        return info
