# Neo4j集成 - Pipeline-All代码知识图谱

这个模块将Pipeline-All生成的代码知识图谱数据导入到Neo4j数据库中，支持高级查询和可视化分析。

## 📋 前置要求

### 1. Neo4j安装和配置

#### 方法1: Neo4j Desktop (推荐)
1. 下载并安装 [Neo4j Desktop](https://neo4j.com/download/)
2. 启动Neo4j Desktop
3. 创建新项目和数据库实例
4. 设置密码为: `6536772a`
5. 启动数据库实例

#### 方法2: Neo4j Community Edition
```bash
# macOS (使用Homebrew)
brew install neo4j

# 启动Neo4j
neo4j start

# 设置密码
neo4j-admin set-initial-password 6536772a
```

#### 方法3: Docker
```bash
# 运行Neo4j容器
docker run \
    --name neo4j-pipeline \
    -p 7474:7474 -p 7687:7687 \
    -e NEO4J_AUTH=neo4j/6536772a \
    -v $PWD/neo4j/data:/data \
    -v $PWD/neo4j/logs:/logs \
    neo4j:latest
```

### 2. Python依赖
```bash
pip install neo4j
```

## 🚀 使用方法

### 1. 测试连接
```bash
python test_neo4j_connection.py
```

### 2. 完整数据导入
```bash
python neo4j_integration/main.py
```

### 3. 自定义配置
通过环境变量配置连接参数:
```bash
export NEO4J_URI="bolt://localhost:7687"
export NEO4J_USERNAME="neo4j"
export NEO4J_PASSWORD="6536772a"
export NEO4J_DATABASE="neo4j"
```

## 📊 数据结构

### 节点类型
- **File**: 源代码文件 (4,465个)
- **Class**: 类定义 (4,353个)
- **Method**: 方法定义 (57,582个)
- **Property**: 属性定义 (14,144个)
- **Protocol**: 协议定义 (3个)
- **Struct**: 结构体定义 (1个)
- **Enum**: 枚举定义 (1个)

### 关系类型
- **DEFINES**: 定义关系 (76,079条)
- **ACCESSES**: 属性访问 (325,934条)
- **DEPENDS_ON**: 类型依赖 (107,377条)
- **IMPORTS**: 导入关系 (8,859条)
- **INHERITS**: 继承关系 (195条)
- **CALLS**: 方法调用 (159条)
- **EXTENDS**: 扩展关系 (64条)
- **IMPLEMENTS**: 协议实现 (25条)

### 仓库覆盖
- QMapBusiness (38,787节点)
- QMapMiddlePlatform (17,024节点)
- QMapNaviKit (11,122节点)
- DragonMapKit (4,581节点)
- QMapHippy (2,820节点)
- QMapFoundation (2,097节点)
- QMapBaseline (1,892节点)
- QMapUIKit (1,110节点)
- TencentMap (291节点)
- QMapRouteSearchKit (261节点)
- DKProtocolsPool (58节点)

## 🔍 示例查询

### 基础统计
```cypher
// 总节点数
MATCH (n:CodeNode) RETURN count(n) as total_nodes

// 总关系数
MATCH ()-[r:RELATED_TO]->() RETURN count(r) as total_relationships

// 节点类型分布
MATCH (n:CodeNode)
RETURN n.node_type as type, count(n) as count
ORDER BY count DESC
```

### 跨仓库分析
```cypher
// 跨仓库依赖关系
MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
WHERE source.repository <> target.repository 
AND source.repository IS NOT NULL 
AND target.repository IS NOT NULL
RETURN source.repository as source_repo, 
       target.repository as target_repo, 
       count(r) as dependency_count
ORDER BY dependency_count DESC

// 最依赖其他仓库的仓库
MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
WHERE source.repository <> target.repository 
RETURN source.repository as repository, 
       count(r) as outgoing_dependencies
ORDER BY outgoing_dependencies DESC
```

### 代码结构分析
```cypher
// 最大的类（按方法数）
MATCH (c:CodeNode {node_type: 'Class'})-[r:RELATED_TO]->(m:CodeNode {node_type: 'Method'})
WHERE r.relationship_type = 'DEFINES'
RETURN c.name as class_name, 
       c.repository as repository, 
       count(m) as method_count
ORDER BY method_count DESC
LIMIT 20

// 继承链分析
MATCH path = (child:CodeNode)-[:RELATED_TO*1..5]->(parent:CodeNode)
WHERE ALL(r IN relationships(path) WHERE r.relationship_type = 'INHERITS')
RETURN child.name as child_class, 
       parent.name as parent_class, 
       length(path) as inheritance_depth
ORDER BY inheritance_depth DESC
```

### 依赖分析
```cypher
// 最常访问的属性
MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode {node_type: 'Property'})
WHERE r.relationship_type = 'ACCESSES'
RETURN target.name as property_name, 
       target.repository as repository, 
       count(r) as access_count
ORDER BY access_count DESC
LIMIT 20

// 最常调用的方法
MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode {node_type: 'Method'})
WHERE r.relationship_type = 'CALLS'
RETURN target.name as method_name, 
       target.repository as repository, 
       count(r) as call_count
ORDER BY call_count DESC
LIMIT 20
```

## 🛠️ 故障排除

### 连接问题
1. 确保Neo4j服务正在运行
2. 检查端口7687是否可访问
3. 验证用户名和密码
4. 检查防火墙设置

### 导入问题
1. 确保数据文件存在:
   - `ast_out_index_all/nodes_all.jsonl`
   - `ast_out_index_all/edges_all.jsonl`
2. 检查磁盘空间
3. 增加Neo4j内存配置

### 性能优化
1. 调整批量大小: `NEO4J_BATCH_SIZE=500`
2. 增加超时时间: `NEO4J_TIMEOUT=600`
3. 配置Neo4j内存设置

## 📈 预期结果

成功导入后，Neo4j数据库将包含:
- **80,549个节点** (7种类型)
- **518,692条关系** (8种类型)
- **15,754个跨仓库关系**
- **11个仓库的完整覆盖**

这为QMap项目提供了强大的代码分析和架构洞察能力。
