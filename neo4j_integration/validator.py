"""
Neo4j数据完整性验证器
验证导入数据与原始数据集的一致性
"""

import json
import logging
from typing import Dict, List, Any, Tuple
from pathlib import Path

from .connection import Neo4jConnection
from .config import Neo4jConfig

logger = logging.getLogger(__name__)


class Neo4jValidator:
    """Neo4j数据完整性验证器"""
    
    def __init__(self, connection: Neo4jConnection, config: Neo4jConfig):
        self.connection = connection
        self.config = config
    
    def load_original_stats(self) -> Dict[str, Any]:
        """加载原始统计数据"""
        stats_file = Path(self.config.stats_file)
        if not stats_file.exists():
            logger.warning(f"统计文件不存在: {stats_file}")
            return {}
        
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载统计文件失败: {e}")
            return {}
    
    def get_neo4j_stats(self) -> Dict[str, Any]:
        """获取Neo4j中的统计数据"""
        logger.info("📊 收集Neo4j统计数据...")
        
        stats = {
            'total_nodes': 0,
            'total_relationships': 0,
            'node_type_distribution': {},
            'relationship_type_distribution': {},
            'repository_distribution': {},
            'cross_repo_relationships': 0
        }
        
        try:
            # 1. 总节点数
            node_count_query = "MATCH (n:CodeNode) RETURN count(n) as count"
            result = self.connection.execute_query(node_count_query)
            stats['total_nodes'] = result[0]['count'] if result else 0
            
            # 2. 总关系数
            rel_count_query = "MATCH ()-[r:RELATED_TO]->() RETURN count(r) as count"
            result = self.connection.execute_query(rel_count_query)
            stats['total_relationships'] = result[0]['count'] if result else 0
            
            # 3. 节点类型分布
            node_type_query = """
            MATCH (n:CodeNode)
            WHERE n.node_type IS NOT NULL
            RETURN n.node_type as node_type, count(n) as count
            ORDER BY count DESC
            """
            
            node_type_results = self.connection.execute_query(node_type_query)
            stats['node_type_distribution'] = {r['node_type']: r['count'] for r in node_type_results}
            
            # 4. 关系类型分布
            rel_type_query = """
            MATCH ()-[r:RELATED_TO]->()
            WHERE r.relationship_type IS NOT NULL
            RETURN r.relationship_type as rel_type, count(r) as count
            ORDER BY count DESC
            """
            
            rel_type_results = self.connection.execute_query(rel_type_query)
            stats['relationship_type_distribution'] = {r['rel_type']: r['count'] for r in rel_type_results}
            
            # 5. 仓库分布
            repo_query = """
            MATCH (n:CodeNode)
            WHERE n.repository IS NOT NULL AND n.repository <> 'unknown'
            RETURN n.repository as repository, count(n) as count
            ORDER BY count DESC
            """
            
            repo_results = self.connection.execute_query(repo_query)
            stats['repository_distribution'] = {r['repository']: r['count'] for r in repo_results}
            
            # 6. 跨仓库关系
            cross_repo_query = """
            MATCH (source:CodeNode)-[r:RELATED_TO]->(target:CodeNode)
            WHERE source.repository <> target.repository 
            AND source.repository IS NOT NULL 
            AND target.repository IS NOT NULL
            AND source.repository <> 'unknown'
            AND target.repository <> 'unknown'
            RETURN count(r) as count
            """
            
            result = self.connection.execute_query(cross_repo_query)
            stats['cross_repo_relationships'] = result[0]['count'] if result else 0
            
            logger.info("✅ Neo4j统计数据收集完成")
            
        except Exception as e:
            logger.error(f"❌ Neo4j统计数据收集失败: {e}")
        
        return stats
    
    def compare_stats(self, original_stats: Dict[str, Any], neo4j_stats: Dict[str, Any]) -> Dict[str, Any]:
        """比较原始统计和Neo4j统计"""
        logger.info("🔍 比较统计数据...")
        
        comparison = {
            'nodes_match': False,
            'relationships_match': False,
            'node_types_match': False,
            'repositories_match': False,
            'overall_match': False,
            'differences': {},
            'match_percentages': {}
        }
        
        try:
            # 获取原始统计中的关键数据
            orig_stats = original_stats.get('stats', {})
            
            # 1. 比较节点数量
            orig_nodes = orig_stats.get('total_nodes', 0)
            neo4j_nodes = neo4j_stats.get('total_nodes', 0)
            
            node_match_percentage = (min(orig_nodes, neo4j_nodes) / max(orig_nodes, neo4j_nodes) * 100) if max(orig_nodes, neo4j_nodes) > 0 else 0
            comparison['nodes_match'] = node_match_percentage >= 95.0
            comparison['match_percentages']['nodes'] = node_match_percentage
            
            if not comparison['nodes_match']:
                comparison['differences']['nodes'] = {
                    'original': orig_nodes,
                    'neo4j': neo4j_nodes,
                    'difference': abs(orig_nodes - neo4j_nodes)
                }
            
            # 2. 比较关系数量
            orig_edges = orig_stats.get('total_edges', 0)
            neo4j_edges = neo4j_stats.get('total_relationships', 0)
            
            edge_match_percentage = (min(orig_edges, neo4j_edges) / max(orig_edges, neo4j_edges) * 100) if max(orig_edges, neo4j_edges) > 0 else 0
            comparison['relationships_match'] = edge_match_percentage >= 95.0
            comparison['match_percentages']['relationships'] = edge_match_percentage
            
            if not comparison['relationships_match']:
                comparison['differences']['relationships'] = {
                    'original': orig_edges,
                    'neo4j': neo4j_edges,
                    'difference': abs(orig_edges - neo4j_edges)
                }
            
            # 3. 比较节点类型分布
            orig_node_types = orig_stats.get('node_types', {})
            neo4j_node_types = neo4j_stats.get('node_type_distribution', {})
            
            node_type_matches = 0
            total_node_types = len(set(list(orig_node_types.keys()) + list(neo4j_node_types.keys())))
            
            for node_type in orig_node_types:
                if node_type in neo4j_node_types:
                    orig_count = orig_node_types[node_type]
                    neo4j_count = neo4j_node_types[node_type]
                    match_pct = (min(orig_count, neo4j_count) / max(orig_count, neo4j_count) * 100) if max(orig_count, neo4j_count) > 0 else 0
                    if match_pct >= 90.0:
                        node_type_matches += 1
            
            node_type_match_percentage = (node_type_matches / total_node_types * 100) if total_node_types > 0 else 0
            comparison['node_types_match'] = node_type_match_percentage >= 80.0
            comparison['match_percentages']['node_types'] = node_type_match_percentage
            
            # 4. 比较仓库分布
            orig_repos = orig_stats.get('repositories', {})
            neo4j_repos = neo4j_stats.get('repository_distribution', {})
            
            repo_matches = 0
            total_repos = len(set(list(orig_repos.keys()) + list(neo4j_repos.keys())))
            
            for repo in orig_repos:
                if repo in neo4j_repos:
                    repo_matches += 1
            
            repo_match_percentage = (repo_matches / total_repos * 100) if total_repos > 0 else 0
            comparison['repositories_match'] = repo_match_percentage >= 90.0
            comparison['match_percentages']['repositories'] = repo_match_percentage
            
            # 5. 总体匹配
            comparison['overall_match'] = (
                comparison['nodes_match'] and
                comparison['relationships_match'] and
                comparison['node_types_match'] and
                comparison['repositories_match']
            )
            
            logger.info(f"✅ 统计比较完成: {'匹配' if comparison['overall_match'] else '不匹配'}")
            
        except Exception as e:
            logger.error(f"❌ 统计比较失败: {e}")
        
        return comparison
    
    def validate_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        logger.info("🔍 开始数据完整性验证...")
        
        validation_result = {
            'validation_passed': False,
            'original_stats': {},
            'neo4j_stats': {},
            'comparison': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 1. 加载原始统计
            validation_result['original_stats'] = self.load_original_stats()
            
            # 2. 获取Neo4j统计
            validation_result['neo4j_stats'] = self.get_neo4j_stats()
            
            # 3. 比较统计
            validation_result['comparison'] = self.compare_stats(
                validation_result['original_stats'],
                validation_result['neo4j_stats']
            )
            
            # 4. 检查问题
            comparison = validation_result['comparison']
            
            if not comparison.get('nodes_match', False):
                validation_result['issues'].append("节点数量不匹配")
                validation_result['recommendations'].append("检查节点导入过程是否有错误")
            
            if not comparison.get('relationships_match', False):
                validation_result['issues'].append("关系数量不匹配")
                validation_result['recommendations'].append("检查关系导入过程是否有错误")
            
            if not comparison.get('node_types_match', False):
                validation_result['issues'].append("节点类型分布不匹配")
                validation_result['recommendations'].append("检查节点类型映射是否正确")
            
            if not comparison.get('repositories_match', False):
                validation_result['issues'].append("仓库分布不匹配")
                validation_result['recommendations'].append("检查仓库识别逻辑是否正确")
            
            # 5. 总体验证结果
            validation_result['validation_passed'] = comparison.get('overall_match', False)
            
            if validation_result['validation_passed']:
                logger.info("✅ 数据完整性验证通过")
            else:
                logger.warning(f"⚠️ 数据完整性验证未通过: {len(validation_result['issues'])} 个问题")
            
        except Exception as e:
            logger.error(f"❌ 数据完整性验证失败: {e}")
            validation_result['issues'].append(f"验证过程异常: {e}")
        
        return validation_result
    
    def print_validation_report(self, validation_result: Dict[str, Any]):
        """打印验证报告"""
        logger.info("📋 数据完整性验证报告")
        logger.info("=" * 60)
        
        # 基本统计
        neo4j_stats = validation_result.get('neo4j_stats', {})
        logger.info(f"Neo4j统计:")
        logger.info(f"  节点总数: {neo4j_stats.get('total_nodes', 0):,}")
        logger.info(f"  关系总数: {neo4j_stats.get('total_relationships', 0):,}")
        logger.info(f"  跨仓库关系: {neo4j_stats.get('cross_repo_relationships', 0):,}")
        
        # 匹配百分比
        comparison = validation_result.get('comparison', {})
        match_percentages = comparison.get('match_percentages', {})
        logger.info(f"\n匹配度:")
        for metric, percentage in match_percentages.items():
            logger.info(f"  {metric}: {percentage:.1f}%")
        
        # 问题和建议
        issues = validation_result.get('issues', [])
        if issues:
            logger.info(f"\n发现的问题:")
            for i, issue in enumerate(issues, 1):
                logger.info(f"  {i}. {issue}")
        
        recommendations = validation_result.get('recommendations', [])
        if recommendations:
            logger.info(f"\n建议:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"  {i}. {rec}")
        
        # 总体结果
        passed = validation_result.get('validation_passed', False)
        logger.info(f"\n验证结果: {'✅ 通过' if passed else '❌ 未通过'}")
        logger.info("=" * 60)
