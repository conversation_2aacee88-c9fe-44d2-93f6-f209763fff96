"""
Neo4j配置管理
用于Pipeline-All代码知识图谱的Neo4j集成
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class Neo4jConfig:
    """Neo4j连接配置"""
    uri: str = "bolt://localhost:7687"
    username: str = "neo4j"
    password: str = "6536772a"
    database: str = "neo4j"
    
    # 批量导入配置
    batch_size: int = 1000
    max_retries: int = 3
    timeout: int = 300  # 5分钟超时
    
    # 数据文件路径
    nodes_file: str = "ast_out_index_all/nodes_all.jsonl"
    edges_file: str = "ast_out_index_all/edges_all.jsonl"
    stats_file: str = "ast_out_index_all/stats_all.json"
    
    @classmethod
    def from_env(cls) -> 'Neo4jConfig':
        """从环境变量创建配置"""
        return cls(
            uri=os.getenv('NEO4J_URI', cls.uri),
            username=os.getenv('NEO4J_USERNAME', cls.username),
            password=os.getenv('NEO4J_PASSWORD', cls.password),
            database=os.getenv('NEO4J_DATABASE', cls.database),
            batch_size=int(os.getenv('NEO4J_BATCH_SIZE', cls.batch_size)),
            max_retries=int(os.getenv('NEO4J_MAX_RETRIES', cls.max_retries)),
            timeout=int(os.getenv('NEO4J_TIMEOUT', cls.timeout))
        )


# 节点标签映射
NODE_LABELS = {
    'File': 'File',
    'Class': 'Class', 
    'Method': 'Method',
    'Property': 'Property',
    'Protocol': 'Protocol',
    'Struct': 'Struct',
    'Enum': 'Enum'
}

# 关系类型映射
RELATIONSHIP_TYPES = {
    'DEFINES': 'DEFINES',
    'ACCESSES': 'ACCESSES', 
    'DEPENDS_ON': 'DEPENDS_ON',
    'IMPORTS': 'IMPORTS',
    'INHERITS': 'INHERITS',
    'CALLS': 'CALLS',
    'EXTENDS': 'EXTENDS',
    'IMPLEMENTS': 'IMPLEMENTS'
}

# 仓库列表
REPOSITORIES = [
    'QMapBusiness',
    'QMapMiddlePlatform', 
    'QMapNaviKit',
    'DragonMapKit',
    'QMapHippy',
    'QMapFoundation',
    'QMapBaseline',
    'QMapUIKit',
    'TencentMap',
    'QMapRouteSearchKit',
    'DKProtocolsPool'
]

# 索引和约束配置
INDEXES_AND_CONSTRAINTS = [
    # 节点唯一性约束
    "CREATE CONSTRAINT node_id_unique IF NOT EXISTS FOR (n:CodeNode) REQUIRE n.id IS UNIQUE",
    
    # 节点属性索引
    "CREATE INDEX node_name_index IF NOT EXISTS FOR (n:CodeNode) ON (n.name)",
    "CREATE INDEX node_file_index IF NOT EXISTS FOR (n:CodeNode) ON (n.file)",
    "CREATE INDEX node_repo_index IF NOT EXISTS FOR (n:CodeNode) ON (n.repository)",
    "CREATE INDEX node_type_index IF NOT EXISTS FOR (n:CodeNode) ON (n.node_type)",
    
    # 文件节点索引
    "CREATE INDEX file_path_index IF NOT EXISTS FOR (f:File) ON (f.file_path)",
    
    # 类节点索引  
    "CREATE INDEX class_name_index IF NOT EXISTS FOR (c:Class) ON (c.name)",
    
    # 方法节点索引
    "CREATE INDEX method_name_index IF NOT EXISTS FOR (m:Method) ON (m.name)",
    "CREATE INDEX method_signature_index IF NOT EXISTS FOR (m:Method) ON (m.signature)",
    
    # 属性节点索引
    "CREATE INDEX property_name_index IF NOT EXISTS FOR (p:Property) ON (p.name)",
    
    # 关系属性索引
    "CREATE INDEX rel_type_index IF NOT EXISTS FOR ()-[r]-() ON (r.type)",
    "CREATE INDEX rel_source_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.source_repo)",
    "CREATE INDEX rel_target_repo_index IF NOT EXISTS FOR ()-[r]-() ON (r.target_repo)"
]
