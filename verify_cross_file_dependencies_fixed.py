#!/usr/bin/env python3
"""
Verify Cross-File Dependencies (Fixed)

This script verifies cross-file dependencies using the correct relationship patterns.
"""

from neo4j import GraphDatabase
from pathlib import Path

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🎉 验证修复后的跨文件依赖关系')
        print('=' * 60)
        
        # 1. 使用正确的层次结构查询跨文件方法调用
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_calls
        ''')
        cross_file_calls = result.single()['cross_file_calls']
        print(f'🌐 跨文件方法调用 (正确查询): {cross_file_calls:,} 条')
        
        # 2. 分析跨文件调用的文件对
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN f1.name as caller_file, f2.name as callee_file, count(*) as call_count
            ORDER BY call_count DESC
            LIMIT 15
        ''')
        
        print('\n📊 跨文件调用统计 (Top 15):')
        total_cross_file_calls = 0
        for record in result:
            caller_file = Path(record['caller_file']).name
            callee_file = Path(record['callee_file']).name
            call_count = record['call_count']
            total_cross_file_calls += call_count
            print(f'  {caller_file} -> {callee_file}: {call_count:,} 条')
        
        # 3. 分析跨文件属性访问
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:DEFINES]->(m1:Method)-[:ACCESSES]->(p:Property)<-[:DEFINES]-(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_accesses
        ''')
        cross_file_accesses = result.single()['cross_file_accesses']
        print(f'\n🔗 跨文件属性访问: {cross_file_accesses:,} 条')
        
        # 4. 分析跨文件继承关系
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:INHERITS]->(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_inheritance
        ''')
        cross_file_inheritance = result.single()['cross_file_inheritance']
        print(f'🧬 跨文件继承关系: {cross_file_inheritance:,} 条')
        
        # 5. 分析跨文件协议实现
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:IMPLEMENTS]->(p:Protocol)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_protocols
        ''')
        cross_file_protocols = result.single()['cross_file_protocols']
        print(f'📋 跨文件协议实现: {cross_file_protocols:,} 条')
        
        # 6. 分析具体的跨文件调用示例
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN f1.name as caller_file, c1.name as caller_class, m1.name as caller_method,
                   f2.name as callee_file, c2.name as callee_class, m2.name as callee_method
            LIMIT 10
        ''')
        
        print('\n🔍 跨文件调用示例:')
        for record in result:
            caller_file = Path(record['caller_file']).name
            caller_class = record['caller_class']
            caller_method = record['caller_method']
            callee_file = Path(record['callee_file']).name
            callee_class = record['callee_class']
            callee_method = record['callee_method']
            
            print(f'  {caller_file}: {caller_class}::{caller_method}')
            print(f'    -> {callee_file}: {callee_class}::{callee_method}')
            print()
        
        # 7. 分析跨仓库依赖关系
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2 AND f1.repository IS NOT NULL AND f2.repository IS NOT NULL
            AND f1.repository <> f2.repository
            RETURN f1.repository as caller_repo, f2.repository as callee_repo, count(*) as call_count
            ORDER BY call_count DESC
            LIMIT 10
        ''')
        
        print('🌍 跨仓库依赖关系:')
        cross_repo_calls = 0
        for record in result:
            caller_repo = record['caller_repo']
            callee_repo = record['callee_repo']
            call_count = record['call_count']
            cross_repo_calls += call_count
            print(f'  {caller_repo} -> {callee_repo}: {call_count:,} 条')
        
        if cross_repo_calls == 0:
            print('  (未检测到跨仓库调用，可能repository属性未设置)')
        
        # 8. 总结分析结果
        print('\n📈 跨文件依赖分析总结:')
        print(f'  ✅ 跨文件方法调用: {cross_file_calls:,} 条')
        print(f'  ✅ 跨文件属性访问: {cross_file_accesses:,} 条')
        print(f'  ✅ 跨文件继承关系: {cross_file_inheritance:,} 条')
        print(f'  ✅ 跨文件协议实现: {cross_file_protocols:,} 条')
        print(f'  ✅ 跨仓库依赖: {cross_repo_calls:,} 条')
        
        total_cross_file_deps = cross_file_calls + cross_file_accesses + cross_file_inheritance + cross_file_protocols
        print(f'\n🎯 跨文件依赖总数: {total_cross_file_deps:,} 条')
        
        if total_cross_file_deps > 0:
            print('🎉 跨文件依赖关系检测成功！')
        else:
            print('⚠️ 跨文件依赖关系仍需进一步分析')

    driver.close()

if __name__ == "__main__":
    main()
