#!/usr/bin/env python3
"""
分析重复ID问题

这个脚本分析AST提取器生成的重复ID问题，找出根本原因。
"""

import json
import hashlib
from pathlib import Path
from collections import defaultdict, Counter

def sha1(text: str) -> str:
    """Generate SHA1 hash for node IDs"""
    return hashlib.sha1(text.encode()).hexdigest()

def analyze_duplicate_ids():
    print('🔍 分析重复ID问题')
    print('=' * 60)
    
    nodes_file = Path('test_p0_fix/nodes.jsonl')
    if not nodes_file.exists():
        print(f'❌ 节点文件不存在: {nodes_file}')
        return
    
    # 统计ID使用情况
    id_to_nodes = defaultdict(list)
    id_counter = Counter()
    label_counter = Counter()
    
    with open(nodes_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            if line.strip():
                try:
                    node = json.loads(line.strip())
                    node_id = node['id']
                    label = node['label']
                    attrs = node.get('attrs', {})
                    
                    id_counter[node_id] += 1
                    label_counter[label] += 1
                    
                    # 记录节点信息
                    node_info = {
                        'line': line_num,
                        'label': label,
                        'attrs': attrs
                    }
                    id_to_nodes[node_id].append(node_info)
                    
                except json.JSONDecodeError as e:
                    print(f'⚠️ JSON解析错误在第{line_num}行: {e}')
    
    print(f'📊 总节点数: {sum(label_counter.values()):,}')
    print(f'📊 唯一ID数: {len(id_to_nodes):,}')
    print(f'📊 节点类型分布:')
    for label, count in label_counter.most_common():
        print(f'  {label}: {count:,}')
    
    # 找出重复的ID
    duplicate_ids = {node_id: nodes for node_id, nodes in id_to_nodes.items() if len(nodes) > 1}
    
    print(f'\n🚨 发现 {len(duplicate_ids)} 个重复ID:')
    
    if not duplicate_ids:
        print('✅ 没有发现重复ID')
        return
    
    # 分析重复ID的详细信息
    for node_id, nodes in list(duplicate_ids.items())[:10]:  # 只显示前10个
        print(f'\n🔍 重复ID: {node_id}')
        print(f'  重复次数: {len(nodes)}')
        
        # 分析重复节点的属性
        for i, node_info in enumerate(nodes):
            attrs = node_info['attrs']
            print(f'  节点 {i+1}: {node_info["label"]}')
            print(f'    文件: {attrs.get("file", "未知")}')
            print(f'    名称: {attrs.get("name", "未知")}')
            print(f'    类: {attrs.get("class", "未知")}')
            print(f'    行号: {node_info["line"]}')
            
            # 尝试重构ID生成逻辑
            if node_info['label'] == 'Class':
                expected_id = sha1(f"{attrs.get('file', '')}::{attrs.get('name', '')}")
                print(f'    预期ID: {expected_id}')
            elif node_info['label'] == 'Method':
                expected_id = sha1(f"{attrs.get('file', '')}::{attrs.get('class', '')}::{attrs.get('name', '')}")
                print(f'    预期ID: {expected_id}')
            elif node_info['label'] == 'Property':
                expected_id = sha1(f"{attrs.get('file', '')}::{attrs.get('class', '')}::{attrs.get('name', '')}")
                print(f'    预期ID: {expected_id}')
    
    # 分析重复ID的模式
    print(f'\n📊 重复ID按标签分布:')
    duplicate_by_label = defaultdict(int)
    for node_id, nodes in duplicate_ids.items():
        for node in nodes:
            duplicate_by_label[node['label']] += 1
    
    for label, count in duplicate_by_label.items():
        print(f'  {label}: {count} 个重复节点')
    
    # 分析可能的原因
    print(f'\n🔍 可能的重复原因分析:')
    
    # 检查是否有相同的文件+名称组合
    same_file_name_combos = defaultdict(list)
    for node_id, nodes in duplicate_ids.items():
        for node in nodes:
            attrs = node['attrs']
            if node['label'] in ['Class', 'Method', 'Property']:
                key = f"{attrs.get('file', '')}::{attrs.get('name', '')}"
                if node['label'] in ['Method', 'Property']:
                    key = f"{attrs.get('file', '')}::{attrs.get('class', '')}::{attrs.get('name', '')}"
                same_file_name_combos[key].append((node_id, node))
    
    print(f'  检查相同文件+名称组合...')
    for combo, node_list in same_file_name_combos.items():
        if len(node_list) > 1:
            print(f'    {combo}: {len(node_list)} 个节点')
    
    # 检查虚拟节点重复
    virtual_duplicates = 0
    for node_id, nodes in duplicate_ids.items():
        for node in nodes:
            if node['attrs'].get('virtual', False):
                virtual_duplicates += 1
                break
    
    print(f'  涉及虚拟节点的重复: {virtual_duplicates} 个')
    
    # 提供修复建议
    print(f'\n💡 修复建议:')
    print(f'  1. 检查ID生成逻辑是否包含足够的唯一性信息')
    print(f'  2. 考虑在ID中添加更多区分信息（如行号、上下文等）')
    print(f'  3. 实施去重机制，避免重复节点创建')
    print(f'  4. 检查虚拟节点和实际节点的ID冲突')

def analyze_specific_duplicate_ids():
    """分析特定的重复ID"""
    print(f'\n🔍 分析特定重复ID:')
    
    # 这些是从Neo4j约束创建失败中发现的重复ID
    duplicate_ids = [
        '00939a192b6f268c5f04e6fd9487ca535507de17',  # Class
        '0011a6f4be7ae57075f44906737e381c2d0fbcd4',  # Method
        '11b45b4e7007926bbfa872e0f32206affe5353a2'   # Property
    ]
    
    nodes_file = Path('test_p0_fix/nodes.jsonl')
    
    for target_id in duplicate_ids:
        print(f'\n🎯 分析ID: {target_id}')
        matching_nodes = []
        
        with open(nodes_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        node = json.loads(line.strip())
                        if node['id'] == target_id:
                            matching_nodes.append({
                                'line': line_num,
                                'node': node
                            })
                    except json.JSONDecodeError:
                        continue
        
        print(f'  找到 {len(matching_nodes)} 个匹配节点:')
        for i, match in enumerate(matching_nodes):
            node = match['node']
            attrs = node['attrs']
            print(f'    节点 {i+1} (行 {match["line"]}):')
            print(f'      标签: {node["label"]}')
            print(f'      文件: {attrs.get("file", "未知")}')
            print(f'      名称: {attrs.get("name", "未知")}')
            print(f'      类: {attrs.get("class", "未知")}')
            print(f'      虚拟: {attrs.get("virtual", False)}')
            
            # 重新计算ID看是否匹配
            if node['label'] == 'Class':
                recalc_id = sha1(f"{attrs.get('file', '')}::{attrs.get('name', '')}")
            elif node['label'] == 'Method':
                if attrs.get('virtual', False):
                    recalc_id = sha1(f"virtual_method::{attrs.get('class', '')}::{attrs.get('name', '')}")
                else:
                    recalc_id = sha1(f"{attrs.get('file', '')}::{attrs.get('class', '')}::{attrs.get('name', '')}")
            elif node['label'] == 'Property':
                if attrs.get('virtual', False):
                    recalc_id = sha1(f"virtual_property::{attrs.get('name', '')}")
                else:
                    recalc_id = sha1(f"{attrs.get('file', '')}::{attrs.get('class', '')}::{attrs.get('name', '')}")
            else:
                recalc_id = "未知"
            
            print(f'      重算ID: {recalc_id}')
            print(f'      ID匹配: {"✅" if recalc_id == target_id else "❌"}')

if __name__ == "__main__":
    analyze_duplicate_ids()
    analyze_specific_duplicate_ids()
