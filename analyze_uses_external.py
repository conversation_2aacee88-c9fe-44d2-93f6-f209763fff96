#!/usr/bin/env python3
"""
Analyze USES Relationships and External Nodes

This script analyzes USES relationships and external node patterns.
"""

from neo4j import GraphDatabase
from pathlib import Path

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 深入分析USES关系和external节点')
        print('=' * 60)
        
        # 1. USES关系总体统计
        result = session.run('MATCH ()-[r:USES]->() RETURN count(r) as uses_count')
        uses_count = result.single()['uses_count']
        print(f'📊 USES关系总数: {uses_count:,}')
        
        # 2. USES关系的模式分析
        result = session.run('''
            MATCH (src)-[r:USES]->(dst)
            RETURN labels(src) as src_type, labels(dst) as dst_type, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 USES关系模式分析:')
        for record in result:
            src_type = ', '.join(record['src_type'])
            dst_type = ', '.join(record['dst_type'])
            count = record['count']
            print(f'  {src_type} -> USES -> {dst_type}: {count:,}')
        
        # 3. 分析external标记的节点
        result = session.run('''
            MATCH (n)
            WHERE n.file = 'external' OR n.external = true
            RETURN labels(n) as node_type, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 external标记的节点分布:')
        total_external = 0
        for record in result:
            node_type = ', '.join(record['node_type'])
            count = record['count']
            total_external += count
            print(f'  {node_type}: {count:,} 个external节点')
        
        print(f'\n📈 external节点总数: {total_external:,}')
        
        # 4. 分析File -> USES -> Class (external)的具体内容
        result = session.run('''
            MATCH (f:File)-[r:USES]->(c:Class)
            WHERE c.file = 'external'
            RETURN f.name as file_name, c.name as class_name, count(*) as usage_count
            ORDER BY usage_count DESC
            LIMIT 15
        ''')
        
        print('\n📊 File -> external Class 使用统计 (Top 15):')
        for record in result:
            file_name = Path(record['file_name']).name if record['file_name'] else 'unknown'
            class_name = record['class_name']
            usage_count = record['usage_count']
            print(f'  {file_name} -> {class_name}: {usage_count} 次使用')
        
        # 5. 分析external类的特征
        result = session.run('''
            MATCH (c:Class)
            WHERE c.file = 'external'
            RETURN c.name as class_name, c.type as class_type, count(*) as frequency
            ORDER BY frequency DESC
            LIMIT 15
        ''')
        
        print('\n📊 最频繁的external类 (Top 15):')
        for record in result:
            class_name = record['class_name']
            class_type = record['class_type']
            frequency = record['frequency']
            print(f'  {class_name} ({class_type}): {frequency} 次出现')
        
        # 6. 分析USES关系的语义合理性
        result = session.run('''
            MATCH (f:File)-[r:USES]->(c:Class)
            WHERE c.file <> 'external'
            RETURN count(*) as internal_uses
        ''')
        internal_uses = result.single()['internal_uses']
        
        result = session.run('''
            MATCH (f:File)-[r:USES]->(c:Class)
            WHERE c.file = 'external'
            RETURN count(*) as external_uses
        ''')
        external_uses = result.single()['external_uses']
        
        print(f'\n📊 USES关系的内外部分布:')
        print(f'  内部类使用: {internal_uses:,} 次')
        print(f'  外部类使用: {external_uses:,} 次')
        print(f'  外部依赖比例: {external_uses/(internal_uses+external_uses)*100:.1f}%')
        
        # 7. 分析external节点对图谱质量的影响
        result = session.run('''
            MATCH (n)
            WHERE n.file = 'external'
            OPTIONAL MATCH (n)-[r]-()
            RETURN labels(n) as node_type, count(DISTINCT r) as relationship_count, count(n) as node_count
            ORDER BY relationship_count DESC
        ''')
        
        print('\n📊 external节点的关系密度:')
        for record in result:
            node_type = ', '.join(record['node_type'])
            relationship_count = record['relationship_count']
            node_count = record['node_count']
            avg_relations = relationship_count / node_count if node_count > 0 else 0
            print(f'  {node_type}: {node_count:,} 节点, 平均 {avg_relations:.1f} 个关系')
        
        # 8. 分析系统框架类的识别
        result = session.run('''
            MATCH (c:Class)
            WHERE c.file = 'external' AND (
                c.name STARTS WITH 'NS' OR 
                c.name STARTS WITH 'UI' OR 
                c.name STARTS WITH 'CG' OR 
                c.name STARTS WITH 'CF' OR
                c.name STARTS WITH 'CA'
            )
            RETURN 
                CASE 
                    WHEN c.name STARTS WITH 'NS' THEN 'Foundation'
                    WHEN c.name STARTS WITH 'UI' THEN 'UIKit'
                    WHEN c.name STARTS WITH 'CG' THEN 'CoreGraphics'
                    WHEN c.name STARTS WITH 'CF' THEN 'CoreFoundation'
                    WHEN c.name STARTS WITH 'CA' THEN 'CoreAnimation'
                    ELSE 'Other'
                END as framework,
                count(*) as class_count
            ORDER BY class_count DESC
        ''')
        
        print('\n📊 系统框架类识别:')
        for record in result:
            framework = record['framework']
            class_count = record['class_count']
            print(f'  {framework}: {class_count} 个类')
        
        # 9. 分析USES关系的创建原因
        result = session.run('''
            MATCH (f:File)-[r:USES]->(c:Class)
            RETURN r.reason as usage_reason, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 USES关系创建原因:')
        for record in result:
            usage_reason = record['usage_reason']
            count = record['count']
            print(f'  {usage_reason}: {count:,} 次')

    driver.close()

if __name__ == "__main__":
    main()
