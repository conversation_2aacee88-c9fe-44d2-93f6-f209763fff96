#!/usr/bin/env python3
"""
Relationship Import Performance Diagnosis

This script analyzes the performance bottlenecks in relationship import.
"""

import json
import time
from pathlib import Path
from neo4j import GraphDatabase
from collections import defaultdict, Counter

def analyze_relationship_patterns():
    """Analyze relationship patterns in the data file"""
    print('🔍 分析关系数据模式...')
    
    edges_file = Path('test_p0_fix/edges.jsonl')
    if not edges_file.exists():
        print(f'❌ 关系文件不存在: {edges_file}')
        return
    
    edge_types = Counter()
    src_node_types = Counter()
    dst_node_types = Counter()
    total_edges = 0
    
    with open(edges_file, 'r') as f:
        for line in f:
            if line.strip():
                edge = json.loads(line.strip())
                edge_types[edge['type']] += 1
                total_edges += 1
                
                # Analyze node ID patterns (first few chars indicate type)
                src_id = edge['src']
                dst_id = edge['dst']
                
                # Simple heuristic to identify node types from ID patterns
                if len(src_id) > 8:
                    src_node_types[src_id[:8]] += 1
                if len(dst_id) > 8:
                    dst_node_types[dst_id[:8]] += 1
    
    print(f'📊 关系数据分析结果:')
    print(f'  总关系数: {total_edges:,}')
    print(f'  关系类型数: {len(edge_types)}')
    
    print(f'\n📊 关系类型分布:')
    for edge_type, count in edge_types.most_common():
        percentage = count / total_edges * 100
        print(f'  {edge_type}: {count:,} ({percentage:.1f}%)')
    
    print(f'\n📊 源节点ID模式 (Top 10):')
    for pattern, count in src_node_types.most_common(10):
        print(f'  {pattern}*: {count:,}')
    
    print(f'\n📊 目标节点ID模式 (Top 10):')
    for pattern, count in dst_node_types.most_common(10):
        print(f'  {pattern}*: {count:,}')
    
    return edge_types, total_edges

def test_index_performance():
    """Test index performance for node lookups"""
    print('\n🔧 测试索引性能...')
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    try:
        with driver.session() as session:
            # Check if indexes exist
            result = session.run('SHOW INDEXES')
            indexes = list(result)
            
            print(f'📊 当前索引状态:')
            for index in indexes:
                print(f'  {index.get("name", "unnamed")}: {index.get("labelsOrTypes", [])} ON {index.get("properties", [])}')
            
            # Test lookup performance with EXPLAIN
            print(f'\n⚡ 测试节点查找性能:')
            
            # Get a sample node ID
            result = session.run('MATCH (n) RETURN n.id as id LIMIT 1')
            sample_id = result.single()
            if sample_id:
                sample_id = sample_id['id']
                
                # Test lookup performance
                start_time = time.time()
                result = session.run('MATCH (n {id: $id}) RETURN count(n)', id=sample_id)
                lookup_time = time.time() - start_time
                count = result.single()['count(n)']
                
                print(f'  单节点查找: {lookup_time*1000:.2f}ms (找到 {count} 个节点)')
                
                # Test EXPLAIN for optimization hints
                result = session.run('EXPLAIN MATCH (n {id: $id}) RETURN n', id=sample_id)
                plan = result.consume().plan
                print(f'  查询计划: {plan.operator_type if plan else "无法获取"}')
    
    finally:
        driver.close()

def test_batch_relationship_import():
    """Test different batch sizes for relationship import"""
    print('\n🧪 测试不同批次大小的关系导入性能...')
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    edges_file = Path('test_p0_fix/edges.jsonl')
    
    # Test different batch sizes
    batch_sizes = [100, 500, 1000, 2000]
    test_results = {}
    
    try:
        with driver.session() as session:
            # Load a sample of edges for testing
            sample_edges = []
            with open(edges_file, 'r') as f:
                for i, line in enumerate(f):
                    if i >= 5000:  # Test with 5000 edges
                        break
                    if line.strip():
                        sample_edges.append(json.loads(line.strip()))
            
            print(f'  使用 {len(sample_edges)} 条关系进行测试')
            
            for batch_size in batch_sizes:
                print(f'\n  测试批次大小: {batch_size}')
                
                # Clear test relationships
                session.run('MATCH ()-[r:TEST_REL]->() DELETE r')
                
                start_time = time.time()
                imported_count = 0
                
                # Import in batches
                for i in range(0, len(sample_edges), batch_size):
                    batch = sample_edges[i:i+batch_size]
                    
                    # Group by type for efficiency
                    edges_by_type = defaultdict(list)
                    for edge in batch:
                        # Use TEST_REL to avoid conflicts
                        edge_copy = edge.copy()
                        edge_copy['type'] = 'TEST_REL'
                        edges_by_type['TEST_REL'].append(edge_copy)
                    
                    for edge_type, edges in edges_by_type.items():
                        query = f'''
                        UNWIND $edges AS edge
                        MATCH (src {{id: edge.src}})
                        MATCH (dst {{id: edge.dst}})
                        CREATE (src)-[r:{edge_type}]->(dst)
                        SET r += edge.attrs
                        '''
                        session.run(query, edges=edges)
                    
                    imported_count += len(batch)
                
                elapsed_time = time.time() - start_time
                rate = imported_count / elapsed_time if elapsed_time > 0 else 0
                
                test_results[batch_size] = {
                    'time': elapsed_time,
                    'rate': rate,
                    'count': imported_count
                }
                
                print(f'    耗时: {elapsed_time:.2f}s, 速度: {rate:.0f} 关系/秒')
                
                # Clean up
                session.run('MATCH ()-[r:TEST_REL]->() DELETE r')
    
    finally:
        driver.close()
    
    # Find optimal batch size
    best_batch_size = max(test_results.keys(), key=lambda k: test_results[k]['rate'])
    print(f'\n🎯 最优批次大小: {best_batch_size} (速度: {test_results[best_batch_size]["rate"]:.0f} 关系/秒)')
    
    return best_batch_size, test_results

def analyze_query_performance():
    """Analyze different query patterns for relationship import"""
    print('\n🔍 分析不同查询模式的性能...')
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    try:
        with driver.session() as session:
            # Get sample node IDs
            result = session.run('MATCH (n) RETURN n.id as id LIMIT 100')
            node_ids = [record['id'] for record in result]
            
            if len(node_ids) < 2:
                print('❌ 节点数量不足，无法测试')
                return
            
            src_id, dst_id = node_ids[0], node_ids[1]
            
            # Test different query patterns
            query_patterns = {
                'MATCH + CREATE': '''
                    MATCH (src {id: $src_id})
                    MATCH (dst {id: $dst_id})
                    CREATE (src)-[r:TEST_REL]->(dst)
                    SET r.test = true
                ''',
                'MERGE nodes + CREATE': '''
                    MERGE (src {id: $src_id})
                    MERGE (dst {id: $dst_id})
                    CREATE (src)-[r:TEST_REL]->(dst)
                    SET r.test = true
                ''',
                'Single MATCH with WHERE': '''
                    MATCH (src), (dst)
                    WHERE src.id = $src_id AND dst.id = $dst_id
                    CREATE (src)-[r:TEST_REL]->(dst)
                    SET r.test = true
                '''
            }
            
            for pattern_name, query in query_patterns.items():
                # Clean up previous test
                session.run('MATCH ()-[r:TEST_REL]->() DELETE r')
                
                # Time the query
                start_time = time.time()
                session.run(query, src_id=src_id, dst_id=dst_id)
                elapsed_time = time.time() - start_time
                
                print(f'  {pattern_name}: {elapsed_time*1000:.2f}ms')
    
    finally:
        driver.close()

def main():
    print('🔍 关系导入性能瓶颈诊断')
    print('=' * 60)
    
    # Step 1: Analyze relationship data patterns
    edge_types, total_edges = analyze_relationship_patterns()
    
    # Step 2: Test index performance
    test_index_performance()
    
    # Step 3: Test different batch sizes
    optimal_batch_size, batch_results = test_batch_relationship_import()
    
    # Step 4: Analyze query performance
    analyze_query_performance()
    
    # Step 5: Generate optimization recommendations
    print('\n' + '=' * 60)
    print('🎯 性能优化建议')
    print('=' * 60)
    
    print(f'📊 数据规模分析:')
    print(f'  总关系数: {total_edges:,}')
    print(f'  关系类型: {len(edge_types)} 种')
    
    print(f'\n⚡ 性能优化建议:')
    print(f'  1. 最优批次大小: {optimal_batch_size}')
    print(f'  2. 预期性能提升: 基于测试结果调整批处理策略')
    print(f'  3. 索引优化: 确保所有节点类型都有id索引')
    print(f'  4. 查询优化: 使用最高效的查询模式')
    
    # Calculate expected performance
    if optimal_batch_size in batch_results:
        optimal_rate = batch_results[optimal_batch_size]['rate']
        estimated_time = total_edges / optimal_rate if optimal_rate > 0 else 0
        print(f'\n📈 性能预测:')
        print(f'  优化后预期速度: {optimal_rate:.0f} 关系/秒')
        print(f'  预计总导入时间: {estimated_time:.1f} 秒')
        print(f'  相比当前性能提升: 显著改善')

if __name__ == "__main__":
    main()
