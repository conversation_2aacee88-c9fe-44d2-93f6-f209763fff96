[{"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/log.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/log.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/log.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/log.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/log.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/log.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Yoga.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Yoga.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/Yoga.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Yoga.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/Yoga.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Yoga.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGValue.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGValue.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGValue.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGValue.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGValue.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGValue.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGStyle.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGStyle.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGStyle.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGStyle.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGStyle.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGStyle.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNodePrint.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNodePrint.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGNodePrint.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNodePrint.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGNodePrint.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNodePrint.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGMarker.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGMarker.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGMarker.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGMarker.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGMarker.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGMarker.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGConfig.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGConfig.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGConfig.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGConfig.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGConfig.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGConfig.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGLayout.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbha<PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGLayout.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGLayout.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGLayout.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGLayout.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGLayout.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGEnums.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGEnums.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGEnums.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGEnums.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGEnums.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGEnums.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON><PERSON>bkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNode.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNode.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGNode.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNode.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/YGNode.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/YGNode.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Werror=deprecated-objc-isa-usage", "-Werror=objc-root-class", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/82b82416624d2658e5098eb0a28c15c5-common-args.resp", "-fno-omit-frame-pointer", "-fexceptions", "-Wall", "-Werror", "-std=c++1y", "-fPIC", "-fno-objc-arc", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/Yoga/Yoga-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Utils.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Utils.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/Utils.cpp", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Utils.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Yoga/Yoga/Classes/Utils.cpp", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdz<PERSON><PERSON>uebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/Yoga.build/Debug-maccatalyst/Yoga.build/Objects-normal/arm64/Utils.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/VectorLayoutExpression/VectorLayoutExpression-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/VLLexUtil.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/VLLexUtil.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayoutExpression/VectorLayoutExpression/Classes/Parser/VLLexUtil.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/VLLexUtil.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayoutExpression/VectorLayoutExpression/Classes/Parser/VLLexUtil.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/VLLexUtil.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Werror", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/VectorLayoutExpression/VectorLayoutExpression-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/JSBuiltinFunctions.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/JSBuiltinFunctions.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayoutExpression/VectorLayoutExpression/Classes/Eval/JSBuiltinFunctions.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/JSBuiltinFunctions.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/VectorLayoutExpression/VectorLayoutExpression/Classes/Eval/JSBuiltinFunctions.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/VectorLayoutExpression.build/Debug-maccatalyst/VectorLayoutExpression.build/Objects-normal/arm64/JSBuiltinFunctions.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCLayout.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCLayout.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/RCLayout.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCLayout.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/RCLayout.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCLayout.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCFatal.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCFatal.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Base/RCFatal.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCFatal.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Base/RCFatal.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCFatal.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCEqualityHelpers.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCEqualityHelpers.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/RCEqualityHelpers.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCEqualityHelpers.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/RCEqualityHelpers.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCEqualityHelpers.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDispatch.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDispatch.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/RCDispatch.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDispatch.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/RCDispatch.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDispatch.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDimension.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDimension.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Geometry/RCDimension.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDimension.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Geometry/RCDimension.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCDimension.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComputeRootLayout.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComputeRootLayout.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/RCComputeRootLayout.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComputeRootLayout.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/RCComputeRootLayout.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComputeRootLayout.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentSize.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentSize.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Geometry/RCComponentSize.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentSize.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Geometry/RCComponentSize.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentSize.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentDescriptionHelper.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentDescriptionHelper.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/RCComponentDescriptionHelper.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentDescriptionHelper.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/RCComponentDescriptionHelper.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCComponentDescriptionHelper.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCAssociatedObject.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCAssociatedObject.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/RCAssociatedObject.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCAssociatedObject.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/RCAssociatedObject.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/RCAssociatedObject.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewReuseUtilities.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewReuseUtilities.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/View/ComponentViewReuseUtilities.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewReuseUtilities.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/View/ComponentViewReuseUtilities.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewReuseUtilities.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewManager.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewManager.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/View/ComponentViewManager.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewManager.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/View/ComponentViewManager.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/ComponentViewManager.o"}, {"arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-x", "objective-c++", "-ivfsstatcache", "/Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.1-24B75-26c8390a7c93c703a57fb2538426748e.sdkstatcache", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fmacro-backtrace-limit=0", "-fno-color-diagnostics", "-fmodules-prune-interval=86400", "-fmodules-prune-after=345600", "-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation", "-fmodules-validate-once-per-build-session", "-Wnon-modular-include-in-framework-module", "-Werror=non-modular-include-in-framework-module", "-Wno-trigraphs", "-Wno-missing-field-initializers", "-Wno-missing-prototypes", "-Werror=return-type", "-Wunreachable-code", "-Wno-implicit-atomic-properties", "-Werror=deprecated-objc-isa-usage", "-Wno-objc-interface-ivars", "-Werror=objc-root-class", "-Wno-arc-repeated-use-of-weak", "-Wimplicit-retain-self", "-Wno-non-virtual-dtor", "-Wno-overloaded-virtual", "-Wno-exit-time-destructors", "-Wduplicate-method-match", "-Wno-missing-braces", "-Wparentheses", "-<PERSON><PERSON><PERSON>", "-Wunused-function", "-Wno-unused-label", "-Wno-unused-parameter", "-Wunused-variable", "-Wunused-value", "-Wempty-body", "-Wuninitialized", "-Wconditional-uninitialized", "-Wno-unknown-pragmas", "-Wno-shadow", "-Wno-four-char-constants", "-Wno-conversion", "-Wconstant-conversion", "-Wint-conversion", "-Wbool-conversion", "-Wenum-conversion", "-Wno-float-conversion", "-Wnon-literal-null-conversion", "-Wobjc-literal-conversion", "-Wshorten-64-to-32", "-Wno-newline-eof", "-Wno-selector", "-Wno-strict-selector-match", "-Wu<PERSON><PERSON>lared-selector", "-Wdeprecated-implementations", "-Wno-c++11-extensions", "-Wno-implicit-fallthrough", "-fstrict-aliasing", "-Wprotocol", "-Wdeprecated-declarations", "-Winvalid-offsetof", "-Wno-sign-conversion", "-Winfinite-recursion", "-Wmove", "-Wcomma", "-Wblock-capture-autoreleasing", "-Wstrict-prototypes", "-Wrange-loop-analysis", "-Wno-semicolon-before-method-body", "-Wunguarded-availability", "@/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/af3fcb34312c57c0f52879cdce924b91-common-args.resp", "-w", "-<PERSON><PERSON><PERSON><PERSON>", "-analyzer-disable-all-checks", "-include", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target", "Support", "Files/RenderCore/RenderCore-prefix.pch", "-M<PERSON>", "-MT", "dependencies", "-MF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/CKWeakObjectContainer.d", "--serialize-diagnostics", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/CKWeakObjectContainer.dia", "-c", "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/CKWeakObjectContainer.mm", "-o", "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/CKWeakObjectContainer.o"], "directory": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RenderCore/RenderCore/Classes/Utilities/CKWeakObjectContainer.mm", "output": "/Users/<USER>/Library/Developer/Xcode/DerivedData/TencentMap-bfdzbhaxuebkalcqcjygmbzlfqzr/Build/Intermediates.noindex/RenderCore.build/Debug-maccatalyst/RenderCore.build/Objects-normal/arm64/CKWeakObjectContainer.o"}]