{"repositories": [{"name": "QMapBusiness", "path": "hammmer-workspace/QMapBusiness", "type": "business", "source_files": 1686, "header_files": 1815, "priority": 1, "enabled": true, "schemes": ["QMapBusiness"], "dependencies": ["hippy", "TencentOpenAPI", "QMapMiddlePlatform", "assert.h>\n#import \"QMUAVAttributePanelItemView.h\"\n\n@interface QMUAVAttributePanelView ()\n", "MapBaseOCModel", "Aspects", "AVKit", "QMapFoundation", "GLMapLib", "MapBizOCMiddleware", "Masonry", "ApolloSDK.h>\n#import <QMapMiddlePlatform", "NSObject+FBKVOController.h>\n\n@interface QMCarPlayNavigationButtonsModule () <DMMapViewDelegate>\n\n@property (nonatomic, strong) QMCarPlayNavigationButtonEventPipeline *pipeline;     ", "MessageUI", "SystemConfiguration", "Weibo_SDK", "RouteGuidance", "StoreKit", "ReplayKit", "PanguIosShell", "KVOController.h>\n\n@interface QMFootprintReplayView ()\n<QMSummary3DReplayShareViewDelegate,QMSummary3DReplaySharePanelViewDelegate,QMFootprintReplayShareManagerDelegate, DMMapViewDelegate>\n@property (nonatomic) CFAbsoluteTime loadedTime;\n@property (nonatomic) QMButton *backButton;\n@property (nonatomic) QMButton *shareButton;\n@property (nonatomic) QMButton *replayButton;\n@property (nonatomic) QMSummary3DReplayShareView *shareView;\n@property (nonatomic) QMFootprintReplayStatusAnimationView *loadingView;\n@property (nonatomic) QMFootprintReplayShareManager *shareMgr;\n\n@property (nonatomic) DMCustomTileModel3DLayer *treeModel3DLayer;\n@property (nonatomic) BOOL needShowShareViewWhenReplayFinish;\n\n@property (nonatomic) int replay3DCycleFileType;\n@property (nonatomic) int replay3DWalkFileType;\n@property (nonatomic) int replay3DCarFileType;\n@property (nonatomic) int replayHDCarFileType;\n@property (nonatomic) int replayLongDistanceCarFileType;\n\n@end\n\n@implementation QMFootprintReplayView\n\n- (instancetype)init {\n    if (self = [super init]) {\n        _shareButtonOriginHidden = YES;\n        self.loadedTime = CFAbsoluteTimeGetCurrent();\n        [self initView];\n        [self mapInitialOptionSetting];\n    }\n    return self;\n}\n\n- (void)initView {\n    [self addSubview:self.mapView];\n    self.contentView = [UIView new];\n    [self addSubview:self.contentView];\n    [self.contentView addSubview:self.replayButton];\n    [self.contentView addSubview:self.loadingView];\n    [self.contentView addSubview:self.backButton];\n    [self.contentView addSubview:self.shareButton];\n    [self.contentView addSubview:self.shareView];\n    \n    [self.mapView mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.edges.offset(0);\n    }];\n    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.edges.offset(0);\n    }];\n    [self.replayButton mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.centerX.equalTo(self);\n        make.centerY.offset(-140);\n    }];\n    [self.loadingView mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.edges.offset(0);\n    }];\n    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.width.height.equalTo(@44);\n        make.left.equalTo(@4);\n        make.top.equalTo(@44);\n    }];\n    [self.shareButton mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.width.height.equalTo(@44);\n        make.right.equalTo(self.mas_right).offset(-4);\n        make.top.equalTo(@44);\n    }];\n    [self.shareView mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.edges.offset(0);\n    }];\n}\n\n#pragma mark - 树、天空盒子\n- (void)mapInitialOptionSetting {\n    APLConfigObject *configObj = ApolloSDK.getData(APLNode.business(@\"3\").module(@\"141\").config(@\"tilt_config\"));\n    NSDictionary *dic = [configObj decodeJSONStringToJSONObject];\n    \n    QMSummary3DReplayConfigModel *configModel = [QMSummary3DReplayConfigModel yy_modelWithDictionary:dic];\n    \n    QMSummary3DReplayInitialOption *initialSetting = configModel.initialOption;\n    [self.mapView setLandmarkEnable:initialSetting.isLocator3DEnable];\n    if(initialSetting.isTreeEnable) {\n        [self.mapView setDefaultTreeEnable:YES];\n        @weakify(self);\n        QMNaviTreeStrategy *treeStrategy = [[QMNaviTreeStrategy alloc] init];\n        [treeStrategy getTreeConfigOnFinish:^(DMCustomTileModel3DLayer * _Nullable layer,\n                                                   QMJCE_MapSSO_GetUniWeatherInfoResp * _Nullable weatherInfo) {\n            @strongify(self);\n            [self p_update3DTreeLayer:layer];\n        } ignoreWeatherTree:YES];\n    }\n}\n", "QMWeChatSDK", "CoreSpotlight", "QMWUP", "QMUserLocationPOIInfo+QMExtension.h>\n#import \"QMNpcDefine.h\"\n#import \"QMHomeToolCrashFeedBackManager.h\"\n#import <QMapBaseline", "QMapHippy", "QMapRouteSearchKit", "DouyinOpenSDK", "CommonCrypto", "QMapBasics", "AVFoundation", "mach", "ApolloSDK", "YYText", "NSObject+MJCoding.h>\n#import \"QMCSTFrequentPlaceDataService.h\"\n#import <MJExtension", "QMapUIKit", "TencentLBS", "QMapProto", "sys", "SDWebImage", "WebKit", "objc", "MediaPlayer", "QMShareViewControllerWithUrl.h>\n#import \"QMUgcPoiSearchViewController.h\"\n#import <QMapMiddlePlatform", "QMapBusBusiness", "QMFootprintService.h>\n#import <QMPhotoReportItem.h>\n#import <QMapProto", "<PERSON><PERSON>", "KVOController.h>\n\nNSString *const QMFootprintShareTypeValueWeixinSession = @\"weixinhaoyou\";\nNSString *const QMFootprintShareTypeValueWeixinTimeline = @\"weixinpengyouquan\";\nNSString *const QMFootprintShareTypeValueQQFriend = @\"qqhaoyou\";\n\nNSInteger const kQMFootprintReplayRecordMinMemorySize = 100;      ", "Apollo.h>\n#import <QMapFoundation", "KVOController.h>\n#import <QMapMiddlePlatform", "AudioToolbox", "CoreText", "QMSpider<PERSON>an", "pthread", "DKProtocolsPool", "QMapWidgets", "SVProgressHUD", "QMapNaviKit", "CarPlay", "JavaScriptCore", "QMHomepageViewController.h>\n#import \"QMPushMessageManager.h\"\n#import <QMapBaseline", "TXMapView", "LongLinkSDK", "PerfSight", "SSZipArchive", "Apollo.h>\n#import <QMapMiddlePlatform", "VectorLayoutiOSDebugTools", "libpag", "os", "Carplay", "VectorLayout", "QMapIntentsExtension", "QMapBusKit", "YYModel", "YYModel.h>\n\n@implementation QMSummary3DReplaySplitRadioOption\n@end\n\n@implementation QMSummary3DRouteOverlayBuilder\n\n", "QMapDarkMode", "TMDylibLazyLoadWrapper", "XiaoHongShuOpenSDK", "QMapBusiness", "CoreLocation", "QPlayAutoSDK", "Photos", "Network", "YYModel.h>\n#import <QMapNaviKit", "CoreMotion", "TencentTravelService", "MobileCoreServices", "KVOController", "QMSpiderManDebugTool", "DragonMapKit", "QMapVectorLayout", "MJExtension", "QMLocalRouteSearcher.h>\n#import <QMapMiddlePlatform", "QMapBuildTools", "MapBizNew", "TMWeAppClient", "QMapBaseline", "fmdb", "QMExtraOrdinaryMap", "Apollo.h>\n\n@implementation QMOperationDataTool\n\n+ (QMClientBannerModel *)clientBannerModelFromOperationBanner:(QMClientOperationBanner *)banner {\n    QMClientBannerModel *model = [QMClientBannerModel new];\n    model.name = banner.modelId;  ", "BeaconAPI_Base", "IntentsUI", "MJExtension.h>\n\n@implementation QMPOISearchParams\n\n- (instancetype)init {\n    self = [super init];\n    if (self) {\n        _sug_num = POISearchDataManager_NoSugNum;\n        _newQueryFlag = YES;\n        _beaconInfo = [NSMutableDictionary dictionary];\n        \n        [self setupBeaconInfo];\n    }\n    \n    return self;\n}\n\n- (void)setupBeaconInfo {\n    ", "Contacts", "TPNS-iOS"]}, {"name": "QMapMiddlePlatform", "path": "hammmer-workspace/QMapMiddlePlatform", "type": "ui_module", "source_files": 1023, "header_files": 1125, "priority": 1, "enabled": true, "schemes": ["QMapMiddlePlatform"], "dependencies": ["TencentOpenAPI", "QMapMiddlePlatform", "pthread.h>\n#import \"QttsParam+Private.h\"\n#import <QMapMiddlePlatform", "MapBaseOCModel", "EDSunriseSet", "QMapFoundation", "MapBizOCMiddleware", "Masonry", "PanguIosShell", "QMWeChatSDK", "GCDWebServer", "Masonry.h>\n#import \"QMMultistageFilterViewDefine.h\"\n\nCGFloat const QMPrimaryOptionViewIndicateLayerWith = 24;\nCGFloat const QMPrimaryOptionViewIndicateLayerHeight = 4;\n\n\n@interface QMPrimaryOptionView ()\n\n", "QMapRouteSearchKit", "TMAISound", "QMSpiderManWrapper", "UIImageView+Addition.h>\n#import \"QMNMarkerViewModel.h\"\n#import \"QMMarkerElementModel.h\"\n#import \"QMNCircleRadiationViewModel.h\"\n#import \"DMMarkerElement+TMElement.h\"\n#import \"DMRouteElement+hippy.h\"\n#import \"DMMarkerElement+hippyBridge.h\"\n#import \"QMCommonParticleManager.h\"\n\n@interface QMNMarkerDataManager ()\n\n", "QMapBasics", "AVFoundation", "CommonCrypto", "ApolloSDK", "<PERSON><PERSON>", "NSObject+FBKVOController.h>\n#import \"QMUGCLiveVideoMediaTypeView.h\"\n\nNSString *const QMUGCVideoHTMLVideoURLPlaceholderString = @\"[QMUGCVideoDefault]\";\nNSString *const QMUGCLiveVideoCardViewLog = @\"QMUGCLiveVideoCardViewLog\";\n\n@interface QMUGCLiveVideoView () <QMUGCUsingCellularDataViewDelegate, QMWebViewDelegate>\n\n", "net", "pthread.h>\n\ntypedef NS_ENUM(NSUInteger, QMTTSMonitorState) {\n    QMTTSMonitorStateDefault,\n    QMTTSMonitorStateRunning\n};\n\n@interface QMTTSMonitorService() <AVAudioRecorderDelegate>\n{\n    BOOL _needMonitor;\n}\n\n", "QMapUIKit", "TencentLBS", "QMapProto", "sys", "SDWebImage", "WebKit", "objc", "Masonry.h>\n\n@interface QMMultistageFileterContentView () <UITableViewDelegate, UITableViewDataSource, QMMultistageFileterContentNormalTableViewCellDelegate>\n\n", "Masonry.h>\n\n@interface QMSecondaryFilterView () <QMSecondaryOptionViewDelegate>\n\n", "MediaPlayer", "TBUIAutoTest.h>\n\n#ifdef QM_AUTO_TEST\nextern NSString *const kAutoTestUITurnOnKey;\nextern NSString *const kAutoTestUILongPressKey;\n#endif\n\n@interface QMUibutton ()\n", "<PERSON><PERSON>", "AudioToolbox", "pthread", "DKProtocolsPool", "QMapWidgets", "TXMapView", "SSZipArchive", "LongLinkSDK", "libpag", "Masonry.h>\n#import <SDWebImage.h>\n\n\n@interface QMSecondaryOptionView () \n\n", "VectorLayout", "YYModel", "UIImageView+WebCache.h>\n#import \"QMPageIndicatorView.h\"\n\nCG_INLINE CGFloat ratio(void) {\n    CGFloat width = [UIScreen mainScreen].nativeBounds.size.width ", "QMapDarkMode", "TMDylibLazyLoadWrapper", "CoreLocation", "Photos", "CoreMotion", "YYWebImage", "Masonry.h>\n#import \"QMUIServiceManager.h\"\n\nCGFloat getTipsHeightInPortrait(BOOL hasQQMusic) {\n    CGFloat tipsHeightInPortrait;\n    if (hasQQMusic) {\n        if (QMHasSafeArea) {\n            tipsHeightInPortrait = K_AutoDismissTips_Portrait_HasSafeArea_QQMusicOn_Height;\n        } else {\n            tipsHeightInPortrait = K_AutoDismissTips_Portrait_NoSafeArea_QQMusicOn_Height;\n        }\n    } else {\n        if (QMHasSafeArea) {\n            tipsHeightInPortrait = K_AutoDismissTips_Portrait_HasSafeArea_QQMusicOff_Height;\n        } else {\n            tipsHeightInPortrait = K_AutoDismissTips_Portrait_NoSafeArea_QQMusicOff_Height;\n        }\n    }\n    return tipsHeightInPortrait;\n}\n\nNSNotificationName QMAutoDismissTipsViewShowNotification = @\"QMAutoDismissTipsViewShowNotification\";\n\n@interface QMAutoDismissTipsView ()\n\n@property (nonatomic, strong) UIView *contentView;\n@property (nonatomic, strong) UIImageView *iconView;\n@property (nonatomic, strong) UILabel *fakeTitleLabel;  ", "KVOController", "Masonry.h>\n\n@interface QMWebLoadingView ()\n\n@property (nonatomic, copy) void(^retryActionBlock)(void);\n\n@property (nonatomic, strong) UIProgressView *progressView;\n\n@property (nonatomic, strong) QMBaseLottieEmptyView *networkEmptyView;\n\n@end\n\n@implementation QMWebLoadingView\n\n- (instancetype)initWithWebView:(UIView *)webView retryAction:(nonnull void (^)(void))retryActionClosure {\n    if (self = [super init]) {\n        _retryActionBlock = retryActionClosure;\n        [self configUI];\n    }\n    return self;\n}\n\n- (void)configUI {\n    self.userInteractionEnabled = NO;\n    self.backgroundColor = UIColor.clearColor;\n    \n    _networkEmptyView = ({\n        QMBaseLottieEmptyView *emptyView = [[QMBaseLottieEmptyView alloc] initWithFrame:self.bounds\n                                                                         lottieViewType:QMEmptyViewTypeNetworkNoWorking\n                                                                                  title:@\"网络异常\"\n                                                                                message:@\"请检查网络设置\"];\n        @weakify(self);\n        QMBaseLottieEmptyAction *action = [QMBaseLottieEmptyAction actionWithTitle:@\"重试\"\n                                                                           handler:^(QMBaseLottieEmptyAction * action) {\n            @strongify(self);\n            if (self.retryActionBlock) {\n                self.retryActionBlock();\n            }\n        }];\n        [emptyView setSingleAction:action];\n        emptyView.backgroundColor = UIColor.qdm_backgroundColor_default; ", "DragonMapKit", "ApolloSDK.h>\n\n", "MJExtension", "QMapBuildTools", "TMWeAppClient", "netinet", "AVFAudio", "pthread.h>\n#import \"QMapUserOpDataManagerProtocol.h\"\n#import \"CityListConfig.h\"\n#import \"QMKingCardManager.h\"\n#import <QMapMiddlePlatform", "QMExtraOrdinaryMap", "BeaconAPI_Base"]}, {"name": "QMapNaviKit", "path": "hammmer-workspace/QMapNaviKit", "type": "ui_module", "source_files": 461, "header_files": 512, "priority": 2, "enabled": true, "schemes": ["QMapNaviKit"], "dependencies": ["QMapWidgets", "QRouteEnlargeLib", "QMapMiddlePlatform", "MapBaseOCModel", "QMapNaviKit", "Aspects", "QMapFoundation", "GLMapLib", "MapBizOCMiddleware", "Masonry", "lottie-ios", "SSZipArchive", "LongLinkSDK", "RouteGuidance", "YYModel", "QMapDarkMode", "QMapRouteSearchKit", "CommonCrypto", "QMapBasics", "AVFoundation", "QPlayAutoSDK", "CoreMotion", "CoreLocation", "YYText", "ApolloSDK", "KVOController", "QMapUIKit", "pthread.h>\n\n@implementation QMTrafficLightBubbleModel\n\n@end\n\n#define QM_MULTI_TRAFFIC_LIGHT_DEMO 0\n\nNSUInteger const QMMaxRedLightCount = 2;\n\n@interface MBIZMultiLightCountdownTimerDrawDescriptor(Private)\n\n@property(nonatomic,readonly) BOOL isLeft;\n@property(nonatomic,readonly) BOOL isTop;\n\n@end\n\nstatic NSMutableDictionary *gApolloImage;\nstatic NSDictionary *gBubbleDirectionDictionary;\nstatic NSDictionary *gLightTypeDictionary;\nstatic NSDictionary *gTurnArrowTypeDictionary;\nstatic NSDictionary *gTurnArrowStringDictionary;\nstatic NSDictionary *gTurnArrowTypeStringDictionary;\nstatic pthread_mutex_t gApolloImageMutex;\n\n", "TencentLBS", "QMapProto", "DragonMapKit", "YYImage", "SDWebImage", "objc", "MJExtension", "MediaPlayer", "GLMapLibHeaders"]}, {"name": "QMapHippy", "path": "hammmer-workspace/QMapHippy", "type": "ui_module", "source_files": 256, "header_files": 259, "priority": 2, "enabled": true, "schemes": ["QMapHippy"], "dependencies": ["hippy", "SVProgressHUD", "QMapWidgets", "QMapMiddlePlatform", "QMapFoundation", "Masonry", "TXMapView", "Masonry.h>\n\n@interface TMWebView () <QMWebViewDelegate>\n\n@property (nonatomic) NSString *context;\n\n@property (nonatomic) NSMutableDictionary *cacheData;\n\n@property (nonatomic) QMWKWebView *webView;\n\n@property (nonatomic) BOOL loadFinish;\n\n@end\n\n@implementation TMWebView\n\n- (void)dealloc {\n    if ([self bridger].onUnregisterJSPlugins) {\n        [self bridger].onUnregisterJSPlugins(_context);\n    }\n}\n\n- (void)didMoveToWindow {\n    [super didMoveToWindow];\n    if (_onPageShowChange) {\n        _onPageShowChange(@{@\"show\" : (self.window ? @1 : @0)});\n    }\n}\n\n- (instancetype)initWithContext:(NSString *)context bridge:(HippyBridge *)bridge {\n    if (self = [super init]) {\n        _context = context;\n        self.bridge = bridge;\n        [self configUI];        \n        [DKGetService(DKPHippyBridgeService) hippy_webview_setupMessageHandler:self bridge:bridge];\n    }\n    return self;\n}\n\n#pragma mark - UI\n\n- (void)configUI {\n    _cacheData = [NSMutableDictionary new];\n    _webView = ({\n        QMWKWebView *webView = (QMWKWebView *)[QMWebViewFactory createDefaultWebView];\n        webView.backgroundColor = UIColor.tmd_background;\n        webView.qmWebViewDelegate = self;\n        webView.opaque = NO;\n        [self addSubview:webView];\n        [webView mas_makeConstraints:^(MASConstraintMaker *make) {\n            make.edges.equalTo(self);\n        }];\n        webView;\n    });\n}\n\n#pragma mark - Api\n\n- (void)setSrc:(NSString *)src {\n    if (!src.length) {\n        return;\n    }\n    NSURLRequest *request = [[NSURLRequest alloc]initWithURL:[NSURL URLWithString:src]];\n    [_webView loadRequest:request];\n}\n\n- (UIScrollView *)scrollView {\n    return _webView.scrollView;\n}\n\n- (void)saveData:(id)data forKey:(NSString *)key {\n    if (data) {\n        [_cacheData setObject:data forKey:key];        \n    }\n}\n\n- (id)getDataWithKey:(NSString *)key {\n    return [_cacheData objectForKey:key];\n}\n\n#pragma mark - Lazy\n\n- (TMWebViewBridger *)bridger {\n    return [TMWebViewBridger bridgerWithName:_context];\n}\n\n#pragma mark - QMWebViewDelegate\n\n- (BOOL)qmWebView:(id<QMWebViewProtocol>)webView\nshouldStartLoadWithRequest:(NSURLRequest *)request\n   navigationType:(QMWebViewNavigationType)navigationType\n      isMainFrame:(BOOL)isMainFrame {\n    if ([DKGetService(DKPHippyBridgeService) respondsToSelector:@selector(hippy_webview_shouldRegisterJSEngineWithRequest:webview:context:bridge:)]) {\n        BOOL res = [DKGetService(DKPHippyBridgeService) hippy_webview_shouldRegisterJSEngineWithRequest:request\n                                                                                                webview:self\n                                                                                                context:_context\n                                                                                                 bridge:self.bridge];\n        return !res;\n    }    \n    return YES;\n}\n\n- (void)qmWebViewDidStartLoading:(id<QMWebViewProtocol>)webView {\n    if (_onStartLoad) {\n        _onStartLoad(@{});\n    }\n}\n\n- (void)qmWebViewDidLoadFinished:(id<QMWebViewProtocol>)webView {\n    if (_onFinishLoad) {\n        _onFinishLoad(@{});\n    }\n}\n\n- (void)qmWebView:(id<QMWebViewProtocol>)webView didFailNavigationWithError:(NSError *)error isMainFrameError:(BOOL)isMainFrameError {\n    if (_onFailedLoad) {\n        _onFailedLoad(@{});\n    }\n}\n\n- (void)qmWebScrollViewDidScroll:(UIScrollView *)scrollView {\n    \n    if ([self.scrollViewDelgate respondsToSelector:@selector(qm_scrollViewDidScroll:)] && self.loadFinish) {\n        [self.scrollViewDelgate qm_scrollViewDidScroll:scrollView];\n    }\n    \n    CGPoint offset = scrollView.contentOffset;\n    \n    if (offset.y > 0) {\n        self.loadFinish = YES;\n    }\n    if (_onScroll) {\n        _onScroll(@{\n            @\"x\":@(offset.x),\n            @\"y\":@(offset.y)\n                  });\n    }\n}\n\n#pragma mark - QQJSWebViewProtocol\n\n- (NSURL*)getPageURL {\n    if (_webView.URL) {\n        return _webView.URL;\n    }\n    return nil;\n}\n\n- (void)executeJsScript:(NSString *)script completionHandler:(void (^)(id, NSError *))completionHandler {\n    void (^completionBlock)(id, NSError *) = ^(id ret, NSError *error) {\n        if (completionHandler != NULL) {\n            completionHandler(ret, nil);\n        }\n    };\n    [_webView evaluateJavaScript:script successHandler:^(QMWebView * _Nullable webView, id  _Nullable ret) {\n        completionBlock(ret, nil);\n    } failHandler:^(QMWebView * _Nullable webView, NSError * _Nullable error) {\n        completionBlock(nil, nil);\n    }];\n}\n\n- (void)dispatchJsEvent:(NSString *)eventName data:(NSDictionary *)data fromSource:(NSDictionary *)source {\n    ", "SSZipArchive", "LongLinkSDK", "SystemConfiguration", "TuringShield", "YYModel", "QMapHippy", "TMDylibLazyLoadWrapper", "QMapDarkMode", "QMapRouteSearchKit", "UserNotifications", "QMapBasics", "Photos", "AVFoundation", "CommonCrypto", "ApolloSDK", "KVOController", "QMapUIKit", "DKProtocolsPool", "DragonMapKit", "TMDispatcherManager.h>\n\n@interface TMNGreenEnergyAnimation()<DMMapViewDelegate>\n\n@property (nonatomic) BOOL stopAnimation;\n\n@end\n\n@implementation TMNGreenEnergyAnimation\n\n- (BOOL)startAnimationWithMapView:(DMMapView *)mapView {\n    mapView.frameInterval = 3;\n    self.stopAnimation = NO;\n    self.greenEnergy.alpha = 1;\n    self.greenEnergy.scale = 0;\n    self.greenLight.alpha = 0;\n    \n    __block DMMarkerStatus status = DMMarkerStatusDefault;\n    CGPoint originScreenOffset = self.greenEnergy.screenOffset;\n    status.alpha = 1;\n    \n    DMAnimationConfig config;\n    config.duration = 0.3;\n    config.curveType = DMAnimationCurveTypeEaseOut;\n    config.animated = YES;\n    @weakify(self);\n    [mapView animateMarker:self.greenLight status:status delay:0.3  animationConfig:config completion:^(BOOL finished) {\n        @strongify(self);\n        [self alphaAnimationWithMapView:mapView completion:^(BOOL finished) {\n        }];\n    } needCallback:YES];\n    \n    ", "SDWebImage", "Masonry.h>\n\nCGFloat const kTMIndoorFloorCellHeight = 38.f;\n\n@implementation TMIndoorFloorSideBarCell\n\n- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(nullable NSString *)reuseIdentifier\n{\n    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];\n    if (self) {\n        self.backgroundColor = [UIColor clearColor];\n        self.contentView.backgroundColor = [UIColor clearColor];\n        [self setupSubviews];\n    }\n    return self;\n}\n\n- (void)setupSubviews\n{\n    UIView *selectedBackgroundView = [[UIView alloc] init];\n    selectedBackgroundView.backgroundColor = UIColor.tmd_blue_2;\n    self.selectedBackgroundView = selectedBackgroundView;\n    \n    UILabel *label = [UILabel new];\n    [self.contentView addSubview:label];\n    [label mas_makeConstraints:^(MASConstraintMaker *make) {\n        make.center.equalTo(self.contentView);\n        make.width.height.equalTo(self.contentView);\n    }];\n", "YYModel.h>\n \n#import <QMapMiddlePlatform", "MJExtension", "objc", "QMExtraOrdinaryMap", "GLMapLibHeaders", "GLKit", "Masonry.h>\n#import <hippy"]}, {"name": "DragonMapKit", "path": "hammmer-workspace/DragonMapKit", "type": "ui_module", "source_files": 207, "header_files": 237, "priority": 2, "enabled": true, "schemes": ["DragonMapKit-Example"], "dependencies": ["QMapWidgets", "<PERSON><PERSON><PERSON><PERSON>", "ApolloSDK", "MapBaseOCModel", "EDSunriseSet", "QMapFoundation", "GLMapLib", "KVOController", "QMLibffi", "TXMapView", "QMapUIKit", "QMapProto", "zlib.h>\n#import <QMapFoundation", "libpag", "DragonMapKit", "SDWebImage", "objc", "CoreLocation", "pthread", "VectorLayout", "OpenGLES", "YYModel", "Accelerate", "QMapDarkMode", "CoreText", "GLKit", "CommonCrypto", "QMapBasics", "DKProtocolsPool"]}, {"name": "QMapBaseline", "path": "hammmer-workspace/QMapBaseline", "type": "foundation", "source_files": 135, "header_files": 142, "priority": 1, "enabled": true, "schemes": ["QMapBaseline"], "dependencies": ["SVProgressHUD", "QMapWidgets", "ApolloSDK", "QMapMiddlePlatform", "QMapNaviKit", "MapBaseOCModel", "QMapFoundation", "QMapUIKit", "TXMapView", "SSZipArchive", "TencentLBS", "SDWebImage", "XCTest", "MJExtension", "QMWeChatSDK", "QMapBuildTools", "pthread", "YYModel", "<PERSON><PERSON>", "MagicalRecord", "QMapHippy", "QuickLook", "QMapBasics"]}, {"name": "QMapFoundation", "path": "hammmer-workspace/QMapFoundation", "type": "foundation", "source_files": 132, "header_files": 154, "priority": 1, "enabled": true, "schemes": ["QMapFoundation-Example"], "dependencies": ["TAM_IOS_SDK", "QMapWidgets", "QMEncrypt", "MapBaseOCModel", "GZIP", "QMapFoundation", "SSZipArchive", "SystemConfiguration", "os", "PanguIosShell", "MapReflux", "YYModel", "MMKV", "QimeiSDK", "MapBaseNew", "NSObject+MJCoding.h>\n\n@implementation QMCSSpacePO\n\n@synthesize domain;\n@synthesize userId;\n@synthesize userType;\n@synthesize serverVersion;\n@synthesize localVersion;\n@synthesize sequenceId;\n@synthesize expectSyncCount;\n\nMJCodingImplementation\n\n- (instancetype)init {\n    if (self = [super init]) {\n        self.expectSyncCount = 10; ", "CommonCrypto", "QMapBasics", "mach", "AVFoundation", "MQTTClient", "CoreLocation", "ApolloSDK", "net", "QCloudCore", "KVOController", "QAPM", "dlfcn.h>\n#import <objc", "QMapUIKit", "NetworkExtension", "TencentLBS", "QMapProto", "ifaddrs.h>\n#import <arpa", "sys", "WebKit", "XCTest", "objc", "MJExtension", "QCloudCOSXML", "mach-o", "QMapBuildTools", "OpenGLES", "BeaconAPI_Base", "pthread", "QMJCE_userdata_DataEntry.h>\n#import <NSObject+MJCoding.h>\n#import <QMapProto"]}, {"name": "QMapUIKit", "path": "hammmer-workspace/QMapUIKit", "type": "ui_module", "source_files": 66, "header_files": 72, "priority": 3, "enabled": true, "schemes": ["QMapUIKit"], "dependencies": ["QMapWidgets", "CarPlay", "<PERSON><PERSON>", "MobileCoreServices", "Masonry", "QMapUIKit", "QMapDarkMode", "AudioToolbox", "PhotosUI", "SDWebImage", "CommonCrypto", "QMapBasics", "Photos", "objc"]}, {"name": "QMapRouteSearchKit", "path": "hammmer-workspace/QMapRouteSearchKit", "type": "ui_module", "source_files": 37, "header_files": 43, "priority": 3, "enabled": true, "schemes": ["QMapRouteSearchKit"], "dependencies": ["ApolloSDK", "RouteGuidanceOCMiddleware", "YYModel", "QMapFoundation", "QMapProto", "QMapRouteSearchKit"]}, {"name": "TencentMap", "path": "hammmer-workspace/TencentMap", "type": "core_module", "source_files": 34, "header_files": 32, "priority": 3, "enabled": true, "schemes": ["BuildInfo", "QMIntentsUI", "QMWidgetExtension", "QMNotificationService", "QMIntents", "TencentMap"], "dependencies": ["QMapWidgets", "QMapMiddlePlatform", "PMonitor", "QMapNaviKit", "QMapFoundation", "<PERSON><PERSON><PERSON>", "PanguIosShell", "TDFDebugBox", "TuringShield", "MMKV", "QMapDarkMode", "QMapHippy", "TMDylibLazyLoadWrapper", "SDWebImageWebPCoder", "QimeiSDK", "TMAISound", "QMSpiderManWrapper", "QMapBusiness", "QMapBasics", "A4QMStartTimeMonitor", "ApolloSDK", "KVOController", "QMapUIKit", "CocoaHotReload", "DragonMapKit", "ITLogin", "QMapVectorLayout", "QMapBuildTools", "TMWeAppClient", "QMapBaseline", "BeaconAPI_Base", "RaftKit", "pthread", "DKMobile"]}, {"name": "DKProtocolsPool", "path": "hammmer-workspace/DKProtocolsPool", "type": "tools", "source_files": 12, "header_files": 56, "priority": 3, "enabled": true, "schemes": ["DKProtocolsPool"], "dependencies": ["XCTest"]}], "total_repositories": 11, "total_source_files": 4049, "last_updated": "2025-07-09"}