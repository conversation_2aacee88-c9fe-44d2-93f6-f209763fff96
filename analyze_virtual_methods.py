#!/usr/bin/env python3
"""
Analyze Virtual Method Nodes

This script analyzes virtual_method nodes and their impact on graph quality.
"""

from neo4j import GraphDatabase
from pathlib import Path

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 深入分析virtual_method节点设计')
        print('=' * 60)
        
        # 1. virtual_method节点统计
        result = session.run('''
            MATCH (m:Method)
            WHERE m.type = 'virtual_method'
            RETURN count(m) as virtual_method_count
        ''')
        virtual_method_count = result.single()['virtual_method_count']
        
        result = session.run('MATCH (m:Method) RETURN count(m) as total_methods')
        total_methods = result.single()['total_methods']
        
        print(f'📊 virtual_method节点: {virtual_method_count:,} / {total_methods:,} ({virtual_method_count/total_methods*100:.1f}%)')
        
        # 2. virtual_method的创建场景分析
        result = session.run('''
            MATCH (m:Method {type: 'virtual_method'})
            RETURN m.name as method_name, m.class as class_name, count(*) as frequency
            ORDER BY frequency DESC
            LIMIT 15
        ''')
        
        print('\n📊 最频繁的virtual_method (Top 15):')
        for record in result:
            method_name = record['method_name']
            class_name = record['class_name']
            frequency = record['frequency']
            print(f'  {class_name}::{method_name}: {frequency} 次引用')
        
        # 3. 分析virtual_method的调用者
        result = session.run('''
            MATCH (caller)-[:CALLS]->(m:Method {type: 'virtual_method'})
            RETURN labels(caller) as caller_type, count(*) as call_count
            ORDER BY call_count DESC
        ''')
        
        print('\n📊 virtual_method的调用者类型:')
        for record in result:
            caller_type = ', '.join(record['caller_type'])
            call_count = record['call_count']
            print(f'  {caller_type} -> virtual_method: {call_count:,} 次调用')
        
        # 4. 分析virtual_method的文件归属
        result = session.run('''
            MATCH (m:Method {type: 'virtual_method'})
            RETURN m.file as file_location, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('\n📊 virtual_method的文件归属:')
        for record in result:
            file_location = record['file_location']
            count = record['count']
            print(f'  {file_location}: {count:,} 个virtual_method')
        
        # 5. 分析File -> virtual_method的调用关系
        result = session.run('''
            MATCH (f:File)-[:CALLS]->(m:Method {type: 'virtual_method'})
            RETURN f.name as caller_file, m.name as method_name, m.class as class_name, count(*) as call_count
            ORDER BY call_count DESC
            LIMIT 10
        ''')
        
        print('\n🚨 File -> virtual_method 调用关系分析:')
        file_to_virtual_calls = 0
        for record in result:
            caller_file = Path(record['caller_file']).name if record['caller_file'] else 'unknown'
            method_name = record['method_name']
            class_name = record['class_name']
            call_count = record['call_count']
            file_to_virtual_calls += call_count
            print(f'  {caller_file} -> {class_name}::{method_name}: {call_count} 次')
        
        print(f'\n📈 File -> virtual_method 总调用数: {file_to_virtual_calls:,}')
        
        # 6. 分析virtual_method的语义正确性
        result = session.run('''
            MATCH (m:Method {type: 'virtual_method'})
            WHERE m.name IN ['init', 'dealloc', 'alloc', 'new', 'copy', 'retain', 'release', 'autorelease']
            RETURN m.name as method_name, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('\n📊 常见系统方法的virtual_method:')
        for record in result:
            method_name = record['method_name']
            count = record['count']
            print(f'  {method_name}: {count} 个virtual节点')
        
        # 7. 分析virtual_method对图谱质量的影响
        result = session.run('''
            MATCH (m:Method {type: 'virtual_method'})
            OPTIONAL MATCH (real_m:Method)
            WHERE real_m.name = m.name AND real_m.class = m.class AND real_m.type <> 'virtual_method'
            RETURN 
                CASE WHEN real_m IS NOT NULL THEN 'has_real_counterpart' ELSE 'no_real_counterpart' END as status,
                count(m) as count
        ''')
        
        print('\n📊 virtual_method与真实方法的重叠分析:')
        for record in result:
            status = record['status']
            count = record['count']
            print(f'  {status}: {count:,} 个virtual_method')
        
        # 8. 分析external类的virtual_method
        result = session.run('''
            MATCH (m:Method {type: 'virtual_method'})
            WHERE m.file = 'external'
            RETURN m.class as class_name, count(*) as method_count
            ORDER BY method_count DESC
            LIMIT 10
        ''')
        
        print('\n📊 external类的virtual_method (Top 10):')
        for record in result:
            class_name = record['class_name']
            method_count = record['method_count']
            print(f'  {class_name}: {method_count} 个virtual方法')

    driver.close()

if __name__ == "__main__":
    main()
