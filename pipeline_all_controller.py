#!/usr/bin/env python3
"""
Pipeline-All 主控制器
协调多仓库的并行处理和跨仓库分析
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from dataclasses import asdict

from pipeline.config_all import PipelineAllConfig, RepositoryConfig, ProcessingStrategy
from pipeline.repository_discovery import RepositoryDiscoveryEngine
from pipeline.core import PipelineResult

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PipelineAllController:
    """Pipeline-All 主控制器"""
    
    def __init__(self, config: PipelineAllConfig = None):
        self.config = config or PipelineAllConfig()
        self.discovery_engine = RepositoryDiscoveryEngine(self.config.workspace_dir)
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def run_full_pipeline(self) -> PipelineResult:
        """运行完整的Pipeline-All流程"""
        logger.info("🚀 开始运行Pipeline-All...")
        self.start_time = time.time()
        
        try:
            # 阶段1: 仓库发现
            self._phase_1_discovery()
            
            # 阶段2: 配置验证
            self._phase_2_validation()
            
            # 阶段3: 并行处理
            self._phase_3_parallel_processing()
            
            # 阶段4: 跨仓库分析
            self._phase_4_cross_repo_analysis()
            
            # 阶段5: 数据聚合
            self._phase_5_data_aggregation()
            
            # 阶段6: 验证和统计
            result = self._phase_6_validation_and_stats()
            
            self.end_time = time.time()
            total_time = self.end_time - self.start_time
            
            logger.info(f"🎉 Pipeline-All执行完成，总耗时: {total_time:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"❌ Pipeline-All执行失败: {e}")
            return PipelineResult(
                success=False,
                message=f"Pipeline-All执行失败: {e}",
                data={"error": str(e)}
            )
    
    def _phase_1_discovery(self):
        """阶段1: 仓库发现"""
        logger.info("📍 阶段1: 仓库发现")
        
        # 发现所有仓库
        repositories = self.discovery_engine.discover_all_repositories()
        
        if not repositories:
            raise Exception("未发现任何有效仓库")
        
        # 更新配置
        self.config.repositories = repositories
        self.config.save_repositories()
        
        # 生成发现报告
        discovery_report = self.discovery_engine.generate_discovery_report(repositories)
        
        logger.info(f"✅ 发现 {len(repositories)} 个仓库")
        logger.info(f"📊 总源文件: {discovery_report['summary']['total_source_files']:,}")
        
        # 保存发现报告
        with open("discovery_report.json", "w", encoding="utf-8") as f:
            json.dump(discovery_report, f, indent=2, ensure_ascii=False)
    
    def _phase_2_validation(self):
        """阶段2: 配置验证"""
        logger.info("📍 阶段2: 配置验证")
        
        enabled_repos = self.config.get_enabled_repositories()
        
        if not enabled_repos:
            raise Exception("没有启用的仓库")
        
        # 验证工作空间
        if not Path(self.config.workspace_dir).exists():
            raise Exception(f"工作空间目录不存在: {self.config.workspace_dir}")
        
        # 创建输出目录
        output_dir = Path(self.config.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # 验证资源限制
        total_files = sum(repo.source_files for repo in enabled_repos)
        if total_files > 10000:
            logger.warning(f"⚠️  文件数量较大: {total_files:,}，可能需要更多时间")
        
        logger.info(f"✅ 配置验证通过，将处理 {len(enabled_repos)} 个仓库")
    
    def _phase_3_parallel_processing(self):
        """阶段3: 并行处理"""
        logger.info("📍 阶段3: 并行处理")
        
        processing_groups = self.config.get_processing_groups()
        logger.info(f"📊 处理策略: {self.config.processing_strategy.value}")
        logger.info(f"📊 处理分组: {len(processing_groups)} 组")
        
        all_results = []
        
        if self.config.processing_strategy == ProcessingStrategy.SEQUENTIAL:
            # 顺序处理
            for group in processing_groups:
                group_results = self._process_repository_group(group)
                all_results.extend(group_results)
        
        else:
            # 并行处理
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                futures = {}
                
                for i, group in enumerate(processing_groups):
                    future = executor.submit(self._process_repository_group, group)
                    futures[future] = f"Group-{i+1}"
                
                for future in as_completed(futures):
                    group_name = futures[future]
                    try:
                        group_results = future.result()
                        all_results.extend(group_results)
                        logger.info(f"✅ {group_name} 处理完成")
                    except Exception as e:
                        logger.error(f"❌ {group_name} 处理失败: {e}")
        
        self.results['repository_results'] = all_results
        logger.info(f"✅ 并行处理完成，处理了 {len(all_results)} 个仓库")
    
    def _process_repository_group(self, repositories: List[RepositoryConfig]) -> List[Dict]:
        """处理仓库组"""
        results = []
        
        for repo in repositories:
            try:
                logger.info(f"🔄 处理仓库: {repo.name}")
                
                # 模拟仓库处理（这里应该调用实际的Pipeline步骤）
                result = self._process_single_repository(repo)
                results.append(result)
                
                logger.info(f"✅ {repo.name} 处理完成")
                
            except Exception as e:
                logger.error(f"❌ {repo.name} 处理失败: {e}")
                results.append({
                    "repository": repo.name,
                    "success": False,
                    "error": str(e),
                    "nodes": 0,
                    "edges": 0
                })
        
        return results
    
    def _process_single_repository(self, repo: RepositoryConfig) -> Dict:
        """处理单个仓库"""
        # 这里应该实现实际的仓库处理逻辑
        # 包括: index-store, usrs, cdb, ast等步骤
        
        # 模拟处理结果
        estimated_nodes = repo.source_files * 7  # 每个源文件约7个节点
        estimated_edges = repo.source_files * 5  # 每个源文件约5条边
        
        return {
            "repository": repo.name,
            "success": True,
            "source_files": repo.source_files,
            "nodes": estimated_nodes,
            "edges": estimated_edges,
            "processing_time": 1.0  # 模拟处理时间
        }
    
    def _phase_4_cross_repo_analysis(self):
        """阶段4: 跨仓库分析"""
        logger.info("📍 阶段4: 跨仓库分析")
        
        if not self.config.cross_repo_analysis:
            logger.info("⏭️  跳过跨仓库分析")
            return
        
        enabled_repos = self.config.get_enabled_repositories()
        
        # 分析跨仓库依赖
        cross_repo_relationships = self._analyze_cross_repository_dependencies(enabled_repos)
        
        self.results['cross_repo_relationships'] = cross_repo_relationships
        logger.info(f"✅ 跨仓库分析完成，发现 {len(cross_repo_relationships)} 个跨仓库关系")
    
    def _analyze_cross_repository_dependencies(self, repositories: List[RepositoryConfig]) -> List[Dict]:
        """分析跨仓库依赖关系"""
        relationships = []
        
        for source_repo in repositories:
            for target_repo in repositories:
                if source_repo.name != target_repo.name:
                    # 检查依赖关系
                    if target_repo.name in source_repo.dependencies:
                        relationships.append({
                            "source": source_repo.name,
                            "target": target_repo.name,
                            "type": "explicit_dependency",
                            "strength": "high"
                        })
                    
                    # 基于仓库类型推断隐式依赖
                    implicit_deps = self._infer_implicit_dependencies(source_repo, target_repo)
                    relationships.extend(implicit_deps)
        
        return relationships
    
    def _infer_implicit_dependencies(self, source: RepositoryConfig, target: RepositoryConfig) -> List[Dict]:
        """推断隐式依赖关系"""
        relationships = []
        
        # 业务模块通常依赖基础模块
        if (source.type.value == "business" and 
            target.type.value in ["foundation", "core_module"]):
            relationships.append({
                "source": source.name,
                "target": target.name,
                "type": "implicit_dependency",
                "strength": "medium"
            })
        
        # UI模块通常依赖基础模块
        if (source.type.value == "ui_module" and 
            target.type.value in ["foundation", "core_module"]):
            relationships.append({
                "source": source.name,
                "target": target.name,
                "type": "implicit_dependency",
                "strength": "medium"
            })
        
        return relationships
    
    def _phase_5_data_aggregation(self):
        """阶段5: 数据聚合"""
        logger.info("📍 阶段5: 数据聚合")
        
        # 聚合所有仓库的结果
        total_nodes = 0
        total_edges = 0
        successful_repos = 0
        
        for result in self.results.get('repository_results', []):
            if result.get('success', False):
                total_nodes += result.get('nodes', 0)
                total_edges += result.get('edges', 0)
                successful_repos += 1
        
        # 添加跨仓库关系
        cross_repo_count = len(self.results.get('cross_repo_relationships', []))
        
        self.results['aggregated_stats'] = {
            "total_repositories": len(self.config.get_enabled_repositories()),
            "successful_repositories": successful_repos,
            "total_nodes": total_nodes,
            "total_edges": total_edges,
            "cross_repo_relationships": cross_repo_count
        }
        
        logger.info(f"✅ 数据聚合完成")
        logger.info(f"📊 总节点: {total_nodes:,}")
        logger.info(f"📊 总边: {total_edges:,}")
        logger.info(f"📊 跨仓库关系: {cross_repo_count:,}")
    
    def _phase_6_validation_and_stats(self) -> PipelineResult:
        """阶段6: 验证和统计"""
        logger.info("📍 阶段6: 验证和统计")
        
        stats = self.results.get('aggregated_stats', {})
        
        # 验证结果
        validation_errors = []
        
        if stats.get('total_nodes', 0) < self.config.min_nodes_count:
            validation_errors.append(f"节点数不足: {stats.get('total_nodes', 0)} < {self.config.min_nodes_count}")
        
        if stats.get('total_edges', 0) < self.config.min_edges_count:
            validation_errors.append(f"边数不足: {stats.get('total_edges', 0)} < {self.config.min_edges_count}")
        
        if stats.get('cross_repo_relationships', 0) < self.config.min_cross_repo_calls:
            validation_errors.append(f"跨仓库关系不足: {stats.get('cross_repo_relationships', 0)} < {self.config.min_cross_repo_calls}")
        
        # 生成最终报告
        final_report = {
            "config_summary": self.config.get_summary(),
            "processing_results": self.results,
            "validation_errors": validation_errors,
            "execution_time": self.end_time - self.start_time if self.end_time else None
        }
        
        # 保存最终报告
        with open(Path(self.config.output_dir) / "pipeline_all_report.json", "w", encoding="utf-8") as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        success = len(validation_errors) == 0
        message = "Pipeline-All执行成功" if success else f"验证失败: {'; '.join(validation_errors)}"
        
        return PipelineResult(
            success=success,
            message=message,
            data=final_report
        )

def main():
    """主函数"""
    print("🚀 启动Pipeline-All...")
    
    # 创建配置
    config = PipelineAllConfig()
    
    # 创建控制器
    controller = PipelineAllController(config)
    
    # 运行Pipeline
    result = controller.run_full_pipeline()
    
    # 输出结果
    print(f"\n{'='*60}")
    print("Pipeline-All 执行结果")
    print('='*60)
    print(f"状态: {'成功' if result.success else '失败'}")
    print(f"消息: {result.message}")
    
    if result.data and 'processing_results' in result.data:
        stats = result.data['processing_results'].get('aggregated_stats', {})
        print(f"总仓库数: {stats.get('total_repositories', 0)}")
        print(f"成功仓库数: {stats.get('successful_repositories', 0)}")
        print(f"总节点数: {stats.get('total_nodes', 0):,}")
        print(f"总边数: {stats.get('total_edges', 0):,}")
        print(f"跨仓库关系: {stats.get('cross_repo_relationships', 0):,}")

if __name__ == "__main__":
    main()
