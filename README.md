# GRAG iOS

## 项目简介

本项目旨在打造一个智能的 AI 代码助手，通过分析项目源代码并结合用户的自然语言需求，自动生成符合项目规范的 MCP 脚本。

## 技术路线

项目采用完整的代码知识图谱构建流水线：

源代码收集 → 符号映射提取 → 编译数据库生成 → AST解析 → 知识图谱建模 → 质量统计校验 → GraphRAG问答

## 当前进展

已完成模块：
- ✅ 源代码收集与管理
- ✅ 符号映射关系提取
- ✅ 编译数据库生成
- ✅ AST语法树解析
- ✅ 知识图谱建模
- ✅ 统计验证体系  python check_neo4j_final_state.py python test_neo4j_connection.py  

## 待办事项

代码重构与优化：
1. 重构项目结构，优化文件组织
2. 完善图谱准确性验证
3. 集成代码注释节点
4. 实现 GraphRAG 检索策略
   - 图结构召回
   - 语义检索召回
5. 构建召回结果线性化模块，对接大模型生成 MCP 脚本

相关核心的指令和脚本

ch
```bash
python check_neo4j_final_state.py
🔍 检查Neo4j最终状态...
============================================================
📊 基础统计:
  总节点数: 157,619
  总关系数: 518,692

🏷️ 节点标签 (8个):
  - Class
  - CodeNode
  - Enum
  - File
  - Method
  - Property
  - Protocol
  - Struct

🔗 关系类型 (8个):
  - ACCESSES
  - CALLS
  - DEFINES
  - DEPENDS_ON
  - EXTENDS
  - IMPLEMENTS
  - IMPORTS
  - INHERITS

📈 节点类型分布:
  Property: 73,744
  Method: 57,607
  Type: 14,047
  File: 4,465
  Class: 4,428
  Module: 3,310
  Protocol: 16
  Struct: 1
  Enum: 1

🔗 关系类型分布:
  ACCESSES: 325,934
  DEPENDS_ON: 107,377
  DEFINES: 76,079
  IMPORTS: 8,859
  INHERITS: 195
  CALLS: 159
  EXTENDS: 64
  IMPLEMENTS: 25

🏢 仓库分布:
  external: 72,895
  QMapBusiness: 40,563
  QMapMiddlePlatform: 16,042
  QMapNaviKit: 10,666
  DragonMapKit: 4,276
  QMapHippy: 3,069
  QMapFoundation: 1,976
  QMapBaseline: 1,843
  QMapUIKit: 1,053
  TencentMap: 309
  QMapRouteSearchKit: 224
  DKProtocolsPool: 41

👻 虚拟节点分布:
  Property: 59,600
  Type: 14,047
  Module: 3,310
  Class: 75
  Method: 25
  Protocol: 13
  总虚拟节点: 77,070

🌉 跨仓库关系: 440,889

✅ 验证结果:
  节点标签: ✅ (8/8)
  关系类型: ✅ (8/8)
  节点数量: ✅ (157,619)
  关系数量: ✅ (518,692)
  跨仓库关系: ✅ (440,889)

🎯 总体评估: ✅ 成功
```


```bash
python test_neo4j_connection.py
🚀 Neo4j连接测试工具
==================================================
🔍 测试Neo4j连接...
URI: bolt://localhost:7687
用户名: neo4j
密码: ********

✅ Neo4j连接成功!

📊 数据库信息:
  Neo4j Kernel: 2025.06.0
  Cypher: 5

📈 当前数据:
  节点数量: 157,619
  关系数量: 518,692

🎉 Neo4j连接测试成功!
现在可以运行完整的数据导入:
  python neo4j_integration/main.py
```

还需要搞清楚：
1. virtual node是什么， 数量和比例，是否有必要
2. 每种顶点的设计，每种边的设计， 数量和比例， 是否有必要
3. 顶点和关系的抽取是否完整，按照设计的scheme和源代码，是否所有的节点和边都完整抽取出来了？怎么证明？
4. 为什么跨代码仓库的关系那么多，比例也很大，这是正常的吗？跨代码仓库的关系，是什么关系，统计比例如何

相关的查询语言：
找到跨库的关系
```CYPHER
// Find most coupled repositories
MATCH (source)-[r]->(target)
WHERE source.repository <> target.repository
RETURN source.repository, target.repository, count(r) as coupling
ORDER BY coupling DESC
```

查找哪些类定义的方法比较多
````CYPHER
// Find largest classes by method count
MATCH (c:Class)-[:DEFINES]->(m:Method)
RETURN c.name, c.repository, count(m) as methods
ORDER BY methods DESC LIMIT 20
````

查找哪些文件定义的节点比较多
````CYPHER
// 找到最多被访问的 CodeNode
MATCH ()-[r:ACCESSES]->(c:CodeNode)
RETURN c.name, count(r) as access_count
ORDER BY access_count DESC LIMIT 20
````

查询跨库关系
````CYPHER
// Repository dependency matrix
MATCH (source)-[r]->(target)
WHERE source.repository <> target.repository
AND source.repository <> 'external'
AND target.repository <> 'external'
RETURN source.repository as from_repo, 
       target.repository as to_repo, 
       count(r) as dependencies
ORDER BY dependencies DESC
````

目前的节点类型分布：
节点类型分布：
属性：73,744（46.8%）——包含59,600个虚拟属性
方法：57,607（36.5%）——所有方法定义
类型：14,047（8.9%）——类型依赖项（均为虚拟）
文件：4,465（2.8%）——源文件
类：4,428（2.8%）——类定义
模块：3,310（2.1%）——导入模块（均为虚拟）
协议：16（0.01%）——协议定义
结构体：1——结构体定义
枚举：1——枚举定义


关系类型分布：

访问关系（ACCESSES）：325,934 条（62.8%） - 属性访问关系
依赖关系（DEPENDS_ON）：107,377 条（20.7%） - 类型依赖关系
定义关系（DEFINES）：76,079 条（14.7%） - 定义关系
导入关系（IMPORTS）：8,859 条（1.7%） - 导入关系
继承关系（INHERITS）：195 条（0.04%） - 继承关系
调用关系（CALLS）：159 条（0.03%） - 方法调用关系
扩展关系（EXTENDS）：64 条（0.01%） - 扩展关系
实现关系（IMPLEMENTS）：25 条（0.005%） - 协议实现关系

总节点数157,619个（远超80,549的目标）
8种不同节点标签：类、代码节点、枚举、文件、方法、属性、协议、结构体
9种节点类型：包含虚拟节点（属性、类型、模块等）


总关系数518,692条（完全符合目标）
8种不同关系类型：访问、调用、定义、依赖、扩展、实现、导入、继承
所有关系类型均保留，且分布比例正确

跨仓库关系数440,889条（远超15,754的目标）
完整覆盖11个仓库，并附带规范元数据
外部依赖追踪（72,895个外部节点）

通过关系的分析，某些节点和边的数量和比例似乎太少了， 不应该设置对应的类型



