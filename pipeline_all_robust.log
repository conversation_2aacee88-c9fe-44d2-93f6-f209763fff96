2025-07-10 15:23:45,513 - __main__ - INFO - 🚀 开始执行Pipeline-All (健壮版本)
2025-07-10 15:23:45,513 - __main__ - INFO - ============================================================
2025-07-10 15:23:45,513 - __main__ - INFO - 📍 阶段1: 环境准备
2025-07-10 15:23:45,513 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-10 15:23:45,545 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-10 15:23:45,552 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-10 15:23:45,604 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-10 15:23:45,677 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-10 15:23:45,699 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-10 15:23:45,786 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-10 15:23:45,921 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-10 15:23:46,168 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-10 15:23:46,702 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-10 15:23:46,925 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-10 15:23:46,984 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-10 15:23:46,984 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-10 15:23:46,984 - __main__ - INFO - ✅ 发现 11 个仓库
2025-07-10 15:23:46,984 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-10 15:23:47,786 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-10 15:23:47,786 - __main__ - INFO - ✅ 环境清理完成
2025-07-10 15:23:47,786 - __main__ - INFO - 📍 阶段2: 索引生成
2025-07-10 15:23:47,786 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-10 15:23:47,786 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-10 15:23:47,786 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-10 15:23:47,788 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-10 15:23:47,788 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-10 15:23:47,789 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成索引...
2025-07-10 15:23:47,791 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-10 15:23:47,792 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-10 15:23:47,797 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成索引...
2025-07-10 15:23:47,799 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-10 15:24:00,617 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapFoundation and configuration Debug
(1 failure)

2025-07-10 15:24:00,617 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-10 15:24:03,748 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DragonMapKit and configuration Debug
(1 failure)

2025-07-10 15:24:03,749 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-10 15:24:05,005 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-10 15:24:05,005 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-10 15:24:05,304 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-10 15:24:05,304 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-10 15:24:05,460 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-10 15:24:05,460 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-10 15:24:05,549 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-10 15:24:05,550 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-10 15:24:05,556 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-10 15:24:05,556 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-10 15:24:07,935 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-10 15:24:07,935 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-10 15:24:17,567 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-10 15:24:21,368 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-10 15:24:24,788 - pipeline.steps.index_store_step_all - ERROR - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug
(1 failure)

2025-07-10 15:24:26,333 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-10 15:24:26,363 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(1 failure)

2025-07-10 15:24:26,487 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-10 15:24:26,625 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-10 15:24:42,088 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 索引生成成功
2025-07-10 15:24:42,089 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-10 15:24:42,143 - pipeline.steps.index_store_step_all - INFO - 索引存储输出验证成功，找到 4747 个索引记录文件
2025-07-10 15:24:42,143 - __main__ - INFO - ✅ 索引生成成功
2025-07-10 15:24:42,143 - __main__ - INFO - 📍 阶段3: 符号提取
2025-07-10 15:24:42,143 - pipeline.steps.usrs_step_all - INFO - 开始提取多仓库USR到文件路径映射
2025-07-10 15:24:42,143 - pipeline.steps.usrs_step_all - INFO - 检查索引存储目录: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-10 15:24:42,143 - pipeline.steps.usrs_step_all - INFO - 开始提取USR到文件路径映射
2025-07-10 15:24:42,144 - pipeline.steps.usrs_step_all - INFO - 使用索引存储路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-10 15:24:42,144 - pipeline.steps.usrs_step_all - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store ast_out_index_all/DerivedData/Index.noindex/DataStore --out index_symbols_all.jsonl
2025-07-10 15:24:45,211 - pipeline.steps.usrs_step_all - INFO - USR提取命令执行成功
2025-07-10 15:24:45,227 - pipeline.steps.usrs_step_all - INFO - 成功提取 148987 个USR映射
2025-07-10 15:24:45,227 - pipeline.steps.usrs_step_all - INFO - 分析提取的USR数据...
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO - USR分析完成:
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -   总USR数: 148,967
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -   项目文件: 0
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -   Pods文件: 35,224
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -   系统文件: 0
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -   仓库分布:
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -     unknown: 143,231
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -     DragonMapKit: 3,030
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -     QMapFoundation: 1,863
2025-07-10 15:24:45,577 - pipeline.steps.usrs_step_all - INFO -     DKProtocolsPool: 843
2025-07-10 15:24:45,577 - __main__ - INFO - ✅ 符号提取成功
2025-07-10 15:24:45,577 - __main__ - INFO - 📍 阶段4: 编译数据库生成
2025-07-10 15:24:45,577 - pipeline.steps.cdb_step_all - INFO - 开始生成多仓库编译数据库
2025-07-10 15:24:45,577 - pipeline.steps.cdb_step_all - INFO - 发现 16 个schemes需要处理
2025-07-10 15:24:45,577 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成编译数据库...
2025-07-10 15:24:45,578 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-10 15:24:45,580 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成编译数据库...
2025-07-10 15:24:45,582 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成编译数据库...
2025-07-10 15:24:45,584 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成编译数据库...
2025-07-10 15:24:45,585 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成编译数据库...
2025-07-10 15:24:45,585 - pipeline.steps.cdb_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成编译数据库...
2025-07-10 15:24:45,585 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成编译数据库...
2025-07-10 15:24:56,153 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败，但继续解析日志
2025-07-10 15:24:56,154 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapFoundation 的构建日志，长度: 14539 字符
2025-07-10 15:24:56,154 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 5 个clang命令
2025-07-10 15:24:56,157 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:56,157 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapFoundation 的scheme 'QMapFoundation' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:56,157 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成编译数据库...
2025-07-10 15:24:57,688 - pipeline.steps.cdb_step_all - WARNING - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败，但继续解析日志
2025-07-10 15:24:57,688 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DragonMapKit 的构建日志，长度: 20632 字符
2025-07-10 15:24:57,688 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:24:57,689 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:57,689 - pipeline.steps.cdb_step_all - INFO - 仓库 DragonMapKit 的scheme 'DragonMapKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:57,689 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成编译数据库...
2025-07-10 15:24:58,220 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败，但继续解析日志
2025-07-10 15:24:58,221 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapNaviKit 的构建日志，长度: 29954 字符
2025-07-10 15:24:58,221 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:24:58,221 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:58,221 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:58,222 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成编译数据库...
2025-07-10 15:24:58,314 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败，但继续解析日志
2025-07-10 15:24:58,314 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapMiddlePlatform 的构建日志，长度: 27963 字符
2025-07-10 15:24:58,314 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:24:58,315 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:58,315 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:58,315 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成编译数据库...
2025-07-10 15:24:58,434 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败，但继续解析日志
2025-07-10 15:24:58,434 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapHippy 的构建日志，长度: 29639 字符
2025-07-10 15:24:58,434 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:24:58,435 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:58,435 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapHippy 的scheme 'QMapHippy' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:58,435 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成编译数据库...
2025-07-10 15:24:58,566 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败，但继续解析日志
2025-07-10 15:24:58,566 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBaseline 的构建日志，长度: 34808 字符
2025-07-10 15:24:58,566 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:24:58,566 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:58,566 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBaseline 的scheme 'QMapBaseline' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:58,566 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成编译数据库...
2025-07-10 15:24:58,867 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败，但继续解析日志
2025-07-10 15:24:58,868 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBusiness 的构建日志，长度: 41602 字符
2025-07-10 15:24:58,868 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:24:58,868 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:24:58,868 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBusiness 的scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:24:58,868 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成编译数据库...
2025-07-10 15:25:03,213 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapUIKit 的构建日志，长度: 6782888 字符
2025-07-10 15:25:03,220 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 242 个CompileC行, 1273 个clang命令
2025-07-10 15:25:07,001 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 257/509 个文件通过过滤
2025-07-10 15:25:07,001 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 编译数据库生成成功，包含 257 个编译命令
2025-07-10 15:25:07,001 - pipeline.steps.cdb_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成编译数据库...
2025-07-10 15:25:10,072 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败，但继续解析日志
2025-07-10 15:25:10,072 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 1963 字符
2025-07-10 15:25:10,072 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 1 个clang命令
2025-07-10 15:25:10,073 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:10,073 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'BuildInfo' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:16,223 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'TencentMap' 构建失败，但继续解析日志
2025-07-10 15:25:16,224 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89211 字符
2025-07-10 15:25:16,224 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:25:16,224 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:16,224 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'TencentMap' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:17,244 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntents' 构建失败，但继续解析日志
2025-07-10 15:25:17,244 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89210 字符
2025-07-10 15:25:17,244 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:25:17,244 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:17,244 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntents' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:17,397 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败，但继续解析日志
2025-07-10 15:25:17,397 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89212 字符
2025-07-10 15:25:17,397 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:25:17,397 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:17,397 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntentsUI' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:17,479 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败，但继续解析日志
2025-07-10 15:25:17,479 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89222 字符
2025-07-10 15:25:17,479 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:25:17,480 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:17,480 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMNotificationService' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:18,948 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败，但继续解析日志
2025-07-10 15:25:18,948 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89218 字符
2025-07-10 15:25:18,948 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 15:25:18,948 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:18,948 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMWidgetExtension' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:22,208 - pipeline.steps.cdb_step_all - WARNING - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败，但继续解析日志
2025-07-10 15:25:22,208 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DKProtocolsPool 的构建日志，长度: 2277 字符
2025-07-10 15:25:22,208 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 2 个clang命令
2025-07-10 15:25:22,209 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 15:25:22,209 - pipeline.steps.cdb_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 15:25:25,827 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败，但继续解析日志
2025-07-10 15:25:25,828 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapRouteSearchKit 的构建日志，长度: 13343765 字符
2025-07-10 15:25:25,840 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 476 个CompileC行, 2474 个clang命令
2025-07-10 15:25:28,410 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 522/1025 个文件通过过滤
2025-07-10 15:25:28,410 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 编译数据库生成成功，包含 522 个编译命令
2025-07-10 15:25:28,411 - pipeline.steps.cdb_step_all - INFO - 合并编译命令完成，共 522 个唯一文件
2025-07-10 15:25:28,415 - pipeline.steps.cdb_step_all - INFO - 编译数据库已保存到: ast_out_index_all/compile_commands/compile_commands.json
2025-07-10 15:25:28,416 - __main__ - INFO - ✅ 编译数据库生成成功
2025-07-10 15:25:28,416 - __main__ - INFO - 📍 阶段5: AST分析 (核心步骤)
2025-07-10 15:25:28,416 - pipeline.steps.ast_step_all - INFO - 开始多仓库AST抽取
2025-07-10 15:25:28,417 - pipeline.steps.ast_step_all - INFO - 从编译数据库加载了 522 个编译命令
2025-07-10 15:25:28,417 - pipeline.steps.ast_step_all - INFO - 加载了 522 个编译命令
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11670: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11671: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11672: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11673: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11674: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11675: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11676: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11677: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11678: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,428 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11679: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 15:25:28,554 - pipeline.steps.ast_step_all - WARNING - 跳过了 20/148987 个无效的符号行
2025-07-10 15:25:28,554 - pipeline.steps.ast_step_all - INFO - 成功加载 148967 个符号映射
2025-07-10 15:25:28,554 - pipeline.steps.ast_step_all - INFO - 处理 522 个编译命令
2025-07-10 15:25:28,554 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1/522: UIColor.m
2025-07-10 15:25:28,554 - pipeline.steps.ast_step_all - INFO - 处理编译文件 2/522: UIColor+Expanded.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 3/522: UIBezierPath.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 4/522: lottie-ios-dummy.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 5/522: NSValue+Compat.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 6/522: LOTValueCallback.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 7/522: LOTValueInterpolator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 8/522: LOTTrimPathNode.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 9/522: LOTTransformInterpolator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 10/522: LOTStrokeRenderer.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 11/522: LOTSizeInterpolator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 12/522: LOTShapeTrimPath.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 13/522: LOTShapeTransform.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 14/522: LOTShapeStroke.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 15/522: LOTShapeStar.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 16/522: LOTShapeRepeater.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 17/522: LOTShapeRectangle.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 18/522: LOTShapePath.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 19/522: LOTShapeGroup.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 20/522: LOTShapeGradientFill.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 21/522: LOTShapeFill.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 22/522: LOTShapeCircle.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 23/522: LOTRoundedRectAnimator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 24/522: LOTRepeaterRenderer.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 25/522: LOTRenderGroup.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 26/522: LOTRenderNode.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 27/522: LOTRadialGradientLayer.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 28/522: LOTPolystarAnimator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 29/522: LOTPolygonAnimator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 30/522: LOTPointInterpolator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 31/522: LOTPathInterpolator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 32/522: LOTPathAnimator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 33/522: LOTNumberInterpolator.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 34/522: LOTMaskContainer.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 35/522: LOTMask.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 36/522: LOTLayerGroup.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 37/522: LOTLayerContainer.m
2025-07-10 15:25:28,555 - pipeline.steps.ast_step_all - INFO - 处理编译文件 38/522: LOTLayer.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 39/522: LOTKeypath.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 40/522: LOTKeyframe.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 41/522: LOTInterpolatorCallback.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 42/522: LOTGradientFillRender.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 43/522: LOTFillRenderer.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 44/522: LOTCompositionContainer.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 45/522: LOTComposition.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 46/522: LOTColorInterpolator.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 47/522: LOTCircleAnimator.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 48/522: LOTCacheProvider.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 49/522: LOTBezierPath.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 50/522: LOTBlockCallback.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 51/522: LOTBezierData.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 52/522: LOTAssetGroup.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 53/522: LOTAsset.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 54/522: LOTArrayInterpolator.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 55/522: LOTAnimatorNode.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 56/522: LOTAnimationView.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 57/522: LOTAnimationTransitionController.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 58/522: LOTAnimationCache.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 59/522: LOTAnimatedSwitch.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 60/522: LOTAnimatedControl.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 61/522: CGGeometry+LOTAdditions.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 62/522: CALayer+Compat.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 63/522: libMMKV.mm
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 64/522: UIView+WebCacheOperation.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 65/522: UIView+WebCache.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 66/522: UIImageView+WebCache.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 67/522: UIImageView+HighlightedWebCache.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 68/522: UIImage+Transform.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 69/522: UIImage+MultiFormat.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 70/522: UIImage+Metadata.m
2025-07-10 15:25:28,556 - pipeline.steps.ast_step_all - INFO - 处理编译文件 71/522: UIImage+MemoryCacheCost.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 72/522: UIImage+GIF.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 73/522: UIImage+ForceDecode.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 74/522: UIImage+ExtendedCacheData.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 75/522: UIColor+SDHexString.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 76/522: UIButton+WebCache.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 77/522: SDWebImageTransition.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 78/522: SDWebImagePrefetcher.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 79/522: SDWebImageOptionsProcessor.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 80/522: SDWebImageOperation.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 81/522: SDWebImageManager.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 82/522: SDWebImageIndicator.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 83/522: SDWebImageError.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 84/522: SDWebImageDownloaderResponseModifier.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 85/522: SDWebImageDownloaderRequestModifier.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 86/522: SDWebImageDownloaderOperation.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 87/522: SDWebImageDownloaderDecryptor.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 88/522: SDWebImageDownloaderConfig.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 89/522: SDWebImageDownloader.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 90/522: SDWebImageDefine.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 91/522: SDWebImageCompat.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 92/522: SDWebImageCacheSerializer.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 93/522: SDWebImageCacheKeyFilter.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 94/522: SDWebImage-iOS12.0-dummy.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 95/522: SDWeakProxy.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 96/522: SDMemoryCache.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 97/522: SDInternalMacros.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 98/522: SDImageTransformer.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 99/522: SDImageLoadersManager.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 100/522: SDImageLoader.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 101/522: SDImageIOCoder.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 102/522: SDImageIOAnimatedCoder.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 103/522: SDImageHEICCoder.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 104/522: SDImageGraphics.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 105/522: SDImageGIFCoder.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 106/522: SDImageFramePool.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 107/522: SDImageFrame.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 108/522: SDImageCodersManager.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 109/522: SDImageCoderHelper.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 110/522: SDImageCoder.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 111/522: SDImageCachesManagerOperation.m
2025-07-10 15:25:28,557 - pipeline.steps.ast_step_all - INFO - 处理编译文件 112/522: SDImageCachesManager.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 113/522: SDImageCacheDefine.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 114/522: SDImageCacheConfig.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 115/522: SDImageCache.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 116/522: SDImageAssetManager.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 117/522: SDImageAWebPCoder.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 118/522: SDImageAPNGCoder.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 119/522: SDGraphicsImageRenderer.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 120/522: SDFileAttributeHelper.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 121/522: SDDisplayLink.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 122/522: SDDiskCache.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 123/522: SDDeviceHelper.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 124/522: SDCallbackQueue.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 125/522: SDAsyncBlockOperation.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 126/522: SDAssociatedObject.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 127/522: SDAnimatedImageView.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 128/522: SDAnimatedImageView+WebCache.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 129/522: SDAnimatedImageRep.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 130/522: SDAnimatedImagePlayer.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 131/522: SDAnimatedImage.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 132/522: NSImage+Compatibility.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 133/522: NSData+ImageContentType.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 134/522: NSButton+WebCache.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 135/522: NSBezierPath+SDRoundedCorners.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 136/522: UIView+QMCoordinate.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 137/522: UIView+QMAutoLayout.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 138/522: UIColor+QMExtension.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 139/522: QMapWidgets-iOS12.0-dummy.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 140/522: QMWindowTools.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 141/522: QMTableViewSection.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 142/522: QMTableViewRow.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 143/522: QMTableViewFormatter.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 144/522: QMEdgeLabel.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 145/522: QMButton.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 146/522: QMAlertWindowViewController.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 147/522: QMAlertController.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 148/522: UIWindow+QDMEnvironment.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 149/522: UIViewController+QDMEnvironment.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 150/522: UIView+QDMEnvironment.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 151/522: UIScreen+QDMEnvironment.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 152/522: UIImageView+QDM.m
2025-07-10 15:25:28,558 - pipeline.steps.ast_step_all - INFO - 处理编译文件 153/522: UIImage+QDM.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 154/522: UIColor+QDM.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 155/522: QMapDarkModeManager.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 156/522: QMapDarkMode-dummy.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 157/522: QDMTraitCollection.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 158/522: QDMRegistry.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 159/522: QDMLog.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 160/522: QMapDarkModeManager.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 161/522: QMapDarkMode-dummy.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 162/522: QDMGlobalTestHooks.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 163/522: QDMEnvConfiguration.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 164/522: QDMConvertor.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 165/522: NSObject+QDMSwizzle.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 166/522: QMapBasics-iOS12.0-dummy.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 167/522: QMTimer.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 168/522: QMSwizzle.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 169/522: QMReachability.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 170/522: QMPath.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 171/522: QMNetworkStatusManager.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 172/522: QMDigest.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 173/522: QMDateFormatterPool.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 174/522: QMCallManager.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 175/522: NSString+QMVersionSplit.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 176/522: NSString+QMVersionCompare.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 177/522: NSString+QMURL.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 178/522: NSString+QMCommon.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 179/522: NSObject+QMSafeTypeConversion.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 180/522: NSMutableString+QMSafeStringOperation.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 181/522: NSMutableSet+QMSafeCollectionSet.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 182/522: NSMutableDictionary+QMSafeCollectionDictionary.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 183/522: NSMutableArray+QMSafeCollectonArray.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 184/522: NSDictionary+QMHasObjectType.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 185/522: NSDictionary+QMCommon.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 186/522: NSDate+Common.m
2025-07-10 15:25:28,559 - pipeline.steps.ast_step_all - INFO - 处理编译文件 187/522: NSData+QMImageContentType.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 188/522: NSData+QMGZip.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 189/522: NSData+QMBase64WithImagePrefix.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 190/522: NSArray+QMSafeCollectonArray.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 191/522: NSArray+QMChaining.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 192/522: Protobuf-dummy.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 193/522: GPBWrappers.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 194/522: GPBWireFormat.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 195/522: GPBWellKnownTypes.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 196/522: GPBUtilities.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 197/522: GPBUnknownFieldSet.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 198/522: GPBUnknownField.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 199/522: GPBType.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 200/522: GPBTimestamp.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 201/522: GPBStruct.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 202/522: GPBSourceContext.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 203/522: GPBRootObject.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 204/522: GPBMessage.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 205/522: GPBFieldMask.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 206/522: GPBExtensionRegistry.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 207/522: GPBExtensionInternals.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 208/522: GPBEmpty.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 209/522: GPBDuration.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 210/522: GPBDictionary.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 211/522: GPBDescriptor.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 212/522: GPBCodedOutputStream.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 213/522: GPBCodedInputStream.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 214/522: GPBArray.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 215/522: GPBApi.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 216/522: GPBAny.pbobjc.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 217/522: ViewController+MASAdditions.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 218/522: View+MASAdditions.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 219/522: NSLayoutConstraint+MASDebugAdditions.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 220/522: NSArray+MASAdditions.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 221/522: Masonry-iOS12.0-dummy.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 222/522: MASViewConstraint.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 223/522: MASViewAttribute.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 224/522: MASLayoutConstraint.m
2025-07-10 15:25:28,560 - pipeline.steps.ast_step_all - INFO - 处理编译文件 225/522: MASConstraintMaker.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 226/522: MASConstraint.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 227/522: MASCompositeConstraint.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 228/522: QMapWidgets-iOS12.0-dummy.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 229/522: QMapBasics-iOS12.0-dummy.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 230/522: MMKVCore-dummy.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 231/522: MMKV-dummy.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 232/522: PBAPLProtocol.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 233/522: ApolloSDK.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 234/522: ApolloSDK-dummy.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 235/522: APLSafeObject.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 236/522: APLSDKHelper.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 237/522: APLSDKConfig.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 238/522: APLRespDTO.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 239/522: APLRegistry.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 240/522: APLPrivate.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 241/522: APLNodeDataRefresher.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 242/522: APLNode.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 243/522: APLNetworkSessionManager.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 244/522: APLNetworkSessionDataTask.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 245/522: APLNetworkSerialization.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 246/522: APLNetworkRequest.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 247/522: APLNetworkEncryptor.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 248/522: APLNetworkAPI.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 249/522: APLModuleObject.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 250/522: APLLog.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 251/522: APLInterface.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 252/522: APLConfigObject.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 253/522: APLCacheConfig.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 254/522: APLCache.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 255/522: APLBusinessObject.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 256/522: APLBasePO.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 257/522: APLAppObject.m
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 258/522: QMUniPacket.mm
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 259/522: QMRequest.mm
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 260/522: QCloudEncryt.mm
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 261/522: QCloudAuthentationV4Creator.mm
2025-07-10 15:25:28,561 - pipeline.steps.ast_step_all - INFO - 处理编译文件 262/522: NSString+QCloudSHA.mm
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 263/522: libwebp-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 264/522: YYModel-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 265/522: YYClassInfo.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 266/522: NSObject+YYModel.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 267/522: TAM_IOS_SDK-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 268/522: TAMDRAsyncOperation.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 269/522: TAMDRAsyncBlockOperation.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 270/522: TAMCopyableObject.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 271/522: TAMAegisUtil.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 272/522: TAMAegisUploadData.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 273/522: TAMAegisResourceSpeedData.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 274/522: TAMAegisLogger.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 275/522: TAMAegisLogUploadProxy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 276/522: TAMAegisInterfaceConfig.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 277/522: TAMAegis.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 278/522: TAMAccumulateTimer.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 279/522: NSTimer+BTWeak.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 280/522: SocketRocket-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 281/522: SRWebSocket.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 282/522: SSZipArchive.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 283/522: SSZipArchive-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 284/522: UIImage+WebP.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 285/522: SDWebImageWebPCoderDefine.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 286/522: SDWebImageWebPCoder-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 287/522: SDImageWebPCoder.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 288/522: QMapBuildTools-iOS12.0-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 289/522: QMBuildManager.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 290/522: UniAttribute.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 291/522: QMWUP-iOS12.0-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 292/522: JceStream.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 293/522: JceOutputStream.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 294/522: JceObjectV2.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 295/522: JceObject.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 296/522: JceInputStream.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 297/522: JceEnumHelper.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 298/522: QMapBuildTools-iOS12.0-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 299/522: QMEncrypt.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 300/522: QMEncrypt-dummy.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 301/522: QMEWDHash.m
2025-07-10 15:25:28,562 - pipeline.steps.ast_step_all - INFO - 处理编译文件 302/522: UIImage+QCloudBundle.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 303/522: UIDevice+QCloudFCUUID.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 304/522: QualityDataUploader.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 305/522: QCloudXMLWriter.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 306/522: QCloudXMLDictionary.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 307/522: QCloudWeakProxy.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 308/522: QCloudUploadPartRequestRetryHandler.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 309/522: QCloudUniversalPathFactory.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 310/522: QCloudUniversalPath.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 311/522: QCloudUniversalFixedPath.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 312/522: QCloudUniversalAdjustablePath.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 313/522: QCloudURLTools.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 314/522: QCloudURLSessionTaskData.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 315/522: QCloudURLHelper.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 316/522: QCloudUICKeyChainStore.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 317/522: QCloudThreadSafeMutableDictionary.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 318/522: QCloudSupervisorySession.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 319/522: QCloudSupervisoryRecord.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 320/522: QCloudSupervisory.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 321/522: QCloudSimplePing.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 322/522: QCloudSignatureFields.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 323/522: QCloudSignature.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 324/522: QCloudServiceConfiguration.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 325/522: QCloudServiceConfiguration+Quality.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 326/522: QCloudService.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 327/522: QCloudSandboxPath.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 328/522: QCloudSHAPart.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 329/522: QCloudSDKModuleManager.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 330/522: QCloudResponseSerializer.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 331/522: QCloudRequestSerializer.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 332/522: QCloudRequestOperation.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 333/522: QCloudRequestData.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 334/522: QCloudReachability.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 335/522: QCloudProgrameDefines.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 336/522: QCloudPingTester.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 337/522: QCloudOperationQueue.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 338/522: QCloudNetResponse.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 339/522: QCloudNetProfile.m
2025-07-10 15:25:28,563 - pipeline.steps.ast_step_all - INFO - 处理编译文件 340/522: QCloudNetEnv.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 341/522: QCloudMultiDelegateProxy.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 342/522: QCloudModel.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 343/522: QCloudMediaPath.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 344/522: QCloudMainBundle.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 345/522: QCloudLoggerOutput.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 346/522: QCloudLogger.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 347/522: QCloudLogModel.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 348/522: QCloudIntelligenceTimeOutAdapter.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 349/522: QCloudHttpMetrics.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 350/522: QCloudHttpDNS.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 351/522: QCloudHosts.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 352/522: QCloudHTTPTaskDelayManager.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 353/522: QCloudHTTPSessionManager.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 354/522: QCloudHTTPRetryHanlder.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 355/522: QCloudHTTPRequestOperation.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 356/522: QCloudHTTPRequest.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 357/522: QCloudHTTPMultiDataStream.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 358/522: QCloudHTTPBodyPart.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 359/522: QCloudGCDTimer.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 360/522: QCloudFileZipper.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 361/522: QCloudFileUtils.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 362/522: QCloudFileOffsetStream.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 363/522: QCloudFileOffsetBody.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 364/522: QCloudFileLogger.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 365/522: QCloudFakeRequestOperation.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 366/522: QCloudFCUUID.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 367/522: QCloudError.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 368/522: QCloudEnv.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 369/522: QCloudEndPoint.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 370/522: QCloudDomain.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 371/522: QCloudCredential.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 372/522: QCloudCredentailFenceQueue.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 373/522: QCloudCoreVersion.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 374/522: QCloudCore-dummy.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 375/522: QCloudConfiguration.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 376/522: QCloudClientContext.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 377/522: QCloudClassInfo.m
2025-07-10 15:25:28,564 - pipeline.steps.ast_step_all - INFO - 处理编译文件 378/522: QCloudBundlePath.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 379/522: QCloudBizHTTPRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 380/522: QCloudAuthentationV5Creator.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 381/522: QCloudAuthentationCreator.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 382/522: QCloudAbstractRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 383/522: NSObject+QCloudModelTool.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 384/522: NSObject+QCloudModel.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 385/522: NSObject+HTTPHeadersContainer.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 386/522: NSMutableData+QCloud_CRC.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 387/522: NSHTTPCookie+QCloudNetworking.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 388/522: NSError+QCloudNetworking.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 389/522: NSDate+QCloudInternetDateTime.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 390/522: NSDate+QCloudComapre.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 391/522: NSDate+QCLOUD.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 392/522: QCloudUploadPartResult.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 393/522: QCloudUploadPartRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 394/522: QCloudUploadPartRequest+Custom.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 395/522: QCloudUploadPartCopyRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 396/522: QCloudUploadObjectResult.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 397/522: QCloudRequestData+COSXMLVersion.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 398/522: QCloudPutObjectRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 399/522: QCloudPutObjectRequest+CustomBuild.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 400/522: QCloudPutObjectRequest+Custom.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 401/522: QCloudPutObjectCopyRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 402/522: QCloudMultipartUploadPart.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 403/522: QCloudMultipartUploadOwner.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 404/522: QCloudMultipartUploadInitiator.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 405/522: QCloudMultipartInfo.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 406/522: QCloudLogManager.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 407/522: QCloudListPartsResult.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 408/522: QCloudListMultipartRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 409/522: QCloudInitiateMultipartUploadResult.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 410/522: QCloudInitiateMultipartUploadRequest.m
2025-07-10 15:25:28,565 - pipeline.steps.ast_step_all - INFO - 处理编译文件 411/522: QCloudHeadObjectRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 412/522: QCloudGetObjectRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 413/522: QCloudGetObjectRequest+Custom.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 414/522: QCloudCopyObjectResult.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 415/522: QCloudCompleteMultipartUploadRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 416/522: QCloudCompleteMultipartUploadInfo.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 417/522: QCloudCOSXMLVersion.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 418/522: QCloudCOSXMLUploadObjectRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 419/522: QCloudCOSXMLServiceUtilities.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 420/522: QCloudCOSXMLService.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 421/522: QCloudCOSXMLService+Transfer.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 422/522: QCloudCOSXMLService+Quality.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 423/522: QCloudCOSXMLService+Configuration.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 424/522: QCloudCOSXMLEndPoint.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 425/522: QCloudCOSXMLDownloadObjectRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 426/522: QCloudCOSXMLCopyObjectRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 427/522: QCloudCOSXML-dummy.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 428/522: QCloudCOSTransferMangerService.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 429/522: QCloudCOSStorageClassEnum.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 430/522: QCloudAppendObjectRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 431/522: QCloudAbstractRequest+Quality.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 432/522: QCloudAbortMultipfartUploadRequest.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 433/522: NSString+RegularExpressionCategory.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 434/522: ReconnectTimer.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 435/522: MQTTWebsocketTransport.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 436/522: MQTTTransport.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 437/522: MQTTStrict.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 438/522: MQTTSessionSynchron.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 439/522: MQTTSessionManager.m
2025-07-10 15:25:28,566 - pipeline.steps.ast_step_all - INFO - 处理编译文件 440/522: MQTTSessionLegacy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 441/522: MQTTSession.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 442/522: MQTTSSLSecurityPolicyTransport.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 443/522: MQTTSSLSecurityPolicyEncoder.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 444/522: MQTTSSLSecurityPolicyDecoder.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 445/522: MQTTSSLSecurityPolicy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 446/522: MQTTProperties.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 447/522: MQTTMessage.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 448/522: MQTTLog.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 449/522: MQTTInMemoryPersistence.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 450/522: MQTTDecoder.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 451/522: MQTTCoreDataPersistence.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 452/522: MQTTClient-dummy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 453/522: MQTTCFSocketTransport.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 454/522: MQTTCFSocketEncoder.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 455/522: MQTTCFSocketDecoder.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 456/522: GCDTimer.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 457/522: ForegroundReconnection.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 458/522: NSString+MJExtension.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 459/522: NSObject+MJProperty.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 460/522: NSObject+MJKeyValue.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 461/522: NSObject+MJCoding.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 462/522: NSObject+MJClass.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 463/522: MJPropertyType.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 464/522: MJPropertyKey.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 465/522: MJProperty.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 466/522: MJFoundation.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 467/522: MJExtensionConst.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 468/522: MJExtension-dummy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 469/522: NSObject+FBKVOController.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 470/522: KVOController-dummy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 471/522: FBKVOController.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 472/522: NSData+GZIP.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 473/522: GZIP-dummy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 474/522: FMResultSet.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 475/522: FMDatabaseQueue.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 476/522: FMDatabasePool.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 477/522: FMDatabaseAdditions.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 478/522: FMDatabase.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 479/522: FMDB-dummy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 480/522: DKProtocolsPool-dummy.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 481/522: DKPServices.m
2025-07-10 15:25:28,567 - pipeline.steps.ast_step_all - INFO - 处理编译文件 482/522: DKPQQMaps.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 483/522: DKPPerformanceStatService.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 484/522: DKPApplications.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 485/522: UIViewController+DKApplication.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 486/522: UINavigationController+DKApplication.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 487/522: DKServiceStartMirror.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 488/522: DKServiceDigest.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 489/522: DKServiceContext.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 490/522: DKSchemeHandler.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 491/522: DKRulesChecker.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 492/522: DKRouterContext.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 493/522: DKRouter.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 494/522: DKRouter+URLRouter.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 495/522: DKRouter+Service.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 496/522: DKRouter+Application.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 497/522: DKRegistryCenter.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 498/522: DKMobileCore.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 499/522: DKPQQMaps.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 500/522: DKMobile-dummy.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 501/522: DKMachORegistry.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 502/522: DKLog.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 503/522: DKCoreDelegate.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 504/522: DKCoreDelegate+CarPlay.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 505/522: DKApplicationDigest.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 506/522: DKApplication.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 507/522: DKAppLifeDelegate.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 508/522: DDTTYLogger.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 509/522: DDOSLogger.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 510/522: DDMultiFormatter.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 511/522: DDLoggerNames.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 512/522: DDLog.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 513/522: DDFileLogger.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 514/522: DDFileLogger+Buffering.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 515/522: DDDispatchQueueLogFormatter.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 516/522: DDContextFilterLogFormatter.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 517/522: DDContextFilterLogFormatter+Deprecated.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 518/522: DDAbstractDatabaseLogger.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 519/522: DDASLLogger.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 520/522: DDASLLogCapture.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 521/522: CocoaLumberjack-dummy.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 处理编译文件 522/522: CLIColor.m
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 发现主项目源文件
2025-07-10 15:25:28,568 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBusiness
2025-07-10 15:25:28,721 - pipeline.steps.ast_step_all - INFO - 在 QMapBusiness 中发现 1686 个源文件
2025-07-10 15:25:28,722 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapMiddlePlatform
2025-07-10 15:25:28,780 - pipeline.steps.ast_step_all - INFO - 在 QMapMiddlePlatform 中发现 1023 个源文件
2025-07-10 15:25:28,780 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBaseline
2025-07-10 15:25:28,792 - pipeline.steps.ast_step_all - INFO - 在 QMapBaseline 中发现 135 个源文件
2025-07-10 15:25:28,792 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapFoundation
2025-07-10 15:25:28,803 - pipeline.steps.ast_step_all - INFO - 在 QMapFoundation 中发现 132 个源文件
2025-07-10 15:25:28,803 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapNaviKit
2025-07-10 15:25:28,992 - pipeline.steps.ast_step_all - INFO - 在 QMapNaviKit 中发现 461 个源文件
2025-07-10 15:25:28,992 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapHippy
2025-07-10 15:25:29,004 - pipeline.steps.ast_step_all - INFO - 在 QMapHippy 中发现 256 个源文件
2025-07-10 15:25:29,004 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DragonMapKit
2025-07-10 15:25:29,036 - pipeline.steps.ast_step_all - INFO - 在 DragonMapKit 中发现 207 个源文件
2025-07-10 15:25:29,036 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapUIKit
2025-07-10 15:25:29,048 - pipeline.steps.ast_step_all - INFO - 在 QMapUIKit 中发现 66 个源文件
2025-07-10 15:25:29,048 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapRouteSearchKit
2025-07-10 15:25:29,050 - pipeline.steps.ast_step_all - INFO - 在 QMapRouteSearchKit 中发现 37 个源文件
2025-07-10 15:25:29,050 - pipeline.steps.ast_step_all - INFO - 扫描仓库: TencentMap
2025-07-10 15:25:29,060 - pipeline.steps.ast_step_all - INFO - 在 TencentMap 中发现 34 个源文件
2025-07-10 15:25:29,060 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DKProtocolsPool
2025-07-10 15:25:29,064 - pipeline.steps.ast_step_all - INFO - 在 DKProtocolsPool 中发现 12 个源文件
2025-07-10 15:25:29,064 - pipeline.steps.ast_step_all - INFO - 总共发现 4049 个项目源文件
2025-07-10 15:25:52,275 - pipeline.steps.ast_step_all - INFO - 补充处理符号映射中的项目文件
2025-07-10 15:25:52,312 - pipeline.steps.ast_step_all - INFO - 处理完成: 编译命令文件 522 个, 发现文件 4049 个, 符号映射文件 395 个, 总计 4966 个文件
2025-07-10 15:25:53,230 - pipeline.steps.ast_step_all - INFO - 节点和边已保存到: ast_out_index_all/nodes_all.jsonl, ast_out_index_all/edges_all.jsonl
2025-07-10 15:25:53,270 - __main__ - INFO - ✅ AST分析成功
2025-07-10 15:25:53,270 - __main__ - INFO - 📍 阶段6: 统计生成
2025-07-10 15:25:53,270 - pipeline.steps.stats_step_all - INFO - 开始多仓库统计验证
2025-07-10 15:25:54,298 - pipeline.steps.stats_step_all - INFO - 加载了 81268 个节点和 518910 条边
2025-07-10 15:25:54,298 - pipeline.steps.stats_step_all - INFO - 计算综合统计信息
2025-07-10 15:39:16,240 - pipeline.steps.stats_step_all - INFO - 统计结果已保存到: ast_out_index_all/stats_all.json
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 多仓库统计结果
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 总节点数: 81,268
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 总边数: 518,910
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 跨仓库关系: 15,754
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 
节点类型分布:
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   Method: 57,582
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   Property: 14,144
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   File: 4,966
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   Class: 4,571
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   Protocol: 3
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   Struct: 1
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   Enum: 1
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 
仓库分布:
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapBusiness: 38,787 节点, 1,706 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapMiddlePlatform: 17,024 节点, 1,003 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapNaviKit: 11,122 节点, 461 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   DragonMapKit: 4,602 节点, 432 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapHippy: 2,820 节点, 256 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapFoundation: 2,170 节点, 205 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapBaseline: 1,892 节点, 135 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapUIKit: 1,110 节点, 66 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   TencentMap: 291 节点, 34 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   QMapRouteSearchKit: 261 节点, 37 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   DKProtocolsPool: 98 节点, 57 文件
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO - 
质量指标:
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   节点边比例: 6.39
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   平均节点度数: 6.57
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   数据完整性: 1.00
2025-07-10 15:39:16,241 - pipeline.steps.stats_step_all - INFO -   文件覆盖率: 1.23
2025-07-10 15:39:16,317 - __main__ - INFO - ✅ 统计生成成功
2025-07-10 15:39:16,317 - __main__ - INFO - 📍 验证最终结果
2025-07-10 15:39:16,371 - __main__ - INFO - 📊 最终结果:
2025-07-10 15:39:16,371 - __main__ - INFO -    节点数量: 81,268
2025-07-10 15:39:16,371 - __main__ - INFO -    边数量: 518,910
2025-07-10 15:39:16,371 - __main__ - INFO -    节点文件大小: 24.9 MB
2025-07-10 15:39:16,371 - __main__ - INFO -    边文件大小: 95.5 MB
2025-07-10 15:39:16,371 - __main__ - INFO - 🎉 Pipeline-All执行完成，耗时: 930.86秒
2025-07-10 17:17:30,488 - __main__ - INFO - 🚀 开始执行Pipeline-All (健壮版本)
2025-07-10 17:17:30,488 - __main__ - INFO - ============================================================
2025-07-10 17:17:30,489 - __main__ - INFO - 📍 阶段1: 环境准备
2025-07-10 17:17:30,489 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-10 17:17:30,511 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-10 17:17:30,514 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-10 17:17:30,555 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-10 17:17:30,618 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-10 17:17:30,633 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-10 17:17:30,701 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-10 17:17:30,792 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-10 17:17:31,031 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-10 17:17:31,391 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-10 17:17:31,724 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-10 17:17:31,732 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-10 17:17:31,733 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-10 17:17:31,733 - __main__ - INFO - ✅ 发现 11 个仓库
2025-07-10 17:17:31,733 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-10 17:17:35,621 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-10 17:17:35,622 - pipeline.steps.clean_step - INFO - 删除文件: index_symbols_all.jsonl
2025-07-10 17:17:35,623 - __main__ - INFO - ✅ 环境清理完成
2025-07-10 17:17:35,623 - __main__ - INFO - 📍 阶段2: 索引生成
2025-07-10 17:17:35,623 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-10 17:17:35,623 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-10 17:17:35,623 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-10 17:17:35,623 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-10 17:17:35,626 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-10 17:17:35,626 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成索引...
2025-07-10 17:17:35,627 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-10 17:17:35,628 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-10 17:17:35,630 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成索引...
2025-07-10 17:17:35,632 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-10 17:17:49,185 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapFoundation and configuration Debug
(1 failure)

2025-07-10 17:17:49,186 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-10 17:17:52,630 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DragonMapKit and configuration Debug
(1 failure)

2025-07-10 17:17:52,630 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-10 17:17:53,565 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-10 17:17:53,565 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-10 17:17:53,751 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-10 17:17:53,752 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-10 17:17:53,897 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-10 17:17:53,897 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-10 17:17:53,921 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-10 17:17:53,922 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-10 17:17:53,994 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-10 17:17:53,994 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-10 17:17:57,004 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-10 17:17:57,005 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-10 17:18:08,066 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-10 17:18:16,348 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-10 17:18:16,889 - pipeline.steps.index_store_step_all - ERROR - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug
(1 failure)

2025-07-10 17:18:18,812 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(1 failure)

2025-07-10 17:18:18,876 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-10 17:18:19,083 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-10 17:18:19,368 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-10 17:18:32,207 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 索引生成成功
2025-07-10 17:18:32,209 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-10 17:18:32,264 - pipeline.steps.index_store_step_all - INFO - 索引存储输出验证成功，找到 4747 个索引记录文件
2025-07-10 17:18:32,274 - __main__ - INFO - ✅ 索引生成成功
2025-07-10 17:18:32,274 - __main__ - INFO - 📍 阶段3: 符号提取
2025-07-10 17:18:32,274 - pipeline.steps.usrs_step_all - INFO - 开始提取多仓库USR到文件路径映射
2025-07-10 17:18:32,274 - pipeline.steps.usrs_step_all - INFO - 检查索引存储目录: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-10 17:18:32,274 - pipeline.steps.usrs_step_all - INFO - 开始提取USR到文件路径映射
2025-07-10 17:18:32,274 - pipeline.steps.usrs_step_all - INFO - 使用索引存储路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-10 17:18:32,274 - pipeline.steps.usrs_step_all - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store ast_out_index_all/DerivedData/Index.noindex/DataStore --out index_symbols_all.jsonl
2025-07-10 17:18:36,174 - pipeline.steps.usrs_step_all - INFO - USR提取命令执行成功
2025-07-10 17:18:36,190 - pipeline.steps.usrs_step_all - INFO - 成功提取 148987 个USR映射
2025-07-10 17:18:36,190 - pipeline.steps.usrs_step_all - INFO - 分析提取的USR数据...
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO - USR分析完成:
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -   总USR数: 148,967
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -   项目文件: 0
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -   Pods文件: 35,224
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -   系统文件: 0
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -   仓库分布:
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -     unknown: 143,231
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -     DragonMapKit: 3,030
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -     QMapFoundation: 1,863
2025-07-10 17:18:36,542 - pipeline.steps.usrs_step_all - INFO -     DKProtocolsPool: 843
2025-07-10 17:18:36,542 - __main__ - INFO - ✅ 符号提取成功
2025-07-10 17:18:36,542 - __main__ - INFO - 📍 阶段4: 编译数据库生成
2025-07-10 17:18:36,542 - pipeline.steps.cdb_step_all - INFO - 开始生成多仓库编译数据库
2025-07-10 17:18:36,542 - pipeline.steps.cdb_step_all - INFO - 发现 16 个schemes需要处理
2025-07-10 17:18:36,542 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成编译数据库...
2025-07-10 17:18:36,542 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-10 17:18:36,543 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成编译数据库...
2025-07-10 17:18:36,543 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成编译数据库...
2025-07-10 17:18:36,543 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成编译数据库...
2025-07-10 17:18:36,546 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成编译数据库...
2025-07-10 17:18:36,548 - pipeline.steps.cdb_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成编译数据库...
2025-07-10 17:18:36,552 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成编译数据库...
2025-07-10 17:18:47,146 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败，但继续解析日志
2025-07-10 17:18:47,147 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapFoundation 的构建日志，长度: 14539 字符
2025-07-10 17:18:47,147 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 5 个clang命令
2025-07-10 17:18:47,149 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:47,149 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapFoundation 的scheme 'QMapFoundation' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:47,149 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成编译数据库...
2025-07-10 17:18:48,924 - pipeline.steps.cdb_step_all - WARNING - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败，但继续解析日志
2025-07-10 17:18:48,924 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DragonMapKit 的构建日志，长度: 20632 字符
2025-07-10 17:18:48,924 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:18:48,924 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:48,924 - pipeline.steps.cdb_step_all - INFO - 仓库 DragonMapKit 的scheme 'DragonMapKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:48,924 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成编译数据库...
2025-07-10 17:18:49,669 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败，但继续解析日志
2025-07-10 17:18:49,669 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapNaviKit 的构建日志，长度: 29954 字符
2025-07-10 17:18:49,669 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:18:49,669 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:49,669 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:49,670 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成编译数据库...
2025-07-10 17:18:49,714 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败，但继续解析日志
2025-07-10 17:18:49,714 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapHippy 的构建日志，长度: 29639 字符
2025-07-10 17:18:49,714 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:18:49,714 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:49,714 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapHippy 的scheme 'QMapHippy' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:49,714 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成编译数据库...
2025-07-10 17:18:49,724 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败，但继续解析日志
2025-07-10 17:18:49,724 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapMiddlePlatform 的构建日志，长度: 27963 字符
2025-07-10 17:18:49,724 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:18:49,724 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:49,724 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:49,725 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成编译数据库...
2025-07-10 17:18:50,037 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败，但继续解析日志
2025-07-10 17:18:50,038 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBaseline 的构建日志，长度: 34808 字符
2025-07-10 17:18:50,038 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:18:50,038 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:50,038 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBaseline 的scheme 'QMapBaseline' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:50,038 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成编译数据库...
2025-07-10 17:18:50,331 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败，但继续解析日志
2025-07-10 17:18:50,331 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBusiness 的构建日志，长度: 41602 字符
2025-07-10 17:18:50,331 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:18:50,331 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:18:50,331 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBusiness 的scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:18:50,331 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成编译数据库...
2025-07-10 17:18:55,764 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapUIKit 的构建日志，长度: 6782896 字符
2025-07-10 17:18:55,771 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 242 个CompileC行, 1273 个clang命令
2025-07-10 17:18:58,763 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 257/509 个文件通过过滤
2025-07-10 17:18:58,763 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 编译数据库生成成功，包含 257 个编译命令
2025-07-10 17:18:58,763 - pipeline.steps.cdb_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成编译数据库...
2025-07-10 17:19:01,258 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败，但继续解析日志
2025-07-10 17:19:01,258 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 1963 字符
2025-07-10 17:19:01,258 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 1 个clang命令
2025-07-10 17:19:01,258 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:01,258 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'BuildInfo' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:05,201 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'TencentMap' 构建失败，但继续解析日志
2025-07-10 17:19:05,202 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89211 字符
2025-07-10 17:19:05,202 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:19:05,203 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:05,203 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'TencentMap' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:07,703 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败，但继续解析日志
2025-07-10 17:19:07,703 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89212 字符
2025-07-10 17:19:07,703 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:19:07,703 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:07,703 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntentsUI' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:07,879 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntents' 构建失败，但继续解析日志
2025-07-10 17:19:07,879 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89210 字符
2025-07-10 17:19:07,879 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:19:07,879 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:07,879 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntents' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:08,694 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败，但继续解析日志
2025-07-10 17:19:08,694 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89222 字符
2025-07-10 17:19:08,694 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:19:08,694 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:08,694 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMNotificationService' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:09,250 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败，但继续解析日志
2025-07-10 17:19:09,250 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89218 字符
2025-07-10 17:19:09,250 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-10 17:19:09,250 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:09,250 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMWidgetExtension' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:13,299 - pipeline.steps.cdb_step_all - WARNING - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败，但继续解析日志
2025-07-10 17:19:13,299 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DKProtocolsPool 的构建日志，长度: 2277 字符
2025-07-10 17:19:13,299 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 2 个clang命令
2025-07-10 17:19:13,299 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-10 17:19:13,299 - pipeline.steps.cdb_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 编译数据库生成成功，包含 0 个编译命令
2025-07-10 17:19:34,732 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapRouteSearchKit 的构建日志，长度: 15574874 字符
2025-07-10 17:19:34,748 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 590 个CompileC行, 2842 个clang命令
2025-07-10 17:19:37,720 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 650/1272 个文件通过过滤
2025-07-10 17:19:37,721 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 编译数据库生成成功，包含 650 个编译命令
2025-07-10 17:19:37,723 - pipeline.steps.cdb_step_all - INFO - 合并编译命令完成，共 650 个唯一文件
2025-07-10 17:19:37,728 - pipeline.steps.cdb_step_all - INFO - 编译数据库已保存到: ast_out_index_all/compile_commands/compile_commands.json
2025-07-10 17:19:37,728 - __main__ - INFO - ✅ 编译数据库生成成功
2025-07-10 17:19:37,728 - __main__ - INFO - 📍 阶段5: AST分析 (核心步骤)
2025-07-10 17:19:37,728 - pipeline.steps.ast_step_all - INFO - 开始多仓库AST抽取
2025-07-10 17:19:37,731 - pipeline.steps.ast_step_all - INFO - 从编译数据库加载了 650 个编译命令
2025-07-10 17:19:37,731 - pipeline.steps.ast_step_all - INFO - 加载了 650 个编译命令
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11670: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11671: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11672: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11673: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11674: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11675: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11676: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11677: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11678: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,742 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 11679: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-10 17:19:37,867 - pipeline.steps.ast_step_all - WARNING - 跳过了 20/148987 个无效的符号行
2025-07-10 17:19:37,867 - pipeline.steps.ast_step_all - INFO - 成功加载 148967 个符号映射
2025-07-10 17:19:37,867 - pipeline.steps.ast_step_all - INFO - 🔧 使用clang AST解析器进行真实的AST分析
2025-07-10 17:19:37,867 - pipeline.steps.ast_step_all - INFO - 使用符号索引文件: index_symbols_all.jsonl
2025-07-10 17:19:37,871 - pipeline.steps.ast_step_all - INFO - 使用clang库: /opt/homebrew/opt/llvm/lib/libclang.dylib
2025-07-10 17:19:37,871 - pipeline.steps.ast_step_all - INFO - 执行clang AST解析器: python3 scripts/ast_extractor.py ast_out_index_all/compile_commands/compile_commands.json --out ast_out_index_all --include-pods --index index_symbols_all.jsonl
2025-07-10 17:19:53,391 - pipeline.steps.ast_step_all - INFO - ✅ clang AST解析器执行成功
2025-07-10 17:19:53,391 - pipeline.steps.ast_step_all - INFO - 输出: [parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MMKV/iOS/MMKV/MMKV/libMMKV.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/lottie-ios/lottie-ios-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/MacCompatability/UIColor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Extensions/UIColor+Expanded.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/MacCompatability/UIBezierPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/MacCompatability/NSValue+Compat.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTValueInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTValueCallback.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/ManipulatorNodes/LOTTrimPathNode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTTransformInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/RenderNodes/LOTStrokeRenderer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTSizeInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeTrimPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeTransform.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeStroke.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeStar.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeRepeater.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeRectangle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapePath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeGroup.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeGradientFill.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeFill.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTShapeCircle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/AnimatorNodes/LOTRoundedRectAnimator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/RenderNodes/LOTRepeaterRenderer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/LOTRenderNode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/RenderNodes/LOTRenderGroup.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Extensions/LOTRadialGradientLayer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/AnimatorNodes/LOTPolystarAnimator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/AnimatorNodes/LOTPolygonAnimator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTPointInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTPathInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/AnimatorNodes/LOTPathAnimator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTNumberInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/AnimatableLayers/LOTMaskContainer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTMask.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTLayerGroup.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/AnimatableLayers/LOTLayerContainer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTLayer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTKeypath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/AnimatableProperties/LOTKeyframe.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTInterpolatorCallback.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/RenderNodes/LOTGradientFillRender.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/RenderNodes/LOTFillRenderer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/AnimatableLayers/LOTCompositionContainer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTComposition.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTColorInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/AnimatorNodes/LOTCircleAnimator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTCacheProvider.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTBlockCallback.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Extensions/LOTBezierPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/AnimatableProperties/LOTBezierData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTAssetGroup.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Models/LOTAsset.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/InterpolatorNodes/LOTArrayInterpolator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/RenderSystem/LOTAnimatorNode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTAnimationView.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTAnimationTransitionController.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTAnimationCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTAnimatedSwitch.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Private/LOTAnimatedControl.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/Extensions/CGGeometry+LOTAdditions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/lottie-ios/lottie-ios/Classes/MacCompatability/CALayer+Compat.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIView+WebCacheOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIView+WebCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImageView+WebCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImageView+HighlightedWebCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+Transform.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+MultiFormat.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+Metadata.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+MemoryCacheCost.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+GIF.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+ForceDecode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIImage+ExtendedCacheData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/UIColor+SDHexString.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/UIButton+WebCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageTransition.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImagePrefetcher.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageOptionsProcessor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageIndicator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageError.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDownloaderResponseModifier.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDownloaderRequestModifier.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDownloaderOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDownloaderDecryptor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDownloaderConfig.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDownloader.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageDefine.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageCompat.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageCacheSerializer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDWebImageCacheKeyFilter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/SDWebImage-iOS12.0/SDWebImage-iOS12.0-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDWeakProxy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDMemoryCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDInternalMacros.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageTransformer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageLoadersManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageLoader.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageIOCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageIOAnimatedCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageHEICCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageGraphics.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageGIFCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDImageFramePool.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageFrame.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCodersManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCoderHelper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDImageCachesManagerOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCachesManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCacheDefine.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCacheConfig.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDImageAssetManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageAWebPCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDImageAPNGCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDGraphicsImageRenderer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDFileAttributeHelper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDDisplayLink.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDDiskCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDDeviceHelper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDCallbackQueue.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDAssociatedObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/SDAsyncBlockOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDAnimatedImageView.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDAnimatedImageView+WebCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDAnimatedImageRep.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDAnimatedImagePlayer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/SDAnimatedImage.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/NSImage+Compatibility.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/NSData+ImageContentType.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Core/NSButton+WebCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImage/SDWebImage/Private/NSBezierPath+SDRoundedCorners.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/View/Coordinate/UIView+QMCoordinate.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/View/AutoLayout/UIView+QMAutoLayout.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/Color/UIColor+QMExtension.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapWidgets-iOS12.0/QMapWidgets-iOS12.0-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/Window/QMWindowTools.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/TableView/Formatter/QMTableViewSection.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/TableView/Formatter/QMTableViewRow.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/TableView/Formatter/QMTableViewFormatter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Tools/View/UILabel/QMEdgeLabel.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Components/Button/QMButton.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Components/AlertController/QMAlertWindowViewController.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapWidgets/QMapWidgets/Components/AlertController/QMAlertController.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIWindow+QDMEnvironment.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIViewController+QDMEnvironment.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIView+QDMEnvironment.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIScreen+QDMEnvironment.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIImageView+QDM.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIImage+QDM.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/UIColor+QDM.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QMapDarkModeManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapDarkMode/QMapDarkMode-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QDMTraitCollection.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QDMRegistry.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QDMLog.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QDMGlobalTestHooks.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QDMEnvConfiguration.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/QDMConvertor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapDarkMode/QMapDarkMode/Classes/NSObject+QDMSwizzle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapBasics-iOS12.0/QMapBasics-iOS12.0-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Timer/QMTimer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Swizzle/QMSwizzle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Reachability/QMReachability.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Path/QMPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Reachability/QMNetworkStatusManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Digest/QMDigest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DateFormatter/QMDateFormatterPool.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/CallDetection/QMCallManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Version/NSString+QMVersionSplit.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Version/NSString+QMVersionCompare.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/URL/NSString+QMURL.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSString+QMCommon.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/BasicMathType/NSObject+QMSafeTypeConversion.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSMutableString+QMSafeStringOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSMutableSet+QMSafeCollectionSet.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSMutableDictionary+QMSafeCollectionDictionary.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSMutableArray+QMSafeCollectonArray.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSDictionary+QMHasObjectType.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSDictionary+QMCommon.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Date/NSDate+Common.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/ImageContentType/NSData+QMImageContentType.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Zip/NSData+QMGZip.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/Base64/NSData+QMBase64WithImagePrefix.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/DataValidation/NSArray+QMSafeCollectonArray.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBasics/QMapBasics/Tools/ChainedCollection/NSArray+QMChaining.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/Protobuf/Protobuf-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBWrappers.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBWireFormat.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBWellKnownTypes.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBUtilities.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBUnknownFieldSet.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBUnknownField.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBType.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBTimestamp.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBStruct.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBSourceContext.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBRootObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBMessage.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBFieldMask.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBExtensionRegistry.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBExtensionInternals.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBEmpty.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBDuration.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBDictionary.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBDescriptor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBCodedOutputStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBCodedInputStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBArray.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBApi.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Protobuf/objectivec/GPBAny.pbobjc.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/ViewController+MASAdditions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/View+MASAdditions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/NSLayoutConstraint+MASDebugAdditions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/NSArray+MASAdditions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/Masonry-iOS12.0/Masonry-iOS12.0-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/MASViewConstraint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/MASViewAttribute.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/MASLayoutConstraint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/MASConstraintMaker.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/MASConstraint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Masonry/Masonry/MASCompositeConstraint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/MMKVCore/MMKVCore-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/MMKV/MMKV-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/PBAPLProtocol.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Core/ApolloSDK.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/ApolloSDK/ApolloSDK-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLSafeObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Core/APLSDKHelper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Core/APLSDKConfig.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLRespDTO.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Core/APLRegistry.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Macros/APLPrivate.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Core/APLNodeDataRefresher.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLNode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Network/APLNetworkSessionManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Network/APLNetworkSessionDataTask.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Network/APLNetworkSerialization.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Network/APLNetworkRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Plugins/APLNetworkEncryptor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Network/APLNetworkAPI.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLModuleObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Plugins/APLLog.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Macros/APLInterface.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLConfigObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Cache/APLCacheConfig.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/Cache/APLCache.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLBusinessObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLBasePO.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/ApolloSDK/ApolloSDK/Classes/DataEntries/APLAppObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/QMUniPacket.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/QMRequest.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Logger/Encryt/QCloudEncryt.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudAuthentationV4Creator.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/QCloudSecret/NSString+QCloudSHA.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/libwebp/libwebp-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/YYModel/YYModel-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/YYModel/YYModel/NSObject+YYModel.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/YYModel/YYModel/YYClassInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/TAM_IOS_SDK/TAM_IOS_SDK-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Util/TAMDRAsyncOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Util/TAMDRAsyncBlockOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Config/TAMCopyableObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Util/TAMAegisUtil.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Core/TAMAegisUploadData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Core/TAMAegisResourceSpeedData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Util/TAMAegisLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Uploader/TAMAegisLogUploadProxy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Config/TAMAegisInterfaceConfig.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Core/TAMAegis.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Util/TAMAccumulateTimer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TAM_IOS_SDK/TAM_IOS_SDK/Category/NSTimer+BTWeak.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/SocketRocket/SocketRocket-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SocketRocket/SocketRocket/SRWebSocket.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SSZipArchive/SSZipArchive/SSZipArchive.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/SSZipArchive/SSZipArchive-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImageWebPCoder/SDWebImageWebPCoder/Classes/UIImage+WebP.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImageWebPCoder/SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/SDWebImageWebPCoder/SDWebImageWebPCoder/Classes/SDImageWebPCoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapBuildTools-iOS12.0/QMapBuildTools-iOS12.0-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapBuildTools/QMapBuildTools/Tools/QMBuildManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/UniAttribute.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMWUP-iOS12.0/QMWUP-iOS12.0-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/JceStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/JceOutputStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/JceObjectV2.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/JceObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/JceInputStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMWUP/WirelessUnifiedProtocol/Serializable/JceEnumHelper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMEncrypt/QMEncrypt/Classes/QMEncrypt.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMEncrypt/QMEncrypt-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMEncrypt/QMEncrypt/Classes/QMEWDHash.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudBundle/UIImage+QCloudBundle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/FCUUID/UIDevice+QCloudFCUUID.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QualityAssurance/QualityDataUploader.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/Serializer/XML/XMLDictionary/Writer/QCloudXMLWriter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/Serializer/XML/XMLDictionary/Reader/QCloudXMLDictionary.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/ObjectFack/QCloudWeakProxy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Retry/QCloudUploadPartRequestRetryHandler.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudUniversalPathFactory.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudUniversalPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudUniversalFixedPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudUniversalAdjustablePath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/URLTools/QCloudURLTools.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/SessionSupport/QCloudURLSessionTaskData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/Serializer/QCloudURLHelper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudUICKeyChainStore.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Bolts-Promise/QCloudThreadSafeMutableDictionary.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Supervisory/QCloudSupervisorySession.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Supervisory/QCloudSupervisoryRecord.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Supervisory/QCloudSupervisory.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DNSCache/QCloudSimplePing.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudSignatureFields.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudSignature.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Service/QCloudServiceConfiguration.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudBase/QualityAssurance/QCloudServiceConfiguration+Quality.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Service/QCloudService.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudSandboxPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDFileUtils/QCloudSHAPart.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/Serializer/QCloudResponseSerializer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Supervisory/QCloudSDKModuleManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/Serializer/QCloudRequestSerializer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Operations/QCloudRequestOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/QCloudRequestData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/reachability/QCloudReachability.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDProgramDefines/QCloudProgrameDefines.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DNSCache/QCloudPingTester.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Operations/QCloudOperationQueue.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Request/QCloudNetResponse.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Profile/QCloudNetProfile.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/reachability/QCloudNetEnv.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/ObjectFack/QCloudMultiDelegateProxy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDFileUtils/QCloudModel.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudMediaPath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudBundle/QCloudMainBundle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Logger/QCloudLoggerOutput.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Logger/QCloudLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Logger/QCloudLogModel.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/TimeOutAdapter/QCloudIntelligenceTimeOutAdapter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Profile/QCloudHttpMetrics.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DNSCache/QCloudHttpDNS.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DNSCache/QCloudHosts.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Retry/QCloudHTTPTaskDelayManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/SessionSupport/QCloudHTTPSessionManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Retry/QCloudHTTPRetryHanlder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Operations/QCloudHTTPRequestOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/QCloudHTTPRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DataForm/QCloudHTTPMultiDataStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DataForm/QCloudHTTPBodyPart.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudGCDTimer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Logger/QCloudFileZipper.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDFileUtils/QCloudFileUtils.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DataForm/QCloudFileOffsetStream.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DataForm/QCloudFileOffsetBody.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/Logger/QCloudFileLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Operations/QCloudFakeRequestOperation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/FCUUID/QCloudFCUUID.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Service/QCloudError.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/QCloudEnv/QCloudEnv.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Service/QCloudEndPoint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/DNSCache/QCloudDomain.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudCredential.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudCredentailFenceQueue.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudCoreVersion.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QCloudCore/QCloudCore-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Service/QCloudConfiguration.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/QCloudClientContext.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDObjectModel/QCloudClassInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/PathUtilities/QCloudBundlePath.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Request/QCloudBizHTTPRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudAuthentationV5Creator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudClientBase/Authentation/QCloudAuthentationCreator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/QCloudAbstractRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDObjectModel/NSObject+QCloudModelTool.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDObjectModel/NSObject+QCloudModel.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/SessionSupport/NSObject+HTTPHeadersContainer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudCategory/NSMutableData+QCloud_CRC.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/CoreRequest/NSHTTPCookie+QCloudNetworking.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCLOUDRestNet/Error/NSError+QCloudNetworking.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudCategory/NSDate+QCloudInternetDateTime.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/DateTools/NSDate+QCloudComapre.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCore/QCloudCore/Classes/Base/QCloudCategory/NSDate+QCLOUD.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudUploadPartResult.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudUploadPartRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudUploadPartRequest+Custom.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudUploadPartCopyRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudUploadObjectResult.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudRequestData+COSXMLVersion.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudPutObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudPutObjectRequest+CustomBuild.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudPutObjectRequest+Custom.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudPutObjectCopyRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudMultipartUploadPart.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudMultipartUploadOwner.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudMultipartUploadInitiator.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudMultipartInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudLogManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudListPartsResult.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudListMultipartRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudInitiateMultipartUploadResult.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudInitiateMultipartUploadRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudHeadObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudGetObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudGetObjectRequest+Custom.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudCopyObjectResult.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudCompleteMultipartUploadRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/model/QCloudCompleteMultipartUploadInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/QCloudCOSXMLVersion.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudCOSXMLUploadObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudCOSXMLServiceUtilities.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudCOSXMLService.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/QCloudCOSXMLService+Transfer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudCOSXMLService+Quality.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudCOSXMLService+Configuration.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudCOSXMLEndPoint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudCOSXMLDownloadObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudCOSXMLCopyObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QCloudCOSXML/QCloudCOSXML-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/QCloudCOSTransferMangerService.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/enum/QCloudCOSStorageClassEnum.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudAppendObjectRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/QCloudAbstractRequest+Quality.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Transfer/request/QCloudAbortMultipfartUploadRequest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QCloudCOSXML/QCloudCOSXML/Classes/Base/NSString+RegularExpressionCategory.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/ReconnectTimer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTWebsocketTransport/MQTTWebsocketTransport.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTTransport.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTStrict.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSessionSynchron.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSessionManager.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSessionLegacy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSession.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSSLSecurityPolicyTransport.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSSLSecurityPolicyEncoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSSLSecurityPolicyDecoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTSSLSecurityPolicy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTProperties.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTMessage.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTLog.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTInMemoryPersistence.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTDecoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTCoreDataPersistence.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/MQTTClient/MQTTClient-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTCFSocketTransport.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTCFSocketEncoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/MQTTCFSocketDecoder.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/GCDTimer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MQTTClient/MQTTClient/MQTTClient/ForegroundReconnection.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSString+MJExtension.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJProperty.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJKeyValue.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJCoding.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/NSObject+MJClass.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJPropertyType.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJPropertyKey.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJProperty.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJFoundation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MJExtension/MJExtension/MJExtensionConst.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/MJExtension/MJExtension-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/KVOController/FBKVOController/NSObject+FBKVOController.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/KVOController/KVOController-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/KVOController/FBKVOController/FBKVOController.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GZIP/GZIP/Sources/NSData+GZIP.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/GZIP/GZIP-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/FMDB/src/fmdb/FMResultSet.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/FMDB/src/fmdb/FMDatabaseQueue.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/FMDB/src/fmdb/FMDatabasePool.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/FMDB/src/fmdb/FMDatabaseAdditions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/FMDB/src/fmdb/FMDatabase.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/FMDB/FMDB-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKProtocolsPool/DKProtocolsPool-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPServices.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPQQMaps.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/Baseline/DKPPerformanceStatService.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKProtocolsPool/DKProtocolsPool/Classes/DKPApplications.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UIViewController+DKApplication.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/UINavigationController+DKApplication.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceStartMirror.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceDigest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKServiceContext.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKSchemeHandler.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRulesChecker.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouterContext.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+URLRouter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Service.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRouter+Application.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKRegistryCenter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMobileCore.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DKMobile/DKMobile-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKMachORegistry.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKLog.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKCoreDelegate+CarPlay.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplicationDigest.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKApplication.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DKMobile/DKMobile/Classes/DKAppLifeDelegate.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDTTYLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDOSLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Extensions/DDMultiFormatter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDLoggerNames.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDLog.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDFileLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Extensions/DDFileLogger+Buffering.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Extensions/DDDispatchQueueLogFormatter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Extensions/DDContextFilterLogFormatter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/Extensions/DDContextFilterLogFormatter+Deprecated.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDAbstractDatabaseLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDASLLogger.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/DDASLLogCapture.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/CocoaLumberjack/CocoaLumberjack-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CocoaLumberjack/Sources/CocoaLumberjack/CLI/CLIColor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_ZoomForNaviParameter.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/DragonMapKitC2OC/DragonMapKitC2OC-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_TXUploadLogArgs.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_UniversalModelTapInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_TXAnimationParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_TMSize.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_TMRect.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_TMBitmapContext.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_TMPoint.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_SimpleLandMarkMode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_ShadowSetting.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_SectionDashedLineParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RouteTurnArrow3DStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RouteStyleAtScale.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RouteNameStyleAtScale.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RouteNameStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RouteGradientParamForSegmentMode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RouteGradientInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RichCallbackInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RGBADashedLineStyleAtScale.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RGBADashedLineExtraParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_RGBAColorLineExtraParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_POIInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_OverlookParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_Object3DTapInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_ModelID.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MarkerGroupIconAnchor.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_Marker4KInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapTree.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapTileID.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapTextDrawInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapTappedInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapRouteSectionWithName.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapRouteSection.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapRouteInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapRouteDescInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapRoadScanOptions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapRectF.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapPrimitive.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapPatternStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapNaviAccuracyCircleOptions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapNaviAccuracyCircleGradientNode.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapModelReportFlag.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerSubPoiInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapModel3DImageBuffer.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerLocatorInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerImageLabelInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerIconInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerGroupIconPosInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerGroupIconInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerCustomIconInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerClusterData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerAvoidRouteRule.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerAvoidDetailedRule.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapMarkerAnnotationInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapLocatorSpeedTextParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapLocatorScanLightOptions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapLocatorBreatheOptions.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapLocatorBreatheNodes.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapLocation.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapLaneID.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapHoleInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapEdgeInsets.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapDisplayParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapCircleInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapBitmapTileID.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_MapBitmap.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_JuncImageInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_InterestScenicAreaInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_InterestIndoorAreaInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_InterestAreaInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_IndoorTappedInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_IndoorParkSpaceInfoBatchs.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_IndoorParkSpaceInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_GuidanceEventInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_GlyphMetrics.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_GLMapFloorName.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_GLMapAnnotationText.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_GLMapAnnotationIcon.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_GLBuildingInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_EngineRenderContent.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_DynamicMapAnnotationObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_DownloadData.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_DayInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CustomTileRegionStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CustomTileQueryInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CustomTilePointStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CustomTileModel3DStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CustomTileLineStyle.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_Collision3DResult.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_ClusterTappedInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CameraOverlookParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_CameraAnimationParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BuildingLoadedParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BuildingLightInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BuildingLightIdAttributeIndex.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BuildingLightAttribute.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BlackWhiteListRule.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BillboardInfo.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_BaseObject.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_AnimationParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/DragonMapKitC2OC/DragonMapKitC2OC/DM_MAP_AnimationContent.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/RGModelInterface.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Utils/MapBaseOCUtils.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/MapBaseOCModel/MapBaseOCModel-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/MapBaseModelInterface.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/MapBaseModel.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/RGModelInterfaceOCCPP.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/MapBaseModelInterfaceOCCPP.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/CustomAgileMapManager.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/AgileMapResourceCallbackAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/AgileMarkerCanvasLayoutParamOCCPP.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/AgileMapManager.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/AgileCreateLayerCallBackAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/CustomAgileMapLayerOption.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/AgileMarkerCanvasLayoutParam.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseOCModel/MapBaseOCModel/Model/AgileLayerOption.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGListenerAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGWalkCycleListenerAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGWalkAPI.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGVersion.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware-dummy.m Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGDriveStatisticsListenerAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGDriveProvider.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGCycleAPI.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGDriveEventListenerAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGDriveBehaviorEventListenerAdapter.mm Error parsing translation unit.
[parse-error] /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidanceOCMiddleware/RouteGuidanceOCMiddleware/RouteGuidanceAPI/RGWalkCycleProtocol.m Error parsing translation unit.
nodes: 0, edges: 0 written to ast_out_index_all

2025-07-10 17:19:53,393 - pipeline.steps.ast_step_all - INFO - ✅ 生成了 0 个节点和 0 条边
2025-07-10 17:19:53,398 - __main__ - ERROR - ❌ Pipeline-All执行失败: AST分析失败: 未生成任何节点
