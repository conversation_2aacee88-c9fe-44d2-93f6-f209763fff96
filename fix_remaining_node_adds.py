#!/usr/bin/env python3
"""
修复剩余的节点添加调用

这个脚本将剩余的self.nodes.add调用替换为self.add_node_safe调用
"""

import re

def fix_remaining_node_adds():
    file_path = 'scripts/enhanced_ast_extractor.py'
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 查找所有剩余的self.nodes.add调用
    pattern = r'(\s+)(.*?)(\s*=\s*node_line\([^)]+\))\s*\n\s*self\.nodes\.add\(\w+\)'
    
    def replace_node_add(match):
        indent = match.group(1)
        var_assignment = match.group(2)
        node_line_call = match.group(3)
        
        # 提取node_line的参数
        node_line_match = re.search(r'node_line\("([^"]+)",\s*(\w+),\s*({[^}]+})\)', node_line_call)
        if node_line_match:
            label = node_line_match.group(1)
            id_var = node_line_match.group(2)
            attrs = node_line_match.group(3)
            
            return f'{indent}self.add_node_safe("{label}", {id_var}, {attrs})'
        
        return match.group(0)  # 如果无法解析，保持原样
    
    # 执行替换
    new_content = re.sub(pattern, replace_node_add, content, flags=re.MULTILINE | re.DOTALL)
    
    # 处理简单的self.nodes.add调用
    simple_pattern = r'self\.nodes\.add\((\w+)\)'
    
    def replace_simple_add(match):
        var_name = match.group(1)
        # 这些需要手动处理，因为我们需要知道具体的label和id
        return match.group(0)  # 保持原样，需要手动处理
    
    new_content = re.sub(simple_pattern, replace_simple_add, new_content)
    
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print('修复完成')

if __name__ == "__main__":
    fix_remaining_node_adds()
