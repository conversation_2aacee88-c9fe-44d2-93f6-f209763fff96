[{"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMapVectorLayout-dummy.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMapVectorLayout-dummy.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-dummy.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMapVectorLayout-dummy.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMapVectorLayout-dummy.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target Support Files/QMapVectorLayout/QMapVectorLayout-dummy.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicDSLPrebuildOperation.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicDSLPrebuildOperation.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicDSLPrebuildOperation.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicDSLPrebuildOperation.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicDSLPrebuildOperation.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicDSLPrebuildOperation.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLService.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLService.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLService.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLService.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLService.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLService.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLReportManager.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLReportManager.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Report/QMVLReportManager.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLReportManager.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLReportManager.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Report/QMVLReportManager.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayerHostingView.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayerHostingView.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayerHostingView.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayerHostingView.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayerHostingView.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayerHostingView.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayer.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayer.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayer.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayer.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPlayer.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLPlayer.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceReporter.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceReporter.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceReporter.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceReporter.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceReporter.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceReporter.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceManager.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceManager.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceManager.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceManager.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLPerformanceManager.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Performance/QMVLPerformanceManager.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapViewWidget.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapViewWidget.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapViewWidget.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapViewWidget.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapViewWidget.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapViewWidget.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapView.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapView.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapView.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapView.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMultiMapView.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMultiMapView.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+RouteExplain.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+RouteExplain.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+RouteExplain.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+RouteExplain.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+RouteExplain.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+RouteExplain.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+OmnipotentMap.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+OmnipotentMap.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+OmnipotentMap.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+OmnipotentMap.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+OmnipotentMap.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+OmnipotentMap.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMediaInfo.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMediaInfo.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLMediaInfo.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMediaInfo.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMediaInfo.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/Player/QMVLMediaInfo.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+Navi.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+Navi.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+Navi.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+Navi.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+Navi.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+Navi.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapViewWidget.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapViewWidget.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapViewWidget.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapViewWidget.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapViewWidget.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapViewWidget.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+BestL4Camera.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+BestL4Camera.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+BestL4Camera.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+BestL4Camera.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLMapView+BestL4Camera.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/MapView/QMVLMapView+BestL4Camera.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieWidget.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieWidget.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieWidget.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieWidget.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieWidget.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieWidget.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieView.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieView.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieView.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieView.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLottieView.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/QMLottieView/QMVLLottieView.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLogger.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLogger.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Log/QMVLLogger.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLogger.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLLogger.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Log/QMVLLogger.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLInjector.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLInjector.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Injector/QMVLInjector.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLInjector.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLInjector.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Injector/QMVLInjector.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageView.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageView.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageView.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageView.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageView.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageView.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageTransformer.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageTransformer.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageTransformer.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageTransformer.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageTransformer.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageTransformer.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageFetchManager.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageFetchManager.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageFetchManager.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageFetchManager.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLImageFetchManager.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/View/ImageView/QMVLImageFetchManager.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLFileItem.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLFileItem.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLFileItem.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLFileItem.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLFileItem.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLFileItem.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLCardViewCache.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLCardViewCache.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLCardViewCache.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLCardViewCache.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMVLCardViewCache.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/Service/Service/QMVLCardViewCache.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerService.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerService.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/QMDynamicMarkerService.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerService.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerService.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/QMDynamicMarkerService.m"}, {"directory": "hammmer-workspace", "command": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -x objective-c -ivfsstatcache /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/SDKStatCaches.noindex/iphonesimulator18.1-22B74-3d93aac3a03ebac1dd8474c5def773dc.sdkstatcache -fmessage-length\\=0 -fdiagnostics-show-note-include-stack -fmacro-backtrace-limit\\=0 -fno-color-diagnostics -fmodules-prune-interval\\=86400 -fmodules-prune-after\\=345600 -fbuild-session-file\\=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex/Session.modulevalidation -fmodules-validate-once-per-build-session -Wnon-modular-include-in-framework-module -Werror\\=non-modular-include-in-framework-module -Wno-trigraphs -Werror -Wno-missing-field-initializers -Wno-missing-prototypes -Werror\\=return-type -Wunreachable-code -Wno-implicit-atomic-properties -Werror\\=deprecated-objc-isa-usage -Wno-objc-interface-ivars -Werror\\=objc-root-class -Wno-arc-repeated-use-of-weak -Wimplicit-retain-self -Wduplicate-method-match -Wno-missing-braces -Wparentheses -Wswitch -Wunused-function -Wno-unused-label -Wno-unused-parameter -Wunused-variable -Wunused-value -Wempty-body -Wuninitialized -Wconditional-uninitialized -Wno-unknown-pragmas -Wno-shadow -Wno-four-char-constants -Wno-conversion -Wconstant-conversion -Wint-conversion -Wbool-conversion -Wenum-conversion -Wno-float-conversion -Wnon-literal-null-conversion -Wobjc-literal-conversion -Wshorten-64-to-32 -Wpointer-sign -Wno-newline-eof -Wno-selector -Wno-strict-selector-match -Wundeclared-selector -Wdeprecated-implementations -Wno-implicit-fallthrough -fstrict-aliasing -Wprotocol -Wdeprecated-declarations -Wno-sign-conversion -Winfinite-recursion -Wcomma -Wblock-capture-autoreleasing -Wstrict-prototypes -Wno-semicolon-before-method-body -Wunguarded-availability -index-store-path /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore @/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/e6072d4f65d7061329687fe24e3d63a7-common-args.resp -w -Xanalyzer -analyzer-disable-all-checks -include /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\\ Support\\ Files/QMapVectorLayout/QMapVectorLayout-prefix.pch -MMD -MT dependencies -MF /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerGenerator.d --serialize-diagnostics /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerGenerator.dia -c /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicMarkerGenerator.m -o /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerGenerator.o -index-unit-output-path /QMapVectorLayout.build/Debug-iphonesimulator/QMapVectorLayout.build/Objects-normal/x86_64/QMDynamicMarkerGenerator.o", "file": "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapVectorLayout/QMapVectorLayout/Classes/QMExtraOrdinaryMap/Downloader/QMDynamicMarkerGenerator.m"}]