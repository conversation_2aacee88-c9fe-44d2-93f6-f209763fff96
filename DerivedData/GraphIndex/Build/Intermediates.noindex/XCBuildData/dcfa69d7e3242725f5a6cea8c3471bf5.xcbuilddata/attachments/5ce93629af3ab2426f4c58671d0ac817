#!/bin/sh
      # Define paths
      if [ -d "${PODS_TARGET_SRCROOT}/AISound" ]; then
        echo 'Seems currently AISound is development pod, change source to the source root'
        SOURCE_DIR="${PODS_TARGET_SRCROOT}/AISound"
      else
        echo 'Seems currently AISound is available under Pods/, using them directly'
        SOURCE_DIR="${PODS_ROOT}/AISound/AISound"
      fi
      DEST_DIR="${CONFIGURATION_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}"

      echo "Copying frameworks from: ${SOURCE_DIR}"
      echo "Destination: ${DEST_DIR}"

      # Define source paths
      AIKIT_CORE_SDK="${SOURCE_DIR}/AIKIT.framework"
      e385e87d7_v11211242_aee_SDK="${SOURCE_DIR}/e385e87d7_v11211242_aee.framework"
      e236009e3_v101_aee_SDK="${SOURCE_DIR}/e236009e3_v101_aee.framework"
      e37a2f217_v1010_aee_SDK="${SOURCE_DIR}/e37a2f217_v1010_aee.framework"
      eec350bad_v106_aee_SDK="${SOURCE_DIR}/eec350bad_v106_aee.framework"

      # Use otool to list dependencies
      echo "Analyzing dependencies of AIKIT_CORE_SDK using otool..."
      DEPENDENCIES=$(otool -L "$AIKIT_CORE_SDK/AIKIT" | awk 'NR>1 {print $1}' | grep -v '^/System/Library' | grep -v '^/usr/lib' | grep -v 'AIKIT' | sort -u)

      echo "AIKIT actual external dependencies are:"
      echo "$DEPENDENCIES"

      # Extract expected dependencies dynamically from framework names
      EXPECTED_DEPENDENCIES=(
        "$(basename "$e385e87d7_v11211242_aee_SDK")" "$(basename "$e236009e3_v101_aee_SDK")" "$(basename "$e37a2f217_v1010_aee_SDK")" "$(basename "$eec350bad_v106_aee_SDK")"
      )

      echo "AIKIT expected dependencies are:"
      for EXPECTED_DEP in "${EXPECTED_DEPENDENCIES[@]}"; do
        echo "    $EXPECTED_DEP"
      done

      # Validate dependencies
      for DEP in $DEPENDENCIES; do
        # Extract just the framework name from the full path
        FRAMEWORK_NAME=$(basename "$(dirname "$DEP")")
        echo "Checking current found dependency: ${FRAMEWORK_NAME}"
        if [[ ! " ${EXPECTED_DEPENDENCIES[@]} " =~ " ${FRAMEWORK_NAME} " ]]; then
          echo "Error: New dependency detected: $DEP, but you didn't add to the copy list" >&2
          echo "If you SEE this message, you should update the framework list," >&2
          echo "As well as check dynamic loading code to properly handle the new framework if necessary" >&2
          exit 1
        fi
      done
      echo "All dependencies are valid."

      # Check and copy each framework
      for SDK_PATH in "$AIKIT_CORE_SDK" "$e385e87d7_v11211242_aee_SDK" "$e236009e3_v101_aee_SDK" "$e37a2f217_v1010_aee_SDK" "$eec350bad_v106_aee_SDK"; do
        if [ ! -e "$SDK_PATH" ]; then
          echo "Error: Framework not found at path: $SDK_PATH" >&2
          exit 1
        fi
        cp -f -R "$SDK_PATH" "${DEST_DIR}"
        if [ $? -ne 0 ]; then
          echo "Error: Failed to copy framework: $SDK_PATH" >&2
          exit 1
        fi
      done

