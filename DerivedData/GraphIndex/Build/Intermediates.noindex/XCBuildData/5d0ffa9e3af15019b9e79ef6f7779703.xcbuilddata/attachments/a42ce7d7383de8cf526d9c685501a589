-target x86_64-apple-ios12.0-simulator '-std=c++17' '-stdlib=libc++' -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DHIPPY_VERSION=*******' '-DJS_HERMES=1' '-DJS_JSC=1' '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DLAYOUT_ENGINE_TAITANK=1' '-DGLES_SILENCE_DEPRECATION=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/hippy-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/hippy-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/hippy-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy-ddc9e178894d483363db4ecabaed93c3-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/hippy-project-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/hippy/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/hippy_hermes_full -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/dom/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/footstone -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/footstone/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/driver/js/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy/modules/vfs/native/include -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/hippy.build/Debug-iphonesimulator/hippy.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/hippy -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/hippy_hermes_full/Full/destroot/Library/Frameworks/universal -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/hippy_hermes_full