-target x86_64-apple-ios12.0-simulator '-std=gnu++20' '-stdlib=libc++' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/QMExtraOrdinaryMap-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/QMExtraOrdinaryMap-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/QMExtraOrdinaryMap-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap-52773e68b542e40d6428f97e8eab6ea8-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/QMExtraOrdinaryMap-project-headers.hmap -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TXMapView/TXMapView.framework/PrivateHeaders -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/Headers -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/Modules -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/_CodeSignature -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders/agilemap -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders/animation -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders/base -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders/overlay -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/Headers -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib/GLMapLib.framework/PrivateHeaders/overlay -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/Headers -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/Modules -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/_CodeSignature -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/algorithm -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/cloudlog -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/common -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/files -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/framework -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jce -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/mapbase -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/net -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/parser -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/protocol -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/simple_queue -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/structure -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/timer -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/framework/v2 -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/ios -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/other -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/ios/protocol -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/other/protocol -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/algorithm -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/cloudlog -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/common -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/files -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/framework -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jce -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/mapbase -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/net -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/parser -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/protocol -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/simple_queue -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/structure -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/timer -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/framework/v2 -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/ios -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/other -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/ios/protocol -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew/MapBaseNew.framework/PrivateHeaders/jceheader/other/protocol -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PLog/PLog.framework/PrivateHeaders -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/QMExtraOrdinaryMap/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMExtraOrdinaryMap -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaLumberjack -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FMDB -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GZIP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/KVOController -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MJExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MQTTClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBaseOCModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCOSXML -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMExtraOrdinaryMap -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapFoundation -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapJCE -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QuickJS -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SocketRocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayout -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutExpression -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/VectorLayoutReactivity -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TLBSiOSAr -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TencentLBSZip -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lottie-ios -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMExtraOrdinaryMap/Headers -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapUIKit/Headers -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/thirdParty/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/thirdParty/src -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/3DConvertor/include/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/3DConvertor/include/include -I/Users/<USER>/GRAG_iOS/QMExtraOrdinaryMap/Classes/thirdParty/include -I/Users/<USER>/GRAG_iOS/QMExtraOrdinaryMap/Classes/thirdParty/src -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMExtraOrdinaryMap.build/Debug-iphonesimulator/QMExtraOrdinaryMap.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/QMExtraOrdinaryMap -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/AgileMap -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Beacon/beacon-frameworks/BeaconAPI_Base -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CrBase -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibCore -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBizNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapReflux -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/OlRoute -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PanguIosShell -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PoiEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PosEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMExtraOrdinaryMap/QMExtraOrdinaryMap/Classes/RenderEngine/lib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapFoundation/QMapFoundation -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapUIKit/QMapUIKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QimeiSDK -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RUM -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidance -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentLBS -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libpag/framework -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/mars/mars -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/AgileMap -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/CrBase -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLibCore -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBizNew -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapReflux -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/NerdApi -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/OlRoute -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PLog -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PanguIosShell -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PoiEngine -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PosEngine -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QimeiSDK -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/RouteGuidance -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TXMapView -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TencentLBS -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/libpag '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap' -I /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TXMapView/TXMapView.framework/PrivateHeaders/