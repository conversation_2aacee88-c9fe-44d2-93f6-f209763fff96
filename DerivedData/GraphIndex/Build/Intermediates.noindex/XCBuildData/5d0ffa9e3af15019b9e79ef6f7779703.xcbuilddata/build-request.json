{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "f97e9f01ddc2e7e78fbe254d5e80cde508170cfc5a7e9ca3bfaa5529e28226c2"}], "containerPath": "/Users/<USER>/GRAG_iOS/hammmer-workspace/TencentMap.xcworkspace", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "x86_64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.1", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "x86_64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products", "derivedDataPath": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex", "indexDataStoreFolderPath": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {"SDKROOT": "iphonesimulator18.1"}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone15,4", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.1", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone15,4", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "TARGET_DEVICE_IDENTIFIER": "F228D6DA-1F3D-453C-9BE0-895BAE5D80D7", "TARGET_DEVICE_MODEL": "iPhone15,4", "TARGET_DEVICE_OS_VERSION": "18.1", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}