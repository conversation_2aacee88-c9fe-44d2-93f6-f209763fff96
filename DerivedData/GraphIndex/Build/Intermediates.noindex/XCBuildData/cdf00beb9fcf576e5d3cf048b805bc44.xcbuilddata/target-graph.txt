Target dependency graph (106 targets)
Target 'QMapMiddlePlatform' in project 'QMapMiddlePlatform'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket'
➜ Explicit dependency on target 'DragonMapK<PERSON>' in project 'DragonMapKit'
➜ Explicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet'
➜ Explicit dependency on target 'GCDWebServer' in project 'GCDWebServer'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'libpag' in project 'libpag'
➜ Explicit dependency on target 'LongLinkSDK' in project 'Long<PERSON>inkSDK'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'MQQK<PERSON>ardKit' in project 'MQQKCardKit'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapSwiftBridge-iOS12.0' in project 'QMapSwiftBridge'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMSpiderManDebugTool' in project 'QMSpiderManDebugTool'
➜ Explicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
➜ Explicit dependency on target 'QMWeChatSDK' in project 'QMWeChatSDK'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
➜ Explicit dependency on target 'Tangram' in project 'Tangram'
➜ Explicit dependency on target 'TencentOpenAPI' in project 'TencentOpenAPI'
➜ Explicit dependency on target 'TMAISound' in project 'TMAISound'
➜ Explicit dependency on target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'TMWeAppClient' in project 'TMWeAppClient'
➜ Explicit dependency on target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
➜ Explicit dependency on target 'YYWebImage' in project 'YYWebImage'
Target 'YYWebImage' in project 'YYWebImage'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'YYImage' in project 'YYImage'
Target 'YYImage' in project 'YYImage' (no dependencies)
Target 'TMWeAppClient' in project 'TMWeAppClient'
➜ Explicit dependency on target 'WeAppSDK' in project 'WeAppSDK'
Target 'WeAppSDK' in project 'WeAppSDK' (no dependencies)
Target 'TMDylibLazyLoadWrapper' in project 'TMDylibLazyLoadWrapper'
➜ Explicit dependency on target 'AISDK' in project 'AISDK'
➜ Explicit dependency on target 'LarkLite' in project 'LarkLite'
➜ Explicit dependency on target 'QMapARNaviKit' in project 'QMapARNaviKit'
Target 'QMapARNaviKit' in project 'QMapARNaviKit' (no dependencies)
Target 'LarkLite' in project 'LarkLite' (no dependencies)
Target 'AISDK' in project 'AISDK' (no dependencies)
Target 'TMAISound' in project 'TMAISound'
➜ Explicit dependency on target 'AISound' in project 'AISound'
Target 'AISound' in project 'AISound' (no dependencies)
Target 'TencentOpenAPI' in project 'TencentOpenAPI' (no dependencies)
Target 'Tangram' in project 'Tangram' (no dependencies)
Target 'QMWeChatSDK' in project 'QMWeChatSDK' (no dependencies)
Target 'QMSpiderManDebugTool' in project 'QMSpiderManDebugTool'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
Target 'QMSpiderManWrapper' in project 'QMSpiderManWrapper'
➜ Explicit dependency on target 'QMSpiderMan' in project 'QMSpiderMan'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
Target 'QMSpiderMan' in project 'QMSpiderMan' (no dependencies)
Target 'QMapSwiftBridge-iOS12.0' in project 'QMapSwiftBridge' (no dependencies)
Target 'QMapRouteSearchKit' in project 'QMapRouteSearchKit'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
Target 'RouteGuidanceOCMiddleware' in project 'RouteGuidanceOCMiddleware'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
Target 'MQQKCardKit' in project 'MQQKCardKit' (no dependencies)
Target 'LongLinkSDK' in project 'LongLinkSDK'
➜ Explicit dependency on target 'GZIP' in project 'GZIP'
➜ Explicit dependency on target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'Protobuf' in project 'Protobuf'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
Target 'GCDWebServer' in project 'GCDWebServer' (no dependencies)
Target 'DragonMapKit' in project 'DragonMapKit'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'DragonMapKit-DragonMapKitBundle' in project 'DragonMapKit'
➜ Explicit dependency on target 'DragonMapKit-QQMapRes' in project 'DragonMapKit'
➜ Explicit dependency on target 'EDSunriseSet' in project 'EDSunriseSet'
➜ Explicit dependency on target 'FMDB' in project 'FMDB'
➜ Explicit dependency on target 'GLMapLibHeaders' in project 'GLMapLibHeaders'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMLibffi' in project 'QMLibffi'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
➜ Explicit dependency on target 'TXMapComLibForiOS' in project 'TXMapComLibForiOS'
➜ Explicit dependency on target 'YYCache' in project 'YYCache'
➜ Explicit dependency on target 'libpag' in project 'libpag'
Target 'YYCache' in project 'YYCache' (no dependencies)
Target 'TXMapComLibForiOS' in project 'TXMapComLibForiOS' (no dependencies)
Target 'QMLibffi' in project 'QMLibffi' (no dependencies)
Target 'QMExtraOrdinaryMap' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMExtraOrdinaryMap-QMExtraOrdinaryMapResources' in project 'QMExtraOrdinaryMap'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
➜ Explicit dependency on target 'libpag' in project 'libpag'
Target 'VectorLayout' in project 'VectorLayout'
➜ Explicit dependency on target 'ComponentKit' in project 'ComponentKit'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QuickJS' in project 'QuickJS'
➜ Explicit dependency on target 'RenderCore' in project 'RenderCore'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'VectorLayoutExpression' in project 'VectorLayoutExpression'
➜ Explicit dependency on target 'VectorLayoutReactivity' in project 'VectorLayoutReactivity'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
➜ Explicit dependency on target 'libpag' in project 'libpag'
Target 'VectorLayoutReactivity' in project 'VectorLayoutReactivity' (no dependencies)
Target 'VectorLayoutExpression' in project 'VectorLayoutExpression' (no dependencies)
Target 'QuickJS' in project 'QuickJS' (no dependencies)
Target 'ComponentKit' in project 'ComponentKit'
➜ Explicit dependency on target 'RenderCore' in project 'RenderCore'
➜ Explicit dependency on target 'Yoga' in project 'Yoga'
Target 'Yoga' in project 'Yoga' (no dependencies)
Target 'RenderCore' in project 'RenderCore' (no dependencies)
Target 'QMExtraOrdinaryMap-QMExtraOrdinaryMapResources' in project 'QMExtraOrdinaryMap' (no dependencies)
Target 'MapBizOCMiddleware' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'MapBizOCMiddleware-MapBizOCMiddlewareRes' in project 'MapBizOCMiddleware'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapFoundation' in project 'QMapFoundation'
Target 'QMapFoundation' in project 'QMapFoundation'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'Beacon' in project 'Beacon'
➜ Explicit dependency on target 'CocoaLumberjack' in project 'CocoaLumberjack'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
➜ Explicit dependency on target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'FMDB' in project 'FMDB'
➜ Explicit dependency on target 'GZIP' in project 'GZIP'
➜ Explicit dependency on target 'KVOController' in project 'KVOController'
➜ Explicit dependency on target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'mars' in project 'mars'
➜ Explicit dependency on target 'MJExtension' in project 'MJExtension'
➜ Explicit dependency on target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'QMEncrypt' in project 'QMEncrypt'
➜ Explicit dependency on target 'RUM' in project 'RUM'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
➜ Explicit dependency on target 'TAM_IOS_SDK' in project 'TAM_IOS_SDK'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
Target 'TAM_IOS_SDK' in project 'TAM_IOS_SDK' (no dependencies)
Target 'RUM' in project 'RUM'
➜ Explicit dependency on target 'RUM-QAPM_cer' in project 'RUM'
Target 'RUM-QAPM_cer' in project 'RUM' (no dependencies)
Target 'QMEncrypt' in project 'QMEncrypt' (no dependencies)
Target 'QMapUIKit' in project 'QMapUIKit'
➜ Explicit dependency on target 'libpag' in project 'libpag'
➜ Explicit dependency on target 'lottie-ios' in project 'lottie-ios'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
Target 'QMapWidgets-iOS12.0' in project 'QMapWidgets'
➜ Explicit dependency on target 'Masonry-iOS12.0' in project 'Masonry'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
Target 'Masonry-iOS12.0' in project 'Masonry' (no dependencies)
Target 'lottie-ios' in project 'lottie-ios' (no dependencies)
Target 'libpag' in project 'libpag' (no dependencies)
Target 'QMapProto-iOS12.0' in project 'QMapProto'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
Target 'QMapJCE' in project 'QMapJCE'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
Target 'QMapDarkMode' in project 'QMapDarkMode'
➜ Explicit dependency on target 'ApolloSDK' in project 'ApolloSDK'
Target 'QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QCloudCOSXML-QCloudCOSXML' in project 'QCloudCOSXML'
➜ Explicit dependency on target 'QCloudCore' in project 'QCloudCore'
Target 'QCloudCore' in project 'QCloudCore' (no dependencies)
Target 'QCloudCOSXML-QCloudCOSXML' in project 'QCloudCOSXML' (no dependencies)
Target 'MQTTClient' in project 'MQTTClient'
➜ Explicit dependency on target 'SocketRocket' in project 'SocketRocket'
Target 'SocketRocket' in project 'SocketRocket' (no dependencies)
Target 'MJExtension' in project 'MJExtension' (no dependencies)
Target 'mars' in project 'mars' (no dependencies)
Target 'KVOController' in project 'KVOController' (no dependencies)
Target 'GZIP' in project 'GZIP' (no dependencies)
Target 'DKProtocolsPool' in project 'DKProtocolsPool'
➜ Explicit dependency on target 'DKMobile' in project 'DKMobile'
Target 'DKMobile' in project 'DKMobile' (no dependencies)
Target 'CocoaLumberjack' in project 'CocoaLumberjack' (no dependencies)
Target 'Beacon' in project 'Beacon'
➜ Explicit dependency on target 'QimeiSDK' in project 'QimeiSDK'
Target 'QimeiSDK' in project 'QimeiSDK' (no dependencies)
Target 'QMapBuildTools-iOS12.0' in project 'QMapBuildTools'
➜ Explicit dependency on target 'QMapBasics-iOS12.0' in project 'QMapBasics'
Target 'QMapBasics-iOS12.0' in project 'QMapBasics' (no dependencies)
Target 'MapBizOCMiddleware-MapBizOCMiddlewareRes' in project 'MapBizOCMiddleware' (no dependencies)
Target 'MapBaseOCModel' in project 'MapBaseOCModel'
➜ Explicit dependency on target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'YYModel' in project 'YYModel'
Target 'YYModel' in project 'YYModel' (no dependencies)
Target 'PanguIosShell' in project 'PanguIosShell'
➜ Explicit dependency on target 'AgileMap' in project 'AgileMap'
➜ Explicit dependency on target 'DragonMapKitC2OC' in project 'DragonMapKitC2OC'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'GLMapLibBusiness' in project 'GLMapLibBusiness'
➜ Explicit dependency on target 'GLMapLibCore' in project 'GLMapLibCore'
➜ Explicit dependency on target 'GLMapLibThirdParty' in project 'GLMapLibThirdParty'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'MapBizNew' in project 'MapBizNew'
➜ Explicit dependency on target 'MapReflux' in project 'MapReflux'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'OlRoute' in project 'OlRoute'
➜ Explicit dependency on target 'PLog' in project 'PLog'
➜ Explicit dependency on target 'PoiEngine' in project 'PoiEngine'
➜ Explicit dependency on target 'PosEngine' in project 'PosEngine'
➜ Explicit dependency on target 'RouteGuidance' in project 'RouteGuidance'
➜ Explicit dependency on target 'TencentLBS' in project 'TencentLBS'
➜ Explicit dependency on target 'TXMapView' in project 'TXMapView'
Target 'TencentLBS' in project 'TencentLBS'
➜ Explicit dependency on target 'CrBase' in project 'CrBase'
➜ Explicit dependency on target 'lib-CoordTransfer' in project 'lib-CoordTransfer'
➜ Explicit dependency on target 'lib-TencentLBSZip' in project 'lib-TencentLBSZip'
➜ Explicit dependency on target 'lib-TLBSiOSAr' in project 'lib-TLBSiOSAr'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'OlRoute' in project 'OlRoute'
➜ Explicit dependency on target 'PosEngine' in project 'PosEngine'
Target 'lib-TLBSiOSAr' in project 'lib-TLBSiOSAr' (no dependencies)
Target 'lib-TencentLBSZip' in project 'lib-TencentLBSZip' (no dependencies)
Target 'lib-CoordTransfer' in project 'lib-CoordTransfer' (no dependencies)
Target 'CrBase' in project 'CrBase' (no dependencies)
Target 'RouteGuidance' in project 'RouteGuidance'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'PosEngine' in project 'PosEngine'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
Target 'PoiEngine' in project 'PoiEngine' (no dependencies)
Target 'OlRoute' in project 'OlRoute' (no dependencies)
Target 'MapReflux' in project 'MapReflux'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'MapBizNew' in project 'MapBizNew'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'GLMapLibThirdParty' in project 'GLMapLibThirdParty' (no dependencies)
Target 'GLMapLibCore' in project 'GLMapLibCore' (no dependencies)
Target 'GLMapLibBusiness' in project 'GLMapLibBusiness' (no dependencies)
Target 'DragonMapKitC2OC' in project 'DragonMapKitC2OC'
➜ Explicit dependency on target 'TXMapView' in project 'TXMapView'
Target 'TXMapView' in project 'TXMapView'
➜ Explicit dependency on target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'PLog' in project 'PLog'
➜ Explicit dependency on target 'QMWUP-iOS12.0' in project 'QMWUP'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'SDWebImageWebPCoder' in project 'SDWebImageWebPCoder'
➜ Explicit dependency on target 'SSZipArchive' in project 'SSZipArchive'
Target 'SSZipArchive' in project 'SSZipArchive' (no dependencies)
Target 'SDWebImageWebPCoder' in project 'SDWebImageWebPCoder'
➜ Explicit dependency on target 'SDWebImage-iOS12.0' in project 'SDWebImage'
➜ Explicit dependency on target 'libwebp' in project 'libwebp'
Target 'libwebp' in project 'libwebp' (no dependencies)
Target 'SDWebImage-iOS12.0' in project 'SDWebImage' (no dependencies)
Target 'QMWUP-iOS12.0' in project 'QMWUP' (no dependencies)
Target 'GLMapLib' in project 'GLMapLib'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'NerdApi' in project 'NerdApi'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'AgileMap' in project 'AgileMap'
➜ Explicit dependency on target 'MapBaseNew' in project 'MapBaseNew'
➜ Explicit dependency on target 'PLog' in project 'PLog'
Target 'PLog' in project 'PLog' (no dependencies)
Target 'MapBaseNew' in project 'MapBaseNew' (no dependencies)
Target 'GLMapLibHeaders' in project 'GLMapLibHeaders' (no dependencies)
Target 'FMDB' in project 'FMDB' (no dependencies)
Target 'EDSunriseSet' in project 'EDSunriseSet' (no dependencies)
Target 'DragonMapKit-QQMapRes' in project 'DragonMapKit' (no dependencies)
Target 'DragonMapKit-DragonMapKitBundle' in project 'DragonMapKit' (no dependencies)
Target 'CocoaAsyncSocket' in project 'CocoaAsyncSocket' (no dependencies)
Target 'ApolloSDK' in project 'ApolloSDK'
➜ Explicit dependency on target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'Protobuf' in project 'Protobuf'
Target 'Protobuf' in project 'Protobuf' (no dependencies)
Target 'MMKV' in project 'MMKV'
➜ Explicit dependency on target 'MMKVCore' in project 'MMKVCore'
Target 'MMKVCore' in project 'MMKVCore' (no dependencies)