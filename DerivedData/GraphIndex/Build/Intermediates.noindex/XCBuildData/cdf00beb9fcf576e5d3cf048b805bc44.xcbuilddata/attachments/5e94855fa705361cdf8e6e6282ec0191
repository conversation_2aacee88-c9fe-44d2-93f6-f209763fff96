{"": {"diagnostics": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge-iOS12.0-master.dia", "emit-module-dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge-iOS12.0-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge-iOS12.0-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge-iOS12.0-master.swiftdeps"}, "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapSwiftBridge/QMapSwiftBridge/Classes/QMapNaviLiveActivity.swift": {"const-values": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.swiftconstvalues", "dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.d", "diagnostics": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.dia", "index-unit-output-path": "/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.o", "llvm-bc": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.bc", "object": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.o", "swift-dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity.swiftdeps", "swiftmodule": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapNaviLiveActivity~partial.swiftmodule"}, "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapSwiftBridge/QMapSwiftBridge/Classes/QMapSwiftBridge.swift": {"const-values": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.swiftconstvalues", "dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.d", "diagnostics": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.dia", "index-unit-output-path": "/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.o", "llvm-bc": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.bc", "object": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.o", "swift-dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge.swiftdeps", "swiftmodule": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapSwiftBridge~partial.swiftmodule"}, "/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapSwiftBridge/QMapSwiftBridge/Classes/QMapWalkCycleNaviLiveActivity.swift": {"const-values": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.swiftconstvalues", "dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.d", "diagnostics": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.dia", "index-unit-output-path": "/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.o", "llvm-bc": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.bc", "object": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.o", "swift-dependencies": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity.swiftdeps", "swiftmodule": "/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMapSwiftBridge.build/Debug-iphonesimulator/QMapSwiftBridge-iOS12.0.build/Objects-normal/x86_64/QMapWalkCycleNaviLiveActivity~partial.swiftmodule"}}