-target x86_64-apple-ios12.0-simulator '-std=gnu++14' '-stdlib=libc++' -fobjc-arc -fobjc-weak -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' -D_LIBCPP_ENABLE_REMOVED_ALLOCATOR_CONST '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/ComponentKit-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/ComponentKit-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/ComponentKit-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit-385c566d2a037b14b1e393a742eb2edf-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/ComponentKit-project-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/ComponentKit/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ComponentKit -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/RenderCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Yoga -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/ComponentKit.build/Debug-iphonesimulator/ComponentKit.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/ComponentKit