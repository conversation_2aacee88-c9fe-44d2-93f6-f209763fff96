-target x86_64-apple-ios12.0-simulator '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/QMEncrypt-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/QMEncrypt-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/QMEncrypt-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt-17b500bea9f8d839f448425d75afca7c-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/QMEncrypt-project-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/QMEncrypt/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMEncrypt -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMEncrypt.build/Debug-iphonesimulator/QMEncrypt.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/QMEncrypt