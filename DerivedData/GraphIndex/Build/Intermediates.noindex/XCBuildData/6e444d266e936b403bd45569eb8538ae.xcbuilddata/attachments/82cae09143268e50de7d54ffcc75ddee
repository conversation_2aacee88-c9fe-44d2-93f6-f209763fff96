#!/bin/sh
      echo "PODS_TARGET_SRCROOT: ${PODS_TARGET_SRCROOT}"
      echo "PODS_ROOT: ${PODS_ROOT}"

      # Define paths
      if [ -d "${PODS_TARGET_SRCROOT}/AISDK" ]; then
        echo 'Seems currently AISDK is development pod, change source to the source root'
        SOURCE_DIR="${PODS_TARGET_SRCROOT}/AISDK"
      else
        echo 'Seems currently AISDK is available under Pods/, using them directly'
        SOURCE_DIR="${PODS_ROOT}/AISDK"
      fi
      DEST_DIR="${CONFIGURATION_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}"

      echo "Copying frameworks from: ${SOURCE_DIR}"
      echo "Destination: ${DEST_DIR}"

      # Define source paths
      AI_SDK="${SOURCE_DIR}/AISDK.framework"

      # Use otool to list dependencies
      echo "Analyzing dependencies of AISDK using otool..."
      DEPENDENCIES=$(otool -L "$AI_SDK/AISDK" | awk 'NR>1 {print $1}' | grep -v '^/System/Library' | grep -v '^/usr/lib' | grep -v 'AISDK' | sort -u)

      echo "AISDK actual external dependencies are:"
      echo "$DEPENDENCIES"

      # Extract expected dependencies dynamically from framework names
      EXPECTED_DEPENDENCIES=(

      )

      echo "AISDK expected dependencies are:"
      for EXPECTED_DEP in "${EXPECTED_DEPENDENCIES[@]}"; do
        echo "    $EXPECTED_DEP"
      done

      # Validate dependencies
      for DEP in $DEPENDENCIES; do
        # Extract just the framework name from the full path
        FRAMEWORK_NAME=$(basename "$(dirname "$DEP")")
        echo "Checking current found dependency: ${FRAMEWORK_NAME}"
        if [[ ! " ${EXPECTED_DEPENDENCIES[@]} " =~ " ${FRAMEWORK_NAME} " ]]; then
          echo "Error: New dependency detected: $DEP, but you didn't add to the copy list" >&2
          echo "If you SEE this message, you should update the framework list," >&2
          echo "As well as check dynamic loading code to properly handle the new framework if necessary" >&2
          exit 1
        fi
      done
      echo "All dependencies are valid."

      # Check and copy each framework
      for SDK_PATH in "$AI_SDK"; do
        if [ ! -e "$SDK_PATH" ]; then
          echo "Error: Framework not found at path: $SDK_PATH" >&2
          exit 1
        fi
        cp -f -R "$SDK_PATH" "${DEST_DIR}"
        if [ $? -ne 0 ]; then
          echo "Error: Failed to copy framework: $SDK_PATH" >&2
          exit 1
        fi
      done

