-target x86_64-apple-ios12.0-simulator '-std=gnu11' -fobjc-arc -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DragonMapKitC2OC-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DragonMapKitC2OC-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DragonMapKitC2OC-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC-2ef015221f2f36037f3d49cf871f6ef5-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DragonMapKitC2OC-project-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/DragonMapKitC2OC/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/DragonMapKitC2OC.build/Debug-iphonesimulator/DragonMapKitC2OC.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/DragonMapKitC2OC -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/NerdApi -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PLog -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TXMapView '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap'