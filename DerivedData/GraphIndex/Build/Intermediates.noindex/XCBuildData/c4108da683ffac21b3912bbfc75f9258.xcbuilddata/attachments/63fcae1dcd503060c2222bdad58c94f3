#!/bin/sh
      # Define paths
      if [ -d "${PODS_TARGET_SRCROOT}/WeAppSDK" ]; then
        echo 'Seems currently WeAppSDK is development pod, change source to the source root'
        SOURCE_DIR="${PODS_TARGET_SRCROOT}/WeAppSDK"
      else
        echo 'Seems currently WeAppSDK is available under Pods/, using them directly'
        SOURCE_DIR="${PODS_ROOT}/WeAppSDK/WeAppSDK"
      fi
      DEST_DIR="${CONFIGURATION_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}"

      echo "Copying frameworks from: ${SOURCE_DIR}"
      echo "Destination: ${DEST_DIR}"

      # Define source paths
      WEAPP_CORE_SDK="${SOURCE_DIR}/WeAppCoreSDK.framework"
      ILINK_SDK="${SOURCE_DIR}/OtherShareFrameworks/ilink.framework" 
      SOUND_TOUCH_SDK="${SOURCE_DIR}/OtherShareFrameworks/SoundTouch.framework"

      # Use otool to list dependencies
      echo "Analyzing dependencies of WeAppCoreSDK using otool..."
      DEPENDENCIES=$(otool -L "$WEAPP_CORE_SDK/WeAppCoreSDK" | awk 'NR>1 {print $1}' | grep -v '^/System/Library' | grep -v '^/usr/lib' | grep -v 'WeAppCoreSDK' | sort -u)

      echo "WeAppSDK actual external dependencies are:"
      echo "$DEPENDENCIES"

      # Extract expected dependencies dynamically from framework names
      EXPECTED_DEPENDENCIES=(
        "$(basename "$ILINK_SDK")"
        "$(basename "$SOUND_TOUCH_SDK")"
      )

      echo "WeAppSDK expected dependencies are:"
      for EXPECTED_DEP in "${EXPECTED_DEPENDENCIES[@]}"; do
        echo "    $EXPECTED_DEP"
      done

      # Validate dependencies
      for DEP in $DEPENDENCIES; do
        # Extract just the framework name from the full path
        FRAMEWORK_NAME=$(basename "$(dirname "$DEP")")
        echo "Checking current found dependency: ${FRAMEWORK_NAME}"
        if [[ ! " ${EXPECTED_DEPENDENCIES[@]} " =~ " ${FRAMEWORK_NAME} " ]]; then
          echo "Error: New dependency detected: $DEP, but you didn't add to the copy list" >&2
          echo "If you SEE this message, you should update the framework list," >&2
          echo "As well as check dynamic loading code to properly handle the new framework if necessary" >&2
          exit 1
        fi
      done
      echo "All dependencies are valid."

      # Check and copy each framework
      for SDK_PATH in "$WEAPP_CORE_SDK" "$ILINK_SDK" "$SOUND_TOUCH_SDK"; do
        if [ ! -e "$SDK_PATH" ]; then
          echo "Error: Framework not found at path: $SDK_PATH" >&2
          exit 1
        fi
        cp -f -R "$SDK_PATH" "${DEST_DIR}"
        if [ $? -ne 0 ]; then
          echo "Error: Failed to copy framework: $SDK_PATH" >&2
          exit 1
        fi
      done

