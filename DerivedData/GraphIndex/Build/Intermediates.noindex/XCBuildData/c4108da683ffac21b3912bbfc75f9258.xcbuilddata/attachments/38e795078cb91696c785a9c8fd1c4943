#!/bin/sh
      echo "PODS_TARGET_SRCROOT: ${PODS_TARGET_SRCROOT}"
      echo "PODS_ROOT: ${PODS_ROOT}"

      # Define paths
      if [ -d "${PODS_TARGET_SRCROOT}/QMapARNaviKit" ]; then
        echo 'Seems currently QMapARNaviKit is development pod, change source to the source root'
        SOURCE_DIR="${PODS_TARGET_SRCROOT}/QMapARNaviKit"
      else
        echo 'Seems currently QMapARNaviKit is available under Pods/, using them directly'
        SOURCE_DIR="${PODS_ROOT}/QMapARNaviKit"
      fi
      DEST_DIR="${CONFIGURATION_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}"

      echo "Copying frameworks from: ${SOURCE_DIR}"
      echo "Destination: ${DEST_DIR}"

      # Define source paths
      AR_SDK="${SOURCE_DIR}/QMapARNaviKit.framework"

      # Use otool to list dependencies
      echo "Analyzing dependencies of AR_SDK using otool..."
      DEPENDENCIES=$(otool -L "$AR_SDK/QMapARNaviKit" | awk 'NR>1 {print $1}' | grep -v '^/System/Library' | grep -v '^/usr/lib' | grep -v 'QMapARNaviKit' | sort -u)

      echo "QMapARNaviKit actual external dependencies are:"
      echo "$DEPENDENCIES"

      # Extract expected dependencies dynamically from framework names
      EXPECTED_DEPENDENCIES=(

      )

      echo "QMapARNaviKit expected dependencies are:"
      for EXPECTED_DEP in "${EXPECTED_DEPENDENCIES[@]}"; do
        echo "    $EXPECTED_DEP"
      done

      # Validate dependencies
      for DEP in $DEPENDENCIES; do
        # Extract just the framework name from the full path
        FRAMEWORK_NAME=$(basename "$(dirname "$DEP")")
        echo "Checking current found dependency: ${FRAMEWORK_NAME}"
        if [[ ! " ${EXPECTED_DEPENDENCIES[@]} " =~ " ${FRAMEWORK_NAME} " ]]; then
          echo "Error: New dependency detected: $DEP, but you didn't add to the copy list" >&2
          echo "If you SEE this message, you should update the framework list," >&2
          echo "As well as check dynamic loading code to properly handle the new framework if necessary" >&2
          exit 1
        fi
      done
      echo "All dependencies are valid."

      # Check and copy each framework
      for SDK_PATH in "$AR_SDK"; do
        if [ ! -e "$SDK_PATH" ]; then
          echo "Error: Framework not found at path: $SDK_PATH" >&2
          exit 1
        fi
        cp -f -R "$SDK_PATH" "${DEST_DIR}"
        if [ $? -ne 0 ]; then
          echo "Error: Failed to copy framework: $SDK_PATH" >&2
          exit 1
        fi
      done

