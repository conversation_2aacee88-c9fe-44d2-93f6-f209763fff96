#!/bin/sh
      # Define paths
      if [ -d "${PODS_TARGET_SRCROOT}/LarkLite" ]; then
        echo 'Seems currently LarkLite is development pod, change source to the source root'
        SOURCE_DIR="${PODS_TARGET_SRCROOT}"
      else
        echo 'Seems currently LarkLite is available under Pods/, using them directly'
        SOURCE_DIR="${PODS_ROOT}/LarkLite"
      fi
      DEST_DIR="${CONFIGURATION_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}"

      echo "Copying frameworks from: ${SOURCE_DIR}"
      echo "Destination: ${DEST_DIR}"

      # Define source paths
      LARKLITE_CORE_SDK="${SOURCE_DIR}/LarkLite.framework"

      # Use otool to list dependencies
      echo "Analyzing dependencies of LARKLITE_CORE_SDK using otool..."
      DEPENDENCIES=$(otool -L "$LARKLITE_CORE_SDK/LarkLite" | awk 'NR>1 {print $1}' | grep -v '^/System/Library' | grep -v '^/usr/lib' | grep -v 'LarkLite' | sort -u)

      echo "LarkLite actual external dependencies are:"
      echo "$DEPENDENCIES"

      # Extract expected dependencies dynamically from framework names
      EXPECTED_DEPENDENCIES=(
      )

      echo "LarkLite expected dependencies are:"
      for EXPECTED_DEP in "${EXPECTED_DEPENDENCIES[@]}"; do
        echo "    $EXPECTED_DEP"
      done

      # Validate dependencies
      for DEP in $DEPENDENCIES; do
        # Extract just the framework name from the full path
        FRAMEWORK_NAME=$(basename "$(dirname "$DEP")")
        echo "Checking current found dependency: ${FRAMEWORK_NAME}"
        if [[ ! " ${EXPECTED_DEPENDENCIES[@]} " =~ " ${FRAMEWORK_NAME} " ]]; then
          echo "Error: New dependency detected: $DEP, but you didn't add to the copy list" >&2
          echo "If you SEE this message, you should update the framework list," >&2
          echo "As well as check dynamic loading code to properly handle the new framework if necessary" >&2
          exit 1
        fi
      done
      echo "All dependencies are valid."

      # Check and copy each framework
      for SDK_PATH in "$LARKLITE_CORE_SDK"; do
        if [ ! -e "$SDK_PATH" ]; then
          echo "Error: Framework not found at path: $SDK_PATH" >&2
          exit 1
        fi
        cp -f -R "$SDK_PATH" "${DEST_DIR}"
        if [ $? -ne 0 ]; then
          echo "Error: Failed to copy framework: $SDK_PATH" >&2
          exit 1
        fi
      done

