-target x86_64-apple-ios12.0-simulator '-std=gnu11' -fobjc-arc -fobjc-weak -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DSD_WEBP=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/SDWebImageWebPCoder-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/SDWebImageWebPCoder-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/SDWebImageWebPCoder-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder-b7467f86279ff92c5097add6a7602304-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/SDWebImageWebPCoder-project-headers.hmap -iquote /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libwebp/src -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/SDWebImageWebPCoder/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/SDWebImageWebPCoder.build/Debug-iphonesimulator/SDWebImageWebPCoder.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/SDWebImageWebPCoder