-target x86_64-apple-ios12.0-simulator '-std=gnu11' -fobjc-arc -fobjc-weak -fmodules '-fmodules-cache-path=/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/ModuleCache.noindex' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DGLES_SILENCE_DEPRECATION=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.1.sdk -fasm-blocks -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/QMSpiderManWrapper-generated-files.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/QMSpiderManWrapper-own-target-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/QMSpiderManWrapper-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper-d5bc69eabfc0fc7481ef65df1062e24f-VFS-iphonesimulator/all-product-headers.yaml -iquote /Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/QMSpiderManWrapper-project-headers.hmap -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/QMSpiderManWrapper/include -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Private/QMSpiderManWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/ApolloSDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/CocoaLumberjack -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKMobile -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DKProtocolsPool -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/DragonMapKitC2OC -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/FMDB -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/GZIP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/KVOController -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MJExtension -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKV -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MMKVCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MQTTClient -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/MapBaseOCModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Masonry -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/Protobuf -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCOSXML -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QCloudCore -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMEncrypt -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMSpiderManWrapper -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMWUP -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBasics -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapBuildTools -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapDarkMode -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapFoundation -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapJCE -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapProto -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/QMapWidgets -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImage -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SDWebImageWebPCoder -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SocketRocket -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/TAM_IOS_SDK -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/YYModel -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TLBSiOSAr -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lib-TencentLBSZip -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/libwebp -I/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/lottie-ios -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapFoundation/Headers -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QMapUIKit/Headers -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/DerivedSources-normal/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/DerivedSources/x86_64 -I/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Intermediates.noindex/QMSpiderManWrapper.build/Debug-iphonesimulator/QMSpiderManWrapper.build/DerivedSources -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/QMSpiderManWrapper -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/AgileMap -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Beacon/beacon-frameworks/BeaconAPI_Base -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/CrBase -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLib -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibCore -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBaseNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapBizNew -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/MapReflux -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/NerdApi -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/OlRoute -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PLog -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PanguIosShell -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PoiEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/PosEngine -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMSpiderMan -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapFoundation/QMapFoundation -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QMapUIKit/QMapUIKit -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/QimeiSDK -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RUM -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/RouteGuidance -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TXMapView -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/TencentLBS -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/libpag/framework -F/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/mars/mars -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/AgileMap -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/CrBase -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLib -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLibBusiness -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLibCore -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/GLMapLibThirdParty -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBaseNew -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapBizNew -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/MapReflux -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/NerdApi -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/OlRoute -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PLog -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PanguIosShell -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PoiEngine -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/PosEngine -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/QimeiSDK -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/RouteGuidance -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TXMapView -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/TencentLBS -F/Users/<USER>/GRAG_iOS/DerivedData/GraphIndex/Build/Products/Debug-iphonesimulator/XCFrameworkIntermediates/libpag '-fmodule-map-file=/Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Headers/Public/SSZipArchive/SSZipArchive.modulemap'