//
//  QMWKAlertViewController.h
//
//  Created by p<PERSON><PERSON><PERSON> on 2016/12/5.
//  Copyright © 2016年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>


typedef void (^WKAlertCompletionHandle)(void);
typedef void (^WKConfirmCompletionHandleBlock)(BOOL result);
typedef void (^WKTextInputCompletionHandleBlock)(NSString * result);

@interface QMWKAlertViewController : QMAlertController

- (void)createContentWithWKAlertCompletionHandle:(WKAlertCompletionHandle)completionHandler;
- (void)createContentWithWKConfirmPanelCompletionHandle:(WKConfirmCompletionHandleBlock)completionHandler;
- (void)createContentWithWKTextInputPanelCompletionHandle:(WKTextInputCompletionHandleBlock)completionHandler defaultText:(NSString *)defaultText;

@end
