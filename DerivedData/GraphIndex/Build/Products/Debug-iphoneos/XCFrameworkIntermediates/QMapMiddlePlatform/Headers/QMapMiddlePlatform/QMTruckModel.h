//
//  QMTruckModel.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/29.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <QMapFoundation/QMModel.h>
#import "QMTruckPlateModel.h"

extern NSString * _Nonnull const QMTruckModelCacheKey;

NS_ASSUME_NONNULL_BEGIN

@interface QMTruckModel : QMModel <YYModel>

+ (BOOL)isSameModelOne:(QMTruckModel *)modelOne modelTwo:(QMTruckModel *)modelTwo;

+ (QMTruckModel *)createTruckModuleWithTruckPlate:(QMTruckPlateModel *)truckPlateModel;


@property (nonatomic, assign) QMTruckType type;

/// 长
@property (nonatomic, assign) double length;
/// 宽
@property (nonatomic, assign) double width;
/// 高
@property (nonatomic, assign) double height;
/// 核定载重
@property (nonatomic, assign) double load;
/// 车辆总重
@property (nonatomic, assign) double totalLoad;
/// 总重区间最小值
@property (nonatomic) double totalLoadMinValue;
/// 总重区间最大值
@property (nonatomic) double totalLoadMaxValue;
/// 载重区间最小值
@property (nonatomic) double loadMinValue;
/// 载重区间最大值
@property (nonatomic) double loadMaxValue;
/// 长度区间最小值
@property (nonatomic) double lengthMinValue;
/// 长度区间最大值
@property (nonatomic) double lengthMaxValue;
/// 宽度区间最小值
@property (nonatomic) double widthMinValue;
/// 宽度区间最大值
@property (nonatomic) double widthMaxValue;
/// 高度区间最小值
@property (nonatomic) double heightMinValue;
/// 高度区间最大值
@property (nonatomic) double heightMaxValue;

- (void)updateTruckPlateModelValue:(QMTruckPlateModel *)truckPlateModel;



@end

NS_ASSUME_NONNULL_END
