//
//  QMapAppBridgeManager.h
//  QMapMiddlePlatform
//
//  Created by admin on 2020/10/30.
//  Copyright © 2020 Tencent. All rights reserved.
//
 
#import <Foundation/Foundation.h>
#import <QMapFoundation/QMapFoundationDefine.h>

NS_ASSUME_NONNULL_BEGIN

#define QMapAppBridegEntity(Protocol) \
({\
    id<Protocol> entity = [[QMapAppBridgeManager sharedInstance] appBridgeEntity:@protocol(Protocol)];\
    entity;\
})


#define QMapAppBridegEntityClass(Protocol) \
({\
    Class entityClass = [[QMapAppBridgeManager sharedInstance] appBridgeEntityClass:@protocol(Protocol)];\
    entityClass;\
})

@interface QMapAppBridgeManager : NSObject
QMSharedInstance_H(QMapAppBridgeManager);

- (BOOL)registerAppBridgeEntityClass:(Class)entityClass forProtocol:(Protocol *)protocol;

- (id)appBridgeEntity:(Protocol *)protocol;

- (Class)appBridgeEntityClass:(Protocol *)protocol;

@end

NS_ASSUME_NONNULL_END
