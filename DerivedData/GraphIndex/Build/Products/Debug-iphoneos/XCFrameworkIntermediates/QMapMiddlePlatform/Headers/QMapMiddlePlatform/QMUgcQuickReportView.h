//
//  QMUgcQuickReportView.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/8.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMUgcReportProtocol.h"
#import "QMUgcReportUIDelegate.h"
typedef NS_ENUM(NSInteger, QMAutoDismissViewType) {
    QMAutoDismissViewTypeUgcReport = 1,
    QMAutoDismissViewTypeCheckEvent = 2,
    QMAutoDismissViewTypeMQTTEvent = 3
};
@protocol QMUgcReportUIDelegate;
@interface QMUgcQuickReportView : UIView

@property(nonatomic,weak) id<QMUgcReportUIDelegate> delegate;
@property(nonatomic,strong) QMUgcReportEventReq *reportRequest;
@property(nonatomic,strong) NSString *eventId;
@property (nonatomic) UIEdgeInsets contentEdgeInsets;
@property (nonatomic) BOOL isCarNavi;
//场景.0:未定义，1:行前，2:行中&etc
@property(nonatomic, assign) int infoScene;
- (void)showInView:(UIView *)view;
-(void)setNightMode:(BOOL)isNight;
-(void)setEventId:(NSString *)eventId;
- (void)setDataForMQTT:(NSDictionary *)dataForMQTT;
- (void)dismissViewNoAnimation;
- (void)updateSuccessTipsText:(NSString *)text;
- (instancetype)initWithType:(QMAutoDismissViewType)popBottomViewType withDataForMQTT:(NSDictionary *)dataForMQTT;
-(instancetype)initWithType:(QMAutoDismissViewType)popBottomViewType;
@end
