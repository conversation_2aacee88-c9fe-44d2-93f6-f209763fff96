//
//  QMLanguagePackageDataService.h
//  QMapMiddlePlatform
//
//  Created by 江航 on 2021/7/20.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
//语音广场数据请求
@interface QMVoiceSquareListDataService : NSObject
QMDefaultManager_H(QMVoiceSquareListDataService);
//语音广场列表数据请求
+ (void)requestVoiceSquareListDataWithParams:(NSDictionary *)params
                                     success:(nullable QMNetworkJSONRequestSuccessBlock)successBlock
                                     failure:(nullable QMNetworkRequestFailureBlock)failureBlock;

//导航设置语音推荐数据请求
+ (void)requestNaviRecommendInfoWithParams:(NSDictionary *)params
                                   success:(nullable QMNetworkJSONRequestSuccessBlock)successBlock
                                   failure:(nullable QMNetworkRequestFailureBlock)failureBlock;

//语音广场录制列表数据请求
+ (void)requestVoiceSquareRecordListDataWithParams:(NSDictionary *)params
                                           success:(nullable QMNetworkJSONRequestSuccessBlock)successBlock
                                           failure:(nullable QMNetworkRequestFailureBlock)failureBlock;


//语音广场录制数据请求
+ (void)requestVoiceSquareRecordDataWithParams:(NSDictionary *)params
                                       success:(nullable QMNetworkJSONRequestSuccessBlock)successBlock
                                       failure:(nullable QMNetworkRequestFailureBlock)failureBlock;
 
//语音广场录制删除数据请求
+ (void)requestVoiceSquareDeleteRecordDataWithParams:(NSDictionary *)params
                                            success:(nullable QMNetworkJSONRequestSuccessBlock)successBlock
                                             failure:(nullable QMNetworkRequestFailureBlock)failureBlock;
    
@end

NS_ASSUME_NONNULL_END
