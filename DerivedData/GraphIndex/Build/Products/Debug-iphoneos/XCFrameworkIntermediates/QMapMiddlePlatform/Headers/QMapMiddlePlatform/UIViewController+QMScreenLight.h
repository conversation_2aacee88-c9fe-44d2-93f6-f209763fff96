//
//  UIViewController+QMScreenLight.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/3/6.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 屏幕常亮抽象模块
 使用方式: 在 VC 中复写如下方法即可在当前页面实现屏幕常亮
 - (BOOL)enableScreenLight {
     return YES;
 }
 */
@interface UIViewController (QMScreenLight)

@end

NS_ASSUME_NONNULL_END
