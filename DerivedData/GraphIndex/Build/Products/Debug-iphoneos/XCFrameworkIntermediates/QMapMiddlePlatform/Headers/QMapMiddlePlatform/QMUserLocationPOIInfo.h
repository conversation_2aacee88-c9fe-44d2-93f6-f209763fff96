//
//  QMUserLocationPOIInfo.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/8/29.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import "JsonPOIInfo.h"

typedef enum
{
    DMUserLocationTypeMyLocationInMap,
    DMUserLocationTypeMyLocationInTaxi,
    DMUserLocationTypeTaxiPlayVoice,
    DMUserLocationTypeMyLocationInGlobalMap,
}DMUserLocationType;


NS_ASSUME_NONNULL_BEGIN

static NSString * const QMULCurrentLocation  = @"我的位置";

static NSString* const KQMUserLocationTaxiPlayVoice = @"重播录音";

#define QM_ADDRESS_SEARCHING @"正在检索..."

#define QMUserLocationDidChangeIconNotification @"QMUserLocationDidChangeIconNotification"
#define QMUserLocationDidUpdateReverseInfo @"QMUserLocationDidUpdateReverseInfo"

@interface QMUserLocationPOIInfo : JsonPOIInfo

@property (nonatomic, assign) DMMapPoint mapPoint;
@property (nonatomic, assign) BOOL isCopiedPOI;
@property (nonatomic, assign) BOOL needTTS;
@property (nonatomic, copy, nullable) NSString *nameForTTS;
@property (nonatomic, weak, readonly) DMMapView *mapView;

@property (nonatomic, assign) DMUserLocationType userLocationType;

- (instancetype)initWithMapView:(DMMapView *)mapView;

@end

NS_ASSUME_NONNULL_END
