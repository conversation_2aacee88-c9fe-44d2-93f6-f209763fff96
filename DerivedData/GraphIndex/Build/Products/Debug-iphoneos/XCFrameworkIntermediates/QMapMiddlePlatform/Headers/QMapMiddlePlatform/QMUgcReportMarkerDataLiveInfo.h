//
//  QMUgcReportMarkerDataLiveInfo.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/3/16.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapProto/QMJCE_tmap_EventMarkerInfo.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMUGCMediaType) {
    QMUGCMediaTypeNone = -1,
    QMUGCMediaTypeVideo,
    QMUGCMediaTypeLive,
};

/// 视频直播相关信息
@interface QMUgcReportMarkerDataLiveInfo : NSObject <NSCopying>

/// 初始化直播信息
/// @param data QMJCE_trafficmarker_markerInfo 对象，从 server 请求过来
- (instancetype)initWithData:(QMJCE_trafficmarker_MarkerInfo *)data;

/// 初始化直播信息
/// @param data QMJCE_tmap_EventMarkerInfo 对象，从 server 请求过来
- (instancetype)initWithExplainClickEventData:(QMJCE_tmap_EventMarkerInfo *)data;

/// 更新重定向后的 URL
/// @param proxyedURL 重定向后的 URL
- (void)updateProxyedURL:(NSString *)proxyedURL;

/// 直播/视频的来源
@property (nonatomic, copy  , readonly) NSString *urlSource;

/// 通过该 url，请求 server，换一个最终的可用的 url
/// 内部会根据优先级判断，
/// 如果有 liveUrl且需要重定向 则返回，
/// 如果有 videoUrl且需要重定向 则返回。
/// 否则返回 @"" 代表不需要重定向。
@property (nonatomic, copy  , readonly) NSString *urlShouldBeProxy;

/// 媒体类型，直播/视频
@property (nonatomic, assign, readonly) QMUGCMediaType mediaType;

/// 最终可用的 直播/视频 地址。
/// 如果有直播/视频且需要重定向，则改值返回重定向后的 url。
/// 如果不需要重定向，返回原始的 url。
/// 如果没有可用的 直播/视频 url，则返回 @""
@property (nonatomic, copy  , readonly) NSString *avaliableMediaURL;

/// 封面图
@property (nonatomic, copy  , readonly) NSArray *coverImageURLList;

@end

NS_ASSUME_NONNULL_END
