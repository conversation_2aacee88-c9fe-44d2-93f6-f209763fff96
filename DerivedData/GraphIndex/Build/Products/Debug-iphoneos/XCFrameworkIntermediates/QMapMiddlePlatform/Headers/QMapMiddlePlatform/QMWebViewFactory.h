//
//  QMWebViewFactory.h
//  TwoWebView
//
//  Created by paul<PERSON><PERSON> on 2019/5/22.
//  Copyright © 2019 paulineli. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "QMWebViewDefine.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWebViewFactory : NSObject

+ (QMWebView *)createDefaultWebView;
+ (QMWebView *)createWKWebViewWithConfiguration:(id _Nullable)configuration;

+ (void)setCustomUserAgent;

// 获取h5实例初始化结束时间
+ (CFTimeInterval)getH5EngineEndTime;
+ (CFTimeInterval)getH5EngineStartTime;
@end

NS_ASSUME_NONNULL_END
