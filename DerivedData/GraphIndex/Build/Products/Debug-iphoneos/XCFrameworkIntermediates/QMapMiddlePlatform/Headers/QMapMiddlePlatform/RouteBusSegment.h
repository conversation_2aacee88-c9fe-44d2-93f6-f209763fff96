//
//  FavoriteBusRouteSegment.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 4/22/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import "RouteSegment.h"
#import <Foundation/Foundation.h>

#import <QMapProto/QMJCE_text_RichInfo.h>

typedef enum BusSegmentType_ {
    BUSSEGMENTTYPE_WALK = 0,
    BUSSEGMENTTYPE_BUS,
    BUSSEGMENTTYPE_SUBWAY,
    BUSSEGMENTTYPE_FINISH,
    BUSSEGMENTTYPE_NONE,
    BUSSEGMENTTYPE_TRAIN, //火车线路
    BUSSEGMENTTYPE_DRIVE, //驾车换乘方案
    BUSSEGMENTTYPE_CAR,//打车线路
    BUSSEGMENTTYPE_CYCLE//骑行线路
} BusSegmentType;

typedef NS_ENUM(NSInteger, BusSegmentTrainType) {
    /// 默认类型
    BusSegmentTrainTypeDefault = 11,
    /// 高铁
    BusSegmentTrainTypeG,
};

@class QMBusETAItem, RoutePoint;
@interface RouteBusSegment : RouteSegment {

    BusSegmentType type_;
    BusSegmentTrainType trainType_;
    NSString *name_;
    NSString *direction_;
    NSString *on_;
    NSString *off_;
    NSInteger stopNum_;
    NSString *walkDirection_;
    NSInteger distance_;
    NSInteger time_;
    NSString *onExit_;
    NSString *offExit_;
    NSInteger isTransfer_;
    NSString *transferTips_;
    TipsType tipsType_; // 天桥和地下通道等提示
    NSInteger tipsStartNum_;
    NSInteger tipsdNumCount_;
    NSInteger legNum_;
    NSInteger dayNum_;
}

@property (nonatomic) BusSegmentType type;
@property (nonatomic) BusSegmentTrainType trainType;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *direction;
@property (nonatomic, copy) NSString *from;
@property (nonatomic, copy) NSString *to;
@property (nonatomic, copy) NSString *on;
@property (nonatomic, copy) NSString *off;
@property (nonatomic, copy) NSString *onUid;
@property (nonatomic, assign) NSInteger onCotype;
@property (nonatomic) NSInteger stopNum;
@property (nonatomic, copy) NSString *walkDirection;
@property (nonatomic) NSInteger roadLength;
@property (nonatomic) NSInteger time;
@property (nonatomic, copy) NSString *onExit;
@property (nonatomic, strong) RoutePoint *onExitPoint;
@property (nonatomic, copy) NSString *offExit;
@property (nonatomic, strong) RoutePoint *offExitPoint;
@property (nonatomic) NSInteger isTransfer;
@property (nonatomic, copy) NSString *transferTips;
@property (nonatomic, copy) NSString *merchantCode;

@property (nonatomic) TipsType tipsType;
@property (nonatomic) NSInteger tipsStartNum;

@property (nonatomic, strong) NSArray *detailSteps;
@property (nonatomic, strong) NSArray *stations;
@property (nonatomic, strong) NSArray *options;

@property (nonatomic, assign) NSInteger runningState;
@property (nonatomic, assign) NSInteger startTime;
@property (nonatomic, assign) NSInteger endTime;
@property (nonatomic, copy) NSString *startTimeStr;
@property (nonatomic, copy) NSString *endTimeStr;
@property (nonatomic, assign) NSInteger arriveTime;
@property (nonatomic, copy) NSString *uid;
@property (nonatomic, copy) NSString *color;
@property (nonatomic, assign) NSInteger legNum;
@property (nonatomic, assign) NSInteger dayNum;

/**
 实景图所在上车点
 */
@property (nonatomic, strong) QMJCE_MapRoute_GetOnOff * getOn;

//票价，>=0，票价真实有效，显示票价；-1，不显示票价
@property (nonatomic, assign) NSInteger price;

// 运营信息
@property (nonatomic, assign) NSInteger tag;
@property (nonatomic, copy) NSString *routedDesc;

@property (nonatomic, strong) QMJCE_text_RichInfo *rich;

// 实时信息
@property (nonatomic, strong) QMBusETAItem *etaItem;
@property (nonatomic, assign) BOOL hasRtBus;
//舒适度
@property (nonatomic, assign) BOOL crowdedness;
// Points
@property (nonatomic,   copy) NSArray<RoutePoint *> *points;

// 定制班车描述信息
@property (nonatomic, strong) NSString *regularBusDesc;
// 发车间隔时间
@property (nonatomic, assign) NSInteger headway;

@property (nonatomic, copy) NSString *busLineId;

@property (nonatomic, copy) NSString *intervalId;

@property (nonatomic, copy) NSString *briefName;

//公交混合算路协议更新字段

@property (nonatomic, strong) QMJCE_routesearch_Walk     *cycle;//骑行段
@property (nonatomic, strong) QMJCE_routesearch_CarRoute *car;  //打车段

@property (nonatomic, assign) BOOL isNeedChangeWalkCell;//是否需要修改换乘段步行的UI显示信息
@property (nonatomic) BOOL hasReported;

- (NSString *)optionsDescription;

- (NSString *)crossCityOptionsDescription;


@end
