//
//  QMUgcParticleModel.h
//  QMapMiddlePlatform
//
//  Created by <PERSON>hui<PERSON><PERSON> on 2023/9/1.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMUgcParticlePosition : NSObject

@property (nonatomic) NSInteger longitude;
@property (nonatomic) NSInteger latitude;

@end


@interface QMUgcParticleDetailTemplateModel : NSObject

@property (nonatomic) NSString* modelUrl;

@property (nonatomic) NSString* myModelUrl;

@property (nonatomic) NSString* classCode2D;

@property (nonatomic) NSString* classCode3D;

@property (nonatomic) NSInteger scaleX;

@property (nonatomic) NSInteger scaleY;

@property (nonatomic) NSInteger angle;

@property (nonatomic) NSInteger pitch;

@end


@interface QMUgcParticleDetailModel : NSObject

@property (nonatomic) NSString* templateType;

@property (nonatomic) NSString* templateId;

@property (nonatomic) QMUgcParticleDetailTemplateModel* model;

@end

@interface QMUgcParticleModel : NSObject

//烟花id
@property (nonatomic) NSString* biz_id;
//搜索关键词
@property (nonatomic) NSString* search_key;
//3D互动类型 1-烟花
@property (nonatomic) NSInteger type;
// 来源 1-用户 2-官方
@property (nonatomic) NSInteger source;
//
@property (nonatomic) NSString* poi_id;
//用户信息
@property (nonatomic) NSDictionary* user_info;
// 位置信息
@property (nonatomic) QMUgcParticlePosition* position;
//
@property (nonatomic) NSString* address;
//
@property (nonatomic) NSString* city_name;

// 创建时间
@property (nonatomic) NSInteger create_time;
// 失效时间
@property (nonatomic) NSInteger invalid_time;

// 浏览次数
@property (nonatomic) NSInteger view_times;
// 点赞次数
@property (nonatomic) NSInteger like_times;
//烟花详细信息
@property (nonatomic) NSString * detail;

// 来源 1-查看 2-点赞 3-创建
@property (nonatomic) NSInteger action;

@end

NS_ASSUME_NONNULL_END
