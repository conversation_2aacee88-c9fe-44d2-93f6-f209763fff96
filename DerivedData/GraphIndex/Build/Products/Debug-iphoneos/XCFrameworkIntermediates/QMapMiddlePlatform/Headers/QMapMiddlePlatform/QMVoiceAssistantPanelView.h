//
//  QMVoiceAssistantPanelView.h
//  SOSOMap
//
//  Created by foo<PERSON><PERSON><PERSON>(王中周) on 2019/11/27.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMVoiceAssistantPanelSubViewDelegate.h"

NS_ASSUME_NONNULL_BEGIN

extern CGFloat const QMVoiceAssistantPanelViewNormalHorizontalMagin;        // 语音面板默认水平边距
extern CGFloat const QMVoiceAssistantPanelViewNormalTopMagin;               // 语音面板顶部边距
extern CGFloat const QMVoiceAssistantPanelViewNormalHeight;                 // 语音面板默认高度

extern CGFloat const QMVoiceAssistantPanelViewSmallWidth;                   // 语音二轮小面板宽度
extern CGFloat const QMVoiceAssistantPanelViewSmallHeight;                  // 语音二轮小面板高度


@protocol QMVoiceAssistantPanelViewPrivateDelegate <NSObject>

/// 面板即将消失回调
- (void)private_willHideVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView;

/// 面板已经消失回调
- (void)private_didHideVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView;

@end


@interface QMVoiceAssistantPanelView : UIView

@property (nonatomic, strong, readonly) UIView<QMVoiceAssistantPanelSubViewDelegate> *subPanelView;
@property (nonatomic, readonly) QMVoiceAssistantPanelViewState state;
@property (nonatomic) BOOL isHidingWithAnimation;

/// 内部属性，外部业务勿用
@property (nonatomic, weak) id<QMVoiceAssistantPanelViewPrivateDelegate> privateDelegate;

/// 新面板出现，默认方式
- (void)showNormlPanelViewWithFrame:(CGRect)frame scene:(QMVoiceAssistantPanelViewScene)scene;
- (void)showSmallPanelViewWithFrame:(CGRect)frame scene:(QMVoiceAssistantPanelViewScene)scene;

- (void)showAIPanelViewWithFrame:(CGRect)frame
                           scene:(QMVoiceAssistantPanelViewScene)scene animation:(BOOL)animated;

/// 大小面板样式切换，默认方式
- (void)showNormalPanelViewFromSmallPanelView:(UIView<QMVoiceAssistantPanelSubViewDelegate> *)subSmallPanelView
                                        frame:(CGRect)frame
                                        scene:(QMVoiceAssistantPanelViewScene)scene;
- (void)showSmallPanelViewFromNormalPanelView:(UIView<QMVoiceAssistantPanelSubViewDelegate> *)subNormalPanelView
                                        frame:(CGRect)frame
                                        scene:(QMVoiceAssistantPanelViewScene)scene;

/// 新面板出现，业务自己展现
- (void)showNormlPanelViewByBusinessWithScene:(QMVoiceAssistantPanelViewScene)scene;
- (void)showSmallPanelViewByBusinessWithScene:(QMVoiceAssistantPanelViewScene)scene;


// 大小面板样式切换，业务自己展现
- (void)showNormalPanelViewByBusinessFromSmallPanelView:(UIView<QMVoiceAssistantPanelSubViewDelegate> *)subSmallPanelView
                                                  scene:(QMVoiceAssistantPanelViewScene)scene;
- (void)showSmallPanelViewByBusinessFromNormalPanelView:(UIView<QMVoiceAssistantPanelSubViewDelegate> *)subNormalPanelView
                                                  scene:(QMVoiceAssistantPanelViewScene)scene;

/// 更新面板内容
- (void)updateContent:(nullable id)content withState:(QMVoiceAssistantPanelViewState)state scene:(QMVoiceAssistantPanelViewScene)scene;

/// 更新当前场景
- (void)updateScene:(QMVoiceAssistantPanelViewScene)scene;

- (void)cancelPerformHidePanelView;

/// 隐藏面板
- (void)hideWithAnimation:(BOOL)animation delegate:(nullable id <QMVoiceAssistantPanelViewDelegate>)delegate;

/// 特定状态是不是小面板
+ (BOOL)isSmallPanelState:(QMVoiceAssistantPanelViewState)state;

@end

NS_ASSUME_NONNULL_END
