//
//  UIButton+Addition.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/8/17.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef enum : NSUInteger {
    QMButtonProcessDirectionLeftright = 0,
    QMButtonProcessDirectionUpdown,
    QMButtonProcessDirectionRightleft,
    QMButtonProcessDirectionDownup
} QMButtonProcessDirection;

typedef NS_ENUM(NSInteger, QMButtonImageDirection) {
    QMButtonImageDirectionTop,
    QMButtonImageDirectionLeft,
    QMButtonImageDirectionBottom,
    QMButtonImageDirectionRight
};

NS_ASSUME_NONNULL_BEGIN

@interface UIButton (Addition)

@property(nonatomic, assign) float process;             //  值为0～1，默认从左到右，需要设置填充颜色后方可生效
@property(nonatomic, strong) UIColor *processColor;      //  填充颜色，配合填充值使用
@property(nonatomic, assign) QMButtonProcessDirection direction;

/**
 设置选中样式, 选中为蓝框蓝色, 非选中为灰框黑字
 @param selected  为 YES 是为选中样式
 */
- (void)qm_selected:(BOOL)selected;

/// 调整button为竖直方向
/// @param spacing title 和 image 的间隔
- (void)adjustImageAndTitleVerticalWithSpacing:(CGFloat)spacing;

- (void)adjustImageWithDirection:(QMButtonImageDirection)direction spacing:(CGFloat)spacing;
@end

NS_ASSUME_NONNULL_END
