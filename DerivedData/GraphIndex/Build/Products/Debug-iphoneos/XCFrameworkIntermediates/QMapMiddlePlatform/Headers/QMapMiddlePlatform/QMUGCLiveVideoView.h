//
//  QMUGCLiveVideoView.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/3/10.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/// 直播，视频的 view
@interface QMUGCLiveVideoView : UIView

- (instancetype)init NS_UNAVAILABLE;

- (instancetype)initWithFrame:(CGRect)frame NS_UNAVAILABLE;

- (instancetype)initWithCoder:(NSCoder *)coder NS_UNAVAILABLE;

/// 初始化直播的 view
/// @param data data 应该为 QMUgcReportMarkerData 或 QMRouteEventItem, 只有这两种数据才会初始化正常的 view，否则只会初始化一个空的 view
- (instancetype)initWithData:(id)data NS_DESIGNATED_INITIALIZER;

@end

NS_ASSUME_NONNULL_END
