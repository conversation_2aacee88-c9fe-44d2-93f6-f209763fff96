//
//  Route.h
//  SOSOMap
//
//  Created by sarah on 2017/5/11.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMPointInfo.h"
#import "RoutePoint.h"
#import <DragonMapKit/DragonMapKit.h>

@class RouteSegment;

@interface Route : QRoute<NSCopying>

@property (nonatomic)BOOL isLocalRoute;
@property (nonatomic, copy) NSString *storedFileName;

+ (NSArray *)getProperties;
//计算路线的frame
- (void)calcMBR;

- (NSArray<RoutePoint *> *)points;
- (NSArray<RouteSegment *> *)segmentsTest;
- (QMPointInfo *)startPoint;
- (QMPointInfo *)endPoint;
- (NSString *)routeID;
- (DMMapRect)dm_frame;
@end
