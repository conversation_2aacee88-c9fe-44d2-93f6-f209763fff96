//
//  QMWebViewOfflineTemplateManager.h
//  QMapMiddlePlatform
//
//  Created by allensun on 2021/7/19.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMWebViewOfflineTemplateDebugConfig) {
    QMWebViewOfflineTemplateDebugConfigDefault,
    QMWebViewOfflineTemplateDebugConfigForceOnline,
};

#define QMOfflineTemplateLogInfo(fmt, ...)  QMLogInfo(@"OfflineTemplate", fmt, ##__VA_ARGS__)

@interface QMWebViewOfflineTemplateManager : NSObject

QMDefaultManager_H(QMWebViewOfflineTemplateManager)

@property (nonatomic) QMWebViewOfflineTemplateDebugConfig debugConfig;

+ (void)performTaskWhenMapDidDrawOrTimeOut;

/**
 按需加载离线模板
 */
- (BOOL)loadOfflineTemplate:(NSURL *)url inWebView:(WKWebView *)webView;

/**
 能否加载离线模板
 */
- (BOOL)canLoadOfflineTemplate:(NSURL *)url;

/**
 移除所有离线模板
 */
- (void)removeAllTemplates;

- (BOOL)isOfflineTemplateAvailable:(NSURL *)url
                   requiredVersion:(NSString *)requiredVersion;

@end

NS_ASSUME_NONNULL_END
