//
//  QMTipView.h
//  SOSOMap
//
//  Created by sarah on 2017/11/3.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapUIKit/QMTipsView.h>
typedef int TipsLevel;

@interface QMTipView : QMTipsView

@property (nonatomic) TipsLevel level;//默认是0, 数值越大,优先级越高
@property (nonatomic) BOOL tolerate;//是否容忍低优先级的存在
@property (nonatomic) CGFloat topOffset;
@property (nonatomic, readonly) UIView *customView;

- (instancetype)initWithCustomView:(UIView *)customView;
- (void)showInViewForever:(UIView *)view;
- (void)hide;

@end

@protocol QMTipViewDirectorDelegate <NSObject>

@optional
- (void)tipViewWillDisappear:(QMTipView *)tipView;

- (void)tipViewDirectorDidShow:(QMTipView *)tip;

@end

@interface QMTipViewDirector : NSObject
@property (nonatomic, strong)NSArray<QMTipView *> *showedViews;
@property (nonatomic, weak) id<QMTipViewDirectorDelegate> delegate;
- (BOOL)showTip:(QMTipView *)tip foreverInView:(UIView *)view;
- (BOOL)showTip:(QMTipView *)tip inView:(UIView *)view disappearAfter:(NSTimeInterval)interval;
- (BOOL)showTip:(QMTipView *)tip
         inView:(UIView *)view
 disappearAfter:(NSTimeInterval)interval
 disappearBlock:(void(^)(void))disappearBlock;

- (QMTipView *)topTip;
- (NSArray<QMTipView *> *)tips;
- (BOOL)hasTips;
- (BOOL)dismissTip:(QMTipView *)tip;
- (void)dismissAllTips;
@end

@interface QMTipViewDirector(Private)

- (NSInteger)indexForTip:(QMTipView *)tip;
- (BOOL)showTip:(QMTipView *)tip inView:(UIView *)view atIndex:(NSInteger)index;
- (BOOL)showFirstTip:(QMTipView *)tip inView:(UIView *)view;
- (BOOL)showLastTip:(QMTipView *)tip inView:(UIView *)view;
- (BOOL)showTip:(QMTipView *)tip inView:(UIView *)view;

- (void)insertTip:(QMTipView *)tip atIndex:(NSInteger)index;

@end
