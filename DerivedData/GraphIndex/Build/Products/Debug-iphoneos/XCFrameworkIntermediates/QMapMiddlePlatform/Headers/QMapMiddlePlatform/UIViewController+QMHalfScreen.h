//
//  UIViewController+QMHalfScreen.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 2017/7/7.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, QMPresentChildViewControllerAnimationType) {
    QMPresentChildViewControllerAnimationTypeDefault = 0,     //默认滑上滑下
    QMPresentChildViewControllerAnimationTypeNaviSetting,     //导航设置出现隐藏
    QMPresentChildViewControllerAnimationTypeRouteTabDrag,   // 多方案页tab拖拽view
};

typedef void (^DismissCompleteBlock)(void);

@protocol QMContainerViewInteractionProtocol

- (void) containerViewTouched;

@end

@interface QMContainerView : UIView

@property (nonatomic, weak) id<QMContainerViewInteractionProtocol> delegate;

@end

@interface QMHalfScreenContext : NSObject

@property (nonatomic, weak) UIViewController *parentViewController;

@property (nonatomic, weak) UIViewController *childViewController;

@property (nonatomic, weak) UIView *containerView;

@property (nonatomic, weak) UIView *backgroundView;

@property (nonatomic) CGRect initialFrame;

@property (nonatomic) QMPresentChildViewControllerAnimationType animationType;

@property (nonatomic, weak) UIControl *dimView;
@property (nonatomic, weak) UIControl *dimViewForPad;

@property (nonatomic, copy) DismissCompleteBlock dismissBlock;

@property (nonatomic, assign) BOOL isAutoDismiss;

@end

@interface QMPresentChildViewControllerParam : NSObject

@property (nonatomic) UIViewController *childController;

@property (nonatomic) CGRect frame;

@property (nonatomic) CGRect initialFrame;

@property (nonatomic) BOOL animated;

@property (nonatomic) CGFloat duration;

@property (nonatomic) BOOL clickToDismiss;

@property (nonatomic) BOOL isAutoDismiss;

@property (nonatomic) CGFloat autoDismissDuration;

@property (nonatomic) QMPresentChildViewControllerAnimationType animationType;

@property (nonatomic, copy) DismissCompleteBlock dismissBlock;

@property (nonatomic) UIColor *dimBackgroundColor;

@end

@interface UIViewController (QMHalfScreen)<QMContainerViewInteractionProtocol>

/// ctx
@property (nonatomic, strong) QMHalfScreenContext *qmHalfScreenContext;

- (void) presentModalChildViewController:(UIViewController*) childController
                                   frame:(CGRect)frame
                                animated:(BOOL) animated;

- (void) presentModalChildViewController:(UIViewController*) childController
                                   frame:(CGRect)frame
                                animated:(BOOL) animated
                                duration:(CGFloat)duration;

- (void) presentModalChildViewController:(UIViewController*) childController
                                   frame:(CGRect)frame
                                animated:(BOOL) animated
                                duration:(CGFloat)duration
                            dismissBlock:(DismissCompleteBlock)dismissBlock;

- (void) presentModalChildViewController:(UIViewController*) childController
                                   frame:(CGRect)frame
                                animated:(BOOL) animated
                                duration:(CGFloat)duration
                           isAutoDismiss:(BOOL)isAutoDismiss
                            dismissBlock:(DismissCompleteBlock)dismissBlock;

- (void) presentModalChildViewController:(UIViewController*) childController
                                   frame:(CGRect)frame
                                animated:(BOOL) animated
                                duration:(CGFloat)duration
                          clickToDismiss:(BOOL)clickToDismiss
                           isAutoDismiss:(BOOL)isAutoDismiss
                     autoDismissDuration:(NSTimeInterval)autoDismissDuration
                            dismissBlock:(DismissCompleteBlock)dismissBlock;

- (void) presentModalChildViewControllerWithParam:(QMPresentChildViewControllerParam *) param;

- (void) dismissFromParentViewControllerAnimated:(BOOL) animated;

- (void) dismissFromParentViewControllerAnimated:(BOOL) animated duration:(CGFloat)duration;

- (void)cancelAutoDismiss;
- (void)recoverAutoDismiss;
- (void) dimViewTapped:(id)sender;
- (void)changeToAutoDismiss;
@end
