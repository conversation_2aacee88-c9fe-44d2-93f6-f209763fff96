//
//  QMPageSession.h
//  QMapMiddlePlatform
//
//  Created by co<PERSON><PERSON> on 2023/2/22.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^whiteScreenCompletion)(BOOL);

NS_ASSUME_NONNULL_BEGIN

@interface QMViewControllerSession : NSObject

QMSharedInstance_H(QMViewControllerSession)

/**
  记录Page加载的主sessionId
 */
@property (nonatomic, readonly) NSString *pageSessionId;

/**
  记录Page加载的主session类型
 */
@property (nonatomic, readonly) NSString *pageSessionType;

/**
  记录Page加载的子sessionId
 */
@property (nonatomic, readonly) NSString *subPageSessionId;

/**
  记录Page加载的sessionPath
 */
@property (nonatomic, readonly) NSMutableString *pageSessionPath;

/**
  记录Page加载的场景值
 */
@property (nonatomic, readonly) NSString *currentPageScene;

@property (nonatomic, copy) whiteScreenCompletion whiteScreenCompletion;

@property(nonatomic) BOOL whiteSrceenShotReport;

- (void)masterDetection:(NSString *)page;

- (void)slaveDetection:(NSString *)page;

- (void)imageNotMatchDetection:(NSDictionary *)data;

- (void)hippyImageViewDetection:(NSDictionary *)data;

@end

NS_ASSUME_NONNULL_END
