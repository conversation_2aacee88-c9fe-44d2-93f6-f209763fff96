//
//  QMUgcReportEntryCollectionView.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/6/11.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMUgcReportTypeData.h"


@protocol QMUgcReportEntryDelegate <NSObject>
-(void) didSelectCell:(QMUgcReportType)typeData;

- (void)walkCycleDidSelectCell:(NSDictionary *)configDict needScreenShot:(BOOL)needShot;
@end
@interface QMCustomCollectionViewFlowLayout : UICollectionViewFlowLayout

- (instancetype)initWithSeparateIndex:(NSInteger)separateIndex;

@property (nonatomic) NSInteger separateIndex;
@property (nonatomic) CGSize contentSize;

@end

@interface QMUgcReportEntryCollectionView : UICollectionView
@property (nonatomic,strong) NSArray* reportTypeDatas;
@property (nonatomic,weak) id<QMUgcReportEntryDelegate> entryDelegate;
@property (nonatomic,assign) BOOL nightMode;
@property (nonatomic) BOOL isFromWalkCycleNav;
@end
