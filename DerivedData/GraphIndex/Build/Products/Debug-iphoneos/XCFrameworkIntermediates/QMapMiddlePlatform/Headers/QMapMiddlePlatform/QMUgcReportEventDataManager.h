//
//  QMUgcReportEventDataManager.h
//  SOSOMap
//
//  Created by leon<PERSON><PERSON> on 2018/1/18.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportEventData.h"
#import "QMUgcReportProtocol.h"
 
 
  
 


#define kQMUgcEventDataChangedNotification  @"kQMUgcEventDataChangedNotification"

@interface QMUgcReportEventDataManager : NSObject<QMUgcReportProtocolDelegate>
@property(nonatomic,strong) NSMutableArray* eventDatas;

QMSharedInstance_H(QMUgcReportEventDataManager)

- (void)startRefreshDataWithMap:(DMMapView *)map;
- (void)stopRefreshData;
- (void)handleMapChanged:(DMMapView *)mapView withScaleChanged:(BOOL)bScaleChanged;
- (void) addTempMarkers:(QMUgcReportEventData*)tmpData;
- (void) removeTempMarkers:(NSString *)originId;

/// 清除现有路况事件
- (void)cleaTrafficEventMarkers;

/// 强制请求路况事件
- (void)forceUpdateTrafficEvents;

/// 发起上报点事件是否有用的网络请求
/// @param isUseful 是否有用
/// @param infoCode 点事件id
- (void)reportEventIsUseful:(BOOL)isUseful withInfoCode:(NSString *)infoCode;

/// 记录点事件点击是否有用，如之前已记录则不需要重新记录，返回NO；如之前未记录则记录，返回YES
/// @param infoCode 点事件id
/// @param isUseful 该点事件数据是否有用或存在
- (BOOL)recordClickEventWithInfoCode:(NSString *)infoCode isUseful:(BOOL)isUseful;

/// 更新点事件在本地的有用（存在）的数量
/// @param infoCode 点事件id
/// @param count 数量
- (void)updateUsefulCountCacheWithInfoCode:(NSString *)infoCode num:(int)count;

/// 更新点事件在本地的无用（不存在）的数量
/// @param infoCode 点事件id
/// @param count 数量
- (void)updateUselessCountCacheWithInfoCode:(NSString *)infoCode num:(int)count;

/// 获取点事件在本地缓存的有用（存在）的数量
/// @param infoCode 点事件id
- (int)getUsefulCountFromCache:(NSString *)infoCode;

/// 获取点事件在本地缓存的无用（不存在）的数量
/// @param infoCode 点事件id
- (int)getUselessCountFromCache:(NSString *)infoCode;

/// 获取用户是否点击了点事件，返回0表示未点击过，返回1表示用户选择了有用（存在），返回2表示用户选择了无用（不存在）
/// @param infoCode 点事件id
- (int)getUserOptionTagWithInfoCode:(NSString *)infoCode;

@end
