//
//  RouteSearcherConstant.h
//  SOSOMap
//
//  Created by sarah on 2017/5/16.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#ifndef RouteSearcherConstant_h
#define RouteSearcherConstant_h

#define QM_PARAM_TYPE_ERROR @"参数类型错误"
#define QM_PARAM_START_DESTINVALID_ERROR @"起终点无效"
#define QM_PARAM_START_DEST_CANNOTBENULL_ERROR @"起终点不能为空"
#define QM_PARAM_PASS_INVALID_ERROR @"途经点无效"
#define QM_PARAM_START_DEST_EQUAL_ERROR @"起终点不能相同"
#define QM_PARAM_START_DEST_EQUAL_ERROR_MODIFY @"起终点不能相同，请修改"

#define QM_PARAM_OFFLINE_NODATA_ERROR @"缺少离线包"

#define KKEYCarDefaultString @"智能推荐"
#define KKEYCarAvoidBusyString @"躲避拥堵"
#define KKEYCarNoHighSpeedString @"不走高速"
#define KKEYCarHighSpeedFirstString @"高速优先"
#define KKEYCarAvoidFeeString @"避开收费"
#define KKEYCarBigRoadString @"大路优先"
#define KKEYCarShortTimeString @"时间短"

#define RouteQueryMode @"RouteQueryMode"
#define RouteQueryModeTime @"RouteQueryModeTime"

#define RouteAcrossCityQueryMode @"RouteAcrossCityQueryMode"
#define RouteAcrossCityQueryModeTime @"RouteAcrossCityQueryModeTime"

#define CalculateRouteMode @"CalculateRouteMode"

#define QMCycleSubmodeUserKey @"CycleSubmode"


#define QM_TRAVEL_WAY_TAXI_STR @"taxi"
#define QM_TRAVEL_WAY_CAR_STR @"car"
#define QM_TRAVEL_WAY_BUS_STR @"bus"
#define QM_TRAVEL_WAY_WALK_STR @"walk"
#define QM_TRAVEL_WAY_CYCLE_STR @"cycle"
#define QM_TRAVEL_WAY_RIDE_STR @"ride"
#define QM_TRAVEL_WAY_TRAIN_STR @"train"
#define QM_TRAVEL_WAY_COACH_STR @"coach"
#define QM_TRAVEL_WAY_PLANE_STR @"plane"
#define QM_TRAVEL_WAY_CHAUFFEUR_STR @"chauffeur"
#define QM_TRAVEL_WAY_TRUCK_STR @"truck"
#define QM_TRAVEL_WAY_MOTOR_STR @"motor"
#define QM_TRAVEL_WAY_RIDE_SHARING @"ridesharing"

#define KCarHistoryMigrateFlag @"KCarHistoryMigrateFlag"
#define KBusHistoryMigrateFlag @"KBusHistoryMigrateFlag"
#define KWalkCycleHistoryMigrateFlag @"KWalkCycleHistoryMigrateFlag"
#define KBusCycleHistoryMigrateFlag @"KBusCycleHistoryMigrateFlag"
#define KTrainHistoryMigrateFlag @"KTrainHistoryMigrateFlag"

#define KHistroyRecordModifyParamNotification @"KHistroyRecordModifyParamNotification"
#define KSwitchRouteTravelWayNotification @"KSwitchRouteTravelWayNotification"

/// 驾车tab页点击
#define KNewEnergyCarTabRouteTabClickNotification @"KNewEnergyCarTabRouteTabClickNotification"
#define KRouteDetailOffsetSyncNotification @"KRouteDetailOffsetSyncNotification"
#define KRouteDetailShowPreferOptionNotification @"KRouteDetailShowPreferOptionNotification"
#define KRouteSearchWillStartNotification @"KRouteSearchWillStartNotification"
#define KRouteSearchDidOnlineSucNotification @"KRouteSearchDidOnlineSucNotification"
#define KRouteSearchDidOnlineFailNotification @"KRouteSearchDidOnlineFailNotification"

#define KRouteSearchDidOfflineSucNotification @"KRouteSearchDidOfflineSucNotification"
#define KRouteSearchDidOfflineFailNotification @"KRouteSearchDidOfflineFailNotification"
#define KCarPlayRadarButtonDidPressNotification @"CarPlayRadarButtonDidPressNotification"
#define KRouteSearchDidChangeRouteNotification @"KRouteSearchDidChangeRouteNotification"
#define KRouteSearchDidChangeCPRouteNotification @"KRouteSearchDidChangeCPRouteNotification"
#define KRouteSearchDidChangeCPSettingNotification @"KRouteSearchDidChangeCPSettingNotification"
#define KRouteSearchChangeAccessibilityElementsNotification @"KRouteSearchChangeAccessibilityElementsNotification"

#define QM_CAR_PALY_START_NAVIGATION_NOTIFICATION @"kCarPlayStartNavigationNotification"
#define QM_CAR_PALY_ROUTE_VIEW_CONTROLLER_BACK_NOTIFICATION @"kCarPlayRouteViewControllerBackNotification"
#define QM_CAR_PALY_ROUTE_VIEW_CONTROLLER_DEALLOC_NOTIFICATION @"kCarPlayRouteViewControllerDeallocNotification"
#define QM_CAR_PALY_ROUTE_VIEW_PARAM_INVALID_NOTIFICATION @"kCarPlayRouteViewParamInvalidNotification"
#define QM_CAR_PALY_SEG_HIT_STATUS_DID_CHANGE_NOTIFICATION @"kCarPlaySegHintStatusDidChangeNotification"
#define QM_CAR_PALY_SEG_HIT_BUTTON_DID_PRESS_NOTIFICATION @"kCarPlaySegHintButtonDidPressNotification"

#define QM_CAR_PASSCERTIFICATION_DID_CHANGE_NOTIFICATION @"kCarPassCertificationDidChangeNotification"
#define QM_CAR_NUMBER_DID_ENTRY_NOTIFICATION @"kCarNumDidEntryNotification"
#define QM_CAR_NUMBER_WILL_ENTRY_NOTIFICATION @"kCarNumWillShowEntry"

#define QM_PAGE_TEXTURE @"color_texture_page_v2"
#define QM_PAGE_OLD_TEXTURE @"color_texture_page_old_v2"

#define QM_WALKCYCLE_TEXTURE_UNSELECT @"navcfg_main_route_unselect"
#define QM_WALKCYCLE_TEXTURE_SELECT @"navcfg_main_route_select"
#define QM_WALKCYCLE_NAVI_TEXTURE_DAY_SELECT @"color_texture_select_day_style"

#define QM_TRAVEL_WAY_USED_TAB_DRAG @"travel_way_used_tab_drag"
#define QM_TRAVEL_WAY_TAB_DRAG_ARRAY @"travel_way_tab_drag_array"

#define QM_TRAVEL_WAY_HAS_ROUTE_SEARCH @"travel_way_has_route_search"

typedef NS_ENUM(NSInteger, QMCarPlayStartNavigationSourceType) {
    QMCarPlayStartNavigationSourceTypeNormal,
    QMCarPlayStartNavigationSourceTypeDashboard, // 分屏
};

typedef enum : NSInteger {
    kStateRunning = 300, //正在运行
    kStateTooLate,       //可能错过末班车
    kStateTooEarly,      //首班车未发出
    kStateNotRun,        //停运
} BusTimeLine;

typedef enum BusResultRecommend_ {
    BUSRECOMMEND_NONE = 0,
    BUSRECOMMEND_EXPRESS,
    BUSRECOMMEND_LESS_TRANSFER,
    BUSRECOMMEND_LESS_WALK,
    BUSRECOMMEND_ISNIGHTBUS = 1 << 3

} BusSearchRecommendCode;

typedef enum : NSInteger {
    kVoiceActionTypeNone,
    kVoiceActionTypeTag,
    kVoiceActionTypePrefer,
} kVoiceActionType;

typedef enum : int {
    kAvoidResticSceneRoute = 1,        //多方案页
    kAvoidResticSceneNav = 2,          //导航中进导航设置
    kAvoidResticSceneNavInSetting = 3, // 从App设置进入的导航设置
    kAvoidResticSceneHome = 4,         // 从Home页进入
    kAvoidResticSceneRouteTips = 5,    //从多方案页tips提示进入
    kAvoidResticSceneDrive4Stop4 = 6,  //从“开四停四”出行助手进入
} AvoidResticScene;

typedef NS_ENUM(NSUInteger, kRouteTipLevel) {
    kRouteTipLevelPark = 9,
    kRouteTipLevelTollHolidayFree, //高速免费
    kRouteTipLevelDestStatus,      //营业时间
    kRouteTipLevelExitSubway,       //步骑地铁站出口信息
    kRouteTipLevelToolong,         //步骑导航，诱导使用词汇
    kRouteTipLevelOnline,          //离线检索
    kRouteTipLevelTollStation,     //高速收费
    kRouteTipLevelSpecial,         //步骑导航，诱导使用词汇
    kRouteTipLevelAvoidLimit,      //避开限行
    kRouteTipLevelWCGPSWeak,       //步骑行多方案页GPS信号弱tip
};

typedef NS_ENUM(NSInteger, kExtInstr) {
    kExtInstrNone,
    kExtInstrChangeTravelWay,
};

typedef NSString *RouteInfoKey;

FOUNDATION_EXTERN RouteInfoKey kVoiceAssistantIntentKey;

FOUNDATION_EXTERN NSString *QMRouteExtInstrNotification;

typedef NS_ENUM(NSUInteger, kRouteRetryReasonType) {
    kRouteRetryReasonTypeNone,
    kRouteRetryReasonTypeLocationUnavailable = 1, //定位
    kRouteRetryReasonTypeServerError,             //在线算路
    kRouteRetryReasonTypeNetwork,                 //网络
    kRouteRetryReasonTypeOlRoutePlanError,        //离线算路
    kRouteRetryReasonTypeInterrupt,
    kRouteRetryReasonTypeNoRoute,
    kRouteRetryReasonTypeNilRoute,
    kRouteRetryReasonTypePreOrderLocationPointUnavailable, // 前序点不可用
};

static NSString *const RouteRetryReasonTypeDefaultDesc = @"没有查询结果";
static NSString *const RouteRetryReasonTypeLocationUnavailableDesc = @"无法获取当前位置";
static NSString *const RouteRetryReasonTypeInterruptDesc = @"检索失败";
static NSString *const RouteRetryReasonTypeNetworkDesc = @"网络异常，请检查网络设置";
static NSString *const RouteRetryReasonTypeServerErrorDesc = @"加载失败，请稍后重试";
static NSString *const RouteReasonTypeTooLongDescWalkReason = @"步行路程过长，建议采用其他出行方式";
static NSString *const RouteReasonTypeTooLongDescPrompt = @"建议采用其他出行方式";
static NSString *const RouteReasonTypeTooLongDescCycleReason = @"骑行路程过长，建议采用其他出行方式";
static NSString *const RouteRetryReasonTypeStartEndInvalid = @"起终点不在支持范围内";
static NSString *const RouteRetryReasonTypePreOrderLocationPointUnavailable = RouteRetryReasonTypeServerErrorDesc;
static NSString *const RouteRetryReasonTypeTooShortDescWalkReason = @"步行路程过短";
static NSString *const RouteRetryReasonTypeTooShortDescCycleReason = @"骑行路程过短";
static NSString *const RouteRetryReasonTypeLBSReducedAccuracy = @"获取最新位置失败，请稍后重试";

// 路线计算性能埋点相关字段
// 路线搜索入口
typedef NS_ENUM(NSInteger, QMRouteReqEntrance) {
    QMRouteReqEntranceNavigating = 0, // 导航中
    QMRouteReqEntranceMultiPlan = 1   // 多方案页
};
// 路线搜索场景
typedef NS_ENUM(NSInteger, QMRouteReqScene) {
    QMRouteReqSceneNone = -1,          // 无，不上报
    QMRouteReqSceneMultiPlan = 0,      // 多方案页
    QMRouteReqSceneDynamicRoute = 1,   // 动态换路
    QMRouteReqSceneAdjointRoute = 2,   // 伴随路线
    QMRouteReqSceneAlongwaySearch = 3, // 沿途搜
    QMRouteReqSceneRefreshRoute = 4,   // 刷新路线
    QMRouteReqSceneChangePrefer = 5,    // 修改偏好
    QMRouteReqScenePassCertifcateChanged = 6 //通行证数据变动
};

typedef NS_ENUM(NSInteger, QMCycleSubmode) {
    QMCycleSubmodeNone,
    QMCycleSubmodeBicycle,
    QMCycleSubmodeEBicycle,
};

#define QM_REPORT_ROUTE_REQ_TIME_COST_DELAY 3

FOUNDATION_EXTERN NSInteger const kRouteSearchDefaultFilterGPSNum;

typedef void(^QMRouteSearchCallBack)(id arg);

typedef void(^QMRouteSearchThreeArgsCallBack)(id arg1, id arg2, id arg3);

#define QM_LOCAL_BUSSEARCH_MIN_DISTANCE 1000

#define REASONS @"reasons"

#endif /* RouteSearcherConstant_h */
