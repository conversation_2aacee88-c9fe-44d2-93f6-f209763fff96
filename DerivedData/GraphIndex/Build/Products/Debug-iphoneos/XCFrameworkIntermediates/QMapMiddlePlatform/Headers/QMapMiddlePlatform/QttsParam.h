//
//  QttsParam.h
//  QMapKit
//
//  Created by we<PERSON><PERSON><PERSON> on 2016.10.10.
//  Copyright 2016 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

extern const int TTSPriorityRouteGuidanceMin;//诱导的最低优先级
extern const int TTSPriorityRouteGuidanceMax;//诱导的最高优先级
extern const int TTSPriorityHigherThanRouteGuidance;//优先级比诱导播报高
extern const int TTSPriorityLowerThanRouteGuidance;//优先级比诱导播报低

typedef NS_ENUM(NSUInteger, TTSSource) {
    TTSSourceFromRouteGuidance = 0,//诱导引擎的播报
    TTSSourceFromClient,//客户端逻辑的播报
    TTSSourceFromVoiceAssistant,//智能语音的播报
};

@interface QttsParam : NSObject <NSCopying>


/**
 播报的字符串内容
 */
@property(strong)NSString* ttsString;

/**
 声音文件路径，filePath和audioData只能设置一个，优先取filePath
 */
@property(strong)NSString* filePath;

/**
 音频数据，filePath和audioData只能设置一个，优先取filePath
 */
@property (nonatomic, strong) NSData *audioData;

/**
 优先级，越小越高
 */
@property(assign)int priority;

/**
 声音文件类型，目前只有叮一声，诱导使用
 */
@property(assign)int messageBeep;

@property(assign)int type;//明星语音功能使用

@property(assign) BOOL egg;//是否是彩蛋

/// 语音彩蛋
@property (nonatomic, assign) NSInteger estrellaNum;

/// 彩蛋场景
@property (nonatomic, assign) NSInteger easterEggType;

/// 彩蛋替换方式 1：整句替换；2：后面拼接；3：前面拼接
@property (nonatomic, assign) NSInteger easterEggWay;

/// 空闲时间（2事件间空闲时间透出，端可使用播报其他东西），值为10000时表明sdk计算不出空闲时间
@property (nonatomic, assign) NSInteger idleTime;

/**
 来源，用来区分是哪个业务的播报
 */
@property (assign) TTSSource source;

/// 播放期间禁用叮当唤醒
@property (nonatomic) BOOL disableDingdangWakeup;

/// 播报完成后回复收音
@property (nonatomic) BOOL recoverRecordAfterPlay;

/// 合成时长
@property (nonatomic) CGFloat realSynTime;

+ (instancetype)ttsParamWithString:(NSString*)ttsString;//优先级默认比诱导低, source默认为TTSSourceFromClient
+ (instancetype)ttsParamWithString:(NSString*)ttsString priority:(int)priority source:(TTSSource)source;
+ (instancetype)ttsParamWithString:(NSString*)ttsString priority:(int)priority beep:(int)messageBeep type:(int)type source:(TTSSource)source;//诱导的播报需要

+ (instancetype)ttsParamWithFilePath:(NSString *)filePath;//优先级默认比诱导低, source默认为TTSSourceFromClient
+ (instancetype)ttsParamWithFilePath:(NSString *)filePath priority:(int)priority source:(TTSSource)source;

+ (instancetype)ttsParamWithData:(NSData *)audioData;//优先级默认比诱导低, source默认为TTSSourceFromClient
+ (instancetype)ttsParamWithData:(NSData *)audioData priority:(int)priority source:(TTSSource)source;

@end
