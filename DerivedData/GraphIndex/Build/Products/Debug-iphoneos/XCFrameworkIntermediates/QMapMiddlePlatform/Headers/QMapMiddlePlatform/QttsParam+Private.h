//
//  QttsParam+Private.h
//  Pods-SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 18/7/18.
//  Copyright © 2022 Tencent. All rights reserved.

#import <UIKit/UIKit.h>
#import "QttsParam.h"

@interface QttsParam (Private)
@property (assign) CGFloat synTime;
@property (nonatomic) NSDate *beginSynDate;
@property (assign) CGFloat duration;
/// 产生的音频流子节长度
@property (nonatomic) NSUInteger audioDataLength;
/// 开始播放的时间，timesince1970
@property (nonatomic) NSTimeInterval beginPlayTime;
@property (assign, nonatomic) BOOL isWriteToFile;//判断该tts是否写入本地file
@end
