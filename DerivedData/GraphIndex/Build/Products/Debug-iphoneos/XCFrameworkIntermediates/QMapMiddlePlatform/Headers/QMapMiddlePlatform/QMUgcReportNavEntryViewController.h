//
//  QMUgcReportNavEntryViewController.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/18.
//  Copyright © 2018年 Tencent. All rights reserved.
//



#import "QMUgcReportBaseViewController.h"
#import "QMUgcReportTypeData.h"
#import "QMUgcReportUIDelegate.h"
#import <QMapMiddlePlatform/JsonPOIInfo.h>
#import "QMUgcReportEntryCollectionView.h"
#import <QMapMiddlePlatform/QMCarRouteNaviCommon.h>

typedef void(^QMAudioReportClick)(void);

@interface QMUgcReportNavEntryViewController:QMViewController

@property(nonatomic,weak) id<QMUgcReportUIDelegate> delegate;

@property (nonatomic,strong)   QMUgcReportEntryCollectionView *eventCollectionView;
@property (nonatomic,assign) BOOL isInNav;
@property (nonatomic,assign) BOOL isNight;
@property (nonatomic,assign) BOOL dayNightStyleSameAsApp;
@property (nonatomic,assign) QMUgcNavType navType;
@property (nonatomic,strong) NSDictionary *naviInfo;
@property (nonatomic) QMUgcEntryViewFromSource fromSource;
@property (nonatomic) QMUgcEntryViewClickFromSource clickFromSource;
@property (nonatomic) BOOL isWalkNav;
@property (nonatomic) QMCarRouteNaviType routeNaviType;
@property (nonatomic) id producer;
@property (nonatomic, assign) BOOL audioReportEnable;
@property (nonatomic, assign) BOOL supportAutoSwitchNightMode;
@property (nonatomic) QMAudioReportClick audioReportClick;
- (void)didSelectCell:(QMUgcReportType)type;

- (CGFloat)frameHeighOnDriveNaviWithShowAudio:(BOOL)showAudio;

@end
