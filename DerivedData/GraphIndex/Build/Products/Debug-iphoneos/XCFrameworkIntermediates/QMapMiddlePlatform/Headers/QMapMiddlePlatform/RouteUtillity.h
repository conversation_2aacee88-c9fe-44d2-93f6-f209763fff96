//
//  RouteDistanceUtil.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/10/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(int, QMRouteType) {
    QMRouteTypeBusList = 14,
    QMRouteTypeBus = 15,
    QMRouteTypeDriveList = 21,
    QMRouteTypeDrive = 44,
    QMRouteTypeWalk = 75,
    QMRouteTypeBounds = 94,
    QMRouteTypeCycle = 98,
    QMRouteTypeECycle = 101,   //电动车
};

@interface RouteUtillity : NSObject {
    
}

+ (NSString *)distanceToString:(NSInteger)distance;
+ (NSString *)minutesToString:(NSInteger)minutes;
+ (NSString *)dateWithTimeInterval:(NSInteger)seconds format:(int)format;
+ (NSString *)minutesToShortString:(NSInteger)minutes;
+ (NSString *)minutesToShortString2:(NSInteger)minutes;
//不足1小时显示"分钟" 
+ (<PERSON><PERSON>ributedString *)minutesToShortAttString3:(NSInteger)minutes;
+ (NSString *)minutesToShortString3:(NSInteger)minutes;
+ (NSAttributedString *)busStationCountToString:(NSInteger)stationNum numberFont:(CGFloat)nf stringFont:(CGFloat)sf;
/**
 *  指定 DMMapPoint 点和当前位置的距离，单位是米
 */

+ (double)getDistanceFromUserlocation:(DMMapPoint)point;
+ (NSString *)arrivalTimeToString:(NSInteger)time;


/// 服务器墨卡托坐标转为经纬度
/// @param coors 墨卡托坐标点串
+ (NSArray *)parseNavPoints:(NSString *)coors;
/// 从info里获取起始点信息，市内和跨城共用
/// @param jsonInfo 解析后台返回后的字典
+ (NSDictionary *)loadFromJsonInfo:(NSDictionary *)jsonInfo;
+ (NSArray *)parseBusPoints:(NSString *)segment;

@end

