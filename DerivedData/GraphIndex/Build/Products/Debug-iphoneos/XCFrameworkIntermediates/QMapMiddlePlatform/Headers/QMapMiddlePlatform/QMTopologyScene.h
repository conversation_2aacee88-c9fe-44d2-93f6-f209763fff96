//
//  QMTopologyScene.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/5.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMTopologySceneDefine.h"
#import "QMTopologySceneProtocol.h"

@class QMTopologyScene;
@class QMTopologySceneActor;
@class QMTopologySceneSubscriber;

NS_ASSUME_NONNULL_BEGIN

/// 场景定义，考虑多实例，比如检索就可以无限跳转
@interface QMTopologyScene : NSObject

@property (nonatomic, weak) id<QMTopologySceneProtocol> scene;

@property (nonatomic, weak) UIViewController *controller;

@property (nonatomic, weak) UIView *view;

@property (nonatomic, strong) NSString *sceneName;

@property (nonatomic, assign) QMTopologySceneState state;

@property (nonatomic, assign) QMTopologySceneAppearState appearState;

/// 上一个场景
@property (nonatomic, weak) QMTopologyScene *lastScene;

#pragma mark - 场景状态变化
- (void)sceneInit;

- (void)sceneAppear;

- (void)sceneDisappear;

- (void)sceneDestroy;

- (void)sceneHotLaunch;

#pragma mark - 参与者状态变化
- (void)actorAppear:(id<QMTopologySceneActorProtocol>)actor;

- (void)actorUpdateLayout:(id<QMTopologySceneActorProtocol>)actor;

- (void)actorDismiss:(id<QMTopologySceneActorProtocol>)actor;

#pragma mark - 订阅者
- (CGFloat)addSubscriber:(id<QMTopologySceneSubscriberProtocol>)subscriber;

- (void)subscriberUpdateLayout:(id<QMTopologySceneSubscriberProtocol>)subscriber;

- (void)removeSubscriber:(id<QMTopologySceneSubscriberProtocol>)subscriber;

@end

NS_ASSUME_NONNULL_END
