//
//  QMTruckWeightVolumeIntervalInfo.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/29.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 备注类型
typedef NS_ENUM(NSUInteger, QMTruckWeightVolumeIntervalInfoNoteType) {
    QMTruckWeightVolumeIntervalInfoNoteTypeShowCase,
    QMTruckWeightVolumeIntervalInfoNoteTypeDefaultString,
};

/// view修改的是什么属性
typedef NS_ENUM(NSUInteger, QMTruckWeightVolumeViewType) {
    QMTruckWeightVolumeViewTypeTotalMass,   // 总重量
    QMTruckWeightVolumeViewTypeLoad,        // 总载重
    QMTruckWeightVolumeViewTypeTruckLength, // 货车长度
    QMTruckWeightVolumeViewTypeTruckWidth,  // 货车宽度
    QMTruckWeightVolumeViewTypeTruckHeight, // 货车高度
};

@interface QMTruckWeightVolumeIntervalInfo : NSObject

@property (nonatomic) NSString *name;
@property (nonatomic) NSString *unit;
@property (nonatomic) QMTruckWeightVolumeViewType viewType;
// 滑杆最大值、最小值
@property (nonatomic) CGFloat sliderMinValue;
@property (nonatomic) CGFloat sliderMaxValue;
// 车型 区间最大值、最小值
@property (nonatomic) CGFloat overflowThreshold;
// 超出范围警告
@property (nonatomic) NSString *overflowString;
@property (nonatomic) QMTruckWeightVolumeIntervalInfoNoteType noteType;
@property (nonatomic) NSString *imageName;
@property (nonatomic) NSString *defaultString;


@end

NS_ASSUME_NONNULL_END
