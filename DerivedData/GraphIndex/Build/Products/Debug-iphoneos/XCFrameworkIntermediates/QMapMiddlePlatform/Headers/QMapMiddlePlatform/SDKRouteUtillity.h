//
//  RouteDistanceUtil.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/10/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapFoundation/QMapFoundation.h>
#import <UIKit/UIKit.h>

@interface SDKRouteUtillity : NSObject {
}

QMSharedInstance_H(SDKRouteUtillity)

// meters 单位：米 将数字和单位分开
- (NSArray *)formatDistance:(NSInteger)meters;

//导航面板显示用这个，区别于formatDistance，<15米显示现在
- (NSArray *)formatDistanceNowIfSmallThanQuarter:(NSInteger)meters;

// meters 单位：米  meters 单位：米 【"<10米", "500米", "1.3公里"， "50公里"】
- (NSString *)metersToString:(NSInteger)meters;

// meters 单位：米  meters 单位：米 【"<10 米", "500 米", "1.3 公里"， "50 公里"】
- (NSString *)metersToString:(NSInteger)meters insertSpace:(BOOL)insertSpace;

//小于1公里，显示xx米，1km≤x≤10km，显示xx.x公里，若小数点后为0，则取整，比如 “4.0公里”则显示为 “4公里” X＞10km，显示xx公里，没有小数点
//途经点展示用这个
- (NSString *)metersToStringForAlongway:(NSInteger)meters;

// x>=30000米时，显示“xx公里”，小数点后进行四舍五入取整；
// 1000米=<x<30000米时，显示xx.x公里，距离最多展示1位小数，若小数点后为0，则取整，比如 “4.0公里”则显示为 “4公里”（1140
// 显示为1.2公里，1160显示为1.2公里） 100米=<x<1000米，显示0.x公里，保留1位小数，始终向上进位，比如“540米”则显示为“0.6公里”、“770米”则显示为“0.8公里”
// x<100米，始终显示0.1公里
//打车司乘同显用这个
- (NSString *)metersToStringForTaxi:(NSInteger)meters;

// meters 单位：米 【"现在", "500米", "1.3公里"， "50公里"】导航面板显示用这个
- (NSString *)leftMetersToString:(NSInteger)meters;

// minutes 单位：分 ["1分钟", "50分钟", "1小时35分", "1小时"]
- (NSString *)minutesToString:(NSInteger)minutes;

// seconds 单位：秒 ["1分钟", "50分钟", "1小时35分", "1小时"]
- (NSString *)secondsToString:(NSInteger)seconds;

// seconds 单位：秒 ["1 分钟", "50 分钟", "1 小时 35 分", "1 小时"]
- (NSString *)secondsToString:(NSInteger)seconds insertSpace:(BOOL)insertSpace;

/*
设原始值为X秒：
 X＜60秒→显示为xx''
 否则显示为xx'xx''
 */
- (NSString *)timeDurationToStringForTaxi:(NSInteger)durationSeconds;

/*
设原始值为X秒：without wait cost
 ① X＜3600秒→显示为“xx(分钟):xx(秒)”
 ② X>=3600秒 显示为超过1小时
 */
- (NSString *)waitingTimeDurationToStringForTaxi:(NSInteger)durationSeconds;

/*
设原始值为X秒：
① X＜60秒→显示为xx秒”
② X>=3600秒 显示为1小时
3、X可以被60整除，显示为xx分钟
4、X不可以被60整除，显示为x分xx秒
 */
- (NSString *)maxWaitTimeToStringForTaxi:(NSInteger)maxWaitSeconds;

/*
设原始值为X秒：
① X＜60秒→显示为“即将到达”
②60秒≤X＜3600秒，显示为“xx分钟”
③ ≥3600秒，显示“xx小时xx分”
→注意，①若“xx分”为0，则只显示“xx小时”，② 这里没有“钟”③余数部分采用“四舍五入”。
*/
- (NSString *)secondsToStringForTaxi:(NSInteger)seconds;

// seconds 单位：秒 【"明天18:30"】
- (NSString *)arrivalTimeToString:(NSInteger)seconds;

//返回格式化的时间字符串，数字和单位是定制的字体
- (NSAttributedString *)naviAttributedStringWithString:(NSString *)aString;

- (NSAttributedString *)naviAttributedStringWithString:(NSString *)aString
                                             valueSize:(CGFloat)valueSize
                                           smallerSize:(CGFloat)smallerSize
                                              unitSize:(CGFloat)unitSize;

//将数字和非数字分开处理，HUD里用到
- (NSAttributedString *)hudAttributedStringWithString:(NSString *)aString;

//将秒转换成km/h
- (NSInteger)secondSpeedToHourSpeed:(CGFloat)speed;

// seconds 数值和单位分开
// <1 分钟；20 分钟；1 小时；36 小时
- (NSArray *)formatTime:(NSInteger)timeSeconds;

// 返回格式为 12:23+1 的到达时间
- (NSString *)arrivalTime:(NSInteger)timeSeconds;

/// 计算两个经纬度之间的距离，算法和安卓一致
/// @param loc1 第一个点的经纬度
/// @param loc2 第二个点的经纬度
- (double)distanceBetweenPointsWithFirstLocation:(CLLocationCoordinate2D)loc1 secondLocation:(CLLocationCoordinate2D)loc2;

/// 将角度转为小数点的比例
/// @param number 角度
- (double)radFromLonOrLat:(double)number;

@end
