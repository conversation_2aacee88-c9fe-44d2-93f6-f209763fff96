//
//  QMVoiceAssistantAIService.h
//  QMapMiddlePlatform
//
//  Created by re<PERSON><PERSON><PERSON><PERSON> on 2024/12/13.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMVoiceAssistantProviderProtocol.h"
#import "QMAIDingDangRequest.h"
#import "QMSSEDataParser.h"

NS_ASSUME_NONNULL_BEGIN

// 后台断句事件
extern NSString *const QMSSEEventStepTextSegEnd;

@class QMVoiceAssistantAIStreamModel;

typedef void (^QMVoiceAssistantAIServiceHandler)(NSString * _Nullable aiSemantic);
typedef void (^QMVoiceAssistantAIStreamHandler)(NSArray<QMVoiceAssistantAIStreamModel *> *streamTextArray);

@interface QMVoiceAssistantAIStreamModel : NSObject

@property (nonatomic) NSString *type;
@property (nonatomic) NSString *message;

@end

@interface QMVoiceAssistantAIService : NSObject

/// 提供额外的参数等
@property (nonatomic, weak) id<QMVoiceAssistantProviderProtocol> provider;

/// 用智平语义处理结果请求 AI 后台，AI 后台进行结果优化、domain 转换。废弃
- (void)requestAIDingDangWithRequest:(QMAIDingDangRequest *)request
                          completion:(QMVoiceAssistantAIServiceHandler)completion;

/// 流式接口，叮当逻辑与非流式一致，但大模型结果以流式输出
/// dingdangCompletion: 老叮当意图处理回调，内部确保有且只有一次回调
/// receiveData: 大模型流式输出回调
- (void)requestAIStreamServiceWithRequest:(QMAIDingDangRequest *)request
                       dingdangCompletion:(QMVoiceAssistantAIServiceHandler)dingdangCompletion
                              receiveData:(QMVoiceAssistantAIStreamHandler)receiveData;

/// 断开当前流式连接
- (void)disconnectStream;

@end

NS_ASSUME_NONNULL_END
