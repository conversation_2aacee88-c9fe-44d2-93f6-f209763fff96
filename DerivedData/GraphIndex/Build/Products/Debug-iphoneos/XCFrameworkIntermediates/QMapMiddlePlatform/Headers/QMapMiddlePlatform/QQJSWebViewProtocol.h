//
//  QQJSWebViewProtocol.h
//  WebviewJSAPI
//
//  Created by soapyang on 15/2/4.
//  Copyright (c) 2015年 soapyang. All rights reserved.
//

#import <Foundation/Foundation.h>

@class AllShareInfo;

@protocol QQJSWebViewProtocol<NSObject>

- (NSURL *)getPageURL;
- (void)executeJsScript:(NSString *)script completionHandler:(void (^)(id, NSError *))completionHandler;
- (void)loadRequest:(NSURLRequest *)request;

@optional
- (id)getWebView;
- (void)saveShareContent:(NSArray *)title
                 content:(NSArray *)content
                  picUrl:(NSArray *)picUrl
                shareUrl:(NSArray *)shareUrl
                  report:(NSDictionary *)report;

- (void)getWebviewPerformanceCallBack:
    (NSDictionary *)query; //有一个极端的情况，webviewPageFinishLoad API在调用时，webview还没有finish load，拿不到数据，这里需要业务自己处理。

- (id)getArgmentsWithQuery:(NSDictionary *)query;

- (void)webViewBackAction;
- (UIImage *)generateShareBigImage:(AllShareInfo *)shareInfo;
- (void)webViewExitAction; // 区别于back，这里是退出webview

/**
 页面传给客户端需要由页面来处理事件的区域

 @param query 页面给的数据
 */
- (void)jsSetEventRect:(NSDictionary *)query;

// 上报数据生产者
- (id)reportDataProducer;

@end
