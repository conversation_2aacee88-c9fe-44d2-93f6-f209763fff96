//
//  QMUserDefaultsManager.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/11/11.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMDaylightManager.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString * const QMUserDefaultsManagerNotificationIsSearchBarDownStateChange;

extern NSString * const QMUserDefaultsManagerNotificationIsShowTravelMap;

/// 驾车模式（普通模式、新能源模式）变更发送的通知
extern NSString * const QMUserDefaultsManagerNotificationDriveModeChange;

extern NSString * const QMUserDefaultsManagerNotificationCompanyChange;

extern NSString * const QMUserDefaultsManagerNotificationIsShowTrafficEvents;

extern NSString * const QMPlaceQMCSLegislationSwitchChangedNotification;

extern NSString * const QMUserDefaultsManagerNotificationIsShowTree;

extern NSString * const QMUserDefaultsManagerNotificationIsShowTerrain;

extern NSString * const QMUserDefaultsManagerNotificationIsShow3DEarth;

extern NSString * const QMCarNaviDayNightStatusDidChangedNotification;
extern NSString * const kKeyDayNightMode;
extern NSString * const kValueDayNightModeAuto;
extern NSString * const kValueDayNightModeAlwaysDay;
extern NSString * const kValueDayNightModeAlwaysNight;

extern NSString * const QMUserDefaultsIsOfflineTraffic;
extern NSString * const QMUserDefaultsIsOfflineTrafficEvents;
extern NSString * const QMUserDefaultsIsOfflineFavorite;
extern NSString * const QMUserDefaultsIsOfflineHandPaint;
extern NSString * const QMUserDefaultsIsOfflineTree;
extern NSString * const QMUserDefaultsIsOfflineTerrain;
extern NSString * const QMUserDefaultsIsOfflineEarth;
extern NSString * const QMUserDefaultsIsOfflineFootprint;
extern NSString * const QMUserDefaultsIsOfflineFromWeChatPoint;

extern NSString * const QMUserDefaultsTrafficDefaultValueKey;
extern NSString * const QMUserDefaultsTrafficEventsDefaultValueKey;
extern NSString * const QMUserDefaultsFavoriteDefaultValueKey;
extern NSString * const QMUserDefaultsHandPaintDefaultValueKey;
extern NSString * const QMUserDefaultsTreeDefaultValueKey;
extern NSString * const QMUserDefaultsTerrainDefaultValueKey;
extern NSString * const QMUserDefaultsEarthDefaultValueKey;
extern NSString * const QMUserDefaultsFootprintDefaultValueKey;
extern NSString * const QMUserDefaultsFromWeChatPointDefaultValueKey;

@class QMHippyDebuggingModel;
@interface QMUserDefaultsManager : NSObject

/**
 公司代码
 */
@property (nonatomic, copy, class) NSString *companyCode;

/**
 公司名称
 */
@property (nonatomic, copy, class) NSString *companyName;

/**
 上一次的登陆类型
 QQ 登录为: isQq
 微信登陆为: isWechat
 */
@property (nonatomic, copy, class) NSString *preLoginType;

/**
 被服务端踢出时当前的登陆类型
 QQ 登录为: QQ
 微信登陆为: WX
 */
@property (nonatomic, copy, class) NSString *kickOffType;


/**
 是否使用上搜索条样式, 默认下搜索条
 */
@property (nonatomic, assign, class) BOOL isSearchBarTop;

/**
 是否设置过搜索框偏好, 默认返回 NO
 */
@property (nonatomic, assign, class) BOOL isSelectSearchBarStyle;

/**
 是否需要显示搜索框引导页面
 */
@property (nonatomic, assign, class) BOOL needShowSearchBarGuide;
/**
 已经显示搜索框引导页面
 */
@property (nonatomic, assign, class) BOOL isShowedSearchBarGuide;

/// 运营商网络自动播放视频
@property (nonatomic, assign, class) BOOL autoPlayVideoWhenUsingCellularData;

/// 使用蜂窝网络时，在播放视频前，是否显示消耗流量播放的tips，判断规则如下
/// 1. 如果是 WiFi，则为 NO
/// 2. 如果开了运营商网络自动播放视频的开关则为 NO
/// 3. 如果距离上次点了7天后在提醒我，还在 7 天内，则为 NO
/// 否则为 YES
@property (nonatomic, assign, class, readonly) BOOL shouldShowTipsBeforePlayVideoWhenUsingCellularData;

/// 点击了 7 日之后再提醒我播放视频要注意流量消耗 的按钮, YES 会记录当前点击时间 NO 会把时间改为 0
@property (nonatomic, assign, class) BOOL remindMeLaterTipsWhitchBeforePlayVideoWhenUsingCellularDataTapped;

/**
 常去地点 & 家/公司 隐私合规的开关
 */
@property (nonatomic, assign, class) BOOL placeQMCSLegislationSwitch;

/**
 图层 - 地图类型
 */
@property (nonatomic, assign, class) NSInteger floatLayerMapMode;

/**
 首页 - 图层 - 公交 tips 是否第一次显示 ( 默认是 NO )
 */
@property (nonatomic, assign, class) BOOL floatLayerBusTipsIsLaunch;

/**
 首页 - 图层 -  旅游全景图 tips 是否第一次显示 ( 默认是 NO )
 */
@property (nonatomic, assign, class) BOOL floatLayerTravelMapTipsIsLaunch;

/**
 首页 - 图层 - 通用 tips 是否第一次显示(默认NO)
 */
+ (BOOL)floatLayerCommonTipsIsLaunch:(NSInteger)version;

/**
 首页 - 图层 - 通用 tips 设置第一次显示
 */
+ (void)setFloatLayerCommonTipsIsLaunch:(BOOL)floatLayerCommonTipsIsLaunch version:(NSInteger)version;

/**
 首页 - 设置 - 左手模式
 */
@property (nonatomic, assign, class) BOOL isLeftHand;

/**
 驾车是否是新能源模式
 */
@property (nonatomic, assign, class) BOOL isNewEnergyMode;

/**
 是否展示卫星地图
 */
@property (nonatomic, assign, class) BOOL optionShowSatelite;

// 首页活动积分tips展示日期
@property (nonatomic, strong, class) NSDate *lastShowPointTipsDate;

/**
 图层 - 底图显示 - 旅游全景图
 */
@property (nonatomic, assign, class) BOOL isShowTravelMap;

/**
 图层 - 底图显示 - 路况事件
 */
@property (nonatomic, assign, class) BOOL isShowTrafficEvents;

/**
 图层 - 底图显示 - 树木植被
 */
@property (nonatomic, class) BOOL isShowTree;

/**
 图层 - 底图显示 - 地形地貌
 */
@property (nonatomic, class) BOOL isShowDem;

/**
 图层 - 底图显示 - 3D地球
 */
@property (nonatomic, class) BOOL isShow3DEarth;
/**
 图层 - 底图显示 - 个人足迹
 */
@property (nonatomic, class) BOOL isShowFootprint;

/**
 图层 - 地图显示 - 来自微信
 */
@property (nonatomic, class) BOOL isShowFromWeChatPoint;

/**
 路况开关默认值
 */
@property (nonatomic, class) BOOL isShowTrafficDefaultValue;

/**
 路况事件开关默认值
 */
@property (nonatomic, class) BOOL isShowTrafficEventsDefaultValue;

/**
 收藏开关默认值
 */
@property (nonatomic, class) BOOL isShowFavoriteDefaultValue;

/**
 手绘图开关默认值
 */
@property (nonatomic, class) BOOL isShowHandPaintDefaultValue;

/**
 树木植被开关默认值
 */
@property (nonatomic, class) BOOL isShowTreeDefaultValue;

/**
 地形地貌开关默认值
 */
@property (nonatomic, class) BOOL isShowDemDefaultValue;

/**
 3D地球开关默认值
 */
@property (nonatomic, class) BOOL isShow3DEarthDefaultValue;

/**
 来自微信开关默认值
 */
@property (nonatomic, class) BOOL isShowFromWechatDefaultValue;

/**
 上报右上角红点
 */
@property (nonatomic, assign, class) BOOL badgeIsAppeared;

/**
 在首页场景下是否是小屏幕, 小屏幕需要做额外的适配
 */
@property (nonatomic, assign, class) BOOL isHomepageSmallDevice;

/**
 上一次的登陆类型
 QQ 登录为: isQq
 微信登陆为: isWechat
 */
@property (nonatomic, strong, class) QMHippyDebuggingModel *hippyDebuggingModel;

/**
 用户轨迹上云下一次是否展示弹窗
 */
@property (nonatomic, assign, class) BOOL isShowTrackUpdateToCloudTipsView;


/**
 用户是否允许轨迹上云
 */
@property (nonatomic, assign, class) BOOL isAuthTrackUpdateToCloud;

/**
 积分上报sync次数限制，为减少服务压力
 */
@property (nonatomic, strong, class) NSDictionary *creditReportTimesLimit;

/**
 AMS游戏授权标志位
 */
@property (nonatomic, assign, class) BOOL amsAuthorization;

/**
是否导航中主动杀死进程
 */
@property (nonatomic, assign, class) BOOL isNavigationQuitReport;

/**
当前是否有收藏点进行过重命名。
 */
@property (nonatomic, assign, class) BOOL isChangedFavNickname;

/**
 图卡联动开关标志位
 */
@property (nonatomic, assign, class) BOOL isNotAllowShowTopCard;


///图面推荐 图层显示 开关
/**
 图层显示 - 动态事件
 */
@property (nonatomic, assign, class) BOOL isShowDynamicEvent;

/**
 图层显示 - 年轻潮玩
 */
@property (nonatomic, assign, class) BOOL isShowYoungFashion;

/**
 图层显示 - 服务优惠
 */
@property (nonatomic, assign, class) BOOL isShowDiscountService;

/**
 图层显示 - 热点活动
 */
@property (nonatomic, assign, class) BOOL isShowHotActivity;

/**
 图层显示 - 主题地图(疫情地图)
 @param themeType 用户主题地图开关设置key(和阿波罗配置的themeType一致)
 @return BOOL 开关是否打开
 */
+ (BOOL)themeAreaSwitch:(NSString *)themeType;

/**
 更新主题地图用户开关
 @param themeType 用户主题地图开关设置key(和阿波罗配置的themeType一致)
 @param open 开关状态
 */
+ (void)updateThemeAreaSwitch:(NSString *)themeType open:(BOOL)open;

/**
 全局浮层 - 用反
 */
@property (nonatomic, assign, class) BOOL isShowFeedBackFollowView;

/**
 是否需要 mock 登陆信息
 */
@property (nonatomic, assign, class) BOOL isMockLoginInfo;

/**
 需要 mock 的 userID
 */
@property (nonatomic, copy, class) NSString *userID;

/**
 记录首页底图是否已经创建
 */
@property (nonatomic, assign, class) BOOL homePageViewLaunched;

@property (nonatomic, assign, class) double travelPageTabStartTime;

@property (nonatomic, assign, class) double personalCenterTabStartTime;

/**
 驾车多方案页最大需要展示的次数
 */
@property (nonatomic, assign, class) NSInteger maxNeedShowAutoFavoriteTips;

/**
 驾车多方案页已经展示次数
 */
@property (nonatomic, assign, class) NSInteger hasShowAutoFavoriteTips;

/**
是否自动开启微信跳转地点收藏
 */
@property (nonatomic, assign, class) BOOL isEnableFavoriteLocationFromWeChat;

//绕开本地开关显示或隐藏主题图层
+ (void)updateThemeAreaSwitchForPoi:(NSString *)themeType open:(BOOL)open;

/**
 首页底图样式
 0-标准地图/1-卫星地图/2-公交地图
 */
@property (nonatomic, assign, class) NSInteger homepageMapMode;

/**
 驾车导航日夜间模式
 */
@property (nonatomic, assign, class) QMapNaviDayNightMode dayNightMode;

/// 字典
/// weekTotalCount 本周已经出现次数
/// totalCount 总计出现次数
/// weekLastTimeStamp 本周最早出现的时间戳
@property (nonatomic, strong, class) NSDictionary *newEnergyGuideStatus;

/**
 ViewController 的完整路由字符串
 Such as:  QMHomePageViewController/DriveNaviViewController/xxxxViewController
 */
@property (nonatomic, copy, class) NSString *vcPath;

/**
 Hippy bundle 加载的完整路由字符串
 Such as:   carList-Index/carList-MapCard/xxx-xxx
 */
@property (nonatomic, copy, class) NSString *hippyBundleName;

/**
 Hippy bundle 加载的次数
 */
@property (nonatomic, assign, class) NSInteger hippyLoadCount;

/**
 步行导航最小缩放等级
 */
@property (nonatomic, class) CGFloat walkNaviMinScale;

/**
 步行导航最大缩放等级
 */
@property (nonatomic, class) CGFloat walkNaviMaxScale;

/**
 步行导航视野倾斜角
 */
@property (nonatomic, class) CGFloat walkNaviSkew;

/**
 步行导航使用默认 3D 车标
 */
@property (nonatomic, class) BOOL walkNaviUseDefault3DLocator;

/**
 骑行导航使用默认 3D 车标
 */
@property (nonatomic, class) BOOL cycleNaviUseDefault3DLocator;

/**
 首页默认加载2D/3D
 */
@property (nonatomic, assign, class) NSInteger homeMapDefaultDimensionalType;

/**
 缩放按钮类型，YES时应该展示滑动缩放
 */
@property (nonatomic, assign, class) BOOL displaySlideZoomBar;

/**
 是否展示缩放按钮
 */
@property (nonatomic, assign, class) BOOL shouldDisplayZoomBar;

/**
 多方案页步行导航 tips 展示时间集合 (只保存一天)
 */
@property (nonatomic, class) NSArray<NSNumber *> *routePlanWalkNaviTipsShowTime;

/**
 多方案页骑行导航 tips 展示时间集合 (只保存一天)
 */
@property (nonatomic, class) NSArray<NSNumber *> *routePlanCycleNaviTipsShowTime;

/**
 多方案页驾车导航 tips 展示时间集合 (只保存一天)
 */
@property (nonatomic, class) NSArray<NSNumber *> *routePlanCarNaviTipsShowTime;

/**
 第二tab热力按钮状态
 */
@property (nonatomic, class) BOOL shouldShowHeat;

/// 驾车导航中上报是否隐私授权
@property (nonatomic, class) BOOL navReportAuthorized;
/// 是否有驾车导航中未同步的上报
@property (nonatomic, class) BOOL hasUnloginNavReport;

/// 是否展示过放大镜提示
@property (nonatomic, class) BOOL hasShowZoomerToast;
/// 是否展示过放大镜首次出现提示
@property (nonatomic, class) BOOL hasShowZoomerFirstToast;

@end

NS_ASSUME_NONNULL_END
