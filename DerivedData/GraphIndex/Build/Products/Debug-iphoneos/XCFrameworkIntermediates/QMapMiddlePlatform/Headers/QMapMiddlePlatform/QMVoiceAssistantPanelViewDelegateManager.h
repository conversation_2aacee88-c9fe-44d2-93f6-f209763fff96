//
//  QMVoiceAssistantPanelViewDelegateManager.h
//  SOSOMap
//
//  Created by foog<PERSON><PERSON>(王中周) on 2019/12/10.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMVoiceAssistantPanelSubViewDelegate.h"

NS_ASSUME_NONNULL_BEGIN

@class QMVoiceAssistantPanelView;
@interface QMVoiceAssistantPanelViewDelegateManager : NSObject

+ (void)addDelegate:(id<QMVoiceAssistantPanelViewDelegate>)delegate;
+ (void)removeDelegate:(id<QMVoiceAssistantPanelViewDelegate>)delegate;
+ (void)enumarateDelegate:(void(^)(id<QMVoiceAssistantPanelViewDelegate> delegate, BOOL *stop))enumerateBlock;

+ (void)becomePanelViewFristResponseDelegate:(id<QMVoiceAssistantPanelViewDelegate>)delegate;
+ (void)unbecomePanelViewFristResponseDelegate:(id<QMVoiceAssistantPanelViewDelegate>)delegate;

+ (void)panelView:(QMVoiceAssistantPanelView *)panelView willShowWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;
+ (void)panelView:(QMVoiceAssistantPanelView *)panelView willHideWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;
+ (void)panelView:(QMVoiceAssistantPanelView *)panelView didHideWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;
+ (void)changePanelView:(QMVoiceAssistantPanelView *)panelView
              fromState:(QMVoiceAssistantPanelViewState)fromState
                toState:(QMVoiceAssistantPanelViewState)toState
               delegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

+ (QMVoiceAssistantPanelViewScene)panelViewScene;
+ (QMVoiceAssistantPanelViewScene)panelViewSceneWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

+ (UIView *)panelViewSuperViewWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

+ (CGRect)panelViewFrameWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate state:(QMVoiceAssistantPanelViewState)state;

+ (CGRect)panelViewNormalFrameWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

+ (CGRect)panelViewSmallFrameWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

+ (BOOL)isBusinessShowVoiceAssistantPanelViewWithDelegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

+ (id <QMVoiceAssistantPanelViewDelegate>)currentDelegate;

@end

NS_ASSUME_NONNULL_END
