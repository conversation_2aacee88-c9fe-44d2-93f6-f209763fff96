//
//  RouteReqParam.h
//  SOSOMap
//
//  Created by sarah on 2017/5/10.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import "QMPointInfo.h"

@interface RouteReqParam : QRouteReqParam<NSCopying>

@property (nonatomic, copy)QMPointInfo *startPoint;
@property (nonatomic, copy)QMPointInfo *destPoint;
@property (nonatomic, copy)NSArray<QMPointInfo *> *passPoints;
@property (nonatomic) NSString *now_routeid;

@property (nonatomic) BOOL customRoute;
@property (nonatomic, copy) NSString *shortLink;
@property (nonatomic, copy) NSString *favoriteId;
@property (nonatomic, assign) BOOL isHasFavoriteRoute;
@property (nonatomic, assign) NSInteger subTravelType;

- (BOOL)hasPassPOIs;

- (NSArray *)specificPoints;

@end

@interface RouteReqParam (ActionLog)

- (void)reportRouteRequestActionLog;
- (void)reportRouteNavigationActionLog;

@end
