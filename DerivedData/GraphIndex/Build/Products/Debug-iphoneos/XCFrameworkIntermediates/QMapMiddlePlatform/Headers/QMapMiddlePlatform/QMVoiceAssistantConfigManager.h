//
//  QMVoiceAssistantConfigManager.h
//  SOSOMap
//
//  Created by foogrywang(王中周) on 2018/7/31.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/NSString+VoiceAssistant.h>

// 多少毫秒不说话就认为超时
/****************** Start ******************/
#define QM_RECORD_DEFAULT_TIMEOUT   (8000)  // 默认值
#define QM_RECORD_NAVI_TIMEOUT      (5000)  // 导航中
#define QM_RECORD_VAD_SILENCE_TIME  (800)   //两个字之间间隔不能超过0.8s
#define QM_RECORD_USER_FEEDBACK_VAD_SILENCE_TIME  (1200)  //两个字之间间隔不能超过1.2s
/****************** End ********************/


// 收音面板引导语对应的界面类型
/****************** Start ******************/
#define QM_VOICE_ASSISTANT_PAGE_HOME              @"home"             // 地图首页
#define QM_VOICE_ASSISTANT_PAGE_MUTI_ROUTES       @"mutiRoutes"       // 多方案页
#define QM_VOICE_ASSISTANT_PAGE_FOLLOW_ROUTE      @"followRoute"      // 路线探测
#define QM_VOICE_ASSISTANT_PAGE_LIST              @"list"             // 列表页
#define QM_VOICE_ASSISTANT_PAGE_POI_LIST          @"poiList"          // POI 列表页
#define QM_VOICE_ASSISTANT_PAGE_POI_CARD          @"poiCard"          // POI 小页卡
#define QM_VOICE_ASSISTANT_PAGE_POI_DETAILS       @"poiDetails"       // POI详情页
#define QM_VOICE_ASSISTANT_PAGE_CONFIRM_PAGE      @"confirmPage"      // 起终点确认页（含途经点确认页）
#define QM_VOICE_ASSISTANT_PAGE_POI_INPUT         @"poiInput"         // 起点、终点、家、公司输入界面
#define QM_VOICE_ASSISTANT_PAGE_NAVI              @"navi"             // 导航中
#define QM_VOICE_ASSISTANT_PAGE_NAVI_OTHERS       @"navi_others"      // 导航中打开其它界面
#define QM_VOICE_ASSISTANT_PAGE_NAVI_DISCLAIMER   @"naviDisclaimer"   // 导航免责声明
#define QM_VOICE_ASSISTANT_PAGE_OTHER             @"other"            // 上述场景以外的界面
#define QM_VOICE_ASSISTANT_PAGE_WALK_CYCLE        @"walk_cycle"       // 步骑行
#define QM_VOICE_ASSISTANT_PAGE_ROUTE_POINT       @"routePoint"       // 路线起终点列表选择
#define QM_VOICE_ASSISTANT_PAGE_DISCOVERY         @"PAGE_EXPLORE"     // 探索页
#define QM_VOICE_ASSISTANT_PAGE_AIHOME            @"PAGE_AIHOME"      // AIHome

/****************** End ********************/

// 手动唤起叮当对应的界面类型
/****************** Start ******************/
#define QM_VOICE_ASSISTANT_PAGE_HOME_SCREEN             @"homeScreen"       // 首屏
#define QM_VOICE_ASSISTANT_PAGE_HOME_KEYBOARD           @"keyboard"         // 键盘
#define QM_VOICE_ASSISTANT_PAGE_HOME_SEARCH_BAR         @"searchBar"        // 搜索栏
#define QM_VOICE_ASSISTANT_PAGE_HOME_ROUTE_SEARCH_BAR   @"routeSearchBar"   // 起终点搜索栏
#define QM_VOICE_ASSISTANT_PAGE_HOME_NAV_PANEL          @"navPanel"         // 导航面板
#define QM_VOICE_ASSISTANT_PAGE_HOME_CONFIRM_PAGE       @"confirmPage"      // 确认页面
#define QM_VOICE_ASSISTANT_PAGE_HOME_AI_HOME            @"aiHome"           // AIHome
#define QM_VOICE_ASSISTANT_PAGE_HOME_EXPLORE            @"explore"          // 探索
#define QM_VOICE_ASSISTANT_PAGE_HOME_OTHER              @"other"            // 其他
/****************** End ********************/

// 提示音类型，目前只有导航中、非导航中两种
/****************** Start ******************/
#define QM_REMINDER_TTS_TYPE_NAVI           @"navi"
#define QM_REMINDER_TTS_TYPE_OTHERS         @"others"
/****************** End ********************/


// 面板引导语占位符
/****************** Start ******************/
#define QM_CONFIG_POI_PLACEHOLDER           @"[poi]"
#define QM_CONFIG_ADDR_PLACE_HOLDER         @"[addr]"
/****************** End ********************/


// 可以播 answer 的技能列表
/****************** Start ******************/
#define QM_ANSWER_SKILL_KEY_DOMAIN          @"domain"
#define QM_ANSWER_SKILL_KEY_INTENTS         @"intents"
/****************** End ********************/


// Group ID
/****************** Begin ********************/
#define QM_GROUP_ID_GUIDE_WORD               @"guideWord"                    // 面板引导语
#define QM_GROUP_ID_EGGS                     @"eggs"                         // 运营彩蛋
#define QM_GROUP_ID_POI_LIST                 @"poiList"                      // 当前城市的知名 POI
#define QM_GROUP_ID_FIRST_KAUNCH_GUIDE       @"firstLanuchGuide"             // 当天首次启动面板引导语
#define QM_GROUP_ID_CORRECT_TTS_GUIDE        @"correctTTSGuide"              // 纠错 TTS
#define QM_GROUP_ID_VOICE_COMMON             @"voiceCommon"                  // 智能语音通用配置，目前里面有收音超时配置
#define QM_GROUP_ID_GOTO_PAGE                @"gotopage"                     // qqmap 跳转协议
#define QM_GROUP_ID_TTS                      @"iOSClientTTS"                 // TTS
#define QM_GROUP_ID_REMINDER_TTS             @"commonReminderTTS"            // 唤醒后提示音
#define QM_GROUP_ID_WAKEUP_WORDS             @"wakeupWords"                  // 特色唤醒词相关配置
#define QM_GROUP_ID_ROUTE_TAG                @"routeTag"                     // 路线标签相关配置
#define QM_GROUP_ID_CORRECT_ASR              @"correctASR"                   // 语音转文字错误纠正
#define QM_GROUP_ID_ANSWER_SKILL             @"answerSkill"                  // 允许播 Answer 的技能
/****************** End ********************/


// 云控 TTS 类型
/****************** Start ******************/
// 左侧和右侧不是完全对应，是因为有些回复语的key和安卓不对齐，由于埋点需求，改成与安卓对齐
#define QM_COMMON_DINGDANG_UNKNOWN                        @"common_dingdang_unkown"
#define QM_COMMON_DINGDANG_UNKNOWN_DEFAULT                @"common_dingdang_unkown_dafault"
#define QM_COMMON_DINGDANG_SERVER_UNKNOWN                 @"common_dingdang_server_unknown"
#define QM_NAV_CANT_DINGDANG_IN_PAGE                      @"common_dingdang_notInPage"
#define QM_COMMON_NO_NET                                  @"common_no_net"
#define QM_COMMON_DINGDANG_OFFLINE_MODE                   @"common_dingdang_offline_mode"
#define QM_COMMON_DINGDANG_BUS_REMINDER                   @"common_dingdang_bus_reminder"
#define QM_COMMON_DINGDANG_TAXI                           @"common_dingdang_taxi"
#define QM_COMMON_DINGDANG_CHAUFFEUR                      @"common_dingdang_chauffeur"
#define QM_COMMON_DINGDANG_RIDESHARING                    @"common_dingdang_rideSharing"
#define QM_COMMON_DINGDANG_CAR_MULTI                      @"common_dingdang_carMulti"
#define QM_COMMON_DINGDANG_DETECTION_NAV                  @"common_dingdang_detection_nav"
#define QM_COMMON_DINGDANG_VIRTRUAL_NAV                   @"common_dingdang_virtrual_nav"
#define QM_COMMON_DINGDANG_HUD                            @"common_dingdang_hud"
#define QM_COMMON_DINGDANG_DOG                            @"common_dingdang_dog"
#define QM_COMMON_INDEX_SECOND_ROUND_CORRECT              @"common_choice_guide_more"
#define QM_COMMON_INDEX_SECOND_ROUND_CORRECT_SUPPORT      @"common_choose_which_one"
#define QM_GLOBAL_ZOOMIN                                  @"glb_zoom_in"
#define QM_GLOBAL_ZOOMOUT                                 @"glb_zoom_out"
#define QM_GLOBAL_CANT_ZOOMIN                             @"glb_cant_zoom_in"
#define QM_GLOBAL_CANT_ZOOMOUT                            @"glb_cant_zoom_out"
#define QM_GLOBAL_ZOOMIN_MAX                              @"glb_map_max_level"
#define QM_GLOBAL_ZOOMOUT_MAX                             @"glb_map_min_level"
#define QM_GLOBAL_2D                                      @"glb_2d"
#define QM_GLOBAL_3D                                      @"glb_3d"
#define QM_GLOBAL_ROUTE_FRONT                             @"glb_route_front"
#define QM_GLOBAL_NORTH_FRONT                             @"glb_north_front"
#define QM_GLOBAL_CANT_2D                                 @"glb_cant_2d"
#define QM_GLOBAL_CANT_3D                                 @"glb_cant_3d"
#define QM_GLOBAL_OPEN_TRAFFIC                            @"glb_open_traffic"
#define QM_GLOBAL_CLOSE_TRAFFIC                           @"glb_close_traffic"
#define QM_GLOBAL_CANT_OPEN_TRAFFIC                       @"glb_cant_open_traffic"
#define QM_GLOBAL_CANT_CLOSE_TRAFFIC                      @"glb_cant_close_traffic"
#define QM_GLOBAL_CUR_LOCATION                            @"glb_cur_location"
#define QM_GLOBAL_GONE_HOME                               @"glb_gone_home"
#define QM_GLOBAL_GONE_BACK                               @"glb_gone_back"
#define QM_GLOBAL_ICAN_DO_SOMETHING                       @"glb_i_can_do_something"
#define QM_GLOBAL_LIMIT_INFO                              @"glb_no_limit_info"
#define QM_GLOBAL_SOUND_TURNUP                            @"glb_sound_turn_up"
#define QM_GLOBAL_SOUND_TURN_DOWN                         @"glb_sound_turn_down"
#define QM_GLOBAL_SOUND_TURN_MAX                          @"glb_sound_turn_max"
#define QM_GLOBAL_SOUND_TURN_OFF                          @"glb_sound_turn_off"
#define QM_GLOBAL_SOUND_TURN_ON                           @"glb_sound_turn_on"
#define QM_GLOBAL_ITS_OK                                  @"glb_its_ok"
#define QM_GLOBAL_SWITCH_TO_VOICE_CENTER                  @"glb_switch_to_voicecenter_with_name"
#define QM_GLOBAL_SWITCH_TO_VOICE_CENTER_4G               @"glb_switch_to_voicecenter_with_name_not_wifi"
#define QM_GLOBAL_SWITCH_TO_VOICE_CENTER_4G_NO_NAME       @"glb_not_found_tts_pack"
#define QM_GLOBAL_SWITCH_TO_VOICE_START_DOWNLOAD          @"glb_start_download_tts"
#define QM_GLOBAL_DOWNLOAD_VOICE_PACKAGE_NOT_IN_NAV       @"glb_download_voice_package_not_in_nav"
#define QM_GLOBAL_DOWNLOAD_VOICE_PACKAGE_IN_NAV           @"glb_download_voice_package_in_nav"
#define QM_GLOBAL_SWITCH_DEFAULT_VOICE                    @"glb_switch_default_voice"
#define QM_GLOBAL_SWITCH_VOICE                            @"glb_switch_voice"
#define QM_GLOBAL_SWITCH_TO_PAGE                          @"glb_switch_to_page_with_name"
#define QM_GLOBAL_SWITCH_TO_PAGE_NOT_LOGIN                @"glb_switch_to_page_with_name_need_login"
#define QM_GLOBAL_FEEDBACK_IN_NAV                         @"glb_feedback_in_nav"
#define QM_GLOBAL_FEEDBACK_NO_LOGIN                       @"glb_feedback_no_login"
#define QM_GLOBAL_FEEDBACK_ENTER                          @"glb_feedback_enter"
#define QM_GLOBAL_CORRECT_STL_TIMEOUT                     @"common_dingdang_say_more"
#define QM_GLOBAL_CORRECT_DEFAULT                         @"common_dingdang_say_more_change"
#define QM_GLOBAL_BRIGHTNESS_UP                           @"glb_brightness_up"
#define QM_GLOBAL_BRIGHTNESS_UP_MAX                       @"glb_brightness_up_max"
#define QM_GLOBAL_BRIGHTNESS_DOWN                         @"glb_brightness_down"
#define QM_GLOBAL_BRIGHTNESS_DOWN_MIN                     @"glb_brightness_down_min"
#define QM_ROUTE_FROM_TO_TIME_DISTANCE_BUS                @"route_from_to_time_distance_bus"
#define QM_ROUTE_FROM_TO_TINE_DISTANCE_WALK               @"route_from_to_time_distance_walk"
#define QM_ROUTE_FROM_TO_TINE_DISTANCE_WALK_TOO_FAR       @"route_from_to_time_distance_walk_toofar"
#define QM_ROUTE_FROM_TO_TIME_DISTANCE_RIDE               @"route_from_to_time_distance_ride"
#define QM_ROUTE_FROM_TO_TIME_DISTANCE_RIDE_TOO_FAR       @"route_from_to_time_distance_ride_toofar"
#define QM_ROUTE_FROM_TO_FAST                             @"route_from_to_fast"
#define QM_ROUTE_FROM_TO_SLOW                             @"route_from_to_slow"
#define QM_ROUTE_ASK_DISTANCE_TIME                        @"route_from_to_time_distance_car"
#define QM_ROUTE_FROM_TO_ROUTE_CAR_ONE_PLAN               @"route_from_to_route_car_one_plan"
#define QM_ROUTE_FROM_TO_ROUTE_CAR_START_END              @"route_from_to_route_car_detail"
#define QM_ROUTE_FROM_TO_ROUTE_CAR_START_END_NO_TAG       @"route_from_to_route_car_detail_common"
#define QM_ROUTE_FROM_TO_ROUTE_CAR_END                    @"route_to_route_car_detail"
#define QM_ROUTE_FROM_TO_ROUTE_CAR_END_NO_TAG             @"route_to_route_car_detail_common"
#define QM_ROUTE_FROM_TO_ROUTE_BUS                        @"route_from_to_route_bus"
#define QM_ROUTE_FROM_TO_ROUTE_BUS_TOO_NEAR               @"route_from_to_route_bus_too_near"
#define QM_ROUTE_FROM_TO_ROUTE_BUS_TOO_NEAR_WALK          @"route_from_to_route_bus_too_near_walk"
#define QM_ROUTE_FROM_TO_ROUTE_WALK                       @"route_from_to_route_walk"
#define QM_ROUTE_FROM_TO_ROUTE_WALK_TOO_FAR               @"route_from_to_route_walk_toofar"
#define QM_ROUTE_FROM_TO_ROUTE_RIDE                       @"route_from_to_route_ride"
#define QM_ROUTE_FROM_TO_ROUTE_RIDE_TOO_FAR               @"route_from_to_route_ride_toofar"
#define QM_ROUTE_FROM_TO_ROUTE_NO_RESULT                  @"route_no_route"
#define QM_ROUTE_SWITCH_PREFER_SINGLE_RESULT              @"route_switch_perfer_single_result"
#define QM_ROUTE_SWITCH_PREFER_MUTI_RESULT                @"route_limit_plan_which_one"
#define QM_ROUTE_SWITCH_TAG_PLAN                          @"route_switch_route_type"
#define QM_ROUTE_SWITCH_INDEX                             @"route_switch_success_need_nav"
#define QM_ROUTE_NO_TAG_PLAN                              @"route_no_tag_plan"
#define QM_ROUTE_SWITCH_ROUTE_TYPE                        @"route_switch_route_type_no_nav"
#define QM_ROUTE_SAY_HOME_ADDR                            @"route_say_home_addr"
#define QM_ROUTE_SAY_COMPANY_ADDR                         @"route_say_company_addr"
#define QM_ROUTE_OPEN_ROUTE_DETECT                        @"route_open_route_detect"
#define QM_ROUTE_OPEN_ROUTE_DETECT_INVALID                @"route_open_route_detect_invalid"
#define QM_ROUTE_LIST_SELECT_HOME                         @"route_list_select_home"
#define QM_ROUTE_LIST_SELECT_COMPANY                      @"route_list_select_company"
#define QM_ROUTE_LIST_SELECT_START                        @"route_multi_start"
#define QM_ROUTE_LIST_SELECT_END                          @"route_multi_dest"
#define QM_ROUTE_LIST_SELECT_MIDDLE                       @"route_list_select_middle"
#define QM_ROUTE_LIST_SELECT_OTHER                        @"route_list_select_other"
#define QM_ROUTE_START_END_POINT_EQUAL                    @"route_start_end_point_equal"
#define QM_POI_SINGLE_RESULT                              @"poi_single_result_need_nav"
#define QM_POI_SINGLE_RESULT_STATION                      @"poi_single_result_station_need_nav"
#define QM_POI_SINGLE_RESULT_STATION_JUMP_CITY            @"poi_single_result_station_jump_city"
#define QM_POI_SINGLE_RESULT_LINE                         @"poi_single_result_line"
#define QM_POI_LIST_SELECT_POI                            @"poi_single_result_need_nav2"
#define QM_POI_LIST_SELECT_STATION                        @"poi_list_select_station"
#define QM_POI_LIST_SELECT_BUS_LINE                       @"poi_list_select_bus_line"
#define QM_POI_MULTI_RESULT_SELECT                        @"poi_multi_result_select"
#define QM_POI_MULTI_RESULT_SELECT_CATEGORY               @"poi_multi_result_select_recommand"
#define QM_POI_MULTI_RESULT_CHAIN_STORE                   @"poi_multi_result_select_nearest"
#define QM_POI_CITY_POI_SINGLE_RESULT                     @"poi_city_poi_single_result_need_nav"
#define QM_POI_CITY_MULTI_RESULT_SELECT                   @"poi_city_multi_result_select"
#define QM_POI_CHANGE_CITY                                @"poi_change_city"
#define QM_POI_CHANGE_REGION                              @"poi_change_region"
#define QM_POI_MULTI_CITY                                 @"poi_multi_city_multi_result_select"
#define QM_POI_MULTI_REGION                               @"poi_multi_region"
#define QM_POI_NO_RESULT                                  @"poi_no_result"
#define QM_NAV_DAY_MODE                                   @"nav_day_mode"
#define QM_NAV_NIGHT_MODE                                 @"nav_night_mode"
#define QM_NAV_OPEN_OVER_VIEW                             @"nav_open_overview"
#define QM_NAV_CLOSE_OVER_VIEW                            @"nav_close_overview"
#define QM_NAV_DIRECT_NAV_FROM_TO                         @"nav_direct_nav_from_to"
#define QM_NAV_DIRECT_NAV_TO                              @"nav_direct_nav_to"
#define QM_NAV_DIRECT_NAV_HOME                            @"nav_direct_nav_home"
#define QM_NAV_DIRECT_NAV_COMPANY                         @"nav_direct_nav_company"
#define QM_NAV_SEARCH_BY_WAY_MUTI_RESULT_NO_BEST_WAY      @"nav_search_by_way_muti_result_no_best_way"
#define QM_NAV_SEARCH_BY_WAY_SINGLE_RESULT_NO_BEST_WAY    @"nav_search_by_way_single_result_no_best_way"
#define QM_NAV_SEARCH_BY_WAY_MUTI_RESULT                  @"nav_search_along_poi_multiple_result"
#define QM_NAV_SEARCH_BY_WAY_SINGLE_RESULT                @"nav_search_along_poi_single_result"
#define QM_NAV_NO_SEARCH_BY_WAY_RESULT                    @"nav_no_search_by_way_result"
#define QM_NAV_NO_SEARCH_BY_WAY_RESULT_NO_KEYWORD         @"nav_no_search_by_way_result_no_keyword"
#define QM_NAV_SEE_YOU                                    @"nav_see_you"
#define QM_NAV_LICENSE                                    @"nav_license"
#define QM_NAV_CANT_DO_IN_PAGE                            @"nav_cant_do_in_page"
#define QM_NAV_CONTINUE_NAV                               @"nav_continue_nav"
#define QM_NAV_ADD_POI_SUCCESS                            @"nav_add_poi_success"
#define QM_NAV_CANT_DO_IN_NAV                             @"nav_cant_do_in_nav"
#define QM_NAV_SWITCH_PREFER                              @"nav_switch_perfer"
#define QM_NAV_SWITCH_TO_PAGE                             @"nav_switch_to_page"
#define QM_NAV_OTHER_BACK_TO_NAV                          @"nav_other_back_to_nav"
#define QM_NAV_EXIT_SECOND_ROUND                          @"nav_quit_nav"
#define QM_NAV_DELETE_POI_SECOND_ROUND                    @"nav_delete_poi_second_round"
#define QM_NAV_DELETE_POI_SUCCESS                         @"nav_delete_poi_success"
#define QM_NAV_CANCEL_DELETE_POI                          @"nav_cancel_delete_poi"
#define QM_NAV_DELETE_NO_POI                              @"nav_delete_no_poi"
#define QM_NAV_MODE_SIMPLE                                @"nav_tts_mode_change_concise_mode_sucess"
#define QM_NAV_MODE_DETAIL                                @"nav_tts_mode_change_detail_mode_sucess"
#define QM_WALK_CYCLE_TO_PAGE                             @"walk_cycle_to_page"
#define QM_WALK_CYCLE_SEE_YOU                             @"walk_cycle_see_you"
#define QM_WALK_CYCLE_CONTINUE_NAV                        @"walk_cycle_continue_nav"
#define QM_WALK_CYCLE_EXIT_SECOND_ROUND                   @"walk_cycle_exit_second_round"
#define QM_WALK_CYCLE_GLOBAL_SOUND_TURN_ON                @"walk_cycle_sound_turn_on"
#define QM_WALK_CYCLE_GLOBAL_SOUND_TURN_OFF               @"walk_cycle_sound_turn_off"
#define QM_WALK_CYCLE_OPEN_OVER_VIEW                      @"walk_cycle_open_overview"
#define QM_WALK_CYCLE_CLOSE_OVER_VIEW                     @"walk_cycle_close_overview"
#define QM_WALK_CYCLE_AR_WALK_DING_DANG_DISABLE           @"walk_cycle_AR_walk_dingdang_disable"
#define QM_WALK_CYCLE_AR_AV_AUTH                           @"walk_cycle_ar_av_auth"
#define QM_WALK_START_AND_END_POINT_EQUAL                 @"walk_start_end_point_equal"
#define QM_CYCLE_START_AND_END_POINT_EQUAL                    @"cycle_start_end_point_equal"
#define QM_FOLLOW_ROUTE_SEE_YOU                           @"follow_route_see_you"
#define QM_FOLLOW_ROUTE_EXIT_SECOND_ROUND                 @"follow_route_exit_second_round"
#define QM_FOLLOW_ROUTE_CONTINUE_NAV                      @"follow_route_continue_nav"
#define QM_COMMUTE_CONFIRM_HOME                           @"commute_comfirm_home"
#define QM_COMMUTE_CONFIRM_COMPANY                        @"commute_comfirm_company"
#define QM_COMMUTE_CONFIRM_OK                             @"commute_comfirm_ok"
#define QM_DRIVE_4_STOP_4_DISABLE                         @"drive_4_stop_4_disable"
#define QM_COMMON_DINGDANG_UNSUPPORT                      @"common_dingdang_unsupport"

#define QM_DINGDANG_EXIT                                    @"dingdang_exit"

#define QM_NAV_DRIVE_MODE        @"新能源车"

static NSString * const QMVoiceAssitantTypePassCheckHelperDisable = @"pass_check_helper_disable";

/****************** End ********************/


typedef NS_ENUM(NSUInteger, QMSILTimeType) {
    QMSILTimeTypeUnknown,
    QMSILTimeTypeCommon,
    QMSILTimeTypeNavi,
    QMSILTimeTypeVAD,   //叮当面板VAD闭合时间
    QMSILTimeTypeAudioFeedBack  //用户语音反馈VAD闭合时间
};

@class QMVoiceAssistantConfigManager;
@protocol QMVoiceAssistantConfigManagerDelegate <NSObject>

/// 云控配置更新回调
- (void)cloudControlConfigDidUpdate;

@end


@class QMTTSContent;
@interface QMVoiceAssistantConfigManager : NSObject

@property (nonatomic, weak) id<QMVoiceAssistantConfigManagerDelegate> delegate;

/**
 语音面板推荐词
 */
- (NSString *)guideWordForPage:(NSString *)page;

/**
 纠正TTS引导语
 */
- (NSString *)correctTTSGuideForPage:(NSString *)page;

/**
 首次启动引导语
 */
- (NSString *)firstLanuchGuide;
/**
 心情主题引导词
 */
- (NSString *)guidWordForFeeling:(NSInteger)themeId;
/**
 随机获取数组中的一个元素
 */
+ (id)getRandomObjectFromArray:(NSArray *)array;

/**
 根据类型获取超时时间，单位是毫秒
 */
- (NSInteger)getSILTimeOutValueWithType:(QMSILTimeType)type;

/**
 获取界面跳转的 URL
 */
- (NSString *)getGotoPageURLWithPage:(NSString *)page;

/**
 获取用于拼接 TTS 的 format 字符串
 
 @param type format 类型
 @param count %@ 占位符的数目
 */
- (NSString *)ttsFormatWithType:(NSString *)type placeholderCount:(NSInteger)count;

/**
 获取用于拼接 TTS 的 format 字符串，%@ 占位符数目为 0
 */
- (NSString *)ttsFormatWithType:(NSString *)type;

/**
 获取叮当唤醒后，开始收音前的提示音
 */
- (QMTTSContent *)reminderTTSContentWithType:(NSString *)type;

/**
 获取特色唤醒词唤醒后的播报内容
 */
- (QMTTSContent *)wakeupTTSContentWithKeyword:(NSString *)keyword;

/**
 获取特色唤醒词唤醒后的跳转 URL
 */
- (NSString *)wakeupQQMapURLWithKeyword:(NSString *)keyword;

/**
 是否允许特色唤醒词唤醒，后台不配置默认允许唤醒
 */
- (BOOL)enableWakeupKeyword:(NSString *)keyword;

/**
 叮当唤醒灵敏度
 */
- (CGFloat)getWakeupSensitive;

#pragma mark - Eggs

/**
 获取运营彩蛋数据
 */
- (NSDictionary *)eggsWithSlotName:(NSString *)slotName keyword:(NSString *)keyword;

/**
 获取运营彩蛋中配置的 TTS 数据
 */
- (QMTTSContent *)eggsTTSContentWithConfig:(NSDictionary *)config;

#pragma mark - Wakeup Voice Upload

/**
 是否允许保存并上传唤醒语音
 */
- (NSString *)enableSavingWakeupVoice;

/**
 允许唤醒音频上传的网络类型，
 */
- (NSString *)uploadWakeupVoiceNetType;

/**
 允许本地保存的音频数目
 */
- (NSString *)getMaxWakeupVoiceFileCount;

/**
 语音转文字一次给后台上传的语音大小，默认值1000，单位：byte
 */
- (NSString *)getVoicePeerSize;

/**
 叮当语义的技能版本号，默认是 0.0.0.0
 */
- (NSString *)getSkillVersion;

/**
 切词资源文件路径，如果资源文件没下载成功，返回 nil
 */
- (NSString *)wordSegmentResourcePath;

/**
 切词资源文件是否已正常下载
 */
- (BOOL)isWordSegmentResourceDownloaded;

/**
 是否允许切词功能，默认允许
 */
- (BOOL)wordSegmentEnable;

#pragma mark - Route Tag

/**
 路线标签相关配置
 */
- (NSDictionary *)routeTagConfig;

/**
 路线偏好相关配置
 */
- (NSDictionary *)preferenceTagConfig;

#pragma mark - Correct ASR

/**
 ASR 纠错相关配置
 */
- (NSArray *)getCorrectASRWithPage:(NSString *)page;

#pragma mark - Wakeup Model

/**
 当前用的唤醒模型文件完整路径
 */
- (NSString *)wakeupModelFilePath;

/**
 当前用的唤醒模型文件名称
 */
- (NSString *)getWakeupModelName;

#pragma mark - VAD Model

/**
 当前用的 VAD 模型文件完整路径
 */
- (NSString *)vadModelFilePath;

#pragma mark - Answer Skill

/**
 当前是否有云控 answer 数据
 */
- (BOOL)hasAnswerSkills;

/**
 当前云控支持的全部领域和意图
 */
- (NSArray *)getAllAnswerSkills;

/**
 可以播 answer 的技能列表
 */
- (NSArray *)getAnswerSkillsWithPage:(NSString *)page;

#pragma mark - Semantic Version

/**
语义版本，默认值是 1.0.0.0
*/
- (NSString *)semanticVersion;

/**
检索接口版本，默认是旧版本（值为1）
*/
- (NSInteger)poiSearchVersion;

/**
 特定的领域和意图在是否包含在特定的技能列表里
 */
- (BOOL)isValidAnswerDomain:(NSString *)domain intent:(NSString *)intent withSkills:(NSArray *)skills;

@end
