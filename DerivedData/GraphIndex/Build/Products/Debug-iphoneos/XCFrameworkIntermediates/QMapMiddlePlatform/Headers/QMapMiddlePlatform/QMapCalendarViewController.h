//
//  QMapCalendarViewController.h
//  QMapMiddlePlatform
//
//  Created by admin on 2021/2/7.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMViewController.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMapCalendarType) {
    QMapCalendarTypeDate,           // 选择1天
    QMapCalendarTypeDateRange,      // 选择日期范围
};

@class QMapCalendarViewController;
@protocol QMapCalendarViewControllerDelegate <NSObject>
@optional

/// 自定义日历页面标题
/// @param calendarVC 日历页面实例
- (NSString *)navBarTitleForCalendarViewController:(QMapCalendarViewController *)calendarVC;


///  自定义日期标签文案
/// @param calendarVC 日历页面实例
/// @param date 需要定义的日期
- (NSString *)calendarViewController:(QMapCalendarViewController *)calendarVC dateTagStringForDate:(NSDate *)date;

/// 自定义日期是否可用
/// @param calendarVC 日历页面实例
/// @param date 需要自定义的日期
- (BOOL)calendarViewController:(QMapCalendarViewController *)calendarVC enableForDate:(NSDate *)date;

/// 自定义日期是否能被选择
/// @param calendarVC 日历页面实例
/// @param date 需要自定义的日期
- (BOOL)calendarViewController:(QMapCalendarViewController *)calendarVC canSelectedForDate:(NSDate *)date;

/// 选中日期的回调
/// @param calendarVC 日历页面实例
/// @param date 选中的日期
- (void)calendarViewController:(QMapCalendarViewController *)calendarVC selectedDate:(NSDate *)date;

/// 选中日期的开始日期
/// @param calendarVC 日历页面实例
/// @param beginDate 选中的开始日期
- (void)calendarViewController:(QMapCalendarViewController *)calendarVC didSelectedBeginDate:(NSDate *)beginDate;

/// 选中日期的结束日期
/// @param calendarVC 日历页面实例 
/// @param endDate 选中的结束日期
- (void)calendarViewController:(QMapCalendarViewController *)calendarVC didSelectedEndDate:(NSDate *)endDate;

/// 选中日期的开始、结束日期
/// @param calendarVC 日历页面实例
/// @param beginDate 选中的开始日期
/// @param endDate 选中的结束日期
- (void)calendarViewController:(QMapCalendarViewController *)calendarVC selectedBeginDate:(NSDate *)beginDate endDate:(NSDate *)endDate;

/// 取消选择日期
/// @param calendarVC 日历页面实例
- (void)calendarViewControllerCancelSelectedDate:(QMapCalendarViewController *)calendarVC;

@end

@interface QMapCalendarViewController : QMViewController
@property (nonatomic, weak) id<QMapCalendarViewControllerDelegate> delegate;
@property (nonatomic, readonly, strong) NSDate *beginDate;
@property (nonatomic, readonly, strong) NSDate *endDate;
- (instancetype)initWithType:(QMapCalendarType)type
                   beginDate:(NSDate *)beginDate
                     endDate:(NSDate *)endDate
                selectedDate:(NSDate *)selectedDate;

- (instancetype)initWithType:(QMapCalendarType)type
                   beginDate:(NSDate *)beginDate
                     endDate:(NSDate *)endDate
          selectedBeiginDate:(NSDate *)selectedBeiginDate
             selectedEndDate:(NSDate *)selectedEndDate;
@end

NS_ASSUME_NONNULL_END
