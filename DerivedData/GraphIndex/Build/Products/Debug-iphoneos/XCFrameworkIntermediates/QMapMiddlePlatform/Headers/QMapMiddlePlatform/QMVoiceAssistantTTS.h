//
//  QMVoiceAssistantTTS.h
//  SOSOMap
//
//  Created by foog<PERSON><PERSON>(王中周) on 2018/7/17.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/iFlyTTSManager.h>


typedef enum : NSUInteger {
    QMTTSTypeUnknown,
    QMTTSTypeTTS,
    QMTTSTypeMP3,
} QMTTSType;

@interface QMTTSContent : NSObject

@property (nonatomic) QMTTSType type;
@property (nonatomic, copy) NSString *tts;

+ (instancetype)contentWithTTS:(NSString *)tts; // 默认是 QMTTSTypeTTS
+ (instancetype)contentWithTTS:(NSString *)tts type:(QMTTSType)type;

@end


@interface QMVoiceAssistantTTS : NSObject

#pragma mark - Play TTS

/// 正在播报
+ (BOOL)isPlaying;

/**
 智能语音播放 TTS，默认展示 waitingView 浮窗，但不二轮收音
 */
+ (BOOL)playSpeak:(NSString *)speak;

/**
 智能语音延迟播放 TTS，默认展示 waitingView 浮窗，但不二轮收音
 */
+ (void)playSpeak:(NSString *)speak afterDelay:(CGFloat)delay;

/**
 智能语音播放 TTS

 @param speak TTS 文本
 @param showWaitingView 是否展示 waitingView 浮窗
 @param secondFound 是否二轮收音窗
 @param block TTS 播报结束回调
 */
+ (BOOL)playSpeak:(NSString *)speak showWaitingView:(BOOL)showWaitingView secondRound:(BOOL)secondRound finishBlock:(AudioPlayerFinishedBlock)block;

/**
 智能语音 delay 方式播放 TTS
 */
+ (void)playSpeak:(NSString *)speak
  showWaitingView:(BOOL)showWaitingView
      secondRound:(BOOL)secondRound
      finishBlock:(AudioPlayerFinishedBlock)block
       afterDelay:(CGFloat)delay;

#pragma mark - Play MP3

/**
 智能语音播放指定路径的MP3文件，默认展示 waitingView 浮窗，但不二轮收音
 */
+ (BOOL)playMP3WithPath:(NSString *)path;

/**
 智能语音播放开始展现面板的 MP3
 */
+ (void)playStartMP3WithFinishedBlock:(AudioPlayerFinishedBlock)finishedBlock;

/**
 智能语音播放指定路径的MP3文件
 
 @param speak MP3 路径
 @param showWaitingView 是否展示 waitingView 浮窗
 @param secondFound 是否二轮收音窗
 @param block TTS 播报结束回调
 */
+ (BOOL)playMP3WithPath:(NSString *)path
        showWaitingView:(BOOL)showWaitingView
            secondRound:(BOOL)secondRound
            finishBlock:(AudioPlayerFinishedBlock)block;

#pragma mark - Play Data

/**
 播放二进制文件
 */
+ (BOOL)playData:(NSData *)data;

#pragma mark - Play TTSContent

/**
 延迟播放 QMTTSContent 对象
 */
+ (void)playTTSContent:(QMTTSContent *)content afterDelay:(CGFloat)delay;

/**
 播放 QMTTSContent 对象
 */
+ (void)playTTSContent:(QMTTSContent *)content finishBlock:(AudioPlayerFinishedBlock)block;

/**
 播放 QMTTSContent 对象
 */
+ (void)playTTSContent:(QMTTSContent *)content
       showWaitingView:(BOOL)showWaitingView
           secondRound:(BOOL)secondRound
           finishBlock:(AudioPlayerFinishedBlock)block;

#pragma mark - Stop

/**
 停止所有的智能语音播放
 */
+ (BOOL)stop;

@end
