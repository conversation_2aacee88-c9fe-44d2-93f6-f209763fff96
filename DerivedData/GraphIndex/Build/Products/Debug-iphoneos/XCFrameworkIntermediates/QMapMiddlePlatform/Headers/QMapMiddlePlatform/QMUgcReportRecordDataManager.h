//
//  QMUgcReportRecordDataManager.h
//  SOSOMap
//
//  Created by leon<PERSON><PERSON> on 2018/1/18.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportMarkerData.h"
#import "QMUgcReportRecordData.h"

@class QMUgcReportRecordDataManager;

@protocol QMUgcReportRecordDataManagerDelegate <NSObject>
- (void)dataManager:(QMUgcReportRecordDataManager *)dataManager didGetRecords:(NSArray*)records error:(NSError*)error hasMore:(BOOL)hasMore;
@end


@interface QMUgcReportRecordDataManager : NSObject

@property (nonatomic, weak) id<QMUgcReportRecordDataManagerDelegate> delegate;
@property (nonatomic, strong) NSMutableArray<QMUgcReportRecordData*> *records;

QMSharedInstance_H(QMUgcReportRecordDataManager)

- (void) getRecords:(long)timeStamp;
- (void) reportRecordsHasRead:(NSArray *)records;
- (void) startRefreshRecord;
- (void) stopRefreshRecord;
- (void) pauseRefreshData; //暂停刷新数据
- (void) resumeRefreshData; //恢复刷新数据
@end
