//
//  QMUgcReportMarkerData.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/18.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapFoundation/QMapFoundation.h>
#import "QMUgcReportTypeData.h"
#import "QMUgcReportMarkerDataLiveInfo.h"

@interface QMUgcReportMarkerData : NSObject

@property(nonatomic,strong) NSString* infoCode;
@property(nonatomic,strong) NSString* originId;
@property(nonatomic,assign) CLLocationCoordinate2D markerPosition;
@property(nonatomic,assign) QMUgcReportType type;
@property(nonatomic,strong) NSString* title;
@property(nonatomic,strong) NSString* desc;
@property(nonatomic,strong) NSArray* laneType;
@property(nonatomic,strong) NSString* infoSource;
@property(nonatomic,assign) long updateTime;
@property(nonatomic,assign) long startTime;
@property(nonatomic,assign) long endTime;
@property(nonatomic,strong) NSString* imgUrl;
@property(nonatomic,assign) NSUInteger usefulNum;
@property(nonatomic,assign) NSUInteger uselessNum;
@property(nonatomic,copy) NSString* detail;
/// 直播相关信息，最终直播/视频 url 相关的请求，以及直播的相关判断在 QMUGCLiveInfoDataUtils 中 QMRouteEventItem 和 QMUgcReportMarkerData 一样。
@property (nonatomic, strong) QMUgcReportMarkerDataLiveInfo *liveInfo;
/// 拥堵特征信息
@property (nonatomic, copy) NSArray<NSDictionary *> *jamDetailArr;
/// 拥堵标签
@property (nonatomic, copy) NSArray<NSString *> *jamTagArr;

@property(nonatomic,strong) NSString* showStartTime;
@property(nonatomic,strong) NSString* showEndTime;
/// 点事件优先级
@property (nonatomic, assign) NSInteger priority;

@end
