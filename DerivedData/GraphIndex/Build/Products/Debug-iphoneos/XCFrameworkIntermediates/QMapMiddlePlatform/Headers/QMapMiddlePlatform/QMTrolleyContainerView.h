//
//  QMTrolleyContainerView.h
//  QMapMiddlePlatform
//
//  Created by leonwzwang on 2023/6/6.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class QMTrolleyContainerView;
@protocol QMTrolleyContainerViewDelegate<NSObject>
@optional

- (void)torlleyViewOnDragTagClick:(QMTrolleyContainerView *)torlleyView;

- (void)torlleyViewWillBeginMove:(QMTrolleyContainerView *)torlleyView;

- (BOOL)torlleyView:(QMTrolleyContainerView *)torlleyView canMoveForChangeToOffsetY:(CGFloat)offsetY;

- (void)torlleyViewDidEndMove:(QMTrolleyContainerView *)torlleyView;

- (void)torlleyView:(QMTrolleyContainerView *)torlleyView moveIntention:(BOOL)isUp speedY:(CGFloat)speedY;

- (void)didTimeOutAfterPreviewTouch;

@end

@interface QMTrolleyContainerView : UIView

@property (nonatomic, weak) id<QMTrolleyContainerViewDelegate> delegate;
/// 最后触摸后的 恢复跟随态的时间 默认 -1
@property (nonatomic) NSInteger timeForAfterTouch;

@property (nonatomic) BOOL isOn;

- (void)loadingView:(UIView *)view;

- (void)setDragTapAccessibilityLabel:(NSString *)label;
/// 移除定时器
- (void)destoryTimeOutCaculate;
/// 重置定时器
- (void)resetTimeOutCaculate;

@end

NS_ASSUME_NONNULL_END
