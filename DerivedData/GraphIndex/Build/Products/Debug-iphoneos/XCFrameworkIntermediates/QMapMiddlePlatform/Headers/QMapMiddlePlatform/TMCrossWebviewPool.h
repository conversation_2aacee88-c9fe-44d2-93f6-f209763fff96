//
//  TMCrossWebviewPool.h
//  QMapMiddlePlatform
//
//  Created by wyh on 2022/3/28.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 Hippy -> h5 通信的webview注册机
 */
@interface TMCrossWebviewPool : NSObject

+ (instancetype)pool;

- (void)registerWebview:(WKWebView *)webview forClientId:(NSString *)clientId;

- (void)unregisterWebviewForClientId:(NSString *)clientId;

- (WKWebView *)webviewForClientId:(NSString *)clientId;

- (void)enumerateAllWebviews:(void(^)(WKWebView *webview, BOOL *stop))enumerateBlock;

@end

NS_ASSUME_NONNULL_END
