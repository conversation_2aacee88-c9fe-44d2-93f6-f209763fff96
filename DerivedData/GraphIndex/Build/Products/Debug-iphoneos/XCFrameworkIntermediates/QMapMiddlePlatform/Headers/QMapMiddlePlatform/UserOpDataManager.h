//
//  UserOpDataManager.h
//  QQMap
//
//  Created by kang zhou on 8/28/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <BeaconAPI_Base/BeaconEvent.h>

//2. user operation data
#define M_<PERSON> @"I_M_C"
#define M_<PERSON> @"I_M_S"
#define M_R @"I_M_R"
#define M_DOWN @"I_M_DOWN"
#define M_LOC @"I_M_LOC"
#define M_L_U @"I_M_L_U"
#define M_L_F @"I_M_L_F"
#define M_L_C @"I_M_L_C"
#define M_S_EDIT @"I_M_S_EDIT"
#define M_SPE @"map_poi_sb_v"
#define M_MORE_SET @"I_M_MORE_SET"
#define M_SVR @"I_M_SVR"
#define M_OPT @"I_M_OPT"
#define M_CPS @"I_M_CPS"
#define M_CIRCUM   @"I_M_CIRCUM"
#define M_ZOOM_IN  @"I_M_ZOOM_IN"   //erased
#define M_ZOOM_OUT @"I_M_ZOOM_OUT"  //erased
#define M_ZOOM_CLK @"I_M_ZOOM_CLK"
#define M_ZOOM_SCALE @"I_M_ZOOM_SCALE"
#define M_ZOOM_CHANGE @"I_M_ZOOM_CHANGE"
#define M_SWITCH_ROUTE @"I_M_SWITCH_REOUTE"
#define M_M_BAR @"I_M_M_BAR"
#define M_R_BAR @"I_M_R_BAR"
#define M_D_BAR @"I_M_D_BAR"
#define M_ME_BAR @"I_M_ME_BAR"
#define M_GROUPON   @"I_M_GPO"
#define M_COUPON    @"I_M_CPO"
#define M_CALL_TAXI @"I_M_C_T"
#define M_LOC_STATUS @"I_M_LOC_STATUS"
#define M_POST_CARD @"I_M_P_C"
#define M_POCKET_SET @"I_M_POCKET"


//main layer options
#define ML_SAT_ON @"map_tc_l_sm_o"
#define ML_SAT_OFF @"map_tc_l_sm_c"

#define ML_SV_ON @"I_ML_SV_ON"
#define ML_SV_OFF @"I_ML_SV_OFF"


#define ML_FAV_ON @"I_ML_FAV_ON"
#define ML_FAV_OFF @"I_ML_FAV_OFF"

//gestures
#define G_2SWP_D @"I_G_2SWP_D"
#define G_2SWP_U @"I_G_2SWP_U"
#define G_2TAP @"I_G_2TAP"
#define G_DTAP @"I_G_DTAP"
#define G_R @"I_G_R"
#define G_P_I @"I_G_P_I"
#define G_P_O @"I_G_P_O"
#define G_P_M @"I_G_P_M"
#define G_DOU_TAP_PAN @"I_G_DOU_TAP_PAN"

//alertviews
#define ALT_UP_OK @"I_ALT_UP_OK"
#define ALT_UP_C @"I_ALT_UP_C"
#define ALT_OD_OK @"I_ALT_OD_OK"  //offline city data
#define ALT_OD_C @"I_ALT_OD_C"

//search
#define S_I_PLA @"I_S_I_PLA"
#define S_I_TYP @"I_S_I_TYP"
#define S_I_SPE @"I_S_I_SPE"
#define S_I_SPE_R @"I_S_I_SPE_R"
#define S_I_BOOK @"I_S_I_BOOK"  //erased
#define S_I_SG_S @"I_S_I_SG_S"
#define S_I_SG_U @"map_poi_ps_upin"
#define S_I_QC @"I_S_I_QC"
#define S_I_KB_GO @"map_poi_ps_s_kb"
#define S_I_C @"I_S_I_C"    //erased
#define S_I_FAV @"I_S_I_FAV"

#define SPE_CANCEL_SPEAK @"I_SPE_C_S"
#define SPE_CANCEL_RECOGNIZING @"I_SPE_C_R"
#define SPE_CANCEL_TIMEOUT @"I_SPE_C_T"
#define SPE_CANCEL_SEARCH @"I_SPE_C_SE"
#define SPE_RETRY @"I_SPE_R"
#define SPE_HELP @"I_SPE_HELP"
#define MAP_POI_MAIN_SB @"map_poi_main_sb"


//cirum
#define S_C_BCK @"I_S_C_BCK"
#define S_C_MORE @"I_S_C_MORE"
#define S_C_A_BCK   @"I_S_C_A_BCK"
#define S_C_A_R @"I_S_C_A_R"
#define S_C_A_R_ITEM @"I_S_C_A_R_ITEM"
#define S_C_A_E @"I_S_C_A_E"
#define S_C_A_E_ITEM @"I_S_C_A_E_ITEM"
#define S_C_A_T @"I_S_C_A_T"
#define S_C_A_T_ITEM @"I_S_C_A_T_ITEM"
#define S_C_A_B @"I_S_C_A_B"        //erase
#define S_C_A_H @"I_S_C_A_H"
#define S_C_A_H_ITEM @"I_S_C_A_H_ITEM"
#define S_C_A_S @"I_S_C_A_S"        //erase
#define S_C_A_M @"I_S_C_A_M"        //erase
#define S_C_A_ED    @"I_S_C_A_ED"   //erase
#define S_C_A_L @"I_S_C_A_L"
#define S_C_A_L_ITEM @"I_S_C_A_L_ITEM"
#define S_C_KB_GO @"I_S_C_KB_GO"    //erase
#define S_C_SEARCH @"I_S_C_SEARCH"
#define NEAR_ENTER @"near_enter"
#define NEAR_INPUT @"near_srch_cl"
#define NEAR_LIST_SHOW @"near_rk_e"
#define NEAR_LIST_CLICK @"near_rk_cl"

#define NEAR_HOT_CLICK @"near_hot_s_cl"
#define NEAR_NORMAL_CLICK @"near_gen_s_cl"
#define NEAR_LIST_POI_CLICK @"near_rk_poilist_cl"

//search result
#define S_R_MAP @"I_S_R_MAP"
#define S_R_LIST @"I_S_R_LIST"
#define S_R_LIST_B @"I_S_R_LIST_B"
#define S_R_NP @"map_poi_pr_next"
#define S_R_PP @"map_poi_pr_pre"
#define S_R_BCELL @"I_S_R_BCELL"
#define S_R_BMORE @"I_S_R_BMORE"
#define S_R_BUSL @"I_S_R_BUSL"
#define S_R_B_NP @"I_S_R_B_NP"
#define S_R_B_PP @"I_S_R_B_PP"
#define S_R_CL_BCK @"I_S_R_CL_BCK"
#define S_R_LIST_BCK @"map_poi_pr_sw_b"
#define S_R_CL_CLK @"I_S_R_CL_CLK"
#define S_PD_CLK @"I_S_PD_CLK"   //erased
#define S_R_CLK @"I_S_R_CLK"
#define S_R_SQ_S @"I_S_R_SQ_S"  //erased
#define S_R_SQ_U @"I_S_R_SQ_U"  //erased
#define S_R_BCK @"map_poi_ps_sw_b"
#define S_R_F_D @"I_S_C_F_D"
#define S_R_F_C @"I_S_C_F_C"
#define S_R_F_S @"I_S_C_F_S"
#define S_R_M @"I_S_R_M"
#define S_S_EDIT @"I_S_S_EDIT"
#define S_R_H_C @"I_S_R_H_C"

#define S_CNT @"I_S_CNT"    //erased

//poi detail
#define PD_WAP @"I_PD_WAP"  //erased    
#define PD_CNT @"I_PD_CNT"
#define PD_TMB @"I_PD_TMB"
#define PD_BEG @"I_PD_BEG"
#define PD_END @"I_PD_END"
#define PD_C @"map_poi_p_c_s"
#define PD_DET_CLK @"map_poi_p_d_l"
#define PD_FAV_CLK @"I_PD_FAV_CLK"
#define PD_DST_CLK @"map_poi_sum_th"
#define PD_FAV @"map_poi_pd_f"
#define PD_FAV_C @"map_poi_pd_f_c"
#define PD_CON @"I_PD_CON"          //contact, erased
#define PD_SMS @"map_poi_pd_s_sms"
#define PD_COPY_URL @"map_poi_s_copy"
#define PD_SINA_WB  @"map_poi_s_sina_b"
#define PD_WX @"map_poi_pd_s_w_f"
#define PD_TL @"map_poi_pd_s_w_c"
#define PD_QQ @"map_poi_s_qq"
#define PD_TxWB @"map_poi_pd_s_w_b"
#define PD_QZONE @"I_PD_QZONE"
#define PD_RB @"I_PD_RB"            //round bus, erased
#define PD_CMT_M @"map_poi_p_c_l"
#define PD_BUS @"I_PD_BUS"
#define PD_SH @"map_poi_pd_s"
#define PD_ST @"map_poi_pd_s_v"
#define PD_WB @"I_PD_WB"            //used only in old microBlogViewController
#define PD_WB_S @"I_PD_WB_S"        //used only in old microBlogViewController
#define PD_WEIBO_CANCEL @"I_PD_WB_C"  //used only in old microBlogViewController
#define PD_TEL @"map_poi_pd_p"
#define PD_BUSC @"I_PD_BUSC"        //bus circum, erased
#define PD_L @"I_PD_L"
#define PD_SV @"I_PD_SV"
#define PD_NAV @"map_poi_pd_nav"
#define PD_RT @"I_PD_RT"
#define PD_EXTEND @"map_poi_mid_up"
#define PD_EXTEND_HALF @"map_poi_small_e"
#define PD_EXTEND_LARGE @"map_poi_large_e"
#define PD_EXTEND_FULL @"map_poi_pr_lar_e"
#define P_ZOOM_CLK @"I_P_ZOOM_CLK"
#define PD_HALF_SHRINK @"map_poi_pr_map_cl"
#define PD_MINI_SHOW @"I_PD_MI_SH"
#define PD_HEADER_SHRINK @"I_PD_HD_SK"
#define PD_DRAGDOWN_MINI @"map_poi_mini_down"
#define PD_DRAGDOWN_HALF @"map_poi_half_down"
#define PD_DRAGDOWN_LARGE @"map_poi_large_down"
#define PD_DRAGDOWN_FULL @"map_poi_full_down"
#define PD_PHONE_SHOW   @"map_poi_sum_e"
#define PD_GROUPON_SHOW @"map_poi_yh_e"
#define PD_MORECOMM_SHOW @"map_poi_pr_com_e"
#define PD_DETAIL_SHOW @"I_PD_DT_SH"

//poi mimi
#define PM_CARD_DRAG @"map_poi_pr_h_sw"

//busline
#define BL_PD_RS @"I_BL_PD_RS"
#define BL_PD_CELL @"I_BL_PD_CELL"
#define BL_PD_DT @"I_BL_PD_DT"
#define BL_MAP   @"I_BL_MAP"

//route
#define R_B_CNT @"I_R_B_CNT"    //erase
#define R_TYP     @"I_R_TYP"
#define R_TYP_SEARCH  @"I_R_TYP_S"
#define R_TYP_RESULT_LIST_CAR  @"I_R_TYP_R_LIST_CAR"    //erased
#define R_TYP_RESULT_LIST_BUS  @"I_R_TYP_R_LIST_BUS"    //erased
#define R_TYP_RESULT_LIST_WALK  @"I_R_TYP_R_LIST_WK"
#define R_TYP_RESULT_MAP   @"I_R_TYP_R_MAP" //erased
#define R_S_CANCEL @"I_R_S_CA"  //erased
#define R_S_EXCHANGE @"I_R_S_EX"
#define R_CHOICE_BACK @"I_R_CH_BA"  //erase
#define R_WALK_SH_WEIXIN @"I_R_W_SH_WX"
#define R_WALK_SH_TL    @"I_R_W_SH_TL"
#define R_WALK_SH_QQ    @"I_R_W_SH_QQ"
#define R_WALK_SH_TXWB  @"I_R_W_SH_TWB"
#define R_WALK_SH_SWB   @"I_R_W_SH_SWB"
#define R_WALK_SH_SMS   @"I_R_W_SH_SMS"
#define R_WALK_SH_URL   @"I_R_W_SH_URL"
#define R_BUS_SH_WEIXIN @"I_R_B_SH_WX"
#define R_BUS_SH_TL    @"I_R_B_SH_TL"
#define R_BUS_SH_QQ    @"I_R_B_SH_QQ"
#define R_BUS_SH_TXWB  @"I_R_B_SH_TWB"
#define R_BUS_SH_SWB   @"I_R_B_SH_SWB"
#define R_BUS_SH_SMS   @"I_R_B_SH_SMS"
#define R_BUS_SH_URL   @"I_R_B_SH_URL"
#define R_CAR_SH_WEIXIN @"nav_dr_s_w_f"
#define R_CAR_SH_TL    @"nav_dr_s_w_c"
#define R_CAR_SH_QQ    @"nav_dr_s_qq"
#define R_CAR_SH_TXWB  @"nav_dr_s_w_b"
#define R_CAR_SH_SWB   @"nav_dr_s_sina_b"
#define R_CAR_SH_SMS   @"nav_dr_s_sms"
#define R_CAR_SH_URL   @"nav_dr_s_copy"


#define R_SHOW  @"I_R_SHOW"            //route segments show, erased
#define R_NEX @"I_R_NS"   //erased
#define R_PRE @"I_R_PS"   //erased
#define R_NEX_BUS   @"I_R_NS_BUS"   //erased
#define R_NEX_CAR   @"I_R_NS_CAR"   //erased
#define R_PRE_BUS   @"I_R_PS_BUS"   //erased
#define R_PRE_CAR   @"I_R_PS_CAR"   //erased
#define R_MAP @"I_R_MAP" //erased
#define R_MAP_CAR   @"I_R_MAP_CAR"  //erased
#define R_MAP_BUS   @"I_R_MAP_BUS"  //erased
#define R_LIST @"I_R_LIST"  //erased
#define R_LIST_BUS   @"I_R_LIST_BUS"    //erased
#define R_LIST_CAR   @"I_R_LIST_CAR" //erased
#define R_SMS      @"I_R_SMS"  // erased
#define R_SMS_CAR  @"nav_dr_sd"
#define R_SMS_BUS  @"I_R_SMS_BUS"
#define R_SMS_WALK @"I_R_SMS_WALK"
#define R_MAP_BUS_BACK @"I_R_M_BUS_BACK"
#define R_MAP_CAR_BACK @"nav_dr_op_sw_b"
#define R_MAP_WALK_BACK @"I_R_M_WALK_BACK"
#define R_CAR_PRE_BUTTON @"I_R_C_P_B"
#define R_CAR_NEXT_BUTTON @"I_R_C_N_B"
#define R_SIMULATE_NAVI  @"I_R_S_NAVI"
#define R_SIMULATE_NAVI_START  @"I_R_S_NAVI_START"
#define R_SIMULATE_NAVI_STOP  @"I_R_S_NAVI_STOP"
#define R_SIMULATE_NAVI_SPEED  @"I_R_S_NAVI_SPEED"
#define R_SIMULATE_NAVI_CANCEL  @"I_R_S_NAVI_CANCEL"

#define R_DETAIL_TABLE_SELECT @"I_R_D_TS"
#define R_DETAIL_NAVIG_BACK @"I_R_D_NB"  // erased
#define R_DETAIL_FAVORITE  @"I_R_D_FA"   // erased
#define R_DETAIL_FAVORITE_BUS @"I_R_D_FA_B"
#define R_DETAIL_FAVORITE_CAR @"I_R_D_FA_C" //erased
#define R_DETAIL_FAVORITE_WALK @"I_R_D_FA_W"
#define R_DETAIL_CAR_NAVI   @"I_R_D_C_N"    //erased
#define R_DETAIL_BUS_WALK   @"I_R_D_B_W"
#define R_DETAIL_BUS_BUS    @"I_R_D_B_B"
#define R_DETAIL_CAR_CLICK  @"nav_dr_r_uf"
#define R_DETAIL_CAR_PRE    @"nav_dr_sd_uf_pr"
#define R_DETAIL_BUS_WALK_ITEM @"I_R_B_W_I"
#define R_DETAIL_WALK_WALK_ITEM @"nav_wk_de"
#define R_BACK  @"I_R_BACK" // erased
#define R_LIST_BACK  @"I_R_L_BACK"  //erased
#define R_LIST_BUS_BACK @"I_R_L_B_BACK"
#define R_LIST_CAR_BACK @"nav_dr_rd_sw_b"
#define R_LIST_WALK_BACK @"nav_wk_m_b"
#define R_SECTION_TABLE_SELECT @"I_R_S_T_S"  //erased
#define R_MINIDETAIL_EXPAND_BUS @"I_R_M_E_B"

#define R_LIST_BUS_TAXI @"I_R_L_B_TAXI"
#define R_LIST_CAR_TAXI @"nav_dr_taxi"
#define R_LIST_WALK_TAXI @"nav_wk_taxi"

#define R_MINICARD_BUS_SWITCH @"I_R_MINI_B_S"
#define R_MINICARD_CAR_SWITCH @"nav_dr_uf_pr_s"
#define R_MINICARD_WALK_SWITCH @"I_R_MINI_W_S"

#define R_BUSEXPRESS  @"I_R_BUSE"   //erased
#define R_BUSLESSTRAN  @"I_R_BUSLT" //erased
#define R_BUSLESSWALK  @"I_R_BUSLW" //erased
#define R_BUSNOSUBWAY  @"I_R_BUSNS" //erased
#define R_CAREXPRESS  @"I_R_CARE"   //erased
#define R_CARESHORTDIST  @"I_R_CARSD" //erased
#define R_CARENOEXPRESS  @"I_R_CARNE"   //erased

#define R_SEARCH_FROM_KEYBOARD  @"I_R_S_F_K"    //erased
#define R_START_CHOICE_SELECT   @"I_R_S_C_S"    //erased
#define R_END_CHOICE_SELECT     @"I_R_E_C_S"
#define R_ROUTE_DETAIL_SELECT   @"I_R_R_D_S"    //erased
#define R_START_INPUT_CLICK     @"I_R_S_I_C"
#define R_BUS_START_RESULT      @"I_R_B_S_R"    //click under conditon of has result
#define R_BUS_END_RESULT       @"I_R_B_E_R"
#define R_CAR_END_RESULT       @"I_R_C_E_R"
#define R_WALK_START_RESULT     @"I_R_W_S_R"
#define R_WALK_END_RESULT       @"I_R_W_E_R"

#define R_START_FAV_CLICK       @"I_R_S_F_C"
#define R_END_FAV_CLICK         @"I_R_E_F_C"
#define R_START_MAP_CLICK       @"I_R_S_M_C"
#define R_END_MAP_CLICK         @"I_R_E_M_C"
#define R_START_VOICE_CLICK     @"I_R_S_V_C"
#define R_END_VOICE_CLICK       @"I_R_E_V_C"
#define R_START_CANCEL_CLICK    @"I_R_S_C_C"
#define R_END_CANCEL_CLICK      @"I_R_E_C_C"
#define R_START_HISTORY_CLICK   @"I_R_S_H_C"
#define R_END_HISTORY_CLICK     @"I_R_E_H_C"
#define R_MY_LOCATION_CLICK     @"I_R_M_L_C"
#define R_START_SEARCH_CLICK    @"I_R_S_S_C"
#define R_END_SEARCH_CLICK      @"I_R_E_S_C"
#define R_BUS_OPTION_CLICK      @"I_R_B_O_C"
#define R_BUS_ITEM_TOTAL        @"I_R_B_I_T"
#define R_BUS_ITEM_ONE          @"I_R_B_I_ONE"
#define R_BUS_ITEM_TWO          @"I_R_B_I_TWO"
#define R_BUS_ITEM_THREE        @"I_R_B_I_THREE"
#define R_BUS_ITEM_FOUR         @"I_R_B_I_FOUR"
#define R_BUS_ITEM_FIVE         @"I_R_B_I_FIVE"
#define R_CAR_OPTION_CLICK      @"I_R_C_O_C"
#define R_CAR_ITEM_TOTAL        @"nav_dr_s"
#define R_CAR_ITEM_ONE          @"I_R_C_I_ONE"
#define R_CAR_ITEM_TWO          @"I_R_C_I_TWO"
#define R_CAR_ITEM_THREE        @"I_R_C_I_THREE"
#define R_WALK_ITEM_CLICK       @"nav_wk_s_l"
#define R_ZOOM_CLK              @"I_R_ZOOM_CLK"
#define R_RESULT_SEGMENT_BUS    @"nav_bus_rs_ty"
#define R_BUS_SUGGEST_WALK      @"I_R_B_S_W"
#define R_BUS_SUGGEST_CAR       @"I_R_B_S_C"
#define R_CAR_OPTION_BCK        @"I_R_C_O_BCK"
#define R_BUS_OPTION_COMMEND    @"I_R_B_O_CM"
#define R_BUS_OPTION_TIME       @"I_R_B_O_TM"
#define R_BUS_OPTION_WALK       @"I_R_B_O_WK"
#define R_BUS_OPTION_TRANSFER   @"I_R_B_O_TF"
#define R_BUS_OPTION_SUBWAY     @"I_R_B_O_SU"
#define R_BUS_OPTION_BUS        @"I_R_B_O_BS"
#define R_BUS_OPTION_BCK        @"I_R_B_O_BCK"
#define R_CAR_OPTION_FEE        @"nav_dr_s_nfee"
#define R_CAR_OPTION_FREE       @"nav_dr_s_nog"
#define R_CAR_OPTION_TIME       @"nav_dr_s_time"
#define R_CAR_OPTION_NEAR       @"I_R_C_O_NR"
#define R_BUS_WALK_SUGGEST      @"I_R_B_W_S"
#define R_BUS_CAR_SUGGEST       @"I_R_B_C_S"

#define ROUTE_SEARCH_ERR       @"I_ROUTE_SEARCH_ERR"
#define POI_SEARCH_ERR       @"I_POI_SEARCH_ERR"
#define LOCATION_FAILED @"I_LOCATION_FAILED"


//main settings...
#define MR_CLR @"I_MR_CLR"      //erased
#define MR_TFC @"map_tc_tfc"
#define MR_TFN @"map_tc_tfn"
#define MR_RS @"I_MR_RS"
#define MR_RS_C @"I_MR_RS_C"
#define MR_RT_BUS @"I_MR_RT_BUS"
#define MR_FM @"I_FM_CNT"
#define MR_FM_SEG @"I_FM_SEG"
#define MR_FM_EDIT @"I_FM_EDIT"
#define MR_FM_LGI @"I_LI_CNT"
#define MR_FM_LGO @"I_LO_CNT"
#define MR_FM_LGI_OK @"I_LI_OK"
#define MR_FM_SN @"I_SYN_CNT"
#define MR_FM_CSN @"I_SYN_C_CNT"
#define MR_ABOUT @"I_MR_ABOUT"
#define MR_FB @"I_MR_FB"
#define MR_SCRN_ON @"I_MR_SCRN_ON"
#define MR_DC @"I_MR_DC"
#define MR_DC_C @"I_MR_DC_C"
#define MR_AL_ON @"I_MR_AL_ON"
#define MR_SV @"I_MR_SV"
#define MR_ABT_UP @"I_MR_ABT_UP"
#define MR_CMT @"I_MR_CMT"
#define MR_NEW_FT @"I_MR_NEW_FT"
#define MR_APP_R @"I_MR_APP_R"
#define MR_MZ_ON @"I_MR_MZ_ON"
#define MR_REC_CLL @"I_MR_REC_CLL"
#define MR_REC_SMS @"I_MR_REC_SMS"
#define MR_REC_EML @"I_MR_REC_EML"
#define MR_REC_WBO @"I_MR_REC_WBO"
#define MR_CLEAN_CACHE_OF_SV @"I_MR_CL_CA"
#define MR_WBO_SHARE @"I_MR_WBO_SHARE"
#define MR_WBO_CANCEL @"I_MR_WBO_CANCEL"
#define MR_USE_LEFT_HAND @"I_MR_U_L_H"

//poi picker
#define R_SEL_F @"I_R_SEL_F"    //erase
#define R_SEL_S @"I_R_SEL_S"    //erase
#define R_SEL_C @"I_R_SEL_C"    //erase

// app log
#define APP_ST_ @"I_APP_ST"
#define APP_ET_ @"I_APP_ET"
#define APP_DURATION @"I_APP_D"
#define APP_LAST_CITY @"I_APP_LAST_C"
#define APP_LAST_LC  @"I_APP_LAST_LC"

//buble buttons
#define BB_L_CIR @"I_BB_L_CIR"
#define BB_R_RT @"I_BB_R_RT"

#define TA_CLK_CNT @"I_TA_CLK_CNT"
#define BS_CLK_CNT @"I_BS_CLK_CNT"
#define BS_BPD_CLK @"I_BS_BPD_CLK"
#define PS_BPD_CLK @"map_poi_pr_f"
#define MAP_L_P @"I_MAP_L_P"
#define R_ICON_CLK @"I_R_ICON_CLK"  //erased

// 离线地图
#define OFFLINE_SEGMENT_CHANGE @"I_OL_SE_CH"
#define OFFLINE_CLICK_DOWN @"I_OL_CL_DO"
#define OFFLINE_CLICK_PAUSE @"I_OL_CL_PA"
#define OFFLINE_AS_DOWN @"I_OL_AS_DO"
#define OFFLINE_AS_CANCELDOWN @"I_OL_AS_CA"
#define OFFLINE_AS_OPENMAP @"I_OL_AS_OP"
#define OFFLINE_FLIP_DELETE @"I_OL_FL_DE"
#define OFFLINE_START_SEARCH @"I_OL_ST_SE"
#define OFFLINE_DOWNLOAD_ERR @"I_OL_DW_ER"
#define OFFLINE_UNZIP_ERR @"I_OL_UZ_ER"
#define OFFLINE_CITY_NUM @"I_OL_CY_NU"
#define OFFLINE_DONE_CLICK @"I_OL_DN_CL"
#define OFFLINE_DOWNLOAD_ERR_KEY @"I_OL_DW_ER_K"    // "下载失败原因"的Key
#define OFFLINE_UPGRADE_SHOW        @"I_OL_A_UPD_S"      // 显示"升级"的Alertview
#define OFFLINE_UPGRADE_OK          @"I_OL_A_UPD_U"      // 点击"升级AlertView"中的"升级"或者"前去升级"
#define OFFLINE_UPGRADE_CANCEL      @"I_OL_A_UPD_C"      // 点击"升级AlertView"中的"升级"或者"前去升级"
#define OFFLINE_DOWNLOAD_DATA       @"I_OL_DW_D"    //下载地图完毕且成功安装包数量

//统计失败率分子
#define OFFLINE_DOWNLOAD_HEADER         @"I_OL_DW_ER_HEADER"    //下载地图包头问题
#define OFFLINE_DOWNLOAD_STATE          @"I_OL_DW_ER_STATE"  //网络状态不对 connectionDidFinishLoading statusCode>=400
#define OFFLINE_DOWNLOAD_EUZIP          @"I_OL_DW_ER_EUZIP"  //解压失败
#define OFFLINE_DOWNLOAD_EIO            @"I_OL_DW_ER_EIO"  //IO失败
#define OFFLINE_DOWNLOAD_CRASHERROR     @"I_OL_DW_ER_CRASH"//下载出现页面数据不一致时crash
//#define OFFLINE_DOWNLOAD_NO_CITY_CLASS  @"I_OL_DW_ER_N_CT_CL" //UI参数错误
#define OFFLINE_DOWNLOAD_EMD5           @"I_OL_DW_ER_EMD5"  //MD5校验失败
#define OFFLINE_DOWNLOAD_ENET           @"I_OL_DW_ER_NET"  //网络原因
#define OFFLINE_DOWNLOAD_NAC_RESULT           @"I_OL_DW_ER_NAC_RESULT"  //NAC会返回结果错误
#define OFFLINE_DOWNLOAD_NAC_ERR           @"I_OL_DW_ER_NAC_ERR"  //NAC网络请求错误

//统计失败率分母：
//以上8种错误+
#define OFFLINE_INSTALL_OK @"I_OL_IN_OK"            // "下载成功次数"统计项


#define OFFLINE_DOWNLOAD_ESIZE           @"I_OL_DW_ER_ESIZE"  //包大小不对
#define OFFLINE_DOWNLOAD_NONET          @"I_OL_DW_ER_NONET"  //无网络

//OFFLINE_DOWNLOAD_EIP = OFFLINE_DOWNLOAD_NAC_RESULT + OFFLINE_DOWNLOAD_NAC_ERR
#define OFFLINE_DOWNLOAD_EIP           @"I_OL_DW_ER_EIP"  //ip访问错误,
#define OFFLINE_DOWNLOAD_4XX5XX           @"I_OL_DW_ER_4XX5XX"  //服务器返回403，404，505等错误


#define OFFLINE_DL_DATA     @"I_OL_DL_DATA"  // 具体是哪个城市被下载

// 离线地图在首页tips展示数量
#define PER_OM_TIPS_E                   @"per_om_tips_e"

// 离线地图在首页tips展示_关闭
#define PER_OM_TIPS_C                   @"per_om_tips_c"

// 离线地图在首页tips展示_点击查看
#define PER_OM_TIPS_DETAIL              @"per_om_tips_detail"

// 离线地图-零流量小蓝条-展现
#define OFFLINE_BLUEBAR_E               @"offline_bluebar_e"

// 离线地图-零流量小蓝条-关闭
#define OFFLINE_BLUEBAR_CLOSE           @"offline_bluebar_close"

// 离线地图-零流量小蓝条-切换在线
#define OFFLINE_BLUEBAR_SWITCHON        @"offline_bluebar_switchon"

//截屏反馈-弹窗展现
#define CUT_FEEDBACK_BOX_PV     @"cut_feedback_box_pv"
#define CUT_FEEDBACK_CL     @"cut_feedback_cl"
#define CUT_FEEDBACK_SHARE  @"cut_feedback_share"

//SV recommend page
#define SVR_CLZ @"I_SVR_CLZ"
#define SVR_H @"I_SVR_H"
#define SVR_H_C @"I_SVR_H_C"
#define SVR_H_TRY @"I_SVR_H_TRY"
#define SV_ENTRY @"I_SV_ENTRY"

#define SVR_ALL @"I_SVR_ALL"

//SV detail
#define SVD_OPEN @"dis_sv_poi_o" //@"I_SVD_OPEN"
#define SVD_MARKER_CLZ @"I_SVD_MARKER_CLZ"
#define SVD_MARKER_OPEN @"I_SVD_MARKER_OPEN"
#define SVD_CLZ @"I_SVD_CLZ"
#define SVD_DST @"I_SVD_DST"
#define SVD_MAP @"I_SVD_MAP"

//SV share
#define SV_SH_WX @"I_SV_SH_WX"
#define SV_SH_TL @"I_SV_SH_TL"
#define SV_SH_WB @"I_SV_SH_WB"
#define SV_SH_SMS @"I_SV_SH_SMS"
#define SV_SH_QZONE @"I_SV_SH_QZONE"
#define SV_SH_QQ @"I_SV_SH_QQ"
#define SV_SH_EMAIL @"I_SV_SH_EM"
#define SV_SH_SINA_WB @"I_SV_SH_SWB"
#define SV_SH_COPY_URL @"I_SV_SH_C_URL"
#define SV_SH_FAV @"I_SV_SH_FAV"
#define SV_SH_C  @"I_SV_SH_C"
#define SV_WAP @"I_SV_WAP"
#define SV_WAP_BCK @"I_SV_WAP_BCK"

#define STREETVIEW_GO @"I_SVG_G"
#define STREETVIEW_SHOWHELP @"I_SVG_HEL"
#define STREETVIEW_ZOOMIN @"I_SVG_ZIN"
#define STREETVIEW_PINCH @"I_SVG_PIN"
#define STREETVIEW_QUIT @"I_SVG_Q"
#define STREETVIEW_MOVE @"I_SVG_MV"
#define STREETVIEW_SWIPE @"I_SVG_SW"    //erase
#define STREETVIEW_MOVE_PIX @"I_SVG_MV_PX"
#define STREETVIEW_SH @"I_SVG_SH"
#define STREETVIEW_DURATION @"I_SVG_DU"

#define STREETVIEW_SWITCH_TITLE_ON @"I_SVG_SW_TI_ON"
#define STREETVIEW_SWITCH_TITLE_OFF @"I_SVG_SW_TI_OFF"

#define LOCATIONSHARE_I_TA          @"per_lo_c"
#define LOCATIONSHARE_I_TA_B        @"I_TA_B"
#define LOCATIONSHARE_I_TA_C        @"per_lo_b_share"//@"I_TA_C"
#define LOCATIONSHARE_I_TA_C_C      @"I_TA_C_C"
#define LOCATIONSHARE_I_TA_C_C_S    @"I_TA_C_C_S"
#define LOCATIONSHARE_I_TA_C_C_C    @"I_TA_C_C_C"
#define LOCATIONSHARE_I_TA_L        @"I_TA_L"
#define LOCATIONSHARE_I_TA_L_S      @"per_lo_l_s"

#define LOCATIONSHARE_I_TA_L_W      @"per_lo_l_w_s"
#define LOCATIONSHARE_I_TA_L_W_R    @"per_lo_l_w_s_rei"
#define LOCATIONSHARE_I_TA_L_W_Q    @"per_lo_l_w_s_dc"

#define LOCATIONSHARE_I_TA_L_Q    @"per_lo_l_dc"
#define LOCATIONSHARE_I_TA_L_Q_R    @"per_lo_l_dc_i"
#define LOCATIONSHARE_I_TA_L_Q_D    @"per_lo_l_dc_del"


#define LOCATIONSHARE_I_TA_MP    @"I_TA_MP"
#define LOCATIONSHARE_I_TA_MP_B    @"I_TA_MP_B"
#define LOCATIONSHARE_I_TA_MP_Q    @"per_lo_map_exit"
#define LOCATIONSHARE_I_TA_MP_Q_Y    @"I_TA_MP_Q_Y"
#define LOCATIONSHARE_I_TA_MP_Q_N    @"I_TA_MP_Q_N"
#define LOCATIONSHARE_I_TA_MP_LOC    @"I_TA_MP_LOC"
#define LOCATIONSHARE_I_TA_MP_TA_TEL    @"I_TA_MP_TA_TEL"
#define LOCATIONSHARE_I_TA_MP_TA_SMS    @"I_TA_MP_TA_SMS"

#define LOCATIONSHARE_I_TA_MEET_N    @"I_TA_MEET_N"
#define LOCATIONSHARE_I_TA_MEET_C    @"I_TA_MEET_C"
#define LOCATIONSHARE_I_TA_MEET_BLG    @"I_TA_MEET_BLG"
#define LOCATIONSHARE_I_TA_MEET_PIC    @"I_TA_MEET_PIC"

//topic
#define TOPIC_ENTRANCE          @"I_AD_IN"  //erased
#define TOPIC_DURATION          @"I_AD_R"   //erased
#define TOPIC_SHARE             @"I_AD_S"   //erased
#define TOPIC_SHARE_CANCEL      @"I_AD_S_C" //erased
#define TOPIC_SHARE_QQ          @"I_AD_S_QQ"    //erased
#define TOPIC_SHARE_WX_SESSION  @"I_AD_S_WX"    //erased
#define TOPIC_SHARE_WX_TIMELINE @"I_AD_S_WXT"   //erased
#define TOPIC_SHARE_SMS         @"I_AD_S_S"     //erased
#define TOPIC_SHARE_TXWEIBO     @"I_AD_S_QWB"   //erased
#define TOPIC_SHARE_QZONE       @"I_AD_S_QZ"    //erased

#define TOPIC_MORE              @"I_AD_MORE"
#define TOPIC_SV                @"I_AD_SOSOSV"
#define TOPIC_POI               @"I_AD_SOSOPOI"

//navigation
#define N_TOTAL_USETIME  @"I_N_T_UT"
#define N_MAP_NAVI   @"I_N_M_N"
#define N_L_C @"I_N_L_C"
#define N_C_N  @"I_N_C_N"
#define N_S_N  @"I_N_S_N"   //erased
#define N_QUIT_ALAET_Q_N @"I_N_Q_A_Q_N" //erased
#define N_QUIT_ALERT_C_N @"I_N_Q_A_C_N" //erased
#define N_M_Z_IN @"I_N_M_Z_I"
#define N_M_Z_OUT @"I_N_M_Z_O"
#define N_G_PAN  @"I_N_G_PAN"
#define N_G_D_TAP @"I_N_G_D_TAP"
#define N_G_T_S_TAP  @"I_N_G_T_S_TAP"
#define N_G_PINCH @"I_N_G_PINCH"
#define N_G_ROTATE @"I_N_G_ROTATE"
#define N_G_SWIPE_UP @"I_N_G_S_UP"
#define N_G_SWIPE_DOWN @"I_N_G_S_DOWN"
#define NAVIGATION_DURATION   @"I_N_DURATION"
#define N_G_P_I  @"I_N_G_P_I"
#define N_G_P_O  @"I_N_G_P_O"

//status record
#define M_TRAFFIC_STATUS @"I_M_TF_S"
#define M_SATELITE_STATUS @"I_M_SA_S"
#define M_3D_STATUS @"I_M_3D_S"

//taxi
#define T_SUBMIT_KEY_ORDER @"I_T_S_K_O"
#define T_SUBMIT_VOICE_ORDER @"I_T_S_V_O"

//netwrok status
#define N_S_WIFI @"I_N_S_WIFI"
#define N_S_2G   @"I_N_S_2G"
#define N_S_3G   @"I_N_S_3G"
//streetViewRoad
#define I_SVR_BTN @"I_SVR_BTN"
#define I_SVR_REVERSE_COUNT @"I_SVR_R_C"
#define I_SVR_REVERSE_S @"I_SVR_R_S"
#define I_SVR_REVERSE_F @"I_SVR_R_F"
#define I_SVR_REVERSE_N @"I_SVR_R_N"
#define I_SVR_IN_COUNT @"I_SVR_I_C"
#define I_SV_MINI_OUT @"I_SV_M_O"
#define I_SV_MINI_CLOSE @"I_SV_M_C"
#define I_SV_MINI_SCROLL @"I_SV_M_S"
#define I_SV_MINI_REVERSE @"I_SV_M_R"


//postcard
#define P_ADDCONTACT_CLICK @"I_P_A_C"
#define P_OTHERCONTACT_CLICK @"I_P_O_C"
#define P_ABTAB_CLICK @"I_P_AB_C"
#define P_QQTAB_CLICK @"I_P_QQ_C"
#define P_SEND_CLICK @"I_P_S_C"
#define P_SEND_TOTAL @"I_P_S_T"
#define P_SEND_QQ_TOTAL @"I_P_S_QQ_T"
#define P_SEND_AB_TOTAL @"I_P_S_AB_T"
#define P_SEND_WECHAT_TOTAL @"I_P_S_WX_T"
#define P_SEND_WXCIRCLE_C @"I_P_S_WXC_C"
#define P_SEND_QQ_C @"I_P_S_QQ_C"
#define P_SEND_TWB_C @"I_P_S_TWB_C"
#define P_SEND_SWB_C @"I_P_S_SWB_C"

//walkguide
#define W_NAVI_BUTTON @"I_W_NAVI_BUTTON"
#define W_NAVI_COMPASS @"I_W_NAVI_COMPASS"
#define W_DETAIL_STREETVIEW @"I_W_DETAIL_STREETVIEW"

//walkguide
#define W_NAVI_INFO_CHANGE @"I_W_NAVI_INFO_CHANGE"
#define W_NAVI_BUTTON_EXIT @"I_W_NAVI_BUTTON_EXIT"
#define W_NAVI_ZOOM_CLK    @"I_W_NAVI_MAP_CONTORL"

//拖动缩放
#define ZOOM_BUTTON_TOUCH_AND_HOLD @"I_ZOOM_UP"
#define ZOOM_BUTTON_SWITCH_SIDE @"I_ZOOM_SWITCH"

//常用左手
#define USE_HAND_LEFT @"I_HAND_L"
#define USE_HAND_RIGHT @"I_HAND_R"

/**唤起街景*/
#define GO_STREETVIEW   (@"I_GO_SV")
/**唤起街景探索*/
#define GO_TOPIC    (@"start_sv")//(@"I_GO_TOPIC")
/**唤起公交*/
#define GO_BUS      (@"I_GO_BUS")
/**唤起驾车*/
#define GO_CAR      (@"I_GO_CAR")
/**唤起POI*/
#define GO_POI      (@"I_GO_POI")
/**躲避拥堵*/
#define ROUTE_SEARCH_AVOID_BUSY (@"nav_dr_s_dis")//(@"I_RS_OPT_BUSY")
/**不走高速*/
#define ROUTE_SEARCH_NO_EXPRESS (@"nav_dr_s_nog")//(@"I_RS_OPT_NO_EXP")
/**躲避收费路段*/
#define ROUTE_SEARCH_NO_TOLL    (@"nav_dr_s_nfee")//(@"I_RS_OPT_NO_TOLL")
/**指南针*/
#define NAVIGATION_COMPASS      (@"I_NAV_COMPASS")
/**导航路牌*/
#define NAVIGATION_PANEL        (@"I_NAV_PANEL")

//poi correction
#define M_POI_CORRECTION_ENTRY @"I_M_P_C_E"
#define M_POI_CORRECTION_NOTEXIST @"I_M_P_C_N"
#define M_POI_CORRECTION_NOTEXIST_CANCEL @"I_M_P_C_N_C"
#define M_POI_CORRECTION_NOTEXIST_SUBMIT @"I_M_P_C_N_S"
#define M_POI_CORRECTION_LOCERR @"I_M_P_C_L"
#define M_POI_CORRECTION_LOCERR_SUBMIT @"I_M_P_C_L_S"
#define M_POI_CORRECTION_LOCERR_CANCEL @"I_M_P_C_L_C"
#define M_POI_CORRECTION_LOCERR_CONTINUE @"I_M_P_C_L_CN"
#define M_POI_CORRECTION_LOCERR_NEWLOCATION @"I_M_P_C_L_NL"
#define M_POI_CORRECTION_DETAIL @"I_M_P_C_D"
#define M_POI_CORRECTION_DETAIL_SUBMIT @"I_M_P_C_D_S"

//

//event id for dengta
#define D_M_MAP_TAP     @"110001"
#define D_M_POI_TAP     @"110002"
#define D_M_NAV_TAP     @"120001"
#define D_M_DISCOVER_TAP     @"130001"
#define D_M_ME_TAP     @"140001"
#define D_M_ENTRY_LAUNCHED    @"000010"
#define D_M_ENTRY_BACKGROUND    @"000030"
#define D_M_ENTRY_FOREGROUND    @"000040"

#define EVENT_DOWNLOAD         @"DOWNLOAD_EVENTS"
#define EVENT_OFFLINEMODE      @"OFFLINEMODE_EVENTS"
#define EVENT_LOCATION         @"LOCATION_EVENTS"
#define EVENT_OFFMODE_SWITCH   @"OFFMODE_SWITCH_EVENTS"

#define QMHistoryDataManagerVersion         @"QMHistoryDataManagerVersion"
/**
 * ++++ start ++++ *
 * 灯塔日志，字符串定义
 */

    /** **** 语音输入 **** */

// gen_voi_g_l	点击引导页入口
#define _DENGTA_gen_voi_g_l     @"gen_voi_g_l"

// gen_voi_in_c	“请说话”取消btn
#define _DENGTA_gen_voi_in_c    @"gen_voi_in_c"

// gen_voi_in_f_c	没有检测到语音输入对话框 - 取消button
#define _DENGTA_gen_voi_in_f_c  @"gen_voi_in_f_c"

// gen_voi_in_l	总进入次数
#define _DENGTA_gen_voi_in_l    @"gen_voi_in_l"

// gen_voi_ip_f_a	识别出错后点击麦克风重新输入
#define _DENGTA_gen_voi_ip_f_a  @"gen_voi_ip_f_a"

// gen_voi_rec_c	“正在识别”取消btn
#define _DENGTA_gen_voi_rec_c   @"gen_voi_rec_c"

// gen_voi_s_c	“正在检索”过程中关闭（包括点取消按钮和back键）
#define _DENGTA_gen_voi_s_c     @"gen_voi_s_c"

    /** **** QQ Login **** */

// wgLogin	QQ登录事件
#define _DENGTA_wgLogin         @"wgLogin"

    /** **** 世界地图 **** */

// wp_dl_	下载世界地图-城市名
#define _DENGTA_wp_dl_          @"wp_dl_"

// wp_enter	世界地图入口
#define _DENGTA_wp_enter        @"wp_enter"

// wp_loc_map	定位点在所在地图
#define _DENGTA_wp_loc_map      @"wp_loc_map"

// wp_poi	打开poi列表页
#define _DENGTA_wp_poi          @"wp_poi"

// wp_subway	点击地铁图层按钮
#define _DENGTA_wp_subway       @"wp_subway"

// wp_tips	点击攻略页面按钮
#define _DENGTA_wp_tips         @"wp_tips"

    /** **** 公交路线 **** */

// nav_bus_car_e	“公交无结果驾车建议”出现
#define _DENGTA_nav_bus_car_e   @"nav_bus_car_e"

// nav_bus_end	终点输入框点击
#define _DENGTA_nav_bus_end     @"nav_bus_end"

// nav_bus_fav	方案详情-收藏btn
#define _DENGTA_nav_bus_fav     @"nav_bus_fav"

// nav_bus_near_e	“近距离步行建议”出现
#define _DENGTA_nav_bus_near_e  @"nav_bus_near_e"

// nav_bus_op_sw	公交路线选项-返回btn
#define _DENGTA_nav_bus_op_sw   @"nav_bus_op_sw"

// nav_bus_r_car	公交无结果驾车建议bar
#define _DENGTA_nav_bus_r_car   @"nav_bus_r_car"

// nav_bus_r_near	近距离步行建议bar
#define _DENGTA_nav_bus_r_near  @"nav_bus_r_near"

// nav_bus_rs_ty	公交tab搜索量
#define _DENGTA_nav_bus_rs_ty   @"nav_bus_rs_ty"    /* ?? */

// nav_bus_s	公交路线选项
#define _DENGTA_nav_bus_s       @"nav_bus_s"

// nav_bus_s_copy	公交方案分享—复制链接
#define _DENGTA_nav_bus_s_copy  @"nav_bus_s_copy"

// nav_bus_s_o	公交方案分享—其他                   /* ?? */

// nav_bus_s_qq	公交方案分享—QQ
#define _DENGTA_nav_bus_s_qq    @"nav_bus_s_qq"

// nav_bus_s_re	公交路线选项-推荐方案（非默认）
#define _DENGTA_nav_bus_s_re    @"nav_bus_s_re"

// nav_bus_s_sina_b	公交方案分享—sina微博
#define _DENGTA_nav_bus_s_sina_b    @"nav_bus_s_sina_b"

// nav_bus_s_sms	公交方案分享—短信
#define _DENGTA_nav_bus_s_sms   @"nav_bus_s_sms"

// nav_bus_s_sina_b	公交方案分享—safari中打开
#define _DENGTA_nav_bus_s_safari   @"nav_bus_s_safari"

// nav_bus_s_subw	公交路线选项-地铁优先
#define _DENGTA_nav_bus_s_subw  @"nav_bus_s_subw"

// nav_bus_s_time	公交路线选项-时间短
#define _DENGTA_nav_bus_s_time  @"nav_bus_s_time"

// nav_bus_s_trans	公交路线选项-少换乘
#define _DENGTA_nav_bus_s_trans @"nav_bus_s_trans"

// nav_bus_s_w_b	公交方案分享—腾讯微博
#define _DENGTA_nav_bus_s_w_b   @"nav_bus_s_w_b"

// nav_bus_s_w_c	公交方案分享—微信朋友圈
#define _DENGTA_nav_bus_s_w_c   @"nav_bus_s_w_c"

// nav_bus_s_w_f	公交方案分享—微信好友
#define _DENGTA_nav_bus_s_w_f   @"nav_bus_s_w_f"

// nav_bus_s_walk	公交路线选项-少步行
#define _DENGTA_nav_bus_s_walk  @"nav_bus_s_walk"

// nav_bus_sd	方案详情-分享btn
#define _DENGTA_nav_bus_sd      @"nav_bus_sd"

// nav_bus_sd_uf	方案详情-横划切换
#define _DENGTA_nav_bus_sd_uf   @"nav_bus_sd_uf"

// nav_bus_st	起点输入框点击
#define _DENGTA_nav_bus_st      @"nav_bus_st"

// nav_bus_sw_b	列表展示-返回btn
#define _DENGTA_nav_bus_sw_b    @"nav_bus_sw_b"

// nav_bus_taxi	打车btn
#define _DENGTA_nav_bus_taxi    @"nav_bus_taxi"

// nav_bus_uf	方案详情-线路方案展开
#define _DENGTA_nav_bus_uf      @"nav_bus_uf"

// nav_bus_up	地图展示-详情页上拉
#define _DENGTA_nav_bus_up      @"nav_bus_up"

// nav_bus_w_de	方案详情-进入步行预览
#define _DENGTA_nav_bus_w_de    @"nav_bus_w_de"

// nav_bus_w_uf	方案详情-步行方案展开
#define _DENGTA_nav_bus_w_uf    @"nav_bus_w_uf"

// nav_bus_y	公交路线选项-公交优先
#define _DENGTA_nav_bus_y       @"nav_bus_y"

    /* **** 线站搜索 **** */
// map_bline_bus_l	公交站详情页中点击下方线路列表中某一项的次数
#define _DENGTA_map_bline_bus_l @"map_bline_bus_l"

// map_bline_load	poi搜索结果进入公交线路页面
#define _DENGTA_map_bline_load  @"map_bline_load"

// map_bline_r_c	文字列表结果点击某一个站进入地图显示的次数
#define _DENGTA_map_bline_r_c   @"map_bline_r_c"

// map_bline_r_n_c	文字列表结果点击“离我最近”站进入地图显示的次数    /** ?? */

// map_bline_r_sw_b	返回按钮
#define _DENGTA_map_bline_r_sw_b    @"map_bline_r_sw_b"     /** ?? */

    /* **** tab 按钮 **** */
// main_dis	“发现”tab-点击选中
#define _DENGTA_main_dis        @"main_dis"

// main_map	“地图”tab-点击选中
#define _DENGTA_main_map        @"main_map"

// main_nav	“导航”tab-点击选中
#define _DENGTA_main_nav        @"main_nav"

// main_person	“我”tab-点击选中
#define _DENGTA_main_person     @"main_person"

    /* **** 街景 **** */
// dis_rcd_load	发现tab“街景”bar
#define _DENGTA_dis_rcd_load    @"dis_rcd_load"

// dis_st_sw_b	返回btn
#define _DENGTA_dis_st_sw_b     @"dis_st_sw_b"

// dis_sv_indoor_b	离开室内景   /** ?? */

// dis_sv_load	进入街景总次数
#define _DENGTA_dis_sv_load     @"dis_sv_load"

// dis_sv_load_wap	从wap进入的次数
#define _DENGTA_dis_sv_load_wap @"dis_sv_load_wap"

// dis_sv_poi_o	POImarker呼起mini详情页  /** ?? */

// dis_sv_s	分享btn
#define _DENGTA_dis_sv_s        @"dis_sv_s"

// dis_sv_s_copy	分享-复制链接
#define _DENGTA_dis_sv_s_copy   @"dis_sv_s_copy"

// dis_sv_s_o	分享-其他       /** ?? */

// dis_sv_s_qq	分享-QQ
#define _DENGTA_dis_sv_s_qq     @"dis_sv_s_qq"

// dis_sv_s_qq_blog	分享-腾讯微博
#define _DENGTA_dis_sv_s_qq_blog    @"dis_sv_s_qq_blog"

// dis_sv_s_sina_b	分享-sina微博
#define _DENGTA_dis_sv_s_sina_b @"dis_sv_s_sina_b"

// dis_sv_s_sms	分享-短信
#define _DENGTA_dis_sv_s_sms    @"dis_sv_s_sms"

// dis_sv_s_w_c	分享-微信朋友圈
#define _DENGTA_dis_sv_s_w_c    @"dis_sv_s_w_c"

// dis_sv_s_w_f	分享-微信好友
#define _DENGTA_dis_sv_s_w_f    @"dis_sv_s_w_f"

// dis_sv_s_safari	分享-微信好友
#define _DENGTA_dis_sv_s_safari    @"dis_sv_s_safari"

// dis_sv_v_m	用户移动的次数
#define _DENGTA_dis_sv_v_m      @"dis_sv_v_m"

// start_sv	唤起街景推荐          /** ?? */

// sv_btn	街景图层按钮点击数
#define _DENGTA_sv_btn          @"sv_btn"

// sv_enter	图层中进入街景次数（气泡点击数）
#define _DENGTA_sv_enter        @"sv_enter"

// sv_fail	反查不成功次数（网络差和其他）
#define _DENGTA_sv_fail         @"sv_fail"

// sv_mini_fc_suc	小地图拖动后反查街景次数
#define _DENGTA_sv_mini_fc_suc  @"sv_mini_fc_suc"

// sv_mini_m_c	分屏小地图收起按钮点击数
#define _DENGTA_sv_mini_m_c     @"sv_mini_m_c"

// sv_mini_m_o	分屏小地图展开按钮点击数
#define _DENGTA_sv_mini_m_o     @"sv_mini_m_o"

// sv_null	反查没有街景次数
#define _DENGTA_sv_null         @"sv_null"

// sv_succ	反查街景（POI）成功次数
#define _DENGTA_sv_succ         @"sv_succ"

// per_s	设置bar点击
#define _DENGTA_per_s           @"per_s"

// per_s_about	设置-关于地图
#define _DENGTA_per_s_about     @"per_s_about"

// per_s_ext	设置-返回按钮
#define _DENGTA_per_s_ext       @"per_s_ext"

// per_s_ext	设置-返回按钮
#define _DENGTA_per_s_ext       @"per_s_ext"

// per_s_f	设置-用户反馈
#define _DENGTA_per_s_f         @"per_s_f"

// per_s_gps_c	设置-GPS（卫星定位）设置

// per_s_hlp	设置-使用帮助
#define _DENGTA_per_s_hlp       @"seye_set_pro"

// per_s_net	设置-流量统计
#define _DENGTA_per_s_net       @"per_s_net"

// per_s_r2f	设置-推荐给朋友
#define _DENGTA_per_s_r2f       @"seye_set_map_share"

// per_s_scr_c	设置-保持屏幕常亮-关
#define _DENGTA_per_s_scr_c     @"per_s_scr_c"

// per_s_scr_s	设置-保持屏幕常亮-开
#define _DENGTA_per_s_scr_s     @"per_s_scr_s"

// per_s_upg	设置-检查更新
#define _DENGTA_per_s_upg       @"per_s_upg"

// per_s_wifi_c	设置-WIFI离线自动更新关闭
#define _DENGTA_per_s_wifi_c    @"per_s_wifi_c"

#define _DENGTA_per_s_wifi_o    @"offline_wifi-ad_open"

// Added by nopwang begin

//dis_nea_a_h_l	输入搜索-历史记录列表点击	“附近”输入搜索页，smartbox历史记录条目点击	附近-附近搜索输入页
#define _DENGTA_dis_nea_a_h_l   @"dis_nea_a_h_l"

// dis_nea_a_s_kb	输入搜索-键盘搜索按钮	“附近”输入搜索页软键盘搜索btn	附近-附近搜索输入页
#define _DENGTA_dis_nea_a_s_kb  @"dis_nea_a_s_kb"

//dis_nea_a_v	语音按钮	周边搜索过程页的语音按钮，不重复，可以区分	附近-附近搜索输入页
#define _DENGTA_dis_nea_a_v     @"dis_nea_a_v"

// dis_nea_c	发现tab“附近”bar	发现tab下“附近”bar点击	附近
#define _DENGTA_dis_nea_c   @"dis_nea_c"

//dis_nea_cm_i_c	结果条目总点击	周边搜索结果列表，结果条目点击总数	附近-附近列表页
#define _DENGTA_dis_nea_cm_i_c  @"dis_nea_cm_i_c"

//dis_nea_d_c	距离btn	周边搜索结果列表，距离筛选控件点击	附近-附近列表页
#define _DENGTA_dis_nea_d_c     @"dis_nea_d_c"

//dis_nea_np	下一页btn	周边搜索结果列表，翻页btn-下一页	附近-附近列表页
#define _DENGTA_dis_nea_np      @"dis_nea_np"

//dis_nea_pp	上一页btn	周边搜索结果列表，翻页btn-上一页	附近-附近列表页
#define _DENGTA_dis_nea_pp      @"dis_nea_pp"

//dis_nea_s	输入搜索btn	“附近”类别选择页，右上角“搜索”btn点击	附近-附近分类页
#define _DENGTA_dis_nea_s       @"dis_nea_s"

//dis_nea_s_c	排序btn	周边搜索结果列表，排序筛选控件点击	附近-附近列表页
#define _DENGTA_dis_nea_s_c     @"dis_nea_s_c"

//dis_nea_sl_c	服务分类btn	周边搜索结果列表，服务分类筛选控件点击	附近-附近列表页
#define _DENGTA_dis_nea_sl_c    @"dis_nea_sl_c"


//dis_nea_sw_b_f	返回btn	“附近”类别选择页，左上角“返回”btn点击	附近-附近分类页
#define _DENGTA_dis_nea_sw_b_f  @"dis_nea_sw_b_f"

//dis_nea_sw_b_s	输入搜索-返回btn	“附近”输入搜索页，左上角“返回”btn的点击	附近-附近搜索输入页
#define _DENGTA_dis_nea_sw_b_s @"dis_nea_sw_b_s"

//dis_nea_sw_b_z	返回btn	周边搜索结果列表，左上方“返回”btn点击	附近-附近列表页
#define _DENGTA_dis_nea_sw_b_z  @"dis_nea_sw_b_z"

//map_ct_chag	缩放控件变换位置		缩放控件
#define _DENGTA_map_ct_chag     @"map_ct_chag"

//map_ct_fly	缩放控件激活挪动		缩放控件
#define _DENGTA_map_ct_fly  @"map_ct_fly"

//map_ct_s_l_off	常用左手操作关		缩放控件
#define _DENGTA_map_ct_s_l_off  @"map_ct_s_l_off"

//map_ct_s_l_on	常用左手操作开		缩放控件
#define _DENGTA_map_ct_s_l_on   @"map_ct_s_l_on"

//nav_dr_v_m	功能菜单btn	语音导航过程界面右下角三横点菜单点击打开次数	驾车路线-语音导航
#define _DENGTA_nav_dr_v_m      @"nav_dr_v_m"

//nav_dr_v_m_c	菜单-取消btn	点击三横点菜单btn后，菜单中“取消”btn点击	驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_c    @"nav_dr_v_m_c"

//nav_dr_v_m_f	菜单-方案详情btn	点击三横点菜单btn后，菜单中“方案详情”btn点击	驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_f    @"nav_dr_v_m_f"

//nav_dr_v_m_hud	菜单-开启HUD模式btn		驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_hud  @"nav_dr_v_m_hud"

//nav_dr_v_m_r	菜单-路线概览btn	点击三横点菜单btn后，菜单中“路线概览”btn点击	驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_r    @"nav_dr_v_m_r"

//nav_dr_v_m_rp	菜单-点击导航路牌btn		驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_rp   @"nav_dr_v_m_rp"

//nav_dr_v_m_v	菜单-闭语音播报btn		驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_v    @"nav_dr_v_m_v"

//nav_dr_v_m_wx_off	菜单-卫星地图btn-关	点击三横点菜单btn后，点击菜单中“卫星地图”btn，关闭卫星视图次数	驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_wx_off   @"nav_dr_v_m_wx_off"

//nav_dr_v_m_wx_on	菜单-卫星地图btn-开	点击三横点菜单btn后，点击菜单中“卫星地图”btn，打开卫星视图次数	驾车路线-语音导航
#define _DENGTA_nav_dr_v_m_wx_on    @"nav_dr_v_m_wx_on"

//nav_dr_fav	方案详情-收藏btn	驾车方案详情页底部“收藏”btn点击	驾车路线-驾车详情页
#define _DENGTA_nav_dr_fav          @"nav_dr_fav"

//nav_dr_go	继续导航	导航过程中“继续导航”点击数	驾车路线-语音导航
#define _DENGTA_nav_dr_go           @"nav_dr_go"

//nav_m_click	模拟导航btn		模拟导航
#define _DENGTA_nav_m_click @"nav_m_click"

//nav_m_e	模拟导航-退出		模拟导航
#define _DENGTA_nav_m_e     @"nav_m_e"

//nav_m_p	模拟导航-播放		模拟导航
#define _DENGTA_nav_m_p     @"nav_m_p"

//nav_m_ps	模拟导航-暂停		模拟导航
#define _DENGTA_nav_m_ps    @"nav_m_ps"

//nav_m_q	模拟导航-快进		模拟导航
#define _DENGTA_nav_m_q     @"nav_m_q"

//nav_wk_button_continue    导航中“继续”btn    在步行导航中，左下角“继续”btn的出现的次数   步行路线-步行导航
#define _DENGTA_nav_wk_button_continue   @"nav_wk_button_continue"

//map_bus_m_c       公交地图展示-关闭btn   地图展示公交方案结果页，右上角“关闭”btn点击    公交路线-公交地图结果页
#define _DENGTA_nav_bus_m_c               @"map_bus_m_c"

//nav_direction_c   导航中-点击方向看板
#define _DENGTA_nav_direction_c               @"nav_direction_c"

//per_cs_e_h_del  通勤引导-家-编辑-删除
#define _DENGTA_per_cs_e_h_del          @"per_cs_e_h_del"

//per_cs_e_c_del  通勤引导-公司-编辑-删除
#define _DENGTA_per_cs_e_c_del          @"per_cs_e_c_del"

//per_cs_h_c    通勤引导-家-点击
#define _DENGTA_per_cs_h_c              @"per_cs_h_c"

//per_cs_c_c    通勤引导-公司-点击
#define _DENGTA_per_cs_c_c              @"per_cs_c_c"

//per_f_e	收藏夹-编辑	进入收藏夹后右上角“编辑”按钮	收藏夹
#define _DENGTA_per_f_e     @"per_f_e"

//per_f_p_more_del  收藏夹-具体地点-更多-删除
#define _DENGTA_per_f_p_more_del    @"per_f_p_more_del"

//per_f_p_more_rn  收藏夹-具体地点-更多-重命名
#define _DENGTA_per_f_p_more_rn     @"per_f_p_more_rn"

//per_f_e_as    收藏夹-地点tab-编辑态中-全选
#define _DENGTA_per_f_e_as          @"per_f_e_as"

//per_f_e_del   收藏夹-地点tab-编辑态中-删除
#define _DENGTA_per_f_e_del         @"per_f_e_del"

//per_f_m	收藏夹bar点击	“我”tab下“收藏夹”bar点击	收藏夹
#define _DENGTA_per_f_m     @"per_f_m"

//per_f_p_c	收藏夹-地点收藏夹中某一地点	在地点收藏夹tab下，点击某一个收藏的地点	收藏夹
#define _DENGTA_per_f_p_c   @"per_f_p_c"

//per_f_p_r	收藏夹-路线收藏夹中某一路线	在路线收藏夹tab下，点击某一个收藏的路线	收藏夹
#define _DENGTA_per_f_p_r   @"per_f_p_r"

//per_f_r	收藏夹-编辑状态下“重命名“	进入收藏夹编辑状态后，“重命名“按钮	收藏夹
#define _DENGTA_per_f_r     @"per_f_r"

//per_f_save	收藏夹-编辑状态下重命名“保存“	进入收藏夹编辑状态，并重命名后，右上角“保存“按钮	收藏夹
#define _DENGTA_per_f_save  @"per_f_save"

//per_f_sw_b	收藏夹-返回按钮	左上角返回按钮	收藏夹
#define _DENGTA_per_f_sw_b  @"per_f_sw_b"

//per_f_syn	收藏夹-同步	进入收藏夹后右下角“同步”按钮	收藏夹
#define _DENGTA_per_f_syn   @"per_f_syn"

//per_f_syn_c_n	收藏夹-同步成功次数	登录成功之后，用户成功同步的次数	收藏夹
#define _DENGTA_per_f_syn_c_n   @"per_f_syn_c_n"

//per_f_t_p	收藏夹-最上方TAB-地点按钮	收藏夹界面顶部tab的地点按钮	收藏夹
#define _DENGTA_per_f_t_p       @"per_f_t_p"

//per_f_t_r	收藏夹-最上方TAB-路线按钮	收藏夹界面顶部tab的路线按钮	收藏夹
#define _DENGTA_per_f_t_r       @"per_f_t_r"

//rqd_appexited	退出	APP退出事件	系统事件
#define _DENGTA_rqd_appexited   @"rqd_appexited"

//rqd_applaunched	启动	APP启动事件	系统事件
#define _DENGTA_rqd_applaunched @"rqd_applaunched"

//start_f_p	通过协议唤起应用		运营关注-唤起app方式
#define _DENGTA_start_f_p   @"start_f_p"

//start_poi	唤起poi		运营关注-唤起app方式
#define _DENGTA_start_poi   @"start_poi"

//start_r	唤起路线		运营关注-唤起app方式
#define _DENGTA_start_r     @"start_r"

// start_re_	唤起来源的字段头
#define _DENGTA_start_re_   @"start_re_"

//start_st	唤起街景		运营关注-唤起app方式
#define _DENGTA_start_st    @"start_st"

// Added by nopwang end

    /* **** nav **** */
// nav_es_bs	终点输入-界面“搜索”btn
//#define _DENGTA_nav_es_bs       @"nav_es_bs" ??

// nav_es_fav	终点输入-收藏的点
#define _DENGTA_nav_es_fav      @"nav_es_fav"

// nav_es_hl	终点输入-历史记录
#define _DENGTA_nav_es_hl       @"nav_es_hl"

// nav_es_m	终点输入-地图选点
#define _DENGTA_nav_es_m        @"nav_es_m"

// nav_es_st_bs	起点输入-界面“搜索”btn
//#define _DENGTA_nav_es_st_bs    @"nav_es_st_bs" ??

// nav_es_st_fav	起点输入-收藏的点
#define _DENGTA_nav_es_st_fav   @"nav_es_st_fav"

// nav_dr_point_fav  导航-驾车tab-途经点-收藏的点
#define _DENGTA_nav_dr_point_fav @"nav_dr_point_fav"

// nav_es_st_hl	起点输入-历史记录
#define _DENGTA_nav_es_st_hl    @"nav_es_st_hl"

// nav_es_st_m	起点输入-地图选点
#define _DENGTA_nav_es_st_m     @"nav_es_st_m"

// nav_es_st_v	起点输入-语音
#define _DENGTA_nav_es_st_v     @"nav_es_st_v"

// nav_es_st_ml 起点输入-我的位置
#define _DENGTA_nav_es_st_ml     @"nav_es_me"

// nav_es_st_ws	起点输入-软键盘“搜索”btn
#define _DENGTA_nav_es_st_ws    @"nav_es_st_ws"

// nav_es_v	终点输入-语音
#define _DENGTA_nav_es_v        @"nav_es_v"

// nav_es_ml 终点输入-我的位置
#define _DENGTA_nav_es_ml     @"nav_st_me"

// nav_es_ws	终点输入-软键盘“搜索”btn
#define _DENGTA_nav_es_ws       @"nav_es_ws"

// nav_es_loc_start  起点输入-我的位置  起点输入页面中，点击“我的位置”（GPS定位的点）的数量，同时上报此时是哪个tab（公交、驾车、步行）被激活，即需要附上来自哪个tab这个参数   起终点输入页-起点输入页
#define _DENGTA_nav_es_loc_start        @"nav_es_loc_start"

// nav_es_loc_end    终点输入-我的位置  终点输入页面中，点击“我的位置”（GPS定位的点）的数量，同时上报此时是哪个tab（公交、驾车、步行）被激活，即需要附上来自哪个tab这个参数   起终点输入页-终点输入页
#define _DENGTA_nav_es_loc_end        @"nav_es_loc_end"

// nav_es_sug        终点输入-suggestion     终点输入页中，suggestion的点击量，同时上报时需要带上被激活的tab（公交、驾车、步行），即需要附上来自哪个tab这个参数          终点输入-suggestion
#define _DENGTA_nav_es_sug            @"nav_es_sug"

// nav_st_sug        起点输入-suggestion     起点输入页中，suggestion的点击量，同时上报时需要带上被激活的tab（公交、驾车、步行），即需要附上来自哪个tab这个参数          起终点输入页-起点输入页
#define _DENGTA_nav_st_sug            @"nav_st_sug"

// bus_subway_tab_cl 统计公交tab下地铁图入口的点击次数
#define _DENGTA_bus_subway_tab_cl     @"bus_subway_tab_cl"

// game_click	彩蛋游戏点击
#define _DENGTA_game_click       @"game_click"

/****起终点输入主子点*****/

#define _DENGTA_sug_start_main_c   @"sug_start_main_c" //起点输入框主点点击

#define _DENGTA_sug_start_sub_c   @"sug_start_sub_c" //起点输入框子点点击

#define _DENGTA_sug_end_main_c   @"sug_end_main_c" //终点输入框主点点击

#define _DENGTA_sug_end_sub_c   @"sug_end_sub_c" //终点输入框主点点击

#define _DENGTA_sug_s_e_main_sub_e @"sug_s_e_main_sub_e" //起终点输入框主子点展示

/****起终点输入主子点*****/

/********sug*******/

#define _DENGTA_sug_rank_near_sub_se  @"sug_rank_near_sub_se"  //附近搜索框sug重排历史记录

#define _DENGTA_sug_rank_home_sub_se  @"sug_rank_home_sub_se"   //主搜索框sug重排历史记录

#define _DENGTA_sug_rank_start_sub_se @"sug_rank_start_sub_se" //起点输入框起点sug重排历史记录

#define _DENGTA_sug_rank_end_sub_se   @"sug_rank_end_sub_se"   //终点输入框起点sug重排历史记录

/********sug*******/

/****主搜索框子点*****/

#define _DENGTA_sug_home_main_c   @"sug_home_main_c" //主搜索框主点点击

#define _DENGTA_sug_home_sub_c    @"sug_home_sub_c" //主搜索框子点点击

#define _DENGTA_sug_home_e        @"sug_home_e" //主搜索框主子点展示

#define MAP_POI_PS_S_G @"map_poi_ps_s_g" // 主搜索框-sug（不包括带子点和类别词的情况）

/****主搜索框子点*****/


/* ---- 堵点 ---- */
// per_p_point	我tab-收藏夹-堵点    518
#define _DENGTA_per_p_point       @"per_f_tab_jbutton"

// per_p_point_loc_nop	我tab-口袋路况-堵点-定位到没有堵点的城市    new
#define _DENGTA_per_p_point_loc_nop       @"per_p_point_loc_nop"

// per_p_point_item_c	口袋路况-堵点-点击页卡    523
#define _DENGTA_per_p_point_item_c       @"per_p_point_item_c"

// per_p_point_item_tra	口袋路况-堵点-点击页卡查看某个堵点的详情-实时路况    524
#define _DENGTA_per_p_point_item_tra       @"per_p_point_item_tra"

// per_p_point_item_halfh	口袋路况-堵点-点击页卡查看某个堵点的详情-30分钟后    525
#define _DENGTA_per_p_point_item_halfh       @"per_p_point_item_halfh"

// per_p_point_item_fullh	口袋路况-堵点-点击页卡查看某个堵点的详情-1小时后    526
#define _DENGTA_per_p_point_item_fullh       @"per_p_point_item_fullh"

// per_p_point_item_twoh	口袋路况-堵点-点击页卡查看某个堵点的详情-2小时后    527
#define _DENGTA_per_p_point_item_twoh       @"per_p_point_item_twoh"

// per_p_point_item_reason	口袋路况-堵点-点击页卡查看某个堵点的详情-拥堵原因    528
#define _DENGTA_per_p_point_item_reason       @"per_p_point_item_reason"

// per_p_point_item_delete	口袋路况-堵点-点击页卡查看某个堵点的详情-删除    new
#define _DENGTA_per_p_point_item_delete       @"per_p_point_item_delete"

// per_p_point_add	口袋路况-堵点-添加    529
#define _DENGTA_per_p_point_add       @"per_p_point_add"

// per_p_point_add_item	口袋路况-堵点-添加-添加列表页-点击页卡查看某个堵点的详情-添加    new
#define _DENGTA_pper_p_point_add_item       @"per_p_point_add_item"

// per_p_point_add_changecity	口袋路况-堵点-添加-切换城市    new
#define _DENGTA_per_p_point_add_changecity       @"per_p_point_add_changecity"

// per_p_point_req_time	我tab-口袋路况-堵点-拉取堵点信息时长    new
#define _DENGTA_per_p_point_req_time       @"per_p_point_req_time"

// per_p_point_add  收藏夹-堵点-添加    收藏夹-堵点-添加-添加列表页，计算直接在这个页面进行“添加+”icon的点击总次数  口袋路况
#define _DENGTA_per_p_point_add       @"per_p_point_add"

// per_f_route_add  收藏夹-路线-添加
#define _DENGTA_per_f_route_add       @"per_f_route_add"


/* ---- end ---- */


/* **** oil price **** */

/*
 埋点名称：oil_dis_enter
 埋点描述：发现-实时油价-PV UV
 */
#define OIL_DIS_ENTER @"oil_dis_enter"
/*
 埋点名称：oil_dis_unsubs
 埋点描述：发现-实时油价-未订阅态 PV UV
 */
#define OIL_DIS_UNSUBS @"oil_dis_unsubs"
/*
 埋点名称：oil_dis_unsubs_station
 埋点描述：发现-实时油价-未订阅态-点击更多加油站 PV UV
 */
#define OIL_DIS_UNSUBS_STATION @"oil_dis_unsubs_station"
/*
 埋点名称：oil_dis_cancel_subs
 埋点描述：发现-实时油价-取消订阅操作的 PV UV
 */
#define OIL_DIS_CANCEL_SUBS @"oil_dis_cancel_subs"
/*
 埋点名称：oil_dis_subs_station
 埋点描述：发现-实时油价-点击订阅的加油站 PV UV
 */
#define OIL_DIS_SUBS_STATION @"oil_dis_subs_station"
/*
 埋点名称：oil_dis_route_c
 埋点描述：发现-实时油价-点击路线btn PV UV
 */
#define OIL_DIS_ROUTE_C @"oil_dis_route_c"

/*
 埋点名称：oil_ugc_oil
 补充说明：附带参数使用带参数的上报接口即可
 埋点描述：上报btn-点击上报实时油价icon-PV UV
 */
#define OIL_UGC_OIL @"oil_ugc_oil"
/*
 埋点名称：oil_ugc_oil_report_succ
 补充说明：附带参数使用带参数的上报接口即可
 埋点描述：上报btn-上报实时油价icon-上报界面界面-提交上报成功PV UV
 */
#define OIL_UGC_OIL_REPORT_SUCC @"oil_ugc_oil_report_succ"
/*
 埋点名称：oil_ugc_oil_report_mod
 埋点描述：上报btn-点击上报实时油价icon-上报界面-点击更改btn  PV UV
 */
#define OIL_UGC_OIL_REPORT_MOD @"oil_ugc_oil_report_mod"
/*
 埋点名称：oil_ugc_oil_report_mod_s
 埋点描述：上报btn-点击上报实时油价icon-上报界面-点击更改btn-搜索  PV UV
 */
#define OIL_UGC_OIL_REPORT_MOD_S @"oil_ugc_oil_report_mod_s"
/*
 埋点名称：oil_setting_myoil
 埋点描述：设置-我关注的油号-PV UV
 */
#define OIL_SETTING_MYOIL @"oil_setting_myoil"
/*
 埋点名称：oil_setting_myoil_select
 补充说明：附带参数使用带参数的上报接口即可
 埋点描述：设置-我关注的油号-选择设置油号  PV UV
 */
#define OIL_SETTING_MYOIL_SELECT @"oil_setting_myoil_select"
/*
 埋点名称：oil_fdialog_e
 埋点描述：关注油号弹窗展现数：PV/UV
 */
#define OIL_FDIALOG_E @"oil_fdialog_e"
/*
 埋点名称：oil_fdialog_close
 埋点描述：关注油号弹窗关闭
 */
#define OIL_FDIALOG_CLOSE @"oil_fdialog_close"
/*
 埋点名称：oil_fdialog_sure
 埋点描述：关注油号弹窗-确定
 */
#define OIL_FDIALOG_SURE @"oil_fdialog_sure"
/*
 埋点名称：oil_fsetting_result
 埋点描述：关注油号设置结果
 */
#define OIL_FSETTING_RESULT @"oil_fsetting_result"
/*
 埋点名称：oil_detail_e
 埋点描述：有油价的详情页展现数：PV/UV
 */
#define OIL_DETAIL_E @"oil_detail_e"
/*
 埋点名称：oil_push
 埋点描述：油价推送次数
 */
#define OIL_PUSH @"oil_push"
/*
 埋点名称：oil_push_open
 埋点描述：油价推送打开数
 */
#define OIL_PUSH_OPEN @"oil_push_open"
/*
 埋点名称：oil_subs_succ
 埋点描述：单日订阅加油站总次数、人数
 */
#define OIL_SUBS_SUCC @"oil_subs_succ"

#define _DENGTA_BUS_RESULT_CLK @"nav_bus_s_l"
#define _DENGTA_BUS_RESULT_SHOW @"nav_bus_s_label_show"
#define _DENGTA_CAR_RESULT_CLK @"nav_car_s_l"
#define _DENGTA_MIX_RESULT_SHOW @"map_bline_detail_e"

/*
 埋点名称：nav_stop_s_e
 埋点描述：起点停车横幅-出现
 */
#define NAV_STOP_S_E @"nav_stop_s_e"
/*
 埋点名称：nav_stop_s_here
 埋点描述：起点停车横幅-停这里
 */
#define NAV_STOP_S_HERE @"nav_stop_s_here"
/*
 埋点名称：nav_stop_s_more
 埋点描述：起点停车横幅-查看更多
 */
#define NAV_STOP_S_MORE @"nav_stop_s_more"
/*
 埋点名称：nav_stop_e_e
 埋点描述：终点停车横幅-出现
 */
#define NAV_STOP_E_E @"nav_stop_e_e"
/*
 埋点名称：nav_stop_e_here
 埋点描述：终点停车横幅-停这里
 */
#define NAV_STOP_E_HERE @"nav_stop_e_here"
/*
 埋点名称：nav_stop_e_more
 埋点描述：终点停车横幅-查看更多
 */
#define NAV_STOP_E_MORE @"nav_stop_e_more"
/*
 埋点名称：nav_stop_s_e_2km
 埋点描述：导航距离小于2km时，出现的起点停车横幅
 */
#define NAV_STOP_S_E_2KM @"nav_stop_s_e_2km"
/*
 埋点名称：nav_more_park
 埋点描述：导航中-更多-查看终点停车场
 */
#define NAV_MORE_PARK @"nav_more_park"
/*
 埋点名称：nav_park_m_c
 埋点描述：导航中-点选停车场marker
 */
#define NAV_PARK_M_C @"nav_park_m_c"
/*
 埋点名称：nav_park_m_end
 埋点描述：导航中-停车场marker-设为终点
 */
#define NAV_PARK_M_END @"nav_park_m_end"


// 违章代缴埋点ID
// 发现-车辆列表-车辆card【支持在线缴费】
#define  PCY_CARL_CARD_PAY          @"pcy_carl_card_pay"

// 发现-车辆列表-车辆记录－勾选在线缴费
#define  PCY_CARL_LIST_CHECKPAY     @"pcy_carl_list_checkpay"

// 发现-车辆列表-车辆记录－底部在线缴费btn点击
#define  PCY_CARL_LIST_PAY          @"pcy_carl_list_pay"

// 发现-车辆列表-车辆记录－在线缴费btn-缴费人信息-发票
#define  PCY_PAYINFOR_BILL          @"pcy_payinfor_bill"

// 发现-车辆列表-车辆记录－在线缴费btn-缴费人信息-确定
#define  PCY_PAYINFOR_CONFIRM       @"pcy_payinfor_confirm"

// 发现-车辆列表-车辆记录－在线缴费btn-缴费人信息-订单确认页微信支付
#define  PCY_ORDER_PAY              @"pcy_order_pay"

// 发现-车辆列表-车辆记录－在线缴费btn-缴费人信息-订单确认页返回btn
#define  PCY_ORDER_PAYBACK          @"pcy_order_payback"

// 发现-车辆列表-右上角我的订单
#define  PCY_CARL_ORDER             @"pcy_carl_order"

// 发现-车辆列表-右上角我的订单-订单详情
#define  PCY_CARL_ORDER_DETAILS     @"pcy_carl_order_details"

// 发现-车辆列表-右上角我的订单-订单详情-取消订单
#define  PCY_CARL_ORDER_CANCEL      @"pcy_carl_order_cancel"

// 发现-车辆列表-右上角我的订单-订单详情-微信支付
#define  PCY_CARL_ORDER_PAY         @"pcy_carl_order_pay"

// 通知栏推送-订单详情
#define  PCY_PUSH_ORDER             @"pcy_push_order"

// 发现-车辆列表-违章记录-缴费信息
#define  PCY_PAY_SUCCESS             @"pcy_pay_success"

//常用地址埋点
#define NAV_HOME_C  @"nav_home_c"

#define NAV_COMPANY_C @"nav_company_c"


/*
 埋点名称：nav_route_error_fb
 埋点描述：路线请求无结果返回错误代码时，弹窗-去反馈btn的点击PV/UV
 */
#define NAV_ROUTE_ERROR_FB @"nav_route_error_fb"

/*
 埋点名称：nav_route_error_retry
 埋点描述：路线请求无结果返回网络型错误代码时，弹窗-在线重试btn的点击PV/UV
 */
#define NAV_ROUTE_ERROR_RETRY @"nav_route_error_retry"

/*
 埋点名称：nav_route_error_parse
 埋点描述：路线请求无结果返回网络型错误代码时，若是客户端数据解析错误，则进行上报
 */
#define NAV_ROUTE_ERROR_PARSE @"nav_route_error_parse"
/*
 埋点名称：nav_route_error_network
 埋点描述：路线请求无结果返回网络型错误代码时，若是客户端判断出是非哈雷传回来的网络类型错误，则进行上报
 */
#define NAV_ROUTE_ERROR_NETWORK @"nav_route_error_network"


// i保养埋点
// i保养-入口访问
#define IMT_ENTER               @"imt_enter"

// i保养-登录成功
#define IMT_LOGIN_SUCC          @"imt_login_succ"


// 十一活动埋点
// 首页运营浮层出现的次数
#define OP_MAIN_FLAYER_E        @"op_main_flayer_e"

// 首页运营浮层-btn
#define OP_MAIN_FLAYER_CLICK    @"op_main_flayer_click"

// 上报-新增上报项（运营上报事件）
#define UGC_NEW_C               @"ugc_new_c"

// 导航/地图首页上报入口统计
#define UGC_PAGE                @"ugc_page"

// 上报事件后调起分享弹窗的次数
#define UGC_REPORTED_SHARE_E    @"ugc_reported_share_e"

// 上报事件后分享弹窗-分享
#define UGC_REPORTED_SHARE_Y    @"ugc_reported_share_y"

// 上报事件后分享弹窗-不分享
#define UGC_REPORTED_SHARE_N    @"ugc_reported_share_n"

// 上报事件后分享弹窗取消默认勾选
#define UGC_REPORTED_SHARE_C    @"ugc_reported_share_c"

// 单日上报事件总次数
#define UGC_REQ_REPORT          @"ugc_req_report"

// H5分享成功
#define OP_H5_SHARE             @"op_h5_share"

// H5登录
#define OP_H5_LOGIN             @"op_h5_login"

// H5登录框出现次数
#define UGC_OP_LOGIN_C          @"ugc_op_login_c"

// H5登录框出现次数
#define UGC_OP_LOGIN_SUCC       @"ugc_op_login_succ"

// 我的上报-点击新增运营上报项list
#define UGC_MY_NEWLIST          @"ugc_my_newlist"

// 新增运营上报项底图marker
#define UGC_MARKER_OPC          @"ugc_marker_opc"

// 新增运营上报项marker弹窗-赞
#define UGC_MARKER_LAYER_G      @"ugc_marker_layer_g"

// 检索上报
// 地图缩放拖拽操作
#define MAP_ZOOM_OP             @"map_zoom_op"

//筛选器
//范围项点击次数
#define MAP_FILTER_RANGE       @"map_filter_range"  

//分类项点击次数
#define MAP_FILTER_CATA       @"map_filter_cata"

//排序点击次数
#define MAP_FILTER_SORT      @"map_filter_sort"

//筛选器整体展示的次数
#define MAP_FILTER_ALL_E       @"map_filter_all_e"

//POI 列表页 热门推荐点击次数
#define MAP_POI_LIST_RE_C      @"map_poi_list_re_c"

//POI 列表页 热门推荐展示次数
#define MAP_POI_LIST_RE_E      @"map_poi_list_re_e"

// 步行导航
// 步行导航结束-出步行小结页
#define NAV_WK_SUMM_E           @"nav_wk_summ_e"

// 步行导航时常统计
#define NAV_WK_TIME             @"nav_wk_time"

// 步行小节页-分享成功
#define WKSUMMARY_SHARE_SUCC    @"wksummary_share_succ"

// 步行小节页-晒一下
#define WKSUMMARY_SHARE         @"wksummary_share"

// 限行提醒
// 限行提醒push点击量
#define PCY_LIMIT_PUSH_CLICK    @"pcy_limit_push_click"

/* **** bus announcement **** */

//POI 公交路线详情页
#define BUS_BOARD_E @"bus_board_e"

//导航 公交路线列表页
#define BUS_BOARD_TAG_E @"bus_board_tag_e"

//导航 公交路线详情页
#define BUS_BOARD_DETAIL_E @"bus_board_detail_e"

#define NAV_DR_M_C @"nav_dr_m_c"

//加油站点击
#define ROUTE_OIL_MARKER  @"route_oil_marker"


/* ---- end ---- */

//events_wechat  来自微信里面跳转到腾讯地图活动专区的行为
#define EVENTS_WECHAT @"events_wechat"
/* ---- end ---- */

/*********城市地图**********/

//城市地图的tips的展现次数
#define CITYMAP_TIPS_E @"citymap_tips_e"

//城市地图的tips点击
#define CITYMAP_TIPS_C @"citymap_tips_c"

//城市地图列表点击
#define CITYMAP_LIST_C @"citymap_list_c"

/*********城市地图**********/

/* ---- 主题 ---- */
//主题-展示主题成功
#define SKIN_LOAD_SUCC          @"skin_load_succ"
/* ---- end ---- */

/* ---- 室内底图 ---- */
//indoor_guide_e	室内地图楼层导览触发次数	室内地图导览与楼层选择控件触发次数
#define INDOOR_GUIDE_E          @"indoor_guide_e"
//indoor_sel_c	室内地图楼层选择控件点击次数	室内地图楼层选择控件点击次数
#define INDOOR_SEL_C            @"indoor_sel_c"
//indoor_guide_c	室内地图导览控件点击次数	室内地图导览控件点击次数
#define INDOOR_GUIDE_C          @"indoor_guide_c"
//indoor_guide_s	室内地图导览页室内搜索框点击次数	室内地图导览页室内搜索框点击次数
#define INDOOR_GUIDE_S          @"indoor_guide_s"
//indoor_guide_hot	室内地图导览页热门推荐类别点击总次数	室内地图导览页热门推荐类别点击总次数
#define INDOOR_GUIDE_HOT        @"indoor_guide_hot"
//indoor_guide_f	室内地图导览页楼层概览总点击总次数	室内地图导览页各楼层概览总点击总次数
#define INDOOR_GUIDE_F          @"indoor_guide_f"
//indoor_guide_s_sug	室内地图导览页室内搜索框sug的点击次数	室内地图导览页室内搜索框sug的点击次数
#define INDOOR_GUIDE_S_SUG      @"indoor_guide_s_sug"
//indoor_guide_s_re	室内地图室内搜索输入页推荐类别的总点击次数	室内地图室内搜索输入页推荐类别的总点击次数
#define INDOOR_GUIDE_S_RE       @"indoor_guide_s_re"
//indoor_poi_list_all	室内POI列表页条目点击的总次数	室内POI列表页条目点击的总次数
#define INDOOR_POI_LIST_ALL     @"indoor_poi_list_all"

/* ---- end ---- */

/* ---- 家和单位 ---- */
#define HOME_CARD_MAIN_SE_C         @"home_card_main_se_c"
#define COM_CARD_MAIN_SE_C          @"com_card_main_se_c"
#define HOME_CARD_MAIN_EDIT_C       @"home_card_main_edit_c"
#define COM_CARD_MAIN_EDIT_C        @"com_card_main_edit_c"
#define HOME_CARD_NAV_SE_C          @"home_card_nav_se_c"
#define COM_CARD_NAV_SE_C           @"com_card_nav_se_c"
#define HOME_CARD_NAV_EDIT_C        @"home_card_nav_edit_c"
#define COM_CARD_NAV_EDIT_C         @"com_card_nav_edit_c"
#define HOME_CARD_BUS_SE_C          @"home_card_bus_se_c"
#define COM_CARD_BUS_SE_C           @"com_card_bus_se_c"
#define HOME_CARD_BUS_EDIT_C        @"home_card_bus_edit_c"
#define COM_CARD_BUS_EDIT_C         @"com_card_bus_edit_c"
#define HOME_CARD_CYCLE_SE_C        @"home_card_cycle_se_c"
#define COM_CARD_CYCLE_SE_C         @"com_card_cycle_se_c"
#define HOME_CARD_WK_EDIT_C         @"home_card_wk_edit_c"
#define HOME_CARD_CYCLE_EDIT_C      @"home_card_cycle_edit_c"
#define COM_CARD_WK_EDIT_C          @"com_card_wk_edit_c"
#define COM_CARD_CYCLE_EDIT_C       @"com_card_cycle_edit_c"
#define HOME_CARD_PER_F_C           @"home_card_per_f_c"
#define COM_CARD_PER_F_C            @"com_card_per_f_c"
#define HOME_COM_TRAFFIC_REQ_COUNT  @"home_com_traffic_req_count"
#define HOME_CARD_PER_CS_C          @"home_card_per_cs_c"
#define COM_CARD_PER_CS_C           @"com_card_per_cs_c"
/* ---- end ---- */

/* ---- 实时公交 ---- */
#define MAP_NEXTBUS_ON              @"map_nextbus_on"
#define MAP_NEXTBUS_OFF             @"map_nextbus_off"
#define NAV_BUS_NEXTBUS_SHOW        @"nav_bus_nextbus_show"
#define MAP_BUS_STATION_SHOW        @"map_bus_station_show"
#define MAP_BUS_LAYER_SHOW          @"map_bus_layer_show"
#define NAV_BUS_NEXTBUS_CLICK       @"nav_bus_nextbus_click"
#define NAV_BUS_S_NEXTBUS           @"nav_bus_s_nextbus"
#define NAV_BUS_RESULT_NEXTBUS      @"nav_bus_result_nextbus"
#define MAP_BUS_STATION_CLICK       @"map_bus_station_click"
#define MAP_BUS_LAYER_CLICK         @"map_bus_layer_click"
#define NEXTBUS_NEARBY              @"nextbus_nearby"
#define NEXTBUS_MYFOLLOW            @"nextbus_myfollow"
#define NEXTBUS_FOLLOW              @"nextbus_follow"
#define NEXTBUS_UNFOLLOW            @"nextbus_unfollow"
#define NEXTBUS_REFRESH             @"nextbus_refresh"
#define NEXTBUS_DETAILS_CLICK       @"nextbus_details_click"
#define DETAILS_MAP_CLICK           @"details_map_click"
/* ---- end ---- */

/* ---- 检索热词 ---- */
#define SEARCH_OPERLETTER_PV @"search_operletter_pv" //主检索框-运营词展现
#define SEARCH_OPERLETTER_CL @"search_operletter_cl" //主检索框-运营词点击
#define SEARCH_QUERY_PV      @"search_query_pv"      //主搜索框-检索词展现
#define SEARCH_QUERY_CL      @"search_query_cl"      //主搜索框-检索词点击
/* ---- end ---- */


/* ---- 九宫格 ---- */
#define LSHARE_LIST_C   @"lshare_list_c" //好友同行点击
#define PER_OL_CT       @"per_ol_ct"    //离线地图
#define PCY_ENTER       @"pcy_enter"    //违章查询
#define DOG_DIS_ENTER   @"dog_dis_enter"//电子狗
#define IL_ENTER_OUT    @"il_enter_out" //i生活
#define EVENTS_ENTER    @"events_enter" //活动专区
/* ---- end ---- */

/* ---- 定位相关 ---- */
#define A_LOCATION_TIME @"A_LOCATION_TIME"
/* ---- end ---- */

/* ---- 导航语音包 ---- */
#define NAV_VOICEPACKET_CL              @"nav_voicepacket_cl" //扩展字段：entrance:discovery,routeplan
#define NAV_VOICEPACKET_DOWNLOAD_SUC    @"nav_voicepacket_download_suc" //扩展：dialect:dongbei,sichuan
#define NAV_VOICEPACKET_START_SUC       @"nav_voicepacket_start_suc"
#define NAV_VOICEPACKET_DEL             @"nav_voicepacket_del"
/* ---- end ---- */

/* ---- 意见反馈 ---- */
#define FEEDBACK_ADD_CL    @"feedback_add_cl"
/* ---- end ---- */

/* ---- 6.8 ---- */
#define HOMEPAGE_SLIDER_CL          @"homepage_slider_cl"//首页点击滑块按钮次数
#define DIS_NEAR_CATEGORY_CL        @"dis_near_category_cl"//发现tab下附近模块后，每个分类点击的次数 传参：按钮字符串
#define SUG_START_PS_C              @"sug_start_ps_c" //导航起点上填
#define SUG_END_PS_C                @"sug_end_ps_c" //导航终点上填
#define SUG_HOME_PS_C               @"sug_home_ps_c" //主搜索框sug上填
/* ---- end ---- */

/* ---- 常用地址推荐 ---- */
#define MAP_HIS_EXT_C       @"map_his_ext_c"        //点击端外历史记录
#define MAP_HIS_EXT_D       @"map_his_ext_d"        //删除端外历史记录
#define MAP_HIS_EXT_ROUTE   @"map_his_ext_route"    //端外历史记录发起路线
#define MAP_HIS_ROUTE       @"map_his_route"        //历史记录发起路线
#define MAP_HIS_Taxi        @"map_his_carhail"      //历史记录发起打车
#define MAP_POI_PS_C        @"map_poi_ps_c"         //清空历史记录
#define MAP_HIS_POI_M_D     @"map_his_poi_m_d"      //历史记录单条删除-主搜索点击
#define MAP_HIS_NEAR_M_D    @"map_his_near_m_d"     //历史记录单条删除-附近检索点击
#define MAP_POI_PR_L_MAP    @"map_poi_pr_l_map"     //搜索结果-点击列表条目
#define MAP_POI_LIST_GO_BTN @"map_poi_list_go_btn"  //搜索结果-列表页点击去这里btn
#define MAP_POI_LIST_NEW_GO_BTN @"filter_go_c"  //新版搜索结果-列表页点击去这里btn
#define MAP_POI_LIST_PHONE_BTN @"filter_phone_c"  //新版搜索结果-列表页点击电话btn
#define MAP_POI_SUB_ITEM    @"map_poi_sub_item"     //搜索结果-列表页点击子点

#define MAP_POI_EXTRA_WX    @"extra_poi_wx"         //微信跳转POI记录
#define MAP_POI_EXTRA_QQ    @"extra_poi_qq"         //QQ跳转POI记录
#define MAP_POI_EXTRA_QB    @"extra_poi_qq_browser" //QQ浏览器跳转POI记录
#define MAP_POI_EXTRA_OTHER @"extra_poi_other"      //其他应用跳转POI记录
#define MAP_POI_OFFLINE     @"offline_poi"          //离线检索获得的POI记录

/* ---- end ---- */

/* ---- 起终点上报 ----*/
#define SE_DELETEHISTORY          @"se_deletehistory"              //起终点历史记录删除
#define SE_CLEARHISTORY           @"se_clearhistory"               //起终点历史记录清空
/* ---- end ----*/

/* ---- 结束导航弹窗 ---- */
#define NAV_POP_ENDNAV            @"nav_pop_endnav"             //“是否结束当前导航”弹窗出现次数
#define NAV_POP_ENDNAV_CANCEL     @"nav_pop_endnav_cancel"      //出“是否结束当前导航”弹窗后，点击“取消”的次数
#define NAV_POP_ENDNAV_END        @"nav_pop_endnav_end"         //出“是否结束当前导航”弹窗后，点击“结束”的次数
/* ---- end ---- */

//---底图操作相关埋点-----//
#define MAP_SCALE                   @"map_scale"



//--- 区域内搜索相关埋点 ---//
#define SEARCH_REGION_BTN_SHOW                  @"search_region_btn_show"
#define SEARCH_REGION_BTN_CLICK                 @"search_region_btn_click"
#define SEARCH_REGION_HASRESULT_SCALE           @"search_region_hasresult_scale"
#define SEARCH_REGION_HASRESULT_KEYWORDS        @"search_region_hasresult_keywords"
#define SEARCH_REGION_HASNORESULT_SCALE         @"search_region_hasnoresult_scale"
#define SEARCH_REGION_HASNORESULT_KEYWORDS      @"search_region_hasnoresult_keywords"

//--- 历史记录相关埋点 ---//
#define HISTORY_DATABASE              @"history_database"
#define HISTORY_DATABASE_OPERATION    @"history_database_operation"
#define HISTORY_DATABASE_ERROR        @"history_database_error"
#define HISTORY_TAGS_NONE             @"history_tags_none"
#define HISTORY_TAGS_JSON_FAILED      @"history_tags_json_failed"
#define HISTORY_TAGS_ERROR            @"history_tags_error"
#define HISTORY_REPORT_USERID         @"history_report_userId"

/* ----- POI底部Bar ----- */
#define BOTTOMBAR_COLLECT           @"bottomBar_collect"
#define BOTTOMBAR_SHARE             @"bottomBar_share"
#define BOTTOMBAR_CANCEL_COLLECT    @"bottomBar_cancel_collect"
#define BOTTOMBAR_NEAR              @"map_poi_p_c_s"
#define BOTTOMBAR_NAV               @"map_poi_pd_s_nav"
#define BOTTOMBAR_TOTHIS            @"map_poi_pd_r"
#define UPPERRIGHTCORNER_ERROREORR  @"upperRightCorner_errorEorr"
#define UPPERRIGHTCORNER_SHAREIT    @"upperRightCorner_shareIt"
/* -------- end -------- */

/*----- 闪屏埋点 ------*/
#define SPLASH_BOOT_GETCITY_SUCCESS     @"splash_boot_getcity_success"
#define SPLASH_BOOT_GETCITY_FAILED      @"splash_boot_getcity_failed"
#define SPLASH_WARMBOOT                 @"splash_warmboot"
#define SPLASH_BOOT_LOCATION_FAILED     @"splash_boot_location_failed"
#define SPLASH_BOOT_LOCATION_TIMEOUT    @"splash_boot_location_timeout"
#define SPLASH_CLOUD_TIMEOUT            @"splash_cloud_timeout"
#define SPLASH_CLEAN_CACHE              @"splash_clean_cache"
#define SPLASH_CHECK_OTHERCITY_DATA     @"splash_check_othercity_data"
#define SPLASH_CLEAN_OTHERCITY_DATA     @"splash_clean_othercity_data"
#define SPLASH_SAVE_DATA                @"splash_save_data"
#define SPLASH_REMOVE_INVALID           @"splash_remove_invalid"
#define SPLASH_DOWNLOAD_IMAGE_SUCCESS   @"splash_download_image_success"
#define SPLASH_CHECK_DISPLAYMODE        @"splash_check_displaymode"
#define SPLASH_DISPLAYMODE_CHANGED      @"splash_displaymode_changed"
#define SPLASH_REQUEST_PARAM            @"splash_request_param"
#define SPLASH_RESPONSE_DATA_NIL        @"splash_response_data_nil"
#define SPLASH_RESPONSE_DATA_PARAM      @"splash_response_data_param"
#define SPLASH_REQUEST_FAILED           @"splash_request_failed"
/*----- end ------*/

/*----- 语音广场和主题广场埋点来源参数Key ------*/
#define VOICE_AND_THEME_FROM_KEY     @"sourceFrom"
/*----- end ------*/

/*----- 积水地图 ------*/
//首页天气动效展现
#define ACCUMWATER_HOMEPAGE_WEATHER             @"homepage_weather"
//首页天气预警tips展现
#define ACCUMWATER_HOMEPAGE_WEATHER_TIPS        @"homepage_weather_tips"
//侧边栏-主题地图-积水地图入口点击
#define ACCUMWATER_LAYER_SUBJECT_PUDDLE         @"layer_subject_puddle"
//首页图层-积水地图气泡曝光
#define ACCUMWATER_HOMEPAGE_LAYER_PUDDLE_SHOW   @"homepage_layer_puddle_show"
//首页图层-积水地图气泡点击
#define ACCUMWATER_HOMEPAGE_LAYER_PUDDLE_CLICK  @"homepage_layer_puddle_click"
/*----- end ------*/

@class JsonPOIInfo;

@interface BeaconEvent (QMExtend)

@property (nonatomic, copy) NSString *localReportTime;

@end


@protocol UserOpDataManagerDebugListener <NSObject>

@optional
- (void)onBeaconEventTrigger:(BeaconEvent *)event;

@end

//拦截者做额外处理
@protocol UserOpDataManagerInterceptor <NSObject>

@optional

- (void)userActionEventTriggered:(NSString *)userAction params:(NSDictionary *)params;// 埋点事件被触发
- (void)directUserActionEventTriggered:(NSString *)userAction params:(NSDictionary *)params;// 实时埋点事件被触发

@end

@interface UserOpDataManager : NSObject

QMSharedInstance_H(UserOpDataManager)

+ (void)initWhenApplicationDidFinishLaunch;
+ (void)performTaskWhenMapDidDrawOrTimeOut;
+ (void)performTaskWhenMainSceneWillEnterForeground;

- (void)accumulateKeyEventReport:(NSString *)name eventId:(NSString *)eventId result:(NSString *)result detail:(NSString *)detail;
//目前给公交新增了一个type类型的上传
- (void)accumulateKeyEventReport:(NSString *)name eventId:(NSString *)eventId result:(NSString *)result detail:(NSString *)detail type:(NSString *)type;
- (void)upload;

//Interceptor
- (void)addInterceptor:(NSObject<UserOpDataManagerInterceptor> *)interceptor;// 添加拦截者 - 执行顺序不保证有序
- (void)removeInterceptor:(NSObject<UserOpDataManagerInterceptor> *)interceptor;// 删除拦截者

+ (void)accumulateKeyEventPoi:(NSString *)name
                    requestid:(NSString *)requestid
                  totalbefore:(int)totalbefore
                     position:(NSString *)pageNumAndIndex
                          uid:(NSString *)uid
                        pname:(NSString *)pname;

//供运营埋点含operationId的使用
+ (void)accmulateKeyEventForDengtaWithEventName:(NSString *)name operationId:(NSInteger)operationId;

// Debug
- (void)setDebugListener:(id<UserOpDataManagerDebugListener>)listener;

@end
