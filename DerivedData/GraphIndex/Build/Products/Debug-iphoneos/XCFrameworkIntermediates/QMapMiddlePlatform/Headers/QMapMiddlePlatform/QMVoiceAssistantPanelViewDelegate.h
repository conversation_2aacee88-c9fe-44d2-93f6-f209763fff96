//
//  QMVoiceAssistantPanelDelegate.h
//  SOSOMap
//
//  Created by 王中周 on 2019/11/21.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 语音面板的状态
typedef NS_ENUM(NSUInteger, QMVoiceAssistantPanelViewState) {
    QMVoiceAssistantPanelViewStateUnknown,
    QMVoiceAssistantPanelViewStateRecommended,          // 默认处于推荐状态
    QMVoiceAssistantPanelViewStateRecognize,            // 语音识别中
    QMVoiceAssistantPanelViewStateLoading,              // loading态
    QMVoiceAssistantPanelViewStateSpeaking,             // 说的状态
    QMVoiceAssistantPanelViewStateListening,            // 听的状态
    QMVoiceAssistantPanelViewStateWeatherToday,         // 当天天气
    QMVoiceAssistantPanelViewStateWeatherSomeDay,       // 某一天天气
    QMVoiceAssistantPanelViewStateWeatherMultiDays,     // 多天天气
    QMVoiceAssistantPanelViewStateAirQualityToday,      // 当天空气质量
    QMVoiceAssistantPanelViewStateAirQualitySomeDay,    // 当天空气质量
    QMVoiceAssistantPanelViewStateAirQualityMultiDays,  // 多天空气质量
};

// 使用语音面板的场景
typedef NS_ENUM(NSUInteger, QMVoiceAssistantPanelViewScene) {
    QMVoiceAssistantPanelViewSceneNormal,                // 普通场景，默认值
    QMVoiceAssistantPanelViewSceneNaviPortraitDay,       // 导航竖屏白天模式
    QMVoiceAssistantPanelViewSceneNaviPortraitNight,     // 导航竖屏夜间模式
    QMVoiceAssistantPanelViewSceneNaviLandscapeDay,      // 导航横屏白天模式
    QMVoiceAssistantPanelViewSceneNaviLandscapeNight,    // 导航横屏夜间模式
    QMVoiceAssistantPanelViewSceneAINormal,              // 新的AI样式场景
    QMVoiceAssistantPanelViewSceneAIFusion,              // 新的AI样式融合场景
};

@class QMVoiceAssistantPanelView;
@protocol QMVoiceAssistantPanelViewDelegate <NSObject>
@optional

/// 当前使用语音的场景
- (QMVoiceAssistantPanelViewScene)voiceAssistantPanelViewScene;

/// 语音大面板展示的父 View，业务自己展示时无需实现
- (UIView *)voiceAssistantPanelViewSuperView;

/// 大面板 frame，业务自己负责展示也需要实现
- (CGRect)voiceAssistantPanelViewNormalFrame;

/// 小面板 frame，业务自己负责展示无需实现
- (CGRect)voiceAssistantPanelViewSmallFrame;

- (CGRect)voiceAssistantPanelViewAIFrame;


/// 新的面板即将显示，业务自己负责展示的不会有回调
- (void)willShowVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView;

/// 面板即将消失，通用方式和业务自己展示的都会有这个回调
- (void)willHideVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView;

/// 面板已经消失，通用方式和业务自己展示的都会有这个回调
- (void)didHideVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView;

/// 面板状态发生了变化，从大面板变成小面板，或者从小面板变成大面板；业务自己负责展示的不会有回调
- (void)changeVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView
                            fromState:(QMVoiceAssistantPanelViewState)fromState
                              toState:(QMVoiceAssistantPanelViewState)toState;

#pragma mark - 业务专用回调

/// 是否由业务自己负责展示面板
- (BOOL)isBusinessShowVoiceAssistantPanelView;

/// 语音模块提供大面板，由业务自己负责展示
- (void)businessShowVoiceAssistantPanelView:(QMVoiceAssistantPanelView *)panelView
                                  fromState:(QMVoiceAssistantPanelViewState)fromState
                                    toState:(QMVoiceAssistantPanelViewState)toState;

/// 告诉业务内容更新
- (void)businessUpdatePanelView:(QMVoiceAssistantPanelView *)panelView
                        content:(nullable id)content
                      fromState:(QMVoiceAssistantPanelViewState)fromState
                        toState:(QMVoiceAssistantPanelViewState)toState;

@end

NS_ASSUME_NONNULL_END
