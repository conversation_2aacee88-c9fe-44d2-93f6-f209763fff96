//
//  QMUGCLiveInfoDataUtils.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/3/16.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMRouteEventItem.h"
#import "QMUgcReportMarkerData.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMUGCLiveInfoDataUtils : NSObject

/// 是否需要请求最终的媒体 URL
/// @param routeEventItem routeEventItem
+ (BOOL)shouldRequestMediaURLWithRouteEventItem:(QMRouteEventItem *)routeEventItem;

/// 是否需要请求最终的媒体 URL
/// @param markerData markerData
+ (BOOL)shouldRequestMediaURLWithUGCMarkerData:(QMUgcReportMarkerData *)markerData;

/// 媒体 URL 是否可用
/// @param routeEventItem routeEventItem
+ (BOOL)mediaURLAvailiableWithRouteEventItem:(QMRouteEventItem *)routeEventItem;

/// 媒体 URL 是否可用
/// @param markerData markerData
+ (BOOL)mediaURLAvailiableWithUGCMarkerData:(QMUgcReportMarkerData *)markerData;

/// 请求可用的媒体 URL，可能是直播，可能是视频
/// @param routeEventItem routeEventItem
/// @param onFinish 完成的回调，若请求成功，则会填充 item.liveInfo.avaliableMediaURL
+ (QMNetworkRequest *)requestAvailableMeidaDataWithRouteEventItem:(QMRouteEventItem *)routeEventItem
                                                         onFinish:(void(^)(QMRouteEventItem * item))onFinish;

/// 请求可用的媒体 URL，可能是直播，可能是视频
/// @param markerData markerData
/// @param onFinish 完成的回调，若请求成功，则会填充 markerData.liveInfo.avaliableMediaURL
+ (QMNetworkRequest *)requestAvailableMeidaDataWithUGCMarkerData:(QMUgcReportMarkerData *)markerData
                                                        onFinish:(void(^)(QMUgcReportMarkerData * markerData))onFinish;

@end

NS_ASSUME_NONNULL_END

