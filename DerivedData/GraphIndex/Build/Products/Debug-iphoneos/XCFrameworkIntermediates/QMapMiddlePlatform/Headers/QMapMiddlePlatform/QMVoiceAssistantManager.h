//
//  QMVoiceAssistantManager.h
//  SOSOMap
//
//  Created by foo<PERSON><PERSON><PERSON>(王中周) on 2018/4/26.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMVoiceAssistantDefine.h>
#import <QMapMiddlePlatform/QMSemanticManager.h>
#import <QMapMiddlePlatform/QMVoiceAssistantConfigManager.h>
#import <QMapMiddlePlatform/QMVoiceAssistantPanelViewManager.h>
#import <QMapMiddlePlatform/QMVoiceAssistantProviderProtocol.h>

typedef NS_ENUM(NSUInteger, QMVoiceAssistantState) {
    QMVoiceAssistantStateUnknown,
    QMVoiceAssistantStateUnWakeup,          // 未唤醒
    QMVoiceAssistantStateWakeup,            // 唤醒态
    QMVoiceAssistantStateWaiting,           // 二轮交互等待中
    QMVoiceAssistantStateTVW,               // TVW 监听中
    QMVoiceAssistantStateClose             // 语音监听结束，叮当切回后台
};

typedef NS_ENUM(NSUInteger, QMVoiceAssistantAvailableState) {
    QMVoiceAssistantAvailableStateUnknown,
    QMVoiceAssistantAvailableStateOK,
    QMVoiceAssistantAvailableStateNetNotReachable,
    QMVoiceAssistantAvailableStateRecordNotStart,
    QMVoiceAssistantAvailableStateOfflineMode,
    QMVoiceAssistantAvailableStateCarplayConnected,
};

typedef NS_ENUM(NSInteger, QMVoiceAssistantDispatchSemanticResultState) {
    QMVoiceAssistantDispatchSemanticResultStateSuccess,
    QMVoiceAssistantDispatchSemanticResultStateRepeat,
    QMVoiceAssistantDispatchSemanticResultStateSpeakError,
};

extern NSString *const DingDangShowNotification;
extern NSString *const DingDangStopNotification;

typedef void(^QMAuthorizationBlock)(BOOL isFirst);
typedef void(^QMAudioReportCallback)(NSString *sessionId,NSString *content, BOOL intentUserFeedback, BOOL needUpdateFeedback, BOOL result);
typedef void(^QMAudioIntentHandleCallback)(NSString *sessionId, BOOL result);


@interface QMVoiceAssistantManager : NSObject

@property (nonatomic, strong, readonly) QMSemanticManager *semanticManager;
@property (nonatomic, strong, readonly) QMVoiceAssistantConfigManager *configManager;
@property (nonatomic, strong, readonly) QMVoiceAssistantPanelViewManager *panelViewManager;
@property (nonatomic) QMVoiceAssistantState status;
@property (nonatomic, readonly) BOOL isRecording;
@property (nonatomic, readonly) NSUInteger requestTraceID;  // 一次语音流程的唯一标识
@property (nonatomic, strong, readonly) QMSemanticData *semantic;// 解析的语义
/// 第一轮的语义信息, 用于二轮确认时，能获取到第一轮的具体语义信息
@property (nonatomic, strong) QMSemanticData *firstSemantic;
@property (nonatomic) BOOL disableDingDangWakeUp;
//是否是行中语音上报
@property (nonatomic) BOOL isInAudioReport;
// 播报文案，传递给AI对话框用于展示回复
@property (nonatomic) NSString *aiChatReply;
// 直接识别文字，用于禁用 TTS 播报
@property (nonatomic) BOOL isDirectText2Semantic;
// 正在流式输出
@property (nonatomic) BOOL isStreaming;

/// 提供额外参数等功能
@property (nonatomic, strong) id<QMVoiceAssistantProviderProtocol> provider;

QMDefaultManager_H(QMVoiceAssistantManager)

/// 启动入口
/// - Parameter provider: 提供额外参数等功能
+ (void)setupWithProvider:(id<QMVoiceAssistantProviderProtocol>)provider;

+ (void)performTaskWhenMapDidDrawOrTimeOut;

/**
 停止智能语音正在进行的所有流程（隐藏收音大面板、二轮小圆圈、停止正在进行的TTS播报等），同时开始离线唤醒
 */
- (BOOL)stop;
- (BOOL)stopWithAnimation:(BOOL)animation delegate:(id <QMVoiceAssistantPanelViewDelegate>)delegate;

/**
 停止智能语音正在进行的所有流程（隐藏收音大面板、二轮小圆圈、停止正在进行的TTS播报等），同时开始离线唤醒，并且停止录音
 */
- (void)stopRecord;

/**
 仅停止收音，实现禁用语音唤醒
 */
- (void)stopRecorderForWakeUp;

/**
 展示语音面板
 
 @param isReusablePanel 复用正在显示的面板
 */
- (BOOL)showRecommendedVoicePanelView:(BOOL)isReusablePanel;

/**
 显示播音状态的面板
 */
- (void)showSpeakingVoicePanelView:(NSString *)content;

/**
显示收音状态的面板
*/
- (void)showListeningVoicePanelView;

/**
 开启语音转文字
 */
- (BOOL)startVoice2Text;

/**
 关闭语音转文字
 */
- (BOOL)stopVoice2Text;

/**
 设置语义处理模块

 @param semantic 当前模块的语义处理对象
 */
- (void)setupSemantic:(QMBaseSemanticManager *)semantic;

/**
 移除语义处理模块，默认会打断当前叮当流程

 @param aDelegate 语义处理模块对应的 delegate
 */
- (void)removeSemanticWithDelegate:(id<QMSemanticDelegate>)aDelegate;

/**
 移除语义处理模块
 
 @param stop 是否打断当前叮当流程
 @param aDelegate 语义处理模块对应的 delegate
 */
- (void)removeSemanticWithDelegate:(id<QMSemanticDelegate>)aDelegate stop:(BOOL)stop;

/**
 设置二轮交互时支持的语义
 */
- (BOOL)updateSecondRoundSemantics:(QMSecondRoundInfo *)secondRoundInfo;

/*
 *  设置语音识别时静音超时时间
 */
- (BOOL)setupSILTimeOutValue:(NSInteger)value;


/// 设置语音结束静音时间
/// @param value 静音结束时长单位毫秒
- (void)updateVoiceRecognitionSpeechEndDuration:(NSInteger)value;

/// 获取语音结束静音时间
- (NSInteger)voiceRecognitionEndDuraton;

/**
 获取当前设置的语音识别超时时间

 @return 超时时间，单位是毫秒
 */
- (NSInteger)getSILTimeOutValue;

/**
 智能语音 SDK 提供的 GUID
  */
- (NSString *)getVoiceGUID;

/*
 *  智能语音固定入口点击响应
 */
- (void)onEntryButtonAction;

- (void)onEntryButtonActionWithComplete:(QMAuthorizationBlock)complete;

/*
 *  智能语音导航入口点击响应
 */
- (void)onNaviEntryButtonAction;


/// 驾车导航中语音上报问题
- (void)startAudioReportWithCallback:(QMAudioReportCallback)callback;
/// 驾车导航中语音上报问题，是否是非点事件
- (void)startAudioReportWithUnClickEvent:(BOOL)isUnclickEvent
                                callback:(QMAudioReportCallback)callback;
/*
 *  当前叮当是否可用的简单版
 */
- (BOOL)isAvailable;

/*
 *  是否是二轮收音
 */
- (BOOL)isSecondRound;

/**
 如果没有开启录音，则开启

 @param checkCondition 是否进行允许开启录音的条件检查
 */
- (void)startRecordWithCheckCondition:(BOOL)checkCondition;

/**
 二轮识别开始，目前用于埋点
 */
- (BOOL)secondRoundDidStart;

/**
显示正在播放音乐蓝条
*/
- (void)showOtherAudioPlayingTipsIfNeed;

/**
停止AISDK
*/
- (void)stopAISDK;

/**
 叮当当前状态
*/
- (QMVoiceAssistantAvailableState)availableState;

/**
 叮当基础埋点数据
 */
- (NSDictionary *)basicEventTrackingData;

/// AI 搜索相关
- (void)enterAISearchPage;
- (void)leaveAISearchPage;

/**
 语义识别
 */
- (int)text2semantic:(NSString *)data;

/**
 当前位于 AIHome 或者探索页
 */
- (BOOL)isAIChating;

/**
 停止智能语音正在进行的所有流程，不依赖浮窗面板关闭（Hippy AI 对话框使用）
 */
- (void)stopVoiceProcessDirectly;

@end
