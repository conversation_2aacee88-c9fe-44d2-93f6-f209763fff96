//
//  RouteSearchParameter.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 5/11/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMPointInfo.h"

#import <CoreLocation/CoreLocation.h>
#define LIGHT_KFENCE_RADIUS (50)

typedef enum BusSearchCondition_ {
    BUSSEARCH_COND_EXPRESS  = 0,
    BUSSEARCH_COND_LESS_TRANSFER,
    BUSSEARCH_COND_LESS_WALK,
    BUSSEARCH_COND_RECOMMEND,
    BUSSEARCH_COND_PREFER_BUS,
    BUSSEARCH_COND_PREFER_SUBWAY,
} BusSearchCondition;


typedef enum CarSearchCondition_ {
    CARSEARCH_COND_EXPRESS  = 0,
    CARSEARCH_COND_SHORT_DISTANCE = 2,
} CarSearchCondition;

typedef enum NavigationType_ {
    NAVIGATION_TYPE_NONE  = 0,
    NAVIGATION_TYPE_LIGHTNAVI,
    NAVIGATION_TYPE_NAVI
} NavigationType;

typedef enum CarRouteSearchSceneType_ {
   
    CarRouteSearch_Scene_Default  = 0,
    CarRouteSearch_Scene_MtPianhang,//多方案页偏航
    CarRouteSearch_Scene_MtPianhao,//多方案页修改偏好
    CarRouteSearch_Scene_MtBansui,//多方案页伴随
    CarRouteSearch_Scene_MtPark,//多方案页设置停车场为终点
    CarRouteSearch_Scene_MtHint,//多方案页点击主辅路
    CarRouteSearch_Scene_MtRefresh,//多方案页一键刷新
    CarRouteSearch_Scene_MtPass,//多方案页添加删除途径点
    CarRouteSearch_Scene_MtLPass,//多方案页长按添加途径点
    CarRouteSearch_Scene_Pianhang,//导航中偏航
    CarRouteSearch_Scene_Park,//导航中设置停车场为终点
    CarRouteSearch_Scene_Hint,//导航中点击主辅路
    CarRouteSearch_Scene_Pass,//导航中添加途径点
    CarRouteSearch_Scene_StartNotMatch,//起点与自车不在一起的吸附失败
    
}CarRouteSearchSceneType;

struct bound {
    CLLocationCoordinate2D minLonLat;
    CLLocationCoordinate2D maxLonLat;
};

@interface RouteSearchParameter : NSObject {

}
@property (nonatomic, strong) QMPointInfo *startPoint;

@property (nonatomic, strong) QMPointInfo *destPoint;

@property (nonatomic) NSUInteger numberOfPasspoints;
/** indexOfPasspoint表示正在添加的途径点的序号（已规划备选路线 还未确认设为途径） 为-1表示当前没有正在添加的途径点 */
@property (nonatomic) NSInteger indexOfPasspoint;

@property (nonatomic, strong) NSMutableArray *passPoints;
@property (nonatomic, strong) NSMutableArray *passTags;

@property (nonatomic) NSInteger cond;
@property (nonatomic) BOOL noExpress;
@property (nonatomic) BOOL noToll;
/**躲避拥堵 zodiacliu 2014-03-25*/
@property (nonatomic) BOOL avoidBusy;
/// 大路优先
@property (nonatomic, assign) BOOL bigRoad;
/// 时间短
@property (nonatomic, assign) BOOL shortTime;
@property (nonatomic) NSInteger naviType;  //jce算路判别导航模式，轻导航，导航等
@property (nonatomic) NSString* args;  //给偏航传递
@property (nonatomic,assign)int segHintType;//主/辅路，桥上/下 信息确认类型
@property (nonatomic,copy)NSString* specAngleForSegHint;//当用户选择在对面时,需要同时传入相反的方向

@property (nonatomic, assign)CLLocationCoordinate2D adjPoint;
@property (nonatomic, assign)double adjAngle;

//V6.3参数梳理:驾车路线规划请求场景, 用于在后台日志平台上跟踪问题
@property (nonatomic, assign)CarRouteSearchSceneType scene;

//偏航属性
@property (nonatomic, assign) NSInteger yawp;
@property (nonatomic, strong) NSString* routeId;
@property (nonatomic, strong) NSString* nowRouteId;

@property (nonatomic) struct bound bound;
@property (nonatomic, assign)int scale;

+ (int)busCondition:(int)cond;
+ (int)cond:(int)busCondition;

- (BOOL)isStartPointAvailable;

- (BOOL)isDestPointAvailable;

- (void)adjustSearchString;

- (void)setPassPoint:(QMPointInfo *)point withTag:(int)tag atIndex:(NSUInteger)index;
- (void)addPassPoint:(QMPointInfo *)point;
- (void)addPassPoint:(QMPointInfo *)point withTag:(int)tag;
- (void)removePassPointAtIndex:(NSUInteger)index;
- (void)modifyPassPoints:(NSArray<NSDictionary *> *)serverPass;

- (void)clearPassPoints;

- (void)clearParameter;
@end
