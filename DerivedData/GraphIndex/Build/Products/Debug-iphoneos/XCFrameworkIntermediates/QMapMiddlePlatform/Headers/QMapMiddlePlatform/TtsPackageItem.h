//
//  TtsPackageItem.h
//  SOSOMap
//
//  Created by we<PERSON><PERSON><PERSON> on 2017-04-10.
//  Copyright (c) 2017 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMDownloadData.h"
#import "QMVoiceSquareProtocol.h"
@class TtsPackageItem;
typedef void(^QMTtsPackageItemSuccessBlock)(TtsPackageItem*item);
typedef void(^QMTtsPackageItemFailedBlock)(TtsPackageItem*item,NSError*error,int startTaskErrorType);

typedef void(^TPB_refreshUI)(TtsPackageItem*item);
typedef void(^EMp3_playFailed)(BOOL failed);

typedef void(^QMTtsDownloadFinishBlk) (NSInteger voiceId, BOOL suc);

typedef NS_ENUM(NSInteger, EMp3PlayStatus){
    EMp3PlayStatus_None,      //未播放,播放完毕
    EMp3PlayStatus_loading,   //下载中
    EMp3PlayStatus_Playing,   //播放中
};

@class QMTtsPackageBusiHandler;

@interface TtsPackageItem : NSObject<QMDownloadData>
/**
 原始数据
 */
@property(strong) QMVoiceSquareLanguagePackageData * dataItem;
/**
 唯一ID
 */
@property (nonatomic) long long ID;
/****************
 *下载包名称，中文
 ****************/
@property (nonatomic, copy) NSString*   name;
/****************
 *下载包拼音
 ****************/
@property (nonatomic, copy) NSString*   spelling;
/****************
 *下载包图标iconURL
 ****************/
@property (nonatomic, copy) NSString*   iconURL;

/// 是否是静默更新
@property (nonatomic, assign) BOOL isSilenceUpdate;

/// 西游记IP系列：切换语音，阿波罗下载的本地语音包地址
@property (nonatomic, copy) NSString*  voicePath;

/****************
 *下载包拼音,没有后缀
 ****************/
- (NSString *)packageName;

/**
 判断item是否符合spell
 */
- (BOOL)matchSpell:(NSString *)spell;

/****************
 *下载包版本号
 ****************/
@property (nonatomic, assign) NSInteger version;
/****************
 *下载包当前状态，比如暂停，下载中，完成等
 CityData_None = 0,                      // 未下载
 CityData_Downloading = 2,               // 正在下载
 CityData_Pause = 3,                     // 暂停
 CityData_OK = 4,                        // 下载完成
 CityData_Waiting=14                     // 等待中，队列中，
 ****************/
@property (nonatomic) CityDataState curState;
/**
 是否需要更新
 */
@property (nonatomic,readonly) BOOL isNeedUpdateData;
/**
 是否正在使用
 */
@property (nonatomic) BOOL isDataUsing;
/**
 下载包总大小
 */
@property (nonatomic) NSInteger totalSize;
/**
 已下载大小
 */
@property (nonatomic) NSInteger downloadedSize;

/****************
 *完整的URL，语音包使用
 ****************/
@property (nonatomic, strong,readonly) NSString* totalUrl;


//not used
/****************
 *下载包数据类型，比如时离线地图包，还是离线导航包
 ****************/
@property (nonatomic, assign) NSInteger dataType;
/**
 0隐藏 1下载 2展示 3重试 4成功
 */
@property (nonatomic, assign) NSInteger isShowThemeTipsView;

+ (instancetype)itemWithVoiceItem:(QMVoiceSquareLanguagePackageData*)item;
- (BOOL)isEqualToDataItem:(QMVoiceSquareLanguagePackageData*)object;

- (void)updateStatusFromOther:(TtsPackageItem*)other;
- (void)updateStatus;

/**
 升级,更新
 */
- (void)upgrade;

/**
 删除，清理
 */
- (void)clear;

/**
 mp3的播放状态
 */
@property (nonatomic, assign) EMp3PlayStatus eMp3PlayStatus;

/**
 本次下载是否是更新下载
 */
@property (nonatomic) BOOL isUpdating;

@property (nonatomic, strong) QMTtsPackageBusiHandler *handler;

/**
 下载时间
 */
@property (nonatomic) NSTimeInterval downloadTime;
/**
 下载业务方
 */
@property (nonatomic) NSString *downloadSource;
/**
 下载结果回调
 */
@property (nonatomic, copy) QMTtsDownloadFinishBlk finishBlk;
 
 /*
 语音包是否已经过期下线
 */
@property (nonatomic, assign) BOOL isOffline;

@end


