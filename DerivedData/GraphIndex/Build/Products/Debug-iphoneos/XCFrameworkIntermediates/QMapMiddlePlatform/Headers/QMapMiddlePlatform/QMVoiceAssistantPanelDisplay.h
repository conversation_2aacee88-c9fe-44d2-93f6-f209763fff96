//
//  QMVoiceAssistantPanelDisplay.h
//  SOSOMap
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/12/23.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMVoiceAssistantPanelDisplay : NSObject

/// 返回省略符号在头部的字符串
/// @param fullString 完整的字符串
/// @param referWidth 每行文字显示的宽度
/// @param font 计算时的字体
+ (NSString *)textWithLineBreakByTruncatingHeadForMaxTwoLines:(NSString *)fullString referWidth:(CGFloat)referWidth font:(UIFont *)font;

+ (NSString *)textWithLineBreakByTruncatingHeadForAIPanel:(NSString *)fullString
                                                 maxWidth:(CGFloat)maxWidth
                                                     font:(UIFont *)font;
@end

NS_ASSUME_NONNULL_END
