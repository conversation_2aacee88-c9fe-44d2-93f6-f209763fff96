//
//  QMapRouteSearchExtend.h
//  QMapNaviKit
//
//  Created by sarah on 2018/2/26.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QMapRouteSearchKit.h>
 
#import <QMapProto/QMJCE_routeguidance_RouteGuidanceGPSPoint.h>
#import <QMapProto/QMJCE_routeguidance_RouteGuidanceMapPoint.h>
#import <QMapProto/QMJCE_routesearch_RouteCloudControl.h>
#import <DragonMapKit/DragonMapKit.h>
#import "RouteTrafficInfo.h"
#import "RemainTimeCalculator.h"
#import "RoutePoint.h"

@interface QMapRouteSearchExtend : NSObject

+ (QRouteCoordinate *)getRouteCoor:(CLLocationCoordinate2D)coordinate;
+ (CLLocationCoordinate2D)getLocationCoor:(QRouteCoordinate *)coordinate;

+ (DMMapRect)dm_getBounds:(NSArray<RoutePoint *> *) points;

+ (NSArray<DMRoutePoint *> *)generateDMRoutePointFromQRouteCoordinate:(NSArray<QRouteCoordinate *> *)coors;

@end

@interface QRoutePlaceInfo (NaviKit)
- (NSString *)title;
- (DMMapPoint)mapPoint;
- (CLLocationCoordinate2D)coordinate;
- (void)setCoordinate:(CLLocationCoordinate2D)coordinate;

/// 两个数据是否一致，只比较经纬度，离线情况下UID可能都为local
/// @param placeInfo 比较的对象
- (BOOL)isEqualToPlaceInfo:(QRoutePlaceInfo *)placeInfo;

@end


@interface QDriveRouteReqParam (NaviKit)

- (BOOL)hasPassPOIs;
- (void)modifyPassPoints:(NSArray<QRoutePlaceInfo *> *)serverPass;
@end

@interface QRouteTraffic (NaviKit)

//堵点中  实时状态有三种0为畅通,1为缓行,2为拥堵. 现在加一个第四种3为猪肝红,trafficType为后台返回的原始路况值
+ (int)convertJamTypeToColorType:(int )trafficType;

@end

@interface QRouteStep (NaviKit)
@property (nonatomic, copy) NSString *roadName;
@property (nonatomic, strong) NSArray *roadNames;//dynRoadNames
@property (nonatomic, copy) NSString *textInfo;//textInfo
@property (nonatomic) int coorStart;//coorStart
@property (nonatomic, assign) int formId;//道路形状
@property (nonatomic) int funcclass;//道路FC
@property (nonatomic) int gradeId;//道路行政等级

@end

@interface QRouteStepForDrive (NaviKit)

@end

@interface QRouteRoundaboutInfo (NaviKit)
- (NSUInteger)getRightExitIndex;
@end

@interface QRouteForDrive (NaviKit)

@property (nonatomic, copy, readonly) NSString *routeID;
@property (nonatomic)BOOL isLocalRoute;
@property (nonatomic, strong) NSArray<RouteTrafficInfo *> *routeTrafficArray;

@property (nonatomic) NSInteger remainingDistance;//单位：米,默认等于总距离
@property (nonatomic) NSInteger remainingTimeSecond;//单位：秒，默认等于总时间

@property (nonatomic) int saveTime;
@property (nonatomic) int forkIndex;
@property (nonatomic) double forkX;
@property (nonatomic) double forkY;
@property (nonatomic, strong) NSString *dynReason;
@property (nonatomic, strong) NSString *reasonVoiceText;//后台下发的播报文案
@property (nonatomic, strong) NSString *reasonDisplayText;//后台下发的显示文案
@property (nonatomic, assign) int timeDiff;//新旧路线的时间差，单位分钟

@property (nonatomic, assign) int jam_len_meters;   //拥堵距离，诱导在主路拥堵气泡展示/隐藏回调中返回，客户端透传给导航后台
@property (nonatomic, assign) int jam_time_seconds;     //拥堵时长，诱导在主路拥堵气泡展示/隐藏回调中返回，客户端透传给导航后台

@property(nonatomic, assign) NSInteger currentMatchIndex;

//默认是0
@property(nonatomic, assign) NSInteger curPassedDistance;
// 上次路况刷新成功的时间戳，供下次刷新路况用
@property (nonatomic, assign) NSTimeInterval lastTrafficSuccessTime;

@property (nonatomic, assign) int segmentIndexOnRequest;//请求剩余时间时所在的segmentindex

@property(nonatomic, strong) QMJCE_routeguidance_RouteGuidanceMapPoint *curEventPoint;
@property(nonatomic, strong) QMJCE_routeguidance_RouteGuidanceMapPoint *preEventPoint;
@property(nonatomic, assign) int curEventPointIndex;
@property(nonatomic, assign) int preEventPointIndex;

//默认是0，最近一次吸附成功的点索引
@property(nonatomic, assign) int latestMatchPointIndex;
//默认是0
@property(nonatomic, assign) int lastestMatchSegmentIndex;

@property(nonatomic, strong) CLLocation *lastestMatchLocation;



- (NSArray<RoutePoint *> *)points;
- (NSInteger)remainingTime;
- (DMMapRect)dm_frame;
- (DMMapRect)clientFrame;
- (NSArray<QMJCE_routeguidance_RouteGuidanceMapPoint *> *)mapPoints;
- (int)mapPointsCount;
- (int)eventPointsCount;
- (BOOL)hasImageInfoAtIndex:(NSInteger)index;
- (NSArray<QRouteTipInfo *> *)totalCameraURL12s;
- (NSArray<QRouteCityInfo *> *)totalCityBorders;
- (NSArray<NSArray<QRouteBRInfo *> *> *)trafficImageArray;
- (NSArray<QRoutePlaceInfo *> *)totalTrafficLights;
- (NSInteger)totalTrafficLightsCount;

// 算路结果回来后，用路况信息更新ETA类
//- (void)updateTimeCalculatorWithTrafficIfNeccessary;

- (BOOL)isLongRoute;

- (NSArray<QRouteSpecialRoad *> *)innerRoads;
- (NSArray<QRouteSpecialRoad *> *)validSpecialRoads;

@end


@interface QRouteResultForDrive (NaviKit)
@property (nonatomic) NSInteger selectedRouteIndex;
@property (nonatomic) BOOL isLocalResult;

//大定位重构后加入的相关参数
@property (nonatomic, copy) NSArray<NSString *> *requestRouteIds;
- (BOOL)isValidRouteResult;
- (BOOL)hasRoutes;
- (QRouteForDrive *)selectedRoute;
- (BOOL)isBoundsResult;
- (BOOL)suggestUseOldRoute;
- (NSArray *)unselectedRouteids;
- (QRouteForDrive *)getCarRouteFromid:(NSString *)routeid;
- (DMMapRect)unionFrame;

// 算路结果回来后，用路况信息更新ETA类
//- (void)updateRoutesTimeCalculatorWithTrafficIfNeccessary;

@end


