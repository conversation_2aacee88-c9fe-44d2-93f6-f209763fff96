//
//  TMMapRichTextInfo.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/8.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import "DMGroupMarkerInfo+hippy.h"

NS_ASSUME_NONNULL_BEGIN

@interface TMMapRichTextSegment : NSObject

/**
 对应当前的数据对象为字符串或图片，当segmentType为0时，表示该段落展示的是文字信息，为1时则该段落为图片资源
 */
@property (nonatomic, assign) TMMapRichTextSegmentType segmentType;

/**
 文本信息，当segmenType值为TMMapRichTextSegmentTypeText时，才会读取相关文本信息
 */
@property (nonatomic, copy) NSString *text;

/**
 文本信息对应的色值，默认色值为#666666
 */
@property (nonatomic, copy) UIColor *color;

/**
 文本信息对应的字体，默认为字体12的系统字体
 */
@property (nonatomic, copy) UIFont *font;

/**
 文本阴影颜色
 */
@property (nonatomic) UIColor *shadowColor;

/**
 文本阴影半径
 */
@property (nonatomic) CGFloat shadowRadius;

/**
 图片信息，当segmentType值为TMMapRichTextSegmentTypeImage时，才会读取相关图片的信息
 */
@property (nonatomic, copy) UIImage *image;

/**
 图片设置大小信息，默认图片大小是12 * 12
 */
@property (nonatomic) CGRect imageBounds;

/**
 图标大小
 */
@property (nonatomic) CGSize iconBound;

/**
 图片路径
 */
@property (nonatomic, copy) NSString *iconPath;

/**
 图片相对文本的位置
 */
@property (nonatomic,copy) NSString *iconPosition;


@end


@interface TMMapRichTextInfo : NSObject

/**
 展示文字是否支持折行，YES表示支持折行，NO表示不支持折行，默认为不支持折行
 */
@property (nonatomic, assign) BOOL lineBreak;

/**
 视图展示的边距，如margin.left/margin.right表示相对superView的左右边距，margin.top/margin.bottom表示相对superView或相邻richTextInfo对象的上下边距
 */
@property (nonatomic) UIEdgeInsets margin;

/**
 前端可设置文本信息的最大展示区域，如果不设置则默认最大区域为（200，20）
 */
@property (nonatomic) CGSize maxSize;

/**
 文本对应的拼接数组
 */
@property (nonatomic, copy) NSArray<TMMapRichTextSegment *> *segments;


@end

@interface TMMapRichBackgroundInfo : NSObject

/**
 根据传入生成的图片
 */
@property (nonatomic) UIImage *image;

/**
 背景颜色，默认 clear
 */
@property (nonatomic) UIColor *color;

/**
 图片拉伸区域， 默认不拉伸
 */
@property (nonatomic) UIEdgeInsets stretchPadding;


/**
 视图展示的边距，如margin.left/margin.right表示相对superView的左右边距，margin.top/margin.bottom表示相对superView或相邻richTextInfo对象的上下边距
 */
@property (nonatomic) UIEdgeInsets margin;

@end

NS_ASSUME_NONNULL_END
