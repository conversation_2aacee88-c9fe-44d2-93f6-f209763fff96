//
//  RouteMarkerUtil.h
//  SOSOMap
//
//  Created by sarah on 2017/4/6.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#ifndef RouteMarkerUtil_h
#define RouteMarkerUtil_h

// 前5个宏不能去掉Navi.bundle，否则QMBusTransStationOverlay.m中pinAnnotationView.imageName无法取到图
#define PNG_START_POINT @"Navi.bundle/ic_start_point.png"
#define PNG_END_POINT   @"Navi.bundle/ic_end_point.png"
#define PNG_REVERSE_POINT @"Navi.bundle/ic_end_marker.png"
#define PNG_START_MARK_POINT @"Navi.bundle/ic_start_marker.png"
#define PNG_SUBWAY_OUT_MARK_POINT @"Navi.bundle/ic_subway_entrance_and_exit.png"
#define PNG_SEGMENT_BUS_POINT      @"busalert_bus"
#define PNG_SEGMENT_SUBWAY_POINT   @"busalert_subway"
#define PNG_SEGMENT_WALK_POINT     @"walk_in_map"
#define PNG_SEGMENT_WALK_TRANSFER_POINT  @"busalert_transfer"
#define PNG_SEGMENT_BRIDGE_POINT         @"walk_bridge_in_map"
#define PNG_SEGMENT_PASSGATEWAY_POINT    @"walk_underground_in_map"
#define SUBWAY_OUT_MARK_POINT_TITLE @"SUBWAY_OUT_MARK_POINT_TITLE"

#define END_MARKER_HEIGHT  (46)
#define END_POINT_HEIGHT  (20)

#endif /* RouteMarkerUtil_h */
