//
//  QMViewController+Dynamic3DAPI.h
//  QMapMiddlePlatform
//
//  Created by 狄弘辉 on 2023/12/1.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import "QMViewController.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMViewController (Dynamic3DAPI)

- (void)handleDynamic3DApiWhenViewDidAppear;
- (void)handleDynamic3DApiWhenViewWillDisappear;

/** 
 业务VC场景可能会根据具体业务条件决定3D图模块是否需要恢复
 @note 具体的子类VC根据实际需要实现这个方法
 @return 条件字典 k-v: QMSharedMapAPIFuncName->enable
         eg: 打卡模块在当前业务场景不想被框架恢复
             return {
                        QMSharedMapAPIFuncNameCheckIn: @(NO)
                    {
 */
- (NSDictionary *)dynamic3DApiRecoverConditions;

/**
 业务VC场景可能会根据具体业务条件决定3D图模块是否需要清除
 @note 具体的子类VC根据实际需要实现这个方法
 @return 条件字典 k-v: QMSharedMapAPIFuncName->enable
         eg: 打卡模块在当前业务场景不想被框架清除
             return {
                        QMSharedMapAPIFuncNameCheckIn: @(NO)
                    {
 */
- (NSDictionary *)dynamic3DApiResetConditions;

@end

NS_ASSUME_NONNULL_END
