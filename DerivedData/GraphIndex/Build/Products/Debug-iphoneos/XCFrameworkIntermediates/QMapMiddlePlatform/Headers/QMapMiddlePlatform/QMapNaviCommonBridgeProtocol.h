//
//  QMapNaviCommonBridgeProtocol.h
//  QMapMiddlePlatform
//
//  Created by admin on 2021/1/17.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/QMapAppBridgeProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@protocol QMapNaviCommonBridgeProtocol<QMapAppBridgeProtocol>

- (void)stringGetUTF16String:(NSString *)str outText:(unsigned short *)text len:(int)len;

- (NSArray<CLLocation *> *)loadGPSPointsFromFile:(NSString *)filePath
                       needRemoveUnmatchedPoints:(BOOL)needRemoveUnmatchedPoints
                          needRemoveFlyingPoints:(BOOL)needRemoveFlyingPoints;

@optional

- (NSArray<CLLocation *> *)loadGPSPointsFromFile:(NSString *)filePath
                       needRemoveUnmatchedPoints:(BOOL)needRemoveUnmatchedPoints
                          needRemoveFlyingPoints:(BOOL)needRemoveFlyingPoints
                                   reverseRecord:(BOOL)reverseRecord;

@end

NS_ASSUME_NONNULL_END
