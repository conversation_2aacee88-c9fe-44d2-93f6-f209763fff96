//
//  QMVoiceAssistantManagerProtocol.h
//  QMapMiddlePlatform
//
//  Created by admin on 2020/11/9.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMSemanticDelegate.h>
#import <QMapMiddlePlatform/QMapAppBridgeProtocol.h>

// 引导页之后的，用户出行偏好
#define QM_NOT_SUPPORT_PAGE_USER_TRAVEL_PERFER @"introduce"

@protocol QMVoiceAssistantManagerProtocol <QMapAppBridgeProtocol>
@required
- (void)setupSemantic:(id)semantic;
- (BOOL)stop;
/**
 移除语义处理模块，默认会打断当前叮当流程

 @param aDelegate 语义处理模块对应的 delegate
 */
- (void)removeSemanticWithDelegate:(id<QMSemanticDelegate>)aDelegate;

- (id)createNotSupportSemanticManagerWithDelegate:(id<QMSemanticDelegate>)aDelegate fromSource:(NSString *)fromSource;

- (UIView *)createVoiceKeyboardEntryView;

@end
