//
//  QMWebBrowser.h
//  SOSOMap
//
//  Created by <PERSON> on 12-2-23.
//  Copyright 2012年 Tencent. All rights reserved.
//

#import "QMLoadingView.h"
#import "QMWebLoadingView.h"
#import "QMNavigationBar.h"
#import "QMShareViewControllerProtocol.h"
#import "QMWebViewDefine.h"
#import <QMapMiddlePlatform/QMViewController.h>
#import <UIKit/UIKit.h>
#import <TMWeAppClient/TM_IWCBaseWebViewControllerProtocol.h>

@class QMWebBrowser;
@protocol QMWebBrowserDelegate<NSObject>

- (void)didClickLeftButton:(QMWebBrowser *)browser;

@end

@interface QMWebBrowser : QMViewController<QMWebViewDelegate, IWCBaseWebViewControllerProtocol> {
    QMWebView *webView_;
    UIButton *goBackB<PERSON>on_;
    UIButton *goForwardButton_;
    UIButton *refreshButton_;
    UIButton *stopButton_;
    UIActivityIndicatorView *waitingView_;

    NSString *urlString_;
    NSString *leftButtonText_;
    NSString *preLoadingTitle_;
    BOOL isBackStyleForLeftButton_;

    id<QMWebBrowserDelegate> __weak delegate_;
}

@property (nonatomic, strong) QMWebView *webView;
@property (nonatomic, copy) NSString *urlString;
@property (nonatomic, copy) NSString *leftButtonText;
@property (nonatomic, copy) NSString *preLoadingTitle;
@property (nonatomic, assign) BOOL isBackStyleForLeftButton;
@property (nonatomic, weak) id<QMWebBrowserDelegate> delegate;
@property (nonatomic, assign) BOOL useNavigationBarAsWebViewControl;
@property (nonatomic, assign) BOOL alwaysGoBackPage;

@property (nonatomic, strong) NSDictionary *requestHeaderFields;

// string property for sharing
@property (copy, nonatomic) NSString *shareDesc;
@property (copy, nonatomic) NSString *shareTitle;
@property (copy, nonatomic) NSString *shareURL;
@property (strong, nonatomic) UIImage *shareImage;
@property (nonatomic, copy) NSString *designedTitle; //自定义的webview title
@property (nonatomic, assign) BOOL isUseCustomNavigationBar; //是否使用自定义的qmnavigationbar
@property (nonatomic, assign) QMNavigationBarStyle barStyle; //设置NavigationBar 的style
@property (nonatomic, strong) QMNavigationBar *navigationBar;
@property (nonatomic, readonly) QMWebLoadingView *loadingView;
@property (nonatomic, assign) QMShareViewControllerInvolkFrom modeType;

@property (nonatomic, assign) BOOL forceUseH5NavBar;
@property (nonatomic) BOOL disableVoiceAssistant; // 是否禁用智能语音
@property (nonatomic, copy) NSString *fromSource;
@property (nonatomic) BOOL disableScroll; // 是否禁止webview的滑动事件
- (void)handleBackButton:(id)sender;

- (NSString *)makeUrl:(NSString *)szUrl;

- (NSString *)makeDefaultParam:(NSString *)szUrl;

- (void)removeWaitingView;

- (void)loadURL;

- (void)reloadIfNeed;

- (void)share;

- (NSString *)defaultTitle;
@end
