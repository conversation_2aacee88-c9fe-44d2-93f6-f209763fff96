//
//  QMTipExplainViewModelProtocol.h
//  SOSOMap
//
//  Created by el<PERSON> on 2020/7/14.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol QMTipExplainViewModelProtocol <NSObject>

///  删除tips
- (void)removeTips;


/// 删除ttips
- (void)showMultTips;

@optional
/// 隐藏消息盒子
- (void)hideMultTips;

- (BOOL)isMsgBoxShow;

- (void)closeCarTipDetailPage;

- (void)removeCarTipsView;

- (void)updateLimitRules:(NSArray *)rules;

- (BOOL)isTipMarkerId:(NSString *)markerId;

- (void)updateTipsView;

- (void)forceRemoveCarTipsView;


@end

NS_ASSUME_NONNULL_END
