//
//  RouteTrafficInfo.h
//  QMapKit
//
//  Created by <PERSON><PERSON><PERSON> on 29/11/2016.
//  Copyright © 2016 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>
#import <QMapFoundation/QMModel.h>
#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, RouteTrafficEventType) {
    RouteTrafficEventTypeSlow = 0,//缓行
    RouteTrafficEventTypeJam = 1,//堵车
    RouteTrafficEventTypeOther = 11,//其他
    RouteTrafficEventTypeUnCrowded = 12,//道路畅通
    RouteTrafficEventType_JAM2 = 201,     //猪肝红
    RouteTrafficEventTypePassed = 10000,    //已经经过的距离
    RouteTrafficEventTypeMaxType//
};

@interface RouteTrafficInfo : QMModel
@property (nonatomic, assign) int eventID;  	//该条事件ID
@property (copy) NSString* roadName;  //路名
@property (assign) int informType;  //播报类型
@property (nonatomic, assign) int startIndex;//路况的起点索引
@property (nonatomic, assign) int endIndex;//路况的终点索引
@property (nonatomic, assign) int color;//颜色
@property (nonatomic, assign) int speed;//速度
@property (nonatomic, assign) int eventType;//事件类型
@property (nonatomic, assign) CLLocationCoordinate2D startCoordinate;//路况起点坐标
@property (nonatomic, assign) CLLocationCoordinate2D endCoordinate;//路况终点坐标
@property (nonatomic, assign) int shapeType;//形状
@property (copy) NSString* desc;  	//事件描述信息
@property (nonatomic, assign) double length;    //路况的长度，单位米

+ (int)translateTrafficType:(int)eventType;
+ (int)translateEventTypeToQRouteTraffic:(int)eventType;
+ (BOOL)isValidTraffic:(NSArray<RouteTrafficInfo *> *)trafficArray maxPointIndex:(NSInteger)index;

/**
 使用当前的交通类型码获取对应的色值

 @param eventType 路况类型码
 @return 返回一个非空的UIColor对象，如果数据不合法，返回clearColor
 */
+ (UIColor *)colorWithEventType:(RouteTrafficEventType)eventType;
+ (UIColor *)colorForIsNeedForSpeedWithEventType:(RouteTrafficEventType)eventType isNightStyle:(BOOL)isNightStyle;

@end
