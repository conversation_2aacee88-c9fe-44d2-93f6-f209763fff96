//
//  QMapIconFontTool.h
//  SOSOMap
//
//  Created by admin on 2020/4/27.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString * const QMapIconFontTravel;
extern NSString * const QMapIconFontDownArrow;
extern NSString * const QMapIconFontItemArrow;
extern NSString * const QMapIconFontRoutesArrow;
extern NSString * const QMapIconFontRightArrow;
extern NSString * const QMapIconFontScreenshot;
extern NSString * const QMapIconFontReminder;
extern NSString * const QMapIconFontReminderClose;
extern NSString * const QMapIconFontGTrian;
extern NSString * const QMapIconFontFeedback;
extern NSString * const QMapIconFontBusIcon;
extern NSString * const QMapIconFontBusCode;
extern NSString * const QMapIconFontSubway;
extern NSString * const QMapIconFontWalk;
extern NSString * const QMapIconFontUpArrow;
extern NSString * const QMapIconFontBusNFC;
extern NSString * const QMapIconFontCenterShot;
extern NSString * const QMapIconFontRouteChoice;
extern NSString * const QMapIconFontBusRouteVerticalLine;

@interface QMapIconFontToolParameter : NSObject

@property (nonatomic, copy) NSString *code;

@property (nonatomic, assign) CGFloat fontSize;

@property (nonatomic, copy) NSString *fontName;

@property (nonatomic, strong) UIColor *color;

@property (nonatomic, assign) CGSize iconSize;

@property (nonatomic, assign) CGFloat leftMargin;

@property (nonatomic, assign) CGFloat rightMargin;

@end

@interface QMapIconFontTool : NSObject

+ (QMapIconFontTool *)sharedInstance; 

- (UIFont *)iconFontWithSize:(CGFloat)fontSize;

- (UIImage *)iconImageWithCode:(NSString *)code
                     iconColor:(UIColor *)color
                      iconSize:(CGFloat)iconSize
                    iconMargin:(CGFloat)margin;

- (NSAttributedString *)iconAttWithCode:(NSString *)code
                              iconColor:(UIColor *)color
                               iconSize:(CGFloat)iconSize;

- (UIImage *)iconImageWithCode:(NSString *)code
                     iconColor:(UIColor *)color
                      iconSize:(CGFloat)iconSize
                iconLeftMargin:(CGFloat)leftMargin
               iconRightMargin:(CGFloat)rightMargin;

- (UIImage *)iconImageWithParamter:(QMapIconFontToolParameter *)param;

@end

NS_ASSUME_NONNULL_END
