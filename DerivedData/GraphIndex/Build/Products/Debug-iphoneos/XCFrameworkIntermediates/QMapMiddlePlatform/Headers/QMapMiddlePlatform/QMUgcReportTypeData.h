//
//  QMUgcReportTypeData.h
//  SOSOMap
//
//  Created by leon<PERSON><PERSON> on 2018/1/18.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>


typedef NS_ENUM(NSInteger, QMUgcReportType) {
    QMUgcReportTypeUseless = -1,
    
    QMUgcReportTypeTrafficJam = 2,      // 拥堵
    QMUgcReportTypeRoadClose,           // 封路
    QMUgcReportTypeRoadOpen,            // 老版本的开通，新版本的障碍物
    QMUgcReportTypeConstruction,        // 施工
    QMUgcReportTypeAccident,            // 事故
    QMUgcReportTypeControl,             // 管制
    
    QMUgcReportTypeCheck = 9,         //检查
    QMUgcReportTypeNormalAccident,    //一般事故
    QMUgcReportTypePoiAdd = 11,         // 新增地点
    QMUgcReportTypePoiCorrect,          // 地点纠错
    QMUgcReportTypeAnnouncement,        // 公告
    QMUgcReportTypeActivity,            // 活动
    QMUgcReportTypeBadWeather,          // 恶劣天气
    QMUgcReportTypeStationProblem,      // 车站相关问题
    
    QMUgcReportTypeOldRoadClose = 108,  // 封路
    QMUgcReportTypeTrafficCone,         //障碍物
    QMUgcReportTypePonding,             //积水
    QMUgcReportTypeFog,                 //雾
    QMUgcReportTypeSnow,                //雪
    QMUgcReportTypeDisaster,            //灾
    QMUgcReportTypeGame,                //比赛
    QMUgcReportTypeWater = 211,         //积水
    
    QMUgcReportTypeOther = 999,         // 其他
    
    QMUgcReportTypeNavBlockRoad = 6010,    //驾车导航-道路不通
    QMUgcReportTypeNavElectronicEye,       //驾车导航-电子眼错误
    QMUgcReportTypeNavBroadcast,           //驾车导航-播报错误
    QMUgcReportTypeNavRoute,               //驾车导航-路线不优
    QMUgcReportTypeNavTurnForbid,          //驾车导航-禁止转向
    QMUgcReportTypeNavCarLogoStutter,      //驾车导航-车标卡住
    QMUgcReportTypeNavTrafficLight,        //驾车导航-红绿灯倒计时
    QMUgcReportTypeNavLane,                //驾车导航-车道级问题
    QMUgcReportTypeNavOther,               //驾车导航-其他

    QMUgcReportTypeNavAudio = 6600,        //驾车导航-语音上报
};
typedef NS_ENUM(NSUInteger, QMClickReportType) {
    QMClickReportTypeAccident = 1,            // 事故
    QMClickReportTypeControl,                 // 管制
    QMClickReportTypeConstruction,            // 施工
    QMClickReportTypeObstacle,               // 障碍
    QMClickReportTypeActivity,               // 活动
    QMClickReportTypeBadWeather,             // 恶劣天气
    QMClickReportTypeDisaster,               //灾
    QMClickReportTypeTrafficJam ,            // 拥堵
    QMClickReportTypeCheck,                  //检查
    QMClickReportTypeNormalAccident,        //一般事故
    QMClickReportTypeWater,                 //积水
    QMClickReportTypeRoadClose,              // 封路
    QMClickReportTypeAnnouncement,          // 公告

};

typedef NS_ENUM(NSInteger, QMUgcReportFromSource) {
    QMUgcReportFromSourceMainPage = 1,           //首页上报中心
    QMUgcReportFromSourceInNav = 2,              //导航中上报
    QMUgcReportFromSourceSearchNoResult = 3,     //搜索无结果
    QMUgcReportFromSourceSearchList = 4,         //搜索列表
    QMUgcReportFromSourceSearchMultiCity = 5,    //搜索多城市列表
    QMUgcReportFromSourcePoiDetail = 6,          //POI详情顶部
    QMUgcReportFromSourceLongPressPoint = 7,     //长按选点
    QMUgcReportFromSourcePoiCorrect = 8,         //poi点H5内部纠错
    QMUgcReportFromSourcePersonalCenter = 9,     //个人中心-意见反馈
    QMUgcReportFromSourcePersonalCenterMyReport, //个人中心-我的上报
    QMUgcReportFromSourceDingdang,               //叮当
    QMUgcReportFromSourceCarVio,                 //违章
    QMUgcReportFromSourceCompanyBus,             //班车入口
    QMUgcReportFromSourceOpenAPI,                //端外呼起
    QMUgcReportFromSourceBusQRSDK,               //乘车码SDK-意见反馈
    
    // 新增
    QMUgcReportFromSourceBusRoute,               //公交算路
    QMUgcOtherReportFromSourceBusRoute,           //公交算路轻反馈
    QMUgcReportFromSourceRealtimeBus,            //实时公交
    QMUgcReportFromSourceARWalk,                 //AR步导-意见反馈
    QMUgcReportFromSourceSummary,                 //小结页跳转截屏反馈上报
    QMUgcReportFromSourceBusOrSubway,                 //地铁和公交
    QMUgcReportFromSourceWalkCycle,                 //步骑行
    QMUgcReportFromSourceBusDetail,               //公交详情非算路页面进入
    QMUgcReportFromSourceStationProlem,           //公交&地铁详情页反馈上报
    QMUgcReportFromSourceNavigation,              //行中上报
    QMUgcReportFromSourceNavigationOther          //行中其他问题上报
};

typedef NS_ENUM(NSInteger, QMUgcReportEntry) {
    QMUgcReportEntryBusOrSubway = 6,     //公交和地铁
    QMUgcReportEntryNoResult = 10,   //搜索无结果新增
    QMUgcReportEntryPOIDetail = 11,  //poi详情H5内部
    QMUgcReportEntryFeedbackHike = 15,  //步行方案页
    QMUgcReportEntryHikeComplete = 16,  //步行小结页
    QMUgcReportEntryFeedbackCycling = 17, // 骑行方案页
    QMUgcReportEntryCyclingComplete = 18, //骑行小结页
    QMUgcReportEntryDingdang = 25,   //叮当
    QMUgcReportEntryCarVio = 27,     //违章
    QMUgcReportEntryCompanyBus = 28, //班车
    QMUgcReportEntryPOITop = 31,     //poi顶部
    QMUgcReportEntryBusQRSDK = 32,      //乘车码SDK-意见反馈
    // 新增
    QMUgcReportEntryBusRoute = 37,               //公交算路
    QMUgcReportEntryRealtimeBus = 38,            //实时公交
    QMUgcReportEntryARWalk = 39,                 //AR步行导航反馈
    QMUgcReportEntryHikeNav = 50,                     //步行导航中
    QMUgcReportEntryCyclingNav = 51,                    //骑行导航中
};
typedef NS_ENUM(NSUInteger, QMUgcNavType) {
    QMUgcNavTypeCar = 1,
    QMUgcNavTypeWalk,
    QMUgcNavTypeCycle,
    QMUgcNavTypeFollow,
    QMUgcNavTypeRouteRecord
};

typedef NS_ENUM(NSUInteger, QMUgcReportWalkType) {
    QMUgcReportWalkTypeNone = 0,
    QMUgcReportWalkTypeBroadcast,
    QMUgcReportWalkTypeRoad,
    QMUgcReportWalkTypedetour
};

typedef NS_ENUM(NSUInteger, QMUgcEntryViewFromSource) {
    QMUgcEntryViewFromCarNav,
    QMUgcEntryViewFromWalkCycleNav,
    QMUgcEntryViewFromDetectTion,
};

typedef NS_ENUM(NSUInteger, QMUgcEntryViewClickFromSource) {
    QMUgcEntryViewClickFromRoute = 1,
    QMUgcEntryViewClickFromNav,
    QMUgcEntryViewClickFromSum,
    QMUgcEntryViewClickFromARNavi
};

typedef NS_ENUM(NSInteger,QMAudioReportFromSource){
    QMAudioReportFromDingdang, // 叮当
    QMAudioReportFromPenguin,  // 小企鹅
    QMAudioReportFromUgc, // 反馈
    QMAudioReportFromRouteRefresh, // 手动多次刷新路线弹窗
    QMAudioReportFromOffcourse, // 连续偏航弹窗
    QMAudioReportFromUnClickEvent // 非点事件
};
 
@interface QMUgcReportTypeData : NSObject

+ (UIImage *)cardIconOfType:(QMUgcReportType)type;
+ (BOOL)isPoiType:(QMUgcReportType)type;
+ (BOOL)isEventType:(QMUgcReportType)type;
+ (NSString *)nameOfType:(QMUgcReportType)type;
+ (UIImage *)markerIconOfType:(QMUgcReportType)type isNight:(BOOL)isNight;
+ (UIImage *)markerPressIconOfType:(QMUgcReportType)type;
+ (UIImage *)reportIconOfType:(QMUgcReportType)type isNight:(BOOL)isNight;
+ (QMUgcReportFromSource)reportFromSourceTypeWithReportFromSourceString:(NSString *)reportFromSourceString;

@end
