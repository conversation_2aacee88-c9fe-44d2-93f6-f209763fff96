//
//  QMViewControllerPerformanceProtocol.h
//  SOSOMap
//
//  Created by l<PERSON> du<PERSON> on 2017/8/15.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol QMViewControllerPerformanceProtocol <NSObject>

@optional

/**
 是否需要性能监控

 @return 默认返回NO
 */
+ (BOOL)isNeedPerformanceMonitor;


/**
 性能监控的组件名称，用于上报的组件名称

 @return 默认返回
 */
- (NSString*)performanceMonitoringModuleName;

- (NSDictionary *)performanceExtraData;

/**
 开始进行网络请求监控
 */
- (void)startRequestPerMon;


/**
 结束网络请求监控
 */
- (void)endRequestPerMon;


/**
 开始UI展示监控
 */
- (void)startUIShowPerMon;

/**
 结束UI展示监控
 */
- (void)endUIShowPerMon;

@end
