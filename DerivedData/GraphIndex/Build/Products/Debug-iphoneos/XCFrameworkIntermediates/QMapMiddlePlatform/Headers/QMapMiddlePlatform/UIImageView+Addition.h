//
//  UIImageView+Addition.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/29.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <SDWebImage/SDWebImage.h>

typedef SDImageCacheType QMImageCacheType;
typedef SDWebImageOptions QMWebImageOptions;

typedef void(^QMExternalCompletionBlock)(UIImage * _Nullable image,
                                         NSError * _Nullable error,
                                         QMImageCacheType cacheType,
                                         NSURL * _Nullable imageURL);
typedef void(^QMImageLoaderProgressBlock)(<PERSON>SI<PERSON><PERSON> receivedSize, <PERSON>SInte<PERSON> expectedSize, NSURL * _Nullable targetURL);

NS_ASSUME_NONNULL_BEGIN

@interface UIImageView (Addition)

- (void)qm_setImageWithURLStr:(NSString *)urlStr;

- (void)qm_setImageWithURLStr:(NSString *)urlStr
             placeholderImage:(UIImage *)placeholder;

- (void)qm_setImageWithURLStr:(nullable NSString *)urlStr
             placeholderImage:(nullable UIImage *)placeholder
                      options:(QMWebImageOptions)options
                     progress:(nullable QMImageLoaderProgressBlock)progressBlock
                    completed:(nullable QMExternalCompletionBlock)completedBlock;

- (void)qm_setWeakNetworkImageURL:(NSString *)weakImgURL
                  perfectImageURL:(NSString *)perfectImageURL
                 placeholderImage:(nullable UIImage *)placeholder
                          options:(QMWebImageOptions)options
                         progress:(nullable QMImageLoaderProgressBlock)progressBlock
                        completed:(nullable QMExternalCompletionBlock)completedBlock;

@end

NS_ASSUME_NONNULL_END
