//
//  QMVoiceAssistantPanelSubViewDelegate.h
//  SOSOMap
//
//  Created by foog<PERSON><PERSON>(王中周) on 2018/6/13.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMVoiceAssistantPanelViewDelegate.h"

#define QM_VOICE_ASSISTANT_UPDATE_CONTENT_KEY_EXTRA_DATA       @"extraData"
#define QM_VOICE_ASSISTANT_UPDATE_CONTENT_KEY_DATA             @"data"

@protocol QMVoiceAssistantPanelSubViewDelegate <NSObject>

@property (nonatomic) QMVoiceAssistantPanelViewState state;

@optional
- (void)updateScene:(QMVoiceAssistantPanelViewScene)scene;
- (void)updateContent:(id)content withState:(QMVoiceAssistantPanelViewState)state scene:(QMVoiceAssistantPanelViewScene)scene;

+ (CGFloat)subViewHeight;

@end
