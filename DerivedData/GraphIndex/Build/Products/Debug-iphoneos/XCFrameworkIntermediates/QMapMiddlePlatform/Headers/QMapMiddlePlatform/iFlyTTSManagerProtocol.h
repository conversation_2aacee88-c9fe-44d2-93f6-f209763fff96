//
//  iFlyTTSManagerProtocol.h
//  QMapMiddlePlatform
//
//  Created by admin on 2020/11/10.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMapAppBridgeProtocol.h>

#define K_VOICE_DIDCHANGE_NOTIFICATION @"voiceDidChange"

@protocol iFlyTTSManagerProtocol <QMapAppBridgeProtocol>
@required
- (void)defaultManager;
- (void)playTTS:(NSString *)text;
- (void)forcePlayMP3:(NSString *)path;
- (void)stop;
- (int)forcePlayMP3:(NSString *)path finishedBlock:(void(^)(BOOL finished))finishedBlock;
@end
