//
//  iFlyTTSManager.h
//  SOSOMap
//
//  Created by nopwang on 11/22/12.
//  Copyright (c) 2012 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import "AudioManager.h" 
#import <QMapMiddlePlatform/QMLanguePackageManager.h>
#import <QMapMiddlePlatform/QttsParam.h> 

typedef void(^AudioPlayerFinishedBlock)(BOOL finished, NSError *error, QttsParam *tts);
typedef void(^AudioPlayerEnqueueFinishedBlock)(BOOL finished, NSError *error, QttsParam *tts, BOOL allFinished);
 
extern NSString *const QMIFlyTTSManagerInitializeNotification;
extern NSString *const QMIFlyTTSManagerChangeIrf;

@protocol QMTtsDingdangDelegate <NSObject>

@optional

- (void)willPlayTts:(QttsParam *)tts;

- (void)didFinishPlayTts:(QttsParam *)tts;

@end

@interface iFlyTTSManager : NSObject <AVAudioPlayerDelegate, AudioModule>
@property (nonatomic, assign) int volumeLevel;
@property (nonatomic, assign) BOOL haveSetVolumeLevelMax;     //!< 上次设置过最大音量

@property (nonatomic, strong, readonly) QttsParam *currentTTS;//当前正在播放的TTS
@property (nonatomic, assign) BOOL isCarNaviMode;     //!< 是否是驾车导航模式
// 是否是步骑行导航模式
@property (nonatomic) BOOL isWalkCycleNaviMode;
@property (nonatomic, assign) BOOL playDindDangSound;     //!< 是不是在播叮当的声音
@property (nonatomic, assign) BOOL playScenicTTS;     //!< 是不是景区播报tts
@property (nonatomic, readonly) BOOL trfEnable;

// 是否处于carplay调整导航音量场景(如果是，需要保持当前声道)
@property (nonatomic) BOOL isCarplayVolumnAdjustmentScene;

@property (nonatomic, weak) id<QMTtsDingdangDelegate> dingdangDelegate;

QMDefaultManager_H(iFlyTTSManager)

+ (BOOL)hasInitialized;

- (void)stop;

- (void)stopWithTTSParam:(QttsParam *)ttsParam;

- (void)stopWithCurrentTTSParam;

/// 仅停止来源于诱导的TTS播报
- (void)stopPlayingTTSByRouteGuidance;

//支持3种类型播报，单文本，单音频(音频文件或音频data)，音频+文本，如果有音频先播音频，播完音频再播文本
- (int)playTTS:(QttsParam *)tts finishedBlock:(AudioPlayerFinishedBlock)finishedBlock;

- (void)enqueueTTS:(QttsParam *)ttsParam
     finishedBlock:(AudioPlayerEnqueueFinishedBlock)finishedBlock;

- (NSTimeInterval)queryTttsTextPlayDuration:(NSString *)ttsString;

//此方法可以用playTTS取代，只是现在代码里有好多调用的地方，改的地方太多，暂时先保留
- (int)forcePlayMP3:(QttsParam *)tts finishedBlock:(AudioPlayerFinishedBlock)finishedBlock;//无论是否静音，都要播报。lock的需求

//是否在播报（播报TTS或者打电话）
- (BOOL)isPlaying;

//是否在播报TTS
- (BOOL)isPlayingTTS;

/// 智平语音包 version
- (NSString *)larkVoicePacketVersion;

//更换语音包，由iFlyTTSManagerTestClass测试类中调用
- (void)changeIrf:(TtsPackageItem *)item completion:(void (^)(BOOL success))completion;

@property (nonatomic, assign, readonly) BOOL ttsCreateSuccess;

-(void)dispatchCallback:(const char *)data withLength:(NSInteger)dataLen isBegin:(Boolean)bBegin isEnd:(Boolean)bEnd msgId:(const char*)sMsgId;
-(void)dispatchErrCallback:(NSInteger)retCode withErrMsg:(const char*)errMsg msgId:(const char*)sMsgId;

@end
