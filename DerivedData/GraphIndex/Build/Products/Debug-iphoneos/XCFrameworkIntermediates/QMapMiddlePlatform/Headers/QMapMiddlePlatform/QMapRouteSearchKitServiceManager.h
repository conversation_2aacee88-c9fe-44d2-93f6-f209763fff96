//
//  QMapRouteSearchKitServiceManager.h
//  QMapMiddlePlatform
//
//  Created by el<PERSON> on 2020/10/12.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import "QMNCarRoutePreferManager.h"
#import <QMapMiddlePlatform/QMCarRouteNaviCommon.h>

@class QRoutePlaceInfo;
@class QDriveRouteReqParam;
@class QRouteResultForDrive;


NS_ASSUME_NONNULL_BEGIN

@interface QMapRouteSearchKitServiceManager : NSObject

/// 获取车牌，京B123456 包含，地区和数字字母，UTF8格式
+ (NSString *)getCarNumber:(QMCarRouteNaviType)routeNaviType;

+ (NSString *)getDefaultCarNumber;

/**
 车牌号里面的汉字，使用GBK编码做URL转义
 之前使用的stringByAddingPercentEscapesUsingEncoding废弃了，所以使用这种方式实现
 */
+ (NSString *)carNumberWithGBKEncodingPercentEscapes:(QMCarRouteNaviType)routeNaviType;

+ (NSString *)getStatus;

+ (void)updateRoutePlaceInfoWithCurrentLocation:(QRoutePlaceInfo *)poi;
+ (void)updateDriveRouteReqParamWithCurrentLocation:(QDriveRouteReqParam *)param;

+ (NSString *)canTriggerDriveRouteReq:(QDriveRouteReqParam *)param;

+ (void)driveRouteReqParamArmed:(QDriveRouteReqParam *)param;

+ (void)syncStartDestName:(QRouteResultForDrive *)result param:(QDriveRouteReqParam *)param;

/// 获取设置的偏好组合，可能多个
+ (QDrivePolicy)preferSetting:(QMNCarRoutePreferType)type;
@end

NS_ASSUME_NONNULL_END
