//
//  QMWechatCustomerSheet.h
//  QMapMiddlePlatform
//
//  Created by he yan on 2023/4/7.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import "QMActionSheet.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMWechatCustomerType) {
    QMWechatCustomerPhone = 1, //电话
    QMWechatCustomerWechat //微信
};

/**标签信息**/
@interface QMContactLabelInfo : NSObject

/**标签文案*/
@property (nonatomic) NSString *text;
/**标签背景图*/
@property (nonatomic) NSString *iconPath;
/**暗黑模式标签背景图**/
@property (nonatomic) NSString *darkIconPath;
/**文案左侧padding，单位dp，无则默认4dp兜底*/
@property (nonatomic) CGFloat paddingLeft;
/**文案右侧padding，单位dp，无则默认4dp兜底*/
@property (nonatomic) CGFloat paddingRight;
/**文案顶部侧padding，单位dp，无则默认2dp兜底*/
@property (nonatomic) CGFloat paddingTop;
/**文案底部padding，单位dp，无则默认2dp兜底*/
@property (nonatomic) CGFloat paddingBottom;
/**文案大小，单位dp，无则默认10dp兜底*/
@property (nonatomic) CGFloat textSize;
/**文案颜色*/
@property (nonatomic) NSString *textColor;
/**暗黑模式文案颜色*/
@property (nonatomic) NSString *darkTextColor;
/**文案是否加粗，true=加粗，默认不加粗*/
@property (nonatomic) BOOL fontBold;

@end

//微信商户信息
@interface QMWechatCustomer : NSObject
@property (nonatomic) NSString *corpId;          //企业id
@property (nonatomic) NSString *url;             //企业url
@property (nonatomic) NSString *iconUrl;         //icon url
@property (nonatomic) NSString *label;           //电话号码，企业微信对应商户名称
@property (nonatomic) QMWechatCustomerType type; //电话&微信 类型
@property (nonatomic) NSString *phone;           //官方电话
@property (nonatomic) NSString *phoneDesc;       //官方电话的描述
@property (nonatomic) NSDictionary *paramDic;    //埋点信息

@property (nonatomic, readonly) NSString *displayLabel; //用来显示sheet上名称
@end

@interface QMWechatCustomerSheet : QMActionSheet

@property (nonatomic) NSMutableDictionary *extraInfo;

- (id)initWithWechatItems:(NSArray <QMWechatCustomer *>*)wechatItems;

- (QMWechatCustomer *)wechatCustomerInfoAtIndex:(NSInteger)buttonIndex;

@end

NS_ASSUME_NONNULL_END
