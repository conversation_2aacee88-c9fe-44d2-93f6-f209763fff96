//
//  QMapCalendarModel.h
//  QMapMiddlePlatform
//
//  Created by admin on 2021/2/7.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMapCalendarDayModel : NSObject
@property (nonatomic, assign) BOOL hidden;
@property (nonatomic, assign) BOOL enable;
@property (nonatomic, assign) NSInteger year;
@property (nonatomic, assign) NSInteger month;
@property (nonatomic, assign) NSInteger day;
@property (nonatomic, assign) NSInteger weak;
@property (nonatomic, strong) NSDate *date;

// 农历
@property (nonatomic, assign) NSInteger cYear;
@property (nonatomic, copy) NSString *cYearStr;
@property (nonatomic, assign) NSInteger cMonth;
@property (nonatomic, copy) NSString *cMonthStr;
@property (nonatomic, assign) NSInteger cDay;
@property (nonatomic, copy) NSString *cDayStr;
@property (nonatomic, copy) NSString *solarTerm;

// 外部自定义标签文字
@property (nonatomic, copy) NSString *dateTag;

@property (nonatomic, assign) BOOL today;
@property (nonatomic, assign) BOOL tomorrow;

- (NSString *)weakStr;

@end

@interface QMapCalendarMonthModel : NSObject 
@property (nonatomic, assign) NSInteger year;
@property (nonatomic, assign) NSInteger month;
@property (nonatomic, assign) NSInteger cYear;
@property (nonatomic, copy) NSString *cYearStr;
@property (nonatomic, assign) NSInteger cMonth;
@property (nonatomic, copy) NSString *cMonthStr;
@property (nonatomic, strong) NSMutableArray<QMapCalendarDayModel *> *days;
@end

NS_ASSUME_NONNULL_END
