//
//  QMVoiceAssistantDefine.h
//  SOSOMap
//
//  Created by foog<PERSON><PERSON>(王中周) on 2018/4/28.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#ifndef QMVoiceAssistantDefine_h
#define QMVoiceAssistantDefine_h

// 车机路况相关
#define QM_SMART_CAR_TRAFFIC_NOTIFICATION   @"kQMSmartCarTrafficNotification"

// 语义重构后这几个都不存在了，重构功能需要灰度，所以临时放在这，方便后面统一删除
#define DOMAIN_RESTAURANT                               @"restaurant"
#define DOMAIN_CINEMA                                   @"cinema"
#define DOMAIN_NAVIGATION_MAP_OLD                       @"navigation_map"
#define INTENT_SURROUND_SEARCH                          @"search"
#define INTENT_NAVIGATION_SEARCH_POI_OLD                @"search_POI"

// Domain 定义
#define DOMAIN_WEATHER                      @"weather"
#define DOMAIN_SURROUND                     @"surround"
#define DOMAIN_NAVIGATION_MAP               @"navigation_map_v2"
#define DOMAIN_TRAFFIC_MAP                  @"traffic_map"
#define DOMAIN_HELP                         @"help"
#define DOMAIN_CHAT                         @"chat"
#define DOMAIN_DEFAULT                      @"default"
#define DOMAIN_GLOBALCTRL                   @"globalctrl"
#define DOMAIN_GENERAL_QUESTION_ANSWERING   @"general_question_answering"
#define DOMAIN_GEOGRAPHY                    @"geography_kbqa"
#define DOMAIN_TRANSLATE                    @"translate"
#define DOMAIN_SPORTS                       @"sports"
#define DOMAIN_ASTRO                        @"astro"
#define DOMAIN_HOLIDAY                      @"holiday"
#define DOMAIN_ALMANAC                      @"almanac"
#define DOMAIN_ACTIVITY                     @"activity"
#define DOMAIN_CAR_QUESTION_ANSWERING       @"car_question_answering"
#define DOMAIN_SCREEN_CONTROL               @"screen_control"
#define DOMAIN_GARBAGE_CLASS                @"garbage_class"
#define DOMAIN_SCIENCE                      @"science"
#define DOMAIN_JOKE                         @"joke"
#define DOMAIN_INTELLIGENT_CAR_CONTROL      @"intelligent_car_control"
#define DOMAIN_GLOBALCTRL3                  @"globalctrl3"
#define DOMAIN_QQMAP                        @"qqmap"

// Intent
/****************** Start ******************/
// 导航
#define INTENT_NAVIGATION_ROUTE              @"navigation_route_v2"
#define INTENT_NAVIGATION_DIRECTLY           @"navigation_directly_v2"
#define INTENT_NAVIGATION_ASK_DISTANCE_TIME  @"ask_distance_time"
#define INTENT_NAVIGATION_SWITCH_NAVI_PREFERENCE  @"switch_navi_preference"
#define INTENT_NAVIGATION_START_NAVIGATION   @"start_navigation"
#define INTENT_NAVIGATION_CANCEL_NAVIGATION  @"cancel_navigation"
#define INTENT_NAVIGATION_SEARCH_POI         @"search_POI_v2"
#define INTENT_NAVIGATION_ZOOM_MAP           @"zoom_map"
#define INTENT_NAVIGATION_SWITCH_NIGHT_MODE  @"switch_night_mode"
#define INTENT_NAVIGATION_SWITCH_OVERVIEW_MODE @"switch_overview_mode"
#define INTENT_NAVIGATION_SWITCH_MAP_VISIUAL  @"switch_map_visual"
#define INTENT_NAVIGATION_ADD_POI            @"add_POI"
#define INTENT_NAVIGATION_REPEAT_NAVI_REMINDER  @"repeat_navi_reminder"
#define INTENT_NAVIGATION_ROUTE_SHARE       @"route_share"
#define INTENT_NAVIGATION_QUERY_TRAFFIC_RESTRICTION_NUM  @"query_traffic_restriction_num"
#define INTENT_NAVIGATION_FRONTPAGE         @"frontpage"
#define INTENT_NAVIGATION_GO_THERE          @"go_there"
#define INTENT_NAVIGATION_SEARCH_BUSLINE    @"search_busline"
#define INTENT_NAVIGATION_ASK_THE_ROAD_AHEAD  @"ask_the_road_ahead"
#define INTENT_NAVIGATION_FEEDBACK_REPORTED @"feedback_reported"
#define INTENT_NAVIGATION_MUTI_SWITCH_NAVI_TYPE  @"muti_switch_navi_type"
#define INTENT_NAVIGATION_NO_VOICEBROADCAST  @"no_voicebroadcast"
#define INTENT_NAVIGATION_VOICEBROADCAST_LOW  @"voicebroadcast_low"
#define INTENT_NAVIGATION_DELETE_POI        @"delete_POI"
#define INTENT_NAVIGATION_DISCLAIMER_CHECK  @"disclaimer_check"
#define INTENT_NAVIGATION_WECHAT_PAYMENT_CHECK @"wechat_payment_check"
#define INTENT_NAVIGATION_PARKING_CHECK     @"parking_check"
#define INTENT_NAVIGATION_REFRESH_ROUTE     @"refresh_route"
#define INTENT_NAVIGATION_CHANGE_DESTINATION @"change_destination"
#define INTENT_NAVIGATION_ASK_SPEED_LIMIT    @"ask_speed_limit"
#define INTENT_NAVIGATION_ASK_DESTINATION_NAME  @"ask_destination_name"
#define INTENT_NAVIGATION_SWITCH_SPEAK_MODE     @"switch_speak_mode"
#define INTENT_NAVIGATION_SWITCH_ROUTE      @"switch_route"
#define INTENT_NAVIGATION_QUERY_TRAFFIC_LIGHTS      @"query_traffic_lights"
#define INTENT_NAVIGATION_QUERY_TRAFFIC_LIGHTS_DISTANCE      @"query_traffic_lights_distance"
#define INTENT_NAVIGATION_START_ARNAVIGATION    @"start_ARnavigation"
#define INTENT_NAVIGATION_CANCEL_ARNAVIGATION   @"cancel_ARnavigation"

// 车机路况
#define INTENT_OPEN_TRAFFIC_REPORT          @"open_traffic_report"
#define INTENT_CLOSE_TRAFFIC_CONTROL        @"close_traffic_control"
#define INTENT_QUERY_ROUTE_TRAFFIC          @"query_route_traffic"
#define INTENT_QUERY_TRAFFIC_AHEAD          @"query_traffic_ahead"

// 检索
#define INTENT_SURROUND_SELF_POSITIONING    @"self_positioning"

// 电影院
#define INTENT_CHINEMA_SEARCH               @"cinema_search"

// 天气
#define INTENT_CONDITIONAL_SEARCH_FEEL      @"conditional_search_feel"
#define INTENT_CONDITIONAL_SEARCH_ACTIVITY  @"conditional_search_activity"
#define INTENT_GENERAL_SEARCH               @"general_search"
#define INTENT_AQI_SEARCH                   @"aqi_search"
#define INTENT_CONDITIONAL_SEARCH_DESCRIPTION  @"conditional_search_description"
#define INTENT_CONDITIONAL_SEARCH_OUTFIT       @"conditional_search_outfit"
#define INTENT_CONDITIONAL_SEARCH_HUMIDITY     @"conditional_search_humidity"
#define INTENT_CONDITIONAL_SEARCH_ULTRAVIOLET  @"conditional_search_ultraviolet"
#define INTENT_CONDITIONAL_SEARCH_TEMPERATURE  @"conditional_search_temperature"

// 帮助
#define INTENT_CANDO                        @"cando"

// 全局
#define INTENT_TURN_UP                      @"turn_up"
#define INTENT_TURN_DOWN                    @"turn_down"
#define INTENT_TURN_DOWN_MAX                @"turn_up_max"
#define INTENT_TURN_DOWN_MIN                @"turn_down_min"
#define INTENT_MUTE                         @"mute"
#define INTENT_UNMUTE                       @"unmute"
#define INTENT_INDEX_V2                     @"index_v2" // 第几个
#define INTENT_YES                          @"yes" // 肯定回答
#define INTENT_NO                           @"no" // 否定回答
#define INTENT_CANCEL                       @"cancel"
#define INTENT_STOP                         @"stop"
#define INTENT_EXIT                         @"exit"
#define INTENT_REPLAY                       @"replay"
#define INTENT_NAVIGATION_VOICE             @"navigation_voice"
#define INTENT_GO_TO_FUNCTION               @"go_to_function"
#define INTENT_DOWNLOAD                     @"download"
#define INTENT_SOUND_CHANNEL_ON             @"sound_channel_on"
#define INTENT_SOUND_CHANNEL_OFF            @"sound_channel_off"
#define INTENT_SOUND_VOLUME_CONTROL         @"sound_volume_control"

// 运营活动
#define INTENT_PLAY_AUDIO                   @"play_audio"
#define INTENT_OPEN_H5                      @"open_H5"
#define INTENT_PLAY_ANIMATION               @"play_animation"

// 亮度调节
#define INTENT_BRIGHTNESS_UP                @"brightness_up"
#define INTENT_BRIGHTNESS_DOWN              @"brightness_down"

// 垃圾回收
#define INTENT_SEARCH_CLASSIFICATION        @"search_classification"


// 讲笑话
#define INTENT_TELL                         @"tell"

// 手图未勾选，但智平支持
#define INTENT_NOTSELECT                    @"not_select"

//globalctrl3
#define INTENT_GLOBALCTRL3_EXIT3             @"exit3"
#define INTENT_GLOBALCTRL3_EXIT_CONVERSATION3       @"exit_conversation3"

/****************** End ********************/

// Slots 定义
/****************** Start ******************/
#define SLOTS_NAME_POI_DESTINATION                      @"poi_destination"
#define SLOTS_NAME_POI_TYPE_DESTINATION_V2              @"poi_type_destination"
#define SLOTS_NAME_CUSTOM_ADDRESS_DESTINATION           @"custom_address_destination"
#define SLOTS_NAME_RANK_DESTINATION                     @"rank_destination"
#define SLOTS_NAME_POI_ORIGIN                           @"poi_origin"
#define SLOTS_NAME_CUSTOM_ADDRESS_ORIGIN                @"custom_address_origin"
#define SLOTS_NAME_CUSTOM_ADDRESS_DESTINATION           @"custom_address_destination"
#define SLOTS_NAME_POI_TYPE_ORIGIN                      @"poi_type_origin"
#define SLOTS_NAME_RANK_DESTINATION_CENTER              @"rank_destination_center"
#define SLOTS_NAME_RANK_ON_THE_WAY_CENTER               @"rank_on_the_way_center"
#define SLOTS_NAME_RANK_ORIGIN_CENTER                   @"rank_origin_center"
#define SLOTS_NAME_RANK_ON_THE_WAY                      @"rank_on_the_way"
#define SLOTS_NAME_RANK_ORIGIN                          @"rank_origin"
#define SLOTS_NAME_RANK                                 @"rank"
#define SLOTS_NAME_REFERENCE_ON_THE_WAY_CENTER          @"reference_on_the_way_center"
#define SLOTS_NAME_CUSTOM_ADDRESS_ON_THE_WAY_CENTER     @"custom_address_on_the_way_center"
#define SLOTS_NAME_POI_TYPE_ON_THE_WAY_CENTER           @"poi_type_on_the_way_center"
#define SLOTS_NAME_POI_ON_THE_WAY_CENTER                @"poi_on_the_way_center"
#define SLOTS_NAME_CUSTOM_ADDRESS_DESTINATION_CENTER    @"custom_address_destination_center"
#define SLOTS_NAME_REFERENCE_DESTINATION_CENTER         @"reference_destination_center"
#define SLOTS_NAME_REFERENCE_DESTINATION                @"reference_destination"
#define SLOTS_NAME_REFERENCE_ORIGIN_CENTER              @"reference_origin_center"
#define SLOTS_NAME_CUSTOM_ADDRESS_ORIGIN_CENTER         @"custom_address_origin_center"
#define SLOTS_NAME_POI_TYPE_DESTINATION_CENTER          @"poi_type_destination_center"
#define SLOTS_NAME_POI_TYPE_ORIGIN_CENTER               @"poi_type_origin_center"
#define SLOTS_NAME_POI_DESTINATION_CENTER               @"poi_destination_center"
#define SLOTS_NAME_POI_ORIGIN_CENTER                    @"poi_origin_center"
#define SLOTS_NAME_REFERENCE_ON_THE_WAY                 @"reference_on_the_way"
#define SLOTS_NAME_CUSTOM_ADDRESS_ON_THE_WAY            @"custom_address_on_the_way"
#define SLOTS_NAME_POI_TYPE_ON_THE_WAY                  @"poi_type_on_the_way"
#define SLOTS_NAME_POI_ON_THE_WAY                       @"poi_on_the_way"
#define SLOTS_NAME_NAVIGATION_PREFERENCE                @"navigation_preference"
#define SLOTS_NAME_CUSTOM_ADDRESS_CENTER                @"custom_address_center"
#define SLOTS_NAME_POI_CENTER                           @"poi_center"
#define SLOTS_NAME_LOCATION_HYPERNYM         @"location_hypernym"
#define SLOTS_NAME_LOCATION_TYPE             @"location_type"
#define SLOTS_NAME_LOCATION_FUNCTION         @"location_function"
#define SLOTS_NAME_LOCATION                  @"location"
#define SLOTS_NAME_POI_TYPE_DESTINATION      @"POI_type_destination"
#define SLOTS_NAME_POI                      @"poi"
#define SLOTS_NAME_POI_TYPE                 @"poi_type"

// 周边
#define SLOTS_NAME_DISTANCE                 @"distance"
#define SLOTS_NAME_DISTANCE_UNIT            @"distance_unit"

// 导航
#define SLOTS_NAME_DESTINATION              @"destination"
#define SLOTS_NAME_ORIGIN                   @"origin"
#define SLOTS_NAME_NAVIGATION_TYPE          @"navigation_type"
#define SLOTS_NAME_CUSTOM_DESTINATION       @"custom_destination_v2"        // 终点是家、公司、单位
#define SLOTS_NAME_CUSTOM_ORIGIN            @"custom_origin"                // 起点是家、公司、单位
#define SLOTS_NAME_ROUTE_TAG                @"route_tag"                    // 导航偏好设置
#define SLOTS_NAME_POI_TYPE_ON_THE_WAY      @"poi_type_on_the_way"
#define SLOTS_NAME_POI_ON_THE_WAY           @"poi_on_the_way"
#define SLOTS_NAME_NAVI_CONTROL             @"navi_control"
#define SLOTS_NAME_SHARE_TYPE               @"share_type"
#define SLOTS_NAME_APP_PAGE                 @"app_page"
#define SLOTS_NAME_NAVIGATION_VOICE         @"navigation_voice"
#define SLOTS_NAME_PAGE_NAME                @"page_name"
#define SLOTS_NAME_BTN_NAME                 @"btn_name"
#define SLOTS_NAME_REFER_ORIGIN             @"reference_origin"
#define SLOTS_NAME_SPEAK_MODE               @"speak_mode"

// 全局
#define SLOTS_NAME_INDEX_ORDINAL            @"index_ordinal"                // 序列数
#define SLOTS_NAME_DATE                     @"date"

// intelligent_car_control
#define SLOTS_NAME_SOUND_UNIT               @"sound_unit"
#define SLOTS_NAME_SOUND_VOLUME_NUMBER      @"sound_volume_number"
#define SLOTS_NAME_SOUND_VOLUME_INCR_NUMBER @"sound_volume_incr_number"
#define SLOTS_NAME_SOUND_VOLUME_DIRECTION   @"sound_volume_direction"
#define SLOTS_NAME_SOUND_VOLUME_MODE        @"sound_volume_mode"

/****************** End ********************/

// 语义字典中的 key
/****************** Start ******************/
#define SEMANTIC_KEY_QUERY                  @"query"
#define SEMANTIC_KET_DOMAIN                 @"domain"
#define SEMANTIC_KEY_INTENT                 @"intent"
#define SEMANTIC_KEY_SLOTS                  @"slots"
#define SEMANTIC_KEY_SLOT_NAME              @"name"
#define SEMANTIC_KEY_SLOT_TYPE              @"type"
#define SEMANTIC_KEY_SLOT_STRUCT            @"slot_struct"
#define SEMANTIC_KEY_SLOT_VALUES            @"values"
//#define SEMANTIC_KEY_SLOT_ORDINAL           @"ordinal"
/****************** End ********************/

// 出行方式
/****************** Start ******************/
#define NAVIGATION_TYPE_BIKE                @"骑自行车"
#define NAVIGATION_TYPE_CAR                 @"开车"
#define NAVIGATION_TYPE_WALK                @"步行"
#define NAVIGATION_TYPE_BUS                 @"公交"
#define NAVIGATION_TYPE_TAXI                @"打车"
#define NAVIGATION_TYPE_TRAIN               @"火车"
#define NAVIGATION_TYPE_COACH               @"客车"
#define NAVIGATION_TYPE_PLANE               @"飞机"
#define NAVIGATION_TYPE_CHAUFFEUR           @"代驾"
#define NAVIGATION_TYPE_TRUCK               @"货车"
#define NAVIGATION_TYPE_MOTOR               @"骑摩托车"
/****************** End ********************/

// 关键字
/****************** Start ******************/
// 家和公司
#define KEYWORD_HOME                        @"家"
#define KEYWORD_COMPANY                     @"公司"
/****************** End ********************/

// 智能语音帮助H5

#define H5_URL_VOICE_ASSISTANT_HELP @"https://map.wap.qq.com/online/h5-voice-help/voice_help_v2.html?&ua=QQ_Map_Mobile&version=10.14.0&platform=ios"

typedef enum : NSUInteger {
    QMSemanticModeValueUnknown,
    QMSemanticModeValueDay,
    QMSemanticModeValueNight,
} QMSemanticModeValue;

typedef enum : NSUInteger {
    QMSemanticControlValueUnknown,
    QMSemanticControlValueOpen,
    QMSemanticControlValueClose,
} QMSemanticControlValue;

// 手图后台路线标签
/****************** Start ******************/
extern NSString* const classic_route;
extern NSString* const less_light;
extern NSString* const short_time;
extern NSString* const short_distance;
extern NSString* const recommend_route;
extern NSString* const route_2;
extern NSString* const route_3;
extern NSString* const has_fee;
extern NSString* const fast_route;
extern NSString* const wide_road_route;
extern NSString* const smooth_route;
extern NSString* const personal_route;
extern NSString* const least_price;
extern NSString* const no_price_toll_txt;
extern NSString* const no_toll_tag;
extern NSString* const highway_prior;
extern NSString* const no_highway;
extern NSString* const avoid_fee;
extern NSString* const preference_big_road;
extern NSString* const preference_short_time;
/****************** End ********************/

extern NSString* const QMZoomMapZoomin;
extern NSString* const QMZoomMapZoomout;
extern NSString* const QMNightModeNight;
extern NSString* const QMNightModeDay;
extern NSString* const QMOverviewMode;
extern NSString* const QMOverviewEntireMode;
extern NSString* const QMOverviewModeOpen;
extern NSString* const QMOverviewModeClose;
extern NSString* const QMViewMode2D;
extern NSString* const QMViewMode3D;

extern NSString* const QMVoiceAssistantIntentKey;
extern NSString* const QMVoiceAssistantValueKey;

#endif /* QMVoiceAssistantDefine_h */
