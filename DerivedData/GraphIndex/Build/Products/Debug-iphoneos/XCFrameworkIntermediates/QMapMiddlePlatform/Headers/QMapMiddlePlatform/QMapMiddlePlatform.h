//
//  QMapMiddlePlatform.h
//  QMapMiddlePlatform
//
//  Created by allensun on 2020/9/15.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

//! Project version number for QMapMiddlePlatform.
FOUNDATION_EXPORT double QMapMiddlePlatformVersionNumber;

//! Project version string for QMapMiddlePlatform.
FOUNDATION_EXPORT const unsigned char QMapMiddlePlatformVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <QMapMiddlePlatform/PublicHeader.h>

// Main
#import <QMapMiddlePlatform/QMPlistFileAccessor.h>
#import <QMapMiddlePlatform/QMapMiddlePlatformBundle.h>

// MWA
#import <QMapMiddlePlatform/QMMWAConfigDataManager.h>
#import <QMapMiddlePlatform/QMMWAConfigDataManager+OperationEggs.h>
#import <QMapMiddlePlatform/QMMWAConfigDataManager+OperationSystem.h>

// 积分
#import <QMapMiddlePlatform/QMCreditService.h>

#import <QMapMiddlePlatform/QMSIMManager.h>

// 登陆
#import <QMapMiddlePlatform/QMLoginServicePublic.h>

// loading 控件
#import <QMapMiddlePlatform/QMMaskView.h>
#import <QMapMiddlePlatform/QMGradientButton.h>

// 基础 VC
#import <QMapMiddlePlatform/QMViewController.h>
#import <QMapMiddlePlatform/QMViewControllerPerformanceProtocol.h>
#import <QMapMiddlePlatform/QMViewController+PageName.h>

// UserDefaults
#import <QMapMiddlePlatform/QMUserDefaultsManager.h>

#import <QMapMiddlePlatform/QMTrafficHelper.h>

//ApolloDataManager
#import <QMapMiddlePlatform/QMBaseLineApolloDataManager.h>
#import <QMapMiddlePlatform/QMNaviApolloDataManager.h>

#import <QMapMiddlePlatform/QMMapPrivacyAlertManager.h>

//Model
#import <QMapMiddlePlatform/QMapMiddlePlatformModelDefine.h>

//LaunchAnimation
#import <QMapMiddlePlatform/QMLaunchAnimationManager.h>

//日夜间
#import <QMapMiddlePlatform/QMDaylightManager.h>

//Navigation
#import <QMapMiddlePlatform/QMMiddlePlatformNavigationDefine.h>
#import <QMapMiddlePlatform/QMNavigationService.h>

//AnimationTransition
#import <QMapMiddlePlatform/QMAnimationTransition.h>

//File
#import <QMapMiddlePlatform/QMFileManager.h>

//Navi
#import <QMapMiddlePlatform/NSString+Navi.h>

//Category
#import <QMapMiddlePlatform/NSString+Addition.h>

#import <QMapMiddlePlatform/QMGeneralPerformanceReporter.h>
