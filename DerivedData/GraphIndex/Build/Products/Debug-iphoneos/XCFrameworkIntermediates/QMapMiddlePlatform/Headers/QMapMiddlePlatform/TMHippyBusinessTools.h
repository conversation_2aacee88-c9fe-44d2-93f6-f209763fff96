//
//  TMHippyBusinessTools.h
//  QMapHippy
//
//  Created by wyh on 2021/10/1.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

@class DMSharedMapView;

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, TMHippyBusinessScene) {
    TMHippyBusinessSceneUnknown = 0,
    
    TMHippyBusinessSceneHomeCard, // 首页卡片
    TMHippyBusinessSceneDriveMulti, // 多方案卡片
    TMHippyBusinessSceneMainSearch, // 主搜卡片
    TMHippyBusinessScenePoiSearch, // 主搜
    TMHippyBusinessScenePoiCard, // 点选小页卡
    TMHippyBusinessSceneSummary, // 小结页
    TMHippyBusinessScenePersonalCenter, // 个人中心
    TMHippyBusinessSceneHomeMapCard, // 图卡联动
    TMHippyBusinessSceneBusTab,
    TMHippyBusinessSceneOrderIndex, //打车
    TMHippyBusinessSceneTravelMap, // travelMap
    TMHippyBusinessScenePOIMultiPlanDetail, // 多方案页终点详情
    TMHippyBusinessSceneHomeSeekModule, //首页探寻
};

@interface TMHippyBusinessTools : NSObject

+ (TMHippyBusinessScene)parseToSceneWithHippyBridgeId:(NSString *)hippyBridgeId;

/// 固定容器的 DMSharedMapView
+ (DMSharedMapView *)getDMSharedMapViewFromCurrentPresentViewController;

@end

NS_ASSUME_NONNULL_END
