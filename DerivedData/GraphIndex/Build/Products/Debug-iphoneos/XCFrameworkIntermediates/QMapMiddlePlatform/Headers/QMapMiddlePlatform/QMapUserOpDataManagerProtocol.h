//
//  QMapUserOpDataManagerProtocol.h
//  QMapMiddlePlatform
//
//  Created by admin on 2020/10/30.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMapAppBridgeProtocol.h>

#define EVENT_DOWNLOAD       @"DOWNLOAD_EVENTS"
#define OFFLINE_DOWNLOAD_EIP @"I_OL_DW_ER_EIP" // ip访问错误,
//统计失败率分子
#define OFFLINE_DOWNLOAD_HEADER @"I_OL_DW_ER_HEADER" //下载地图包头问题
#define OFFLINE_DOWNLOAD_4XX5XX @"I_OL_DW_ER_4XX5XX" //服务器返回403，404，505等错误

@protocol QMapUserOpDataManagerProtocol <QMapAppBridgeProtocol>
@required
- (void)accumulateKeyEventReport:(NSString *)name eventId:(NSString *)eventId result:(NSString *)result detail:(NSString*)detail;

@end
