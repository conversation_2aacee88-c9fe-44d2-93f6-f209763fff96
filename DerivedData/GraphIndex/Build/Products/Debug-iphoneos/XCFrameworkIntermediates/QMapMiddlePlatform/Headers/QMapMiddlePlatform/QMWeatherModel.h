//
//  QMWeatherModel.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/4/10.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class QMWeatherDataModel;
@interface QMWeatherModel : NSObject

@property (nonatomic) NSString *msg;

@property (nonatomic) NSString *req_id;

@property (nonatomic) NSArray<QMWeatherDataModel *> *data;

@end

@interface QMWeatherDataModel : NSObject

@property (nonatomic) NSInteger max_temp;

@property (nonatomic) NSInteger weather;

@property (nonatomic) NSString *city_name;

@property (nonatomic) NSInteger temp;

@property (nonatomic) NSInteger min_temp;

@property (nonatomic) NSString *weather_desc;

@property (nonatomic) NSInteger unusual_weather;

@end

NS_ASSUME_NONNULL_END
