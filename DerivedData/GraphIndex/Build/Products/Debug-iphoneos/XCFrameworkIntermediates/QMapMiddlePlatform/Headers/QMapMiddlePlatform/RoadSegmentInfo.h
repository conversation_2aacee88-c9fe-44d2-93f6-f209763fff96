//
//  RoadSegmentInfo.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 12/16/13.
//  Copyright (c) 2013 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, RoadSegmentType)
{
    RoadSegmentTypeFullSegment = 0, //整条路段
    RoadSegmentTypeAcceleration, //加速
    RoadSegmentTypeDeceleration, //减速
    RoadSegmentTypeCorner, //急转弯
    RoadSegmentTypeOverSpeed, //超速
};

typedef NS_ENUM(NSInteger, RoadSegmentColor)
{
    RoadSegmentNormalColor = 0,
    RoadSegmentDecelerationColor,
    RoadSegmentAccelerationColor,
    RoadSegmentCornerColor,
    RoadSegmentOverSpeedColor,
};

@interface RoadSegmentInfo : NSObject
@property (nonatomic, assign) double beginTimeStamp;
@property (nonatomic, assign) double endTimeStamp;
@property (nonatomic, assign) NSInteger beginSegmentIndex;
@property (nonatomic, assign) NSInteger endSegmentIndex;
@property (nonatomic, assign) NSInteger expandedBeginIndex; //在beginIndex的基础上往左扩充N个点后的索引
@property (nonatomic, assign) NSInteger expandedEndIndex; //在endIndex的基础上往右扩充N个点后的索引
@property (nonatomic, assign) NSInteger beginIndexInKeededArary;//在抽稀后的关键点索引数组中的开始索引
@property (nonatomic, assign) NSInteger endIndexInKeepedArray; //在抽稀后的关键点索引数组中的结束索引
@property (nonatomic, assign) CGFloat distance;
@property (nonatomic, assign) RoadSegmentType type;
@property (nonatomic, assign) RoadSegmentColor color;

+ (NSUInteger)convertColorToTextureColorIndex:(RoadSegmentColor)color;//将颜色对应到纹理中的索引
- (int)priority; //当起始索引完全相同时根据type排序
- (BOOL)compare:(RoadSegmentInfo *)comparedObj; //绘制优先级排序，按起始点的索引排序


@end
