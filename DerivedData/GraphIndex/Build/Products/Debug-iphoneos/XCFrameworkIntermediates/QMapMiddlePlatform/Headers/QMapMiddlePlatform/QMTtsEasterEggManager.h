//
//  QMTtsEasterEggManager.h
//  QMapMiddlePlatform
//
//  Created by 江航 on 2022/3/25.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN




@class QttsParam;
//处理tts播报替换mp3彩蛋内容
@interface QMTtsEasterEggManager : NSObject

QMDefaultManager_H(QMTtsEasterEggManager)

+ (void)performTaskWhenMapDidDrawOrTimeOut;

//播报开始导航前彩蛋,返回值含义：是否播报
- (BOOL)playBeforeNaviEggIfNeed;
//导航前彩蛋是否ready
- (BOOL)canPlayBeforeNaviEgg;
//导航结束清除标记
- (void)clear;
//满足条件，替换mp3彩蛋
- (QttsParam *)replaceParamIfNeed:(QttsParam *)param;

//空闲距离
- (NSInteger)freeTimeEggDistance;
//空闲彩蛋是否可用
- (BOOL)isFreeTimeEggEnable;

/**切换主题重新下载对应的彩蛋数据*/
- (void)p_preDownloadIfNeed;
/**处理空闲时机**/
- (void)handleFreeTime:(NSInteger)idleTime;

@end

NS_ASSUME_NONNULL_END
