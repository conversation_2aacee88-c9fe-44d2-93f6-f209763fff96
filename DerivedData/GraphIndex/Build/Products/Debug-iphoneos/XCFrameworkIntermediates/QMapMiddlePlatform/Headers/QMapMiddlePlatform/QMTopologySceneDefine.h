//
//  QMTopologySceneDefine.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/5.
//  Copyright © 2024 Tencent. All rights reserved.
//

#ifndef QMTopologySceneDefine_h
#define QMTopologySceneDefine_h

/// 场景状态
typedef NS_ENUM(NSInteger, QMTopologySceneState) {
    QMTopologySceneStateInit = 1, // 初始化
    QMTopologySceneStateAppear, // 显示
    QMTopologySceneStateDisappear, // 消失
    QMTopologySceneStateDestroy // 销毁
};

/// 场景展示状态
typedef NS_ENUM(NSInteger, QMTopologySceneAppearState) {
    QMTopologySceneAppearLaunch, // 冷启动
    QMTopologySceneAppearHotLaunch, // 热启动
    QMTopologySceneAppearReturn, // 端内返回
};

/// 层级
typedef NS_ENUM(NSInteger, QMTopologySceneLevel) {
    QMTopologySceneLevelGround = 10, // 背景层
    QMTopologySceneLevelGrid = 30, // 介于背景和前景之间的层级
    QMTopologySceneLevelFront = 40, // 前景层
    QMTopologySceneLevelCover = 50, // 全屏覆盖层
    QMTopologySceneLevelAlert = 100 // 弹框，层级最高，必须是全屏的
};

FOUNDATION_EXTERN NSString *const QMTopologySceneHome;  /// 首页场景

FOUNDATION_EXTERN NSString *const QMTopologySceneSearch;    /// 搜索场景

#endif /* QMTopologySceneDefine_h */
