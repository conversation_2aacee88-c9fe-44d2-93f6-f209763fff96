//
//  QMUIUtils.h
//  QMapMiddlePlatform
//
//  Created by elwin on 2020/10/4.
//  Copyright © 2020 Tencent. All rights reserved.
//

#ifndef QMUIUtils_h
#define QMUIUtils_h

typedef NS_ENUM(NSUInteger, QMRectAngleCorner) {
    QMRectAngleCornerBottomRight = 0,
    QMRectAngleCornerBottomLeft  = 1,
    QMRectAngleCornerTopRight    = 2,
    QMRectAngleCornerTopLeft     = 3,
};

typedef NS_ENUM(NSUInteger, QMRouteNaviScene) {
    QMRouteNaviSceneNone =                 0,
    QMRouteNaviSceneDriveMulti = 1,      // 驾车多方案页
    QMRouteNaviSceneDriveNavi = 2,       // 驾车导航
    QMRouteNaviSceneWalkMulti = 3,       // 步行多方案
    QMRouteNaviSceneCycleMulti = 4,      // 骑行多方案
    QMRouteNaviSceneWalkNavi = 5,        // 步行导航
    QMRouteNaviSceneCycleNavi = 6,       // 骑行导航
    QMRouteNaviSceneDetectionNavi = 7,   // 路线探测
    QMRouteNaviSceneFutureETA = 8,        // 未来出行ETA
    QMRouteNaviSceneTruckMulti = 9,      // 货车多方案页
    QMRouteNaviSceneMotorMulti = 10,     // 摩托车多方案页
};

// 对应 QMJCE_tmap_Marker 中的 marker_style
typedef NS_ENUM(NSUInteger, QMExplainMarkerStyle) {
    QMExplainMarkerStyleDefault =                 0,// 默认样式
    QMExplainMarkerStyleWalkCycleKeyPoint = 1,      // 步骑关键节点的 marker 样式
};



#endif /* QMUIUtils_h */
