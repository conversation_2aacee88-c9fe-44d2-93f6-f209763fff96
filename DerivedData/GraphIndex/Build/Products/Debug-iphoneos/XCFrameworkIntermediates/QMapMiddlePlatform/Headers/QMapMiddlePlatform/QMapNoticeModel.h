//
//  QMapNoticeModel.h
//  SOSOMap
//
//  Created by admin on 2020/3/23.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, QMNoticeDataType) {
    QMNoticeDataTypeAnnouncement,
    QMNoticeDataTypePoiActivity,
    QMNoticeDataTypePoiMiniProgram
};
// 需对齐协议
typedef NS_ENUM(NSUInteger, QMNoticeType) {
    QMNoticeTypeNone = 0,
    QMNoticeTypeInfo = 1,
    QMNoticeTypeError = 2,
    QMNoticeTypeSuccess = 3,
    QMNoticeTypeWarning = 4,
    QMNoticeTypeOperation = 5,
};

typedef NS_ENUM(NSUInteger, QMAnnouncementType) {
    QMAnnouncementTypeUnknow = 0,
    QMAnnouncementTypeOpeningHours = 1, // 营业时间
    QMAnnouncementTypeRealtimeBus = 2, // 实时公交信号中断
    QMAnnouncementTypeThirdClient = 3, // 第三方跳转
    QMAnnouncementTypeWeatherAlarm = 4, // 天气预警
    QMAnnouncementTypeWeatherForecaset = 5, // 特殊天气预报
};

@class QMAnnouncementInfo;

@interface QMNoticeDataInfo : NSObject
// 1:poi活动信息   2:poi小程序信息
@property (nonatomic) QMNoticeDataType dataType;
// 透出是小程序时才有
@property (nonatomic) NSArray<NSString *> *icons;
// 展示标题
@property (nonatomic) NSString *title;
// 高亮文案
@property (nonatomic) NSString *heightLighText;
// 跳转按钮标题
@property (nonatomic) NSString *buttonTitle;
// 跳转链接
@property (nonatomic) NSString *link;
@property (nonatomic) NSString *poiId;

- (BOOL)isPoiAroundActivity;
@end


@interface QMNoticeWeatherInfo : NSObject
//天气预警类型 - 天气预警tips使用
@property (nonatomic) NSString *alarmName;
//天气预警等级 - 天气预警tips使用
@property (nonatomic) NSString *alarmLevel;
//特殊天气类型 - 特殊天气预报tips使用
@property (nonatomic) NSString *forecastName;
// 跳转链接
@property (nonatomic) NSString *jumpTo;
@end

@interface QMapNoticeDetailModel : NSObject
@property (nonatomic, assign) QMNoticeType type;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSAttributedString *attText;
@end

@interface QMapNoticeDetailSectionModel : NSObject
@property (nonatomic, assign) QMNoticeType type;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSArray<QMapNoticeDetailModel *> *detailList;
@end

@interface QMapNoticeModel : NSObject
@property (nonatomic) QMNoticeDataType dataType;
@property (nonatomic, assign) QMNoticeType type;
@property (nonatomic, copy) NSAttributedString *attText;
@property (nonatomic, copy) NSString *picUrl;
@property (nonatomic, strong) UIImage *picImage;
@property (nonatomic, copy) NSString *linkUrl;

@property (nonatomic) BOOL showDetailButton;
@property (nonatomic) NSString *leftIcon;
@property (nonatomic) NSString *title;
@property (nonatomic) NSString *text;

/// 上报信息 云控Tips Key
@property (nonatomic, copy) NSString *tipsKey;
@property (nonatomic, assign) int weight;
/// 解释性tips类型
@property (nonatomic) QMAnnouncementType source;
/// 天气样式信息
@property (nullable, nonatomic) QMNoticeWeatherInfo *weatherExt;
@property (nonatomic, assign) BOOL hasCustomDetail;
@property (nonatomic, copy) NSArray<QMapNoticeDetailSectionModel *> *customDetailList;
@property (nonatomic) QMJCE_text_AnnouncementInfo *announceInfo;
@property (nonatomic) QMJCE_MapRoute_DataInfo *jceDataInfo;
@property (nonatomic) QMNoticeDataInfo *dataInfo;
@end

NS_ASSUME_NONNULL_END
