//
//  RemainTimeCalculator.h
//  QMapNaviKit
//
//  Created by <PERSON><PERSON><PERSON> on 12/6/2017.
//  Copyright © 2017 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TrafficTimeInfo.h"
#import <QMapRouteSearchKit/QRouteSearchItem.h>

@class QRouteForDrive;

/// ETA计算类，兜底逻辑也在其中
@interface RemainTimeCalculator : NSObject

/**
 @param trafficTimesArray 网络请求返回的剩余时间OnRouteEvent数组,如果为空表示请求失败，剩余时间计算使用兜底策略
 @param totalTrafficTime 蚯蚓请求路况总时间
 @param route 路线，如果有trafficTimesArray会更新路况中的是OnRouteEvent数据到route.routeTrafficArray
 */
+ (void)updateTrafficTimesArray:(NSArray *)trafficTimesArray totalTrafficTime:(NSInteger)totalTrafficTime route:(QRouteForDrive *)route;

/// 强制使用兜底策略
/// @param route 路线数据
+ (void)forceUpdateRouteExposedTraffticTimes:(QRouteForDrive *)route;

@end
