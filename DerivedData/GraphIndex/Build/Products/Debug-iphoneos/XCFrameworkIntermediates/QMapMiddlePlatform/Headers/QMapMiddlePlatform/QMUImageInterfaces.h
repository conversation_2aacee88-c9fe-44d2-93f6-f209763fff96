//
//  QMUImageInterfaces.h
//  QMapMiddlePlatform
//
//  Created by wyh on 2021/10/22.
//  Copyright © 2021 Tencent. All rights reserved.
//

#ifndef QMUImageInterfaces_h
#define QMUImageInterfaces_h

static NSString * const QMULogDomain = @"QMUImage";

typedef NS_ENUM(NSUInteger, QMUImageViewType) {
    QMUImageViewTypeUnknown,
    QMUImageViewTypePAG,
    QMUImageViewTypeLottie,
    QMUImageViewTypePNG, // Compatible with JPEG / Gif
};

typedef NS_ENUM(NSUInteger, QMUSrcFileType) {
    QMUSrcFileTypeInputError,
    
    QMUSrcFileTypeURL_Zip,
    QMUSrcFileTypeURL_PNG,
    QMUSrcFileTypeURL_PAG,
    QMUSrcFileTypeFilePath_LOT,
    QMUSrcFileTypeFilePath_PAG,
    QMUSrcFileTypeFilePath_PNG,
};

#endif /* QMUImageInterfaces_h */
