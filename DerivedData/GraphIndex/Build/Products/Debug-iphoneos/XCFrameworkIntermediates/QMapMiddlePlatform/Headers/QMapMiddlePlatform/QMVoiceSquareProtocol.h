//
//  QMVoiceSquareProtocol.h
//  QMapMiddlePlatform
//
//  Created by 江航 on 2021/7/20.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark --Request
//语音广场请求参数
@interface QMVoiceSquareListServiceReq : QMModel
//
@property (nonatomic, assign) NSInteger act_id;
//设备qimei
@property (nonatomic, copy) NSString *qimei;
//用户uid > 0
@property (nonatomic, assign) NSInteger user_id;
//业务类型voicesquare
@property (nonatomic, copy) NSString *bid;
//请求版本号
@property (nonatomic, copy) NSString *source;

@end

//语音广场请求录制语音包参数
@interface QMVoiceSquareRecordListServiceReq : QMModel
//用户uid > 0
@property (nonatomic) NSInteger user_id;
//请求容量
@property (nonatomic) NSInteger size;
//请求页码
@property (nonatomic) NSInteger page;
//数据签名
@property (nonatomic) NSString *sign;
//签名来源
@property (nonatomic) NSString *sign_source;
//签名版本
@property (nonatomic) NSInteger sign_version;
//时间戳 秒
@property (nonatomic) NSInteger timestamp;
//请求ID
@property (nonatomic) NSString *req_id;

@end


//语音广场录制语音包状态
@interface QMVoiceSquareRecordPackageStateReq : QMModel

//请求版本号
@property (nonatomic) NSString *voice_id;
//数据签名
@property (nonatomic) NSString *sign;
//签名来源
@property (nonatomic) NSString *sign_source;
//签名版本
@property (nonatomic) NSInteger sign_version;
//时间戳 秒
@property (nonatomic) NSInteger timestamp;
//请求ID
@property (nonatomic) NSString *req_id;

@end


//语音广场录制语音包删除请求
@interface QMVoiceSquareRecordPackageDeleteReq : QMModel

//请求版本号
@property (nonatomic) NSString *voice_id;
//用户uid
@property (nonatomic) NSInteger user_id;
//数据签名
@property (nonatomic) NSString *sign;
//签名来源
@property (nonatomic) NSString *sign_source;
//签名版本
@property (nonatomic) NSInteger sign_version;
//时间戳 秒
@property (nonatomic) NSInteger timestamp;
//请求ID
@property (nonatomic) NSString *req_id;

@end

#pragma mark --Response

@class QMVoiceSquareRecordData;
//语音广场请求录制语音包返回结果
@interface QMVoiceSquareRecordListServiceResp : QMModel

//结果码，成功为0
@property (nonatomic) NSInteger errCode;
//结果信息，成功为success
@property (nonatomic) NSString *errMsg;
//返回数据
@property (nonatomic) QMVoiceSquareRecordData *data;

@end

@class QMVoiceSquareLanguagePackageData;
@interface QMVoiceSquareRecordData : QMModel

//page页码
@property (nonatomic) NSInteger page;
//page页容量
@property (nonatomic) NSInteger size;
//总数
@property (nonatomic) NSInteger count;
//语音列表分组信息
@property (nonatomic, strong) NSArray<QMVoiceSquareLanguagePackageData *> *voice_data;

@end

//语音广场录制语音包状态返回结果
@interface QMVoiceSquareRecordPackageState : QMModel

//结果码，成功为0
@property (nonatomic) NSInteger errCode;
//结果信息，成功为success
@property (nonatomic) NSString *errMsg;
//返回数据
@property (nonatomic) QMVoiceSquareLanguagePackageData *data;

@end

@class QMVoiceSquareData;
//语音广场请求返回结果
@interface QMVoiceSquareListServiceResp : QMModel

//结果码，成功为0
@property (nonatomic, assign) NSInteger code;
//结果信息，成功为success
@property (nonatomic, copy) NSString *msg;
//?
@property (nonatomic, copy) NSString *traceid;
//返回数据
@property (nonatomic, strong) QMVoiceSquareData *data;

@end

@class QMVoiceSquareBannerData;
@class QMVoiceSquareListData;
//返回数据
@interface QMVoiceSquareData : QMModel

//轮播banner数据
@property (nonatomic, strong) NSArray<QMVoiceSquareBannerData *> *banner;
//语音列表分组信息
@property (nonatomic, strong) NSArray<QMVoiceSquareListData *> *square;

@end

//banner结构
@interface QMVoiceSquareBannerData : QMModel

//图片url
@property (nonatomic, copy) NSString *icon_url;
//跳转地址
@property (nonatomic, copy) NSString *link_url;

@end

//列表结构
@interface QMVoiceSquareListData : QMModel

//分组名称
@property (nonatomic, copy) NSString *group_name;
//分组ID
@property (nonatomic, assign) NSInteger group_id;
//分组内语音包信息
@property (nonatomic, strong) NSArray<QMVoiceSquareLanguagePackageData *> *voices;

@end

@class QMVoiceSquareShareData;
//语音包结构
@interface QMVoiceSquareLanguagePackageData : QMModel
//语音包关联主题ID
@property (nonatomic, assign) NSInteger theme_id;
//语音包唯一ID
@property (nonatomic, assign) long long voice_id;
//语音包彩蛋
@property (nonatomic) NSString *voice_eggs;
//任务状态 0:未知 1:制作中 2:制作完成 3:制作失败
@property (nonatomic) NSInteger task_status;
//删除标志 0:未知 1:正常 2:已删除 3:已下架
@property (nonatomic) NSInteger del_flag;
//任务进度
@property (nonatomic) NSInteger progress;
//语音包背景颜色
@property (nonatomic) NSString *voice_base_color;
//语音包语音类型
@property (nonatomic) NSString *voice_base_type;
//语音包风格类型
@property (nonatomic) NSString *voice_style_type;
//前端需要，端上暂时无用
@property (nonatomic) NSString *voice_list;
//录制语音包预估剩余时间
@property (nonatomic) NSInteger production_time;
//用户ID的MD5值
@property (nonatomic) NSString *attribution;
//语音包来源0运营，1录制
@property (nonatomic, assign) NSInteger voice_source;
//包大小
@property (nonatomic, assign) NSInteger voice_size;
//语音包md5
@property (nonatomic, copy) NSString *voice_md5;
//语音包版本
@property (nonatomic, assign) NSInteger voice_version;
//语音包类型，2D ？ 3D
@property (nonatomic, assign) NSInteger voice_type;
//废弃
@property (nonatomic, copy) NSString *city_code;
//未启用，红点标记
@property (nonatomic, assign) NSInteger recommend;
//播放速度？
@property (nonatomic, assign) NSInteger speed;
//优先级，未使用
@property (nonatomic, assign) NSInteger priority;
//下载包是否需要解压
@property (nonatomic, assign) NSInteger special;
//废弃
@property (nonatomic, assign) NSInteger force_show_type;
//废弃
@property (nonatomic, copy) NSString *relate_name_key;
//未启用
@property (nonatomic, copy) NSString *voice_background_image;
//语音包主标题
@property (nonatomic, copy) NSString *title;
//语音包介绍
@property (nonatomic, copy) NSString *introduce;
//语音包头像
@property (nonatomic, copy) NSString *voice_icon;
//试听语音
@property (nonatomic, copy) NSString *audio_test;
//语音包
@property (nonatomic, copy) NSString *audio;
//详情页h5 id
@property (nonatomic, assign) NSInteger detail_id;
//热区跳转链接，仅特殊语音包配置时有效，普通语音包本地拼接链接跳转h5
@property (nonatomic, copy) NSString *hot_link;
//关联主题头图
@property (nonatomic, copy) NSString* theme_path;
//分享内容
@property (nonatomic, strong) QMVoiceSquareShareData *share;
//关联主题tips展示疲劳度开关，关掉时不做控制
@property (nonatomic) BOOL theme_frequency;
/// 灰度强制更新
@property (nonatomic) BOOL gray_force_update;

@end

//分享内容结构
@interface QMVoiceSquareShareData : QMModel
//渠道
@property (nonatomic, copy) NSString *channel;
//h5分享主标题
@property (nonatomic, copy) NSString *h5_title;
//h5分享副标题
@property (nonatomic, copy) NSString *h5_content;
//分享小图
@property (nonatomic, copy) NSString *share_icon;
//端外详情页链接
@property (nonatomic, copy) NSString *detail_url;
//卡片大图
@property (nonatomic, copy) NSString *card_picture;

@end
NS_ASSUME_NONNULL_END
