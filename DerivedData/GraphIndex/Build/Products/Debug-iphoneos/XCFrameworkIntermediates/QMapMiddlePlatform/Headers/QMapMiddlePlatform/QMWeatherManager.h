//
//  QMWeatherManager.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON> on 2018/1/12.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMWeather.h"
#import <QMapRouteSearchKit/QRouteStepNavInfo.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMWeatherManager : NSObject

- (void)cancelAllRequest;


- (QMNetworkRequest *)requestWeatherDataForAlongCitys:(NSArray <QRouteCityInfo *>*)citys
                                            onSuccess:(void (^)(NSArray<QMWeather *> * _Nullable weathers))onSuccessBlock
                                              onError:(void(^)(NSError * _Nullable error))onErrorBlock;

- (QMNetworkRequest *)requestWeatherDataForCitys:(NSArray *)citys
                                       onSuccess:(void (^)(NSArray<QMWeather *> * _Nullable weathers))onSuccessBlock
                                         onError:(void(^)(NSError * _Nullable error))onErrorBlock;

/// 根据经纬度请求天气信息，文档 https://iwiki.woa.com/pages/viewpage.action?pageId=1582401499
/// - Parameters:
///   - location: 经纬度信息
///   - type: 请求的类型，具体对应 QMJCE_MapSSO_WeatherInfoType 中的枚举值
///   - onFinishBlock: 成功的回调
///   - onErrorBlock: 失败的回调
- (void)requestWeatherWithLocation:(CLLocation *)location
                              type:(NSArray <NSNumber *> *)type
                          onFinish:(void(^)(QMJCE_MapSSO_GetUniWeatherInfoResp * _Nullable rsp))onFinishBlock
                           onError:(void(^)(NSError * _Nullable error))onErrorBlock;

/// 取消根据位置的天气请求
- (void)cancelLocationWeatherRequest;

@end

NS_ASSUME_NONNULL_END


