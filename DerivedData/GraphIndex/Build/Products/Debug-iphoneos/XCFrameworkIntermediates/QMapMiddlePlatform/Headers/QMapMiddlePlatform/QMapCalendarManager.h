//
//  QMapCalendarManager.h
//  QMapMiddlePlatform
//
//  Created by admin on 2021/2/7.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMapCalendarDayModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMapCalendarManager : NSObject

QMDefaultManager_H(QMapCalendarManager)

/// 获取日期对应的日历Model
/// @param date 日期
- (QMapCalendarDayModel *)getCalendarDayModelForDate:(NSDate *)date;

/// 从开始日期到结束日期的日期列表
/// @param beginDate 开始日期
/// @param endDate 结束日期
- (NSArray<QMapCalendarDayModel *> *)datesWithBeignDate:(NSDate *)beginDate
                                                endDate:(NSDate *)endDate;

/// 从当前日期起后 天数的 日期列表
/// 包括开始日期所在月, 结束日期所在月
/// @param dayCount 需要有效日期的天数
- (NSArray<QMapCalendarDayModel *> *)datesWithDayCount:(NSInteger)dayCount;

/// 从当前日期起后 天数的 日期列表
/// 包括开始日期所在月, 结束日期所在月
/// @param dayCount 需要有效日期的天数
- (NSArray<QMapCalendarDayModel *> *)datesWithBeginDate:(NSDate *)beginDate
                                          afterDayCount:(NSInteger)dayCount;

/// 从开始日期到结束日期的月列表
/// @param beginDate 开始日期
/// @param endDate 结束日期
- (NSArray<QMapCalendarMonthModel *> *)dateMonthsWithBeignDate:(NSDate *)beginDate
                                                       endDate:(NSDate *)endDate;

/// 从当前日期起后 天数的  月列表
/// 包括开始日期所在月, 结束日期所在月
/// @param dayCount 需要有效日期的天数
- (NSArray<QMapCalendarMonthModel *> *)dateMonthsWithDayCount:(NSInteger)dayCount;

/// 从当前日期起后 天数的 月列表
/// 包括开始日期所在月, 结束日期所在月
/// @param dayCount 需要有效日期的天数
- (NSArray<QMapCalendarMonthModel *> *)dateMonthsWithBeginDate:(NSDate *)beginDate
                                                 afterDayCount:(NSInteger)dayCount;

@end

NS_ASSUME_NONNULL_END
