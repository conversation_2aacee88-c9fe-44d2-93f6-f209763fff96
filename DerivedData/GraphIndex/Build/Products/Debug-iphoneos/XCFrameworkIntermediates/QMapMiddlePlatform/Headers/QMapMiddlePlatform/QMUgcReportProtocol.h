//
//  QMUgcReportProtocol.h
//  SOSOMap
//
//  Created by <PERSON>on<PERSON><PERSON> on 2018/1/29.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportRecordData.h"
 
#import "QMUgcReportEventData.h"
#import "QMThumbUpsReq.h"

/*
 用户数据
 */
@interface QMUgcReportUserInfo : NSObject
@property (nonatomic,strong) NSString* userId;
@property (nonatomic,assign) BOOL isLogin;
@property (nonatomic,strong) NSString* nickName;
@property (nonatomic,strong) NSString* qqNum;
- (void)getUserInfo;
@end

/*
 导航数据
 struct navigationInfo {
 0 optional navType ntype ;          //场景
 1 optional string routeId;          //routeId
 2 optional string yawRouteId;       //偏航routeId
 3 optional vector<double> coors;    //路线点串
 4 optional Point start;             //当前routeId起点经纬度
 5 optional Point end;               //当前routeId终点经纬度
 6 optional int speed;               //当前车速
 };
 */
@interface QMUgcReportNavigationInfo : NSObject
@property (nonatomic,assign) QMUgcNavType navType;
@property (nonatomic,strong) NSString* routeId;
@property (nonatomic,strong) NSString* yawRouteId;
@property (nonatomic,assign) CLLocationCoordinate2D startPoint;
@property (nonatomic,assign) CLLocationCoordinate2D endPoint;
@property (nonatomic,assign) int speed;
@property (nonatomic) NSString *mFileName; //放到extInfo 轨迹文件名称
@end

/*
 事件详细信息
 struct oriReportInfo {
 0 require int eventType;            //事件类型
 1 optional string eventTag;         //事件标签
 2 optional string eventDetail;      //事件详情
 3 optional vector<string> picUrl;   //照片url列表
 4 optional string eventDesc;        //问题描述
 };
 */
@interface QMUgcReportOriEventInfo : NSObject
@property (nonatomic,assign) NSInteger eventType;
@property (nonatomic,strong) NSArray* eventTags;
@property (nonatomic,strong) NSString* eventDetail;
@property (nonatomic,strong) NSArray* picUrls;
@property (nonatomic,strong) NSString* eventDesc;
@property (nonatomic,strong) NSString* infoCode; //上报开通用来指定原情报ID
@property (nonatomic,strong) NSString* address;
@end

/*
 struct POIInfo {
 1 optional string name;                 //地点名称
 2 optional bool bMerchant;              //是否是商户
 3 optional string openingTime;          //营业时间
 4 optional vector<string> url;          //营业执照
 5 optional string tel;                  //地点电话
 6 optional string sUid;
 7 optional string province;             //省
 8 optional string city;                 //市
 9 optional string district;             //区
 10 optional string area_code;           //区划码
 11 optional string category;            //分类
 12 optional string category_code;       //分类码
 };
 */
@interface QMUgcReportPoiInfo : NSObject
@property (nonatomic,copy) NSString* sUid;
@property (nonatomic,copy) NSString* poiName;
@property (nonatomic,copy) NSString* poiAddr;
@property (nonatomic,assign) BOOL bMerchant;
@property (nonatomic,strong) NSArray* licencePicUrls;
@property (nonatomic,copy) NSString* openTime;
@property (nonatomic,copy) NSString* poiTel;
@property (nonatomic,copy) NSString* province;
@property (nonatomic,copy) NSString* city;
@property (nonatomic,copy) NSString* district;
@property (nonatomic,copy) NSString* area_code;
@property (nonatomic,copy) NSString* category;
@property (nonatomic,copy) NSString* category_code;
@property (nonatomic,copy) NSString* coType;
@end

/*
// 用户事件客户端上报接口
struct CSIntelligenceReportRep {
    0 require Point selectPoint;        //选点位置
    1 require Point llng;               //定位位置
    2 require string cityName;          //城市名
    3 require oriReportInfo event;      //事件信息
    4 require userInfo uInfo;           //用户信息
    5 require entranceType fromresource;//上报入口
    7 optional navigationInfo nav;      //导航信息
};
*/

@interface QMUgcReportEventReq : NSObject
@property (nonatomic,assign) CLLocationCoordinate2D locationPoint;
@property (nonatomic,assign) CLLocationCoordinate2D pickerPoint;
@property (nonatomic,strong) NSString* cityName;
@property (nonatomic,assign) BOOL useGps;
@property (nonatomic,strong) QMUgcReportOriEventInfo* eventInfo;
@property (nonatomic,strong) QMUgcReportPoiInfo* poiInfo;
@property (nonatomic,assign) NSInteger fromSource;
@property (nonatomic,copy)   NSString* contact;
@property (nonatomic,assign) BOOL isInNav;
@property (nonatomic,assign) QMUgcNavType navType;
@property (nonatomic,strong) QMUgcReportNavigationInfo* navInfo;
@property (nonatomic) NSString *issueTag;

- (NSDictionary<NSString *, NSString *> *)getReportExtInfo;

@end

@class QMUgcReportProtocol;

@protocol QMUgcReportProtocolDelegate <NSObject>
@optional
- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol
         didReportCompmlete:(NSInteger)error
                   originId:(NSString*)oriId;

- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol
          didGetMarkerLists:(NSArray*)markerList
                 serverTime:(NSString*)time;

- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol
             didGetMyRecords:(NSArray*)recordList
                       error:(NSError*)error
                countRequest:(NSUInteger)count
                       total:(NSUInteger)totalNum
                  serverTime:(long)time;

- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol
        didGetMyHeadRecords:(NSArray*)recordList
                      error:(NSError*)error;

- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol
      readRecordDidComplete:(int)error;

- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol
         didGetThumbupsInfo:(int)error
                   infoCode:(NSString*)infoCode
                     useful:(NSInteger)usefullCount
                    useless:(NSInteger) uselessCount;

//上报获取存在不存在数据 替换老接口方式 qmUgcReportProtocol
- (void)qmUgcReportProtocol:(QMUgcReportProtocol*)ugcReportProtocol resDict:(NSDictionary *)dict;

- (void)qmUgcThumUpWitDict:(NSDictionary *)dict type:(QMUGCReportClickType)type;


@end

@interface QMUgcReportProtocol : NSObject

@property (nonatomic, assign) BOOL isReportEventing ;//是否正在进行上报，防止多次点击
@property (nonatomic,weak) id<QMUgcReportProtocolDelegate> delegate;

-(void) cancelRequest;

/*
 事件上报
 */
-(void) reportEvent:(QMUgcReportEventReq *)reportRequest;


/*
 拉取显示marker列表
 */
-(void) getTrafficMarkerList:(DMMapRect)bounds mapLevel:(int)level;

////首页、导航中点事件上报接口替换
//- (void)addUsefulNum:(NSString*)infoCode
//            originId:(NSString *)originId
//                succ:(void(^)(NSDictionary *dict))succ
//                fail:(void(^)(NSError *err))fail;
//
////首页、导航中点事件上报接口替换
//- (void)addUselessNum:(NSString*)infoCode
//             originId:(NSString *)originId
//                 succ:(void(^)(NSDictionary *dict))succ
//                 fail:(void(^)(NSError *err))fail;

//点事件点击上报接口，支持扩展字段
- (void)thumbUpsReq:(QMThumbUpsReq *)req;

//点事件点击上报接口，支持扩展字段
- (void)thumbUpsReq:(QMThumbUpsReq *)req
               succ:(void(^)(NSDictionary *dict))succ
               fail:(void(^)(NSError *err))fail;
/*
 拉取我的上报记录
 */
-(void) getMyRecords:(NSArray*)pulledIds time:(long)timeStamp;


/*
 拉取第一页记录，用于红点展示，临时marker处理等
 */
-(void) getMyHeadRecords;

/*
 拉取已用无用数量 替换 getThumbUpsInfo
 */
- (void)getThumbUpsInfo:(NSString*)infoCode
               originId:(NSString *)originId
                    lat:(double)lat
                    lon:(double)lon
                   succ:(void(^)(NSDictionary *dict))succ
                   fail:(void(^)(NSError *err))fail;


/*
 已读状态上报
 */
-(void) reportRecordsRead:(NSArray*) recordOriginIds;


/*
 拉取已用无用数量
 */
-(void) getThumbUpsInfo:(NSString*) infoCode;

/**
 获取点事情的详情，
 @param eventId: 点事件的id
 @param succ:   成功的回调, QMJCE_trafficmarker_markerInfo
 @param fail:   失败的回调
 */
+ (void)getTrafficMarkerDetail:(NSString *)eventId
                          succ:(void(^)(id data))succ
                          fail:(void(^)(NSError *err))fail;

@end
