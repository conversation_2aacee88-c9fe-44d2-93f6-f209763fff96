//
//  QMTruckInfoClickSegmentControl.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/29.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^TruckInfoClickSegmentSelectCallback)(NSInteger index);


@interface QMTruckInfoClickSegmentControl : UIView

@property (nonatomic, readonly) BOOL selected;
@property (nonatomic) NSInteger selectedIndex;
@property (nonatomic) TruckInfoClickSegmentSelectCallback selectCallback;

- (QMButton *)createButtonWithButtonTitle:(NSString *)title;

- (instancetype)initWithItems:(NSArray<NSString *> *)items space:(CGFloat)space title:(NSString *)title;

- (void)hideRedDot:(BOOL)hidden;

@end

NS_ASSUME_NONNULL_END
