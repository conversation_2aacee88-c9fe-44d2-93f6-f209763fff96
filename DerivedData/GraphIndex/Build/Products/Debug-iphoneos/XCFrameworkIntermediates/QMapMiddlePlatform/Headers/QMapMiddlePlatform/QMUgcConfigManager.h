//
//  QMUgcConfigManager.h
//  SOSOMap
//
//  Created by leonlia<PERSON> on 2018/2/6.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportTypeData.h"
#import <QMapMiddlePlatform/QMCarRouteNaviCommon.h>

@interface QMUgcConfigManager : NSObject

QMDefaultManager_H(QMUgcConfigManager)

- (NSArray*)getConfigReportTypeList:(QMUgcEntryViewFromSource)fromSource;
- (NSArray*)getConfigReportTypeList:(QMUgcEntryViewFromSource)fromSource routeNaviType:(QMCarRouteNaviType)routeNaviType;
- (NSArray *)getNaviSecondaryReportConfig;

@end
