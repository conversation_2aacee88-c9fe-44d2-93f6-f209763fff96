//
//  QMUgcReasonReportView.h
//  QMapMiddlePlatform
//
//  Created by 江航 on 2022/4/18.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMRouteEventVerifyInfo.h"
#import <MapBaseOCModel/RGModelInterface.h>

NS_ASSUME_NONNULL_BEGIN

//view delegate
@protocol QMUgcReasonReportViewDelegate <NSObject>
@optional
//选项点击
- (void)didClickBtnWithReasonItem:(QMEventVerifyReasonItem *)reasonItem eventInfo:(RGTrafficEventInfo *)eventInfo;
//关闭按钮点击
- (void)didClickReasonCloseBtn;
//计时结束
- (void)handleReasonReportViewCountDown;
@end

//原因类型验证弹窗
@interface QMUgcReasonReportView : UIView
//delegate
@property (nonatomic, weak) id<QMUgcReasonReportViewDelegate> delegate;
//data
@property (nonatomic, strong) QMEventVerifyReasonInfo *reasonInfo;
//点事件信息
@property (nonatomic, strong) RGTrafficEventInfo *eventInfo;
//初始化
- (instancetype)initWithEventReasonInfo:(QMEventVerifyReasonInfo *)reasonInfo isNight:(BOOL)isNight;

@end

NS_ASSUME_NONNULL_END
