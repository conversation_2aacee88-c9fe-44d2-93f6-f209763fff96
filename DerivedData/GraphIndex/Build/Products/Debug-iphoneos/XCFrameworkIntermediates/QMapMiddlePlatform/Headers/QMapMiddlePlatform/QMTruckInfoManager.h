//
//  QMTruckInfoManager.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/24.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapFoundation/QMCS.h>
#import "QMTruckPlateModel.h"
#import "QMTruckModel.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMTruckPlateChangeNotify;
extern NSString *const QMTruckPlateChangeOps;
extern NSString *const QMTruckPlateChangeOpsAdd;
extern NSString *const QMTruckPlateChangeOpsUpdate;
extern NSString *const QMTruckPlateChangeOpsRemove;

typedef NS_ENUM(NSInteger, QMTruckInfoFromSource) {
    QMTruckInfoFromSourceOther = 0,
    QMTruckInfoFromSourceRouteSearch
};



@interface QMTruckInfoManager : NSObject  <QMCSExtServiceProtocol>

QMDefaultManager_H(QMTruckInfoManager);

// 多方案页临时微调信息
@property (nonatomic, strong, nullable) QMTruckModel *tempInfo;


- (QMTruckPlateModel *)mainTruckPlateModel;

- (NSArray<QMTruckPlateModel *> *)truckPlates;

- (BOOL)canAddTruckPlate;

- (BOOL)hasSameTruckPlate:(NSString *)carNumber;

- (void)addTruckPlateModel:(QMTruckPlateModel *)model needNotify:(BOOL)flag;

- (void)updateTruckPlateModels:(NSArray<QMTruckPlateModel *> *)model needNotify:(BOOL)flag;

- (void)removeTruckPlateModel:(QMTruckPlateModel *)model needNotify:(BOOL)flag;

- (void)changeTruckPlateModelIntoSelected:(QMTruckPlateModel *)plateModel;

- (NSUInteger)indexOfTruckPlateModel:(QMTruckPlateModel *)model;

- (NSInteger)replaceTruckPlateModel:(QMTruckPlateModel *)model
                 newTruckPlateModel:(QMTruckPlateModel *)newModel
                         needNotify:(BOOL)flag;

- (void)localSaveTruckPlateModels:(NSArray<QMTruckPlateModel *> *)model needNotify:(BOOL)flag;

- (void)updateToServer;

- (void)showTruckLimitSettingVCFromSource:(QMTruckInfoFromSource)fromSource complete:(void(^ _Nullable)(BOOL))settingComplete;

@end

NS_ASSUME_NONNULL_END
