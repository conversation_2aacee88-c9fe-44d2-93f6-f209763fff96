//
//  QMTopologySceneSubscriber.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/5.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMTopologySceneActor.h"

NS_ASSUME_NONNULL_BEGIN

/// 场景监听者，本身也是参与者
@interface QMTopologySceneSubscriber : QMTopologySceneActor

/**
 当前的覆盖比例，0～1之间
 @discussion 如果存在多个覆盖者，以最大覆盖面积的为准
 */
@property (nonatomic, assign) CGFloat coverRatio;

/**
 当前覆盖者
 @discussion 如果存在多个覆盖者，以最大覆盖面积的为准
 */
@property (nonatomic, weak) QMTopologySceneActor *coverActor;

@property (nonatomic, weak) id<QMTopologySceneSubscriberProtocol> subscriber;

/**
 场景显示变化时，需要通知曝光状态，但这个时候有可能观察者是不可见的，因此需要等待可见了再去发送通知
 */
@property (nonatomic, assign) BOOL sceneAppearChangedToNotify;

@end

NS_ASSUME_NONNULL_END
