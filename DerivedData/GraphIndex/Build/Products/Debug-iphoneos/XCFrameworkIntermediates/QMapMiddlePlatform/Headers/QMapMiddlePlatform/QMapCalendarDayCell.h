//
//  QMapCalendarDayCell.h
//  QMapMiddlePlatform
//
//  Created by admin on 2021/2/19.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class QMapCalendarDayModel;

typedef NS_ENUM(NSInteger, QMapCalendarDayCellSelectedType) {
    QMapCalendarDayCellSelectedTypeUnselected = 0,
    // 日期选择选择样式
    QMapCalendarDayCellSelectedTypeSelected,
    // 时间范围选择样式
    QMapCalendarDayCellSelectedTypeSelectedBegin,
    QMapCalendarDayCellSelectedTypeSelectedBetween,
    QMapCalendarDayCellSelectedTypeSelectedEnd,
};

@protocol QMapCalendarDayCellDelegate <NSObject>
@required
- (QMapCalendarDayCellSelectedType)calendarDayCellSelectedTypeWithDay:(QMapCalendarDayModel *)dayModel;
@end
@interface QMapCalendarDayCell : UICollectionViewCell
@property (nonatomic, weak) id<QMapCalendarDayCellDelegate> delegate;
- (void)updateCalendarDay:(QMapCalendarDayModel *)dayModel;
@end

NS_ASSUME_NONNULL_END
