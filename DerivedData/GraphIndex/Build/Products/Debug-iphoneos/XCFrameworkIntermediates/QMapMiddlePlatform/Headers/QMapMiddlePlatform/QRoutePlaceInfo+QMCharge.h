//
//  QRoutePlaceInfo+QMCharge.h
//  QMapMiddlePlatform
//
//  Created by mt<PERSON><PERSON> on 2023/12/6.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import <QMapRouteSearchKit/QMapRouteSearchKit.h>
NS_ASSUME_NONNULL_BEGIN

@interface QRoutePlaceInfo (QMCharge)
/// 距离起点的距离
@property (nonatomic) NSInteger disFromOrigin;
@end

NS_ASSUME_NONNULL_END
