//
//  QMTipExplainProcessorProtocol.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/25.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMRouteExplainContext.h"


NS_ASSUME_NONNULL_BEGIN

@protocol QMTipExplainProcessorProtocol <NSObject>

@required

- (id <QMTipExplainProcessorProtocol>)initWithContext:(QMRouteExplainContext *)context;

- (void)showTipsView;

@optional


/// 删除tipsview 包含 tips 和消息盒子的tips
- (void)removeTipsView;

- (void)changeRoute;
/// 显示
- (void)showMultTips;

/// 隐藏
- (void)hideMultTips;

- (BOOL)isMsgBoxShow;

- (void)closeCarTipDetailPage;

- (void)removeCarTipsView;

- (void)updateLimitRules:(NSArray *)rules;

- (BOOL)isTipMarkerId:(NSString *)markerId;

- (void)updateTipsView;

- (void)forceRemoveCarTipsView;




@end

NS_ASSUME_NONNULL_END
