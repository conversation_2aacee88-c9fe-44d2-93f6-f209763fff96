//
//  QMapRichTextInfoViewBuilder.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/8.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DMGroupMarkerInfo+hippy.h"
#import "TMMapRichTextInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMapRichTextInfoViewBuilder : NSObject

/**
 使用对应的TMMapRichTextInfo对象生成UILabel视图对象，如果富文本没有设置有效的色值或字体，默认字体采用12号系统字体，色值为666666灰色
 
 @param textInfo 富文本TMMapRichTextInfo对象
 @return 对应的呈现UILabel对象
 */
+ (UILabel *)formatLabelWithTMMapRichTextInfo:(TMMapRichTextInfo *)textInfo;

/**
 根据TMMapRichTextSegment对象构建一个富文本信息对象
 
 @param segment 对应的参数对象
 @return 如果数据有效返回属性字符串，否则返回nil
 */
+ (NSAttributedString *)attributeStringWithRichTextSegment:(TMMapRichTextSegment *)segment;

/**
 输入原始的富文本数据生成一个透明背景的，包含了转换过后的label及icon的视图对象

 @param textInfoArray 原始富文本数据
 @return 如果数据有效则返回生成的视图对象，如果数据无效则返回nil
 */
+ (UIView *)contentViewWithTextInfoArray:(NSArray<TMMapRichTextInfo *> *)textInfoArray;

/**
 输入原始的富文本数据生成一个指定背景包含了实景图的视图对象

 @param textInfoArray 原始富文本数据
 @return 如果数据有效则返回生成的视图对象，如果数据无效则返回nil
 */
+ (UIView *)contentRealMapViewWithTextInfoArray:(NSArray<TMMapRichTextInfo *> *)textInfoArray;

/**
 输入原始的富文本数据生成一个指定背景包含了实景图的视图对象

 @param textInfoArray 原始富文本数据
- @return 如果数据有效则返回生成的视图对象，如果数据无效则返回nil
+ @param complete view对象回调
 */
+ (void)contentNMapViewWithInfoArray:(NSArray<TMMapRichTextInfo *> *)textInfoArray complete:(RichTextInfoBlock)complete;

@end

NS_ASSUME_NONNULL_END
