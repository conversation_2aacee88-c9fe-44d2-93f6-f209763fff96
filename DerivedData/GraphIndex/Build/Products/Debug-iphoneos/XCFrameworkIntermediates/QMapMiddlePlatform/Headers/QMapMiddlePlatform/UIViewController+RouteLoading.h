//
//  UIViewController+RouteLoading.h
//  SOSOMap
//
//  Created by 33 on 23/07/2018.
//  Copyright © 2018 Tencent. All rights reserved.
// 
#import <QMapMiddlePlatform/QMLoadingView.h>

#ifndef UIViewController_RouteLoading_h
#define UIViewController_RouteLoading_h

@interface UIViewController(RouteLoading)
@property (nonatomic, strong) QMLoadingView *loadingView;
@property (nonatomic, strong) QMLoadingView *exceptionView;
@property (nonatomic, assign) CGFloat loadingExceptionViewHeight;
@property (nonatomic, assign) QMLoadingStyle loadingStyle;
@property (nonatomic, strong) NSString *loadingImageName;

- (void)showLoading;

- (void)hideLoadingView;
- (void)showRetryReason:(NSString *)reason retryBlock:(void (^)(void)) block;
- (void)showSingleTipsReason:(NSString *)reason;
- (void)showDoubleTipsReason:(NSString *)reason;
- (void)showTipsReason:(NSString *)reason prompt:(NSString *)prompt;
@end
#endif /* UIViewController_RouteLoading_h */
