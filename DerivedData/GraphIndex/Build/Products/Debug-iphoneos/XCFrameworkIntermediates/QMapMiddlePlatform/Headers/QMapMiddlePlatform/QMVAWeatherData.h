//
//  QMVAWeatherData.h
//  SOSOMap
//
//  Created by leon<PERSON><PERSON> on 2018/7/14.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, QMVAWeatherDataType) {
    QMVAWeatherDataTypeDayWeather,
    QMVAWeatherDataTypeDayCurrentWeather,
};

@interface QMVAWeatherData : NSObject
@property (nonatomic,assign) QMVAWeatherDataType type;
@property (nonatomic,copy) NSString* sCityName;
@property (nonatomic,copy) NSString* sDistictName;
@property (nonatomic,copy) NSString* sCurrentT;
@property (nonatomic,copy) NSString* sMaxT;
@property (nonatomic,copy) NSString* sMinT;
@property (nonatomic,copy) NSString *sWeek;
@property (nonatomic,copy) NSString *sAQIDes;
@property (nonatomic,copy) NSString *sAQI;
@property (nonatomic,assign) NSUInteger sAQILevel;
@property (nonatomic,copy) NSString *sDweather;
@property (nonatomic,assign) NSUInteger sWeatherIndex;
@property (nonatomic,strong) UIColor *AQColor;
@property (nonatomic,copy) NSString *sWindPower;
@property (nonatomic,copy) NSString *sPm25;
@property (nonatomic,copy) NSString *sTim;
+ (NSDictionary *)cityWeatherInfoWithData:(NSDictionary *)data;
+ (BOOL)isCityWeatherInfoDataValid:(NSDictionary*)content forDate:(NSString*)dateStr;
+ (QMVAWeatherData*) createDayWeatherData:(NSDictionary*)data;
+ (QMVAWeatherData*) createCurrentWeatherData:(NSDictionary*)data;
+ (UIColor*) colorForAQLevel:(NSInteger)aqLevel;
@end
