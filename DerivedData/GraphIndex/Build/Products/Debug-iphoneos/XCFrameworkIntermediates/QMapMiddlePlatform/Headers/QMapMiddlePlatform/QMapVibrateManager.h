//
//  QMapVibrateManager.h
//  SOSOMap
//
//  Created by admin on 2020/7/31.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMapVibrateManager : NSObject

+ (QMapVibrateManager *)shareInstance;

- (BOOL)canVibrate;

- (void)vibrate;

- (void)heavy;

- (void)medium;

- (void)light;

- (void)selection;

- (void)success;

- (void)warning;

- (void)error;

- (void)playVibrate:(NSUInteger)count;

@end

NS_ASSUME_NONNULL_END
