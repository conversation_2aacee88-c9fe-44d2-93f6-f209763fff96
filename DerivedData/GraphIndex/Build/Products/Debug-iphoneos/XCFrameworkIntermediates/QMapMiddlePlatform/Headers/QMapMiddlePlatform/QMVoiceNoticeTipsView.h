//
//  QMVoiceNoticeTips.h
//  SOSOMap
//  Created by foo<PERSON><PERSON><PERSON>(王中周) on 2018/9/3.
//  Modify by <PERSON><PERSON>iu(刘澈) on 2020/06/20.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^ViewHideCompleteBlock)(void);

@protocol QMViewModelProtocol <NSObject>
@property (nonatomic,strong,readonly) id view;
- (instancetype)initWithView:(UIView *)view;
+(Class)viewClass;

@end

@protocol QMViewProtocol <NSObject>
@property (nonatomic,weak,readonly)id<QMViewModelProtocol>viewModel;
+(Class)viewModelClass;
@end


@class QMVoiceNoticeTipsView;

@interface QMVoiceNoticeTipsViewModel: NSObject <QMViewModelProtocol>
@property (nonatomic,copy,  readonly) NSString *tips;
@property (nonatomic,strong,readonly) QMVoiceNoticeTipsView <QMViewProtocol> *view;


- (BOOL)viewCanShow;
- (void)hide:(ViewHideCompleteBlock)block;

@end

@interface QMVoiceNoticeTipsView : UIView<QMViewProtocol>
@property (nonatomic,weak,readonly) QMVoiceNoticeTipsViewModel<QMViewModelProtocol> *viewModel;
- (UILabel *)tipsLabel;
- (void)updateTitle:(NSString *)title;
@end

typedef void(^feelingTipsDismissBlk)(void);

@interface QMFeelingVoiceTipsView : QMVoiceNoticeTipsView

- (void)setText:(NSString *)text
       showTime:(NSInteger)showTime
   dismissBlock:(feelingTipsDismissBlk)dismissBlock;

@end
