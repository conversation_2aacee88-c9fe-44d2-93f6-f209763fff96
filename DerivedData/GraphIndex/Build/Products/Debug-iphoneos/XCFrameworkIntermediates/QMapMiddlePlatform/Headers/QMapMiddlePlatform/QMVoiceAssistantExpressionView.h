//
//  QMVoiceAssistantExpressionView.h
//  SOSOMap
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/11/18.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMDingDangAnimationDefine.h"

NS_ASSUME_NONNULL_BEGIN

@class QMVoiceAssistantExpressionView;

@protocol QMVoiceAssistantExpressionViewDelegate <NSObject>

- (void)voiceAssistantExpressionViewDidClicked:(QMVoiceAssistantExpressionView *)view;

@end

@interface QMVoiceAssistantExpressionView : UIView

@property (nonatomic, assign, readonly) QMVoiceAssistantExpressionType expressionType;
@property (nonatomic, weak, nullable) id<QMVoiceAssistantExpressionViewDelegate> delegate;

/// 生成叮当形象（不支持禁用态）
/// @param source 形象来源
- (instancetype)initWithPanelSource:(QMVoiceAssistantExpressionSource)source;

/// 生成叮当形象，自动启动expressionType的动画（不支持禁用态）
/// @param source 形象来源
/// @param expressionType 动画类型
- (instancetype)initWithPanelSource:(QMVoiceAssistantExpressionSource)source
              initialExpressionType:(QMVoiceAssistantExpressionType)expressionType;

/// 生成叮当形象（支持禁用态）
/// @param source 形象来源
/// @param disableType 禁用态类型
- (instancetype)initWithPanelSource:(QMVoiceAssistantExpressionSource)source
                        disableType:(QMVoiceAssistantExpressionType)disableType;

- (void)startAnimationOfType:(QMVoiceAssistantExpressionType)expressionType;

/// !!释放对象前，需要主动调用该方法
- (void)stopAllAnimations;

/// alpha从0到1
/// @param animated 是否使用动画
- (void)showWithAnimated:(BOOL)animated;

/// alpha从1到0
/// @param animated 是否使用动画
- (void)hideWithAnimated:(BOOL)animated;

/// 根据是否可用的状态修改叮当图标
- (void)updateFaceViewImgIsDisable:(BOOL)disable;
@end

NS_ASSUME_NONNULL_END
