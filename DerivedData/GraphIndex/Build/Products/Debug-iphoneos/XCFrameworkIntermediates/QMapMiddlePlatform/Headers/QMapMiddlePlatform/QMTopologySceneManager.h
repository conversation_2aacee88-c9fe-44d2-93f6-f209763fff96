//
//  QMTopologySceneManager.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/5.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMTopologySceneProtocol.h"
#import "UIView+TopologyScene.h"
#import "UIViewController+TopologyScene.h"

NS_ASSUME_NONNULL_BEGIN

/**
 场景拓扑管理器，主要用来处理视图之间相互遮挡的消息通知，比如曝光埋点会用到
 ！！！只能在主线程调用
 */
@interface QMTopologySceneManager : NSObject

//@property (nonatomic, readonly, class) BOOL isHotLaunch;
//
//@property (nonatomic, readonly, class) QMTopologySceneState sceneState;
//
//+ (QMTopologySceneState)sceneStateForScene:(NSString *)sceneName;

#pragma mark - 场景注册
+ (void)registScene:(id<QMTopologySceneProtocol>)scene;

#pragma mark - 场景变化
+ (void)sceneInit:(id<QMTopologySceneProtocol>)scene;

+ (void)sceneAppear:(id<QMTopologySceneProtocol>)scene;

+ (void)sceneDisappear:(id<QMTopologySceneProtocol>)scene;

+ (void)sceneDestroy:(id<QMTopologySceneProtocol>)scene;

#pragma mark - 参与者
+ (void)actorAppear:(id<QMTopologySceneActorProtocol>)actor inScene:(nullable NSString *)sceneName;

/// 参与者布局变化了，场景需要进行计算
/// - Parameter actor: 参与者
+ (void)actorUpdateLayout:(id<QMTopologySceneActorProtocol>)actor inScene:(nullable NSString *)sceneName;

+ (void)actorDismiss:(id<QMTopologySceneActorProtocol>)actor inScene:(nullable NSString *)sceneName;

#pragma mark - 监听者
/// 添加监听，返回当前覆盖度
/// - Parameters:
///   - subscriber: 监听者
///   - sceneName: 场景
+ (CGFloat)addSubscriber:(id<QMTopologySceneSubscriberProtocol>)subscriber inScene:(nullable NSString *)sceneName;

/// 订阅者布局变化了，场景需要进行计算
/// - Parameter actor: 参与者
+ (void)subscriberUpdateLayout:(id<QMTopologySceneSubscriberProtocol>)subscriber inScene:(nullable NSString *)sceneName;

+ (void)removeSubscriber:(id<QMTopologySceneSubscriberProtocol>)subscriber inScene:(nullable NSString *)sceneName;

@end

NS_ASSUME_NONNULL_END
