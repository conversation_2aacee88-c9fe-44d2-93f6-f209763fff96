//
//  QMUImageCache.h
//  QMapMiddlePlatform
//
//  Created by wyh on 2021/10/22.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUFileComposition.h"

NS_ASSUME_NONNULL_BEGIN

/**
 Find file cache from disk, step like this:
 
 ┌──────────┐           ┌─────────┐           ┌──────────────┐
 │  <PERSON>tie  │ ───────── │   PAG   │ ───────── │  SDWebImage  │
 └──────────┘           └─────────┘           └──────────────┘
 */
@interface QMUImageCache : NSObject

+ (instancetype)sharedCache;

/**
 Query image cache in QMUCache
 
 @param file file composition
 @param doneClosure done complete callback (imageData will be NSString if lottie or PAG, otherwise will be UIImage if PNG)
 */
- (void)queryCache:(QMUFileComposition *)file done:(void(^)(BOOL isExist, id _Nullable imageData, QMUImageViewType type))doneClosure;

@end

NS_ASSUME_NONNULL_END
