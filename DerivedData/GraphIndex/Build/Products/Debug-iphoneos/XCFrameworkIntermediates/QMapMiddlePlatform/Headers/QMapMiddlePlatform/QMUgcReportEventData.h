//
//  QMUgcReportEventData.h
//  SOSOMap
//
//  Created by <PERSON>on<PERSON><PERSON> on 2018/1/18.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportTypeData.h"
#import "QMUgcReportMarkerData.h"

typedef NS_ENUM(NSInteger, QMUGCReportClickType) {
    QMUGCReportClickTypeNone, //未点击操作
    QMUGCReportClickTypeExit, //点击存在
    QMUGCReportClickTypeUnExit,//点击不存在
    QMUGCReportClickTypeCancelExit, //取消点击存在
    QMUGCReportClickTypeCancelUnExit,//取消点击不存在
    
};

static NSString * const QMNaviUgcReportCompleteStatus = @"QMNaviUgcReportCompleteStatus";
@interface QMUgcReportEventData : QMUgcReportMarkerData

@property (nonatomic, readonly) DMMapPoint dmMapPoint;
@property (nonatomic, assign) BOOL isTempMarker;
@property (nonatomic, assign) BOOL hasThumbUp;
@property (nonatomic, assign) BOOL hasThumbDown;
@property (nonatomic, assign) long displayTime; // 是否展示开通封闭时间 0:不展示 1:展示
@property (nonatomic, assign) QMUGCReportClickType clickType;
@property (nonatomic, assign) CLLocationCoordinate2D coordinate;

@end
