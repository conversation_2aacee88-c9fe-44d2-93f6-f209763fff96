//
//  QMTruckPlateModel.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/24.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import "QMNCarLicensePlateModel.h"

NS_ASSUME_NONNULL_BEGIN

// 车牌颜色
typedef NS_ENUM(NSUInteger, QMTruckPlateColor) {
    QMTruckPlateColorInvalid = 0,
    QMTruckPlateColorBlue,
    QMTruckPlateColorYellow,
    QMTruckPlateColorBlack,
    QMTruckPlateColorWhite,
    QMTruckPlateColorGreen,
    QMTruckPlateColorYellowGreen
};

// 货车类型
typedef NS_ENUM(NSUInteger, QMTruckType) {
    QMTruckTypeInvalid = 0,
    QMTruckTypeMini,      // 微型
    QMTruckTypeLight,     // 轻型
    QMTruckTypeMedium,    // 中型
    QMTruckTypeHeavy,     // 重型
};

// 货车拖挂类型
typedef NS_ENUM(NSUInteger, QMTruckTrailerType) {
    QMTruckTrailerTypeInvalid = 0,
    QMTruckTrailerTypeFlatbed,        // 平板
    QMTruckTrailerTypeContainer,      // 箱货
    QMTruckTrailerTypeSemiTrailer,    // 半挂
    QMTruckTrailerTypeFull,           // 全挂
};

// 排放标准
typedef NS_ENUM(NSUInteger, QMTruckEmissionStandard) {
    QMTruckEmissionStandardInvalid = 0,
    QMTruckEmissionStandardI,           // 国一
    QMTruckEmissionStandardII,          // 国二
    QMTruckEmissionStandardIII,         // 国三
    QMTruckEmissionStandardIV,          // 国四
    QMTruckEmissionStandardV,           // 国五
    QMTruckEmissionStandardVI,          // 国六
};



@interface QMTruckPlateModel : QMNCarLicensePlateModel

/// 长
@property (nonatomic, assign) double length;
/// 宽
@property (nonatomic, assign) double width;
/// 高
@property (nonatomic, assign) double height;
/// 核定载重
@property (nonatomic, assign) double load;
/// 车辆总重
@property (nonatomic, assign) double totalLoad;
/// 轴数
@property (nonatomic, assign) double axCnt;
/// 拖挂类型
@property (nonatomic, assign) QMTruckTrailerType trailerType;
/// 车牌颜色
@property (nonatomic, assign) QMTruckPlateColor plateColor;
/// 货车类型
@property (nonatomic, assign) QMTruckType type;
/// 排放标准
@property (nonatomic, assign) QMTruckEmissionStandard gasEmiss;

- (NSString *)typeStr;




@end

NS_ASSUME_NONNULL_END
