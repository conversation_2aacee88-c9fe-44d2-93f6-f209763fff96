//
//  QMEventReportUIProtocol.h
//  SOSOMap
//
//  Created by leon<PERSON><PERSON> on 2018/1/23.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportRecordData.h"
#import "QMUgcQuickReportView.h"
#import "QMAutoDismissTipsView.h"

typedef void(^QMUgcCosUrlResult)(NSString *cosUrl);
@class QMUgcQuickReportView;
@protocol QMUgcReportUIDelegate <NSObject>

@optional
-(void) gotoUgcReportPage:(QMUgcReportType)typeData;
-(void) gotoUgcRecordPage;
-(void) showUgcQuickReportView:(QMUgcQuickReportView*)view;
-(void) showUgcReportSucTips;
-(void) ugcReportFail;
-(void) showEventTipsView:(NSString *)tips dismissTime:(CGFloat)dismissTime;
-(void) updateNoResponseCnt;
-(void) updateWhenReportViewDismiss;
- (void)gotoUgcReportPageWithConfigDict:(NSDictionary *)configDict forType:(NSInteger)type;
- (void) closeUgcReportVc:(BOOL)needScreenShot;
- (void)didClickCarNavReportType:(QMUgcReportType)type isOther:(BOOL)isOther;
- (void)privacyConfimViewOnshow:(void (^)(void))block;
- (void)closeNavOtherModel;
- (void)reportNavInfo:(NSDictionary *)query;
- (void)getImageOnCosResult:(QMUgcCosUrlResult)result;
- (void)uploadLogsWithFeedback;
- (void)showFeedBackTips;
@end

