//
//  QMWordSegment.h
//  SOSOMap
//
//  Created by foogrywang(王中周) on 2019/1/7.
//  Copyright © 2019年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

//#define DingDangWordSegment // 是否使用叮当切词方案


@interface QMWordSegment : NSObject

/**
 将 string 切词，同样将 strings 里的每一个字符串也切词，用前者的切词结果跟后者数组里的字符串切词
 结果做匹配。如果能匹配上，返回被匹配上的字符串在数组中的 index；匹配不上则返回 NSNotFound。

 @param string 要匹配的字符串
 @param strings 被匹配的字符串集合
 @return strings 中能和 string 匹配的字符串在 strings 中的 index
 */
+ (NSUInteger)indexOfString:(NSString *)string matchWithStrings:(NSArray<NSString *> *)strings;

@end


@class JsonPOIInfo;
@class QMPointInfo;
@interface QMWordSegment (JsonPOIInfo)

/**
 pois 中能和 query 匹配的 JsonPOIInfo；没有能匹配的则返回 nil
 */
+ (JsonPOIInfo *)poiMatchQuery:(NSString *)query withPOIs:(NSArray<JsonPOIInfo *> *)pois;

@end


@class QMPointInfo;
@interface QMWordSegment (QMPointInfo)

/**
 points 中能和 query 匹配的 QMPointInfo；没有能匹配的则返回 nil
 */
+ (QMPointInfo *)pointMatchQuery:(NSString *)query withPoints:(NSArray<QMPointInfo *> *)points;

@end
