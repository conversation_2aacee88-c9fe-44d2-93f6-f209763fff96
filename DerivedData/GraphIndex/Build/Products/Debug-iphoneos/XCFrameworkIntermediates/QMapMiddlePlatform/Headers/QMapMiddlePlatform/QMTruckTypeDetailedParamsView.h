//
//  QMTruckTypeDetailedParamsView.h
//  QMapMiddlePlatform
//
//  Created by t<PERSON><PERSON><PERSON> on 2023/11/29.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMTruckWeightVolumeModifyView.h"
#import "QMTruckModelFactory.h"

typedef void(^QMTruckTypeDetailedParamsBlock)(void);


NS_ASSUME_NONNULL_BEGIN

@interface QMTruckTypeDetailedParamsView : UIView

@property (nonatomic) QMTruckWeightVolumeModifyViewScene scene;
@property (nonatomic) CGFloat axleCount;
@property (nonatomic) QMTruckTypeDetailedParamsBlock valueChangedBlock;

- (instancetype)initWithScene:(QMTruckWeightVolumeModifyViewScene)scene truckType:(QMTruckType)truckType;

- (void)initTruckModel:(QMTruckModel *)truckModel axleCount:(CGFloat)axleCount;

- (void)setTruckModel:(QMTruckModel *)truckModel;

- (QMTruckModel *)currentTruckModel;

- (BOOL)isValid;

@end

NS_ASSUME_NONNULL_END
