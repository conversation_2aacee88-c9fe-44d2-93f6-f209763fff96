//
//  FavoriteRouteSegment.h
//  QQMap
//
//  Created by <PERSON><PERSON><PERSON> on 4/22/11.
//  Copyright 2011 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMapRouteSearchExtend.h>

typedef enum TipsType_ { TIPSTYPE_NONE = 0, TIPSTYPE_BRIDGE, TIPSTYPE_PASSGATEWAY, TIPSTYPE_CROSS, TIPSTYPE_STEPPED } TipsType;

@interface RouteSegment : QRouteStep {
}

@property (nonatomic, copy) NSString *roadName;
@property (nonatomic, strong) NSArray *roadNames;
@property (nonatomic, copy) NSString *textInfo;
@property (nonatomic) int coorStart;

@property (nonatomic, assign) int formId; //道路形状
@property (nonatomic) int funcclass; //道路FC
@property (nonatomic) int gradeId; //道路行政等级

@end
