//
//  QMTopologySceneProtocol.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/5.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "QMTopologySceneDefine.h"

NS_ASSUME_NONNULL_BEGIN

/// 场景协议
@protocol QMTopologySceneProtocol <NSObject>

@required
- (NSString *)topologySceneName;

@optional
- (UIViewController *)topologySceneController;

- (UIView *)topologySceneView;

@end

/// 参与者协议，告知场景当前状态
@protocol QMTopologySceneActorProtocol <NSObject>

@optional
/// actor视图
- (UIView *)topologySceneActorView;

/// 在场景下的Frame
- (CGRect)topologySceneActorFrame;

/// 是否是全屏，避免计算逻辑
- (BOOL)topologySceneActorIsFullScreen;

/// 层级模式默认是QMTopologySceneLevelAlert
- (QMTopologySceneLevel)topologySceneActorLevel;

@end

/// 订阅者协议，告知场景当前状态
@protocol QMTopologySceneSubscriberProtocol <QMTopologySceneActorProtocol>

@optional
/// 场景状态变化了
/// - Parameters:
///   - sceneName: 场景名字
///   - state: 当前状态
- (void)topologyScene:(NSString *)sceneName
    sceneStateChanged:(QMTopologySceneState)sceneState
     sceneAppearState:(QMTopologySceneAppearState)sceneAppearState;

/// 场景的显示状态发生变化，注意，首次监听也会返回一次回调
/// - Parameters:
///   - sceneName: 场景名字
///   - sceneAppearState: 场景显示状态
///   - coverRatio: 监听者当前覆盖率
///   - sceneAppearState: 场景显示状态
- (void)topologyScene:(NSString *)sceneName sceneAppearStateChanged:(QMTopologySceneAppearState)sceneAppearState coverRatio:(CGFloat)coverRatio;

/// 订阅者的覆盖关系发生变化
/// - Parameters:
///   - sceneName: 场景名字
///   - coverRatio: 覆盖比例，0～1之间的值
///   - sceneAppearState: 场景显示状态
- (void)topologyScene:(NSString *)sceneName 
    coverRatioChanged:(CGFloat)coverRatio
     sceneAppearState:(QMTopologySceneAppearState)sceneAppearState;

/// 订阅者是否视图层级在参与者下面，主要在同级场景（QMTopologySceneLevel相同）中使用
/// - Parameters:
///   - sceneName: 场景名
///   - subscriber: 订阅者
///   - actor: 参与者
- (BOOL)topologyScene:(NSString *)sceneName 
           subscriber:(id<QMTopologySceneSubscriberProtocol>)subscriber
           belowActor:(id<QMTopologySceneActorProtocol>)actor;

#pragma mark - 曝光逻辑
/// 观察者曝光，与topologyScene:isAppearForCover:配合使用
/// - Parameters:
///   - sceneName: 场景名
///   - sceneAppearState: 曝光时机
///   - coverRatio: 监听者当前覆盖率
- (void)topologyScene:(NSString *)sceneName 
     subscriberAppear:(QMTopologySceneAppearState)sceneAppearState
           coverRatio:(CGFloat)coverRatio;

/// 观察者覆盖度是否达到曝光条件，与topologyScene:subscriberAppear:配合使用
/// - Parameters:
///   - sceneName: 场景名
///   - coverRatio: 覆盖度
- (BOOL)topologyScene:(NSString *)sceneName isAppearForCover:(CGFloat)coverRatio;

@end

NS_ASSUME_NONNULL_END
