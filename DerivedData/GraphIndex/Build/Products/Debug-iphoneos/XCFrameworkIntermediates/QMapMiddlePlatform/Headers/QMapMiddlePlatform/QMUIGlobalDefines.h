//
//  QMUIGlobalDefines.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/15.
//  Copyright © 2018年 Tencent. All rights reserved.
//
 

#import <QMapMiddlePlatform/QMDataProvider.h>

#ifndef QMUIGlobalDefines_h
#define QMUIGlobalDefines_h

//自8.0视觉规范开始

/// 高亮文字按钮颜色 蓝
#define  QMBlueHighlightBtnColor UIColor.tmd_blue_2 //QMColorHex(0x427cff)

/// 全局背景颜色
#define  QMGrayBackgroundDefaultColor  [UIColor qdm_backgroundColor_default]

/// 全局前景颜色
#define  QMGrayFrontDefaultColor  [UIColor qdm_textColor_default]

///  设置页高亮文字按钮颜色 蓝
#define  QMBlueBtnColor UIColor.tmd_blue_2

///  一种灰色背景颜色
#define  QMGrayBackgroundPaleColor [UIColor qdm_backgroundColor_default]

/// 驾车导航夜间模式背景颜色 深蓝
#define  QMDarkBlueBackgroundColor QMColorRGB(42, 49, 61)

/// 驾车导航夜间模式分割线 面板背景颜色 偏黑
#define  QMDarkBlackBackgroundColor QMColorRGBA(27, 31, 38, 0.98)

///  设置夜间页高亮文字按钮颜色 亮蓝
#define  QMLightBlueBtnColor QMColorRGB(51, 166, 255)

/// 文字颜色 黑色
#define  QMTextBlackColor UIColor.qdm_textColor_blackColor // [UIColor qm_colorFromHexadecimal:@"#E6000000"]

/// 文字颜色 深黑色
#define  QMTextDeepBlackColor  QMColorHex(0x111111)

/// 文字颜色 深灰色
#define  QMTextDarkGrayColor  UIColor.tmd_font_secondary
/// 文字颜色 中灰
#define  QMTextMidGrayColor  UIColor.qdm_textColor_0x777777 //QMColorHex(0x777777)
/// 文字颜色 灰色
#define  QMTextGrayColor  QMColorHex(0x888888)
/// 文字颜色 浅灰色
#define  QMTextLightGrayColor  QMColorHex(0xAAAAAA)

/// 分割线颜色 全局
#define  QMGraySeperatorDefaultColor UIColor.qdm_seperatorLineColor //QMColorHex(0xEEEEEE)
/// 分割线颜色 路线TitleBar 底部灰线
#define  QMGraySeperatorBottomColor  QMColorHex(0xcccccc)

/// 分割线高度
#define  QMGraySeperatorHeight (0.5)

/// 水平距离左侧的GAP
#define  QMTableViewHorizontalMarginGap (20)
#define  QMLeftMargin (QMTableViewHorizontalMarginGap)

/// 输入框颜色
#define  QMGrayTextFieldColor  QMColorHex(0xefefef)

/// 蒙层颜色
#define  QMMaskGrayColor  [QMColorHex(0x000000) colorWithAlphaComponent:0.4f]

/// POI 分类颜色 橘黄
#define  QMOrangePOICategoryColor  QMColorHex(0xFF7733)
/// POI 分类颜色 紫色
#define  QMPurplePOICategoryColor  QMColorHex(0x7E7EED)
/// POI 分类颜色 绿色
#define  QMGreenPOICategoryColor  QMColorHex(0x32A67F)
#define  QMRedPOICategoryColor  QMColorHex(0xF24E4E)
#define  QMBluePOICategoryColor  QMColorHex(0x5A8AF2)
#define  QMPinkPOICategoryColor  QMColorHex(0xED588A)
#define  QMYellowPOICategoryColor  QMColorHex(0xFAA700)
#define  QMBlueDarkPOICategoryColor  QMColorHex(0x6C7CA7)
/// POI 按钮描边 灰色
#define  QMGrayButtonBorderColor QMColorHex(0xCCCCCC)

///  设置页高亮文字按钮颜色 蓝
#define  QMRedBtnColor QMColorRGB(255, 82, 82)

#define  QMHexColorPileGray QMColorHex(0xF7F8F9)
#define  QMHexColorAzure QMColorHex(0x0090ff)
#define  QMHexColorAzure10 QMColorHexA(0x0090FF, 0.1)
#define  QMHexColorBrownGrey QMColorHex(0x999999)
#define  QMHexColorWhite QMColorHex(0xFFFFFF)

/// 字体
#define QMFontSystemSize(_x_)           ([UIFont systemFontOfSize:(_x_)])
#define QMMediumFontOfSize(_x_)         ([UIFont systemFontOfSize:(_x_) weight:UIFontWeightMedium])

#define QMTitleBarTextFont              QMFontSystemSize(20)
#define QMMainTextFont                  QMFontSystemSize(16)
#define QM_MAINTEXT_MEDIUMFONT          QMMediumFontOfSize(14)

#define QMButtonLargeTitleMediumFont    QMMediumFontOfSize(18)
#define QMLabelMediumFont               QMMediumFontOfSize(14)
 
#define QM_DETAIL_TEXTFONT              QMFontSystemSize(13)
#define QM_SUGDEATAI_TEXTFONT           QMFontSystemSize(12)
#define QMButtonTitleTextFont           QMFontSystemSize(14)
#define QMMainEditTextMediumFont        QMMediumFontOfSize(15)


/// 区块分割
#define QMSectionSepratorHeight  (6)

/// 圆角大小
#define QMPictureLayerCornerRadius  (4)   //图片圆角
#define QMNormalLayerCornerRadius   (4)   //控件圆角
#define QMListLayerCornerRadius     (8)   //列表圆角
#define QMTagLayerCornerRadius      (6)     //  919年轻化 标签圆角
#define QMButtonLayerCornerRadius   (12)    //  919年轻化 按钮、服务区圆角
#define QMNaviCardLayerCornerRadius (16)    //  10.8导航横屏卡片圆角
#define QMCardLayerCornerRadius     (20)    //  919年轻化 页面卡片圆角
#define QMDrawerLayerCornerRadius   (28)    //  919年轻化 抽屉圆角
#define QMDrawerLayerInNaviLandscapeCornerRadius   (16)    //  919年轻化 抽屉圆角


/// 阴影
#define QMShadowOffset              CGSizeMake(0, 1)        //阴影位移
#define QMShadowOpacity             (0.1)                       //阴影透明度
#define QMShadowColor               QMColorHex(0x000000)    //阴影颜色
#define QMShadowRadius              (8)
#define QMSepLineHeight             (1/[UIScreen mainScreen].scale) //分割线高度


///switch 缩放
#define QMSwitchScaleTransform      CGAffineTransformMakeScale(0.863, 0.863)

#define kNavigationBarHeight4Custom (48.0f)
#define kNavigationBarHeight4Default (44.0f)

///image
#define QMImageNamed(_x_) ([QMBullShitResourcesBundle imageNamed:(_x_)])

///以 375x667 设计图为基准, 自动转换尺寸
#define QM_SV(_x_)   ((_x_) * [[QMDataProvider sharedInstance] screenScale])


#define QMCenterSpot    @"・"

#endif /* QMUIGlobalDefines_h */
