//
//  QMViewController+QMDetectSimilarity.h
//  QMapMiddlePlatform
//
//  Created by co<PERSON><PERSON> on 2023/2/23.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import "QMViewController.h"

NS_ASSUME_NONNULL_BEGIN

typedef double QMViewControllerSimilarity;
 
@interface QMViewControllerGetSimilarity : NSObject

- (void)setImgWithImgA:(UIImage *)imgA imgB:(UIImage *)imgB;//设置需要对比的图片
- (void)setImgAWidthImg:(UIImage *)img;
- (void)setImgBWidthImg:(UIImage *)img;
- (QMViewControllerSimilarity)getSimilarityValue; //获取相似度
+ (QMViewControllerSimilarity)getSimilarityValueWithImgA:(UIImage *)imga imgB:(UIImage *)imgb;//类方法

@end

@interface QMViewController (QMDetectSimilarity)

- (BOOL)whiteSrceenDetection;

- (void)whiteScreenDectectAsync:(void (^)(BOOL))result;

@end

NS_ASSUME_NONNULL_END
