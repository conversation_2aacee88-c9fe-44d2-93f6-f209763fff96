//
//  QMWebViewDefine.h
//  TwoWebView
//
//  Created by paul<PERSON><PERSON> on 2019/5/21.
//  Copyright © 2019 paulineli. All rights reserved.
//

#ifndef QMWebViewDefine_h
#define QMWebViewDefine_h

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, QMWebViewNavigationType) {
    QMWebViewNavigationTypeLinkClicked,
    QMWebViewNavigationTypeFormSubmitted,
    QMWebViewNavigationTypeBackForward,
    QMWebViewNavigationTypeReload,
    QMWebViewNavigationTypeFormResubmitted,
    QMWebViewNavigationTypeOther = -1,
};

@protocol QMWebViewProtocol;
typedef UIView<QMWebViewProtocol> QMWebView;

typedef void (^ _Nullable QMWebViewFailBlock)(QMWebView * _Nullable webView, NSError * _Nullable error);
typedef void (^ _Nullable QMWebViewSuccessBlock)(QMWebView * _Nullable webView, _Nullable id ret);

@protocol QMWebViewDelegate;
@protocol QMWebViewProtocol <NSObject>


/**
 网页地址
 */
@property (nonatomic, readonly, strong, nullable) NSString *qmMainFrameURLString;

/**
 网页标题
 */
@property (nonatomic, readonly, copy, nullable) NSString *qmMainFrameTitle;

/**
 出现错误页面的时真正的访问地址
 */
@property (nonatomic, copy, readonly, nullable) NSURL *realRequestURL;

@property (nullable, nonatomic, readonly) NSURL *URL;

/**
 加载离线h5时候，用于全局上报的页面名称
 */
@property (nonatomic, readonly, nullable) NSString *h5OffLinePageName;

/**
 加载进度。WKWebView表示正常的加载进度
 */
@property (nonatomic, assign) double estimatedProgressShown;

//@property (nonatomic, assign) BOOL progressEnded;

/**
 正在加载中？
 */
@property (nonatomic, readonly, getter=isLoading) BOOL loading;

@property (nonatomic, readonly, strong, nullable) UIScrollView *scrollView;

@property (nonatomic, weak, nullable) id<QMWebViewDelegate> qmWebViewDelegate;

/**
 加载网址

 @param request 网址请求
 */
- (nullable id)loadRequest: (nullable NSURLRequest *) request;

- (BOOL)canGoBack;

- (BOOL)canGoForward;

- (nullable id)goBack;

- (nullable id)goForward;

- (nullable id)reload;

- (void)stopLoading;

/**
 WebView执行javascript
 WKWebView执行异步js

 @param javaScriptString js语句
 @param successHandler 执行成功后回调
 @param failHandler 执行失败后回调
 */
- (void)evaluateJavaScript:(nullable NSString *)javaScriptString
            successHandler:(QMWebViewSuccessBlock)successHandler
               failHandler:(QMWebViewFailBlock)failHandler;

/**
 WebView执行javascript
 WKWebView执行异步js

 @param javaScriptString js语句
 */
- (void)evaluateJavaScript:(nullable NSString *)javaScriptString;

/**
 网页截屏

 @return 截屏图片
 */
- (UIImage *_Nullable)webviewSnapshotImage;
@end

@class QMWebHistoryItem;

@protocol QMWebViewDelegate <NSObject>

@optional


/**
 是否允许加载request
 
 @param webView 回调的webview
 @param request 请求
 @param navigationType 加载类型
 @param isMainFrame 是否是main frame
 @return 是否继续加载
 */
- (BOOL)qmWebView:(nullable id<QMWebViewProtocol>)webView
shouldStartLoadWithRequest:(nullable NSURLRequest *)request
   navigationType:(QMWebViewNavigationType)navigationType
      isMainFrame:(BOOL)isMainFrame;

/**
 webView开始load
 @param webView webView description
 */
- (void)qmWebViewDidStartLoading:(nullable id<QMWebViewProtocol>)webView;

/**
 webView加载结束
 WKWebView中等价于- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation
 @param webView 回调的webview
 */
- (void)qmWebViewDidLoadFinished:(nullable id<QMWebViewProtocol>)webView;

/**
 mainframe加载出错
 @param webView 回调的webview
 @param error eror
 @param isMainFrameError 是否是mainframe error
 */
- (void)qmWebView:(nullable id<QMWebViewProtocol>)webView
didFailNavigationWithError:(nullable NSError *)error
 isMainFrameError:(BOOL)isMainFrameError;

/**
 接收到网页的title

 @param webView 回调的webview
 @param title 标题
 */
- (void)qmWebView:(nullable id<QMWebViewProtocol>)webView didReceiveTitle:(nullable NSString *)title;

/**
 网页加载进度发生变化

 @param webView 回调的webview
 @param progressValue 进度信息
 */
- (void)qmWebView:(nullable id<QMWebViewProtocol>)webView didReceiveProgress:(CGFloat)progressValue;

/**
 对应webview的pointInside:withEvent:方法

 @param webview 当前webview
 @param point 触控点位置
 @param event 触控事件
 @param superValue [super pointInside:withEvent]的值
 @return 返回webview是否包含point
 */
- (BOOL)qmWebView:(nullable id<QMWebViewProtocol>)webview
      pointInside:(CGPoint)point
        withEvent:(nullable UIEvent *)event
       superValue:(BOOL)superValue;

/**
 收到response时决定请求是否继续进行，只有WKWebView支持次此接口

 @param webView 回调的webview
 @param response 网络响应
 @param isMainFrame 是否是main frame
 @return 是否继续加载
 */
- (BOOL)qmWebView:(nullable id<QMWebViewProtocol>)webView
shouldContinueWithResponse:(nullable NSURLResponse *)response
       isMainFrame:(BOOL)isMainFrame;

/**
 WKWebView通知浏览器新建窗口，此接口暂不使用。页面内跳转都在当前网页加载

 @param webView 回调的webview
 @param configuration webview配置
 @param request 请求信息
 @param navigationType 加载类型
 @param isMainFrame 是否是main frame
 @return 根据参数定义的新建的webview
 */
- (nullable id<QMWebViewProtocol>)qmWebView:(nullable id<QMWebViewProtocol>)webView
       createWebViewWithConfiguration:(nullable id)configuration
                          withRequest:(nullable NSURLRequest *)request
                       navigationType:(QMWebViewNavigationType)navigationType
                          isMainFrame:(BOOL)isMainFrame;

/**
 页面地址发生改变
 @param webView 回调的webview
 @param url 当前页面地址
 */
- (void)qmWebView:(nullable id<QMWebViewProtocol>)webView didChangeLocationWithinPage:(nullable NSURL *)url;

/**
 以下多个ScrollView相关方法，都为webview内部scrollview对应的delegate

 @param scrollView webview中的scrollView
 */
- (void)qmWebScrollViewDidScroll:(nullable UIScrollView *)scrollView;
- (void)qmWebScrollViewWillBeginDragging:(nullable UIScrollView *)scrollView;
- (void)qmWebScrollViewWillEndDragging:(nullable UIScrollView *)scrollView
                          withVelocity:(CGPoint)velocity
                   targetContentOffset:(inout CGPoint * __nullable)targetContentOffset;
- (void)qmWebScrollViewDidEndDragging:(nullable UIScrollView *)scrollView willDecelerate:(BOOL)decelerate;
- (void)qmWebScrollViewDidEndDecelerating:(nullable UIScrollView *)scrollView;
- (void)qmWebScrollViewDidZoom:(nullable UIScrollView *)scrollView;
- (void)qmWebScrollViewDidEndZooming:(nullable UIScrollView *)scrollView withView:(nullable UIView *)view2 atScale:(float)scale;


@end
#endif /* QMWebViewDefine_h */
