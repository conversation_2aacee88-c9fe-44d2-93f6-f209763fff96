//
//  QMVoiceAssistantPanelViewManager.h
//  SOSOMap
//
//  Created by foo<PERSON><PERSON><PERSON>(王中周) on 2019/11/29.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMVoiceAssistantPanelViewDelegate.h"
#import "QMVoiceAssistantPanelSubViewDelegate.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMVoiceAssistantPanelViewWillShowNotification;
extern NSString *const QMVoiceAssistantPanelViewWillHideNotification;
extern NSString *const QMVoiceAssistantPanelViewDidHideNotification;

@interface QMVoiceAssistantPanelViewManager : NSObject

@property (nonatomic) BOOL isAIChatVoicePanelShowing;
@property (nonatomic) BOOL isSecondRound;
@property (nonatomic) NSString *source;


- (void)addDelegate:(id<QMVoiceAssistantPanelViewDelegate>)delegate;
- (void)removeDelegate:(id<QMVoiceAssistantPanelViewDelegate>)delegate;

- (void)showWithContent:(nullable id)content state:(QMVoiceAssistantPanelViewState)state;
- (void)hideWithAnimation:(BOOL)animation delegate:(nullable id <QMVoiceAssistantPanelViewDelegate>)delegate;

- (BOOL)isBigPanelViewShowing;
- (BOOL)isPanelViewShowing;
- (void)updateContent:(nullable id)content withState:(QMVoiceAssistantPanelViewState)state;
- (void)updateScene:(QMVoiceAssistantPanelViewScene)scene;

- (void)startTimer;
- (void)stopTimer;


@end

NS_ASSUME_NONNULL_END
