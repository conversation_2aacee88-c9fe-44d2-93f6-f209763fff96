//
//  QMUFileComposition.h
//  QMapMiddlePlatform
//
//  Created by wyh on 2021/10/22.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUImageInterfaces.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMUFileComposition : NSObject

/**
 Now only support NSString (path or URL)
 */
@property (nonatomic, strong, readonly) id fileData;

/**
 Placeholder image
 */
@property (nonatomic, strong, readonly) UIImage *placeholderImage;

/**
 source input type, local path or URL (zip/PAG/PNG)
 */
@property (nonatomic, assign, readonly) QMUSrcFileType srcType;

/**
 Whether is a URL link
 */
@property (nonatomic, assign, readonly) BOOL isURLComposition;

/**
 Whether auto play animation, default is YES
 */
@property (nonatomic, assign) BOOL autoPlay;

/**
 File cache key, the name will be automatically generated if you don’t specify it
 */
@property (nonatomic, copy) NSString *name;

+ (QMUFileComposition *)filePath:(NSString *)filePath;

+ (QMUFileComposition *)fileURL:(NSString *)fileURL;

+ (QMUFileComposition *)fileURL:(NSString *)fileURL placeholder:(nullable UIImage *)placeholder;

@end

@interface QMUFileParser : NSObject

+ (QMUImageViewType)parseFromDestFilePath:(NSString *)destFilePath;

+ (NSString *)findLottieJSONPathInDirectory:(NSString *)directory;

+ (NSString *)findPAGPathInDirectory:(NSString *)directory;

+ (NSString *)findPNGPathInDirectory:(NSString *)directory;

@end

NS_ASSUME_NONNULL_END
