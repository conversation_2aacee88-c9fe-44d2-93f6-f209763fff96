//
//  QMWidgetAddTutorialView.h
//  QMapBusiness
//
//  Created by sandy<PERSON> on 2024/12/18.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, QMWidgetTipsFromType) {
    QMWidgetTipsFromCommute, //通勤设置页场景
    QMWidgetTipsFromOften,  //oftenpoi=常去地点设置场景
    QMWidgetTipsFromHomeToast,  //首页卡片的设置家和公司的toast场景
    QMWidgetTipsFromOhter,  //其他的页面来源
};

@interface QMWidgetAddTutorialView : UIView

+ (void)showInViewFromType:(QMWidgetTipsFromType)fromType;
+ (void)showInViewFromType:(QMWidgetTipsFromType)fromType
                     scene:(nullable NSString *)scene;
+ (void)closeGuide;
@end

@interface QMWidgetAddManager : NSObject
QMDefaultManager_H(QMWidgetAddManager);
// 设置是否需要查询状态的标识
- (void)setNeedCheckStatus:(BOOL)needCheckStatus;

// 设置当前场景
- (void)setCurrentScene:(NSString *)currentScene;

// 通知hippy取消添加
- (void)notiHippyCancelAddWidgetInScene:(NSString *)scene;
@end

@interface QMWidgetFirstPageGuideInfo : NSObject
@property (nonatomic) NSString *cardType;
@property (nonatomic) NSString *tip;
@property (nonatomic) NSString *lightImgUrl;
@property (nonatomic) NSString *darkImgUrl;
@end

@interface QMWidgetSceneConfig : NSObject
@property (nonatomic) NSString *title;
@property (nonatomic) NSString *scene;
@property (nonatomic) NSString *cardType;
@property (nonatomic) BOOL onlyLight;
@property (nonatomic) QMWidgetFirstPageGuideInfo *info;
@end

@interface QMWidgetGuideConfig : NSObject
@property (nonatomic) NSArray<NSString *> *commonTips;
@property (nonatomic) NSArray<NSString *> *commonDarkImgUrl;
@property (nonatomic) NSArray<NSString *> *commonLightImgUrl;
@property (nonatomic) NSArray<QMWidgetSceneConfig *> *sceneConfis;
@property (nonatomic) NSArray<QMWidgetFirstPageGuideInfo *> *firstPageInfos;
@end
NS_ASSUME_NONNULL_END
