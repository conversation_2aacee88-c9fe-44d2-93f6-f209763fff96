//
//  UIImage+Addition.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/30.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIImage (Addition)

///UIImage 转 Data, 限制不大于多少 byte, 如: 限制于图片小于 5Kb, maxLength = 5 * 1024
- (NSData *)dataWithMaxLength:(NSUInteger)maxLength;

/// 在此图片之上绘制其他图片
- (UIImage *)qm_combineOtherImage:(UIImage *)otherImage rect:(CGRect)frame;

/// 根据文字生成途经点图片
/// @param title 文字，如果是个非空的字符串，生成正常的图片，否则返回 nil，通常情况下，应该为 (经；1；2) 之类的很短的文字
+ (nullable UIImage *)qm_createRouteViaPointImageWithString:(NSString *)title;

/**
 将图片根据期望的尺寸重新绘制
 @param originImage 原始图片对象
 @param width 期望的宽度 单位:dp
 @param height 期望的高度 单位:dp
 @return UIImage* 尺寸变化后的图片对象
 */
+ (UIImage *)resizeImageWithOriginImage:(UIImage *)originImage width:(CGFloat)width height:(CGFloat)height;

@end

NS_ASSUME_NONNULL_END
