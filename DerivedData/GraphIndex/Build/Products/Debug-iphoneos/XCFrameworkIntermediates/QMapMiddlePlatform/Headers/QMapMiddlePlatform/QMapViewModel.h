//
//  QMapViewModel.h
//  SOSOMap
//
//  Created by el<PERSON> on 2020/7/9.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 ViewModel 数据流协议，使用字典便于扩展
 */

@protocol QMapViewModelProtocol <NSObject>
- (void)setContext:(NSDictionary<NSString *, id> *)context;
@end


/**
ViewModel 数据流 传递 ，使用字典便于扩展
*/
@interface QMapViewModel : NSObject <QMapViewModelProtocol>
@property (nonatomic, strong) NSDictionary *context; //弱持有上下文对象
@end

NS_ASSUME_NONNULL_END
