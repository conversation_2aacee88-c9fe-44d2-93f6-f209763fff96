//
//  QMTruckUtils.h
//  QMapMiddlePlatform
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/5.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import "QMTruckModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMTruckUtils : NSObject

// 根据当前用户货车信息更新货车算路参数
+ (void)updateTruckParamByTruckInfo:(QTruckRouteReqParam *)truckParam;
+ (void)updateSearchParamWithTruckModel:(QMTruckModel *)model truckParam:(QTruckRouteReqParam *)truckParam;
+ (void)updateSearchParamWithTruckPlate:(QMTruckPlateModel *)plate truckParam:(QTruckRouteReqParam *)truckParam;

// 基于临时参数更改算路参数
+ (void)updateSearchParamWithTempInfo:(nullable QMTruckModel *)tempInfo truckParam:(QTruckRouteReqParam *)truckParam;

@end

NS_ASSUME_NONNULL_END
