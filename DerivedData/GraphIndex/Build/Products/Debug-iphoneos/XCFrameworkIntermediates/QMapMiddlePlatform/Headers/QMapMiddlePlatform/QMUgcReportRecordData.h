//
//  QMUgcReportRecordData.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/30.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMUgcReportTypeData.h"

//情报状态, 1有效,2失效,3审核中,4无效
typedef NS_ENUM(NSUInteger, QMUgcReportRecordStatus) {
    QMUgcReportRecordStatusValid = 1,
    QMUgcReportRecordStatusInValid,
    QMUgcReportRecordStatusVerifying,
    QMUgcReportRecordStatusOutValid,
};

@interface QMUgcReportRecordData : NSObject
@property(nonatomic,strong) NSString* originId;
@property(nonatomic,assign) long reportTime;
@property(nonatomic,assign) long updateTime;
@property(nonatomic,assign) NSUInteger usefulNum;
@property(nonatomic,assign) BOOL isRead;
@property(nonatomic,assign) BOOL isLocalRead; //用来控制点击消除红点
@property(nonatomic,assign) QMUgcReportType type;
@property(nonatomic,assign) QMUgcReportRecordStatus status;
@property(nonatomic,strong) NSArray* tags;
@property(nonatomic,strong) NSString* detail;
@property(nonatomic,strong) NSArray* imgUrls;
@property(nonatomic,strong) NSString* recordDesc;
@property(nonatomic,strong) NSString* address;
@property(nonatomic,strong) NSString* detailReson;
@property(nonatomic,assign) BOOL detailResonExpand;
+ (NSString*)nameOfStatus:(QMUgcReportRecordStatus)type ofReportType:(QMUgcReportType)reportType;

@end
