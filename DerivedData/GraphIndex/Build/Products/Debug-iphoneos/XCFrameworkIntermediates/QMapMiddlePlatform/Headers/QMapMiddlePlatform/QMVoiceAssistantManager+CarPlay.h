//
//  QMVoiceAssistantManager+CarPlay.h
//  SOSOMap
//
//  Created by foo<PERSON><PERSON><PERSON>(王中周) on 2018/11/9.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import "QMVoiceAssistantManager.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const kCarplayConnectStateChangedNotification;
extern NSString *const kVoiceErrorCPRouteNotification;
extern NSString *const kVoiceSemanticErrorCPRouteNotification;

@interface QMVoiceAssistantManager (CarPlay)

- (void)startCarPlayInputVoice;
- (void)stopCarPlayInputVoice;
- (BOOL)isCarPlayConnected;

// Carplay展示收音页面
- (void)showCarPlayVoiceInput;

// Carplay退出收音页面
- (void)exitCarPlayVoiceInput;

@end

NS_ASSUME_NONNULL_END
