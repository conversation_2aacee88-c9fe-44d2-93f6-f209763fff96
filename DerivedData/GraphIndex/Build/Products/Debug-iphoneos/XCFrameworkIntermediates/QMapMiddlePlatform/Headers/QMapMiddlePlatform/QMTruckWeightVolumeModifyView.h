//
//  QMTruckWeightVolumeModifyView.h
//  QMapMiddlePlatform
//
//  Created by t<PERSON><PERSON><PERSON> on 2023/11/29.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMTruckWeightVolumeIntervalInfo.h>

NS_ASSUME_NONNULL_BEGIN

// 参数改变回调
typedef void(^QMTruckWeightVolumeValueChangedBlock)(QMTruckWeightVolumeViewType viewType, CGFloat value);

// view 在什么场景展示
typedef NS_ENUM(NSUInteger, QMTruckWeightVolumeModifyViewScene) {
    QMTruckWeightVolumeModifyViewSceneRouteSearch,
    QMTruckWeightVolumeModifyViewSceneCarlimit,
};

@interface QMTruckWeightVolumeModifyView : UIView

@property (nonatomic) QMTruckWeightVolumeIntervalInfo *intervalInfo;

@property (nonatomic) QMTruckWeightVolumeValueChangedBlock valueChangedBlock;

@property (nonatomic) QMTruckWeightVolumeViewType viewType;

- (instancetype)initWithIntervalInfo:(QMTruckWeightVolumeIntervalInfo *)intervalInfo scene:(QMTruckWeightVolumeModifyViewScene)scene;

- (void)setSliderValue:(CGFloat)value;

- (BOOL)isWarningLabelShowing;


@end

NS_ASSUME_NONNULL_END
