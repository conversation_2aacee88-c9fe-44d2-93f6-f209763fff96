//
//  QMUgcExistReportView.h
//  QMapMiddlePlatform
//
//  Created by 江航 on 2022/4/24.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <MapBaseOCModel/RGModelInterface.h>
#import "QMRouteEventVerifyInfo.h"

NS_ASSUME_NONNULL_BEGIN

//view delegate
@protocol QMUgcExistReportViewDelegate <NSObject>
@optional
//选项点击
- (void)didClickBtnWith:(BOOL)exist eventInfo:(RGTrafficEventInfo *)eventInfo;
//关闭按钮点击
- (void)didClickExistCloseBtn;
//计时结束
- (void)handleExistReportViewCountDown;
@end

//存在类型众验卡片
@interface QMUgcExistReportView : UIView
//delegate
@property (nonatomic, weak) id<QMUgcExistReportViewDelegate> delegate;
//点事件信息
@property (nonatomic, strong) RGTrafficEventInfo *eventInfo;
//初始化
- (instancetype)initWithComfirmInfo:(QMEventVerifyConfirmInfo *)confirm isNight:(BOOL)isNight;
@end

NS_ASSUME_NONNULL_END
