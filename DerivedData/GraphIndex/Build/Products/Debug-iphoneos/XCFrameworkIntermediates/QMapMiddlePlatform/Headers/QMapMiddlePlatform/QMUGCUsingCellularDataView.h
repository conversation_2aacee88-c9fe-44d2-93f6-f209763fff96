//
//  QMUGCUsingCellularDataView.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2021/3/9.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol QMUGCUsingCellularDataViewDelegate <NSObject>

/// 7日不在提醒按钮点击
- (void)onCheckBoxClick;

/// 继续播放按钮点击的回调
/// @param isSelected 7日不在提醒有没有选中
- (void)onContinuePlayTappedWithCheckBoxSelectedState:(BOOL)isSelected;

@end

/// 使用移动网络的提示view
@interface QMUGCUsingCellularDataView : UIView

/// delegate
@property (nonatomic, weak  ) id <QMUGCUsingCellularDataViewDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
