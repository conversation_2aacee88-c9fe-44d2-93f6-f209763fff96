//
//  QMWebView.h
//  QMapBusiness
//
//  Created by lidazhu on 2021/9/26.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <WebKit/WebKit.h>
#import "QMWebViewDelegateDispatcher.h"
#import "QMWebViewBuilder.h"
#import "QMWebViewContext.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWebView : WKWebView

@property (nonatomic, strong, readonly) QMWebViewDelegateDispatcher *delegateDispatcher;
@property (nonatomic, strong, readonly) QMWebViewContext *context;
@property (nonatomic, strong, readonly) QMWebViewBuilder *builder;
@property (nonatomic, strong, readonly) QMWebViewLoader  *loader;
@property (nonatomic, strong, readonly) NSMutableArray   *customPlugins;

- (instancetype)initWithURL:(NSString *)url options:(QMWebViewOption)options;

- (void)setup;
- (void)build;

@end

NS_ASSUME_NONNULL_END
