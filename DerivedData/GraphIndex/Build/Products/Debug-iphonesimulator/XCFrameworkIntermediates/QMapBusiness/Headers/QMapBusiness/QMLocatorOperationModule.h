//
//  QMLocatorOperationModule.h
//  QMapBusiness
//
//  Created by 江航 on 2021/12/27.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMInteractionEventTrack.h>
  

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMHomePageLocatorOperationStatus) {
    QMHomePageLocatorOperationStatusNone,   //未展示状态
    QMHomePageLocatorOperationStatusHide,   //收起状态
    QMHomePageLocatorOperationStatusShow,   //展开状态
};
typedef NS_ENUM(NSInteger, QMLocatorOperationType) {
    QMLocatorOperationTypeUnknown,
    QMLocatorOperationTypeHome,
    QMLocatorOperationTypeBus,
    QMLocatorOperationTypeHomeEngine,
};

@protocol QMLocatorOperationModuleDelegate <NSObject>

@optional
//动画能否展示
- (BOOL)shouldAnimationShow;

@end


@class QMClientOperationHomePageLocator;
@class QMHomePageEngineLocator;

@interface QMLocatorOperationModule : NSObject
//delegate
@property (nonatomic, weak) id<QMLocatorOperationModuleDelegate> delegate;
//定位点运营位展示状态
@property (nonatomic) QMHomePageLocatorOperationStatus locatorOperationStatus;
//本次启动是否展示过定位点运营位
@property (nonatomic) BOOL hasShowLocatorOperation;

// touch互动引擎的数据，用于互动引擎上下行治理，没有数据传nil
- (void)showLocatorOperationInMapView:(DMMapView *)mapView
                                 type:(QMLocatorOperationType)type
                                model:(QMClientOperationHomePageLocator *)model
                                touch:(QMInteractionTouchData * _Nullable)touch;

//收起定位点资源位
- (void)hideLocatorOperationIfNeed;

//关闭定位点资源位
- (void)closeLocatorOperationIfNeed;

// 设置首页 定位点的新数据格式：10.19版本开始
- (void)showHomeEngineLocatorOperationInMapView:(DMMapView *)mapView
                                           type:(QMLocatorOperationType)type
                                          model:(QMHomePageEngineLocator *)model
                                          touch:(QMInteractionTouchData * _Nullable)touch;
@end

NS_ASSUME_NONNULL_END
