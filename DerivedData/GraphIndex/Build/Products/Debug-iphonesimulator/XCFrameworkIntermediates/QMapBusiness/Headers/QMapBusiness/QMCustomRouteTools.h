//
//  QMCustomRouteTools.h
//  QMapBusiness
//
//  Created by leon<PERSON>zwang on 2024/1/8.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMCustomRouteTools : NSObject

+ (BOOL)canCustomWithRoute:(QRouteResultForTravel *)result;

// 根据算路参数和结果返回驾车导航VC
+ (UIViewController *)navVCWithParam:(QDriveRouteReqParam *)originalParam 
                              result:(QRouteResultForDrive *)originalResult;

+ (void)addFavouriteRoute:(QRouteForTravel *)route
                wayPoints:(NSArray <QRoutePlaceInfo *>*)wayPoints
                completed:(void(^)(NSString * _Nullable favoriteId))completed;

+ (void)cancelFavouriteRouteWithfavId:(NSString *)favoriteId
                            completed:(void(^)(BOOL success))completed;

+ (NSString *)stringByLocalLevel:(CGFloat)zoomLevel;

//  计算给定经纬度是否在路线上
+ (BOOL)coordinate:(CLLocationCoordinate2D)coor inRoute:(QRouteForTravel *)route mapScale:(CGFloat)mapScale;

+ (CLLocationCoordinate2D)projectionCoordinateInRoute:(QRouteForTravel *)route withCoordinate:(CLLocationCoordinate2D)coor mapScale:(CGFloat)mapScale;

// 找出路线中距离给定的经纬度最近的点
+ (CLLocationCoordinate2D)nearestCoordinateInRoute:(QRouteForTravel *)route  withCoordinate:(CLLocationCoordinate2D)coor;

// 在对应比例尺下判断用户操作（点击、长按等）允许的误差
+ (double)pxDistanceByMapScale:(CGFloat)mapScale;

+ (void)getShortLinkWithWayPoints:(NSArray<QRoutePlaceInfo *> *)wayPoints completion:(void(^)(NSString * _Nullable link))completion;

@end

NS_ASSUME_NONNULL_END
