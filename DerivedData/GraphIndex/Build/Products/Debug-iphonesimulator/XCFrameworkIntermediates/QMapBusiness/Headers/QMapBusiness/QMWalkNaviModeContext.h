//
//  QMWalkNaviModeContext.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/10.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapNaviKit/ViewEventInfo.h>
#import <QMapNaviKit/QMRoute.h>
#import "NavigationLogicController.h"
#import "QMWeakSignalGPSHandler.h"
#import <QMapNaviKit/QMWalkCycleBase.h>
#import <QMapNaviKit/NaviAutoDismissConfirmView.h>
#import <QMapMiddlePlatform/RouteSearcherConstant.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMWalkNaviModeContext : NSObject


/// 路线当前吸附点Index
@property (nonatomic, assign) NSUInteger routePointIndex;
/// 路线当前吸附点坐标
@property (nonatomic, assign) DMMapPoint routeMapPoint;
/// 底部面板信息
@property (nonatomic, strong) ViewEventInfo *info;
/// 顶部面板信息
@property (nonatomic, strong) QMRoute *route;
///  骑行方式
@property (nonatomic, assign) QMCycleSubmode currentCycleMode;

typedef NS_ENUM(NSUInteger, QMWalkNaviModeContextSource) {
    QMWalkNaviModeContextSourceNone,
    QMWalkNaviModeContextSourceNormal, // 普通步导
    QMWalkNaviModeContextSourceAR      // AR步导
};



///AR和普通导航共享属性

/// 当前模式下的上一模式
@property (nonatomic, assign) QMWalkNaviModeContextSource sourceFrom;
@property (nonatomic, strong, nullable) NavigationLogicController *logic;
@property (nonatomic, strong, nullable) QMWeakSignalGPSHandler *weakGPSHandler;
@property (nonatomic, strong) QRouteResult *searchResult;
@property (nonatomic, strong) RouteReqParam *searchParameter;
/// 和引擎回调，偏航时绘制灰色虚线有关
@property (nonatomic, assign) int matchFailedType;

/// 步骑行路线
@property (nonatomic, strong) QMWalkCycleBase *jsonRoute;
@property (nonatomic, assign) double sumTimeOfAR;
@property (nonatomic, assign) double sumTimeOfWalk;
@property (nonatomic) BOOL isJumpFromBusDetail;

@property (nonatomic) BOOL isGPSWeak;

// 记录是否有出现 驾车强切 步骑行的弹框
@property (nonatomic) NaviAutoDismissConfirmView *autoDissmisConfirmView;
// 记录上一次吸附给到的吸附点，用于在切换AR和普通导航时，要把微偏路线画上
@property (nonatomic, strong) CLLocation *lastFixedLocation;
// 记录最新一次画出微偏路线时自车标的位置
@property (nonatomic, strong) CLLocation *locatorCurLocation;



@end

NS_ASSUME_NONNULL_END
