//
//  QMWebViewMessageHandler.h
//  QMapBusiness
//
//  Created by allensun on 2021/6/22.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import <QMapMiddlePlatform/QQJSWebViewProtocol.h>
#import "QMCommonJSBridgePlugin.h"
#import "QMMapJSBridgePlugin.h"
#import "QMApolloJSBridgePlugin.h"
#import "QMOperationJSBridgePlugin.h"
#import "POIDetailJSBridgePlugin.h"
#import "QMCreditJSBridgePlugin.h"
#import "QMUgcReportJSPlugin.h"
#import "QMVoiceAssistantJSBridgePlugin.h"
#import "QMDriveJSBridgePlugin.h"
#import "QMVoiceJSBridgePlugin.h"
#import "QMPayJSBridgePlugin.h"
#import "QMRumMonitorJSBridgePlugin.h"
#import "QMDynamicJSBridgePlugin.h"
#import "QMDarkModeBridgePlugin.h"
#import "QMWidgetJSBridgePlugin.h"
NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMWebViewMessageHandlerName;

@interface QMWebViewMessageHandler : NSObject

@property (nonatomic, readonly) QMCommonJSBridgePlugin *commonJSBridge;
@property (nonatomic, readonly) QMMapJSBridgePlugin *mapJSBridge;
@property (nonatomic, readonly) QMApolloJSBridgePlugin *apolloJSBridge;
@property (nonatomic, readonly) QMDarkModeBridgePlugin *darkmodeJSBridge;
@property (nonatomic, readonly) QMOperationJSBridgePlugin *opertaionJSBridge;
@property (nonatomic, readonly) POIDetailJSBridgePlugin *poiJSBridge;
@property (nonatomic, readonly) QMCreditJSBridgePlugin *creditJSBridge;
@property (nonatomic, readonly) QMUgcReportJSPlugin *ugcJSBridge;
@property (nonatomic, readonly) QMVoiceAssistantJSBridgePlugin *voiceAssistantJSBridge;
@property (nonatomic, readonly) QMDriveJSBridgePlugin *driveJSBridge;
@property (nonatomic, readonly) QMVoiceJSBridgePlugin *voiceJSBridge;
@property (nonatomic, readonly) QMPayJSBridgePlugin *payJSBridge;
@property (nonatomic, readonly) QMRumMonitorJSBridgePlugin *rumJSBridge;
@property (nonatomic, readonly) QMDynamicJSBridgePlugin *dynamicJSBridge;
@property (nonatomic, readonly) QMWidgetJSBridgePlugin *widgetJSBridge;


- (instancetype)initWithWebView:(WKWebView *)webView oldHandler:(nullable id<QQJSWebViewProtocol>)oldHandler;

/**
 分发事件
 @param eventName 事件名称
 @param parameters 参数
 */
- (void)dispatchEvent:(NSString *)eventName parameters:(nullable NSDictionary<NSString *, id> *)parameters;

/**
 处理 callback 回调
 */
- (void)handleCallbackWithQuery:(NSDictionary *)query param:(id)param;

@end

NS_ASSUME_NONNULL_END
