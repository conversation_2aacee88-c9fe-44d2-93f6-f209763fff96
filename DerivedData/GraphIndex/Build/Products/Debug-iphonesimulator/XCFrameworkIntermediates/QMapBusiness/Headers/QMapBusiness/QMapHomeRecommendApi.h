//
//  QMapHomeRecommendApi.h
//  SOSOMap
//
//  Created by admin on 2020/6/28.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapProto/QMJCE_MapBus_GetRecommendLineRequest.h>
#import <QMapProto/QMJCE_MapBus_GetRecommendLineResponse.h>
#import <QMapProto/QMJCE_MapBus_UpdateRecommendTagRequest.h>

NS_ASSUME_NONNULL_BEGIN
 
@interface QMapHomeRecommendApi : NSObject

+ (void)sendHomeRecommendRequest:(QMJCE_MapBus_GetRecommendLineRequest *)request
                         success:(void(^)(QMJCE_MapBus_GetRecommendLineResponse *)) success
                         failure:(void(^)(NSUInteger, NSString * _Nonnull))failure;

+ (void)closeHomeRecommendRequest:(QMJCE_MapBus_UpdateRecommendTagRequest *)request;

@end

NS_ASSUME_NONNULL_END
