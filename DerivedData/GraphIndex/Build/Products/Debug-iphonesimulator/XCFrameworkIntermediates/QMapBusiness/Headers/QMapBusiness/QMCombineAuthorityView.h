//
//  QMCombineAuthorityView.h
//  QMapMiddlePlatform
//
//  Created by he yan on 2022/7/4.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, QMAuthorityType) {
    QMAuthorityTypeMiniprogram,       //仅小程序授权
    QMAuthorityTypeCombineAuthority   //联合授权
};

typedef void (^QMCombineAuthorityCallbackBlock)(QMAuthorityType type);

@interface QMCombineAuthorityView : UIView

+ (QMCombineAuthorityView *)sharedInstance;

//显示
- (void)showAuthorityViewWithCallback:(QMCombineAuthorityCallbackBlock)callback;

//消失
- (void)disMissAuthorityView;
@end

NS_ASSUME_NONNULL_END
