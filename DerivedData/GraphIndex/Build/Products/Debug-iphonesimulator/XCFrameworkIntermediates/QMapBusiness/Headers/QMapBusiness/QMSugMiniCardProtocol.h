//
//  QMSugMiniCardProtocol.h
//  QMapBusiness
//
//  Created by 衡松 on 2021/3/17.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import "QMSugMiniCardInfo.h"

@class QMSugMiniCardInfo;
typedef void (^sugMiniCardLogClickBlock)(NSString *appName);
typedef void (^QMSugMiniCardIconClickBlock)(QMSugMiniCardInfo *cardInfo, NSString *requestId);
@protocol QMSugMiniCardProtocol <NSObject>

@required
- (void)updateSugMiniCardInfo:(QMSugMiniCardInfo *)cardInfo;
- (NSString *)miniCardIdentifier;
//- (CGFloat)sugMiniCardHeight;

@optional

- (void)setKeyword:(NSString *)keyword;
- (void)setSugMiniAppLogClick:(sugMiniCardLogClickBlock)clickBlock;
- (void)setSugMiniAppRightIconClick:(QMSugMiniCardIconClickBlock)clickBlock;
- (void)setSugMiniAppRequestId:(NSString *)requestId;

@end

