//
//  QMCommuteConfirmViewController.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON>(林汉达) on 2019/4/8.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <QMapMiddlePlatform/QMCommuteProtocol.h>
#import "QMRouteEditViewController.h" 

typedef NS_ENUM(int, QMCommuteConfirmInTranType) {
    QMCommuteConfirmInTranTypePop = 0,
    QMCommuteConfirmInTranTypePresent,
};

@protocol QMCommuteConfirmViewControllerDelegate;

@interface QMCommuteConfirmViewController : QMViewController

- (instancetype)initWithCommuteFlag:(QMCommuteFlag)commuteFlag commuteMode:(QMCommuteType)commuteMode locateInfo:(QMCommuteLocateInfo *)locateInfo;


/**
 协议对象
 */
@property (nonatomic, weak) id<QMCommuteConfirmViewControllerDelegate> delegate;

/**
 通勤标志位，标志家或公司
 */
@property (nonatomic, assign, readonly) QMCommuteFlag commuteFlag;


/**
 通勤方式，标志驾车或公交
 */
@property (nonatomic, assign, readonly) QMCommuteType commuteType;

/**
 通勤目的信息数据
 */
@property (nonatomic, strong, readonly) QMCommuteLocateInfo *locateInfo;


/**
 设置是起点或终点
 */
@property (nonatomic, assign) RouteEditType editType;


/**
 进入页面的方式，是通过present还是pop
 */
@property (nonatomic, assign) QMCommuteConfirmInTranType transType;


/**
 页面进入标识符
 */
@property (nonatomic, assign) QMCommuteGuideShowType showType;

- (void)confirmHomeCompany;
- (void)modifyHomeCompanyWithReqSource:(kReqSource)reqSource;

@end


@protocol QMCommuteConfirmViewControllerDelegate <NSObject>

@optional

/**
 当用户设置成功家或公司之后回调

 @param vc 自身控制器
 @param locateInfo 位置数据信息
 @param commuteFlag 标志是家还是公司
 @param isModify 是否是二次修改的结果
 */
- (void)qmCommuteConfirmViewController:(QMCommuteConfirmViewController *)vc didUpdateLocateInfo:(QMCommuteLocateInfo *)locateInfo commuteFlag:(QMCommuteFlag)commuteFlag isModify:(BOOL)isModify;

@end
