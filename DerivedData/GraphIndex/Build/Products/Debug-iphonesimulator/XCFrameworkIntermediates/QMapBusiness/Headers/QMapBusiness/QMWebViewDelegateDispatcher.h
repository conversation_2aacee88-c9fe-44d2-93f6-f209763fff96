//
//  QMWebViewDelegateDispatcher.h
//  QMapBusiness
//
//  Created by lidazhu on 2021/9/14.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WebKit/WebKit.h>
#import "QMWebViewDelegate.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMWebViewDelegateDispatcher : NSObject <WKUIDelegate, WKNavigationDelegate, UIScrollViewDelegate>

@property (nonatomic, weak) WKWebView *webView;

- (void)addDelegate:(id<QMWebViewDelegate>)delegate;

- (void)notifyViewWillAppear:(BOOL)animated;
- (void)notifyViewDidAppear:(BOOL)animated;
- (void)notifyViewWillDisappear:(BOOL)animated;
- (void)notifyViewDidDisappear:(BOOL)animated;
- (void)nofityJSBridgePerformanceCallback:(NSDictionary *)query;

@end

NS_ASSUME_NONNULL_END
