//
//  EngineEventInfo.h
//  SOSOMap
//
//  Created by Bruce<PERSON><PERSON> on 2017/3/17.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <GLMapLibHeaders/data_type.h>

@interface EngineEventInfo : NSObject

@property (nonatomic, assign) NSInteger fixedPointIndex;
@property (nonatomic, assign) NSInteger segmentIndex;
@property (nonatomic, assign) NSInteger segmentDistance;  // 距离下一诱导点的距离
@property (nonatomic, assign) NSInteger distanceLeft;
@property (nonatomic, assign) double distancePassed;
@property (nonatomic, assign) NSInteger eventPointIndex;
@property (nonatomic, assign) DMMapPoint eventPoint;
@property (nonatomic, strong) NSString* action;
@property (nonatomic, strong) NSString* nextRoadName;
@property (nonatomic, assign) BOOL isLastSegment;
@property (nonatomic, assign) float course;             //路线角度
@property (nonatomic, strong) NSString *roadName;       //AR步导新增，当前道路名
@property (nonatomic, assign) double outAngle;          //AR步导新增，link角度
@property (nonatomic, assign) int roadLength;           //道路长度
@property (nonatomic, assign) int intersection;         //诱导类型
@property (nonatomic, assign) int distanceToShapePoint; //距离下一形状点的距离
@property (nonatomic, assign) int accAction;            //辅助动作（上、下天桥，上、下地下通道）
 
@end
