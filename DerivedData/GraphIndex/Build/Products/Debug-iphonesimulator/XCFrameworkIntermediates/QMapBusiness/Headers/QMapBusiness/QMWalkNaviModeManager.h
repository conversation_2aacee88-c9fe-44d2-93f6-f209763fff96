//
//  QMWalkNaviModeManager.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/10.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "QMWalkNaviModeContext.h"


NS_ASSUME_NONNULL_BEGIN

@protocol QMWalkNaviModeDelegate <NSObject>

/// 切换至普通步导
- (void)switchToNormalWalkNaviMode;
/// 切换至AR步导
- (void)switchToARWalkNaviMode;
/// AR和普通步导模式切换实现信息同步
/// @param context 需共享的导航数据
- (void)syncNaviContext:(QMWalkNaviModeContext *)context;

@optional
/// 获取AR和普通步导的共享信息
- (QMWalkNaviModeContext *)getWalkNaviModeContext;

@end





/// 步行导航模式管理器
@interface QMWalkNaviModeManager : NSObject

@end

NS_ASSUME_NONNULL_END
