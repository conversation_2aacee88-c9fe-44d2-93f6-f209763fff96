//
//  QMWalkRouteProtocol.h
//  SOSOMap
//
//  Created by sarah on 2017/5/16.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/RouteReqParam.h>
@class QRouteResult;
@class QMJCE_routesearch_TmapWalkRouteReq;
@class QMWalkCycleCommonRouteResult;

@interface QMWalkRouteProtocol : NSObject

@end

@interface WalkRouteReqParam : RouteReqParam<NSCopying>
@property (nonatomic, assign) int maptype;
@property (nonatomic, copy) NSString *angle;
@property (nonatomic, copy) NSString *reason;
@property (nonatomic, copy) NSString *yawInfoJSONString;
@property (nonatomic, assign) int adsorb_len;
@property (nonatomic, assign) int yawp;
@property (nonatomic, copy) NSString *routeid;
@property (nonatomic, copy) NSArray<QMPointInfo *> *specificPoints;

@property (nonatomic) BOOL isNeedSearchFromHint;
@property (nonatomic) CLLocation *matchLocation;

+ (QMJCE_routesearch_TmapWalkRouteReq *)generateTmapWalkReq:(WalkRouteReqParam *)reqParam;

+ (QMJCE_routesearch_WalkRouteReq *)generateWalkRouteReq:(WalkRouteReqParam *)param;

//组包，不包括cmd和subCmd
- (NSData *)getReqDataWithExtraHippyParam:(NSDictionary *)param;

//组包，不包括cmd和subCmd，提供给Hippy使用。比如驾车小结页巡航
+ (QMJCE_routesearch_TmapWalkRouteReq *)regenerateJCEReq:(QMJCE_routesearch_TmapWalkRouteReq *)req fromExtraHipppyDic:(NSDictionary *)dictionary;

@end

//解包
@interface QMWalkRouteProtocol (walkRouteUnpacking)

+ (QMWalkCycleCommonRouteResult *)walkRouteResult:(NSData *)resp error:(NSInteger *)error;

+ (QMWalkCycleCommonRouteResult *)parseWalkRouteResponse:(QMJCE_routesearch_TmapWalkRouteRsp *)resp
                                                userInfo:(NSDictionary *)userInfo
                                                   error:(NSInteger *)error;

/// 起终点信息，根据Dictionary转为QMJCE对象
/// @param pointDic 起终点字典
+ (QMJCE_common_SimplePOIRequestInfo *)regenerateWithPointRequestDic:(NSDictionary *)pointDic;

@end
