//
//  QMNHippyBridger_HBusCard.h
//  QMapBusiness
//
//  Created by wyh on 2022/2/12.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapHippy/QMNHippyBridgerFactory.h>

extern NSString * _Nonnull const QMNHBusCardHippyBundleName;

NS_ASSUME_NONNULL_BEGIN

@class QMNHippyBridger_HBusCard;

@protocol QMNHippyBridgerHBusCardPosting <NSObject>

- (void)bridger:(QMNHippyBridger_HBusCard *)bridger cardListHeightWillUpdate:(CGFloat)totalHeight;

- (NSDictionary *)bridgerGetScrollParameters:(QMNHippyBridger_HBusCard *)bridger;

@end

@interface QMNHippyBridger_HBusCard : QMNHippyBaseBridger

@property (nonatomic, strong, readonly) TMHippyViewBox *hippyBox;

@property (nonatomic, assign, readonly) CGFloat cardHeight;

- (NSDictionary *)getScrollParameter;

- (void)callbackCardHeightWillUpdate:(CGFloat)cardHeight;

#pragma mark -

- (void)dispatchToHippyClickLocationButton;

- (void)dispatchToHippyDrawerStateChanged;;

- (void)dispatchToHippyDrawerScrollDidDecelerated;

- (void)dispatchToHippyLocationUpdate:(CLLocation *)location;

//绿色能量任务点击
- (void)safe_dispatchToHippyClickGreenTask:(NSString *)markerId taskId:(NSString *)taskId;

- (void)dispatchToHippyMapFullModeChange:(BOOL)full;

- (void)dispatchToHippyTabBarClicked:(BOOL)hasSelected;

- (void)dispatchToHippyTabShowMiniCard;

- (void)dispatchToHippyTabChangeState:(NSInteger)level animation:(BOOL)animation;

@end

NS_ASSUME_NONNULL_END
