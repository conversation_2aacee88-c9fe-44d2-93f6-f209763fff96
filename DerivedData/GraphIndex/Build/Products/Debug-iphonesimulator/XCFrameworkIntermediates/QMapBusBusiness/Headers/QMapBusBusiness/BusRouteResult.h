//
//  BusRouteResult.h
//  SOSOMap
//
//  Created by sarah on 2017/5/27.
//  Copyright © 2017年 Tencent. All rights reserved.
//

//#import "WalkRoute.h"
#import <QMapMiddlePlatform/TaxiFee.h>
#import <QMapBusBusiness/BusRoute.h>
#import <QMapBusBusiness/QMBusRouteInterveneModel.h>
typedef enum QMWalkCycleETAType {
    QMWalkCycleETATypeUnknown = 0,
    QMWalkCycleETATypeBus, //步骑行终点为公交站
    QMWalkCycleETATypeSubway,//步骑行终点为地铁站
    QMWalkCycleETATypeEndPoint,//正常路线
} QMWalkCycleETAType;

@interface BusWalkRoute : NSObject
@property (nonatomic, assign) NSInteger time;       //单位为:分钟
@property (nonatomic, assign) NSInteger distance;   //单位为：米
@end

@interface BusRouteResult : QRouteResult <NSCopying, NSCoding> {
    NSArray<BusRoute *> *_routes;
}

/**路线，支持多路线，成员类型为QRoute子类**/
@property (nonatomic, strong) NSArray<BusRoute *> *routes;
@property (nonatomic) NSArray<QMJCE_MapRoute_CoachInfo *> *coach;       //跨城客车方案
@property (nonatomic, copy) NSArray<TaxiFee *> *taxiFees;
@property (nonatomic, copy) NSString *taxiFeeString;

@property (nonatomic, assign) int error;
@property (nonatomic, assign) int type;
@property (nonatomic, assign) int iErrNo;
@property (nonatomic,   copy) NSString *errMsg;
@property (nonatomic, assign) NSInteger selectedRouteIndex;
@property (nonatomic, assign) NSInteger routeStat;      //  路线刷新时标记路线状态 [0, 100) 表示成功 [100, 200) 路线失效 [200, 300) 不支持刷新

@property(nonatomic, assign) BOOL tooNearToBus;//离线下距离过近，不要进行公交搜索
@property(nonatomic, assign) BOOL isCityCrossBus;
@property(nonatomic, copy) NSArray<BusWalkRoute *> *walkRoutes;
@property(nonatomic,   copy) NSString *traceId;
@property (nonatomic,  copy) NSString *reqRouteUrl;//保存在线路线请求返回的url
@property (nonatomic,  copy) NSArray *etaCheckItems;
@property (nonatomic, copy) NSString *routeTraceId;
@property (nonatomic) NSString *shortAddr;
@property (nonatomic) QMWalkCycleETAType etaType;
@property (nonatomic) NSString *buslineId;
@property (nonatomic) NSString *subwayName;
@property (nonatomic) BOOL isOffOperation;
@property (nonatomic) NSInteger offOperationIndex;
/// 干预信息
@property (nonatomic) QMBusRouteInterveneModel *intervene;
- (BOOL)hasRoutes;
- (BOOL)hasWalkRoutes;
- (BOOL)hasBusRoutes;

- (id)selectedRoute;

@end
