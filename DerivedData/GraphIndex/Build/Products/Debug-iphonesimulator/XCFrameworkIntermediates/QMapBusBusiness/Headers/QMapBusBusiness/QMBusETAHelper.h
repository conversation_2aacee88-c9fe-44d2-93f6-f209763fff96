//
//  QMBusETAHelper.h
//  SOSOMap
//
//  Created by admin on 2020/6/9.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@class QMBusETAItem, BusRouteResult, BusRoute, QMJCE_MapBus_BusLineRealtimeInfo, QMRealtimeLine;

typedef NS_ENUM(NSUInteger, QMRealtimeBusFromsouce) {
    QMRealtimeBusFromsouceUnknown,
    QMRealtimeBusFromsouceWalkCycle = 10
};
@interface QMBusETAHelper : NSObject

/// 公交算路多方案页
/// @param result 算路结果
/// @return 返回拼装好的 ETA 请求数据
+ (NSArray<QMBusETAItem *> *)getETAItemListByBusRouteResult:(BusRouteResult *)result;

/// 公交方案详情
/// @param busRoute 单方案
+ (NSArray<QMBusETAItem *> *)getETAItemListByBusRoute:(BusRoute *)busRoute mutiple:(BOOL)mutiple;

/// 公交方案首段公交信息
/// @param route busRoute 单方案
/// @return 返回拼装好的 ETA 请求数据
+ (NSArray<QMBusETAItem *> *)getFirstETAItemByBusRoute:(BusRoute *)route;

/// 解析 QMJCE_MapBus_BusLineRealtimeInfo
/// @param lineInfo jce对象
+ (QMRealtimeLine *)parseJCEBusLineRealtime:(QMJCE_MapBus_BusLineRealtimeInfo *)lineInfo;

/// 打开实时公交主页
+ (void)openRealtimeHomePage;

/// 打开实时公交线路详情页
/// @param stopId 站点ID
/// @param lineId 线路ID
+ (void)openRealtimeDetailWithStopId:(NSString *)stopId lineId:(NSString *)lineId fromAction:(NSString  * _Nullable )fromAction;

/// 打开实时公交线路详情页
/// @param stopPoiId 站点ID
/// @param lineId 线路ID
+ (void)openRealtimeDetailWithStopPoiId:(NSString *)stopPoiId lineId:(NSString *)lineId fromAction:(NSString  * _Nullable )fromAction;

/// 打开实时公交地铁线路详情页
/// @param stopId 站点ID
/// @param lineId 线路ID
+ (void)openRealtimeSubwayDetailWithStopId:(NSString *)stopId lineId:(NSString *)lineId;

/// 打开实时公交地铁线路详情页
/// @param stopPoiId 站点ID
/// @param lineId 线路ID
+ (void)openRealtimeSubwayDetailWithStopPoiId:(NSString *)stopPoiId lineId:(NSString *)lineId;

/// 打开实时公交线路详情页
/// @param stopPoiId 站点ID
/// @param lineId 线路ID
/// @param fromSouce 跳转来源

+ (void)openRealtimeDetailWithStopId:(NSString *)stopId
                              lineId:(NSString *)lineId
                           fromSouce:(QMRealtimeBusFromsouce)source
                          fromAction:(NSString  * _Nullable )fromAction;

@end

NS_ASSUME_NONNULL_END
