//
//  QMapNoticeAPI.h
//  SOSOMap
//
//  Created by admin on 2020/3/18.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMapNoticeModel.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, QMNoticeReqScene) {
    // 公交相关 场景
    QMNoticeReqSceneBusRoute = 1,          // 算路、多方案页
    QMNoticeReqSceneBusStartEndPoint = 2,  // 起终点
    QMNoticeReqSceneBusRealTime = 3,
};

@interface QMapNoticeAPI : NSObject

+ (QMapNoticeAPI *)shareInstance;  

/// 发送请求 获取公告信息
/// @param noticeRequest 请求内容
/// @param success 请求成功的回调
/// @param failure 请求失败的回调
/// @return requestSeq 发送请求的标识 用来取消请求
- (void)sendRequest:(QMJCE_MapRoute_DestAnnouncementReq *) request
            success:(void(^)(QMJCE_MapRoute_DestAnnouncementResp *responce)) success
            failure:(void(^)(NSUInteger errCode, NSString *errMsg)) failure;

@end

NS_ASSUME_NONNULL_END
