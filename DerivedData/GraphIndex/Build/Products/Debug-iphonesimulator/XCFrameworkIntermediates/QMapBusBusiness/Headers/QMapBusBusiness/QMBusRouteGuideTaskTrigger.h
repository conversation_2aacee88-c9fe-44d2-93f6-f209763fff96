//
//  QMBusRouteGuideTaskTrigger.h
//  QMapBusiness
//
//  Created by admin on 2021/5/1.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMBusRouteGuideTask : NSObject
@property (nonatomic, copy) NSString *taskName;
@property (nonatomic, assign) NSTimeInterval intervalTime;
@property (nonatomic, assign) BOOL repeat; 
@end

@interface QMBusRouteGuideTaskTrigger : NSObject

QMSharedInstance_H(QMBusRouteGuideTaskTrigger)

- (BOOL)setupTaskName:(NSString *)name
         intervalTime:(NSTimeInterval)intervalTime
           taskMethod:(void(^)(QMBusRouteGuideTask *task)) doTask
               repeat:(BOOL)repeat;
 
- (void)removeTask:(NSString *)taskName;

- (BOOL)taskHasSetup:(NSString *)taskName;

@end

NS_ASSUME_NONNULL_END
