//
//  QMBusLocationManager.h
//  SOSOMap
//
//  Created by 胡朔溢 on 2019/7/18.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


@class BusRoute;

typedef enum : NSUInteger {
    BusCurrentLoactionDefault,
    BusCurrentLoactionStart,
    BusCurrentLoactionBusSubway,
    BusCurrentLoactionEnd,
} BusCurrentLoactionType;

typedef enum : NSUInteger {
    BusNearStationDefault,
    BusNearStationStart,
    BusNearStationEnd
} BusNearStationType;

@interface QMBusCurrentLoactionModel : NSObject

@property (nonatomic, copy) NSString *uid;
@property (nonatomic, assign) BusCurrentLoactionType type;

@end

@interface QMBusStationLoactionModel : NSObject

@property (nonatomic, copy) NSString *uid;
@property (nonatomic, assign) NSUInteger pointIndex;

@end

@protocol QMBusLocationManagerDelegate <NSObject>

- (void)busLocationChanged;

@end

@interface QMBusLocationManager : NSObject

@property (nonatomic, weak) id <QMBusLocationManagerDelegate> delegate;
@property (nonatomic, strong, readonly)QMBusCurrentLoactionModel *currentModel;

- (void)updateRoute:(BusRoute *)route;
- (void)pause;
- (void)resume;

@end

NS_ASSUME_NONNULL_END
