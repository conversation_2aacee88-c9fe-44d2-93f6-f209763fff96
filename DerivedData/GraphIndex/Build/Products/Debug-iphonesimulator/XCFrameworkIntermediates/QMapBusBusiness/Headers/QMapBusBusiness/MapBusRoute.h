//
//  MapBusRoute.h
//  SOSOMap
//
//  Created by shu<PERSON><PERSON><PERSON> on 2020/4/17.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "BusRoute.h"
#import <QMapProto/QMJCE_MapRoute_BusRoute.h>
#import "QMBusRouteJce+MJExtension.h"

@class RoutePoint;
@class RouteBusSegment;
@class QMDataArchive;

NS_ASSUME_NONNULL_BEGIN

@interface MapBusRoute : BusRoute <NSCopying>

@property (nonatomic, nullable) NSString *favoriteId;   //  转换成对应的BusFavoriteRoutePO类后的id

@property (nonatomic, strong) QMJCE_MapRoute_BusRoute *jceRoute;
- (QMJCE_MapRoute_BusRoute *)content;
- (id)initWithContent:(QMJCE_MapRoute_BusRoute *)jceRoute;
- (NSMutableDictionary *)getJceDictionary;
- (NSArray<QMJCE_MapRoute_Tran *> *)getJceRouteTrans;
- (NSArray<QMJCE_MapRoute_BusRouteLineContainer *> *)getJceRouteLineConrtainers;
@end

NS_ASSUME_NONNULL_END
