//
//  BusRouteTrainModel.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/17.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, BusTrainBriefItemType) {
    BusTrainBriefItemTypeUnknow = 0,   //缺省值
    BusTrainBriefItemTypeTrain,        //普通火车
    BusTrainBriefItemTypeRail,         //高铁动车
    BusTrainBriefItemTypeRailAndTrain, //高铁动车+普通火车 手图后台添加特定跨城火车类型
    BusTrainBriefItemTypePlane,        //跨城飞机
    BusTrainBriefItemTypeCoach,        //跨城客车
    BusTrainBriefItemTypeBus,          //跨城纯公交方案
    BusTrainBriefItemTypeOther,        //其他时间方案
};

@class BusCrossCityParaItem, BusRouteResult, BusTrainBriefItem, BusRoute, RouteBusSegment;
@interface BusRouteTrainViewModel : NSObject

@property (nonatomic, copy) NSArray<BusTrainBriefItem *> *items;
@property (nonatomic, assign) NSInteger trainCount;
@property (nonatomic, assign) NSInteger railwayCount;
@property (nonatomic, assign) NSInteger hasDriveCount;
@property (nonatomic) NSArray<QMJCE_MapRoute_CoachInfo *> *coach;       //跨城客车方案
@property (nonatomic) NSArray<BusRoute *> *bus;                         //公交方案

- (instancetype)initWithBusRouteResult:(BusRouteResult *)result;

@end

typedef NS_ENUM(NSUInteger, BusCrossCityParaItemType) {
    /// 公交地铁 >样式
    BusCrossCityParaItemTypeBusSubway,
    /// 火车
    BusCrossCityParaItemTypeTrain,
    /// 步行前往
    BusCrossCityParaItemTypeWalk,
    /// 驾车前往
    BusCrossCityParaItemTypeDrive,
}; 

@interface BusTrainBriefItem : NSObject

@property (nonatomic, copy) NSString *title;
@property (nonatomic, assign) BusTrainBriefItemType type; 
@property (nonatomic, copy) NSString *timeStr;
@property (nonatomic, copy) NSString *priceStr;
@property (nonatomic, assign) NSUInteger price;
@property (nonatomic, assign) NSUInteger time;
@property (nonatomic, assign) BOOL isDriveItem;
@property (nonatomic, copy) NSString *trainStartStation;
@property (nonatomic) int deptime;

@property (nonatomic, copy) NSArray <RouteBusSegment *>* busRouteList;

@property (nonatomic) NSArray<QMJCE_MapRoute_TaxiInfo*>* taxis;
@property (nonatomic) BusRoute *busRoute;
@property (nonatomic) BOOL isFuture;   //是否未来数据


- (instancetype)initWithBusRoute:(BusRoute *)route;

@end

@interface QMBusTrainBriefItemGroup : NSObject
@property (nonatomic) BusTrainBriefItemType groupType;
@property (nonatomic) NSString *groupTitle;
@property (nonatomic) NSString *groupTime;
@property (nonatomic) NSString *groupPrice;
@property (nonatomic) BOOL canExpand;
@property (nonatomic) BOOL isExpand;
@property (nonatomic) NSArray <BusTrainBriefItem *>* briefItemArray;
@property (nonatomic) NSArray <QMJCE_MapRoute_CoachInfo *> *coach;
@end

