//
//  QMBusEtaHealthReport.h
//  BullShit
//
//  Created by admin on 2021/1/6.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMBusEtaShowType) {
    QMBusEtaShowTypeDetailCard = 1, //eta详情页面
    QMBusEtaShowTypeBigCard = 2,    //eta大卡片
    QMBusEtaShowTypeMiddleCard = 3, //eta中卡片
    QMBusEtaShowTypeSmallCard = 4,  //eta小卡片
};

typedef NS_ENUM(NSInteger, QMBusEtaStatus) {
    QMBusEtaStatusNormal = 1,               //正常显示eta
    QMBusEtaStatusNoBus = 2,                //未发车
    QMBusEtaStatusNoOperatingTime = 3,      //非营运时间
    QMBusEtaStatusSignalInterrupt = 4,      //eta信息中断
    QMBusEtaStatusNetworkInterrupt = 5,     //eta信息中断
    QMBusEtaStatusNotEtaBusLine = 6,        //非eta线路
};

@interface QMBusEtaHealthEntity : NSObject
// 展示模块的名称，为展示模块命名，另外一个页面可能有多个展示模块，以做区分【后续：需要给定一个模块对应的点击事件关系，如xxx_click】
@property (nonatomic, strong) NSString *moduleName;
// 页面曝光对应的event_code
@property (nonatomic, strong) NSString *pageCode;
// eta展示类型（1: eta详情页面，2: 大卡片，3: 中卡片，4: 小卡片）
@property (nonatomic, assign) QMBusEtaShowType etaShowType;
// 线路方案所在ETA展示模块中的序号，从上到下，或者从左右右第几个单元（eta详情页为0）
@property (nonatomic, assign) NSInteger etaSequence;
// 线路id
@property (nonatomic, strong) NSString *routeID;
// 线路名字
@property (nonatomic, strong) NSString *routeName;
// 站点ID
@property (nonatomic, strong) NSString *stationID;
// 站点名称
@property (nonatomic, strong) NSString *stationName;
// eta显示状态 1:正常显示eta，2:未发车，3:非营运时间，4:eta信息中断，5:网络中断，6:非eta线路
@property (nonatomic, assign) QMBusEtaStatus eta_status;

- (NSString *)reportKey;

- (NSDictionary *)toReportDictionary;

@end

typedef NSArray<QMBusEtaHealthEntity *>* _Nullable (^QMGetBusETAReportDataBlock)(void);
typedef void(^QMBusEtaReportAppWillEnterForeground)(void);
@interface QMBusEtaHealthReporter : NSObject

- (void)applicationWillEnterForegroundAction:(QMBusEtaReportAppWillEnterForeground)block;

- (void)setReportGetDataMethod:(QMGetBusETAReportDataBlock)block;

- (void)willReportData;

- (void)cancelReportData; 

- (void)clearReportedHistory;

- (BOOL)needReportEtaDataWithBusStopUid:(NSString *)stopUid
                             busLineUid:(NSString *)busLineUid;

@end

NS_ASSUME_NONNULL_END
