//
//  QMBusRouteGuidePostion.h
//  QMapBusBusiness
//
//  Created by minhuiliao on 2022/2/22.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, QMBusRouteGuidePostionType) {
    QMBusRouteGuidePostionTypeLevelStart = -2, // 退离 起点
    QMBusRouteGuidePostionTypeLevelEnd = -1, // 退离 终点点
    QMBusRouteGuidePostionTypeUnknown = 0, // 未知
    QMBusRouteGuidePostionTypeStart = 1, // 起点
    QMBusRouteGuidePostionTypeStation = 2, // 站点
    QMBusRouteGuidePostionTypeBetweenStation = 3, // 站间
    QMBusRouteGuidePostionTypeBetweenTran = 4, // 换成步行间
    QMBusRouteGuidePostionTypeEnd = 5, // 终点
    QMBusRouteGuidePostionTypeInterval = 6, // 骑行打车中
    QMBusRouteGuidePostionTypeIntervalBegin = 7, // 骑行打车开始
    QMBusRouteGuidePostionTypeIntervalEnd = 8, // 骑行打车结束
};

@interface QMBusRouteGuidePostion : NSObject
@property (nonatomic, assign) QMBusRouteGuidePostionType postionType;
@property (nonatomic, strong) NSString *uid;
@property (nonatomic, strong) NSString *intervalId;
@property (nonatomic, readonly, assign) BOOL onRoad; // 是否行程中

- (NSString *)getPostionTypeStr;
- (NSString *)description;
- (BOOL)onRoad;
- (BOOL)isEqual:(id)object;
@end

NS_ASSUME_NONNULL_END
