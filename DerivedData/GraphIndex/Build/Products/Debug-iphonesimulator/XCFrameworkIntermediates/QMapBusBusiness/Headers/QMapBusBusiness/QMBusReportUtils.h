//
//  QMBusReportUtils.h
//  QMapBusiness
//
//  Created by minhuiliao on 2022/1/21.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>


NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM (NSInteger, QMBusReportErrorCode)
{
    QMBusReportErrorOk = 0,///<请求没错
    
    QMBusReportSeverError  = 404, ///<后台服务error
    QMBusReportServerErrorLineNotGet = 103,///<NavGuide获取线路详情出错
    QMBusReporthServerErrorStartEndInvalid = -10,///<起终点坐标不合法错误
    QMBusReportServerErrorEndNotMatch= -2,///<终点吸附错误
    QMBusReportServerErrorStartNotMatch = -1,///<起点吸附错误
    QMBusReportServerErrorRouteFail = 97,///<NavSearch路由失败
    QMBusReportServerErrorStartEndZero = -2001,///<驾车起终点为0
    QMBusReportServerErrorTimeout = -3,///<请求导航后台超时
    QMBusReportServerErrorNoNet = -4, ///<驾车算路哈雷反馈状态码：-4>
    QMBusReportServerErrorJSONParse = -1000,///<json无法解析
    QMBusReportHTMLError = -10001, //
    QMBusReport4XXOR5XXError  = -10002, //
    QMBusReportJCEDeseError = -10012,///jce解码为空
    QMBusReporthDataError = -10013, /// 返回数据错误
};
@interface QMBusReportUtils : NSObject

/// 发起请求参数错误埋点
+ (void)reportBusUtilsRequestParamError:(NSString *)name;
/// 发起请求次数计时
+ (void)reportBusUtilsCount:(NSString *)name;
/// 发起请求成功埋点
+ (void)reportBusUtilsSuccess:(NSString *)name;
/// 发起请求失败埋点
+ (void)reportBusUtilsFail:(NSError *)error withName:(NSString *)name;
/// 发起请求取消埋点
+ (void)reportBusUtilsCancel:(NSString *)name;
/// 发起请求耗时
+ (void)reportBusUtilsRequestTime:(NSInteger)duration withName:(NSString *)name;
///解析耗时
+ (void)reportBusUtilsParseTime:(NSInteger)duration withName:(NSString *)name;
///数据返回出错
+ (void)reportBusUtilsDataError:(NSString *)name;

@end

NS_ASSUME_NONNULL_END
