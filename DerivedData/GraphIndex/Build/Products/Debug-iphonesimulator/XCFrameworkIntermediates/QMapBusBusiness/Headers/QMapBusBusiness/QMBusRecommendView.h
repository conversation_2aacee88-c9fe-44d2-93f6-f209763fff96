//
//  QMBusRealTimeBaseView.h
//  SOSOMap
//
//  Created by minhuilia<PERSON> on 2020/6/19.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapBusBusiness/QMRealtimeLine.h>

NS_ASSUME_NONNULL_BEGIN

@class QMapHomeRecommendModel;

typedef NS_ENUM(NSInteger, QMBusRecommendFromSource) {
    QMBusRecommendFromSourceUnknown = 0,
    QMBusRecommendFromSourceHomepage = 1,
    QMBusRecommendFromSourceRouteSearch = 2,
};

//定义实时公交线路条数详情
@interface QMBusRecommendView : UIView
@property (nonatomic, strong, readonly) QMButton *closeButton;//关闭按钮
@property (nonatomic, copy) void(^closeBlock)(void);
@property (nonatomic, copy) void(^clickBusLineBlock)(void);
@property (nonatomic, copy) void(^clickBusMoreBlock)(void);
@property (nonatomic, readonly, assign) QMBusRecommendFromSource fromSource;
@property (nonatomic, readonly, strong) QMapHomeRecommendModel *recommendModel;
- (instancetype)initWithFromSource:(QMBusRecommendFromSource)fromSource;

- (void)handleRealtimeBusBtnClick;

- (void)updateRecommendData:(QMapHomeRecommendModel *)recommendData;

- (void)close;

- (void)resetUIWithAnimationProgress:(CGFloat)progress;

@end

@interface QMBusRecommendView(EtaHealthReport)

- (void)openEtaHealthReportCheckWithSource:(BOOL)fromHomePage;

- (void)willReportEta;

- (void)cancelReportEta;

- (void)applicationWillEnterForegroundAction:(void(^)(void))block;

@end
NS_ASSUME_NONNULL_END
