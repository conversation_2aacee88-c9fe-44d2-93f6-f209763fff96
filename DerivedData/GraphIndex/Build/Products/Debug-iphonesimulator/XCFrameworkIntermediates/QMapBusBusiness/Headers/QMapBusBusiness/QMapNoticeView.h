//
//  QMapNoticeView.h
//  SOSOMap
//
//  Created by admin on 2020/3/18.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapUIKit/QMapUIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class QMapNoticeModel;
@interface QMapNoticeView : QMTipsView 
@property (nonatomic, copy) void(^ onNoticeClick)(QMapNoticeModel *notice);
@property (nonatomic, assign) BOOL hiddenCloseBtn;
@property (nonatomic, assign) BOOL hasShadow;
@property (nonatomic, assign) CGFloat height;
+ (BOOL)needShowNotice:(QMapNoticeModel *)notice;
- (void)updateNoticeInfo:(QMapNoticeModel *)notice;
@end

NS_ASSUME_NONNULL_END
