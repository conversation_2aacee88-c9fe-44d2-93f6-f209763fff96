//
//  QMBusRouteGuideEvent.h
//  QMapBusiness
//
//  Created by admin on 2021/5/12.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


@interface QMBusGuidePoint : NSObject
// 径上的Geo序列索引号
@property (nonatomic) NSInteger coorIdx;
// 形状区间上的偏移距离, 即对coorIdx的坐标, 在路线上的偏移距离
@property (nonatomic) NSInteger shapeOffset;
// 位置, 墨卡托
@property (nonatomic) DMMapPoint geoPoint;
@end

@interface QMBusGuidePush : NSObject
@property (nonatomic, assign) BOOL need;
@end

@interface QMBusGuideToast : NSObject
@property (nonatomic, assign) BOOL need;
@end

@interface QMBusGuideAlert : NSObject
@property (nonatomic, assign) BOOL need;
@end

@interface QMBusGuideVibrate : NSObject
@property (nonatomic, assign) NSInteger level;
@end 

@interface QMBusGuideVoiceEvent : NSObject
@property (nonatomic, copy) NSString *content;
@property (nonatomic, strong) QMBusGuidePush *push;
@property (nonatomic, strong) QMBusGuideToast *toast;
@property (nonatomic, strong) QMBusGuideAlert *alert;
@property (nonatomic, strong) QMBusGuideVibrate *vibrate;
@end

typedef NS_ENUM(NSInteger, QMBusGuideEventPointType) {
    QMBusGuideEventPointTypeNone = 0,
    QMBusGuideEventPointTypeInterval = 1,
    QMBusGuideEventPointTypeTran = 2,
};

@interface QMBusGuideEventPointItem : NSObject
@property (nonatomic, assign) QMBusGuideEventPointType eventPointType;
@property (nonatomic, copy) NSString *segmentId;
@property (nonatomic, assign) int distance;
@end

typedef NS_ENUM(NSInteger, QMBusGuideDisplayKind) {
    QMBusGuideDisplayKindStart = 10000,
    QMBusGuideDisplayKindEnd = 10001,
    QMBusGuideDisplayKindStation = 10002,
    QMBusGuideDisplayKindStationBetween = 10003,
    QMBusGuideDisplayKindTran = 10004,
    QMBusGuideDisplayKindInterval = 10005,
    QMBusGuideDisplayKindIntervalBegin = 10006,
    QMBusGuideDisplayKindIntervalEnd = 10007,
    QMBusGuideDisplayKindLeaveStart = 10008,
    QMBusGuideDisplayKindLeaveEnd = 10009,
};

@interface QMBusGuideDisplayEvent : NSObject
@property (nonatomic, strong) QMBusGuidePoint *targetPos;
@property (nonatomic, copy) NSString *targetUid;
@property (nonatomic, assign) QMBusGuideDisplayKind displayKind;
@property (nonatomic, copy) NSString *segmentUid;
@end

NS_ASSUME_NONNULL_END
