//
//  QMRealtimeLine.h
//  SOSOMap
//
//  Created by Panda on 2017/12/22.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMBusStopInfo.h>

typedef NS_ENUM(NSUInteger, QMBusComfortableLevel) {
    QMBusComfortableLevel_1 = 1,
    QMBusComfortableLevel_2 = 2,
    QMBusComfortableLevel_3 = 3,
    QMBusComfortableLevel_4 = 4,
    QMBusComfortableLevel_5 = 5
};

typedef NS_ENUM(NSInteger, QMRealtimeLineType) {
    QMRealtimeLineTypeUnknown         = -1, //未知
    QMRealtimeLineTypeETAAndLocation  = 0,  //普通实时公交，返回ETA和车辆位置
    QMRealtimeLineTypeLocation        = 1,  //乘车码实时公交，仅返回车辆位置
    QMRealtimeLineTypePredict         = 2,  //仅返回预测到站时间段
    
};

@interface QMRealtimeBusLocation : NSObject

@property (nonatomic, assign)CLLocationCoordinate2D location;

@end

//后台的扩展字段，保存一些扩展信息，所以命名与其相对应
@interface QMRealtimeExt : NSObject

@property (nonatomic, assign)BOOL isDescShowEta;

@end
/// 舒适度
@interface QMBusComfortableModel : NSObject
/// 满载率
@property (nonatomic) NSInteger crowdPercentage;
@property (nonatomic, assign) QMBusComfortableLevel level;
@property (nonatomic) QMBusComfortableLevel originLevel;
@property (nonatomic, strong, readonly) NSString *iconImageName;
@property (nonatomic, strong, readonly) NSString *comfortableDesc;
@property (nonatomic, readonly) UIColor *fullColor;
@property (nonatomic, assign) NSTimeInterval editTime;
@property (nonatomic, strong) NSString *lineName;
@property (nonatomic, strong) QMBusLineInfo *lineInfo;
@property (nonatomic, assign) BOOL isShow;
- (BOOL)currentValid;
+ (UIColor *)fullColorWithLevel:(QMBusComfortableLevel)level;
+ (NSString *)comfortableDescWithLevel:(QMBusComfortableLevel)level;
@end

@interface QMRealtimeBus : NSObject
@property (nonatomic, copy) NSString *busId;
@property (nonatomic, copy) NSString *lineId;
@property (nonatomic, assign) NSInteger eat;
@property (nonatomic, assign) NSInteger stopNum;
@property (nonatomic, assign) CLLocationCoordinate2D location;
@property (nonatomic, assign) NSInteger distance;
@property (nonatomic, assign) int realtimeBusStatus;
@property (nonatomic, copy) NSString *strEta;
@property (nonatomic, copy) NSString *realtimeBusStatusDesc;
@property (nonatomic, strong) QMRealtimeExt *realtimeExt;

@property (nonatomic, assign) QMBusComfortableLevel level;
/// 满载率
@property (nonatomic) NSInteger crowdPercentage;
@property (nonatomic, copy, readonly) NSString *iconImageName;
@property (nonatomic, copy) NSString *comfortableDesc;
@property (nonatomic, readonly) UIColor *fullColor;
// 小车轨迹
@property (nonatomic, strong) QMBusTrajInfo *busTrajInfo;

@property (nonatomic) QMRealtimeLineType rtBusType;
/// 预计到达时间段
@property (nonatomic) NSString *preStrEta;

- (BOOL)comfortableValid;
- (BOOL)realtimeValid;
- (BOOL)predictValid;
@end

@interface QMRealtimeLine : NSObject
@property (nonatomic, copy) NSString *lineUid;
@property (nonatomic, copy) NSString *lineName;
@property (nonatomic, assign) NSInteger hasRealtimeBus;
@property (nonatomic, assign) NSInteger eat;
@property (nonatomic, assign) NSInteger stopNum;
@property (nonatomic, copy) NSArray <QMRealtimeBus *> *buses;
@property (nonatomic, assign) NSInteger distance;
@property (nonatomic, assign) int realtimeBusStatus;
@property (nonatomic, copy) NSString *beginTime;
@property (nonatomic, copy) NSString *endTime;
@property (nonatomic, copy) NSString *strEta;
@property (nonatomic, copy) NSString *realtimeBusStatusDesc;
@property (nonatomic, strong) QMRealtimeExt *realtimeExt;
@property (nonatomic, assign) QMBusComfortableLevel comforableLevel;
@property (nonatomic, copy) NSString *comfortableDesc;
/// 实时公交类型
@property (nonatomic) QMRealtimeLineType rtBusType;
/// 对应的jce源数据
@property (nonatomic) QMJCE_MapBus_BusLineRealtimeInfo *jceInfo;
- (NSDictionary *)dictionaryFromQMRealtimeLine;

@end



