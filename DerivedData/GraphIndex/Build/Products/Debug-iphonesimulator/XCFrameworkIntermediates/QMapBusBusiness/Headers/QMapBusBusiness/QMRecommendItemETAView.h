//
//  QMRecommendItemETAView.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/29.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class QMRealtimeLine, QMRealtimeBus, QMLineRealTimeModel;
typedef NS_ENUM(NSInteger, QMRecommendLinesType) {
    QMRecommendBusOne = 0,
    QMRecommendLinesOne = 1,
    QMRecommendLinesTwo = 2,
    QMRecommendLinesMultiple
};

typedef NS_ENUM(NSInteger,  QMBusRecommendETAStyle) {
    QMBusRecommendETAStyleGreen = 0,
    QMBusRecommendETAStyleWhite = 1,
    QMBusRecommendETAStyleSmallGreen = 2,
    QMBusRecommendETAStyleSmallMiniGreen = 3,
    QMBusRecommendETAStyleNormalGray = 4,
};

@interface QMBusRecommendComfortableView : UIView
@property (nonatomic, assign) BOOL hasDescStr;
@property (nonatomic, assign) CGSize imageSize;
- (void)updateComfortableForRealTimeBus:(QMRealtimeBus *)bus;
- (void)hiddenComfortable;
- (BOOL)needShowComfortable;
@end
@interface QMBusRecommendETAView : UIView
@property (nonatomic, assign) BOOL noResetFontSize;
@property (nonatomic, readonly, assign) QMBusRecommendETAStyle style;
- (instancetype)initWithStyle:(QMBusRecommendETAStyle)style;
- (void)updateRealtimeBus:(QMRealtimeBus *)bus;
- (void)updateETALabelWithDesc:(NSString *)statusStr realtimeStatus:(int)status;
- (void)resetEta;
- (void)updateStyle:(QMBusRecommendETAStyle)style;
- (void)resetUIWithAnimationProgress:(CGFloat)progress;
- (NSString *)getEtaLabelStr;
/// 刷新掌上公交数据
- (void)updatePalmStationModel:(QMLineRealTimeModel *)model;
@end

@interface QMBusRecommendOneItemETAView : UIView
@property (nonatomic, strong) QMBusRecommendComfortableView *comfortableView;
@property (nonatomic, strong) QMBusRecommendETAView *recommendItemETAView;
- (void)updateRealtimeLine:(QMRealtimeLine *)line busIndex:(NSInteger)busIndex;
//- (void)updateRealtimeBus:(QMRealtimeBus *)bus;
//- (void)updateETALabelWithShowEta:(BOOL)showEta
//                           strEta:(NSString *)strEta
//            realtimeBusStatusDesc:(NSString *)statusStr;

@end

NS_ASSUME_NONNULL_END
