//
//  TTCarMoveDefine.h
//  TencentTravelService
//
//  Created by 33 on 2019/6/8.
//  Copyright © 2019 Tencent. All rights reserved.
//

#ifndef TTCarMoveDefine_h
#define TTCarMoveDefine_h

typedef NS_ENUM(NSInteger, TTOrderState) {
    TTOrderStateUnknown,
    TTOrderStateDispatch = 110,         // 调度中；正在派发订单，等待司机接单
    TTOrderStateAcceptOrder = 120,      // 已接单；司机接到订单
    TTOrderStateDriveChange = 121,      // 司机改派；司机取消订单，等待或指派新司机接单
    TTOrderStateArrival = 130,          // 已到达；司机到达接驾地点，但未开始计费
    TTOrderStateTripBegin = 140,        // 行程开始；接到乘客，开始行程，开始计费
    TTOrderStateToBePaid = 150,         // 待支付；订单已结算，等待乘客支付
    TTOrderStatePayed = 160,            // 已支付；订单支付成功。
    TTOrderStatePassengerCancelled = 170,     // 乘客取消
    TTOrderStateDriveCancelled = 180,         // 司机取消
    TTOrderStateSystemCancelled = 190,        // 系统(客服)取消
    TTOrderStateDispatchFailed,         // 调度失败；打车一期不涉及
    TTOrderStateWaitingEvaluation,      // 待评价；打车一期不涉及
    TTOrderStateEvaluated,              // 已评价；打车一期不涉及
};

#endif /* TTCarMoveDefine_h */
