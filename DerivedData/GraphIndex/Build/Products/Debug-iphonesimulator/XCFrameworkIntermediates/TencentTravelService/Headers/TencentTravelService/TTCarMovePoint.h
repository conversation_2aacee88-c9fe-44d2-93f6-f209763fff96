//
//  TTCarMovePoint.h
//  TencentTravel
//
//  Created by 王中周 on 2019/5/30.
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <CoreLocation/CLLocation.h>

NS_ASSUME_NONNULL_BEGIN

@interface TTCarMovePoint : NSObject <NSCopying>

@property (nonatomic) CGFloat angle;                        // 旋转角度
@property (nonatomic) CGFloat scale;                        // 缩放比例
@property (nonatomic) CLLocationCoordinate2D coordinate;    // 经纬度
@property (nonatomic) CGFloat duration;                     // 动画持续时间
@property (nonatomic) NSInteger index;                      // 吸附的路线 index
@property (nonatomic, copy) NSString *routeID;              // 吸附的路线 ID
@property (nonatomic) NSTimeInterval timestamp;             // 生成时间戳

@end

NS_ASSUME_NONNULL_END
