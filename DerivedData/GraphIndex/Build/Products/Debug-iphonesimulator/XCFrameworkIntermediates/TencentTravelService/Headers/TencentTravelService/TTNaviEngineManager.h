//
//  TTNaviEngineManager.h
//  TencentTravel
//
//  Created by 33 on 2019/5/31.
//

#import <Foundation/Foundation.h>
#import <QMapNaviKit/QMCarNaviLightEngine.h>

NS_ASSUME_NONNULL_BEGIN

@interface TTNaviEngineManager : NSObject
+ (instancetype)defaultManager;
- (void)startWithRouteResult:(QRouteResultForDrive *)routeResult;
- (void)stop;
// 返回类型：routeId-QMCarNaviLightEngineLocationUpdateResult
- (NSDictionary *)updateLocation:(CLLocation *)location;

@end

NS_ASSUME_NONNULL_END
