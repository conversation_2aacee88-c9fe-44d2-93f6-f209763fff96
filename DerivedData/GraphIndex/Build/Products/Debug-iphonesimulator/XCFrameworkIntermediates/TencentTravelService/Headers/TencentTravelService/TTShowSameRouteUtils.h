//
//  TTShowSameRouteUtils.h
//  TencentTravelService
//
//  Created by z<PERSON><PERSON><PERSON> on 2020/6/30.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import <QMapMiddlePlatform/RouteTrafficInfo.h>

NS_ASSUME_NONNULL_BEGIN

@interface TTShowSameRouteUtils : NSObject

+ (nullable NSArray <QRouteCoordinate *> *)parseDiffGeoPoints:(NSDictionary *)info;

+ (nullable NSArray <id > *)parseTrafficInfo:(NSDictionary *)info className:(NSString *)className;

+ (NSInteger)parseTrafficVersion:(NSDictionary *)info;

+ (NSInteger)parseDuration:(NSDictionary *)info;

+ (NSInteger)parseDistance:(NSDictionary *)info;

+ (NSString *)parseRouteID:(NSDictionary *)info;

+ (NSArray<QRouteStepForDrive *>*)parseRouteSteps:(NSDictionary *)info;

+ (QRoutePlaceInfo *)routePlaceInfoWithCoordinate:(CLLocationCoordinate2D)coordinate;

@end

NS_ASSUME_NONNULL_END
