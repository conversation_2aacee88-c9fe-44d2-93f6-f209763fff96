//
//  TTRouteSearcher.h
//  TencentTravel
//
//  Created by 33 on 2019/5/29.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString * const TTRouteSearcherParamError;
extern NSString * const TTRouteSearcherInValidParamError;
extern NSString * const TTRouteSearcherSamePointParamError;
extern NSString * const TTRouteSearcherResultError;

typedef void(^TTRouteSearchCompletionBlock)(BOOL success, NSString * _Nullable errorMessage, QRouteResultForDrive * _Nullable result, NSDictionary * _Nullable option);

@interface TTRouteSearcher : NSObject
- (void)startRouteSearch:(QTaxiRouteReqParam *)param
         completionBlock:(TTRouteSearchCompletionBlock)completionBlock;
- (void)cancel;
@end

NS_ASSUME_NONNULL_END

