//
//  TTShowSameRoute.h
//  TencentTravelService
//
//  Created by z<PERSON><PERSON><PERSON> on 2020/6/30.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapRouteSearchKit/QRouteSearchItem.h>
#import <QMapMiddlePlatform/RouteTrafficInfo.h>

NS_ASSUME_NONNULL_BEGIN

@interface TTShowSameRoute : NSObject

/// 解析司乘同显信息，返回一条路线信息
/// @param info hippy 传过来的司乘同显信息
- (nonnull QRouteResultForDrive *)calcultRouteWithInfo:(NSDictionary *)info;

/// 更新 trafficInfo，司乘同显中，trafficInfo 没有起始点的坐标，导致刷新交通信息出现异常，这里需要补全 RouteTrafficInfo 的 startCoordinate 和 endCoordinate。
/// 由于司乘同显中，不是每次都有路线信息，所以需要外部的路线信息来进行辅助，从而计算出 trafficInfo 中的起始点坐标
/// @param route 路线信息，trafficInfo 中的起始坐标会从 route.coors 中取
/// @param trafficInfo trafficInfo
- (void)updateTrafficInfoWithRoute:(QRouteForDrive *)route trafficInfo:(NSArray <RouteTrafficInfo *> *)trafficInfo;

+ (BOOL)isSupportShareTrack;

@end

NS_ASSUME_NONNULL_END
