//
//  TTCommonDefine.h
//  TencentTravel
//
//  Created by 王中周 on 2019/5/17.
//

#import <Foundation/Foundation.h>
#import <QMapFoundation/QMapFoundation.h>

#ifndef TTCommonDefine_h
#define TTCommonDefine_h

typedef NS_ENUM(NSInteger, TTRCTErrorCode) {
    TTRCTErrorCodeError = -1,
    TTRCTErrorCodeSuccess = 0,
};

typedef NS_ENUM(NSInteger, TTTabState) {
    TTTabStateNone = 0,
    TTTabStateTaxi = 1,
    TTTabStateCar = 2,
    TTTabStateBus = 3,
    TTTabStateWalk = 4,
    TTTabStateCycle = 5,
    TTTabStateTrain = 6,
    TTTabStateChauffeur = 7,
    TTTabStateRideSharing = 8
};

typedef NS_ENUM(NSInteger, TTEnvType) {
    TTEnvTypeNone = 0,
    TTEnvTypeDev = 1,
    TTEnvTypeTest = 2,
    TTEnvTypeOnline = 3,
};

typedef NS_ENUM(NSInteger, TTBindPhoneResultCode) {
    TTBindPhoneResultCodeFail = -1,
    TTBindPhoneResultCodeSuccess = 0,
};

typedef NS_ENUM(NSInteger, TTCarMoveType) {
    TTCarMoveTypeUnknown,
    TTCarMoveTypePickUpPassenger = 1,   // 接驾
    TTCarMoveTypeSendPassenger,     // 送驾
};

typedef void(^TTCompletionBlock)(BOOL success, NSString *errorMessage);
typedef void(^TTTaxiRouteSearchCompletionBlock)(BOOL success, NSArray *coor, NSString *routeId, NSString *errorMessage);
typedef void(^TTCancelBlock)(void);

extern NSString *const kTTStart;
extern NSString *const kTTDestination;
extern NSString *const kTTMarkerIds;

extern NSString *const kTTStartMarkerZIndex;
extern NSString *const kTTEndMarkerZIndex;
extern NSString *const kTTRouteZIndex;

/**
 根据错误码获取错误描述信息
 */
NSString * TTLocalizedDescriptionWithCode(TTRCTErrorCode code);

/**
 car move 模块写Debug log
 */
#define TTCarMoveLogDebug(format, ...) QMLogDebug(@"打车-feature", format, ##__VA_ARGS__);

#define TTCarMoveLogInfo(format, ...) QMLogInfo(@"打车-feature", format, ##__VA_ARGS__);
#define TTETAShowLogInfo(format, ...) QMLogInfo(@"打车-feature-showETA", format, ##__VA_ARGS__);

void TTCarMoveLogWarn(NSString *log);

void TTCarMoveLogError(NSString *log);

#endif /* TTCommonDefine_h */
