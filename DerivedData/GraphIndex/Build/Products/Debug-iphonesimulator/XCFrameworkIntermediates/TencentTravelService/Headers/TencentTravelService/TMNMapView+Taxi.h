//
//  TMNMapView+Taxi.h
//  TencentTravelService
//
//  Created by co<PERSON><PERSON> on 2022/9/1.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapHippy/TMNMapView.h>
#import <QMapHippy/TMCommonMarker.h>

NS_ASSUME_NONNULL_BEGIN

@interface TMNMapView (Taxi)

/**
 根据 marker 的唯一标识获取它们的 rect
 */
- (void)rectWithIdentifiers:(NSArray<NSString *> *)identifiers
                 edgeInsets:(UIEdgeInsets)edgeInsets
                      param:(NSDictionary *)param
                 completion:(void(^)(DMMapRect rect, UIEdgeInsets edgeInsets))completion;

/**
 根据 marker 的唯一标识获取它们对应的 QAnnotationView 集合
 */
- (NSArray<TMCommonMarker *> *)markersWithIdentifiers:(NSArray<NSString *> *)identifiers;

@end

NS_ASSUME_NONNULL_END
