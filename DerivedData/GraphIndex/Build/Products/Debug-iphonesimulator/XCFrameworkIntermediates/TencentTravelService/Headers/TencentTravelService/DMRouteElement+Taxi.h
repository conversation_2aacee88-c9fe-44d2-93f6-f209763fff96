//
//  DMRouteGroup+Taxi.h
//  TencentTravelService
//
//  Created by co<PERSON><PERSON> on 2022/9/1.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <DragonMapKit/DragonMapKit.h>
#import <QMapMiddlePlatform/QMMapRefactorUtils.h>
#import <QMapMiddlePlatform/DMMapView+QMMiddlePlatform.h>
#import <QMapMiddlePlatform/DMHippyMapViewStack.h>
#import <QMapRouteSearchKit/QRouteCoordinate.h>

NS_ASSUME_NONNULL_BEGIN

@interface DMRouteElement (Taxi)

/**
 展示路线起点 Marker
 */
- (BOOL)showStartMarkerWithRoutePoint:(QRouteCoordinate *)routePoint mapView:(DMMapView *)mapView param:(NSDictionary *)param;

/**
 展示路线终点 Marker
 */
- (BOOL)showEndMarkerWithRoutePoint:(QRouteCoordinate *)routePoint mapView:(DMMapView *)mapView param:(NSDictionary *)param;

/**
 移除路线起点 Marker
 */
- (BOOL)removeStartMarkerWithMapView:(DMMapView *)mapView;

/**
 移除路线终点 Marker
 */
- (BOOL)removeEndMarkerWithMapView:(DMMapView *)mapView;

@end

NS_ASSUME_NONNULL_END
