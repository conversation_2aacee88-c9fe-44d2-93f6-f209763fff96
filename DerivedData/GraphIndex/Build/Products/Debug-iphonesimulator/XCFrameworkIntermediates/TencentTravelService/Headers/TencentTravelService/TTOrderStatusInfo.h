//
//  TTOrderStatusInfo.h
//  TencentTravelService
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TTCarMoveDefine.h"

NS_ASSUME_NONNULL_BEGIN

@interface TTOrderStatusInfo : NSObject

// 订单状态
@property (nonatomic, assign) TTOrderState orderState;

// 订单ID
@property (nonatomic, copy) NSString *orderID;

// 订单状态更新时间戳，单位秒，10位
@property (nonatomic, assign) NSTimeInterval orderStatusUpdateTime;

// 司机开始等待的时间戳，单位秒，10位
@property (nonatomic, assign) NSTimeInterval driverStartWaitTime;

// 服务器当前时间戳，单位秒，10位
@property (nonatomic, assign) NSTimeInterval currentServerTime;

// 客户端当前时间戳-服务器当前时间戳，单位秒，10位
@property (nonatomic, assign) NSTimeInterval nativeTimeMinusServerTimeGap;

// 没有等待费的时间间隔
@property (nonatomic, assign) NSTimeInterval lastWaitWithoutCostMaxTime;

// 开始收取等待费的时间戳，单位秒，10位
@property (nonatomic, assign) NSTimeInterval waitStartCostTime;

// 是否产生等待费
@property (nonatomic, assign) BOOL hasWaitCost;

- (instancetype)initWithParams:(NSDictionary *)params;

// 剩余倒计时时间，单位秒，10位
- (NSTimeInterval)currentLastWaitingTime;

@end

NS_ASSUME_NONNULL_END
