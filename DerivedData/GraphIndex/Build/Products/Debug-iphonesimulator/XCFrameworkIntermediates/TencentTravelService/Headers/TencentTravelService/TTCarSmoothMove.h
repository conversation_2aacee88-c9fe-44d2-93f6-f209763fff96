//
//  TTCarSmoothMove.h
//  TencentTravel
//
//  Created by foog<PERSON><PERSON>(王中周) on 2019/6/2.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CLLocation.h>
#import "TTCarMoveDefine.h"
#import <QMapHippy/TMNMapView.h>
#import <QMapMiddlePlatform/DMMapView+QMMiddlePlatform.h>
#import <QMapMiddlePlatform/DMHippyMapViewStack.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^TTCarMoveToPointCompletionBlock)(BOOL finished, CLLocationCoordinate2D coordinate, NSInteger matchedIndex, NSString *routeID);

@class QRouteCoordinate;
@class TTCarETACalloutView;
@class QRouteForDrive;
@class TTOrderStatusInfo;
@interface TTCarSmoothMove : NSObject

@property (nonatomic, strong) DMMapView *mapView;
@property (nonatomic, strong) TMNMapView *tmnMapView;
@property (nonatomic, copy) TTCarMoveToPointCompletionBlock moveToPointCompletionBlock; // 汽车每移动一个点，就回调一次
@property (nonatomic, readonly) TTCarETACalloutView *carETACalloutView;

@property (nonatomic, weak) NSMutableDictionary<NSNumber *,DMRouteGroup *> *dRouteGroupMap;

@property (nonatomic, assign) CGFloat carMoveMinDuration;

@property (nonatomic, assign) CGFloat carMoveMaxDuration;
// 车标的固定方向, -1表示随路线方向，[0,360)表示车标的固定方向
@property (nonatomic) CGFloat rotate;

#pragma mark - Smooth Move

/**
 沿着路线将小车移动到特定的点

 @param toPoint 要去的点
 @param angle 小车移动过程中的角度
 @param index 要去的点在路线上的 index
 @param routePoints 路线点串
 @return 移动操作是否成功触发
 */
- (BOOL)moveOnRouteToPoint:(QRouteCoordinate *)toPoint
                     angle:(CGFloat)angle
                     index:(NSInteger)index
               routePoints:(NSArray<QRouteCoordinate *> *)routePoints;

/**
 未吸附情况下，将小车不延路线地移动到特定的点

 @param toPoint 要去的点
 @param angle 小车移动过程中的角度
 @param index 要去的点在路线上的 index，未吸附情况下为 -1
 @return 移动操作是否成功触发
 */
- (BOOL)moveToPoint:(QRouteCoordinate *)toPoint angle:(CGFloat)angle index:(NSInteger)index;

/**
 沿路线无动画直接移动到某个点

 @param toPoint 要去的点
 @param angle 小车移动过程中的角度
 @param index 要去的点在路线上的 index，未吸附情况下为 -1
 @param currentRouteID  路线 ID
 @return 移动操作是否成功触发
 */
- (BOOL)moveOnRouteNoAnimationToPoint:(QRouteCoordinate *)toPoint
                                angle:(CGFloat)angle
                                index:(NSInteger)index
                       currentRouteID:(NSString *)currentRouteID;

/**
 更新路线 ID，通常在路线变化时调用
 */
- (void)updateRouteID:(NSString *)routeID;

/**
 停止正在和将要进行的平滑移动，如果有
 */
- (BOOL)stopSmoothMove;

/**
 后台切换到前台后，计算获取的第一个点与放到后台的最后一个点，大于一定距离直接跳过去（距离通过配置，暂定500米）不执行逐渐消隐的动画
 */
- (BOOL)jumpToCurrentLocationEnterForgroundIfNeeded;


#pragma mark - Car Marker

/**
 算路前，更新气泡内容
 */
- (void)updateCarCalloutMarker:(DMMarkerElement *)carMarker
       calloutMarker:(DMMarkerElement *)calloutMarker
                   withParam:(NSDictionary *)param;
/**
 算路后，更新气泡内容
 */
- (void)updateCarViewWithRoute:(nullable QRouteForDrive *)route
               orderStatusInfo:(TTOrderStatusInfo *)orderStatusInfo;

/**
 移除小车和气泡
 */
- (void)removeCarETACalloutView;

@end

NS_ASSUME_NONNULL_END
