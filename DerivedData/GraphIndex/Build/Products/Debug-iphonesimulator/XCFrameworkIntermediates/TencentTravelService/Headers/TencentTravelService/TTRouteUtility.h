//
//  TTRouteUtility.h
//  TencentTravel
//
//  Created by foog<PERSON><PERSON>(王中周) on 2019/6/15.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CLLocation.h>


@class QRouteForDrive;
@class DMRouteGroup;

typedef NS_OPTIONS(NSInteger, TTRouteStartEndMarkerType) {
    TTRouteStartEndMarkerTypeNone = 1,  //起终点marker都不显示
    TTRouteStartEndMarkerTypeStart = 1<<1,  //只显示起点marker
    TTRouteStartEndMarkerTypeEnd = 1<<2,    //只显示终点marker
    TTRouteStartEndMarkerTypeStartAndEnd = 6,    //起终点marker都显示
};

@interface TTRouteUtility : NSObject

/**
 渲染路线和路线的起点、终点
 */
+ (void)showRoute:(QRouteForDrive *)route overlayGroup:(DMRouteGroup *)overlayGroup
         dMapView:(DMMapView *)dmapView showMarkerType:(TTRouteStartEndMarkerType)showMarkerType
       routeParam:(NSDictionary *)routeParam reverseStartEnd:(BOOL)reverseStartEnd;

/**
 从 param 中解析出起点经纬度
 */
+ (CLLocationCoordinate2D)startCoordinateWithParam:(NSDictionary *)param;

/**
 从 param 中解析出终点经纬度
 */
+ (CLLocationCoordinate2D)endCoordinateWithParam:(NSDictionary *)param;


/// 从param中解析出起点Uid
/// @param param 参数
+ (NSString *)sourcePOIIDWithParam:(NSDictionary *)param;


/// 从param中解析出终点Uid
/// @param param 参数
+ (NSString *)destPOIIDWithParam:(NSDictionary *)param;

/**
 从 route 中获取 ETA 数据
 */
+ (NSDictionary *)getTrafficInfoWithRoute:(QRouteForDrive *)route;

/**
 从 route 构造polyline所需信息
 */
+ (NSDictionary *)getPolylineInfoWithRoute:(QRouteForDrive *)route;

/**
 从 route 构造hippy需要信息,polyline数据+ETA数据
 */
+ (NSDictionary *)getNecessaryInfoWithRoute:(QRouteForDrive *)route;


/// 计算ETA时间
/// @param route 线路（traffics和吸附的latestMatchedPointIndex必要时需要更新）
+ (NSInteger)timeRemainingSecondsFromRoute:(QRouteForDrive *)route;

@end

