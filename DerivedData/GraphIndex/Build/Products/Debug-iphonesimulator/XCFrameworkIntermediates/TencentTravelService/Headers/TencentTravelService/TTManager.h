//
//  TTManager.h
//  hippy
//
//  Created by 33 on 2019/5/11.
//

#import <UIKit/UIKit.h>
#import <QMapHippy/TMCommonDefine.h>

NS_ASSUME_NONNULL_BEGIN

@class TMHippyManager;
@protocol TTManagerDelegate <NSObject>
@optional

// hippy 更新客户端打车起终点
- (void)setRouteStartAndEnd:(NSDictionary *)routeParam;

// 获取路线规划起点和终点
- (NSDictionary *)getRouteStartAndEnd;

// 获取路线规划起点
- (NSDictionary *)getStart;

// 获取路线规划终点
- (NSDictionary *)getEnd;

// hippy 更新多方案页打车Tab下,顶部地址栏的状态
- (void)setMultiRouteTopAddressVisible:(NSDictionary *)visibleParam;

// hippy 多方案页打车Tab下,页卡状态变更的通知
- (void)updateMultiRouteCardStatus:(NSDictionary *)statusParam;

//hippy 打车获取画中画是否支持
- (BOOL)isSupportPictureInPicture;

//hippy  打车获取画中画关闭次数

- (NSUInteger)closePictureInPictureTimes;

//hippy 打车画中画设置按钮开关

- (void)playingPipWithStatusChanged:(BOOL)status;

//打车画中画 追加车型

- (void)updateRidingList:(NSInteger)ridinglistNum;


@end


@class HippyBridge;
@interface TTManager : NSObject

@property (nonatomic, weak) id<TTManagerDelegate> delegate;
@property (nonatomic, readonly) TMHippyManager *hippyManager;
+ (instancetype)shareInstance;

// 提供给H5按hippyName调用的调用的View，带初始化参数
- (UIView *)viewWithHippyName:(NSString *)hippyName params:(nullable NSDictionary *)params;


/// 创建HippyView
/// @param hippyName hippy页面名称
/// @param bundleName bundle名称
/// @param params 参数
- (UIView *)viewWithHippyName:(NSString *)hippyName bundleName:(NSString *)bundleName params:(nullable NSDictionary *)params;

/// 打车埋点上报
/// @param key 埋点的Key
/// @param dic 埋点的字典
- (BOOL)accumulateTaxiDengtaKey:(NSString *)key dic:(NSDictionary *_Nullable)dic;

/// 打车埋点上报
/// @param key 埋点的Key
/// @param dic 埋点的字典
/// @param autoAppendValue 是否需要拼上额外参数
- (BOOL)accumulateTaxiDengtaKey:(NSString *)key dic:(NSDictionary *_Nullable)dic autoAppendValue:(BOOL)autoAppendValue;

#pragma mark - Message To Hippy

// 客户端起终点变更同步给 hippy
- (BOOL)setStartEndToHippyWithDictionary:(nullable NSDictionary *)dictionary bridge:(nullable HippyBridge *)bridge;


/// 代驾tab切换
/// @param dictionary 参数
/// @param bridge hippy实例
- (BOOL)setChauffeurStartEndToHippyWithDictionary:(NSDictionary *)dictionary bridge:(HippyBridge *)bridge;

// 客户端同步切换tab给hippy
- (BOOL)taxiTabSwitch:(NSInteger)tabState bridge:(HippyBridge *)bridge;

/// 代驾tab切换
/// @param tabState tab状态
/// @param bridge hippy 实例
- (BOOL)chauffeurTabSwitch:(NSInteger)tabState bridge:(HippyBridge *)bridge;

@end

NS_ASSUME_NONNULL_END
