//
//  TTNEntryManager.h
//  TencentTravelService
//
//  Created by co<PERSON><PERSON> on 2022/9/1.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TTCommonDefine.h"

NS_ASSUME_NONNULL_BEGIN

@class TMNMapView;
@interface TTNEntryManager : NSObject
@property (nonatomic, strong, readonly) TMNMapView *mapView;
/**
 打车 tab 算路并展示路线

 @param tmMapView 地图实例
 @param mapId 地图 hippyTag
 @param param 相关参数
 @param completionBlock 完成回调
 */
- (void)taxiTabShowRouteWithMapView:(TMNMapView *)tmMapView
                              mapId:(NSNumber *)mapId
                              param:(NSDictionary *)param
                    completionBlock:(nullable void(^)(BOOL success, NSDictionary *trafficInfo, NSString *errorMessage))completionBlock;

/**
 打车 tab 算路并返回polyline所需数据

 @param tmMapView 地图实例
 @param mapId 地图 hippyTag
 @param param 相关参数
 @param completionBlock 完成回调
 */
- (void)searchRoutePolylines:(TMNMapView *)tmMapView
                       mapId:(NSNumber *)mapId
                       param:(NSDictionary *)param
             completionBlock:(nullable void(^)(BOOL success, NSDictionary *polylinesInfo, NSString *errorMessage))completionBlock;

/**
 打车 Tab 最佳视野参数
 
 @param tmMapView 地图实例
 @param mapId 地图 hippyTag
 @param param markerIDs
 @return 操作是否成功
 */
- (BOOL)taxiTabAdjustBestViewWithMapView:(TMNMapView *)tmMapView
                                   mapId:(NSNumber *)mapId
                                   param:(NSDictionary *)param;

/**
 清除打车 Tab 路线和 marker

 @param tmMapView 地图实例
 @param mapId 地图 hippyTag
 @return 操作是否成功
 */
- (BOOL)taxiTabClearWithMapView:(TMNMapView *)tmMapView
                          mapId:(NSNumber *)mapId;

/**
 设置路线相关参数
 @return 操作是否成功
 **/
- (BOOL)taxiTabSetRouteParam:(NSDictionary *)param
                     mapView:(TMNMapView *)tmMapView
                       mapId:(NSNumber *)mapId;
@end

NS_ASSUME_NONNULL_END
