//
//  TTCarMoveBestVision.h
//  TencentTravel
//
//  Created by 王中周 on 2019/6/1.
//

#import <Foundation/Foundation.h>
 
#import <QMapMiddlePlatform/QMMapRefactorUtils.h>
 
  
 
#import <QMapMiddlePlatform/DMMapView+QMMiddlePlatform.h>
#import <QMapMiddlePlatform/DMHippyMapViewStack.h>

NS_ASSUME_NONNULL_BEGIN

@class QMapView;
@class QRouteForDrive;
@class TTOrderStatusInfo;

@interface TTCarMoveBestVision : NSObject

@property (nonatomic, strong) DMMapView *mapView;
@property (nonatomic) UIEdgeInsets edgeInsets; // 最佳视野的边界
@property (nonatomic) BOOL userChangeVision; // 用户手动操作了地图
/**
 挪图后自动回滚视野的时间间隔, 单位: 秒
 */
@property (nonatomic, assign) CGFloat autoTrackingBestVisionTime;

/**
 打车订单状态
 */
@property (nonatomic, weak) TTOrderStatusInfo *orderStatusInfo;

/**
 强制将 route 调整在可视范围，不做 checkMapScale 和 checkMapCenter 检查

 @return 是否显示成功
 */
- (BOOL)updateVisionWithRoute:(QRouteForDrive *)route;

/**
 将 route 调整在可视范围

 @param route 要调整的路线
 @param checkMapScale 为 YES 时，当 mapScale 变化不超过特定值，则不调整可视范围；为 NO 时，不检查 mapScale 变化情况直接调整
 @param checkMapCenter 为 YES 时，当 mapCenter 变化不超过特定值，则不调整可视范围；为 NO 时，不检查 mapCenter 变化情况直接调整
 @param animated 是否带动画
 @Discussion checkMapScale 和 checkMapCenter 都需要检查时，只要有一个满足条件，就进行视野调整
 @return 是否调整成功
 */
- (BOOL)updateVisionWithRoute:(QRouteForDrive *)route checkMapScale:(BOOL)checkMapScale checkMapCenter:(BOOL)checkMapCenter animated:(BOOL)animated latestMatchedPointIndex:(NSInteger)latestMatchedPointIndex;


- (BOOL)updateVisionWithRoute:(QRouteForDrive *)route
                checkMapScale:(BOOL)checkMapScale
               checkMapCenter:(BOOL)checkMapCenter
                     animated:(BOOL)animated
      latestMatchedPointIndex:(NSInteger)latestMatchedPointIndex
                      padding:(UIEdgeInsets)padding;

/**
 将 rect 区域显示在可视范围

 @param rect 要显示的 rect
 @param checkMapScale 为 YES 时，当 mapScale 变化不超过特定值，则不调整可视范围；为 NO 时，不检查 mapScale 变化情况直接调整
 @param checkMapCenter 为 YES 时，当 mapCenter 变化不超过特定值，则不调整可视范围；为 NO 时，不检查 mapCenter 变化情况直接调整
 @param animated 是否带动画
 @Discussion checkMapScale 和 checkMapCenter 都需要检查时，只要有一个满足条件，就进行视野调整
 @return 是否调整成功
 */
- (BOOL)updateVisionWithRect:(DMMapRect)rect checkMapScale:(BOOL)checkMapScale checkMapCenter:(BOOL)checkMapCenter animated:(BOOL)animated;

/**
 触发用户手动操作底图后 10 秒后底图视野恢复逻辑
 当底图需要纳入线路上所有点时调用这个方法

 @return 是否成功触发
 */
- (BOOL)userChangeMapVisionWithRoute:(QRouteForDrive *)route;

/**
 设置最佳视野要额外包含进去的点
 */
- (void)updateIncludePoint:(CLLocationCoordinate2D)includePoint;

/**
 设置最佳视野要额外包含进去的区域
 */
- (void)updateIncludeRect:(DMMapRect)includeRect;

/**
 取消最佳视野
 */
- (void)cancelBestVision;

@end

NS_ASSUME_NONNULL_END
