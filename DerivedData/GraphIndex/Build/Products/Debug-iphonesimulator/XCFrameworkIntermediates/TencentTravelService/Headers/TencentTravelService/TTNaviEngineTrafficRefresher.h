//
//  TTNaviEngineTrafficRefresher.h
//  TencentTravelService
//
//  Created by 33 on 2019/6/4.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TTRouteDefine.h"

NS_ASSUME_NONNULL_BEGIN


static const NSInteger TTNaviEngineTrafficRefresherNoRestTime = -1;

extern NSString *const TTNaviEngineTrafficRefresherTrafficTimes;
extern NSString *const TTNaviEngineTrafficRefresherTrafficTime;
extern NSString *const TTNaviEngineTrafficRefresherTrafficInfos;

@class QRouteResultForDrive, QRouteForDrive;
// 请求结果block
typedef void(^TTNaviEngineTrafficRefresherCompletionBlock)(NSError * _Nullable error, NSDictionary * _Nullable responseDic);
// 请求条件block，是否能发送请求，不提供判断时默认NO
typedef BOOL(^TTNaviEngineTrafficRefresherStopBlock)(void);


@interface TTNaviEngineTrafficRefresher : NSObject
// 请求间隔，默认一分钟
@property (nonatomic, assign) NSTimeInterval timeInterval;
@property (nonatomic, assign) TTRouteSceneType sceneType;
// 用于请求刷新的路线数据
@property (nonatomic, strong) QRouteResultForDrive *routeResult;
@property (nonatomic, copy) TTNaviEngineTrafficRefresherCompletionBlock completionBlock;
@property (nonatomic, copy) TTNaviEngineTrafficRefresherStopBlock conditionBlock;

- (void)start;
- (void)forceRefresh;
- (void)stop;

- (NSInteger)remainingSeconds:(NSDictionary *)responseDic route:(QRouteForDrive *)route;

@end


NS_ASSUME_NONNULL_END
