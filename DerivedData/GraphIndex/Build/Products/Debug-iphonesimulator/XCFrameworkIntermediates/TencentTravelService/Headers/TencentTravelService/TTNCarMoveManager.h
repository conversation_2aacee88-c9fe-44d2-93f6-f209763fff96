//
//  TTNCarMoveManager.h
//  TencentTravelService
//
//  Created by co<PERSON><PERSON> on 2022/8/31.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TTCommonDefine.h"
#import "TTCarMoveDefine.h"

NS_ASSUME_NONNULL_BEGIN

@class TMNMapView, TTOrderStatusInfo;
@interface TTNCarMoveManager : NSObject

@property (nonatomic, strong) NSDictionary *routeParam;
/**
 算路并显示路线
 
 @param tmMapView 要操作的底图
 @param mapId 底图唯一id
 @param param 参数
 @param completionBlock 完成block
 */
- (void)searchAndShowRouteWithMapView:(TMNMapView *)tmMapView
                                mapId:(NSNumber *)mapId
                                param:(NSDictionary *)param
                      completionBlock:(nullable TTTaxiRouteSearchCompletionBlock)completionBlock
                          cancelBlock:(nullable TTCancelBlock)cancelBlock;

/**
 更新当前车的位置
 
 @param tmMapView 要操作的底图
 @param mapId 底图唯一id
 @param param 参数
 @return 操作是否成功
 */
- (BOOL)updateCarLocationWithMapView:(TMNMapView *)tmMapView
                               mapId:(NSNumber *)mapId
                               param:(NSDictionary *)param;

/**
 清除当前客户端存储的路线等信息
 
 @param tmMapView 要操作的底图
 @param mapId 底图唯一id
 @return 操作是否成功
 */
- (BOOL)clearCarMoveInfoWithMapView:(TMNMapView *)tmMapView mapId:(NSNumber *)mapId;

/**
 将特定 markers 包含进最佳视野
 
 @param tmMapView 要操作的底图
 @param mapId 底图唯一id
 @param param 参数
 @return 操作是否成功
 */
- (BOOL)includeRouteMarkersWithMapView:(TMNMapView *)tmMapView
                                 mapId:(NSNumber *)mapId
                                 param:(NSDictionary *)param;

/**
 更新订单信息
 
 @param tmMapView 要操作的底图
 @param mapId 底图唯一id
 @param orderStatusInfo 订单状态信息
 @return 操作是否成功
 */
- (BOOL)updateOrderState:(TMNMapView *)tmMapView mapId:(NSNumber *)mapId orderStatusInfo:(TTOrderStatusInfo *)orderStatusInfo;

/**
 获取路况信息
 @return leftDistance 剩余距离 单位米；leftTime 剩余时间 单位秒
 **/
- (NSDictionary *)getTrafficInfo:(TMNMapView *)tmMapView;

/**
 设置路线相关参数
 @return 操作是否成功
 **/
- (BOOL)carMoveSetRouteParam:(NSDictionary *)param
                       mapId:(NSNumber *)mapId;


/**
 客户端根据当前订单状态强制刷新最佳视野 到默认值
**/
- (void)autoAdjustBestViewToDefault:(TMNMapView *)tmMapView;

/// 更新小车气泡ETA信息
/// - Parameters:
///   - tmMapView: <#tmMapView description#>
///   - mapId: <#mapId description#>
///   - param: <#param description#>
- (BOOL)updateCarEtaInfos:(TMNMapView *)tmMapView mapId:(NSNumber *)mapId param:(NSDictionary *)param;

// 获取订单状态
- (TTOrderState)orderState;

@end

NS_ASSUME_NONNULL_END
