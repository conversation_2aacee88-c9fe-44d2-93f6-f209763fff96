//
//  TTCommonBridge.h
//  TencentTravelService
//
//  Created by 33 on 2019/7/11.
//

#import <Foundation/Foundation.h>
#import "TTCommonDefine.h"

@class CLLocation;
@protocol TTCommonBridgeProtocol <NSObject>
@optional

/**
 是否允许司乘同显调试

 @discussion 如果允许，轨迹回放状态，会使用回放的点更新司机位置;
             非轨迹回放状态，会将后台下发的司机位置写文件，方便调试
             
 */
- (BOOL)enableCarMoveDebug;

/**
 司机位置写文件
 */
- (BOOL)appendCarMoveLocation:(nullable CLLocation *)location;


/**
司机位置prepare to写文件
*/
- (void)startRecordGSPWithFileName:(NSString *_Nullable)fileName;

/**
司机位置end写文件
*/
- (void)stopRecordGPS;


/// 几个点未吸附成功，则判定为偏航
- (NSInteger)outWayCount;

@end

NS_ASSUME_NONNULL_BEGIN

@interface TTCommonBridge : NSObject
+ (instancetype)shareInstance;

@property (nullable, nonatomic, weak) id<TTCommonBridgeProtocol> delegate;
@end

NS_ASSUME_NONNULL_END
