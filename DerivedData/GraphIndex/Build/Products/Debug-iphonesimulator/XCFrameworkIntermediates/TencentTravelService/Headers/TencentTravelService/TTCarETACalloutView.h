//
//  TTCarETACalloutView.h
//  TencentTravelService
//
//  Created by 33 on 2019/6/5.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "TTCarMoveDefine.h"
#import "TTCommonDefine.h"
#import <QMapMiddlePlatform/QMMapRefactorUtils.h>
#import <QMapMiddlePlatform/DMMapView+QMMiddlePlatform.h>
#import <QMapMiddlePlatform/DMHippyMapViewStack.h>

NS_ASSUME_NONNULL_BEGIN

@class TTCarMovePoint;
@class QRouteForDrive;
@class TTOrderStatusInfo;
extern const NSInteger TTCarETACalloutViewTag;

@interface TTCarETACalloutView : NSObject
@property (nonatomic, readonly) CGFloat angle;
@property (nonatomic, readonly) CLLocationCoordinate2D center;

@property (nonatomic, strong, nullable) DMMarkerElement *carMarker;
@property (nonatomic, strong, nullable) DMMarkerElement *ETACalloutMarker;

@property (nonatomic, assign) BOOL forceUserDefaultText;    //强制使用默认文案

// 创建/更新小车和气泡
- (void)updateCarCalloutViewOnDMapView:(DMMapView *)mapView
                    carMarker:(DMMarkerElement *)carMarker
                calloutMarker:(DMMarkerElement *)calloutMarker
                          calloutText:(nullable NSString *)calloutText;

// 更新小车和气泡的位置
- (BOOL)updateCarETACalloutPositionWithPoint:(TTCarMovePoint *)point
                                      mapView:(DMMapView *)mapView
                            carMoveCompletion:(void (^)(BOOL finished))carMoveCompletion;

- (void)updateCarViewOnMap:(DMMapView *)mapView
                     route:(QRouteForDrive *)route
           orderStatusInfo:(TTOrderStatusInfo *)orderStatusInfo;

// 刷新ETA
- (void)freshETAWithRoute:(QRouteForDrive *)route 
          orderStatusInfo:(TTOrderStatusInfo *)orderStatusInfo
      waitingRedLightDesc:(NSString *)waitingRedLightDesc;

- (void)restoreETAInfosWithRoute:(QRouteForDrive *)route
                 orderStatusInfo:(TTOrderStatusInfo *)orderStatusInfo
             waitingRedLightDesc:(NSString *)waitingRedLightDesc;

// 移除小车和气泡
- (void)removeCarETACalloutViewFrom:(DMMapView *)mapView;

// 应用即将进入前台
- (void)willAppEnterForeground;

// 应用即将切后台
- (void)willAppEnterBackground;

// 更新传递下来的参数
- (void)updateCarParam:(NSDictionary *)dictionry;

- (BOOL)updateCarETAInfosWithRoute:(QRouteForDrive *)route 
                   orderStatusInfo:(TTOrderStatusInfo *)orderStatusInfo
                             infos:(NSDictionary *)infos;
    
@end

NS_ASSUME_NONNULL_END
