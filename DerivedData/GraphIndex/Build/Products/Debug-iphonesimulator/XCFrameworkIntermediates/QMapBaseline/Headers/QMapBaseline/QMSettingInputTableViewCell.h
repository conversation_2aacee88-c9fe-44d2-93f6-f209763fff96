//
//  QMSettingInputTableViewCell.h
//  SOSOMap
//
//  Created by 王中周 on 2020/7/13.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "QMSettingTableViewCell.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMSettingInputTableValueKey;

@class QMSettingInputTableViewCell;
@protocol QMSettingInputTableViewCellDelegate <NSObject>
@optional
- (void)inputCell:(QMSettingInputTableViewCell *)cell textDidChange:(NSString *)text;
- (BOOL)textFieldShouldReturn:(UITextField *)textField;
- (BOOL)textFieldShouldBeginEditing:(UITextField *)textField;

@end

@interface QMSettingInputTableViewCell : QMSettingTableViewCell

@property (nonatomic, weak) id<QMSettingInputTableViewCellDelegate> delegate;

- (void)updateCellWithRow:(QMTableViewRow *)row;

@end

NS_ASSUME_NONNULL_END
