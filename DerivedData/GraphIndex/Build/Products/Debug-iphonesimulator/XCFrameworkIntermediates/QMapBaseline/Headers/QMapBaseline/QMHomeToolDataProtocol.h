//
//  QMHomeToolDataProtocol.h
//  SOSOMap
//
//  Created by wyh on 2020/12/23.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapProto/QMJCE_MapTools_Group.h>
#import <QMapProto/QMJCE_MapTools_Tool.h>


NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXTERN NSUInteger const QMH_MAX_RECENTGROUP_COUNT;
FOUNDATION_EXTERN NSString * const QMH_TOOL_LOG_DOMAIN;
FOUNDATION_EXTERN NSUInteger const QMH_EACH_ROW_NUMBER;
FOUNDATION_EXTERN NSUInteger const QMH_TOOL_RECENT_INDEX;
FOUNDATION_EXTERN NSString * const QMH_RECENT_TOOL_NAME;


typedef NS_ENUM(NSUInteger, QMHomeToolJumpType) {
    QMHomeToolJumpTypeUnknown = 0,
    QMHomeToolJumpType_QQMap,
    QMHomeToolJumpType_SupplierAlert,
};

typedef NS_ENUM(NSUInteger, QMHomeToolDotType) {
    QMHomeToolDotTypeNone = 0,
    QMHomeToolDotTypeRedDot,
    QMHomeToolDotTypeRedText,
    QMHomeToolDotTypeRichMediaImage,
    QMHomeToolDotTypeLottie,
    QMHomeToolDotTypePag,
    
    // local edit type below:
    QMHomeToolDotTypeAdded,
    QMHomeToolDotTypeWaitAdd,
    QMHomeToolDotTypeDelete,
};

@interface QMHomeToolData_Jump : NSObject <NSCoding>

@property (nonatomic, copy) NSString *url;

@property (nonatomic, assign) QMHomeToolJumpType type;

@property (nonatomic, strong) id data;


- (instancetype)initWithJCE:(QMJCE_MapTools_Jump *)jce;

@end

@interface QMHomeToolData_RemindDot : NSObject <NSCoding>

@property (nonatomic, assign) QMHomeToolDotType type;

@property (nonatomic, assign) long long createTime;

@property (nonatomic, copy) NSString *value;

@property (nonatomic, copy) NSString *imageURL;

@property (nonatomic, copy) NSString *keyName;

- (instancetype)initWithJCE:(QMJCE_MapTools_Remind *)jce;

@end

@interface QMHomeToolData : NSObject <NSCoding>

@property (nonatomic, copy) NSString *key;

@property (nonatomic, copy) NSString *title;

@property (nonatomic, copy) NSString *normalIconURL;
@property (nonatomic, copy) NSString *selectedIconURL;
@property (nonatomic, copy) NSString *image3DIconURL;
@property (nonatomic, copy) NSString *emotionalCustomAnimationURL;  //情感化配置动画URL
@property (nonatomic, copy) NSString *emotionalCustomIconURL;       //情感化配置ICON URL
@property (nonatomic, copy) NSString *emotionalCustomBgIconURL;     //情感化配置背景URL
@property (nonatomic, copy) NSString *lottieIconURL;

// darkmode icons
@property (nonatomic) NSString *darkNormalIconUrl;
@property (nonatomic) NSString *darkSelectedIconUrl;
@property (nonatomic) NSString *darkLottieIconUrl;
@property (nonatomic) NSString *dark3DIconUrl;

@property (nonatomic, copy) NSString *normalLocalImageName;
@property (nonatomic, copy) NSString *selectedLocalImageName;

@property (nonatomic, strong) QMHomeToolData_Jump *jump;

@property (nonatomic, assign) BOOL isShow3D;

@property (nonatomic, assign) BOOL isCheckLogin;

@property (nonatomic, strong) QMHomeToolData_RemindDot *remind;

@property (nonatomic, assign) BOOL isSupport;

@property (nonatomic, copy) NSString *bucketID;

@property (nonatomic, copy) NSString *requestId;

@property (nonatomic, copy) NSString *bgColor;

@property (nonatomic, assign) BOOL isShowBgColor;
/// 932版本需要添加位置埋点（列表中的index）
@property (nonatomic) NSIndexPath *tagIndex;

@property (nonatomic) NSInteger index; //提供给hippy index，从1开始

- (void)parseFromJCE:(QMJCE_MapTools_Tool *)jce;

@end

@interface QMHomeToolGroupObject : NSObject

@property (nonatomic, assign) BOOL isRecentGroup;

@property (nonatomic, copy) NSString *name;

@property (nonatomic, copy) NSArray<QMHomeToolData *> *tools;

- (void)removeObjectWithToolKey:(NSString *)toolKey;
- (void)removeObjectAtIndex:(NSUInteger)index;
- (void)insertObject:(QMHomeToolData *)object atIndex:(NSUInteger)index;
- (void)addObject:(QMHomeToolData *)object;

- (BOOL)isContainToolKey:(NSString *)toolKey;

- (NSArray<NSString *>*)getAllToolKeys;

@end

@interface QMHomeToolSupplierEachColumn : NSObject

@property (nonatomic, copy) NSString *name;

@property (nonatomic, copy) NSString *icon;

@property (nonatomic, copy) NSString *url;

@property (nonatomic, copy) NSString *content;

@end

@interface QMHomeToolSupplierObject : NSObject

@property (nonatomic, copy) NSString *name;

@property (nonatomic, copy) NSString *icon;

@property (nonatomic, copy) NSString *url;

@property (nonatomic, copy) NSArray<QMHomeToolSupplierEachColumn *> *columns;

- (instancetype)initWithJCE:(QMJCE_MapTools_Supplier *)jce;

@end


@interface QMHomeToolDataParser : NSObject

+ (QMHomeToolGroupObject *)parseToGroupObjcFromJCE:(QMJCE_MapTools_Group *)jce;

@end

@interface QMHomeToolTestDarkImager : NSObject

+ (UIImage *)getDarkImageWithToolKey:(NSString *)toolkey;

@end

NS_ASSUME_NONNULL_END
