//
//  QMNHippyBridger_WalkCycleMultiCard.h
//  QMapBaseline
//
//  Created by hangjiang on 2022/12/6.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapHippy/QMNHippyBridgerFactory.h>

NS_ASSUME_NONNULL_BEGIN

@class QMNHippyBridger_CycleMultiCard;

@protocol QMNHippyBridgerCycleMultiCardPosting <NSObject>

@optional
- (void)bridger:(QMNHippyBridger_CycleMultiCard * _Nonnull)bridger cycleCardListHeightWillUpdate:(CGFloat)totalHeight;

- (NSDictionary *_Nonnull)bridgerGetScrollParameters:(QMNHippyBridger_CycleMultiCard * _Nonnull)bridger;

@end

@interface QMNHippyBridger_CycleMultiCard : QMNHippyBaseBridger

@property (nonatomic, strong, readonly) TMHippyViewBox *hippyBox;

@property (nonatomic, assign, readonly) CGFloat cardHeight;

- (void)callbackCardHeightWillUpdate:(CGFloat)cardHeight;
/// 路线方案切换通知 hippy 接口
- (void)dispatchToHippyOnSchemeChanged;

@end

NS_ASSUME_NONNULL_END
