//
//  QMFavoriteTableViewCell.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/6/10.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN



@protocol QMFavoriteTableViewCellDelegate <NSObject>

- (void)didSelectMoreButtonWith:(id)info;

@end

@interface QMFavoriteTableViewCell : UITableViewCell


@property (nonatomic, weak) id<QMFavoriteTableViewCellDelegate> delegate;
@property (nonatomic) BOOL isEditingState;
@property (nonatomic) BOOL isLastCell;
@property (nonatomic) BOOL isFirstCell;
@property (nonatomic, strong, nullable) id info;
@property (nonatomic) BOOL hideMoreButton;
@property (nonatomic) BOOL hideTagLabel;

- (void)hideBottomLineImageView:(BOOL)hide;
- (void)setDataAccess;
- (void)setKeyword:(NSString *)keyword;

@end

NS_ASSUME_NONNULL_END
