//
//  QMFloatLayerDefine.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/8/10.
//  Copyright © 2020 Tencent. All rights reserved.
//

#ifndef QMFloatLayerDefine_h
#define QMFloatLayerDefine_h

#define QMFloatLayerWidth (QM_SV(260.0))

#define QMFloatLayerItemNum (3)
#define QMFloatLayerItemLeftMargin (20)
#define QMFloatLayerItemTopMargin (24)
#define QMFloatLayerItemMargin (14)
#define QMFloatLayerItemTitleMargin (10)
#define QMFloatLayerItemTitleHeight (14)
#define QMFloatLayerItemBottomMargin (32)
#define QMFloatLayerItemWidth \
    (((QMFloatLayerWidth - 2 * QMFloatLayerItemLeftMargin) - (QMFloatLayerItemNum - 1) * QMFloatLayerItemMargin) / QMFloatLayerItemNum)
#define QMFloatLayerItemIconHeight (QMFloatLayerItemWidth * 48 / 64.0)
#define QMFloatLayerItemHeight \
    (QMFloatLayerItemTopMargin + QMFloatLayerItemIconHeight + QMFloatLayerItemTitleMargin \
    + QMFloatLayerItemTitleHeight + QMFloatLayerItemBottomMargin)
#define QMFloatLayerMapDisplayItemHeight (QM_SV(48.0) + QMFloatLayerItemTitleMargin + QMFloatLayerItemTitleHeight)
#define QMFloatLayerItemSelectedBorderColor (QMColorHex(0x0090FF))
#define QMFloatLayerItemSelectedBorderWidth (1.5)
#define QMFloatLayerAngleItemWidth (QMFloatLayerWidth - 2 * QMFloatLayerItemLeftMargin)
#define QMFloatLayerAngleItemMargin (5)
#define QMFloatLayerAngleItemBtnWidth (QMFloatLayerAngleItemWidth * .5 - 2 * QMFloatLayerAngleItemMargin)
#define QMFloatLayerAngleItemBtnHeight (30)
#define QMFloatLayerAngleItemBtnContentHeight (QMFloatLayerAngleItemBtnHeight + 2 * QMFloatLayerAngleItemMargin)
#define QMFloatLayerAngleItemHeight (QMFloatLayerAngleItemBtnContentHeight + 2 * QMFloatLayerItemLeftMargin)


typedef NS_ENUM(NSUInteger, QMFloatLayerMapType) {
    QMFloatLayerMapTypeNormal,
    QMFloatLayerMapTypeSatellite,
    QMFloatLayerMapTypeBus,
};

typedef NS_ENUM(NSUInteger, QMFloatLayerMapDisplayType) {
    QMFloatLayerMapDisplayTypeTraffice,                 //路况
    QMFloatLayerMapDisplayTypeFavorite,                 //收藏
    QMFloatLayerMapDisplayTypeScenicSpot,               //景区手绘图
    QMFloatLayerMapDisplayTypeTravel,                   //旅游全景图
    QMFloatLayerMapDisplayTypeTrafficEvents,            //路况事件
    QMFloatLayerMapDisplayTypeDynamicEvent,             //动态事件
    QMFloatLayerMapDisplayTypeYoungFashion,             //年轻潮玩
    QMFloatLayerMapDisplayTypeDiscountService,          //优惠服务
    QMFloatLayerMapDisplayTypeHotActivity,              //热点事件
    QMFloatLayerMapDisplayTypeThemeArea,                //主题图层
    QMFloatLayerMapDisplayTypeTree,                     //树木植被
    QMFloatLayerMapDisplayTypeTerrain,                  //地形地貌
    QMFloatLayerMapDisplayTypeEarth,                    //3D地球
    QMFloatLayerMapDisplayTypeFootprint,                //个人足迹
    QMFloatLayerMapDisplayTypeFromWeChatPoint,          //来自微信
};

typedef NS_ENUM(NSUInteger, QMFloatLayerFromType) {
    QMFloatLayerFromTypeNone,
    QMFloatLayerFromTypeHomePage,
    QMFloatLayerFromTypeSearchResult,
};

#endif /* QMFloatLayerDefine_h */
