//
//  QMMapFloatLayerBaseCollectionViewCell.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/6/10.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "QMFloatLayerTableCellDefine.h"
#import "QMFloatLayerButton.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMMapFloatLayerBaseCollectionViewCell : UICollectionViewCell

@property (nonatomic, strong) QMFloatLayerButton *button;
@property (nonatomic, assign) QMFloatLayerCellTargetType targetType;
@property (nonatomic, weak) id<QMFloatLayerCollectionViewCellDelegate> cellDelegate;

- (void)updateData:(NSDictionary *)data;
- (void)updateSelectedStatus;
- (void)buttonDidClicked:(QMFloatLayerButton *)button;
@end

NS_ASSUME_NONNULL_END
