//
//  QMLaunchStatManager.h
//  QMapBusiness
//
//  Created by allensun on 2021/1/27.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMLaunchStatManager : NSObject

QMDefaultManager_H(QMLaunchStatManager)

/**
 统计 < iOS 13 的冷启动
 */
- (void)statLaunchWithOptions:(NSDictionary *)options;

/**
 统计 ≥ iOS 13 的冷启动
 */
- (void)statLaunchWithSceneConnectionOptions:(UISceneConnectionOptions *)options API_AVAILABLE(ios(13.0));

/**
 统计切换前台
 */
- (void)statEnterForeground;

/**
 统计其他渠道
 */
- (void)statReference:(NSString *)reference;

/**
 重置上报生命周期
 */
- (void)resetReportLifecycle;

@end

NS_ASSUME_NONNULL_END
