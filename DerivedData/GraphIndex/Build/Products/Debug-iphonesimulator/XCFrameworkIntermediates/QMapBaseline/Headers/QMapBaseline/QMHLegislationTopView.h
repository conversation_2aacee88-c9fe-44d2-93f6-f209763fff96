//
//  QMHLegislationTopView.h
//  QMapBusiness
//
//  Created by wyh on 2021/8/23.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

extern CGFloat const QMHLegislationTopViewFitSizeHeight;

//枚举
typedef NS_ENUM(NSInteger, QMLegislationSourceType) {
    QMLegislationSourceTypeNone = 0,
    QMLegislationSourceTypePlaceDetailVC = 1,
    QMLegislationSourceTypeFavouriteVC = 2,
    QMLegislationSourceTypeCommuteSettingVC = 3,
    QMLegislationSourceTypeCompanySetSheetView = 4,
    QMLegislationSourceTypeHomeSetSheetView = 5,
    QMLegislationSourceTypeMyTrack = 6,
};

@interface QMHLegislationTopView : UIView

@property (nonatomic, assign) QMLegislationSourceType sourceType;
@property (nonatomic, assign) BOOL isBlueBackground;
- (void)renderSwitchState;

- (void)updateStatue;

@end

NS_ASSUME_NONNULL_END
