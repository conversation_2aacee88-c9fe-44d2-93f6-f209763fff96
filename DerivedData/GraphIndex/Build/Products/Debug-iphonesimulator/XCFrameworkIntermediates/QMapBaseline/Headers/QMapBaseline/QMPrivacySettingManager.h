//
//  QMPrivacySettingManager.h
//  QMapBusiness
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/9/29.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMPrivacySettingManager : NSObject

QMDefaultManager_H(QMPrivacySettingManager)

+ (void)initWhenApplicationDidFinishLaunch;

// 上报个性化推荐设置
- (void)reportPersonalizedRecommondSetting;

// 扩充个性化推荐列表
- (void)expandPersonalizedRecommondListByApollo;

// 获取个性化列表
- (NSArray *)getArrayData;

// 获取个性化列表键值对，值为0或1
- (NSDictionary *)getPrivacyModes;

@end

NS_ASSUME_NONNULL_END
