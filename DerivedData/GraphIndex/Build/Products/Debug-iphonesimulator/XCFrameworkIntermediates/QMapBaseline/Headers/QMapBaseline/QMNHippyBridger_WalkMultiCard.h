//
//  QMNHippyBridger_WalkMultiCard.h
//  QMapBaseline
//
//  Created by hangjiang on 2023/1/11.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <QMapHippy/QMNHippyBridgerFactory.h>

NS_ASSUME_NONNULL_BEGIN

@class QMNHippyBridger_WalkMultiCard;

@protocol QMNHippyBridgerWalkMultiCardPosting <NSObject>

@optional
- (void)bridger:(QMNHippyBridger_WalkMultiCard * _Nonnull)bridger walkCardListHeightWillUpdate:(CGFloat)totalHeight;

@end

@interface QMNHippyBridger_WalkMultiCard : QMNHippyBaseBridger

@property (nonatomic, strong, readonly) TMHippyViewBox *hippyBox;

@property (nonatomic, assign, readonly) CGFloat cardHeight;

- (void)callbackCardHeightWillUpdate:(CGFloat)cardHeight;

/// 路线方案切换通知 hippy 接口
- (void)dispatchToHippyOnSchemeChanged;

@end

NS_ASSUME_NONNULL_END
