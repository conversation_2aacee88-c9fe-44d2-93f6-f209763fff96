//
//  QMFloatLayerGuideModel.h
//  QMapBaseline
//
//  Created by hangjiang on 2023/8/15.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <QMapFoundation/QMModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMFloatLayerGuideModel : QMModel

/// 图片或pag链接
@property (nonatomic) NSString *icon;

/// 配置key，保证唯一
@property (nonatomic) NSString *key;

/// 每日展示次数上限
@property (nonatomic) NSInteger dayFrequency;

/// 累计展示次数上限
@property (nonatomic) NSInteger totalFrequency;

@property (nonatomic) NSInteger repeatCount;

@property (nonatomic) NSInteger delayTime;

/// 数据是否可用
- (BOOL)isDataValid;

/// 本次能否展示
- (BOOL)canShow;

/// 展示记录
- (void)didShow;

/// 点击记录
- (void)didClick;

@end

NS_ASSUME_NONNULL_END
