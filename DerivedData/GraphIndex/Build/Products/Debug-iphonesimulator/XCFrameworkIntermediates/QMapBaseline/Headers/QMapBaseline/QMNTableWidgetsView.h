//
//  QMNTableWidgetsView.h
//  QMapBaseline
//
//  Created by leon<PERSON><PERSON><PERSON> on 2023/7/25.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMNTableWidgetsView : UIView

@property (class, nonatomic, readonly) CGFloat contentWidth; // 38.0
@property (class, nonatomic, readonly) CGFloat cellHeight; // 31.0
@property (class, nonatomic, readonly) CGFloat horizentalPadding; // 10.0
@property (class, nonatomic, readonly) CGFloat verticalPadding; // 10.0

@property (nonatomic, readonly) UIButton *topButton;
@property (nonatomic, readonly) UIButton *switchButton;
@property (nonatomic, readonly) UITableView *tableView;

// 设计同学要求单个cell时上下渐变的效果不展示
- (void)refreshUIWithCellCount:(NSInteger)count;
+ (CGFloat)viewHeightWithCellCount:(NSInteger)count;

@end

NS_ASSUME_NONNULL_END
