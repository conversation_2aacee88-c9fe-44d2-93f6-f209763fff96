//
//  QMHTopCardProtocol.h
//  QMapBusiness
//
//  Created by wyh on 2021/9/27.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString * const QMHTopCardLogDomain;

extern NSString * _Nonnull const QMHTopCardApperanceUpdateNotification;
extern NSString * _Nonnull const QMHTopCardDataUpdateNotification;

extern NSTimeInterval const QMHTopCardDefaultShowAnimationDuration;
extern NSTimeInterval const QMHTopCardDefaultDismissAnimationDuration;
extern CGFloat const QMHTopCardDefaultHeight;
extern CGFloat const QMHTopCardButtonWidth;

typedef NS_ENUM(NSUInteger, QMHTopCardType) {
    QMHTopCardType_Unknown,
    QMHTopCardType_Commute = 1, // 通勤
    QMHTopCardType_Bus, // 公交
    QMHTopCardType_COperation, // C位运营
};

typedef NS_ENUM(NSUInteger, QMHTopCardJumpType) {
    QMHTopCardJumpTypeUnknown,
    QMHTopCardJumpTypeQQMap = 1,
    QMHTopCardJumpTypeH5URL,
};

@interface QMHTopCardBaseData : NSObject

@property (nonatomic, strong) id data;

@property (nonatomic, assign) QMHTopCardType type;

- (instancetype)initWithJSON:(NSDictionary *)json;

@end

@interface QMHTopCardDetailData_Commute : NSObject

@property (nonatomic, copy) NSString *eta;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *descriptionText;
@property (nonatomic, copy) NSString *goURL; // qqmap
@property (nonatomic, copy) NSString *buttonTitle;

- (instancetype)initWithJSON:(NSDictionary *)json;

@end


@interface QMHTopCardDetailData_BusETAItem : NSObject

@property (nonatomic, assign) BOOL isShow;
@property (nonatomic, copy) NSString *text;

@end

@interface QMHTopCardDetailData_Bus : NSObject

@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *busStationName;
@property (nonatomic, copy) NSArray<QMHTopCardDetailData_BusETAItem *> *etaInfos;
@property (nonatomic, assign) BOOL isSupportBusCode;
@property (nonatomic, copy) NSString *buttonTitle;

- (instancetype)initWithJSON:(NSDictionary *)json;

@end

@interface QMHTopCardDetailData_Jump : NSObject

@property (nonatomic, assign) QMHTopCardJumpType type;

@property (nonatomic, copy) NSString *url;

@end

@interface QMHTopCardDetailData_ImageInfo : NSObject

// support lottie / PAG / PNG
@property (nonatomic, copy) NSString *url;

@property (nonatomic, assign) NSInteger width;

@property (nonatomic, assign) NSInteger height;

@property (nonatomic, strong) QMHTopCardDetailData_Jump *action;

@end

@interface QMHTopCardDetailData_COperation : NSObject

@property (nonatomic, strong) QMHTopCardDetailData_ImageInfo *bgImage;

@property (nonatomic, strong) QMHTopCardDetailData_ImageInfo *button;

@property (nonatomic, strong) QMHTopCardDetailData_ImageInfo *close;

@property (nonatomic, strong) QMHTopCardDetailData_ImageInfo *lottie;

@property (nonatomic, assign) CGFloat offset;

@property (nonatomic, copy) NSString *cardName;

- (instancetype)initWithJSON:(NSDictionary *)json cardName:(NSString *)cardName;

@end

@protocol QMHTopCardOptimizeProtocol <NSObject>
- (void)protocolCreateZeroCard;
- (void)protocolDestroyZeroCard;
- (BOOL)protocolGetZeroCardState;

- (void)protocolCreateZeroVLCard:(NSDictionary *)param;
- (void)protocolDestroyZeroVLCard;
- (void)protocolUpdateVLData:(NSDictionary *)dic;
- (BOOL)protocolVLCardExist;
@end
NS_ASSUME_NONNULL_END
