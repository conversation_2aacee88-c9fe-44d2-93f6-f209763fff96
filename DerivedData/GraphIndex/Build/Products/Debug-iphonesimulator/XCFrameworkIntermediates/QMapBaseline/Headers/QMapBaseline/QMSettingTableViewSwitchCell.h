//
//  QMSettingTableViewSwitchCell.h
//  SOSOMap
//
//  Created by p<PERSON><PERSON><PERSON> on 2019/10/1.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import "QMSettingTableViewCell.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const QMSettingTableSwitchValueKey;

@protocol QMSettingTableViewSwitchCellDelegate;

@interface QMSettingTableViewSwitchCell : QMSettingTableViewCell

@property (nonatomic, weak) id<QMSettingTableViewSwitchCellDelegate> switchDelegate;

@end

@protocol QMSettingTableViewSwitchCellDelegate <NSObject>
@optional

/// cell的switch选中值发生变化
/// @param cellRowType 唯一标示当前cell的id
/// @param isOn switch是否打开
- (void)switchCell:(NSInteger)cellRowType switchClicked:(BOOL)isOn;

- (void)switchCell:(QMSettingTableViewSwitchCell *)cell valueChanged:(BOOL)value;

@end

NS_ASSUME_NONNULL_END
