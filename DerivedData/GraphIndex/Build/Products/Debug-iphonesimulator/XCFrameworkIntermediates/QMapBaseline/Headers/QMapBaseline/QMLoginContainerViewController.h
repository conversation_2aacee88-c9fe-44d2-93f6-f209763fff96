//
//  QMLoginContainerViewController.h
//  SOSOMap
//
//  Created by vector on 2020/1/19.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "QMHippyCommonProtocol.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * QMLoginContainerViewController 适用于以下场景：
 * 某个业务依赖账号体系，在业务界面展示之前需要判断是否登录账号，
 * 若已登录，展示业务界面；若未登录，则弹出登录界面，登录成功后再进入业务界面。
 */
@interface QMLoginContainerViewController : QMViewController<QMHippyCommonProtocol>

/**
 * 登录 view controller
 */
@property (nonatomic, strong, readonly) QMLoginViewController *loginViewController;

/**
 * 业务 view controller
 */
@property (nonatomic, strong, readonly) QMViewController *businessViewController;

/**
 * 登录界面展示文案
 */
@property (nonatomic, copy, readonly) NSString *loginWording;

/**
 * 生成容器 view controller
 *
 * @param businessViewController 业务 view controller
 * @param wording 登录界面展示的文案
 */
- (instancetype)initWithBusinessViewController:(__kindof QMViewController *)businessViewController loginWording:(NSString *)wording;

@end

NS_ASSUME_NONNULL_END
