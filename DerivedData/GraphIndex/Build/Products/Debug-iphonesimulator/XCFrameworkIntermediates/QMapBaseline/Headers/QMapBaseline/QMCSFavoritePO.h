//
//  QMCSFavoritePO.h
//  SOSOMap
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/8/16.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <QMapMiddlePlatform/QMCSTInterface.h>

NS_ASSUME_NONNULL_BEGIN

/// 收藏标签
@interface QMCSFavoriteLabelPO : QMCSBaseBPO

@property (nonatomic, copy) NSString *name;
@property (nonatomic) NSString *iconGroupId;
@property (nonatomic) NSString *iconSubId;
@property (nonatomic) NSString *iconUrl;
@property (nonatomic) NSString *iconDarkUrl;

@end

@interface QMCSFavoritePO : QMCSBaseBPO

/// 用户id，设备id或登录id
@property (nonatomic, copy) NSString *userId;

/// poi uid
@property (nonatomic, copy) NSString *uid;

/// 收藏名
@property (nonatomic, copy) NSString *name;

/// 收藏重命名
@property (nonatomic, copy) NSString *nickName;

/// 收藏地址
@property (nonatomic, copy) NSString *address;

/// poi纬度，不乘10的l6次方
@property (nonatomic, copy) NSString *lat;

/// poi经度，不乘10的l6次方
@property (nonatomic, copy) NSString *lng;

/// poi高度
@property (nonatomic, copy) NSString *altitude;

/// 建筑ID
@property (nonatomic, copy) NSString *buildingID;

/// 楼层ID
@property (nonatomic, copy) NSString *floorID;

/// 室内楼层
@property (nonatomic, copy) NSString *floorName;

/// sync接口返回的数据，此时只需要修改QMCloudSyncBaseItem里的内容
@property (nonatomic, assign) BOOL isSyncReturn;

@property (nonatomic, assign) NSInteger type;

/// 标签
@property (nonatomic, copy) NSArray *labelIds;

/// 看过
@property (nonatomic) BOOL hasView;

/// 是否打卡
@property (nonatomic) BOOL hasCheckIn;
/// poi 类型
@property (nonatomic) NSInteger mapPoiType;


@end

NS_ASSUME_NONNULL_END
