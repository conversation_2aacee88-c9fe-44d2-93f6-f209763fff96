//
//  QMHTopCardService.h
//  QMapBusiness
//
//  Created by wyh on 2021/10/1.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMHTopCardProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMHTopCardService : NSObject

@property (nonatomic, assign) BOOL shouldShow;
@property (nonatomic, weak) UIView<QMHTopCardOptimizeProtocol> *topCardView;
@property (nonatomic, assign) CGFloat topMapCardHeight;
@property (nonatomic, assign) CGFloat topMapCardOffset;
// push下发后给hippy端提供messageId
@property (nonatomic, copy) NSString *messageId;

+ (void)initWhenApplicationDidFinishLaunch;

+ (instancetype)defaultService;

- (void)showOrHideTopCardIfNeeded:(BOOL)canShow;

- (void)setNeedsShowOrHideTopCard:(BOOL)isShow;

- (void)updateTopCardData:(NSDictionary *)topCardData;

- (QMHTopCardBaseData *)getTopCardData;

- (void)setCardTypeCanNotShow:(QMHTopCardType)cardType;
- (NSDictionary *)getMapCardStatus:(NSDictionary *)param;

- (void)setTopMapCardData:(NSDictionary *)topMapCardData;

- (NSDictionary *)getTopMapCardData:(NSDictionary *)params;

- (void)setTopMapCardLayoutData:(NSDictionary *)params;

- (void)setNeedMapDragRefresh:(BOOL)need;

- (void)handleMapRegionChanged;

/// 创基及销毁零位卡的bundle及view
- (void)createZeroCardBundleAndView;
- (void)destroyZeroCardBundleAndView;
- (BOOL)getZeroCardBundleState;

/// 创建及销毁零位卡VLCard
- (void)createZeroCardVLCard:(NSDictionary *)param;
- (void)destroyZeroCardVLCard;

- (void)updateZeroCardVLCardData:(NSDictionary *)dic;
- (BOOL)zeroCardVLCardViewExist;
@end

NS_ASSUME_NONNULL_END
