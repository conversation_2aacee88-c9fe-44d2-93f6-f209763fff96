//
//  QMContinueNavigationView.h
//  QMapBaseline
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/15.
//  Copyright © 2024 Tencent. All rights reserved.

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface QMStableFunctionManager : NSObject
QMDefaultManager_H(QMStableFunctionManager);

// 功能回滚开关
+ (BOOL)canUseWithApollo;
@end

@interface QMContinueNavigationView : UIView


/// 弹出新的继续导航UI
/// - Parameters:
///   - view: 父视图
///   - title: 标题
///   - message: 地点
///   - cancelCallback: 取消按钮回调
///   - continueCallback: 继续导航按钮回调
+ (void)showInView:(UIView *)view 
             title:(NSString *)title
           message:(NSString *)message
       cancelBlock:(void(^)(void))cancelCallback
     continueBlock:(void(^)(void))continueCallback;

@end

@interface QMCrashFeedBackView : UIView
+ (void)show;
@end

NS_ASSUME_NONNULL_END
