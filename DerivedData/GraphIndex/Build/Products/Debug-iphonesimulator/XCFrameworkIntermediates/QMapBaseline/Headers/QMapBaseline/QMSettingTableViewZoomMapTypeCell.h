//
//  QMSettingTableViewZoomMapTypeCell.h
//  QMapBusiness
//
//  Created by 杨尔军 on 2021/6/4.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
@class QMSettingTableViewZoomMapTypeCell;
NS_ASSUME_NONNULL_BEGIN

@protocol QMSettingTableViewZoomMapTypeCellDelegate <NSObject>

- (void)zoomMapTypeCellDidChanged:(QMSettingTableViewZoomMapTypeCell *)zoomMapTypeCell selectFirst:(BOOL)selectFirst;

@end

@interface QMSettingTableViewZoomMapTypeCell : UITableViewCell

@property(nonatomic,weak) id<QMSettingTableViewZoomMapTypeCellDelegate> delegate;

- (instancetype)initWithTitle:(NSString *)title
                        items:(NSArray<NSString *> *)items
                  selectFirst:(BOOL)selectFirst
              reuseIdentifier:(NSString *)reuseIdentifier;

@end

NS_ASSUME_NONNULL_END
