//
//  QMIndoorMapHelper.h
//  SOSOMap
//
//  Created by lihuafeng on 15/11/26.
//  Copyright © 2015年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMIndoorFloorListView.h"
#import <QMapMiddlePlatform/JsonPOIInfo.h>

UIKIT_EXTERN NSString *const QMIndoorFloorSelectNeedsMoveMapRectNotification;

@class IndoorMapFloor, JsonPoiInfo;

extern const CGFloat kIndoorFloorCellHeight;

/**
 * 触发室内地图的回调
 */
@protocol IndoorMapDelegate;

/**
 * QMIndoorMapHelper
 *
 * 封装室内地图
 */
@interface QMIndoorMapHelper : NSObject <UITableViewDataSource, UITableViewDelegate>

/**
 * 室内地图是否被触发
 */
@property (nonatomic, assign)BOOL isOpenIndoorMap;

@property (nonatomic, weak) id<IndoorMapDelegate> delegate;

@property (nonatomic) NSIndexPath  *selectPath;

//记录上次poi点击所属室内ma值
@property (nonatomic, copy) NSString *selctPoiLastFloorInMa;

@property (nonatomic, strong) JsonPOIInfo *selectPoiInfo;
/**
 * 初始化
 */
-(void)initIndoorFloorTableView:(UITableView *) tableview mapBaseView:(DMMapView *)mapView;

/*
 * 地图移动时，查询视野内室内地图数据，并更新楼层控件
 */
-(void)queyIndoorMapInfo;

/*
 * 获取室内图显示的scale 统一返回 17： 跟阿波罗配置的取最大值
 * 原通过面积计算返回对应的 scale 逻辑保留
 */
- (int)getIndoorMapShowScale;

/*
 * 获取当前楼层控件选中的楼层id
 */
- (int)getCurrentSelectedFloorId;

/*
 * 楼层总数
 */
- (NSUInteger)totalFloors;

/*
 * 所有楼层
 */
- (NSArray <IndoorMapFloor *> *)allFloorList;

/*
 * 设置室内图绘制状态
 */
- (void)setOutdoorDrawEnable:(BOOL)draw;

//获取室内 类型
+ (QMIndoorFloorType)indoorFloorType:(TXInterestAreaInfo *)areaInfo;

/// 获取室内导览的完整url
+ (NSString *)indoorFloorMarketH5UrlWithGuid:(NSString *)guid;


@end


@protocol IndoorMapDelegate <NSObject, UIScrollViewDelegate>

/**
 */
@optional
-(void)onOpenIndoorMap:(BOOL)isOpen;

-(void)gotoIndoorGuide;

-(void)indoorHelp:(QMIndoorMapHelper *)helper selectIndoorFloorId:(NSInteger) floorId;

- (void)setIndoorDrawEnable:(BOOL)enable;

@end

/**
 * IndoorMapFloor
 *
 * 室内地图楼层结构体
 */
@interface IndoorMapFloor : NSObject

/**
 * 楼层名字
 */
@property(nonatomic, strong)NSString * name;

/**
 * 楼层ID，从0开始
 */
@property(nonatomic, assign)int floorID;

@end


@interface InddorMapParkData : NSObject

@property (nonatomic, copy) NSString *parkId;
@property (nonatomic, copy) NSString *colors;

@end
