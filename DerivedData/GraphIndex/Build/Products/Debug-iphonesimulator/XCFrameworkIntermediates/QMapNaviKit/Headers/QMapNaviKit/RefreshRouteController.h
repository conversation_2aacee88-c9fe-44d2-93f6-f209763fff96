//
//  RefreshRouteController.h
//  QMapNaviKit
//
//  Created by Bruce<PERSON><PERSON> on 2018/4/8.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import "AdjointCommon.h"
#import "CarRouteOnlineSearcher.h"
#import "NaviModuleProtocols.h"
#import "RouteSearchChoiceProtocol.h"
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

extern NSString *const QMManualRefreshRouteTipsDismissNotificationName;

@interface RefreshRouteController : NSObject<AdjointRouteControllerProtocol>

@property (nonatomic, strong, readonly) UIButton *refreshButton;
@property (nonatomic, assign) BOOL isVoiceOn;
@property (nonatomic, weak) id<RouteSearchChoiceProtocol> routeSearchChoiceDelegate;

- (void)updateRefreshButton:(BOOL)isNight;
- (void)destroy;
- (void)refreshButtonClickEventWithKind:(NSString *)kind aiRouteInfo:(NSString *)aiRouteInfo;
- (void)doRequest:(QDriveRouteReqParam *)searchParameter;

@end
