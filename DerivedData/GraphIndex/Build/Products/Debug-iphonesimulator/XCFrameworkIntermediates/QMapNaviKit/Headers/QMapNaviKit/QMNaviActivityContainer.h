//
//  QMNaviActivityContainer.h
//  SOSOMap
//
//  Created by 刘宁 on 2018/11/15.
//  Copyright © 2018 Tencent. All rights reserved.
//

#import "QMPassthroughView.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^QMNaviActivityButtonRotateBlock)(void);

@interface QMNaviActivityContainer : QMPassthroughView
@property (nonatomic, weak) IBOutlet UIButton *redPackegButton;
@property (nonatomic, weak) IBOutlet UIImageView *tipsBgImageView;
@property (nonatomic, weak) IBOutlet UIView *tipsView;
@property (nonatomic, weak) IBOutlet UILabel *countLabel;
@property (nonatomic, weak) IBOutlet UILabel *tipsLabel;
@property (nonatomic, weak) IBOutlet UIView *countView;

/// 放缩动画
- (void)doScaleAnimation;

/// 旋转动画
- (void)doRotateAnimation;

/// 更新计数视图内容
/// @param isHidden 是否隐藏
/// @param text 计数内容
- (void)updateCountView:(BOOL)isHidden text:(NSString *)text;
@end

NS_ASSUME_NONNULL_END
