//
//  QMNaviRoadEnlargementView.h
//  QMapNaviKit
//
//  Created by 33 on 2019/11/20.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import "DriveNaviModule.h"
 
#import <DragonMapKit/DMEnlargeOverlayView.h>
#import <QMapRouteSearchKit/QRouteStepNavInfo.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^RoadEnlargementViewTouchCloseBlock)(void);
/**
 路口放大图
 负责控制bitmap放大图&矢量放大图的内部逻辑：白天/夜间模式切换，小车吸附
 */
@interface QMNaviRoadEnlargementView : UIView

@property (nonatomic, assign) BOOL isNight;
@property (nonatomic, assign) NSInteger abDistance;

@property (nonatomic, weak) id<DriveNaviModuleDelegate> viewDelegate;
@property (nonatomic, copy) RoadEnlargementViewTouchCloseBlock touchCloseBlock;

@property (nonatomic, strong) UIImage *roadEnlargementImage;

/// 底图改造后对应的放大图
@property (nonatomic, strong, nullable) DMEnlargeOverlayView *dmEnlargeView;

@end

NS_ASSUME_NONNULL_END
