//
//  QMDetectionRefreshButtonModule.h
//  QMapNaviKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/10/15.
//  Copyright © 2020 Tencent. All rights reserved.
//

#import "DriveNaviModule.h"
#import "NaviModuleProtocols.h"
#import "RouteSearchChoiceProtocol.h"

NS_ASSUME_NONNULL_BEGIN

/// 路线探测刷新按钮，将刷新逻辑从原来导航的AdjointModule中拆分出来
@interface QMDetectionRefreshButtonModule : DriveNaviModule

/// 离线搜索的代理
@property (nonatomic, weak) id<RouteSearchChoiceProtocol> routeSearchChoiceDelegate;
/// 在线算路的代理
@property (nonatomic, weak) id<NavigationRouteRequestProtocol> requestDelegate;
/// 埋点的代理
@property (nonatomic, weak) id<QMNaviStatManagerDelegate> naviStatDelegate;


-(void)refreshRoutes;

- (void)refreshRoutesWithNewDestination:(QRoutePlaceInfo *)destination;

@end

NS_ASSUME_NONNULL_END
