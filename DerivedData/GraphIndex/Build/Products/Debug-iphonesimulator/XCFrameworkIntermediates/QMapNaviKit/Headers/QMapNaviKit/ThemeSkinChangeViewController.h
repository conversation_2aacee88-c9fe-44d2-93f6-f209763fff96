//
//  ThemeSkinChangeViewController.h
//  AutoCoding
//
//  Created by el<PERSON> on 2020/6/9.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>

// //////////////////////////////////////////
//   主题皮肤管理，present出来的VC,不存数据，仅用于交互
// /////////////////////////////////////////
typedef NS_ENUM(NSUInteger, QMThemeSkinChangeType) {
    QMThemeSkinChangeTypeDownloadandApply, //下载并应用
    QMThemeSkinChangeTypeDownload,         //仅仅下载
    QMThemeSkinChangeTypeApply,            //仅仅使用
};
@class QMThemePackageItem;
@protocol ThemeSkinChangeViewControllerProtocol;

@interface ThemeSkinChangeViewController : QMPopoverViewController // 需要更换父类，响应叮当等需求

/// 默认选中的皮肤
@property (nonatomic, assign) NSUInteger themeIconSelectedIndex;
/// 初始选择的皮肤id   上面那个在选择之后会变化，现在产品要求提示要对更新与下载做区别
@property (nonatomic, assign) NSInteger oldThemeID;
/// 配置主题 icon url 数据
/// @param arr 数据数组
//- (void)configThemeIconURLs:(NSArray<NSString *> *)arr;

- (void)configThemePackageItems:(NSArray<QMThemePackageItem *> *)dataArray;

/// 夜间模式，用于下午更新夜间模式使用，后续单独管理
@property (nonatomic, assign) BOOL isNightMode;

@property (nonatomic, assign) UIInterfaceOrientationMask previousControllerOrientation;

@property (nonatomic, weak) id<ThemeSkinChangeViewControllerProtocol> delegate;
/// 更新进度和是否下载完成 和使用状态
- (void)updateThemeItem:(id)item;

- (void)themeShowLoading;

- (void)themeHideLoadingView;

- (void)showNetworkErrorView;

- (void)dismissSkinViewController;

@end

@protocol ThemeSkinChangeViewControllerProtocol<NSObject>

@optional

/// 下载 主题皮肤
/// @param item  需要下载的数据源
/// @param type 下载类型
- (void)downloadItemAtIndex:(NSUInteger)index type:(QMThemeSkinChangeType)type;

- (void)pauseDownloadItemAtIndex:(NSUInteger)index;
///  异步获取数据列表，用于加载失败后，用户主动加载
- (void)dataSource:(void (^)(NSArray *arr))dataBlock;

/// 处理页面消失的回调, 处理scrollview 和 mapview 的mode 问题
- (void)qmViewDisappear;

/// 创建主题广场VC
- (UIViewController *)createThemeSquareVC;

@end
