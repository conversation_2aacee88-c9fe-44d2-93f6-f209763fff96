//
//  QMTrafficLightBubbleCreater.h
//  CameraImageDemo
//
//  Created by 刘澈 on 2022/8/10.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@class MBIZLightCountdownTimerDrawDescriptor;
@class MBIZMultiLightCountdownTimerDrawDescriptor;
@class MBIZAnimationImageParam;

extern NSUInteger const QMMaxRedLightCount;



@interface QMTrafficLightBubbleModel : NSObject

@property(nonatomic) UIImage *image;
@property(nonatomic) NSArray<MBIZAnimationImageParam *> *frameParams;

@end

@interface QMTrafficLightBubbleCreater : NSObject

+ (QMTrafficLightBubbleModel *)createTrafficLightBubbleWithConfig:(MBIZMultiLightCountdownTimerDrawDescriptor *)descriptor
                                                       isDarkMode:(BOOL)isDarkMode;

+ (QMTrafficLightBubbleModel *)createTrafficSubLightBubbleWithConfig:(MBIZMultiLightCountdownTimerDrawDescriptor *)config isDarkMode:(BOOL)isDarkMode;

+ (UIImage *)createMultiTrafficLightBubbleWithConfig:(MBIZMultiLightCountdownTimerDrawDescriptor *)config
                                        isHomeCruise:(BOOL)isHomeCruise
                                        isDarkMode:(BOOL)isDarkMode;

+ (UIImage *)createMultiTrafficSubLightBubbleWithConfig:(MBIZMultiLightCountdownTimerDrawDescriptor *)config
                                           isHomeCruise:(BOOL)isHomeCruise
                                             isDarkMode:(BOOL)isDarkMode;

@end

NS_ASSUME_NONNULL_END
