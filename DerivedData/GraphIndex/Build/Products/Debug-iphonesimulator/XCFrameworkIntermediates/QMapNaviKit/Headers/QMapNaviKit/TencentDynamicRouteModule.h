//
//  TencentDynamicRouteModule.h
//  QMapNaviKit
//
//  Created by <PERSON><PERSON><PERSON> on 14/6/2017.
//  Copyright © 2017 Tencent. All rights reserved.
//

#import "AdjointRouteMapPresenter.h"
#import "DriveNaviModule.h"
#import "NaviModuleProtocols.h"

@interface TencentDynamicRouteModule : DriveNaviModule
@property (nonatomic, assign) int resType;

@property (nonatomic, weak) id<AdjointRouteMapPresenterProtocol> AdjointRouteMapPresenterDelegate;
@property (nonatomic, weak) id<QMNaviStatManagerDelegate> naviStatDelegate;

// 过分歧点强制取消动态换路
- (void)forkForceRefuseDynamicRoute;

@end
