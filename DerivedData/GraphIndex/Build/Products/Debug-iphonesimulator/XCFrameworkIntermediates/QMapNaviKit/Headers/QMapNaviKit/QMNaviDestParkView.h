//
//  QMNaviDestParkView.h
//  QMapNaviKit
//
//  Created by z<PERSON><PERSON><PERSON> on 2023/2/16.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMImageView.h>
#import "UIView+DriveNaviModule.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^QMNaviDestParkViewTappedBlock)(void);

@interface QMNaviDestParkView : UIView

- (instancetype)initWIthNaviDelegate:(id<DriveNaviModuleDelegate>)delegate poiRichInfo:(QMJCE_ParkRecommendProxy_PoiRichInfo *)richInfo;

/// 是否是日夜间
@property (nonatomic, assign) BOOL isNightStyle;

/// 点击的回调
@property (nonatomic, copy  ) QMNaviDestParkViewTappedBlock onTappedBlock;

@property (nonatomic, strong, readonly) QMImageView *pagView;

/// 右上角标签
@property (nonatomic, nullable) UIImage *tagImg;

/// 车位状态 及 空余百分比
@property (nonatomic, strong, readonly) UIView *_parkingStatusContainerView;
/// 停车场位置
@property (nonatomic, strong, readonly) UILabel *parkLocationLabel;
///车位数量
@property (nonatomic, strong, readonly) UILabel *parkNumLabel;

///更多停车场
@property (nonatomic, strong, readonly) UIView *parkMoreView;

- (void)relayoutViewWithRichInfo:(QMJCE_ParkRecommendProxy_PoiRichInfo *)richInfo;

@end

NS_ASSUME_NONNULL_END
