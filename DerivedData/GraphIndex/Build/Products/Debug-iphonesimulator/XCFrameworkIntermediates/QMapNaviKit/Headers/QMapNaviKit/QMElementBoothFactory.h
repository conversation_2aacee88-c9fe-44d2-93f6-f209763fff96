//
//  QMElementBoothFactory.h
//  QMapNaviKit
//
//  Created by 关旭航 on 2024/5/28.
//  Copyright © 2024 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "QMElementBoothConfig.h"

NS_ASSUME_NONNULL_BEGIN

@interface QMElementBoothFactory : NSObject

/// 一些老业务可以从此处拿config 也可以业务自建config
+ (QMElementBoothConfig *)landscapeElementBoothConfigWithNaviViewType:(DriveNaviViewType)naviViewType;

+ (QMElementBoothConfig *)portraitElementBoothConfigWithNaviViewType:(DriveNaviViewType)naviViewType;

+ (NSArray<QMElementBoothConfig *> *)autoProductElementBoothConfigsWithIsLandscape:(BOOL)isLandscape;
+ (NSArray<QMElementBoothConfig *> *)autoProductElementBoothConfigsWithIsLandscape:(BOOL)isLandscape
                                                              isMapUnfollowLocator:(BOOL)isMapUnfollowLocator;
+ (NSArray<QMElementBoothConfig *> *)autoProductElementBoothConfigsWithIsLandscape:(BOOL)isLandscape
                                                              isMapUnfollowLocator:(BOOL)isMapUnfollowLocator
                                                                 findTrueViewBlock:(_Nullable QMElementBoothContainerViewFindViewBlock)findViewBlock;
+ (NSArray<QMElementBoothConfig *> *)autoProductVisibleViewConfigs:(NSArray<QMElementBoothConfig *> *)configs
                                                 findTrueViewBlock:(QMElementBoothContainerViewFindViewBlock)findTrueViewBlock;

///  横屏挪图态右下布局需要聚合的业务
+ (NSArray<NSNumber *> *)landspaceUnFollowLocatorRightBottomAggregationNaviViewTypes;

/// 档位布局需要响应的业务类型
+ (NSArray<NSNumber *> *)boothNeedResponseDriveNaviViewTypes;


+ (NSArray<QMElementBoothConfig *> *)filterVisibleConfigs:(NSArray<QMElementBoothConfig *> *)configs isLeft:(BOOL)isLeft;


+ (NSString *)naviViewTypeDescription:(DriveNaviViewType)naviViewType;

@end

NS_ASSUME_NONNULL_END
