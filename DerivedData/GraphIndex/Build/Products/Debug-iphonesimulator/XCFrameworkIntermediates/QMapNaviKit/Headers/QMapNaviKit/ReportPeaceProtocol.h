//
//  ReportPeaceProtocol.h
//  QMapNaviKit
//
//  Created by Panda on 2018/1/11.
//  Copyright © 2018年 Tencent. All rights reserved.
//

#import <CoreLocation/CoreLocation.h>
#import <Foundation/Foundation.h>

struct NaviReportLocation {
    CLLocationCoordinate2D attached;
    CLLocationCoordinate2D location;
};

struct LeftEtaAndDistance {
    long leftEta;
    long leftDistance;
};

typedef struct NaviReportLocation NaviReportLocation;
typedef struct LeftEtaAndDistance LeftEtaAndDistance;

@class QMJCE_peace_CSNavStartReportReq;
@class QMJCE_peace_SCNavStartReportRsp;

@interface ReportPeaceProtocol : NSObject

+ (QMJCE_peace_CSNavStartReportReq *)getNavStartReportReq:(NSDictionary *)userInfo routeID:(NSString *)routeID lastSessionId:(long long)lastSessionId;
+ (NSDictionary *)parseNavStartReportResp:(QMJCE_peace_SCNavStartReportRsp *)resp;

+ (QMJCE_peace_CSNavingReportReq *)getNavingReportReq:(long long)sessionId
                                              routeID:(NSString *)routeID
                                   naviReportLocation:(NaviReportLocation)naviReportLocation
                                      isValidAttached:(short)isValidAttached
                                          attachIndex:(int)attachIndex
                                   leftEtaAndDistance:(LeftEtaAndDistance)leftEtaAndDistance;
+ (NSDictionary *)parseNavingReportResp:(QMJCE_peace_SCNavingReportRsp *)resp;
+ (QMJCE_peace_CSNavEndReportReq *)getNavEndReportReq:(long long)sessionId
                                            isAutoEnd:(short)isAutoEnd
                                              routeID:(NSString *)routeID
                                   naviReportLocation:(NaviReportLocation)naviReportLocation
                                      isValidAttached:(BOOL)isValidAttached
                                          attachIndex:(int)attachIndex
                                   leftEtaAndDistance:(LeftEtaAndDistance)leftEtaAndDistance;
+ (NSDictionary *)parseNavEndReportResp:(QMJCE_peace_SCNavEndReportRsp *)resp;

@end
