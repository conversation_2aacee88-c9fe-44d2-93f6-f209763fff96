//
//  QMNaviTopPanelViewModelProtocol.h
//  QMapNaviKit
//
//  Created by 王瑞华 on 2021/2/13.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import "QMNaviBox.h"
#import "QMNavigationMainPanelUtility.h"
#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN
@class RGShowEnlargeMapInfo;
@protocol QMNaviTopPanelViewModelProtocol<NSObject>

/// 是否采用动画过度
@property (nonatomic, assign, readonly) BOOL transitionAnimated;

/// 视图展示类型 QMNavigationMainPanelViewMode
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *viewMode;

/// 车道级导航mini面板视图展示类型 QMHDNaviMiniPanelViewMode
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *miniViewMode;

/// 视图展示方向 QMNavigationMainPanelViewOrient
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *viewOrient;

/// 数据类型 QMNavigationMainPanelViewDataType
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *dataType;

/// 是否正在展示算路UI
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *routeCalculating;

/// 叮当 是否应该展示
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *dingDangShow;

/// 当前是否为夜间模式
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *isNightStyle;

///  更新是否展示进度条参数，默认隐藏进度条
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *progressViewHidden;

///  改变叮当 icon 状态
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *dingDangSleepy;

///  是否是GPS信号弱
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *gpsWeak;

///  是否是智能定位模式
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *smartLoc;

///  转向图片
@property (nonatomic, strong, readonly) QMNaviBox<UIImage *> *actionIconImage;

///  转向文字描述
@property (nonatomic, strong, readonly) QMNaviBox<NSString *> *actionText;

///  面板上展示的道路名
@property (nonatomic, strong, readonly) QMNaviBox<NSString *> *roadName;

///  面板上展示的出口文字
@property (nonatomic, strong, readonly) QMNaviBox<NSString *> *exitString;

///  然后轮转向图片
@property (nonatomic, strong, readonly) QMNaviBox<UIImage *> *nextActionIconImage;

///  然后轮道路名
@property (nonatomic, strong, readonly) QMNaviBox<NSString *> *nextRoadName;

///  距离下一条道路多少米
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *distanceMeters;

///  距离路口放大图多少米
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *dis_to_map;

///  放大图显示文案
@property (nonatomic, strong, readonly) QMNaviBox<NSString *> *display_text;

///  总里程还剩多少米
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *totalDistanceLeftMeters;

///  总里程剩余多长时间
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *timeLeftSeconds;

///  总里程剩余红绿灯🚥个数
@property (nonatomic, strong, readonly) QMNaviBox<NSNumber *> *trafficLights;

/// 是否展示起点区域面指示内容
@property (nonatomic, strong) QMNaviBox<NSNumber *> *spEnhanceShow;

/// 增益信息展示的图片
@property (nonatomic, strong) QMNaviBox<UIImage *> *spEnhanceIconImage;

/// 增益信息展示的文字内容
@property (nonatomic, strong) QMNaviBox<NSString *> *spEnhanceActionText;

/// 是否显示高速出口增强提示
@property (nonatomic, strong) QMNaviBox<NSNumber *> *isShowSpBlinkExit;

/// 是否显示北斗定位按钮
@property (nonatomic) QMNaviBox<NSNumber *> *isShowBeiDouButton;

@optional
/// 途经点个数信息
@property (nonatomic, strong) QMNaviBox<NSNumber *> *wayPointCount;
/// 途经点信息
@property (nonatomic, strong) QMNaviBox<NSString *> *wayPointDistanceETA;

/// 设备方向发生了转变
@property (nonatomic, strong) QMNaviBox<NSNumber *> *deviceOrientationDidChanged;

/// 是否显示北斗车道定位标签
@property (nonatomic) QMNaviBox<NSNumber *> *isShowBeidouHDLaneLabel;

/// 当前定位环境文案
@property (nonatomic) QMNaviBox<NSString *> *locationSceneText;

/// 超速
@property (nonatomic) QMNaviBox<NSNumber *> *isOverSpeed;

/// 面板叮当被点击
- (void)qmNaviTopPanelViewDingDangTapped;

/// 添加北斗icon视图
- (void)addBeiDouIconViewAtTopPanelView;

/// 是否是从AR导航条过来
- (BOOL)isFromARNavi;

- (QMNavigationMainPanelViewFromType)currentNaviFromType;

// @optional
/// 当面板高度发生变化就进行的回调通知
/// @param panelView 面板对象
/// @param height 面板高度
- (void)qmNaviTopPanelView:(UIView *)panelView didUpdateViewModeHeight:(CGFloat)height;

/// 当竖直面板高度发生变化就进行的回调通知，如有然后轮视图到普通视图的变化
/// @param panelView 面板对象
/// @param height 面板高度
- (void)qmNaviTopPanelView:(UIView *)panelView didUpdatePortraitLargeHeight:(CGFloat)height;


- (void)qmNaviTopPanelView:(UIView *)panelView didUpdateViewModeSize:(CGSize)size;

@end

NS_ASSUME_NONNULL_END
