//
//  QMNaviTopPanelView.h
//  QMapNaviKit
//
//  Created by 王瑞华 on 2021/1/13.
//  Copyright © 2021 Tencent. All rights reserved.
//

#import "QMNaviTopPanelViewModelProtocol.h"
#import "DriveNaviModule.h"
#import <UIKit/UIKit.h>

@class QMTopPanelLocationDisplayModel;

NS_ASSUME_NONNULL_BEGIN

/// 诱导面板视图
@interface QMNaviTopPanelView : UIView

@property (nonatomic, assign) BOOL disableLayoutInPortrait;

@property (nonatomic, weak) id<DriveNaviModuleDelegate> viewDelegate;
/// 视图模型
@property (nonatomic, weak) id<QMNaviTopPanelViewModelProtocol> viewModel;

@property (nonatomic) void(^hiddenLocationGuardBlock)(BOOL isHidden);
/// viewModel变化回调 （QMNaviBox奇葩脚手架 居然不支持多个监听者？直接用RAC多好，还得子传父）
@property (nonatomic) void(^mainViewModeChange)(QMNavigationMainPanelViewMode mode);


- (void)showOnlyLoadingView:(BOOL)isLoading;
- (void)setBackgroundGradientViewAccessStr:(NSString *)str;
- (void)layoutSubviewsInLargePortraitNormal;
///透传 道路变更的逻辑调用
- (void)driveRouteWillChange:(NSDictionary *)naviInfo;

- (void)displaySpeedViewInTopPanel:(UIView *)speedView isShow:(BOOL)isShow;

@end

NS_ASSUME_NONNULL_END
