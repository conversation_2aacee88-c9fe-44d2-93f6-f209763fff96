//
//  QMBigPopperView.h
//  QMapNaviKit
//
//  Created by sarah on 2018/11/15.
//  Copyright © 2018 Tencent. All rights reserved.
//

#import <QMapRouteSearchKit/QRouteLabel.h>
#import <UIKit/UIKit.h>
#import <DragonMapKit/DragonMapKit.h>

@interface QMActionModel : NSObject
@property (nonatomic, strong) UIImage *image;
@property (nonatomic, strong) QRouteLabel *title;
@property (nonatomic, copy) dispatch_block_t handler;
@property (nonatomic, assign) BOOL actionAvailable; //默认YES
@end

@interface QMBigPopperView : DMMarkerAccessoryView

@property (nonatomic, strong) QRouteLabel *firstLine;
@property (nonatomic, strong) NSArray<QRouteLabel *> *subLines;
@property (nonatomic, strong) QMActionModel *actionModel;

- (QMBigPopperView *)initWithPosition:(CGPoint)origin
                            firstLine:(QRouteLabel *)first
                             subLines:(NSArray<QRouteLabel *> *)subs
                               action:(QMActionModel *)model;

- (void)updateLayout;
@end
