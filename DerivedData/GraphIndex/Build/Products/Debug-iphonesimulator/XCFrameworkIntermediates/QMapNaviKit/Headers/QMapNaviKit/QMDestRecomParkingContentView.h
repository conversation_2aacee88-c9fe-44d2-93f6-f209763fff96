//
//  QMDestRecomParkingContentView.h
//  QMapNaviKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/10.
//  Copyright © 2023 Tencent. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <QMapMiddlePlatform/QMDestSuggestParkCardView.h>

NS_ASSUME_NONNULL_BEGIN

/// 这个 content view 将被放在 NaviAutoDismissConfirmView 中，以显示更多的停车 rich 信息
@interface QMDestRecomParkingContentView : UIView

- (QMDestRecomParkingContentView *)initWithPOIInfo:(JsonPOIInfo *)poiInfo
                                          etaEntry:(QMJCE_tmap_EtaEntry * _Nullable)etaEntry
                                          richInfo:(QMDestChangeSelectViewRichInfo * _Nullable)richInfo
                                       routeResult:(QRouteResultForDrive *)routeResult
                                           isNight:(BOOL)isNight;
@end

NS_ASSUME_NONNULL_END
