//
//  QMRedLightInfo.h
//  QMapNaviKit
//
//  Created by 刘澈 on 2022/8/18.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <MapBaseOCModel/MapBaseModelInterface.h>


typedef NS_ENUM(int, QMRedLightStates) {
    QMRedLightStates_Unknow = 0,
    QMRedLightStates_UnWork= 1,
    QMRedLightStates_Red = 3,
    QMRedLightStates_Green = 5,
    QMRedLightStates_Yellow = 7
};

typedef NS_ENUM(int, QMRedLightRequestType) {
    QMRedLightRequestType_SD = 0,
    QMRedLightRequestType_HD= 1,
    QMRedLightRequestType_Queue = 2
};

@interface QMPrivateRedLightStatesModel : QMModel

// NOTE: 此处变量命名方式为下划线方式的原因是迁移老代码遗留，后续统一修改

@property (nonatomic) QMRedLightStates light_status;//当前周期信号灯颜色，枚举值
@property (nonatomic) long  start_time;//当前颜色灯态开始时间戳
@property (nonatomic) long  end_time;//当前颜色灯态结束时间戳
@property (nonatomic) long  update_time;//当前灯态数据更新时间戳
@property (nonatomic) int  remain_time;//当前颜色灯态剩余时间倒计时，秒
@property (nonatomic) QMRedLightStates next_light_status;//下一灯态颜色
@property (nonatomic) long  next_start_time; //下一颜色灯态开始时间戳
@property (nonatomic) long  next_end_time; //下一颜色灯态结束时间戳
@property (nonatomic) int  next_duration; //下一颜色灯态持续时间
@property (nonatomic) NSString *start_end_link; //单个灯态对应的静态link对
@property (nonatomic) int  dir; //当前请求的红绿灯link对的流向信息
@property (nonatomic) int  is_queue; //是否支持入队播报
@property (nonatomic) int  is_countdown; //是否支持倒计时展示
@property (nonatomic) int confidence_level; // 播报置信度,0、1、2分别代表低、中、高置信度，默认值2
@property (nonatomic) NSString *green_info; //绿灯状态时展示文案
@property (nonatomic) NSString *red_info; //红灯状态时展示文案
@property (nonatomic) NSString *yellow_info; //黄灯状态时展示文案
@property (nonatomic) int  source; //灯态数据来源
@property (nonatomic) int  green_hide; //绿灯结束前的倒计时消隐时间，秒
@property (nonatomic) int  red_hide; //红灯开始后的倒计时消隐时间，秒
@property (nonatomic) NSString *light_text; // 等灯轮次文案

@end

@interface MapBaseRedLightInfo(QMRedLightInfoModule)

@property (nonatomic) NSArray<QMPrivateRedLightStatesModel *> *redLightStates;

// 二维红绿灯数据数组 （巡航用）
@property (nonatomic) NSArray<NSArray <QMPrivateRedLightStatesModel *> *> *redLightInfos;

// 二维红绿灯数据数组对应的inLink数组
@property (nonatomic) NSArray<NSString *> *redLightInfoLinks;


@end


@interface QMRedLightInfoLocationRecord: NSObject
@property (nonatomic) long long time;
@property (nonatomic) long long speed;
@property (nonatomic) long long longitude;
@property (nonatomic) long long latitude;

+ (void)handleRedInfoRecords:(NSMutableArray<QMRedLightInfoLocationRecord *> *)records locationData:(CLLocation *)location;

+ (NSString *)jsonStringWithRecords:(NSMutableArray<QMRedLightInfoLocationRecord *> *)records;

@end

@interface MapBaseMultiLightCountdowntimerBubbleInfo(QMRedLightInfoModule)

@property (nonatomic) BOOL isWaitRound;

@end

