//
//  QMNaviSettingLocalStore.h
//  SOSOMap--DEBUG
//
//  Created by <PERSON><PERSON><PERSON> on 2017/7/17.
//  Copyright © 2017年 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>
 
#import <QMapMiddlePlatform/QMCarNaviSettingDefine.h>
#import <QMapMiddlePlatform/QMDaylightManager.h>
#import <QMapNaviKit/QMapNaviCommon.h>
#import <QMapMiddlePlatform/QMCarRouteNaviCommon.h>

extern NSString *const KKeyNorFaceNaviMode;

typedef NS_ENUM(NSUInteger, QMScreenOrientation) { //!< 屏幕方向控制
    QMScreenOrientationAuto,                       //!< 自动
    QMScreenOrientationPortrait,                   //!< 锁定竖屏
    QMScreenOrientationLandscape,                  //!< 锁定横屏
};


typedef NS_ENUM(NSUInteger, QMTopPanelMode) { //!< 屏幕方向控制
    QMTopPanelAuto,                       //!< 自动
    QMTopPanelMini,                   //!< 锁定竖屏
    QMTopPanelMain,                  //!< 锁定横屏
};

@protocol QMNaviSettingStoreProtocol<NSObject>

@optional

// 是否黑夜模式
- (BOOL)getDarkMode;

- (BOOL)avoidBusyToAuto;
- (void)setAvoidBusyToAuto:(BOOL)avoidBusyToAuto;

- (BOOL)avoidBusy;
- (void)setAvoidBusy:(BOOL)avoidBusy;

- (BOOL)noHighway;
- (void)setNoHighway:(BOOL)noHighway;

- (BOOL)noToll;
- (void)setNoToll:(BOOL)noToll;

- (BOOL)highSpeedFirst;
- (void)setHighSpeedFirst:(BOOL)highSpeedFirst;

- (QMapNaviTTSMode)TTSMode;
- (void)setTTSMode:(QMapNaviTTSMode)TTSMode;

- (NSInteger)changeTTSModeToReport:(QMapNaviTTSMode)TTSMode;;

- (QMapNaviTTSMode)truckTTSMode;
- (void)setTruckTTSMode:(QMapNaviTTSMode)TTSMode;

- (QMapNaviTTSMode)motorTTSMode;
- (void)setMotorTTSMode:(QMapNaviTTSMode)TTSMode;

- (BOOL)showTraffic;
- (void)setShowTraffic:(BOOL)showTraffic;

// 记录使用于导航的路况开关状态
- (BOOL)showTrafficForNavi;
- (void)setShowTrafficForNavi:(BOOL)showTraffic;

- (BOOL)screenDirection;
- (void)setScreenDirection:(BOOL)screenDirection;

- (BOOL)showMiniMap;
- (void)setShowMiniMap:(BOOL)showMiniMap;

- (BOOL)showBeamView;
- (void)setShowBeamView:(BOOL)showBeamView;

- (BOOL)showExpectTime;
- (void)setShowExpectTime:(BOOL)showExpectTime;

// 道路朝前 or 正北向上  （道路朝前/正北向上，比 2D 模式/ 3D 模式多了定位跟随、自车标位置不同等效果，只改旋转角，不改俯仰角）
- (BOOL)isRouteFront;
- (void)setIsRouteFront:(BOOL)isRouteFront;

// 2D 模式 or 3D 模式 （只改俯仰角，不改旋转角）
- (BOOL)isDimension3D;
- (void)setDimension3D:(BOOL)is3D;

- (QMapNaviDayNightMode)daylightMode;
- (void)setDaylightMode:(QMapNaviDayNightMode)daylightMode;

- (BOOL)avoidLimit;
- (void)setAvoidLimit:(BOOL)avoidLimit;

- (BOOL)isCarPlayOn;
- (void)setIsCarPlayOn:(BOOL)isCarPlayOn;

- (BOOL)isCarPlayConnected;
- (void)setIsCarPlayConnected:(BOOL)isCarPlayConnected;

- (BOOL)wxpayPrompt;
- (void)setWxpayPrompt:(BOOL)on;

- (BOOL)QQMusicEnable;
- (void)setQQMusicEnable:(BOOL)on;

- (BOOL)northFaceNaviMode;
- (void)setNorthFaceNaviMode:(BOOL)northFaceNaviMode;

- (BOOL)isFullTimeDriver;
- (void)setIsFullTimeDriver:(BOOL)isFullTimeDriver;

- (BOOL)showGuideLine;
- (void)setShowGuideLine:(BOOL)showGuideLine;

- (BOOL)enableHDNavigation;
- (void)setEnableHDNavigation:(BOOL)enableHDNavigation;

- (BOOL)stillListenBroadCastInCall;
- (void)setStillListenBroadCastInCall:(BOOL)listenBroadCastInCall;

- (BOOL)autoEnterRouteDetectionEnable;
- (void)setAutoEnterRouteDetectionEnable:(BOOL)on;

/// 获取是否自动进入巡航本地开关状态
- (BOOL)autoEnterCarPlayCruiseEnable;

/// 设置是否自动进入巡航本地开关状态
- (void)setAutoEnterCarPlayCruiseEnable:(BOOL)isOn;

- (BOOL)voiceWakeUpDingdangEnable;
- (void)setVoiceWakeUpDingdangEnable:(BOOL)on;

/// 路线探测静音状态
- (QMapNaviTTSMode)detectTTSMode;
- (void)setDetectTTSMode:(QMapNaviTTSMode)TTSMode;

/// 巡航播报状态
- (QMapNaviTTSMode)cruiseTTSMode;
- (void)setCruiseTTSMode:(QMapNaviTTSMode)TTSMode;

- (BOOL)autoScale;
- (void)setAutoScale:(BOOL)autoScale;

// 省电模式
- (BOOL)powerSavingModeEnable;
- (void)setPowerSavingModeEnable:(BOOL)on;

// 是否由提示打开的省电模式
- (BOOL)isRemindOpenPowerSavingMode;
- (void)setIsRemindOpenPowerSavingMode:(BOOL)isRemind;

@end

@interface QMCarNaviSettingStore : NSObject<QMNaviSettingStoreProtocol>

QMSharedInstance_H(QMCarNaviSettingStore)

    @property(nonatomic, assign) BlueToothChoice BTChoice;
@property (nonatomic, assign) MusicControlChoice musicControlChoice; //!< 音乐控制选项
@property (nonatomic, strong) NSString *audioCategory;
@property (nonatomic, strong) NSString *walkCycleAudioCategory;
@property (nonatomic, assign) QMScreenOrientation screenOrientation; //屏幕方向选项
@property (nonatomic, assign) QMTopPanelMode topPanelMode; //屏幕方向选项
// carplay大开关，设置中
@property (nonatomic, readonly) BOOL isCarPlayOn;
// carplay是否连接开关
@property (nonatomic, assign) BOOL isCarPlayConnected;
// 是否是专职司机
@property (nonatomic, assign) BOOL isFullTimeDriver;

@property (nonatomic, assign) BOOL isUserLastSetMaxVolume; //!< 用户上次是否设置为最大音量

@property (nonatomic, assign) UIApplicationState applicationState; //!<

@property (nonatomic) BOOL isLiveActivityNeedAlert;//灵动岛是否需要转向提醒

@end

@interface QMCarNaviSettingStore(Utils)

+ (QMapNaviTTSMode)TTSModeWithRouteNaviType:(QMCarRouteNaviType)routeNaviType;
+ (void)setTTSMode:(QMapNaviTTSMode)ttsMode withRouteNaviType:(QMCarRouteNaviType)routeNaviType;

@end

@interface QMWalkNaviSettingStore : NSObject<QMNaviSettingStoreProtocol>
+ (QMapNaviTTSMode)TTSMode;
@end

@interface QMCycleNaviSettingStore : NSObject<QMNaviSettingStoreProtocol>
+ (QMapNaviTTSMode)TTSMode;
@end
