//
//  QMQQMusicLibraryViewController.h
//  QMapNaviKit
//
//  Created by 王瑞华 on 2020/8/27.
//  Copyright © 2022 Tencent. All rights reserved.
//

#import <QMapUIKit/QMapUIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef void (^QMQQMusicLibrarySelectBlock)(NSInteger row);
typedef void (^QMQQMusicLibraryBlock)(void);
@interface QMQQMusicLibraryViewController : QMPopoverViewController

@property (nonatomic, copy) NSArray *dataSource;                     //!< 数据源
@property (nonatomic, copy) QMQQMusicLibrarySelectBlock selectBlock; //!< 选择了某个歌单
@property (nonatomic, copy) QMQQMusicLibraryBlock closeBlock;        //!< 关闭
@property (nonatomic, copy) QMQQMusicLibraryBlock openQQMusicBlock;  //!< 打开QQ音乐
@property (nonatomic, copy) QMQQMusicLibraryBlock retryBlock;        //!< 重试点击
@property (nonatomic, assign) BOOL isNightStyle;                     //!< 是否为黑夜模式
/// 区分是驾车导航还是路线探测，处理横竖屏, YES 为驾车
@property (nonatomic, assign) BOOL isDrivingScene;

- (void)libriayShowLoading;

- (void)libriayHideLoading;

- (void)libriayShowFail;

- (void)libriayHideFail;

@end

NS_ASSUME_NONNULL_END
