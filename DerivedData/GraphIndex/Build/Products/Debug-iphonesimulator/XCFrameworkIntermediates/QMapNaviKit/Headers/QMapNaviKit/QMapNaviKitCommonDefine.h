//
//  QMapNaviKitCommonDefine.h
//  QMapNaviKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/2/14.
//  Copyright © 2023 Tencent. All rights reserved.
//
//  ！！！警告 ！！！
//  这个类只允许定义一些比较基础的类型声明, 架构设计上作为一个随时可迁移的文件存在
//  如果是驾车内部使用的定义可以写在这里, 如果需要跨库引用, 可以下沉到中台层, 根据需要，直至下沉到 Foundation
//  『禁止引用』驾车相关的任何工具类代码
#import <Foundation/Foundation.h>

/**
 三端路线不一致的错误详情
 */
typedef NS_ENUM(NSUInteger, QMCarRouteDiffErrorType) {
    QMCarRouteDiffErrorTypeDefault = 0,           //默认
    QMCarRouteDiffErrorTypeAllMatch = 1,          //三端一致
    QMCarRouteDiffErrorTypeAllUnMatch = 2,        //三端都不一致
    QMCarRouteDiffErrorTypeRouteMatch = 3,        //吸附错误(定位)
    QMCarRouteDiffErrorTypeRG = 4,                //诱导错误
    QMCarRouteDiffErrorTypeOther = 5,             //其他(理论上不会出现)
};

