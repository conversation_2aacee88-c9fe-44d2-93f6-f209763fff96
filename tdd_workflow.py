#!/usr/bin/env python3
"""
TDD工作流程脚本 - 确保严格遵循测试驱动开发

使用方法:
    python tdd_workflow.py --feature "新功能名称"    # 开始新功能开发
    python tdd_workflow.py --test                   # 运行所有测试
    python tdd_workflow.py --commit "提交信息"       # TDD提交流程
    python tdd_workflow.py --status                 # 查看当前TDD状态
"""

import sys
import subprocess
import argparse
import json
from pathlib import Path
from datetime import datetime


class TDDWorkflow:
    """TDD工作流程管理器"""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.tdd_state_file = self.project_root / ".tdd_state.json"
        self.load_state()

    def load_state(self):
        """加载TDD状态"""
        if self.tdd_state_file.exists():
            with open(self.tdd_state_file, 'r') as f:
                self.state = json.load(f)
        else:
            self.state = {
                "current_feature": None,
                "phase": "RED",  # RED -> GREEN -> REFACTOR
                "test_files": [],
                "last_test_run": None,
                "test_passed": False
            }

    def save_state(self):
        """保存TDD状态"""
        with open(self.tdd_state_file, 'w') as f:
            json.dump(self.state, f, indent=2)

    def run_command(self, cmd, cwd=None):
        """运行命令并返回结果"""
        print(f"🔧 运行命令: {' '.join(cmd)}")
        try:
            result = subprocess.run(
                cmd,
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
            print(result.stdout)
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False, e.stderr

    def start_feature(self, feature_name):
        """开始新功能开发 (RED阶段)"""
        print(f"🚀 开始新功能开发: {feature_name}")

        # 确保当前没有未完成的功能
        if self.state["current_feature"] and not self.state["test_passed"]:
            print(f"⚠️  警告: 当前功能 '{self.state['current_feature']}' 尚未完成")
            response = input("是否继续新功能开发? (y/N): ")
            if response.lower() != 'y':
                return False

        self.state.update({
            "current_feature": feature_name,
            "phase": "RED",
            "test_files": [],
            "test_passed": False
        })
        self.save_state()

        print(f"📝 请为功能 '{feature_name}' 编写失败的测试 (RED阶段)")
        print("   1. 在 tests/unit/ 或 tests/integration/ 中创建测试文件")
        print("   2. 编写描述期望行为的测试用例")
        print("   3. 运行 'python tdd_workflow.py --test' 确认测试失败")

        return True