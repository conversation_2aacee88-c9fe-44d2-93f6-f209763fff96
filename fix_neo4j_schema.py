#!/usr/bin/env python3
"""
修复Neo4j模式问题并重新导入数据
解决节点标签和关系类型的映射问题
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from neo4j_integration.config import Neo4jConfig
from neo4j_integration.connection import Neo4jConnection
from neo4j_integration.schema import Neo4jSchemaManager
from neo4j_integration.importer import Neo4jImporter
from neo4j_integration.validator import Neo4jValidator

def verify_current_schema():
    """验证当前数据库模式"""
    print("🔍 检查当前Neo4j数据库模式...")
    
    config = Neo4jConfig()
    connection = Neo4jConnection(config)
    
    if not connection.connect():
        print("❌ 无法连接到Neo4j")
        return False
    
    try:
        # 检查节点标签
        labels_query = "CALL db.labels() YIELD label RETURN label ORDER BY label"
        labels_result = connection.execute_query(labels_query)
        current_labels = [r['label'] for r in labels_result]
        
        # 检查关系类型
        rel_types_query = "CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType"
        rel_types_result = connection.execute_query(rel_types_query)
        current_rel_types = [r['relationshipType'] for r in rel_types_result]
        
        # 检查数据量
        node_count_query = "MATCH (n) RETURN count(n) as count"
        node_count = connection.execute_query(node_count_query)[0]['count']
        
        rel_count_query = "MATCH ()-[r]->() RETURN count(r) as count"
        rel_count = connection.execute_query(rel_count_query)[0]['count']
        
        print(f"\n📊 当前数据库状态:")
        print(f"  节点数量: {node_count:,}")
        print(f"  关系数量: {rel_count:,}")
        print(f"  节点标签: {current_labels}")
        print(f"  关系类型: {current_rel_types}")
        
        # 检查是否有问题
        expected_labels = ['File', 'Class', 'Method', 'Property', 'Protocol', 'Struct', 'Enum', 'CodeNode']
        expected_rel_types = ['DEFINES', 'ACCESSES', 'DEPENDS_ON', 'IMPORTS', 'INHERITS', 'CALLS', 'EXTENDS', 'IMPLEMENTS']
        
        has_specific_labels = any(label in expected_labels for label in current_labels if label != 'CodeNode')
        has_specific_rel_types = any(rel_type in expected_rel_types for rel_type in current_rel_types if rel_type != 'RELATED_TO')
        
        if not has_specific_labels:
            print("\n❌ 问题: 只有通用的CodeNode标签，缺少特定节点类型")
        
        if not has_specific_rel_types:
            print("❌ 问题: 只有通用的RELATED_TO关系，缺少特定关系类型")
        
        return has_specific_labels and has_specific_rel_types
        
    finally:
        connection.disconnect()

def fix_and_reimport():
    """修复模式并重新导入数据"""
    print("\n🔧 开始修复Neo4j模式并重新导入数据...")
    
    config = Neo4jConfig()
    connection = Neo4jConnection(config)
    
    if not connection.connect():
        print("❌ 无法连接到Neo4j")
        return False
    
    try:
        # 初始化组件
        schema_manager = Neo4jSchemaManager(connection)
        importer = Neo4jImporter(connection, config)
        validator = Neo4jValidator(connection, config)
        
        # 1. 清理数据库
        print("\n🧹 清理现有数据...")
        if not schema_manager.clear_database():
            print("❌ 数据库清理失败")
            return False
        
        # 2. 重新创建模式
        print("🏗️ 重新创建数据库模式...")
        if not schema_manager.create_schema():
            print("⚠️ 模式创建部分失败，但继续执行")
        
        # 3. 重新导入数据
        print("📥 重新导入数据...")
        start_time = time.time()
        
        if not importer.import_all():
            print("❌ 数据导入失败")
            return False
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 4. 验证结果
        print(f"\n🔍 验证导入结果...")
        
        # 检查节点标签
        labels_query = "CALL db.labels() YIELD label RETURN label ORDER BY label"
        labels_result = connection.execute_query(labels_query)
        final_labels = [r['label'] for r in labels_result]
        
        # 检查关系类型
        rel_types_query = "CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType"
        rel_types_result = connection.execute_query(rel_types_query)
        final_rel_types = [r['relationshipType'] for r in rel_types_result]
        
        # 检查数据量
        node_count_query = "MATCH (n) RETURN count(n) as count"
        node_count = connection.execute_query(node_count_query)[0]['count']
        
        rel_count_query = "MATCH ()-[r]->() RETURN count(r) as count"
        rel_count = connection.execute_query(rel_count_query)[0]['count']
        
        # 检查节点类型分布
        node_type_query = """
        MATCH (n)
        WHERE n.node_type IS NOT NULL
        RETURN n.node_type as type, count(n) as count
        ORDER BY count DESC
        """
        node_type_results = connection.execute_query(node_type_query)
        
        # 检查关系类型分布
        rel_type_query = """
        MATCH ()-[r]->()
        WHERE r.relationship_type IS NOT NULL
        RETURN r.relationship_type as type, count(r) as count
        ORDER BY count DESC
        """
        rel_type_results = connection.execute_query(rel_type_query)
        
        print(f"\n🎉 重新导入完成!")
        print(f"⏱️ 耗时: {duration:.2f} 秒")
        print(f"\n📊 最终数据库状态:")
        print(f"  节点数量: {node_count:,}")
        print(f"  关系数量: {rel_count:,}")
        print(f"  节点标签: {final_labels}")
        print(f"  关系类型: {final_rel_types}")
        
        print(f"\n📈 节点类型分布:")
        for result in node_type_results:
            print(f"  {result['type']}: {result['count']:,}")
        
        print(f"\n🔗 关系类型分布:")
        for result in rel_type_results:
            print(f"  {result['type']}: {result['count']:,}")
        
        # 验证是否修复成功
        expected_labels = ['File', 'Class', 'Method', 'Property', 'Protocol', 'Struct', 'Enum']
        expected_rel_types = ['DEFINES', 'ACCESSES', 'DEPENDS_ON', 'IMPORTS', 'INHERITS', 'CALLS', 'EXTENDS', 'IMPLEMENTS']
        
        has_specific_labels = any(label in expected_labels for label in final_labels)
        has_specific_rel_types = any(rel_type in expected_rel_types for rel_type in final_rel_types)
        
        if has_specific_labels and has_specific_rel_types:
            print("\n✅ 模式修复成功! Neo4j现在包含正确的节点标签和关系类型")
            return True
        else:
            print("\n❌ 模式修复失败，仍然存在问题")
            return False
        
    finally:
        connection.disconnect()

def main():
    """主函数"""
    print("🔧 Neo4j模式修复工具")
    print("=" * 60)
    print("这个工具将修复Neo4j数据库中的节点标签和关系类型问题")
    print("预计耗时: 5-10分钟")
    print()
    
    # 检查当前模式
    if verify_current_schema():
        print("\n✅ 当前数据库模式正确，无需修复")
        return
    
    # 确认执行
    confirm = input("\n确认修复并重新导入数据? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消执行")
        return
    
    # 执行修复
    if fix_and_reimport():
        print("\n🎉 Neo4j模式修复成功!")
        print("现在可以使用正确的节点标签和关系类型进行查询")
        print("示例查询:")
        print("  MATCH (m:Method) RETURN count(m)")
        print("  MATCH ()-[r:DEFINES]->() RETURN count(r)")
    else:
        print("\n❌ Neo4j模式修复失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
