# 项目清理和文档更新计划

## 🎯 清理目标
清理过时的文件、代码和文档，更新现有文档以反映最新的系统状态和功能。

## 📋 需要删除的过时文件

### 1. 测试和调试文件
- `test_ast_debug/` - 旧的AST调试目录
- `test_ast_enhanced/` - 被更新版本替代
- `test_enhanced_ast/` - 被修复版本替代
- `test_extended_ast/` - 测试文件，不需要保留
- `test_source_files/` - 测试用源文件，不需要保留
- `test_compile_commands.json` - 测试用编译数据库

### 2. 过时的脚本和工具
- `check_neo4j_final_state.py` - 被新的验证器替代
- `demo_pipeline_all.py` - 演示脚本，不需要保留
- `discover_repositories.py` - 功能已集成到pipeline中
- `fix_neo4j_schema.py` - 临时修复脚本
- `fix_relationships.py` - 临时修复脚本
- `test_neo4j_connection.py` - 测试脚本
- `verify_files.py` - 临时验证脚本
- `verify_fixed_neo4j.py` - 被新验证器替代

### 3. 过时的数据文件
- `ast_out_index/` - 旧的单仓库输出
- `discovery_report.json` - 旧的发现报告
- `index_symbols.jsonl` - 旧的符号文件
- `schemes.txt` - 临时文件

### 4. 过时的日志文件
- `neo4j_integration.log` - 旧日志
- `pipeline.log` - 旧日志
- `pipeline_all.log` - 旧日志
- `pipeline_all_robust.log` - 旧日志

### 5. 过时的配置和临时文件
- `pipeline_all_repositories.json` - 临时配置
- `tdd_workflow.py` - 临时工作流脚本

## 📝 需要更新的文档

### 1. 主要文档更新
- `README.md` - 更新为最新的系统架构和使用方法
- `CHANGELOG.md` - 添加最新的变更记录
- `Pipeline-All完整操作指南.md` - 更新为最新的操作流程

### 2. 技术文档整理
- 保留最新的架构文档
- 删除过时的实现文档
- 更新API文档

### 3. 新增文档
- 系统使用指南
- Neo4j验证文档
- 性能优化指南

## 🔧 需要保留的重要文件

### 1. 核心系统文件
- `run_pipeline_all_robust.py` - 主要执行脚本
- `scripts/enhanced_ast_extractor.py` - 核心AST提取器
- `neo4j_requirements_validator.py` - 验证系统
- `performance_optimization.py` - 性能优化工具

### 2. 最新数据文件
- `ast_out_index_all_fixed/` - 修复后的完整数据
- `ast_out_index_all_extended/` - 扩展关系数据
- `neo4j_validation_report.json` - 验证报告
- `neo4j_requirements_summary.md` - 验证总结

### 3. 核心模块
- `pipeline/` - 核心流水线模块
- `neo4j_integration/` - Neo4j集成模块
- `tests/` - 测试框架
- `tools/` - 工具目录

## 📋 执行计划

1. **第一阶段：删除过时文件**
   - 删除测试和调试文件
   - 删除过时的脚本
   - 删除临时数据文件

2. **第二阶段：更新文档**
   - 更新README.md
   - 更新操作指南
   - 整理技术文档

3. **第三阶段：验证清理结果**
   - 确保系统仍然正常工作
   - 验证文档的准确性
   - 测试关键功能
