#!/usr/bin/env python3
"""
Optimized Neo4j Import Script

This script uses batch processing and transactions for better performance.
"""

import json
from pathlib import Path
from neo4j import GraphDatabase

def batch_import_nodes(session, nodes_file, batch_size=1000):
    """Import nodes in batches for better performance"""
    print(f'📦 批量导入节点 (批次大小: {batch_size})...')
    
    nodes_count = 0
    batch = []
    
    with open(nodes_file, 'r') as f:
        for line in f:
            if line.strip():
                node = json.loads(line.strip())
                batch.append(node)
                
                if len(batch) >= batch_size:
                    # Process batch
                    with session.begin_transaction() as tx:
                        for node in batch:
                            node_id = node['id']
                            label = node['label']
                            attrs = node.get('attrs', {})
                            
                            query = f"""
                            MERGE (n:{label} {{id: $id}})
                            SET n += $attrs
                            """
                            tx.run(query, id=node_id, attrs=attrs)
                    
                    nodes_count += len(batch)
                    batch = []
                    
                    if nodes_count % 10000 == 0:
                        print(f'  已导入 {nodes_count} 个节点...')
        
        # Process remaining nodes
        if batch:
            with session.begin_transaction() as tx:
                for node in batch:
                    node_id = node['id']
                    label = node['label']
                    attrs = node.get('attrs', {})
                    
                    query = f"""
                    MERGE (n:{label} {{id: $id}})
                    SET n += $attrs
                    """
                    tx.run(query, id=node_id, attrs=attrs)
            
            nodes_count += len(batch)
    
    return nodes_count

def batch_import_edges(session, edges_file, batch_size=1000):
    """Import edges in batches for better performance"""
    print(f'🔗 批量导入关系 (批次大小: {batch_size})...')
    
    edges_count = 0
    edge_type_counts = {}
    batch = []
    
    with open(edges_file, 'r') as f:
        for line in f:
            if line.strip():
                edge = json.loads(line.strip())
                batch.append(edge)
                
                if len(batch) >= batch_size:
                    # Process batch
                    with session.begin_transaction() as tx:
                        for edge in batch:
                            edge_type = edge['type']
                            src_id = edge['src']
                            dst_id = edge['dst']
                            attrs = edge.get('attrs', {})
                            
                            query = f"""
                            MATCH (src {{id: $src_id}})
                            MATCH (dst {{id: $dst_id}})
                            MERGE (src)-[r:{edge_type}]->(dst)
                            SET r += $attrs
                            """
                            tx.run(query, src_id=src_id, dst_id=dst_id, attrs=attrs)
                            edge_type_counts[edge_type] = edge_type_counts.get(edge_type, 0) + 1
                    
                    edges_count += len(batch)
                    batch = []
                    
                    if edges_count % 10000 == 0:
                        print(f'  已导入 {edges_count} 条关系...')
        
        # Process remaining edges
        if batch:
            with session.begin_transaction() as tx:
                for edge in batch:
                    edge_type = edge['type']
                    src_id = edge['src']
                    dst_id = edge['dst']
                    attrs = edge.get('attrs', {})
                    
                    query = f"""
                    MATCH (src {{id: $src_id}})
                    MATCH (dst {{id: $dst_id}})
                    MERGE (src)-[r:{edge_type}]->(dst)
                    SET r += $attrs
                    """
                    tx.run(query, src_id=src_id, dst_id=dst_id, attrs=attrs)
                    edge_type_counts[edge_type] = edge_type_counts.get(edge_type, 0) + 1
            
            edges_count += len(batch)
    
    return edges_count, edge_type_counts

def main():
    print('🚀 优化的Neo4j导入脚本')
    print('=' * 50)
    
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    with driver.session() as session:
        # Clear database
        print('🧹 清理数据库...')
        session.run('MATCH (n) DETACH DELETE n')
        
        # Import with optimized batching
        nodes_count = batch_import_nodes(session, 'ast_out_index_all_fixed/nodes.jsonl')
        edges_count, edge_type_counts = batch_import_edges(session, 'ast_out_index_all_fixed/edges.jsonl')
        
        print(f'✅ 优化导入完成: {nodes_count} 节点, {edges_count} 关系')
        
        # Create indexes
        create_indexes(session)
    
    driver.close()

if __name__ == "__main__":
    main()
