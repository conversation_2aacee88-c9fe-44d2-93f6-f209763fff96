# 大型 iOS 多仓库代码知识图谱完整方案

> 目标：在 **Hammer-workspace**（多 Git 仓库源代码 + CocoaPods 第三方库）之上，生成覆盖 *全部源代码* 的 **精准知识图谱**，支持跨仓库的引用 / 调用 / 依赖分析及 GraphRAG 问答。

---
## 0. 总览

阶段 | 关键产物 | 关键技术
--- | --- | ---
发现与拉取仓库 | repo 列表 + commit 锁定 | Git / hammerpods
统一构建配置 | workspace + scheme matrix | Xcodebuild / xcconfig 合并
编译数据库生成 | `compile_commands.json` (全局) | xcpretty / Bear / layered_compile_db.py
AST 解析 | `*.ast.json` 或直接写入图谱 | libClang Python Bindings
图谱建模与导入 | Neo4j / TigerGraph | Cypher / bulk loader
增量更新 & CI | Graph diff + Git hook | GitHub / GitLab CI
验证与指标 | 覆盖度 / 完整性 / 正确率 | 单元测试 + 随机抽样

---
## 1. 仓库发现 & 拉取

1. **仓库列表收集**
   - 主工程 Podfile/hammerpods 列出所有内部组件仓库 URL。
   - 递归解析 `.podspec` 的 `dependency` 字段补全间接依赖。
2. **统一源码目录结构**
   - 建议 `repos/<repo_name>` 或维持 hammer-workspace 现有结构。
3. **版本锁定**
   - 依据 Podfile.lock / hammerpods commit 字段 checkout 指定 commit，确保后续重复构建一致。

---
## 2. 统一构建配置

多仓库往往各自使用不同 Xcode 项目或 scheme：

1. **生成混编 Workspace**
   - 方案 A: 复用 `TencentMap.xcworkspace`（母工程）+ `Pods` target；即可覆盖 90% 源文件。
   - 方案 B: 使用 `xcodegen` 生成虚拟 workspace，将各 repo 的 `.xcodeproj` 汇入并统一 deployment target / 架构。
2. **xcconfig 归并**
   - 运用 CocoaPods `post_install` 钩子自动调整 `EXCLUDED_ARCHS*`、`VALID_ARCHS` 等冲突字段。
3. **Scheme Matrix**
   - 如果单一 scheme 无法编译所有文件，记录“待编译 target → scheme”映射表，后续批量执行。

---
## 3. 全局编译数据库 (`compile_commands.json`)

### 3.1 生成方法

| 方法 | 优点 | 缺点 |
| --- | --- | --- |
| xcpretty + Xcodebuild (推荐) | 无需额外依赖，官方支持 | 需循环多 scheme，输出需后处理 |
| Bear + clang | 适用于 Makefile 工程 | Xcode 项目集成复杂 |

**脚本步骤**（示例）：
```bash
schemes=(TencentMap QMapUIKit ...)   # 由 scheme matrix 脚本动态计算
for SCHEME in "${schemes[@]}"; do
  xcodebuild -workspace TencentMap.xcworkspace \
             -scheme "$SCHEME" \
             -configuration Debug -sdk iphonesimulator \
             -destination 'platform=iOS Simulator,name=iPhone 16 Pro' \
             clean build | \
    xcpretty --report json-compilation-database \
             --output build/$SCHEME.json
 done
python3 tools/layered_compile_db.py build/*.json -o compile_commands.json
```

### 3.2 合并与去重关键要点

1. **路径规范化**：全部转 **绝对路径**，解决不同 repo 相对路径错配。
2. **重复记录去重**：同一源文件可能被多个 target 编译；保留参数 union 或首条记录。
3. **Header-only 文件**：clang 需 `-include` 指令才能进入编译数据库，必要时扫描 `.pch`/Header Maps。
4. **覆盖率评估**：计算 `#source_files_in_repo / #entries_in_db`，确保 >95%。

---
## 4. AST 解析 (libClang + Python)

### 4.1 解析流程

```python
from clang.cindex import Index
index = Index.create()
for cmd in compile_db:
    tu = index.parse(cmd['file'], args=cmd['arguments'])
    walk(tu.cursor)  # DFS/BFS 提取实体
```

抽取节点 → 映射实体：
- `FUNCTION_DECL` → Function
- `VAR_DECL` → Variable
- `CALL_EXPR` → Call edge (src→dst)
- `INCLUSION_DIRECTIVE` → Include edge

### 4.2 性能优化
- **多进程**：按 translation unit 分 sharding。
- **libclang path** 固定到 Xcode toolchain。
- **缓存**：对头文件 TU 结果做增量 hash。

---
## 5. 图谱模型设计

### 5.1 节点类型
| Label | 属性 |
| --- | --- |
| File | path, repo, language |
| Function | name, returnType, isStatic, loc |
| Class | name, superclass |
| Variable | name, type, storage |
| Namespace/Module | name |

### 5.2 关系
| 类型 | 含义 |
| --- | --- |
| `CALLS` | Function → Function |
| `REFERS_TO` | Variable/Parameter → Type |
| `DEFINED_IN` | Entity → File |
| `IMPORTS` | File → File |
| `INHERITS` | Class → Class |

### 5.3 导入 Neo4j

1. **批量 CSV**：解析阶段直接写 CSV，使用 `neo4j-admin import`。
2. **增量**：Python 驱动 + `UNWIND` 小批次 MERGE。

---
## 6. 跨仓库关系保障

- 关键在 **统一文件路径** 与 **Compile DB 完整性**。
- libClang 解析时即会解析 `#include` 指向的其他仓库头文件，从而产生调用/引用边。
- 对二进制 Pod：若缺源码，只能获取 symbol stub；考虑：
  1. 让 hammerpods fallback 到源码（已自动）
  2. 若仍无源码 → 通过 `nm -gU` 抽取符号 + `otool -Iv` 获得调用边，写入额外边类型 `BIN_CALLS`。

---
## 7. 增量更新 & CI

1. **Git hook**：push 触发 GitHub Action → 重跑受影响 TU → Neo4j diff MERGE。
2. **Snapshot**：保存图谱版本号 = Git commit hash，支持回溯。

---
## 8. 质量验证

指标 | 方法 | 门限
--- | --- | ---
实体覆盖率 | `#function_in_graph / clang-index` | > 90%
调用边正确率 | 随机 200 边人工抽检 | > 95%
编译数据库覆盖 | `#m_files/#entries` | > 95%

---
## 9. 里程碑与人力估算

阶段 | 周期 | 负责人
--- | --- | ---
PoC (单仓库) | 1 周 | A
多仓库编译数据库 | 2 周 | B
AST 解析 & 图谱导入 | 2 周 | C
增量更新 & CI | 1 周 | D
验证与文档 | 1 周 | A/C

---
## 10. 参考工具 & 资源
- **clangd-indexer**：可生成全局索引，可对比 libclang 方案。
- **SourceGraph / LSIF**：图谱建模的业界实践。
- **GraphRAG**：Neo4j + GPT-4 Turbo.

---
## 11. 风险 & 缓解

风险 | 描述 | 缓解措施
--- | --- | ---
二进制 Pod 无源码 | 图谱缺边 | 让供应方开放源码或使用二进制符号抽取
编译数据库缺失 | 头文件未编译 | 强制 `-H` 统计、增补虚拟 TU
解析性能 | TU > 1w，耗时长 | 分布式解析 + 缓存

---
## 12. 下一步

1. 确认 scheme matrix & 覆盖率报告脚本实现。
2. 启动 PoC：选 `QMapFoundation` 仓库跑通流程。
3. εβδομαδιαία（每周）评审进度，逐步扩展到全部仓库。

---

### 12.3 使用 xcpretty-json-compilation-database 生成 clang 编译数据库

为了获得 *libClang* 可直接消费的 `compile_commands.json`，采用社区成熟的 `xcpretty-json-compilation-database`（XCPJCD）工具。

步骤：
1. 安装（macOS 自带 Ruby 环境）：
   ```bash
   sudo gem install xcpretty xcpretty-json-compilation-database
   ```
2. 生成 DB（以 `QMapFoundation` Scheme 为例）：
   ```bash
   xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace \
              -scheme QMapFoundation \
              -configuration Debug \
              -sdk iphonesimulator \
              build | \
   xcpretty -r json-compilation-database -o compile_commands_xcpretty.json
   ```
   - `xcpretty` 将 Xcode 原始 build log 转换为 clang 调用；
   - 输出路径可自定义；推荐统一放在仓库根。
3. 多 Scheme 情况：
   ```bash
   while read SCHEME; do
     xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace \
                -scheme "$SCHEME" -configuration Debug -sdk iphonesimulator build | \
       xcpretty -r json-compilation-database -o build/db_$SCHEME.json
   done < schemes.txt
   python3 scripts/layered_compile_db.py build/db_*.json -o compile_commands.json
   ```
4. 解析 AST：
   ```bash
   CLANG_LIBRARY_FILE=/opt/homebrew/opt/llvm/lib/libclang.dylib \
   python3 scripts/ast_extractor.py compile_commands_xcpretty.json --out ast_out
   ```

> 相比 Bear，此方法无需 DYLD 注入，对 Xcode 15 新 Build System 100% 有效。若后续出现未知 clang 参数，可继续沿用 `ast_extractor.py` 的白名单过滤。

### 14.3 PoC 节点 / 边统计（前 200 TU）

| 类型 | 计数 |
| --- | --- |
| File | 64 |
| Class | 83 |
| Method | 1,122 |

| 边类型 | 计数 |
| --- | --- |
| DEFINES | 2,538 |
| MEMBER_OF | 529 |
| CALLS | 225 |
| INHERITS | 3 |

> **说明**：仅对 `compile_commands_xcpretty.json` 前 200 条编译命令试跑，用于快速验证管线通路。计数与期望规模相符。

---

## 13. 进度追踪（2025-07-07）

已完成 ✅
| 编号 | 事项 |
| --- | --- |
| 1 | 初版《大型 iOS 多仓库代码知识图谱完整方案》撰写（本文件） |
| 2 | `scripts/gen_scheme_matrix.py`：解析 workspace 输出可编译 scheme |
| 3 | `scripts/layered_compile_db.py`：合并多份 compile DB 去重 |
| 4 | `scripts/gen_compile_db.sh`：循环 scheme 调用 xcodebuild 生成/合并 compile DB |
| 5 | 运行 `gen_scheme_matrix.py` 并生成 `schemes.txt`（~160+ schemes） |
| 6 | 启动 QMapFoundation PoC 构建，生成 `build/QMapFoundation.json` ✅ |
| 7 | 合并生成首个 `compile_commands.json` ✅ |

待完成 ⏳
| 编号 | 事项 |
| --- | --- |
| A | —— |
| B | —— |
| C | 全 Scheme 生成 compile DB，得到全局 `compile_commands.json` ✅ |
| D | 恢复/重写 `cores/ast_analyzer.py`，支持 Neo4j 写入 |
| E | 跑 AST 解析 → 图谱导入全流程，输出实体/关系统计 |
| F | 编写覆盖率 / 正确率验证脚本并接入 CI |
| G | 处理二进制 Pod 符号抽取脚本 |

---
## 14. AST → 中间层 Schema v1

### 14.1 顶点设计
仅保留三种 **核心节点**，符合“最小可用并可扩展”原则：
| Label | 关键属性 | 说明 |
| --- | --- | --- |
| File | `path`, `repo`, `language`, `hash` | 源文件（.m/.mm/.c/.cpp/.h）唯一节点 |
| Class | `usr`, `name`, `kind`(ObjC/CPP), `loc` | Objective-C/Swift/CPP 类或协议 / struct / enum 统一抽象 |
| Method | `usr`, `name`, `signature`, `isInstance`, `loc` | 包含函数 & 类方法；顶层 C 函数视作 `Class='__GLOBAL__'` 特例 |

> 变量、宏、字段等后续可作为 Method 的属性或等待 v2 扩展。

### 14.2 边类型
| 关系 | 起点 → 终点 | 语义 |
| --- | --- | --- |
| DEFINES   | File → {Class\|Method} | File 声明/实现了实体 |
| MEMBER_OF | Method → Class | Method 隶属于类；C 函数指向 `__GLOBAL__` 虚拟类 |
| INHERITS  | Class → Class | 继承 / 实现 / 协议遵循 |
| CALLS     | Method → Method | 显式或隐式函数/方法调用 |
| IMPORTS   | File → File | `#import/#include` 依赖 |

附加属性：`loc_from`, `loc_to`, `isVirtual`, `callKind` 等放入 edge.attrs 便于后续可视化。

### 14.3 中间层格式
仍采用 JSON Lines：
- `nodes.jsonl` 每行 `{"label":"Class","id":"usr...","attrs":{...}}`
- `edges.jsonl` 每行 `{"type":"CALLS","src":"usr1","dst":"usr2","attrs":{...}}`

这样既可导入 Neo4j，也兼容 GraphQL / Spark。

### 14.4 评估
优点：
1. 结构足够简单，前端查询语义清晰 (`(m:Method)-[:CALLS]->(n:Method)`).
2. 解析实现量小：只关注 CursorKind(FunctionDecl / ObjCMethodDecl / CXXMethod) 与类/文件。 
3. 后续可向 v2 无痛加 Variable/Macro 等节点，不破坏已有边。

可接受的局限：
- 无变量粒度依赖 => 无法做精确 data-flow；但对代码理解 / 调用链分析已足够。
- 宏展开、模板实例化信息仍缺失，需要二期处理。

综上：**采用 File | Class | Method 三节点模型作为 v1 实施方案可行。**

---
> 文档如需调整，请在 `docs/code_knowledge_graph_plan.md` 直接编辑.

---

## 15. 跨 TU 完整符号映射（index-store-db 方案）

> **背景**：当前 AST 抽取阶段，如果调用目标的方法/函数只在其他目标的二进制或 Swift 模块中实现，`libClang` 无法在当前 TU 内解析其定义，导致缺失 `DEFINES` 边，从而统计得到的跨仓库调用为 0。
>
> Apple 在 Xcode 中提供的 *index-store* 保存了编译期间对每个符号（USR）→ 文件路径的静态索引，天然跨 TU。利用它即可补全缺失定义。

### 15.1 目标
1. 每个符号（ObjC/C/C++/Swift）都能在图中找到唯一定义文件。
2. 不修改现有 AST 抽取逻辑的主体，仅在缺失定义时查补。
3. 精确统计跨仓库 `CALLS` 边。

### 15.2 实施步骤
1. **生成 index-store**
   ```bash
   xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace \
              -scheme TencentMap \
              -configuration Debug \
              -derivedDataPath DerivedData/GraphIndex \
              clean build
   ```
   生成目录：`DerivedData/GraphIndex/Index/DataStore/`
2. **导出符号 → 文件映射**
   使用 Xcode CLI 工具：
   ```bash
   STORE=DerivedData/GraphIndex/Index/DataStore
   DB=$(mktemp /tmp/index.sqlite.XXXX)
   index-import -store "$STORE" -db "$DB" -quiet
   sqlite3 "$DB" <<'SQL'
   .once index_symbols.jsonl
   SELECT json_object('usr', usr, 'path', canonical_path)
   FROM occurrences o JOIN files f ON o.file_id = f.id
   WHERE o.roles & 1; -- DECLARATION|DEFINITION
   SQL
   ```
   得到 `index_symbols.jsonl`，每行 `{"usr":"...","path":"..."}`。
3. **在 `ast_extractor.py` 中查补定义**
   ```python
   with open('index_symbols.jsonl') as f:
       XINDEX = {j['usr']: j['path'] for j in map(json.loads, f)}
   ...
   if ref_usr and ref_usr not in defined_methods:
       if ref_usr in XINDEX:
           def_path = XINDEX[ref_usr]
           def_file_id = sha1(def_path)
           nodes.add(node_line('File', def_file_id, {'path': def_path}))
           edges.add(edge_line('DEFINES', def_file_id, ref_usr))
   ```
4. **复用 `detect_repo()`** 对 `def_path` 做仓库归属解析。
5. **验证**：
   - 重新抽取后缺失 `dst_method` 应为 0；
   - `graph_stats.py cross-repo` 应输出非零跨仓库调用数；
   - 随机抽样 CALLS 边人工核验。

### 15.3 评估
| 维度 | 说明 |
| --- | --- |
| 准确率 | 依托 Xcode 官方索引，符号→文件映射权威；可覆盖 Swift / 静态库 |
| 性能 | index-store 导入一次即可，多仓库增量编译仍保持；json 查表为 O(1) |
| 工程量 | 脚本 +100 行左右，改动 `ast_extractor.py` < 30 行 |
| 依赖 | 仅 Xcode 自带工具，无外部包 |

> **时间预估**：1.5 天（脚本 & 集成 1 天 + 0.5 天测试/边缘 case）。

### 15.4 实施结果 (2025-07-08)

#### 执行步骤

1. **环境准备**
   ```bash
   # 设置 Clang 库路径
   export CLANG_LIBRARY_FILE=/opt/homebrew/opt/llvm/lib/libclang.dylib
   ```

2. **提取符号映射**
   ```bash
   cd /Users/<USER>/GRAG_iOS/tools/indexstore-db
   swift run -c release dump-usrs \
     --store ../../DerivedData/GraphIndex/Index.noindex/DataStore \
     --out ../../index_symbols.jsonl
   ```
   - 输入：`DerivedData/GraphIndex/Index.noindex/DataStore` (Xcode 索引数据)
   - 输出：`index_symbols.jsonl` (204,245 条 USR→文件路径映射)

3. **执行 AST 提取**
   ```bash
   cd /Users/<USER>/GRAG_iOS
   python3 scripts/ast_extractor.py compile_commands_all4.json \
     --index index_symbols.jsonl \
     --out ast_out_index
   ```
   - 输出：
     - `ast_out_index/nodes.jsonl` (42,378 个节点)
     - `ast_out_index/edges.jsonl` (70,832 条边)

4. **生成统计信息**
   ```bash
   # 跨仓库调用统计
   python3 scripts/graph_stats.py cross-repo \
     --nodes ast_out_index/nodes.jsonl \
     --edges ast_out_index/edges.jsonl
   
   # 外部/未解析调用统计
   python3 scripts/graph_stats.py external \
     --nodes ast_out_index/nodes.jsonl \
     --edges ast_out_index/edges.jsonl
   ```

#### 结果汇总

- **索引数据**：提取 204,245 条 USR→文件路径映射 (`index_symbols.jsonl`)
- **AST 抽取结果**：
  - 节点：42,378
  - 边：70,832
- **调用边统计**：
  - 总 `CALLS` 边：13,238
  - 跨仓库调用：1,474 (11.1%)
  - 外部/未解析调用：1,333 (10.1%)

> 注意：部分文件（主要是 Hippy 相关组件）在 AST 解析时出现错误，这些错误可能导致未解析调用数增加。

### 15.5 后续计划
1. **降低未解析率**
   - 调查 Hippy pods 的解析错误，调整编译标志或排除非必要组件
   - 为 `ast_extractor.py` 添加重试逻辑（如回退到 `-fmodules` 标志）

2. **性能优化**
   - 缓存 `index_symbols.jsonl` 并支持增量更新
   - 并行化 AST 抽取过程

3. **CI 集成**
   - 添加 Makefile/GitHub Actions 工作流，实现夜间自动构建和统计

4. **代码整理**
   - 将脚本重构为 Python 模块
   - 更新 README 文档，添加端到端使用示例

---


