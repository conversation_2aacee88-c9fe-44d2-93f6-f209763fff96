# 改进的Pipeline架构设计

## 当前Makefile问题分析

### 主要问题
1. **错误处理缺失**: 步骤失败时无法优雅处理，继续执行后续步骤
2. **依赖验证不足**: 不检查必需工具/文件是否存在
3. **无部分恢复**: 失败后必须重新开始整个流程
4. **进度跟踪缺失**: 无法了解步骤完成状态
5. **硬编码路径**: 无法适配不同环境
6. **缺乏测试框架**: 无法验证单个步骤
7. **串行执行**: 无并行化优化

## 改进的架构设计

### 核心原则
- **模块化**: 每个步骤独立可测试
- **可恢复**: 支持从任意步骤重新开始
- **可配置**: 支持不同环境和参数
- **可观测**: 详细日志和进度跟踪
- **健壮性**: 智能重试和错误恢复

### 架构组件

#### 1. Pipeline核心模块 (`pipeline/core.py`)
```python
class PipelineStep:
    """单个流水线步骤的抽象基类"""
    def validate_prerequisites(self) -> bool
    def execute(self) -> StepResult
    def cleanup_on_failure(self) -> None
    def get_progress(self) -> float

class Pipeline:
    """流水线管理器"""
    def add_step(self, step: PipelineStep)
    def run(self, start_from: str = None)
    def get_status(self) -> PipelineStatus
```

#### 2. 步骤实现 (`pipeline/steps/`)
- `clean_step.py`: 清理步骤
- `index_store_step.py`: 索引生成步骤
- `usrs_step.py`: USR提取步骤
- `cdb_step.py`: 编译数据库生成步骤
- `ast_step.py`: AST抽取步骤
- `stats_step.py`: 统计验证步骤

#### 3. 配置管理 (`pipeline/config.py`)
```python
@dataclass
class PipelineConfig:
    workspace_path: str
    schemes: List[str]
    output_dir: str
    derived_data_path: str
    parallel_jobs: int = 4
    retry_attempts: int = 3
```

#### 4. 状态管理 (`pipeline/state.py`)
```python
class PipelineState:
    """持久化流水线状态，支持断点续传"""
    def save_checkpoint(self, step_name: str)
    def load_checkpoint(self) -> Optional[str]
    def clear_state(self)
```

#### 5. 测试框架 (`tests/`)
```
tests/
├── unit/
│   ├── test_clean_step.py
│   ├── test_index_store_step.py
│   ├── test_usrs_step.py
│   ├── test_cdb_step.py
│   ├── test_ast_step.py
│   └── test_stats_step.py
├── integration/
│   ├── test_full_pipeline.py
│   └── test_partial_recovery.py
└── fixtures/
    ├── sample_workspace/
    └── expected_outputs/
```

### 执行流程

#### 1. 预检查阶段
- 验证环境依赖 (Xcode, Python packages, tools)
- 检查配置文件有效性
- 验证输入文件/目录存在

#### 2. 执行阶段
- 按依赖顺序执行步骤
- 每步完成后保存检查点
- 实时进度报告和日志记录

#### 3. 验证阶段
- 检查输出文件完整性
- 运行质量验证测试
- 生成执行报告

### 错误处理策略

#### 1. 重试机制
- 网络相关错误: 指数退避重试
- 编译错误: 清理后重试
- 资源不足: 等待后重试

#### 2. 部分失败恢复
- 文件级别的增量处理
- 跳过已成功的子任务
- 智能依赖分析

#### 3. 错误分类
- **可恢复错误**: 自动重试
- **配置错误**: 提示用户修复
- **环境错误**: 提供解决方案
- **致命错误**: 安全停止并保存状态

### 并行化策略

#### 1. 步骤内并行
- AST抽取: 按文件并行处理
- 编译数据库: 按scheme并行生成

#### 2. 步骤间并行
- 独立步骤可并行执行
- 智能依赖图分析

### 监控和可观测性

#### 1. 进度跟踪
- 实时进度条显示
- 预估剩余时间
- 资源使用监控

#### 2. 日志系统
- 结构化日志输出
- 不同级别的详细程度
- 错误堆栈跟踪

#### 3. 指标收集
- 执行时间统计
- 成功率监控
- 资源消耗分析

## 实施计划

### Phase 1: 核心框架
1. 实现Pipeline核心类
2. 创建基础步骤抽象
3. 添加配置管理

### Phase 2: 步骤迁移
1. 将现有脚本包装为步骤类
2. 添加错误处理和重试
3. 实现状态持久化

### Phase 3: 测试框架
1. 创建单元测试套件
2. 添加集成测试
3. 实现CI/CD集成

### Phase 4: 优化增强
1. 添加并行化支持
2. 实现智能重试
3. 优化性能和资源使用

## 预期收益

1. **可靠性提升**: 错误处理和重试机制
2. **开发效率**: 快速定位和修复问题
3. **维护性**: 模块化设计便于扩展
4. **可测试性**: 完整的测试覆盖
5. **用户体验**: 清晰的进度反馈和错误提示