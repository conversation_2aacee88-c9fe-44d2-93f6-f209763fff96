
# QMap 代码图谱 Pipeline（QMapBusiness + QMapMiddlePlatform）

> 本文档描述在 **QMapBusiness** 与 **QMapMiddlePlatform** 两个代码仓库上，从源码到知识图谱的完整自动化流程。后续实现与调试都以此为唯一来源。

## 流程总览
| 步骤 | 产物 | 关键脚本 / 技术 |
| --- | --- | --- |
| 0. 清理输出目录 | `DerivedData/GraphIndex`、`compile_commands/`、`ast_out_index/` | `scripts/clean.sh` |
| 1. 生成 index-store | `Index.noindex/DataStore` | `xcodebuild` |
| 2. 提取 USR→文件映射 | `index_symbols.jsonl` | `tools/indexstore-db/dump-usrs` |
| 3. 生成 compile DB | `compile_commands/{QMapBusiness,QMapMiddlePlatform}_cdb.json` | `scripts/batch_gen_cdb.py` |
| 4. AST 抽取 | `ast_out_index/{{nodes,edges}.jsonl}` | `scripts/ast_extractor.py` |
| 5. 图统计 & 校验 | 统计表 / 终端输出 | `scripts/graph_stats.py` |

### 成功标准
1. 四类节点（File / Class / Method / Namespace）与五类边（DEFINES / MEMBER_OF / INHERITS / CALLS / IMPORTS）计数均 > 0。
2. `graph_stats.py cross-repo` 输出的跨仓库 `CALLS` 边计数 > 0。
3. 任一计数为 0 视为异常，CI 应立即 fail 并输出错误日志。

---

## 更新记录
| 日期 | 修改内容 |
| --- | --- |
| 2025-07-08 | • 修复 Step-3 代码块排版<br>• Step-5 增加统计示例输出<br>• 新增 `Makefile` 一键脚本与 GitHub Action CI 配置 |

## 详细实现方式
### 环境准备
```bash
brew install llvm protobuf
python3 -m venv .venv && source .venv/bin/activate
pip install "clang==15.*" neo4j
export CLANG_LIBRARY_FILE=/opt/homebrew/opt/llvm/lib/libclang.dylib
```

### Step-0 清理
```bash
./scripts/clean.sh  # 删除 DerivedData / ast_out_index 等历史产物
```

### Step-1 生成 index-store
```bash
SCHEMES=(QMapBusiness QMapMiddlePlatform)
for S in "${SCHEMES[@]}"; do
  xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace \
             -scheme "$S" \
             -configuration Debug -sdk iphoneos \
             -destination 'generic/platform=iOS' \
             -derivedDataPath DerivedData/GraphIndex \
             COMPILER_INDEX_STORE_ENABLE=YES clean build
done
```

### Step-2 提取符号映射
```bash
cd tools/indexstore-db
swift run -c release dump-usrs \
  --store ../../DerivedData/GraphIndex/Index.noindex/DataStore \
  --out ../../index_symbols.jsonl
cd ../../
```

### Step-3 生成 compile_commands.json
```bash
python3 scripts/batch_gen_cdb.py \
  --workspace hammmer-workspace/TencentMap.xcworkspace \
  --out-dir compile_commands \
  --prefix 'QMapBusiness|QMapMiddlePlatform'
```

### Step-4 AST 抽取
```bash
env PYTHONWARNINGS="ignore" \
python3 scripts/ast_extractor.py \
        compile_commands/QMapBusiness_cdb.json \
        compile_commands/QMapMiddlePlatform_cdb.json \
        --index index_symbols.jsonl \
        --out ast_out_index
```

### Step-5 统计与校验
```bash
python3 scripts/graph_stats.py cross-repo \
        --nodes ast_out_index/nodes.jsonl \
        --edges ast_out_index/edges.jsonl
python3 scripts/graph_stats.py external \
        --nodes ast_out_index/nodes.jsonl \
        --edges ast_out_index/edges.jsonl
```
若脚本检测到任意节点或边计数为 0，或跨仓库调用计数为 0，将退出码设为非 0，CI 直接失败。

---

## 后续工作
1. 将上述步骤串联为 `make pipeline` 一键脚本。
2. 接入 GitHub Action：push/PR 自动执行并上传统计结果。
3. 持续修复脚本依赖与兼容性问题，确保在 Xcode 更新后仍可运行。

### `Makefile` 示例
```make
# 顶层 Makefile（位于仓库根）

.PHONY: pipeline clean

# 一键跑完整流程：生成索引 → USR → compile DB → AST → 统计
pipeline: clean index-store usrs cdb ast stats

clean:
	./scripts/clean.sh

index-store:
	./scripts/build_schemes.sh QMapBusiness QMapMiddlePlatform

usrs:
	cd tools/indexstore-db && swift run -c release dump-usrs \
		--store ../../DerivedData/GraphIndex/Index.noindex/DataStore \
		--out ../../index_symbols.jsonl && cd ../../

cdb:
	python3 scripts/batch_gen_cdb.py \
		--workspace hammmer-workspace/TencentMap.xcworkspace \
		--out-dir compile_commands \
		--prefix 'QMapBusiness|QMapMiddlePlatform'

ast:
	env PYTHONWARNINGS="ignore" python3 scripts/ast_extractor.py \
		compile_commands/QMapBusiness_cdb.json \
		compile_commands/QMapMiddlePlatform_cdb.json \
		--index index_symbols.jsonl \
		--out ast_out_index

stats:
	python3 scripts/graph_stats.py cross-repo \
		--nodes ast_out_index/nodes.jsonl \
		--edges ast_out_index/edges.jsonl && \
	python3 scripts/graph_stats.py external \
		--nodes ast_out_index/nodes.jsonl \
		--edges ast_out_index/edges.jsonl
```

执行 `make pipeline` 即可在本地完整复现流程。

### GitHub Action CI 配置
在 `.github/workflows/pipeline.yml` 中添加以下内容，推送或 PR 时自动运行：
```yaml
name: QMap Graph Pipeline

on:
  pull_request:
    paths:
      - '**/*.swift'
      - '**/*.m'
      - '**/*.mm'
      - 'scripts/**'
      - '.github/workflows/pipeline.yml'
  push:
    branches: [ main ]

jobs:
  build-graph:
    runs-on: macos-14 # macOS Sonoma + Xcode 15

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install deps
        run: |
          brew install llvm protobuf swift-format
          python -m pip install --upgrade pip
          pip install "clang==15.*" neo4j

      - name: Run pipeline
        run: make pipeline

      - name: Archive stats
        uses: actions/upload-artifact@v4
        with:
          name: graph_stats
          path: ast_out_index/*
```

这样即可在 CI 中持续验证成功标准。

----

## 附录：旧版详细步骤
以下内容保留旧版命令行细节，供排查差异时参考。

### 环境准备
1. 安装更新Xcode>=15
2. brew install llvm
3. 安装python依赖（使用虚拟环境venv）：pip install "clang==15.*"
4. 设置libclang路径：
   export CLANG_LIBRARY_FILE=/opt/homebrew/opt/llvm/lib/libclang.dylib
5. brew install protobuf

# 单次跑通流程

## 1. 生成 Xcode index-store

> 目标：在 `hammmer-workspace` 下为 **QMapBusiness**、**QMapMiddlePlatform**（或所有 scheme）产出 `Index.noindex/DataStore` 目录。

### 1.1 仅为单个 Scheme 生成索引
```bash
# 清理旧的构建文件
rm -rf DerivedData/
rm -rf build/
xcodebuild clean -workspace hammmer-workspace/TencentMap.xcworkspace -scheme QMapBusiness

# 重新安装 Pods
cd hammmer-workspace
pod install
cd ..

# 示例：仅编译 QMapBusiness
xcodebuild \
  -workspace hammmer-workspace/TencentMap.xcworkspace \
  -scheme QMapBusiness \
  -configuration Debug \
  -sdk iphoneos \
  -destination 'generic/platform=iOS' \
  -derivedDataPath DerivedData/GraphIndex \
  COMPILER_INDEX_STORE_ENABLE=YES \
  build

```
结果路径：`DerivedData/GraphIndex/Index.noindex/DataStore`

### 1.2 为多个指定 Scheme 生成索引
```bash
SCHEMES=(QMapBusiness QMapMiddlePlatform)
for S in "${SCHEMES[@]}"; do
  xcodebuild -workspace hammmer-workspace/TencentMap.xcworkspace \
             -scheme "$S" \
             -configuration Debug \
             -derivedDataPath DerivedData/GraphIndex \
             COMPILER_INDEX_STORE_ENABLE=YES build
done
```

### 1.3 仅为 **QMapBusiness** 生成索引（推荐）
```bash
# ⚙️ Step-1: Build QMapBusiness with Index-Store enabled
xcodebuild \
  -workspace hammmer-workspace/TencentMap.xcworkspace \
  -scheme QMapBusiness \
  -configuration Debug \
  -sdk iphoneos \
  -destination 'generic/platform=iOS' \
  -derivedDataPath DerivedData/GraphIndex \
  COMPILER_INDEX_STORE_ENABLE=YES \
  clean build
```

输出：
** BUILD SUCCEEDED **

该脚本内部会：
1. `xcodebuild -list` 列出全部 schemes
2. 循环编译每个 scheme（只编译一次即可）

> 如只想编译，但暂时不进行后续“提取 USR / AST 抽取”，可加 `--phase build`；完整版流程见后文。

---

## 2. 提取 USR → 文件映射
*(保持与之前步骤一致，可复用同一 DerivedData)*


```bash
cd tools/indexstore-db
swift run -c release dump-usrs \
  --store ../../DerivedData/GraphIndex/Index.noindex/DataStore \
  --out ../../index_symbols.jsonl
cd ../../
```
输出：
```bash
.venv(base) qy@JONAYQIAO-MC1 GRAG_iOS % cd tools/indexstore-db
swift run -c release dump-usrs \
  --store ../../DerivedData/GraphIndex/Index.noindex/DataStore \
  --out ../../index_symbols.jsonl
cd ../../
Building for production...
[1/1] Write swift-version--58304C5D6DBC2206.txt
Build of product 'dump-usrs' complete! (0.23s)
Wrote 369832 symbols → /Users/<USER>/GRAG_iOS/index_symbols.jsonl
```
符号索引表， 其中index_symbols.jsonl包含usr和path

## 3. 生成 compile_commands.json
- 如已有 `compile_commands_all4.json`，此步可跳过。
```bash
# Step-3 为 QMapBusiness 与 QMapMiddlePlatform 生成 compile_commands.json
python3 scripts/batch_gen_cdb.py \
  --workspace hammmer-workspace/TencentMap.xcworkspace \
  --out-dir compile_commands \
  --prefix 'QMapBusiness|QMapMiddlePlatform'

# 结果文件： compile_commands/QMapBusiness_cdb.json 与 compile_commands/QMapMiddlePlatform_cdb.json
输出：
✓ Wrote 314 compile commands → /Users/<USER>/GRAG_iOS/compile_commands.json
Saved compile_commands/QMapBusiness_cdb.json
Saved compile_commands/QMapMiddlePlatform_cdb.json
```
批量为 Xcode 工程中的多个 Scheme 生成 compile_commands.json，从而让 clangd / ccls 等语言服务器能够完成 C/C++/Objective-C 的语义索引。

流程如下：
1.解析命令行参数：工作区路径、Scheme 列表文件、输出目录、前缀过滤条件。
2.读取 schemes.txt → 依据 --prefix 过滤出目标 Scheme。
3.逐一调用同目录下的 scripts/gen_cdb.swift（本质是 xcodebuild … clean build），生成临时的 compile_commands.json。
4.若生成成功，则重命名为 <Scheme>_cdb.json 并移动到 --out-dir；如失败则删除空文件并警告。

## 4. AST 抽取（使用改进版 ast_extractor.py）
```bash
# Step-4 运行 AST 抽取
env PYTHONWARNINGS="ignore" \
python3 scripts/ast_extractor.py \
        compile_commands/QMapBusiness_cdb.json \
        compile_commands/QMapMiddlePlatform_cdb.json \
        --index index_symbols.jsonl \
        --out ast_out_index
```

## 5. 统计调用关系
```bash
python3 scripts/graph_stats.py cross-repo \
        --nodes ast_out_index/nodes.jsonl \
        --edges ast_out_index/edges.jsonl

python3 scripts/graph_stats.py external \
        --nodes ast_out_index/nodes.jsonl \
        --edges ast_out_index/edges.jsonl
```
示例输出：
```text
Nodes  : File=752, Class=1 234, Method=9 876, Namespace=24
Edges  : DEFINES=9 876, MEMBER_OF=11 110, INHERITS=321, CALLS=45 678, IMPORTS=801
Cross-repo CALLS: 1 234
✔ 统计结果符合成功标准
```

---



