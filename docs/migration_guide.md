# Pipeline-All 迁移指南

## 🎯 迁移概述

本指南详细说明如何从当前的2仓库Pipeline (QMapBusiness + QMapMiddlePlatform) 迁移到支持11个仓库的Pipeline-All系统。

### 迁移目标
- **从**: 2仓库 → **到**: 11仓库
- **从**: 20,156节点 → **到**: 35,000+节点
- **从**: 19,117边 → **到**: 30,000+边
- **从**: 2,736跨仓库调用 → **到**: 8,000+跨仓库调用

## 📋 迁移前准备

### 1. 环境检查
```bash
# 检查Python版本 (需要3.8+)
python --version

# 检查可用内存 (建议8GB+)
free -h

# 检查磁盘空间 (建议10GB+)
df -h

# 检查CPU核心数 (建议4核+)
nproc
```

### 2. 备份现有数据
```bash
# 备份当前Pipeline输出
cp -r ast_out_index ast_out_index_backup_$(date +%Y%m%d)

# 备份配置文件
cp pipeline/config.py pipeline/config_backup.py

# 备份运行日志
cp *.log logs_backup/
```

### 3. 依赖检查
```bash
# 检查现有依赖
pip list | grep -E "(concurrent|threading|multiprocessing)"

# 安装新依赖 (如果需要)
pip install -r requirements_all.txt
```

## 🔄 迁移步骤

### 步骤1: 安装Pipeline-All组件

```bash
# 1. 复制新的配置文件
cp pipeline/config_all.py pipeline/
cp pipeline/repository_discovery.py pipeline/

# 2. 复制主控制器
cp pipeline_all_controller.py ./

# 3. 更新核心模块
# (已自动包含PipelineResult类)
```

### 步骤2: 配置仓库发现

```bash
# 1. 运行仓库发现
python discover_repositories.py

# 2. 检查发现结果
cat pipeline_all_repositories.json | jq '.summary'

# 3. 验证仓库配置
python -c "
from pipeline.config_all import PipelineAllConfig
config = PipelineAllConfig()
print(f'发现 {len(config.repositories)} 个仓库')
for repo in config.repositories:
    print(f'  - {repo.name}: {repo.source_files} 源文件')
"
```

### 步骤3: 配置调整

#### 3.1 基础配置
```python
# 编辑 pipeline/config_all.py 中的关键参数
class PipelineAllConfig:
    # 调整工作线程数 (根据CPU核心数)
    max_workers: int = 8  # 建议为CPU核心数
    
    # 调整内存限制 (根据可用内存)
    memory_limit_gb: int = 6  # 建议为可用内存的75%
    
    # 调整超时时间 (根据仓库大小)
    timeout_seconds: int = 3600  # 1小时，可根据需要调整
```

#### 3.2 仓库特定配置
```python
# 可以为特定仓库调整配置
config = PipelineAllConfig()

# 禁用某个仓库 (如果有问题)
repo = config.get_repository_by_name("ProblematicRepo")
if repo:
    repo.enabled = False

# 调整仓库优先级
large_repo = config.get_repository_by_name("QMapBusiness")
if large_repo:
    large_repo.priority = 1  # 最高优先级

config.save_repositories()
```

### 步骤4: 并行运行验证

```bash
# 1. 运行现有Pipeline (作为基准)
echo "运行现有Pipeline..."
python run_pipeline.py > pipeline_v1_output.log 2>&1

# 2. 运行Pipeline-All
echo "运行Pipeline-All..."
python pipeline_all_controller.py > pipeline_all_output.log 2>&1

# 3. 对比结果
echo "对比结果..."
python compare_results.py pipeline_v1_output.log pipeline_all_output.log
```

### 步骤5: 结果验证

#### 5.1 数据质量验证
```python
# 验证脚本
import json
from pathlib import Path

def validate_migration():
    # 加载Pipeline-All结果
    report_file = Path("ast_out_index_all/pipeline_all_report.json")
    if report_file.exists():
        with open(report_file) as f:
            report = json.load(f)
        
        stats = report['processing_results']['aggregated_stats']
        
        print("📊 Pipeline-All结果:")
        print(f"  仓库数: {stats['total_repositories']}")
        print(f"  节点数: {stats['total_nodes']:,}")
        print(f"  边数: {stats['total_edges']:,}")
        print(f"  跨仓库关系: {stats['cross_repo_relationships']:,}")
        
        # 验证改进
        baseline_nodes = 20156
        baseline_edges = 19117
        baseline_cross_repo = 2736
        
        node_improvement = (stats['total_nodes'] - baseline_nodes) / baseline_nodes * 100
        edge_improvement = (stats['total_edges'] - baseline_edges) / baseline_edges * 100
        cross_repo_improvement = (stats['cross_repo_relationships'] - baseline_cross_repo) / baseline_cross_repo * 100
        
        print(f"\n📈 改进幅度:")
        print(f"  节点数: +{node_improvement:.1f}%")
        print(f"  边数: +{edge_improvement:.1f}%")
        print(f"  跨仓库关系: +{cross_repo_improvement:.1f}%")
        
        return stats['total_nodes'] > baseline_nodes
    
    return False

if __name__ == "__main__":
    success = validate_migration()
    print(f"\n✅ 迁移验证: {'成功' if success else '需要调整'}")
```

#### 5.2 性能验证
```bash
# 检查处理时间
echo "Pipeline-All处理时间:"
grep "总耗时" pipeline_all_output.log

# 检查内存使用
echo "内存使用情况:"
grep -i "memory\|内存" pipeline_all_output.log

# 检查错误率
echo "错误统计:"
grep -c "ERROR\|❌" pipeline_all_output.log
```

## 🔧 故障排除

### 常见问题1: 内存不足
**症状**: 进程被系统杀死，出现OOM错误
**解决方案**:
```python
# 调整配置
config = PipelineAllConfig()
config.max_workers = 4  # 减少并行度
config.memory_limit_gb = 4  # 降低内存限制
config.processing_strategy = ProcessingStrategy.SEQUENTIAL  # 改为顺序处理
```

### 常见问题2: 处理时间过长
**症状**: 处理时间超过预期
**解决方案**:
```python
# 优化配置
config = PipelineAllConfig()
config.processing_strategy = ProcessingStrategy.HYBRID  # 使用混合策略
config.chunk_size = 50  # 减少块大小
config.timeout_seconds = 7200  # 增加超时时间
```

### 常见问题3: 某个仓库处理失败
**症状**: 特定仓库总是失败
**解决方案**:
```python
# 临时禁用问题仓库
config = PipelineAllConfig()
problem_repo = config.get_repository_by_name("ProblematicRepo")
if problem_repo:
    problem_repo.enabled = False
    config.save_repositories()
```

### 常见问题4: 跨仓库关系检测不准确
**症状**: 跨仓库关系数量异常
**解决方案**:
```python
# 调整跨仓库分析参数
config = PipelineAllConfig()
config.max_cross_repo_depth = 2  # 减少分析深度
config.cross_repo_analysis = True  # 确保启用
```

## 📊 迁移验证清单

### 功能验证 ✅
- [ ] 所有11个仓库被正确发现
- [ ] 仓库类型分类正确
- [ ] 并行处理正常工作
- [ ] 跨仓库分析产生结果
- [ ] 输出文件格式正确

### 性能验证 ✅
- [ ] 处理时间 < 15分钟
- [ ] 内存使用 < 6GB
- [ ] CPU利用率 > 70%
- [ ] 错误率 < 1%

### 数据质量验证 ✅
- [ ] 节点数 > 28,000
- [ ] 边数 > 20,000
- [ ] 跨仓库关系 > 100
- [ ] 数据完整性检查通过

### 兼容性验证 ✅
- [ ] 输出格式与现有工具兼容
- [ ] API接口保持一致
- [ ] 配置文件向后兼容

## 🚀 切换到生产环境

### 1. 灰度发布
```bash
# 阶段1: 仅处理小型仓库
python pipeline_all_controller.py --repositories="DKProtocolsPool,QMapUIKit"

# 阶段2: 处理中型仓库
python pipeline_all_controller.py --repositories="QMapFoundation,QMapHippy"

# 阶段3: 处理大型仓库
python pipeline_all_controller.py --repositories="QMapBusiness,QMapMiddlePlatform"

# 阶段4: 全量处理
python pipeline_all_controller.py
```

### 2. 监控设置
```bash
# 设置性能监控
echo "设置监控..."
# 添加CPU、内存、磁盘监控
# 设置告警阈值

# 设置日志轮转
echo "配置日志..."
# 配置logrotate
# 设置日志级别
```

### 3. 备份策略
```bash
# 设置自动备份
echo "配置备份..."
# 每日备份输出结果
# 保留最近7天的备份
# 设置备份验证
```

## 📚 后续维护

### 定期任务
- **每周**: 检查新仓库，更新配置
- **每月**: 性能调优，清理缓存
- **每季度**: 架构评估，优化策略

### 监控指标
- **处理时间**: 趋势分析
- **数据质量**: 节点/边数量变化
- **错误率**: 异常检测
- **资源使用**: 容量规划

### 扩展计划
- **新仓库**: 自动发现和集成
- **新功能**: 增量分析，实时更新
- **新平台**: 支持其他代码仓库类型

## ✅ 迁移完成确认

迁移完成后，请确认以下项目：

1. ✅ Pipeline-All能够成功处理所有11个仓库
2. ✅ 数据质量指标达到或超过预期
3. ✅ 性能指标在可接受范围内
4. ✅ 现有工具和流程正常工作
5. ✅ 团队成员了解新的操作流程
6. ✅ 监控和告警系统正常运行
7. ✅ 备份和恢复流程已验证

完成以上确认后，可以正式停用旧的2仓库Pipeline，全面切换到Pipeline-All系统。

---

**注意**: 如果在迁移过程中遇到任何问题，请参考[故障排除指南](troubleshooting_guide.md)或联系技术支持团队。
