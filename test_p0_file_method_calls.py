#!/usr/bin/env python3
"""
TDD Test for P0: Eliminate File->virtual_method Semantic Errors

This test validates the current problem and verifies the fix.
"""

import pytest
from neo4j import GraphDatabase
import json
from pathlib import Path

class TestFileMethodCallsElimination:
    
    @classmethod
    def setup_class(cls):
        """Setup Neo4j connection for testing"""
        cls.driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    @classmethod
    def teardown_class(cls):
        """Close Neo4j connection"""
        cls.driver.close()
    
    def test_current_file_to_virtual_method_calls(self):
        """Test: Verify current File -> virtual_method calls exist (should be 7,228)"""
        with self.driver.session() as session:
            result = session.run('''
                MATCH (f:File)-[:CALLS]->(m:Method {type: 'virtual_method'})
                RETURN count(*) as file_to_virtual_calls
            ''')
            file_to_virtual_calls = result.single()['file_to_virtual_calls']
            
            print(f"📊 当前File -> virtual_method调用: {file_to_virtual_calls:,} 条")
            
            # 验证问题存在
            assert file_to_virtual_calls > 5000, f"Expected >5000 File->virtual_method calls, got {file_to_virtual_calls}"
    
    def test_current_file_to_method_calls_total(self):
        """Test: Verify total File -> Method calls (should be significant)"""
        with self.driver.session() as session:
            result = session.run('''
                MATCH (f:File)-[:CALLS]->(m:Method)
                RETURN count(*) as total_file_to_method_calls
            ''')
            total_calls = result.single()['total_file_to_method_calls']
            
            print(f"📊 当前File -> Method调用总数: {total_calls:,} 条")
            
            # 验证语义错误的严重性
            assert total_calls > 7000, f"Expected >7000 File->Method calls, got {total_calls}"
    
    def test_method_to_method_calls_ratio(self):
        """Test: Verify Method -> Method calls ratio (should be low currently)"""
        with self.driver.session() as session:
            # Method -> Method calls
            result = session.run('''
                MATCH (m1:Method)-[:CALLS]->(m2:Method)
                RETURN count(*) as method_to_method_calls
            ''')
            method_to_method = result.single()['method_to_method_calls']
            
            # Total CALLS relationships
            result = session.run('MATCH ()-[:CALLS]->() RETURN count(*) as total_calls')
            total_calls = result.single()['total_calls']
            
            ratio = method_to_method / total_calls if total_calls > 0 else 0
            
            print(f"📊 Method -> Method调用: {method_to_method:,} / {total_calls:,} ({ratio*100:.1f}%)")
            
            # 当前应该是低比例（因为大量File -> Method调用）
            assert ratio < 0.5, f"Expected Method->Method ratio <50%, got {ratio*100:.1f}%"
    
    def test_virtual_method_distribution(self):
        """Test: Analyze virtual_method distribution"""
        with self.driver.session() as session:
            result = session.run('''
                MATCH (m:Method {type: 'virtual_method'})
                RETURN m.class as class_name, count(*) as count
                ORDER BY count DESC
                LIMIT 10
            ''')
            
            print("\n📊 virtual_method分布 (Top 10):")
            for record in result:
                class_name = record['class_name']
                count = record['count']
                print(f"  {class_name}: {count} 个virtual方法")
    
    def test_file_caller_examples(self):
        """Test: Show examples of File -> virtual_method calls"""
        with self.driver.session() as session:
            result = session.run('''
                MATCH (f:File)-[:CALLS]->(m:Method {type: 'virtual_method'})
                RETURN f.name as file_name, m.name as method_name, m.class as class_name
                LIMIT 10
            ''')
            
            print("\n🔍 File -> virtual_method调用示例:")
            for record in result:
                file_name = Path(record['file_name']).name if record['file_name'] else 'unknown'
                method_name = record['method_name']
                class_name = record['class_name']
                print(f"  {file_name} -> {class_name}::{method_name}")

class TestFileMethodCallsAfterFix:
    """Tests to run after implementing the fix"""
    
    @classmethod
    def setup_class(cls):
        cls.driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    @classmethod
    def teardown_class(cls):
        cls.driver.close()
    
    def test_no_file_to_method_calls(self):
        """Test: Verify File -> Method calls are eliminated (should be 0)"""
        with self.driver.session() as session:
            result = session.run('''
                MATCH (f:File)-[:CALLS]->(m:Method)
                RETURN count(*) as file_to_method_calls
            ''')
            file_to_method_calls = result.single()['file_to_method_calls']
            
            print(f"✅ 修复后File -> Method调用: {file_to_method_calls:,} 条")
            
            # 目标：应该为0
            assert file_to_method_calls == 0, f"Expected 0 File->Method calls, got {file_to_method_calls}"
    
    def test_method_to_method_calls_increased(self):
        """Test: Verify Method -> Method calls ratio increased"""
        with self.driver.session() as session:
            # Method -> Method calls
            result = session.run('''
                MATCH (m1:Method)-[:CALLS]->(m2:Method)
                RETURN count(*) as method_to_method_calls
            ''')
            method_to_method = result.single()['method_to_method_calls']
            
            # Total CALLS relationships
            result = session.run('MATCH ()-[:CALLS]->() RETURN count(*) as total_calls')
            total_calls = result.single()['total_calls']
            
            ratio = method_to_method / total_calls if total_calls > 0 else 0
            
            print(f"✅ 修复后Method -> Method调用: {method_to_method:,} / {total_calls:,} ({ratio*100:.1f}%)")
            
            # 修复后应该是高比例
            assert ratio > 0.8, f"Expected Method->Method ratio >80%, got {ratio*100:.1f}%"
    
    def test_semantic_correctness_improved(self):
        """Test: Verify overall semantic correctness"""
        with self.driver.session() as session:
            # Check for any remaining semantic errors
            result = session.run('''
                MATCH (f:File)-[:CALLS]->(target)
                RETURN labels(target) as target_labels, count(*) as count
            ''')
            
            print("\n✅ 修复后File的CALLS关系目标:")
            for record in result:
                target_labels = ', '.join(record['target_labels'])
                count = record['count']
                print(f"  File -> {target_labels}: {count} 条")
            
            # 应该没有File -> Method的调用
            file_calls = list(result)
            method_calls = [r for r in file_calls if 'Method' in r['target_labels']]
            assert len(method_calls) == 0, f"Found {len(method_calls)} File->Method relationships"

def run_current_state_tests():
    """Run tests to validate current problematic state"""
    print("🔍 运行当前状态测试...")
    
    test_instance = TestFileMethodCallsElimination()
    test_instance.setup_class()
    
    try:
        test_instance.test_current_file_to_virtual_method_calls()
        test_instance.test_current_file_to_method_calls_total()
        test_instance.test_method_to_method_calls_ratio()
        test_instance.test_virtual_method_distribution()
        test_instance.test_file_caller_examples()
        
        print("✅ 当前状态测试完成 - 问题已确认")
        
    finally:
        test_instance.teardown_class()

def run_post_fix_tests():
    """Run tests to validate fix effectiveness"""
    print("\n🎯 运行修复后验证测试...")
    
    test_instance = TestFileMethodCallsAfterFix()
    test_instance.setup_class()
    
    try:
        test_instance.test_no_file_to_method_calls()
        test_instance.test_method_to_method_calls_increased()
        test_instance.test_semantic_correctness_improved()
        
        print("✅ 修复验证测试完成 - 问题已解决")
        
    except AssertionError as e:
        print(f"❌ 修复验证失败: {e}")
        return False
        
    finally:
        test_instance.teardown_class()
    
    return True

if __name__ == "__main__":
    # 运行当前状态测试
    run_current_state_tests()
    
    print("\n" + "="*60)
    print("📋 测试总结:")
    print("- File -> virtual_method调用关系存在")
    print("- 语义错误已确认")
    print("- 准备实施修复方案")
    print("="*60)
