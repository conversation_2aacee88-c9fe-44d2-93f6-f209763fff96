#!/usr/bin/env python3
"""
Neo4j Requirements Validator

This script validates that the Neo4j database meets all specified requirements
for the code knowledge graph system.
"""

from neo4j import GraphDatabase
import json
from datetime import datetime

class Neo4jRequirementsValidator:
    def __init__(self):
        self.driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
        self.validation_results = {}
        self.requirements_met = True
        
    def validate_all_requirements(self):
        """Validate all requirements and generate comprehensive report"""
        print('🔍 Neo4j数据库需求验证')
        print('=' * 60)
        
        # Core requirements validation
        self.validate_node_count_requirements()
        self.validate_relationship_requirements()
        self.validate_relationship_types()
        self.validate_node_classification()
        self.validate_data_quality()
        self.validate_architectural_analysis_capability()
        
        # Generate final report
        self.generate_validation_report()
        
        return self.requirements_met
    
    def validate_node_count_requirements(self):
        """Validate node count requirements"""
        print('📊 验证节点数量需求...')
        
        with self.driver.session() as session:
            # Total nodes
            result = session.run('MATCH (n) RETURN count(n) as total_nodes')
            total_nodes = result.single()['total_nodes']
            
            # Node type distribution
            result = session.run('MATCH (n) RETURN labels(n) as labels, count(n) as count ORDER BY count DESC')
            node_distribution = {}
            for record in result:
                labels = ', '.join(record['labels'])
                node_distribution[labels] = record['count']
            
            # Requirements check
            requirements = {
                'total_nodes_minimum': (total_nodes >= 10000, f"总节点数: {total_nodes:,} (要求: ≥10,000)"),
                'method_nodes': (node_distribution.get('Method', 0) >= 5000, f"Method节点: {node_distribution.get('Method', 0):,} (要求: ≥5,000)"),
                'class_nodes': (node_distribution.get('Class', 0) >= 100, f"Class节点: {node_distribution.get('Class', 0):,} (要求: ≥100)"),
                'file_nodes': (node_distribution.get('File', 0) >= 100, f"File节点: {node_distribution.get('File', 0):,} (要求: ≥100)"),
                'property_nodes': (node_distribution.get('Property', 0) >= 500, f"Property节点: {node_distribution.get('Property', 0):,} (要求: ≥500)")
            }
            
            self.validation_results['node_requirements'] = requirements
            
            for req_name, (passed, message) in requirements.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {message}")
                if not passed:
                    self.requirements_met = False
    
    def validate_relationship_requirements(self):
        """Validate relationship count and type requirements"""
        print('🔗 验证关系数量和类型需求...')
        
        with self.driver.session() as session:
            # Total relationships
            result = session.run('MATCH ()-[r]->() RETURN count(r) as total_rels')
            total_rels = result.single()['total_rels']
            
            # Relationship type distribution
            result = session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC')
            rel_distribution = {}
            for record in result:
                rel_distribution[record['rel_type']] = record['count']
            
            # Requirements check
            requirements = {
                'total_relationships_minimum': (total_rels >= 10000, f"总关系数: {total_rels:,} (要求: ≥10,000)"),
                'calls_relationships': (rel_distribution.get('CALLS', 0) >= 1000, f"CALLS关系: {rel_distribution.get('CALLS', 0):,} (要求: ≥1,000)"),
                'defines_relationships': (rel_distribution.get('DEFINES', 0) >= 1000, f"DEFINES关系: {rel_distribution.get('DEFINES', 0):,} (要求: ≥1,000)"),
                'accesses_relationships': (rel_distribution.get('ACCESSES', 0) >= 100, f"ACCESSES关系: {rel_distribution.get('ACCESSES', 0):,} (要求: ≥100)"),
                'inherits_relationships': (rel_distribution.get('INHERITS', 0) >= 10, f"INHERITS关系: {rel_distribution.get('INHERITS', 0):,} (要求: ≥10)"),
                'implements_relationships': (rel_distribution.get('IMPLEMENTS', 0) >= 1, f"IMPLEMENTS关系: {rel_distribution.get('IMPLEMENTS', 0):,} (要求: ≥1)")
            }
            
            self.validation_results['relationship_requirements'] = requirements
            
            for req_name, (passed, message) in requirements.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {message}")
                if not passed:
                    self.requirements_met = False
    
    def validate_relationship_types(self):
        """Validate that all required relationship types exist"""
        print('🏷️ 验证关系类型完整性...')
        
        required_relationship_types = ['CALLS', 'DEFINES', 'ACCESSES', 'INHERITS', 'IMPLEMENTS']
        
        with self.driver.session() as session:
            result = session.run('MATCH ()-[r]->() RETURN DISTINCT type(r) as rel_type')
            existing_types = set(record['rel_type'] for record in result)
            
            for rel_type in required_relationship_types:
                if rel_type in existing_types:
                    print(f"  ✅ {rel_type}关系类型存在")
                else:
                    print(f"  ❌ {rel_type}关系类型缺失")
                    self.requirements_met = False
            
            self.validation_results['relationship_types'] = {
                'required': required_relationship_types,
                'existing': list(existing_types),
                'missing': list(set(required_relationship_types) - existing_types)
            }
    
    def validate_node_classification(self):
        """Validate node classification quality"""
        print('📋 验证节点分类质量...')
        
        with self.driver.session() as session:
            # Check for unclassified nodes
            result = session.run('MATCH (n:CodeNode) RETURN count(n) as unclassified_count')
            unclassified_count = result.single()['unclassified_count']
            
            # Check total nodes
            result = session.run('MATCH (n) RETURN count(n) as total_nodes')
            total_nodes = result.single()['total_nodes']
            
            classification_rate = ((total_nodes - unclassified_count) / total_nodes * 100) if total_nodes > 0 else 0
            
            requirements = {
                'classification_rate': (classification_rate >= 90, f"节点分类率: {classification_rate:.1f}% (要求: ≥90%)"),
                'unclassified_nodes': (unclassified_count <= total_nodes * 0.1, f"未分类节点: {unclassified_count:,} (要求: ≤{int(total_nodes * 0.1):,})")
            }
            
            self.validation_results['node_classification'] = requirements
            
            for req_name, (passed, message) in requirements.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {message}")
                if not passed:
                    self.requirements_met = False
    
    def validate_data_quality(self):
        """Validate data quality metrics"""
        print('🎯 验证数据质量指标...')
        
        with self.driver.session() as session:
            # Check for orphaned nodes (nodes with no relationships)
            result = session.run('''
                MATCH (n)
                WHERE NOT (n)--()
                RETURN count(n) as orphaned_count
            ''')
            orphaned_count = result.single()['orphaned_count']
            
            # Check for nodes with proper attributes
            result = session.run('''
                MATCH (n)
                WHERE n.name IS NOT NULL
                RETURN count(n) as nodes_with_names
            ''')
            nodes_with_names = result.single()['nodes_with_names']
            
            result = session.run('MATCH (n) RETURN count(n) as total_nodes')
            total_nodes = result.single()['total_nodes']
            
            name_coverage = (nodes_with_names / total_nodes * 100) if total_nodes > 0 else 0
            orphaned_rate = (orphaned_count / total_nodes * 100) if total_nodes > 0 else 0
            
            requirements = {
                'name_coverage': (name_coverage >= 80, f"节点名称覆盖率: {name_coverage:.1f}% (要求: ≥80%)"),
                'orphaned_nodes': (orphaned_rate <= 10, f"孤立节点率: {orphaned_rate:.1f}% (要求: ≤10%)")
            }
            
            self.validation_results['data_quality'] = requirements
            
            for req_name, (passed, message) in requirements.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {message}")
                if not passed:
                    self.requirements_met = False
    
    def validate_architectural_analysis_capability(self):
        """Validate capability for architectural analysis"""
        print('🏗️ 验证架构分析能力...')
        
        with self.driver.session() as session:
            # Test key architectural queries
            test_queries = [
                ("方法调用链查询", "MATCH (f:File)-[:CALLS]->(m:Method) RETURN count(*) as count"),
                ("类继承层次查询", "MATCH (c1:Class)-[:INHERITS]->(c2:Class) RETURN count(*) as count"),
                ("跨文件依赖查询", "MATCH (f1:File)-[:CALLS]->(m:Method)<-[:DEFINES]-(f2:File) WHERE f1 <> f2 RETURN count(*) as count"),
                ("属性访问模式查询", "MATCH (f:File)-[:ACCESSES]->(p:Property) RETURN count(*) as count")
            ]
            
            query_results = {}
            for query_name, query in test_queries:
                try:
                    result = session.run(query)
                    count = result.single()['count']
                    query_results[query_name] = count
                    status = "✅" if count > 0 else "⚠️"
                    print(f"  {status} {query_name}: {count:,} 结果")
                except Exception as e:
                    query_results[query_name] = f"错误: {e}"
                    print(f"  ❌ {query_name}: 查询失败 - {e}")
                    self.requirements_met = False
            
            self.validation_results['architectural_analysis'] = query_results
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        print()
        print('📋 验证报告总结')
        print('=' * 60)
        
        if self.requirements_met:
            print('🎉 所有需求验证通过！')
            print('✅ Neo4j数据库满足代码知识图谱系统的所有要求')
        else:
            print('⚠️ 部分需求未满足')
            print('❌ Neo4j数据库需要进一步优化以满足所有要求')
        
        # Save detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'PASSED' if self.requirements_met else 'FAILED',
            'validation_results': self.validation_results
        }
        
        with open('neo4j_validation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f'📄 详细报告已保存到: neo4j_validation_report.json')
    
    def close(self):
        """Close database connection"""
        self.driver.close()

def main():
    validator = Neo4jRequirementsValidator()
    
    try:
        requirements_met = validator.validate_all_requirements()
        
        if requirements_met:
            print()
            print('🎯 验证结论: Neo4j数据库完全满足要求！')
            return True
        else:
            print()
            print('🎯 验证结论: Neo4j数据库需要改进以满足所有要求')
            return False
            
    finally:
        validator.close()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
