#!/usr/bin/env python3
"""
Verify CALLS Relationship Fix

This script verifies that the CALLS relationship semantic errors have been fixed.
"""

from neo4j import GraphDatabase

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🎉 验证修复后的CALLS关系质量')
        print('=' * 60)
        
        # 检查CALLS关系的源节点和目标节点类型
        result = session.run('''
            MATCH (src)-[r:CALLS]->(dst)
            RETURN labels(src) as src_labels, labels(dst) as dst_labels, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('📊 修复后的CALLS关系节点类型分布:')
        method_to_method = 0
        file_to_method = 0
        
        for record in result:
            src_labels = ', '.join(record['src_labels'])
            dst_labels = ', '.join(record['dst_labels'])
            count = record['count']
            print(f'  {src_labels} -> {dst_labels}: {count:,} 条关系')
            
            if 'Method' in src_labels and 'Method' in dst_labels:
                method_to_method += count
            elif 'File' in src_labels and 'Method' in dst_labels:
                file_to_method += count
        
        print()
        print('🎯 关系质量分析:')
        print(f'  Method -> Method: {method_to_method:,} 条 (✅ 正确的语义)')
        print(f'  File -> Method: {file_to_method:,} 条 (⚠️ 需要修复的语义)')
        
        # 检查具体的CALLS关系示例
        result = session.run('''
            MATCH (src:Method)-[r:CALLS]->(dst:Method)
            WHERE src.name IS NOT NULL AND dst.name IS NOT NULL
            RETURN src.name as src_name, src.class as src_class,
                   dst.name as dst_name, dst.class as dst_class,
                   r.call_type as call_type, r.caller_method as caller_method, 
                   r.caller_class as caller_class, r.receiver as receiver, r.method as method
            LIMIT 10
        ''')
        
        print()
        print('🔍 Method -> Method CALLS关系示例:')
        for record in result:
            src_name = record['src_name']
            src_class = record['src_class']
            dst_name = record['dst_name']
            dst_class = record['dst_class']
            call_type = record['call_type']
            caller_method = record['caller_method']
            caller_class = record['caller_class']
            receiver = record['receiver']
            method = record['method']
            
            print(f'  {src_class}::{src_name} -> {dst_class}::{dst_name}')
            print(f'    调用类型: {call_type}')
            print(f'    调用者: {caller_class}::{caller_method}')
            print(f'    目标: {receiver}::{method}')
            print()
        
        # 检查跨文件依赖关系
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_calls
        ''')
        
        cross_file_calls = result.single()['cross_file_calls']
        print(f'🌐 跨文件方法调用: {cross_file_calls:,} 条')
        
        # 总体统计
        result = session.run('MATCH ()-[r:CALLS]->() RETURN count(r) as total_calls')
        total_calls = result.single()['total_calls']
        
        result = session.run('MATCH (n) RETURN count(n) as total_nodes')
        total_nodes = result.single()['total_nodes']
        
        print()
        print('📈 修复成果总结:')
        print(f'  总CALLS关系: {total_calls:,} 条')
        print(f'  总节点数: {total_nodes:,} 个')
        print(f'  Method->Method比例: {(method_to_method/total_calls*100):.1f}%')
        print(f'  跨文件调用: {cross_file_calls:,} 条')
        
        # 验证修复是否成功
        if method_to_method > file_to_method:
            print('✅ CALLS关系语义修复成功！')
            print('✅ 大部分关系现在是正确的Method->Method模式')
        else:
            print('⚠️ CALLS关系仍需进一步修复')
            print('⚠️ File->Method关系仍然占主导地位')

    driver.close()

if __name__ == "__main__":
    main()
