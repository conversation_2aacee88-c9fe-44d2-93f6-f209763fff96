["tests/integration/test_full_pipeline.py::TestFullPipeline::test_pipeline_creation_and_setup", "tests/integration/test_full_pipeline.py::TestFullPipeline::test_pipeline_dry_run", "tests/integration/test_full_pipeline.py::TestFullPipeline::test_pipeline_status_reporting", "tests/unit/test_clean_step.py::TestCleanStep::test_clean_step_execute_partial_cleanup", "tests/unit/test_clean_step.py::TestCleanStep::test_clean_step_execute_success", "tests/unit/test_clean_step.py::TestCleanStep::test_clean_step_execute_with_permission_error", "tests/unit/test_clean_step.py::TestCleanStep::test_clean_step_get_cleanup_targets", "tests/unit/test_clean_step.py::TestCleanStep::test_clean_step_initialization", "tests/unit/test_clean_step.py::TestCleanStep::test_clean_step_validate_prerequisites_success", "tests/unit/test_config.py::TestPipelineConfig::test_custom_initialization", "tests/unit/test_config.py::TestPipelineConfig::test_default_initialization", "tests/unit/test_config.py::TestPipelineConfig::test_from_file", "tests/unit/test_config.py::TestPipelineConfig::test_from_file_not_found", "tests/unit/test_config.py::TestPipelineConfig::test_to_file", "tests/unit/test_core.py::TestPipeline::test_add_duplicate_step", "tests/unit/test_core.py::TestPipeline::test_add_step", "tests/unit/test_core.py::TestPipeline::test_pipeline_failed_run", "tests/unit/test_core.py::TestPipeline::test_pipeline_initialization", "tests/unit/test_core.py::TestPipeline::test_pipeline_partial_run", "tests/unit/test_core.py::TestPipeline::test_pipeline_status", "tests/unit/test_core.py::TestPipeline::test_pipeline_successful_run", "tests/unit/test_core.py::TestPipeline::test_pipeline_validate_dependencies", "tests/unit/test_core.py::TestPipelineStep::test_step_dependencies", "tests/unit/test_core.py::TestPipelineStep::test_step_execution_with_exception", "tests/unit/test_core.py::TestPipelineStep::test_step_failed_execution", "tests/unit/test_core.py::TestPipelineStep::test_step_initialization", "tests/unit/test_core.py::TestPipelineStep::test_step_progress", "tests/unit/test_core.py::TestPipelineStep::test_step_successful_execution", "tests/unit/test_error_handling.py::TestErrorClassification::test_error_classifier_configuration_errors", "tests/unit/test_error_handling.py::TestErrorClassification::test_error_classifier_network_errors", "tests/unit/test_error_handling.py::TestErrorClassification::test_error_classifier_resource_errors", "tests/unit/test_error_handling.py::TestErrorClassification::test_non_retryable_error_creation", "tests/unit/test_error_handling.py::TestErrorClassification::test_retryable_error_creation", "tests/unit/test_error_handling.py::TestRetryStrategies::test_exponential_backoff_strategy", "tests/unit/test_error_handling.py::TestRetryStrategies::test_fixed_delay_strategy", "tests/unit/test_error_handling.py::TestRetryStrategies::test_retry_strategy_should_retry", "tests/unit/test_error_handling.py::TestStepRetryMechanism::test_step_with_non_retryable_error", "tests/unit/test_error_handling.py::TestStepRetryMechanism::test_step_with_retry_max_attempts_exceeded", "tests/unit/test_error_handling.py::TestStepRetryMechanism::test_step_with_retry_success_after_failures", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_build_command_generation", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_execute_build_failure", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_execute_multiple_schemes", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_execute_success", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_initialization", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_validate_prerequisites_no_workspace", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_validate_prerequisites_no_xcodebuild", "tests/unit/test_index_store_step.py::TestIndexStoreStep::test_index_store_step_validate_prerequisites_success", "tests/unit/test_usrs_step.py::TestUsrsStep::test_usrs_step_execute_success", "tests/unit/test_usrs_step.py::TestUsrsStep::test_usrs_step_execute_tool_failure", "tests/unit/test_usrs_step.py::TestUsrsStep::test_usrs_step_initialization", "tests/unit/test_usrs_step.py::TestUsrsStep::test_usrs_step_validate_prerequisites_success"]