name: Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  test:
    runs-on: macos-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install system dependencies
      run: |
        brew install llvm protobuf
        echo "CLANG_LIBRARY_FILE=/opt/homebrew/opt/llvm/lib/libclang.dylib" >> $GITHUB_ENV

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install "clang==15.*" neo4j
        python run_tests.py --install-deps

    - name: Run unit tests
      run: |
        python run_tests.py --unit --coverage --verbose

    - name: Run integration tests
      run: |
        python run_tests.py --integration --verbose

    - name: Run code quality checks
      run: |
        python run_tests.py --quality

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          htmlcov/
          bandit-report.json
          pytest-report.xml