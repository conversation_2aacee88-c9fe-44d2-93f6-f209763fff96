#!/usr/bin/env python3
"""
QMap Pipeline测试运行器

使用方法:
    python run_tests.py                    # 运行所有测试
    python run_tests.py --unit             # 只运行单元测试
    python run_tests.py --integration      # 只运行集成测试
    python run_tests.py --coverage         # 运行测试并生成覆盖率报告
    python run_tests.py --verbose          # 详细输出
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"运行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def install_test_dependencies():
    """安装测试依赖"""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0",
        "pytest-mock>=3.10.0",
        "pytest-xdist>=3.0.0"  # 并行测试
    ]

    for dep in dependencies:
        cmd = [sys.executable, "-m", "pip", "install", dep]
        if not run_command(cmd):
            print(f"安装依赖失败: {dep}")
            return False

    return True


def run_tests(test_type="all", coverage=False, verbose=False, parallel=True):
    """运行测试"""

    # 构建pytest命令
    cmd = [sys.executable, "-m", "pytest"]

    # 添加测试路径
    if test_type == "unit":
        cmd.append("tests/unit/")
    elif test_type == "integration":
        cmd.append("tests/integration/")
    else:
        cmd.append("tests/")

    # 添加选项
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")

    if parallel:
        cmd.extend(["-n", "auto"])  # 自动并行

    if coverage:
        cmd.extend([
            "--cov=pipeline",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])

    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",  # 简短的错误回溯
        "--strict-markers",  # 严格的标记检查
        "--disable-warnings"  # 禁用警告
    ])

    return run_command(cmd)