#!/bin/bash
# Neo4j Setup Script for macOS
# This script installs and configures Neo4j for Pipeline-All integration

set -e

echo "🚀 Neo4j Setup for Pipeline-All Integration"
echo "=========================================="

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    echo "❌ Homebrew not found. Please install Homebrew first:"
    echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

echo "✅ Homebrew found"

# Install Neo4j
echo "📦 Installing Neo4j..."
if brew list neo4j &> /dev/null; then
    echo "✅ Neo4j already installed"
else
    brew install neo4j
    echo "✅ Neo4j installed successfully"
fi

# Check Neo4j version
NEO4J_VERSION=$(neo4j version 2>/dev/null || echo "unknown")
echo "📋 Neo4j version: $NEO4J_VERSION"

# Set initial password
echo "🔐 Setting Neo4j password..."
if neo4j-admin dbms set-initial-password 6536772a 2>/dev/null; then
    echo "✅ Password set successfully"
else
    echo "⚠️ Password may already be set or Neo4j may be running"
fi

# Start Neo4j
echo "🚀 Starting Neo4j..."
if neo4j start; then
    echo "✅ Neo4j started successfully"
else
    echo "⚠️ Neo4j may already be running"
fi

# Wait for Neo4j to be ready
echo "⏳ Waiting for Neo4j to be ready..."
sleep 10

# Check if Neo4j is accessible
echo "🔍 Testing Neo4j connection..."
if curl -s http://localhost:7474 > /dev/null; then
    echo "✅ Neo4j Browser accessible at http://localhost:7474"
else
    echo "❌ Neo4j Browser not accessible"
    echo "💡 Try manually starting Neo4j:"
    echo "   neo4j start"
fi

if nc -z localhost 7687 2>/dev/null; then
    echo "✅ Neo4j Bolt protocol accessible at bolt://localhost:7687"
else
    echo "❌ Neo4j Bolt protocol not accessible"
fi

echo ""
echo "🎉 Neo4j setup complete!"
echo "📋 Connection details:"
echo "   Browser: http://localhost:7474"
echo "   Bolt: bolt://localhost:7687"
echo "   Username: neo4j"
echo "   Password: 6536772a"
echo ""
echo "🔧 Next steps:"
echo "   1. Test connection: python test_neo4j_connection.py"
echo "   2. Import data: python neo4j_integration/main.py"
