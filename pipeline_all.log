2025-07-09 01:52:41,461 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 01:52:41,462 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 01:52:41,462 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 01:52:41,495 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 01:52:41,498 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 01:52:41,560 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 01:52:41,653 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 01:52:41,666 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 01:52:41,792 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 01:52:41,907 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 01:52:42,189 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 01:52:42,781 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 01:52:43,040 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 01:52:43,112 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 01:52:43,112 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 01:52:43,113 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 01:52:43,113 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 01:52:43,113 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 01:52:43,113 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 01:52:43,113 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 01:52:43,113 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 01:52:43,113 - pipeline.steps.clean_step - ERROR - 清理过程中发生错误: 'PipelineAllConfig' object has no attribute 'derived_data_path'
2025-07-09 01:52:43,113 - pipeline.core - ERROR - 步骤 clean 执行失败: 清理失败: 'PipelineAllConfig' object has no attribute 'derived_data_path'
2025-07-09 01:52:43,113 - pipeline.core - ERROR - 步骤 clean 执行失败，停止流水线
2025-07-09 01:52:43,113 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
2025-07-09 01:53:15,423 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 01:53:15,423 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 01:53:15,423 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 01:53:15,456 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 01:53:15,459 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 01:53:15,524 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 01:53:15,604 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 01:53:15,617 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 01:53:15,744 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 01:53:15,855 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 01:53:16,120 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 01:53:16,715 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 01:53:16,971 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 01:53:17,042 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 01:53:17,042 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 01:53:17,043 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 01:53:17,043 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 01:53:17,043 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 01:53:17,043 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 01:53:17,043 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 01:53:17,043 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 01:53:17,043 - pipeline.steps.clean_step - ERROR - 清理过程中发生错误: 'PipelineAllConfig' object has no attribute 'index_symbols_file'
2025-07-09 01:53:17,043 - pipeline.core - ERROR - 步骤 clean 执行失败: 清理失败: 'PipelineAllConfig' object has no attribute 'index_symbols_file'
2025-07-09 01:53:17,043 - pipeline.core - ERROR - 步骤 clean 执行失败，停止流水线
2025-07-09 01:53:17,043 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
2025-07-09 01:54:00,249 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 01:54:00,249 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 01:54:00,249 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 01:54:00,286 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 01:54:00,288 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 01:54:00,353 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 01:54:00,438 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 01:54:00,451 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 01:54:00,572 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 01:54:00,686 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 01:54:00,956 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 01:54:01,546 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 01:54:01,812 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 01:54:01,882 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 01:54:01,882 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 01:54:01,883 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 01:54:01,883 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 01:54:01,883 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 01:54:01,883 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 01:54:01,883 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 01:54:01,883 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 01:54:07,084 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-09 01:54:07,084 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-09 01:54:07,085 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-09 01:54:07,085 - pipeline.steps.clean_step - INFO - 删除文件: compile_commands.json
2025-07-09 01:54:07,085 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 61754 个项目，释放 9064538266 字节
2025-07-09 01:54:07,085 - pipeline.core - INFO - 执行步骤 2/6: index-store-all
2025-07-09 01:54:07,085 - pipeline.core - INFO - 开始执行步骤: index-store-all
2025-07-09 01:54:07,085 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-09 01:54:07,085 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 01:54:07,085 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-09 01:54:07,086 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 01:54:07,086 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-09 01:54:07,088 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation-Example' 生成索引...
2025-07-09 01:54:07,088 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-09 01:54:07,088 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-09 01:54:07,090 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit-Example' 生成索引...
2025-07-09 01:54:07,092 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-09 01:54:11,545 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation-Example' 构建失败: 2025-07-09 01:54:10.417 xcodebuild[25482:1503269] Writing error result bundle to /var/folders/8_/0tvfq7fn43j534cqbz0xyccw0000gn/T/ResultBundle_2025-09-07_01-54-0010.xcresult
xcodebuild: error: The workspace named "TencentMap" does not contain a scheme named "QMapFoundation-Example". The "-list" option can be used to find the names of the schemes in the workspace.

2025-07-09 01:54:11,546 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-09 01:54:11,549 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit-Example' 构建失败: 2025-07-09 01:54:10.421 xcodebuild[25485:1503276] Writing error result bundle to /var/folders/8_/0tvfq7fn43j534cqbz0xyccw0000gn/T/ResultBundle_2025-09-07_01-54-0010.xcresult
xcodebuild: error: The workspace named "TencentMap" does not contain a scheme named "DragonMapKit-Example". The "-list" option can be used to find the names of the schemes in the workspace.

2025-07-09 01:54:11,550 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-09 01:54:24,861 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-09 01:54:24,862 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-09 01:54:25,001 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-09 01:54:25,001 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-09 01:54:25,192 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-09 01:54:25,192 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-09 01:54:25,268 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-09 01:54:25,268 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-09 01:54:25,601 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-09 01:54:25,601 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-09 01:54:25,660 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-09 01:54:25,661 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-09 01:54:27,097 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapRouteSearchKit and configuration Debug
(1 failure)

2025-07-09 01:54:29,693 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-09 01:54:33,404 - pipeline.steps.index_store_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 索引生成成功
2025-07-09 01:54:45,792 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-09 01:54:46,187 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-09 01:54:46,271 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-09 01:54:46,428 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-09 01:57:38,478 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Ld /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/TencentMap.app/TencentMap normal (in target 'TencentMap' from project 'TencentMap')
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(2 failures)

2025-07-09 01:57:38,483 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 01:57:38,549 - pipeline.steps.index_store_step_all - WARNING - 未找到索引文件
2025-07-09 01:57:38,549 - pipeline.core - ERROR - 步骤 index-store-all 执行失败: 索引生成部分完成，成功: 2, 失败: 14
2025-07-09 01:57:38,549 - pipeline.core - ERROR - 步骤 index-store-all 执行失败，停止流水线
2025-07-09 01:57:38,549 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
2025-07-09 02:11:44,417 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 02:11:44,417 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 02:11:44,417 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 02:11:44,436 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 02:11:44,446 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 02:11:44,508 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 02:11:44,544 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 02:11:44,550 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 02:11:44,619 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 02:11:44,706 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 02:11:44,918 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 02:11:45,400 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 02:11:45,691 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 02:11:45,724 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 02:11:45,724 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 02:11:45,725 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 02:11:45,725 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 02:11:45,725 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 02:11:45,725 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 02:11:45,725 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 02:11:45,725 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 02:11:45,725 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 02:11:45,725 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 02:11:45,725 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 02:11:45,725 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 02:11:45,725 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 02:11:45,725 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 02:11:45,725 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 02:11:45,725 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 02:11:56,202 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-09 02:11:56,202 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 134447 个项目，释放 *********** 字节
2025-07-09 02:11:56,202 - pipeline.core - INFO - 执行步骤 2/6: index-store-all
2025-07-09 02:11:56,202 - pipeline.core - INFO - 开始执行步骤: index-store-all
2025-07-09 02:11:56,202 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-09 02:11:56,202 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 02:11:56,202 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-09 02:11:56,205 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 02:11:56,205 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-09 02:11:56,207 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成索引...
2025-07-09 02:11:56,208 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-09 02:11:56,209 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-09 02:11:56,213 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成索引...
2025-07-09 02:11:56,216 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-09 02:12:09,549 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapFoundation and configuration Debug
(1 failure)

2025-07-09 02:12:09,550 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-09 02:12:13,500 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-09 02:12:13,500 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-09 02:12:13,545 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DragonMapKit and configuration Debug
(1 failure)

2025-07-09 02:12:13,545 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-09 02:12:13,563 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-09 02:12:13,564 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-09 02:12:13,734 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-09 02:12:13,734 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-09 02:12:13,743 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-09 02:12:13,743 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-09 02:12:13,792 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-09 02:12:13,793 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-09 02:12:19,713 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-09 02:12:19,713 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-09 02:12:26,113 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-09 02:12:35,690 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-09 02:12:35,755 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-09 02:12:36,063 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(1 failure)

2025-07-09 02:12:36,325 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-09 02:12:36,655 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-09 02:12:37,856 - pipeline.steps.index_store_step_all - ERROR - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug
(1 failure)

2025-07-09 02:12:38,439 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	PhaseScriptExecution [CP]\ Copy\ XCFrameworks /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/NerdApi.build/Debug-iphoneos/NerdApi.build/Script-8ECC9900000240.sh (in target 'NerdApi' from project 'NerdApi')
	PhaseScriptExecution [CP]\ Copy\ XCFrameworks /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RouteGuidance.build/Debug-iphoneos/RouteGuidance.build/Script-66646D00000240.sh (in target 'RouteGuidance' from project 'RouteGuidance')
	ProcessInfoPlistFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QCloudCOSXML/QCloudCOSXML.bundle/Info.plist /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/QCloudCOSXML/ResourceBundle-QCloudCOSXML-QCloudCOSXML-Info.plist (in target 'QCloudCOSXML-QCloudCOSXML' from project 'QCloudCOSXML')
	Building workspace TencentMap with scheme QMapRouteSearchKit and configuration Debug
(4 failures)

2025-07-09 02:12:38,439 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:12:38,492 - pipeline.steps.index_store_step_all - INFO - 索引存储输出验证成功，找到 3507 个索引记录文件
2025-07-09 02:12:38,492 - pipeline.core - INFO - 步骤 index-store-all 执行成功: 索引生成部分完成，成功: 1, 失败: 15
2025-07-09 02:12:38,492 - pipeline.core - INFO - 执行步骤 3/6: usrs-all
2025-07-09 02:12:38,492 - pipeline.core - INFO - 开始执行步骤: usrs-all
2025-07-09 02:12:38,495 - pipeline.steps.usrs_step_all - INFO - 开始提取多仓库USR到文件路径映射
2025-07-09 02:12:38,495 - pipeline.steps.usrs_step_all - INFO - 检查索引存储目录: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:12:38,495 - pipeline.steps.usrs_step_all - INFO - 开始提取USR到文件路径映射
2025-07-09 02:12:38,495 - pipeline.steps.usrs_step_all - INFO - 使用索引存储路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:12:38,495 - pipeline.steps.usrs_step_all - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store ast_out_index_all/DerivedData/Index.noindex/DataStore --out index_symbols_all.jsonl
2025-07-09 02:12:40,774 - pipeline.steps.usrs_step_all - INFO - USR提取命令执行成功
2025-07-09 02:12:40,786 - pipeline.steps.usrs_step_all - INFO - 成功提取 111352 个USR映射
2025-07-09 02:12:40,786 - pipeline.steps.usrs_step_all - INFO - 分析提取的USR数据...
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO - USR分析完成:
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO -   总USR数: 111,332
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO -   项目文件: 0
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO -   Pods文件: 19,715
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO -   系统文件: 0
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO -   仓库分布:
2025-07-09 02:12:41,054 - pipeline.steps.usrs_step_all - INFO -     unknown: 111,332
2025-07-09 02:12:41,054 - pipeline.core - INFO - 步骤 usrs-all 执行成功: USR提取完成，提取了 111352 个USR映射
2025-07-09 02:12:41,054 - pipeline.core - INFO - 执行步骤 4/6: cdb-all
2025-07-09 02:12:41,054 - pipeline.core - INFO - 开始执行步骤: cdb-all
2025-07-09 02:12:41,054 - pipeline.steps.cdb_step_all - INFO - 开始生成多仓库编译数据库
2025-07-09 02:12:41,054 - pipeline.steps.cdb_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 02:12:41,054 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成编译数据库...
2025-07-09 02:12:41,054 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-09 02:12:41,058 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成编译数据库...
2025-07-09 02:12:41,059 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成编译数据库...
2025-07-09 02:12:41,061 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成编译数据库...
2025-07-09 02:12:41,061 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成编译数据库...
2025-07-09 02:12:41,061 - pipeline.steps.cdb_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成编译数据库...
2025-07-09 02:12:41,063 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成编译数据库...
2025-07-09 02:12:51,444 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapUIKit 的scheme 'QMapUIKit' 构建失败，但继续解析日志
2025-07-09 02:12:51,445 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapUIKit 的构建日志，长度: 559487 字符
2025-07-09 02:12:51,447 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 25 个CompileC行, 118 个clang命令
2025-07-09 02:12:51,562 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 27/54 个文件通过过滤
2025-07-09 02:12:51,562 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 编译数据库生成成功，包含 27 个编译命令
2025-07-09 02:12:51,562 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成编译数据库...
2025-07-09 02:12:51,976 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败，但继续解析日志
2025-07-09 02:12:51,976 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBusiness 的构建日志，长度: 54881 字符
2025-07-09 02:12:51,976 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:12:51,976 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:51,976 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBusiness 的scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:51,977 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成编译数据库...
2025-07-09 02:12:52,274 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败，但继续解析日志
2025-07-09 02:12:52,274 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapMiddlePlatform 的构建日志，长度: 41338 字符
2025-07-09 02:12:52,274 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:12:52,274 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:52,274 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:52,274 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成编译数据库...
2025-07-09 02:12:52,338 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败，但继续解析日志
2025-07-09 02:12:52,339 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapFoundation 的构建日志，长度: 14539 字符
2025-07-09 02:12:52,339 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 5 个clang命令
2025-07-09 02:12:52,339 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:52,339 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapFoundation 的scheme 'QMapFoundation' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:52,339 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成编译数据库...
2025-07-09 02:12:52,641 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败，但继续解析日志
2025-07-09 02:12:52,641 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapNaviKit 的构建日志，长度: 42486 字符
2025-07-09 02:12:52,642 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:12:52,642 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:52,642 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:52,642 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成编译数据库...
2025-07-09 02:12:53,087 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败，但继续解析日志
2025-07-09 02:12:53,087 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBaseline 的构建日志，长度: 48087 字符
2025-07-09 02:12:53,087 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:12:53,087 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:53,087 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBaseline 的scheme 'QMapBaseline' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:53,087 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成编译数据库...
2025-07-09 02:12:53,592 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败，但继续解析日志
2025-07-09 02:12:53,593 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapHippy 的构建日志，长度: 42918 字符
2025-07-09 02:12:53,593 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:12:53,593 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:53,593 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapHippy 的scheme 'QMapHippy' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:53,593 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成编译数据库...
2025-07-09 02:12:54,277 - pipeline.steps.cdb_step_all - WARNING - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败，但继续解析日志
2025-07-09 02:12:54,277 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DragonMapKit 的构建日志，长度: 31434 字符
2025-07-09 02:12:54,277 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:12:54,277 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:54,277 - pipeline.steps.cdb_step_all - INFO - 仓库 DragonMapKit 的scheme 'DragonMapKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:12:54,277 - pipeline.steps.cdb_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成编译数据库...
2025-07-09 02:12:57,019 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 36344 字符
2025-07-09 02:12:57,019 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 1 个clang命令
2025-07-09 02:12:57,019 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:12:57,019 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'BuildInfo' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:06,574 - pipeline.steps.cdb_step_all - WARNING - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败，但继续解析日志
2025-07-09 02:13:06,574 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DKProtocolsPool 的构建日志，长度: 2277 字符
2025-07-09 02:13:06,574 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 2 个clang命令
2025-07-09 02:13:06,574 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:13:06,574 - pipeline.steps.cdb_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:07,406 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'TencentMap' 构建失败，但继续解析日志
2025-07-09 02:13:07,406 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89211 字符
2025-07-09 02:13:07,406 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:13:07,407 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:13:07,407 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'TencentMap' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:08,216 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntents' 构建失败，但继续解析日志
2025-07-09 02:13:08,216 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89210 字符
2025-07-09 02:13:08,217 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:13:08,217 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:13:08,218 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntents' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:10,161 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败，但继续解析日志
2025-07-09 02:13:10,161 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89212 字符
2025-07-09 02:13:10,162 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:13:10,162 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:13:10,162 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntentsUI' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:10,485 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败，但继续解析日志
2025-07-09 02:13:10,485 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89222 字符
2025-07-09 02:13:10,485 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:13:10,485 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:13:10,485 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMNotificationService' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:11,751 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败，但继续解析日志
2025-07-09 02:13:11,751 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89218 字符
2025-07-09 02:13:11,751 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:13:11,751 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:13:11,751 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMWidgetExtension' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:13:24,967 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapRouteSearchKit 的构建日志，长度: 6394130 字符
2025-07-09 02:13:24,974 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 268 个CompileC行, 1159 个clang命令
2025-07-09 02:13:26,252 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 301/579 个文件通过过滤
2025-07-09 02:13:26,252 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 编译数据库生成成功，包含 301 个编译命令
2025-07-09 02:13:26,252 - pipeline.steps.cdb_step_all - INFO - 合并编译命令完成，共 301 个唯一文件
2025-07-09 02:13:26,255 - pipeline.steps.cdb_step_all - INFO - 编译数据库已保存到: ast_out_index_all/compile_commands/compile_commands.json
2025-07-09 02:13:26,255 - pipeline.core - INFO - 步骤 cdb-all 执行成功: 编译数据库生成完成，处理了 16 个scheme，生成 301 个编译命令
2025-07-09 02:13:26,255 - pipeline.core - INFO - 执行步骤 5/6: ast-all
2025-07-09 02:13:26,255 - pipeline.core - INFO - 开始执行步骤: ast-all
2025-07-09 02:13:26,255 - pipeline.steps.ast_step_all - INFO - 开始多仓库AST抽取
2025-07-09 02:13:26,256 - pipeline.steps.ast_step_all - INFO - 从编译数据库加载了 301 个编译命令
2025-07-09 02:13:26,256 - pipeline.steps.ast_step_all - INFO - 加载了 301 个编译命令
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6045: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6046: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6047: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6048: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6049: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6050: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6051: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6052: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6053: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,262 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6054: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:13:26,362 - pipeline.steps.ast_step_all - WARNING - 跳过了 20/111352 个无效的符号行
2025-07-09 02:13:26,362 - pipeline.steps.ast_step_all - INFO - 成功加载 111332 个符号映射
2025-07-09 02:13:26,362 - pipeline.steps.ast_step_all - INFO - 处理 301 个编译命令
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1/301: APLAppObject.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 2/301: PBAPLProtocol.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 3/301: ApolloSDK.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 4/301: ApolloSDK-dummy.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 5/301: APLSDKHelper.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 6/301: APLSafeObject.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 7/301: APLSDKConfig.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 8/301: APLRespDTO.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 9/301: APLNodeDataRefresher.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 10/301: APLNode.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 11/301: APLNetworkSessionManager.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 12/301: APLRegistry.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 13/301: APLNetworkSessionDataTask.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 14/301: APLPrivate.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 15/301: APLNetworkSerialization.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 16/301: MMKVCore-dummy.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 17/301: APLNetworkRequest.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 18/301: APLNetworkEncryptor.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 19/301: APLNetworkAPI.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 20/301: APLModuleObject.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 21/301: APLLog.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 22/301: APLInterface.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 23/301: APLConfigObject.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 24/301: APLCacheConfig.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 25/301: APLCache.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 26/301: APLBusinessObject.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 27/301: APLBasePO.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 28/301: NSError+QCloudNetworking.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 29/301: NSDate+QCloudInternetDateTime.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 30/301: NSDate+QCloudComapre.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 31/301: QCloudCore-dummy.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 32/301: NSDate+QCLOUD.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 33/301: NSString+MJExtension.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 34/301: NSObject+MJProperty.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 35/301: NSObject+MJKeyValue.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 36/301: NSObject+MJCoding.m
2025-07-09 02:13:26,363 - pipeline.steps.ast_step_all - INFO - 处理编译文件 37/301: NSObject+MJClass.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 38/301: MJPropertyType.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 39/301: MJPropertyKey.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 40/301: MJProperty.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 41/301: MJFoundation.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 42/301: MJExtensionConst.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 43/301: MJExtension-dummy.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 44/301: NSObject+FBKVOController.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 45/301: KVOController-dummy.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 46/301: FBKVOController.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 47/301: QCloudAbstractRequest.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 48/301: NSObject+QCloudModelTool.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 49/301: NSObject+QCloudModel.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 50/301: NSObject+HTTPHeadersContainer.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 51/301: NSMutableData+QCloud_CRC.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 52/301: NSHTTPCookie+QCloudNetworking.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 53/301: NSData+GZIP.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 54/301: GZIP-dummy.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 55/301: FMResultSet.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 56/301: FMDatabaseQueue.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 57/301: FMDatabasePool.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 58/301: FMDatabaseAdditions.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 59/301: FMDatabase.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 60/301: FMDB-dummy.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 61/301: UIViewController+DKApplication.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 62/301: UINavigationController+DKApplication.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 63/301: DKServiceStartMirror.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 64/301: DKServiceDigest.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 65/301: DKServiceContext.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 66/301: DKSchemeHandler.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 67/301: DKRulesChecker.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 68/301: DKRouterContext.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 69/301: DKRouter.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 70/301: DKRouter+URLRouter.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 71/301: DKRouter+Service.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 72/301: DKRouter+Application.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 73/301: DKRegistryCenter.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 74/301: DKMobileCore.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 75/301: DKMobile-dummy.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 76/301: DKMachORegistry.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 77/301: DKLog.m
2025-07-09 02:13:26,364 - pipeline.steps.ast_step_all - INFO - 处理编译文件 78/301: DKCoreDelegate.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 79/301: DKCoreDelegate+CarPlay.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 80/301: DKApplicationDigest.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 81/301: DKApplication.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 82/301: DKAppLifeDelegate.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 83/301: DDTTYLogger.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 84/301: DDOSLogger.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 85/301: DDMultiFormatter.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 86/301: DDLoggerNames.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 87/301: DDLog.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 88/301: DDFileLogger.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 89/301: DDFileLogger+Buffering.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 90/301: DDDispatchQueueLogFormatter.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 91/301: DDContextFilterLogFormatter.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 92/301: DDContextFilterLogFormatter+Deprecated.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 93/301: DDAbstractDatabaseLogger.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 94/301: DDASLLogger.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 95/301: DDASLLogCapture.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 96/301: CocoaLumberjack-dummy.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 97/301: CLIColor.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 98/301: UIImage+WebP.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 99/301: SDWebImageWebPCoderDefine.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 100/301: SDWebImageWebPCoder-dummy.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 101/301: SDImageWebPCoder.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 102/301: QCloudUploadPartResult.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 103/301: QCloudUploadPartRequest.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 104/301: QCloudUploadPartRequest+Custom.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 105/301: QCloudUploadPartCopyRequest.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 106/301: QCloudUploadObjectResult.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 107/301: QCloudRequestData+COSXMLVersion.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 108/301: QCloudPutObjectRequest.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 109/301: QCloudPutObjectRequest+CustomBuild.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 110/301: QCloudPutObjectRequest+Custom.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 111/301: QCloudPutObjectCopyRequest.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 112/301: QCloudMultipartUploadPart.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 113/301: QCloudMultipartUploadOwner.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 114/301: QCloudMultipartUploadInitiator.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 115/301: QCloudMultipartInfo.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 116/301: QCloudLogManager.m
2025-07-09 02:13:26,365 - pipeline.steps.ast_step_all - INFO - 处理编译文件 117/301: QCloudListPartsResult.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 118/301: QCloudListMultipartRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 119/301: QCloudInitiateMultipartUploadResult.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 120/301: QCloudInitiateMultipartUploadRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 121/301: QCloudHeadObjectRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 122/301: QCloudGetObjectRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 123/301: QCloudGetObjectRequest+Custom.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 124/301: QCloudCopyObjectResult.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 125/301: QCloudCompleteMultipartUploadRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 126/301: QCloudCompleteMultipartUploadInfo.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 127/301: QCloudCOSXMLVersion.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 128/301: QCloudCOSXMLUploadObjectRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 129/301: QCloudCOSXMLServiceUtilities.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 130/301: QCloudCOSXMLService.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 131/301: QCloudCOSXMLService+Transfer.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 132/301: QCloudCOSXMLService+Quality.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 133/301: QCloudCOSXMLService+Configuration.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 134/301: QCloudCOSXMLEndPoint.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 135/301: QCloudCOSXMLDownloadObjectRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 136/301: QCloudCOSXMLCopyObjectRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 137/301: QCloudCOSXML-dummy.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 138/301: QCloudCOSTransferMangerService.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 139/301: QCloudCOSStorageClassEnum.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 140/301: QCloudAppendObjectRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 141/301: QCloudAbstractRequest+Quality.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 142/301: QCloudAbortMultipfartUploadRequest.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 143/301: NSString+RegularExpressionCategory.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 144/301: ReconnectTimer.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 145/301: MQTTWebsocketTransport.m
2025-07-09 02:13:26,366 - pipeline.steps.ast_step_all - INFO - 处理编译文件 146/301: MQTTTransport.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 147/301: MQTTStrict.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 148/301: MQTTSessionSynchron.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 149/301: MQTTSessionManager.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 150/301: MQTTSessionLegacy.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 151/301: MQTTSession.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 152/301: MQTTSSLSecurityPolicyTransport.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 153/301: MQTTSSLSecurityPolicyEncoder.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 154/301: MQTTSSLSecurityPolicyDecoder.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 155/301: MQTTSSLSecurityPolicy.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 156/301: MQTTProperties.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 157/301: MQTTMessage.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 158/301: MQTTLog.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 159/301: MQTTInMemoryPersistence.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 160/301: MQTTDecoder.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 161/301: MQTTCoreDataPersistence.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 162/301: MQTTClient-dummy.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 163/301: MQTTCFSocketTransport.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 164/301: MQTTCFSocketEncoder.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 165/301: MQTTCFSocketDecoder.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 166/301: GCDTimer.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 167/301: ForegroundReconnection.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 168/301: DKProtocolsPool-dummy.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 169/301: DKPServices.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 170/301: DKPQQMaps.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 171/301: DKPPerformanceStatService.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 172/301: DKPApplications.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 173/301: DKPQQMaps.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 174/301: DM_MAP_UniversalModelTapInfo.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 175/301: DragonMapKitC2OC-dummy.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 176/301: DM_MAP_ZoomForNaviParameter.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 177/301: DM_MAP_TXAnimationParam.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 178/301: DM_MAP_TMRect.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 179/301: DM_MAP_TMSize.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 180/301: DM_MAP_TMPoint.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 181/301: DM_MAP_TMBitmapContext.m
2025-07-09 02:13:26,367 - pipeline.steps.ast_step_all - INFO - 处理编译文件 182/301: DM_MAP_TXUploadLogArgs.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 183/301: DM_MAP_ShadowSetting.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 184/301: DM_MAP_SimpleLandMarkMode.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 185/301: DM_MAP_SectionDashedLineParam.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 186/301: DM_MAP_RouteTurnArrow3DStyle.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 187/301: DM_MAP_RouteStyleAtScale.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 188/301: DM_MAP_RouteNameStyleAtScale.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 189/301: DM_MAP_RouteNameStyle.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 190/301: DM_MAP_RouteGradientParamForSegmentMode.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 191/301: DM_MAP_RouteGradientInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 192/301: DM_MAP_RichCallbackInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 193/301: DM_MAP_RGBADashedLineStyleAtScale.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 194/301: DM_MAP_RGBADashedLineExtraParam.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 195/301: DM_MAP_RGBAColorLineExtraParam.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 196/301: DM_MAP_POIInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 197/301: DM_MAP_OverlookParam.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 198/301: DM_MAP_Object3DTapInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 199/301: DM_MAP_ModelID.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 200/301: DM_MAP_MarkerGroupIconAnchor.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 201/301: DM_MAP_Marker4KInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 202/301: DM_MAP_MapTree.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 203/301: DM_MAP_MapTileID.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 204/301: DM_MAP_MapTextDrawInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 205/301: DM_MAP_MapTappedInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 206/301: DM_MAP_MapRouteSectionWithName.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 207/301: DM_MAP_MapRouteSection.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 208/301: DM_MAP_MapRouteInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 209/301: DM_MAP_MapRouteDescInfo.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 210/301: DM_MAP_MapRoadScanOptions.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 211/301: DM_MAP_MapRectF.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 212/301: DM_MAP_MapPrimitive.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 213/301: DM_MAP_MapPatternStyle.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 214/301: DM_MAP_MapNaviAccuracyCircleOptions.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 215/301: DM_MAP_MapNaviAccuracyCircleGradientNode.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 216/301: DM_MAP_MapModelReportFlag.m
2025-07-09 02:13:26,368 - pipeline.steps.ast_step_all - INFO - 处理编译文件 217/301: DM_MAP_MapModel3DImageBuffer.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 218/301: DM_MAP_MapMarkerSubPoiInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 219/301: DM_MAP_MapMarkerLocatorInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 220/301: DM_MAP_MapMarkerImageLabelInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 221/301: DM_MAP_MapMarkerIconInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 222/301: DM_MAP_MapMarkerGroupIconPosInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 223/301: DM_MAP_MapMarkerGroupIconInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 224/301: DM_MAP_MapMarkerCustomIconInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 225/301: DM_MAP_MapMarkerClusterData.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 226/301: DM_MAP_MapMarkerAvoidRouteRule.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 227/301: DM_MAP_MapMarkerAvoidDetailedRule.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 228/301: DM_MAP_MapMarkerAnnotationInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 229/301: DM_MAP_MapLocatorSpeedTextParam.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 230/301: DM_MAP_MapLocatorScanLightOptions.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 231/301: DM_MAP_MapLocatorBreatheOptions.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 232/301: DM_MAP_MapLocatorBreatheNodes.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 233/301: DM_MAP_MapLocation.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 234/301: DM_MAP_MapLaneID.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 235/301: DM_MAP_MapHoleInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 236/301: DM_MAP_MapEdgeInsets.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 237/301: DM_MAP_MapDisplayParam.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 238/301: DM_MAP_MapCircleInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 239/301: DM_MAP_MapBitmapTileID.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 240/301: DM_MAP_MapBitmap.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 241/301: DM_MAP_JuncImageInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 242/301: DM_MAP_InterestScenicAreaInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 243/301: DM_MAP_InterestIndoorAreaInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 244/301: DM_MAP_InterestAreaInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 245/301: DM_MAP_IndoorTappedInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 246/301: DM_MAP_IndoorParkSpaceInfoBatchs.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 247/301: DM_MAP_IndoorParkSpaceInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 248/301: DM_MAP_GuidanceEventInfo.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 249/301: DM_MAP_GlyphMetrics.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 250/301: DM_MAP_GLMapFloorName.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 251/301: DM_MAP_GLMapAnnotationText.m
2025-07-09 02:13:26,369 - pipeline.steps.ast_step_all - INFO - 处理编译文件 252/301: DM_MAP_GLMapAnnotationIcon.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 253/301: DM_MAP_GLBuildingInfo.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 254/301: DM_MAP_EngineRenderContent.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 255/301: DM_MAP_DynamicMapAnnotationObject.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 256/301: DM_MAP_DownloadData.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 257/301: DM_MAP_DayInfo.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 258/301: DM_MAP_CustomTileRegionStyle.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 259/301: DM_MAP_CustomTileQueryInfo.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 260/301: DM_MAP_CustomTilePointStyle.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 261/301: DM_MAP_CustomTileModel3DStyle.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 262/301: DM_MAP_CustomTileLineStyle.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 263/301: DM_MAP_Collision3DResult.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 264/301: DM_MAP_ClusterTappedInfo.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 265/301: DM_MAP_CameraOverlookParam.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 266/301: DM_MAP_CameraAnimationParam.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 267/301: DM_MAP_BuildingLoadedParam.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 268/301: DM_MAP_BuildingLightInfo.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 269/301: DM_MAP_BuildingLightIdAttributeIndex.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 270/301: DM_MAP_BuildingLightAttribute.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 271/301: DM_MAP_BlackWhiteListRule.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 272/301: DM_MAP_BillboardInfo.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 273/301: DM_MAP_BaseObject.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 274/301: DM_MAP_AnimationParam.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 275/301: DM_MAP_AnimationContent.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 276/301: MapBaseOCUtils.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 277/301: RGModelInterface.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 278/301: MapBaseModelInterface.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 279/301: MapBaseOCModel-dummy.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 280/301: MapBaseModel.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 281/301: RGModelInterfaceOCCPP.mm
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 282/301: MapBaseModelInterfaceOCCPP.mm
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 283/301: CustomAgileMapLayerOption.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 284/301: AgileMarkerCanvasLayoutParam.m
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 285/301: CustomAgileMapManager.mm
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 286/301: AgileMarkerCanvasLayoutParamOCCPP.mm
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 287/301: AgileMapManager.mm
2025-07-09 02:13:26,370 - pipeline.steps.ast_step_all - INFO - 处理编译文件 288/301: AgileMapResourceCallbackAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 289/301: AgileLayerOption.m
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 290/301: AgileCreateLayerCallBackAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 291/301: RGWalkCycleListenerAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 292/301: RGListenerAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 293/301: RGWalkAPI.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 294/301: RGVersion.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 295/301: RouteGuidanceOCMiddleware-dummy.m
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 296/301: RGDriveProvider.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 297/301: RGDriveStatisticsListenerAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 298/301: RGWalkCycleProtocol.m
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 299/301: RGDriveBehaviorEventListenerAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 300/301: RGDriveEventListenerAdapter.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 处理编译文件 301/301: RGCycleAPI.mm
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 发现主项目源文件
2025-07-09 02:13:26,371 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBusiness
2025-07-09 02:13:26,522 - pipeline.steps.ast_step_all - INFO - 在 QMapBusiness 中发现 1686 个源文件
2025-07-09 02:13:26,522 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapMiddlePlatform
2025-07-09 02:13:26,587 - pipeline.steps.ast_step_all - INFO - 在 QMapMiddlePlatform 中发现 1023 个源文件
2025-07-09 02:13:26,587 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapNaviKit
2025-07-09 02:13:26,788 - pipeline.steps.ast_step_all - INFO - 在 QMapNaviKit 中发现 461 个源文件
2025-07-09 02:13:26,788 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapHippy
2025-07-09 02:13:26,796 - pipeline.steps.ast_step_all - INFO - 在 QMapHippy 中发现 256 个源文件
2025-07-09 02:13:26,796 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DragonMapKit
2025-07-09 02:13:26,816 - pipeline.steps.ast_step_all - INFO - 在 DragonMapKit 中发现 207 个源文件
2025-07-09 02:13:26,816 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBaseline
2025-07-09 02:13:26,827 - pipeline.steps.ast_step_all - INFO - 在 QMapBaseline 中发现 135 个源文件
2025-07-09 02:13:26,827 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapFoundation
2025-07-09 02:13:26,832 - pipeline.steps.ast_step_all - INFO - 在 QMapFoundation 中发现 132 个源文件
2025-07-09 02:13:26,832 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapUIKit
2025-07-09 02:13:26,839 - pipeline.steps.ast_step_all - INFO - 在 QMapUIKit 中发现 66 个源文件
2025-07-09 02:13:26,839 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapRouteSearchKit
2025-07-09 02:13:26,840 - pipeline.steps.ast_step_all - INFO - 在 QMapRouteSearchKit 中发现 37 个源文件
2025-07-09 02:13:26,840 - pipeline.steps.ast_step_all - INFO - 扫描仓库: TencentMap
2025-07-09 02:13:26,848 - pipeline.steps.ast_step_all - INFO - 在 TencentMap 中发现 34 个源文件
2025-07-09 02:13:26,848 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DKProtocolsPool
2025-07-09 02:13:26,850 - pipeline.steps.ast_step_all - INFO - 在 DKProtocolsPool 中发现 12 个源文件
2025-07-09 02:13:26,850 - pipeline.steps.ast_step_all - INFO - 总共发现 4049 个项目源文件
2025-07-09 02:13:26,887 - pipeline.steps.ast_step_all - INFO - 补充处理符号映射中的项目文件
2025-07-09 02:13:26,912 - pipeline.steps.ast_step_all - INFO - 处理完成: 编译命令文件 301 个, 发现文件 4049 个, 符号映射文件 112 个, 总计 4462 个文件
2025-07-09 02:13:26,932 - pipeline.steps.ast_step_all - INFO - 节点和边已保存到: ast_out_index_all/nodes_all.jsonl, ast_out_index_all/edges_all.jsonl
2025-07-09 02:13:26,936 - pipeline.core - INFO - 步骤 ast-all 执行成功: AST抽取完成，生成了 8812 个节点和 4350 条边
2025-07-09 02:13:26,936 - pipeline.core - INFO - 执行步骤 6/6: stats-all
2025-07-09 02:13:26,936 - pipeline.core - INFO - 开始执行步骤: stats-all
2025-07-09 02:13:26,936 - pipeline.steps.stats_step_all - INFO - 开始多仓库统计验证
2025-07-09 02:13:26,950 - pipeline.steps.stats_step_all - INFO - 加载了 8812 个节点和 4350 条边
2025-07-09 02:13:26,950 - pipeline.steps.stats_step_all - INFO - 计算综合统计信息
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 统计结果已保存到: ast_out_index_all/stats_all.json
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 多仓库统计结果
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 总节点数: 8,812
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 总边数: 4,350
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 跨仓库关系: 0
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 
节点类型分布:
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   File: 4,462
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   Class: 4,350
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 
仓库分布:
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapBusiness: 3,412 节点, 1,706 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapMiddlePlatform: 2,006 节点, 1,003 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapNaviKit: 922 节点, 461 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   DragonMapKit: 618 节点, 309 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapHippy: 512 节点, 256 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapBaseline: 270 节点, 135 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapFoundation: 264 节点, 132 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapUIKit: 132 节点, 66 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   QMapRouteSearchKit: 74 节点, 37 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   TencentMap: 68 节点, 34 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   DKProtocolsPool: 34 节点, 17 文件
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO - 
质量指标:
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   节点边比例: 0.49
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   平均节点度数: 1.00
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   数据完整性: 1.00
2025-07-09 02:13:27,660 - pipeline.steps.stats_step_all - INFO -   文件覆盖率: 1.10
2025-07-09 02:13:27,661 - pipeline.core - ERROR - 步骤 stats-all 执行失败: 统计验证失败: min_nodes, min_edges, min_cross_repo
2025-07-09 02:13:27,661 - pipeline.core - ERROR - 步骤 stats-all 执行失败，停止流水线
2025-07-09 02:13:27,661 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
2025-07-09 02:14:42,999 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 02:14:42,999 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 02:14:42,999 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 02:14:43,024 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 02:14:43,034 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 02:14:43,097 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 02:14:43,134 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 02:14:43,139 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 02:14:43,236 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 02:14:43,312 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 02:14:43,485 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 02:14:44,018 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 02:14:44,310 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 02:14:44,313 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 02:14:44,313 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 02:14:44,314 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 02:14:44,314 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 02:14:44,314 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 02:14:44,314 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 02:14:44,314 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 02:14:44,314 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 02:14:44,314 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 02:14:44,314 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 02:14:44,314 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 02:14:44,314 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 02:14:44,314 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 02:14:44,314 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 02:14:44,314 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 02:14:44,314 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 02:14:46,361 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-09 02:14:46,362 - pipeline.steps.clean_step - INFO - 删除文件: index_symbols_all.jsonl
2025-07-09 02:14:46,362 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 30135 个项目，释放 6685773224 字节
2025-07-09 02:14:46,362 - pipeline.core - INFO - 执行步骤 2/6: index-store-all
2025-07-09 02:14:46,362 - pipeline.core - INFO - 开始执行步骤: index-store-all
2025-07-09 02:14:46,362 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-09 02:14:46,362 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 02:14:46,362 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-09 02:14:46,363 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 02:14:46,363 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-09 02:14:46,365 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成索引...
2025-07-09 02:14:46,367 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-09 02:14:46,369 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-09 02:14:46,370 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成索引...
2025-07-09 02:14:46,374 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-09 02:14:59,798 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapFoundation and configuration Debug
(1 failure)

2025-07-09 02:14:59,798 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-09 02:15:00,893 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DragonMapKit and configuration Debug
(1 failure)

2025-07-09 02:15:00,894 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-09 02:15:01,935 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-09 02:15:01,942 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-09 02:15:02,233 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-09 02:15:02,233 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-09 02:15:02,247 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-09 02:15:02,247 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-09 02:15:02,315 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-09 02:15:02,316 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-09 02:15:02,610 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-09 02:15:02,610 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-09 02:15:09,654 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-09 02:15:09,654 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-09 02:15:14,884 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-09 02:15:19,608 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-09 02:15:22,369 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(1 failure)

2025-07-09 02:15:22,447 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-09 02:15:22,459 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-09 02:15:22,803 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-09 02:15:25,688 - pipeline.steps.index_store_step_all - ERROR - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug
(1 failure)

2025-07-09 02:15:51,021 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 索引生成成功
2025-07-09 02:15:51,023 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:15:51,088 - pipeline.steps.index_store_step_all - INFO - 索引存储输出验证成功，找到 4646 个索引记录文件
2025-07-09 02:15:51,089 - pipeline.core - INFO - 步骤 index-store-all 执行成功: 索引生成部分完成，成功: 2, 失败: 14
2025-07-09 02:15:51,089 - pipeline.core - INFO - 执行步骤 3/6: usrs-all
2025-07-09 02:15:51,089 - pipeline.core - INFO - 开始执行步骤: usrs-all
2025-07-09 02:15:51,089 - pipeline.steps.usrs_step_all - INFO - 开始提取多仓库USR到文件路径映射
2025-07-09 02:15:51,089 - pipeline.steps.usrs_step_all - INFO - 检查索引存储目录: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:15:51,089 - pipeline.steps.usrs_step_all - INFO - 开始提取USR到文件路径映射
2025-07-09 02:15:51,089 - pipeline.steps.usrs_step_all - INFO - 使用索引存储路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:15:51,089 - pipeline.steps.usrs_step_all - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store ast_out_index_all/DerivedData/Index.noindex/DataStore --out index_symbols_all.jsonl
2025-07-09 02:15:54,260 - pipeline.steps.usrs_step_all - INFO - USR提取命令执行成功
2025-07-09 02:15:54,277 - pipeline.steps.usrs_step_all - INFO - 成功提取 145274 个USR映射
2025-07-09 02:15:54,277 - pipeline.steps.usrs_step_all - INFO - 分析提取的USR数据...
2025-07-09 02:15:54,618 - pipeline.steps.usrs_step_all - INFO - USR分析完成:
2025-07-09 02:15:54,618 - pipeline.steps.usrs_step_all - INFO -   总USR数: 145,254
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -   项目文件: 0
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -   Pods文件: 35,057
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -   系统文件: 0
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -   仓库分布:
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -     unknown: 139,518
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -     DragonMapKit: 3,030
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -     QMapFoundation: 1,863
2025-07-09 02:15:54,619 - pipeline.steps.usrs_step_all - INFO -     DKProtocolsPool: 843
2025-07-09 02:15:54,619 - pipeline.core - INFO - 步骤 usrs-all 执行成功: USR提取完成，提取了 145274 个USR映射
2025-07-09 02:15:54,619 - pipeline.core - INFO - 执行步骤 4/6: cdb-all
2025-07-09 02:15:54,619 - pipeline.core - INFO - 开始执行步骤: cdb-all
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 开始生成多仓库编译数据库
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成编译数据库...
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成编译数据库...
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成编译数据库...
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成编译数据库...
2025-07-09 02:15:54,619 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成编译数据库...
2025-07-09 02:15:54,621 - pipeline.steps.cdb_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成编译数据库...
2025-07-09 02:15:54,623 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成编译数据库...
2025-07-09 02:16:05,425 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败，但继续解析日志
2025-07-09 02:16:05,427 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapFoundation 的构建日志，长度: 14539 字符
2025-07-09 02:16:05,428 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 5 个clang命令
2025-07-09 02:16:05,429 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:05,429 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapFoundation 的scheme 'QMapFoundation' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:05,430 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成编译数据库...
2025-07-09 02:16:06,639 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapUIKit 的构建日志，长度: 4240129 字符
2025-07-09 02:16:06,644 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 242 个CompileC行, 794 个clang命令
2025-07-09 02:16:06,962 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败，但继续解析日志
2025-07-09 02:16:06,967 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBaseline 的构建日志，长度: 48087 字符
2025-07-09 02:16:06,972 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:06,975 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:06,976 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBaseline 的scheme 'QMapBaseline' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:06,977 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成编译数据库...
2025-07-09 02:16:07,515 - pipeline.steps.cdb_step_all - WARNING - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败，但继续解析日志
2025-07-09 02:16:07,516 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DragonMapKit 的构建日志，长度: 20632 字符
2025-07-09 02:16:07,516 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:07,517 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:07,517 - pipeline.steps.cdb_step_all - INFO - 仓库 DragonMapKit 的scheme 'DragonMapKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:07,518 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成编译数据库...
2025-07-09 02:16:07,548 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 257/509 个文件通过过滤
2025-07-09 02:16:07,549 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 编译数据库生成成功，包含 257 个编译命令
2025-07-09 02:16:07,549 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成编译数据库...
2025-07-09 02:16:07,878 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败，但继续解析日志
2025-07-09 02:16:07,878 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapMiddlePlatform 的构建日志，长度: 27963 字符
2025-07-09 02:16:07,878 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:07,879 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:07,879 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:07,879 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成编译数据库...
2025-07-09 02:16:08,375 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败，但继续解析日志
2025-07-09 02:16:08,376 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapNaviKit 的构建日志，长度: 29954 字符
2025-07-09 02:16:08,376 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:08,376 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:08,376 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:08,376 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成编译数据库...
2025-07-09 02:16:08,421 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败，但继续解析日志
2025-07-09 02:16:08,422 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapHippy 的构建日志，长度: 29639 字符
2025-07-09 02:16:08,422 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:08,422 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:08,422 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapHippy 的scheme 'QMapHippy' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:08,422 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成编译数据库...
2025-07-09 02:16:21,627 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败，但继续解析日志
2025-07-09 02:16:21,627 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapRouteSearchKit 的构建日志，长度: 15669 字符
2025-07-09 02:16:21,627 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 5 个clang命令
2025-07-09 02:16:21,627 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:21,628 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:21,628 - pipeline.steps.cdb_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成编译数据库...
2025-07-09 02:16:22,560 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败，但继续解析日志
2025-07-09 02:16:22,560 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 1963 字符
2025-07-09 02:16:22,560 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 1 个clang命令
2025-07-09 02:16:22,560 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:22,560 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'BuildInfo' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:28,543 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'TencentMap' 构建失败，但继续解析日志
2025-07-09 02:16:28,544 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89211 字符
2025-07-09 02:16:28,544 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:28,544 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:28,544 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'TencentMap' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:29,174 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntents' 构建失败，但继续解析日志
2025-07-09 02:16:29,174 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89210 字符
2025-07-09 02:16:29,174 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:29,174 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:29,174 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntents' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:29,895 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败，但继续解析日志
2025-07-09 02:16:29,895 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89212 字符
2025-07-09 02:16:29,895 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:29,896 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:29,896 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntentsUI' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:30,492 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败，但继续解析日志
2025-07-09 02:16:30,492 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89218 字符
2025-07-09 02:16:30,492 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:30,493 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:30,493 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMWidgetExtension' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:32,073 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败，但继续解析日志
2025-07-09 02:16:32,073 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89222 字符
2025-07-09 02:16:32,073 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:16:32,074 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:32,074 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMNotificationService' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:16:35,226 - pipeline.steps.cdb_step_all - WARNING - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败，但继续解析日志
2025-07-09 02:16:35,227 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DKProtocolsPool 的构建日志，长度: 2277 字符
2025-07-09 02:16:35,227 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 2 个clang命令
2025-07-09 02:16:35,227 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:16:35,227 - pipeline.steps.cdb_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:18:05,716 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBusiness 的构建日志，长度: 32152803 字符
2025-07-09 02:18:05,748 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 1109 个CompileC行, 4951 个clang命令
2025-07-09 02:18:12,100 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 1768/2920 个文件通过过滤
2025-07-09 02:18:12,101 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBusiness 的scheme 'QMapBusiness' 编译数据库生成成功，包含 1768 个编译命令
2025-07-09 02:18:12,104 - pipeline.steps.cdb_step_all - INFO - 合并编译命令完成，共 1768 个唯一文件
2025-07-09 02:18:12,119 - pipeline.steps.cdb_step_all - INFO - 编译数据库已保存到: ast_out_index_all/compile_commands/compile_commands.json
2025-07-09 02:18:12,120 - pipeline.core - INFO - 步骤 cdb-all 执行成功: 编译数据库生成完成，处理了 16 个scheme，生成 1768 个编译命令
2025-07-09 02:18:12,120 - pipeline.core - INFO - 执行步骤 5/6: ast-all
2025-07-09 02:18:12,120 - pipeline.core - INFO - 开始执行步骤: ast-all
2025-07-09 02:18:12,120 - pipeline.steps.ast_step_all - INFO - 开始多仓库AST抽取
2025-07-09 02:18:12,126 - pipeline.steps.ast_step_all - INFO - 从编译数据库加载了 1768 个编译命令
2025-07-09 02:18:12,126 - pipeline.steps.ast_step_all - INFO - 加载了 1768 个编译命令
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14041: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14042: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14043: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14044: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14045: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14046: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14047: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14048: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14049: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,139 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 14050: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - WARNING - 跳过了 20/145274 个无效的符号行
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 成功加载 145254 个符号映射
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理 1768 个编译命令
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1/1768: libMMKV.mm
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 2/1768: CALayer+Compat.m
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 3/1768: NSBezierPath+SDRoundedCorners.m
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 4/1768: QMAlertController.m
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 5/1768: NSObject+QDMSwizzle.m
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 6/1768: GPBAny.pbobjc.m
2025-07-09 02:18:12,267 - pipeline.steps.ast_step_all - INFO - 处理编译文件 7/1768: MASCompositeConstraint.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 8/1768: APLAppObject.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 9/1768: NSArray+QMChaining.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 10/1768: CGGeometry+LOTAdditions.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 11/1768: LOTAnimatedControl.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 12/1768: LOTAnimationCache.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 13/1768: LOTAnimationTransitionController.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 14/1768: LOTAnimatedSwitch.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 15/1768: LOTAnimationView.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 16/1768: LOTAnimatorNode.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 17/1768: LOTArrayInterpolator.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 18/1768: LOTAsset.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 19/1768: LOTAssetGroup.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 20/1768: LOTBezierData.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 21/1768: lottie-ios-dummy.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 22/1768: UIColor.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 23/1768: UIColor+Expanded.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 24/1768: UIBezierPath.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 25/1768: NSValue+Compat.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 26/1768: LOTValueInterpolator.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 27/1768: LOTValueCallback.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 28/1768: LOTTrimPathNode.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 29/1768: LOTTransformInterpolator.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 30/1768: LOTStrokeRenderer.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 31/1768: LOTSizeInterpolator.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 32/1768: LOTShapeTrimPath.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 33/1768: LOTShapeTransform.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 34/1768: LOTShapeStroke.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 35/1768: LOTShapeStar.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 36/1768: LOTShapeRepeater.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 37/1768: LOTShapeRectangle.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 38/1768: LOTShapePath.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 39/1768: LOTShapeGroup.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 40/1768: LOTShapeGradientFill.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 41/1768: LOTShapeFill.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 42/1768: LOTShapeCircle.m
2025-07-09 02:18:12,268 - pipeline.steps.ast_step_all - INFO - 处理编译文件 43/1768: LOTRoundedRectAnimator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 44/1768: LOTRepeaterRenderer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 45/1768: LOTRenderNode.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 46/1768: LOTRenderGroup.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 47/1768: LOTPolystarAnimator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 48/1768: LOTRadialGradientLayer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 49/1768: LOTPolygonAnimator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 50/1768: LOTPointInterpolator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 51/1768: LOTPathInterpolator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 52/1768: LOTPathAnimator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 53/1768: LOTNumberInterpolator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 54/1768: LOTMaskContainer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 55/1768: LOTMask.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 56/1768: LOTLayerGroup.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 57/1768: LOTLayerContainer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 58/1768: LOTLayer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 59/1768: LOTKeypath.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 60/1768: LOTInterpolatorCallback.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 61/1768: LOTKeyframe.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 62/1768: LOTGradientFillRender.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 63/1768: LOTFillRenderer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 64/1768: LOTComposition.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 65/1768: LOTColorInterpolator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 66/1768: LOTCircleAnimator.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 67/1768: LOTCacheProvider.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 68/1768: LOTCompositionContainer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 69/1768: LOTBlockCallback.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 70/1768: LOTBezierPath.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 71/1768: QMapBasics-iOS12.0-dummy.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 72/1768: QMSwizzle.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 73/1768: QMTimer.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 74/1768: QMReachability.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 75/1768: QMPath.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 76/1768: QMDigest.m
2025-07-09 02:18:12,269 - pipeline.steps.ast_step_all - INFO - 处理编译文件 77/1768: QMDateFormatterPool.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 78/1768: QMCallManager.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 79/1768: NSString+QMVersionSplit.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 80/1768: QMNetworkStatusManager.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 81/1768: UIView+WebCacheOperation.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 82/1768: UIView+WebCache.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 83/1768: UIImageView+WebCache.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 84/1768: UIImageView+HighlightedWebCache.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 85/1768: UIImage+Transform.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 86/1768: UIImage+MultiFormat.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 87/1768: UIImage+Metadata.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 88/1768: UIImage+MemoryCacheCost.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 89/1768: UIImage+GIF.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 90/1768: UIImage+ForceDecode.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 91/1768: UIImage+ExtendedCacheData.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 92/1768: UIButton+WebCache.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 93/1768: UIColor+SDHexString.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 94/1768: SDWebImageTransition.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 95/1768: SDWebImagePrefetcher.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 96/1768: SDWebImageOptionsProcessor.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 97/1768: SDWebImageManager.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 98/1768: SDWebImageOperation.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 99/1768: SDWebImageError.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 100/1768: SDWebImageIndicator.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 101/1768: SDWebImageDownloaderResponseModifier.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 102/1768: SDWebImageDownloaderRequestModifier.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 103/1768: SDWebImageDownloaderOperation.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 104/1768: SDWebImageDownloaderDecryptor.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 105/1768: SDWebImageDownloaderConfig.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 106/1768: SDWebImageDownloader.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 107/1768: SDWebImageDefine.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 108/1768: SDWebImageCompat.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 109/1768: SDWebImageCacheSerializer.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 110/1768: SDWebImageCacheKeyFilter.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 111/1768: SDWebImage-iOS12.0-dummy.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 112/1768: SDWeakProxy.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 113/1768: SDMemoryCache.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 114/1768: SDInternalMacros.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 115/1768: SDImageTransformer.m
2025-07-09 02:18:12,270 - pipeline.steps.ast_step_all - INFO - 处理编译文件 116/1768: SDImageLoadersManager.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 117/1768: SDImageLoader.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 118/1768: SDImageIOCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 119/1768: SDImageIOAnimatedCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 120/1768: SDImageHEICCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 121/1768: SDImageGraphics.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 122/1768: SDImageGIFCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 123/1768: SDImageFramePool.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 124/1768: SDImageFrame.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 125/1768: SDImageCodersManager.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 126/1768: SDImageCoderHelper.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 127/1768: SDImageCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 128/1768: SDImageCachesManagerOperation.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 129/1768: SDImageCachesManager.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 130/1768: SDImageCacheDefine.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 131/1768: SDImageCacheConfig.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 132/1768: SDImageCache.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 133/1768: SDImageAssetManager.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 134/1768: SDImageAWebPCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 135/1768: SDImageAPNGCoder.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 136/1768: SDGraphicsImageRenderer.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 137/1768: QMapBasics-iOS12.0-dummy.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 138/1768: SDFileAttributeHelper.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 139/1768: SDDisplayLink.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 140/1768: SDDiskCache.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 141/1768: SDDeviceHelper.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 142/1768: SDCallbackQueue.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 143/1768: SDAsyncBlockOperation.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 144/1768: SDAssociatedObject.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 145/1768: SDAnimatedImageView.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 146/1768: SDAnimatedImageView+WebCache.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 147/1768: SDAnimatedImageRep.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 148/1768: SDAnimatedImagePlayer.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 149/1768: SDAnimatedImage.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 150/1768: NSImage+Compatibility.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 151/1768: NSData+ImageContentType.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 152/1768: NSButton+WebCache.m
2025-07-09 02:18:12,271 - pipeline.steps.ast_step_all - INFO - 处理编译文件 153/1768: UIView+QMCoordinate.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 154/1768: UIView+QMAutoLayout.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 155/1768: UIColor+QMExtension.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 156/1768: QMapWidgets-iOS12.0-dummy.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 157/1768: QMWindowTools.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 158/1768: QMTableViewSection.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 159/1768: QMTableViewRow.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 160/1768: QMTableViewFormatter.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 161/1768: QMEdgeLabel.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 162/1768: QMButton.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 163/1768: QMAlertWindowViewController.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 164/1768: UIWindow+QDMEnvironment.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 165/1768: UIViewController+QDMEnvironment.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 166/1768: UIView+QDMEnvironment.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 167/1768: UIScreen+QDMEnvironment.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 168/1768: UIImageView+QDM.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 169/1768: UIImage+QDM.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 170/1768: UIColor+QDM.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 171/1768: QMapDarkModeManager.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 172/1768: QMapDarkMode-dummy.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 173/1768: QDMTraitCollection.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 174/1768: QMapWidgets-iOS12.0-dummy.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 175/1768: QDMRegistry.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 176/1768: QMapDarkModeManager.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 177/1768: QDMLog.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 178/1768: QDMGlobalTestHooks.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 179/1768: QDMEnvConfiguration.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 180/1768: QDMConvertor.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 181/1768: NSString+QMVersionCompare.m
2025-07-09 02:18:12,272 - pipeline.steps.ast_step_all - INFO - 处理编译文件 182/1768: NSString+QMURL.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 183/1768: QMapDarkMode-dummy.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 184/1768: NSString+QMCommon.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 185/1768: NSObject+QMSafeTypeConversion.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 186/1768: NSMutableString+QMSafeStringOperation.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 187/1768: NSMutableSet+QMSafeCollectionSet.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 188/1768: NSMutableDictionary+QMSafeCollectionDictionary.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 189/1768: NSMutableArray+QMSafeCollectonArray.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 190/1768: NSDictionary+QMHasObjectType.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 191/1768: NSDictionary+QMCommon.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 192/1768: NSDate+Common.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 193/1768: NSData+QMImageContentType.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 194/1768: NSData+QMGZip.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 195/1768: NSData+QMBase64WithImagePrefix.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 196/1768: NSArray+QMSafeCollectonArray.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 197/1768: Protobuf-dummy.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 198/1768: GPBWrappers.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 199/1768: GPBWireFormat.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 200/1768: GPBWellKnownTypes.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 201/1768: GPBUtilities.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 202/1768: GPBUnknownFieldSet.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 203/1768: GPBUnknownField.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 204/1768: GPBType.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 205/1768: GPBTimestamp.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 206/1768: GPBStruct.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 207/1768: GPBSourceContext.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 208/1768: GPBRootObject.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 209/1768: GPBMessage.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 210/1768: GPBFieldMask.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 211/1768: GPBExtensionRegistry.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 212/1768: GPBExtensionInternals.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 213/1768: GPBEmpty.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 214/1768: GPBDuration.pbobjc.m
2025-07-09 02:18:12,273 - pipeline.steps.ast_step_all - INFO - 处理编译文件 215/1768: GPBDictionary.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 216/1768: GPBDescriptor.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 217/1768: GPBCodedOutputStream.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 218/1768: GPBCodedInputStream.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 219/1768: GPBArray.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 220/1768: GPBApi.pbobjc.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 221/1768: ViewController+MASAdditions.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 222/1768: View+MASAdditions.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 223/1768: NSLayoutConstraint+MASDebugAdditions.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 224/1768: NSArray+MASAdditions.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 225/1768: Masonry-iOS12.0-dummy.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 226/1768: MASViewConstraint.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 227/1768: MASViewAttribute.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 228/1768: MASLayoutConstraint.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 229/1768: MASConstraintMaker.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 230/1768: MASConstraint.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 231/1768: MMKVCore-dummy.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 232/1768: MMKV-dummy.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 233/1768: PBAPLProtocol.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 234/1768: ApolloSDK.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 235/1768: ApolloSDK-dummy.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 236/1768: APLSafeObject.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 237/1768: APLSDKHelper.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 238/1768: APLSDKConfig.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 239/1768: APLRespDTO.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 240/1768: APLRegistry.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 241/1768: APLPrivate.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 242/1768: APLNodeDataRefresher.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 243/1768: APLNode.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 244/1768: APLNetworkSessionManager.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 245/1768: APLNetworkSessionDataTask.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 246/1768: APLNetworkSerialization.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 247/1768: APLNetworkRequest.m
2025-07-09 02:18:12,274 - pipeline.steps.ast_step_all - INFO - 处理编译文件 248/1768: APLNetworkEncryptor.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 249/1768: APLNetworkAPI.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 250/1768: APLModuleObject.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 251/1768: APLLog.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 252/1768: APLInterface.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 253/1768: APLConfigObject.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 254/1768: APLCacheConfig.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 255/1768: APLCache.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 256/1768: APLBusinessObject.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 257/1768: APLBasePO.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 258/1768: Yoga-dummy.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 259/1768: YYTextWeakProxy.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 260/1768: YYTextView.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 261/1768: YYTextUtilities.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 262/1768: YYTextTransaction.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 263/1768: YYTextSelectionView.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 264/1768: YYTextRunDelegate.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 265/1768: YYTextRubyAnnotation.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 266/1768: YYTextParser.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 267/1768: YYTextMagnifier.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 268/1768: YYTextLine.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 269/1768: YYTextLayout.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 270/1768: YYTextKeyboardManager.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 271/1768: YYTextInput.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 272/1768: YYTextEffectWindow.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 273/1768: YYTextDebugOption.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 274/1768: YYTextContainerView.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 275/1768: YYTextAttribute.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 276/1768: YYTextAsyncLayer.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 277/1768: YYTextArchiver.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 278/1768: YYText-dummy.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 279/1768: YYLabel.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 280/1768: UIView+YYText.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 281/1768: UIPasteboard+YYText.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 282/1768: NSParagraphStyle+YYText.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 283/1768: NSAttributedString+YYText.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 284/1768: YYSpriteSheetImage.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 285/1768: YYImageCoder.m
2025-07-09 02:18:12,275 - pipeline.steps.ast_step_all - INFO - 处理编译文件 286/1768: YYImage.m
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 287/1768: YYImage-dummy.m
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 288/1768: YYFrameImage.m
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 289/1768: YYAnimatedImageView.m
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 290/1768: UIView+Render.m
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 291/1768: VFSUriLoader.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 292/1768: VFSUriHandler.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 293/1768: UIView+Hippy.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 294/1768: UIView+DomEvent.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 295/1768: UIView+DirectionalLayout.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 296/1768: TypeConverter.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 297/1768: NativeRenderManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 298/1768: NSURLSessionDataProgress.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 299/1768: NSURLResponse+ToUnorderedMap.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 300/1768: NSObject+CtxValue.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 301/1768: HippyWaterfallView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 302/1768: HippyViewPager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 303/1768: HippyViewManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 304/1768: HippyUIManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 305/1768: HippyTurboModuleManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 306/1768: HippyTouchHandler.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 307/1768: HippyTextViewManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 308/1768: HippyTextView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 309/1768: HippyTextManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 310/1768: HippyText.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 311/1768: HippyShadowView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 312/1768: HippyShadowTextView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 313/1768: HippyShadowText.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 314/1768: HippyScrollViewManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 315/1768: HippyScrollView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 316/1768: HippyRootViewManager.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 317/1768: HippyRootView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 318/1768: HippyRootShadowView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 319/1768: HippyRedBox.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 320/1768: HippyOCTurboModule.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 321/1768: HippyOCToHippyValue.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 322/1768: HippyOCToDomArgument.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 323/1768: HippyNextBaseListView.mm
2025-07-09 02:18:12,276 - pipeline.steps.ast_step_all - INFO - 处理编译文件 324/1768: HippyNetWork.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 325/1768: HippyNetInfo.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 326/1768: HippyNavigatorViewManager.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 327/1768: HippyModuleMethod.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 328/1768: HippyModuleData.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 329/1768: HippyModalHostViewManager.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 330/1768: HippyLog.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 331/1768: HippyJSExecutor.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 332/1768: HippyJSEnginesMapper.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 333/1768: HippyImageViewManager.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 334/1768: HippyImageLoaderModule.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 335/1768: HippyFootstoneUtils.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 336/1768: HippyFont.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 337/1768: HippyFileHandler.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 338/1768: HippyExceptionModule.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 339/1768: HippyEventObserverModule.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 340/1768: HippyEventDispatcher.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 341/1768: HippyDomUtils.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 342/1768: HippyDeviceBaseInfo.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 343/1768: HippyDevMenu.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 344/1768: HippyComponentMap.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 345/1768: HippyComponentData.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 346/1768: HippyBridge.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 347/1768: HippyBridge+VFSLoader.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 348/1768: HippyBridge+VFS.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 349/1768: HippyBridge+PerformanceAPI.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 350/1768: HippyBridge+ModuleManage.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 351/1768: HippyBridge+BundleLoad.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 352/1768: HippyBase64DataHandler.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 353/1768: HippyAsyncLocalStorage.mm
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 354/1768: hippy-dummy.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 355/1768: UIView+MountEvent.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 356/1768: UIBezierPath+HippyShadow.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 357/1768: NativeRenderTouchesView.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 358/1768: NSObject+HippyTurbo.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 359/1768: NSData+DataType.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 360/1768: HippyWebSocketManager.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 361/1768: HippyWeakProxy.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 362/1768: HippyWaterfallViewManager.m
2025-07-09 02:18:12,277 - pipeline.steps.ast_step_all - INFO - 处理编译文件 363/1768: HippyWaterfallViewDataSource.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 364/1768: HippyWaterfallViewCell.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 365/1768: HippyWaterfallItemViewManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 366/1768: HippyWaterfallItemView.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 367/1768: HippyVsyncManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 368/1768: HippyViewPagerManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 369/1768: HippyViewPagerItemManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 370/1768: HippyViewPagerItem.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 371/1768: HippyViewInnerLayer.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 372/1768: HippyView.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 373/1768: HippyUtils.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 374/1768: HippyTextSelection.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 375/1768: HippyTextField.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 376/1768: HippySimpleWebViewManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 377/1768: HippySimpleWebView.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 378/1768: HippyShadowWaterfallItem.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 379/1768: HippyShadowListView.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 380/1768: HippySRWebSocket.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 381/1768: HippySRSIMDHelpers.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 382/1768: HippyRenderUtils.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 383/1768: HippyRefreshWrapperViewManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 384/1768: HippyRefreshWrapperItemViewManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 385/1768: HippyRefreshWrapperItemView.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 386/1768: HippyRefreshWrapper.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 387/1768: HippyRefresh.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 388/1768: HippyQMGlobalProvider.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 389/1768: HippyParserUtils.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 390/1768: HippyNextShadowListItem.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 391/1768: HippyNextListTableView.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 392/1768: HippyNextCollectionViewFlowLayout.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 393/1768: HippyNextBaseListViewManager.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 394/1768: HippyNextBaseListViewDataSource.m
2025-07-09 02:18:12,278 - pipeline.steps.ast_step_all - INFO - 处理编译文件 395/1768: HippyNextBaseListViewCell.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 396/1768: HippyNextBaseListItemViewManager.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 397/1768: HippyNextBaseListItemView.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 398/1768: HippyNetInfoIntenal.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 399/1768: HippyNavigatorRootViewController.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 400/1768: HippyNavigatorItemViewController.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 401/1768: HippyNavigatorHostView.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 402/1768: HippyNavigationControllerAnimator.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 403/1768: HippyModulesSetup.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 404/1768: HippyModalTransitioningDelegate.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 405/1768: HippyModalHostViewController.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 406/1768: HippyModalHostView.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 407/1768: HippyModalCustomPresentationController.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 408/1768: HippyModalCustomAnimationTransition.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 409/1768: HippyMemoryShare.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 410/1768: HippyKeyCommands.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 411/1768: HippyJSStackFrame.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 412/1768: HippyImageView.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 413/1768: HippyI18nUtils.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 414/1768: HippyHeaderRefreshManager.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 415/1768: HippyHeaderRefresh.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 416/1768: HippyGradientObject.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 417/1768: HippyFrameUpdate.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 418/1768: HippyFooterRefreshManager.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 419/1768: HippyFooterRefresh.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 420/1768: HippyErrorInfo.m
2025-07-09 02:18:12,279 - pipeline.steps.ast_step_all - INFO - 处理编译文件 421/1768: HippyDisplayLink.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 422/1768: HippyDevInfo.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 423/1768: HippyDefaultImageProvider.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 424/1768: HippyConvert.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 425/1768: HippyConvert+NativeRender.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 426/1768: HippyCollectionViewWaterfallLayout.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 427/1768: HippyBundleURLProvider.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 428/1768: HippyBorderDrawing.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 429/1768: HippyBaseTextInput.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 430/1768: HippyAssert.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 431/1768: HippyAnimatedImageView.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 432/1768: HippyAnimatedImage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 433/1768: YYMemoryCache.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 434/1768: YYKVStorage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 435/1768: YYDiskCache.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 436/1768: YYCache.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 437/1768: YYCache-dummy.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 438/1768: _YYWebImageSetter.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 439/1768: YYWebImageOperation.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 440/1768: YYWebImageManager.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 441/1768: YYWebImage-dummy.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 442/1768: YYImageCache.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 443/1768: UIImageView+YYWebImage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 444/1768: UIImage+YYWebImage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 445/1768: UIButton+YYWebImage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 446/1768: MKAnnotationView+YYWebImage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 447/1768: CALayer+YYWebImage.m
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 448/1768: RCLayout.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 449/1768: RCFatal.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 450/1768: RCDispatch.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 451/1768: RCDimension.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 452/1768: RCComputeRootLayout.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 453/1768: RCComponentDescriptionHelper.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 454/1768: RCComponentSize.mm
2025-07-09 02:18:12,280 - pipeline.steps.ast_step_all - INFO - 处理编译文件 455/1768: RCEqualityHelpers.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 456/1768: VLLexUtil.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 457/1768: JSBuiltinFunctions.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 458/1768: RCAssociatedObject.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 459/1768: ComponentViewReuseUtilities.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 460/1768: ComponentViewManager.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 461/1768: CKWeakObjectContainer.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 462/1768: CKViewConfiguration.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 463/1768: CKSizeRange.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 464/1768: CKMountedObjectForView.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 465/1768: CKMountableHelpers.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 466/1768: CKInternalHelpers.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 467/1768: CKGlobalConfig.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 468/1768: CKComponentViewClass.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 469/1768: CKComponentViewAttribute.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 470/1768: PerfSightApi.mm
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 471/1768: VectorLayoutReactivity-dummy.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 472/1768: VLReactivity.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 473/1768: VLReactivity+Private.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 474/1768: VLReactiveEffect.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 475/1768: VLDictionaryProxy.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 476/1768: VLArrayProxy.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 477/1768: NSObject+VLReactivity.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 478/1768: NSObject+VLReactiveEqual.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 479/1768: NSObject+VLProxyObject.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 480/1768: VectorLayoutExpression-dummy.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 481/1768: VLExpressionParser.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 482/1768: VLExpressionLex.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 483/1768: NSString+VLExpression.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 484/1768: NSObject+JSOperator.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 485/1768: JSWithStatement.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 486/1768: JSWhileStatement.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 487/1768: JSVariableDeclarator.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 488/1768: JSVariableDeclaration.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 489/1768: JSUpdateExpression.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 490/1768: JSUnaryExpression.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 491/1768: JSTryStatementNode.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 492/1768: JSThrowStatement.m
2025-07-09 02:18:12,281 - pipeline.steps.ast_step_all - INFO - 处理编译文件 493/1768: JSThisExpression.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 494/1768: JSSwitchStatement.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 495/1768: JSSwitchCaseNode.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 496/1768: JSStatement.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 497/1768: JSSequenceExpression.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 498/1768: JSScope.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 499/1768: JSReturnStatementNode.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 500/1768: JSPropertyNode.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 501/1768: JSProgramNode.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 502/1768: JSOperationType.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 503/1768: JSObjectExpression.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 504/1768: JSNewExpression.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 505/1768: JSMemberExpression.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 506/1768: JSLogicalExpression.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 507/1768: JSLiteral.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 508/1768: JSLabeledStatementNode.m
2025-07-09 02:18:12,282 - pipeline.steps.ast_step_all - INFO - 处理编译文件 509/1768: JSInterpreter.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 510/1768: JSIfStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 511/1768: JSIdentifier.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 512/1768: JSFunctionExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 513/1768: JSFunctionDeclaration.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 514/1768: JSFunctionBody.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 515/1768: JSFunction.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 516/1768: JSForStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 517/1768: JSForInStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 518/1768: JSExpressionStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 519/1768: JSExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 520/1768: JSEmptyStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 521/1768: JSDoWhileStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 522/1768: JSDirective.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 523/1768: JSContinueStatementNode.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 524/1768: JSConditionalExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 525/1768: JSCatchClauseNode.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 526/1768: JSCallExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 527/1768: JSBreakStatementNode.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 528/1768: JSBlockStatement.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 529/1768: JSBinaryOperationUtil.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 530/1768: JSBinaryExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 531/1768: JSAssignmentExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 532/1768: JSArrayExpression.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 533/1768: JSASTNode.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 534/1768: JSASTFactory.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 535/1768: SVRadialGradientLayer.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 536/1768: SVProgressHUD.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 537/1768: SVProgressHUD-dummy.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 538/1768: SVProgressAnimatedView.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 539/1768: SVIndefiniteAnimatedView.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 540/1768: RenderCore-dummy.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 541/1768: QuickJS-dummy.m
2025-07-09 02:18:12,283 - pipeline.steps.ast_step_all - INFO - 处理编译文件 542/1768: RouteTraffic.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 543/1768: QMapIntentsExtension-dummy.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 544/1768: QMRouteConditionIntentHandler.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 545/1768: QMRouteConditionIntent.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 546/1768: IntentHandler.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 547/1768: PerfSight-dummy.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 548/1768: QmlinkClientUpMsg.pbobjc.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 549/1768: QmlinkClientDownMsg.pbobjc.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 550/1768: QMLinkSubscribeReceipt.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 551/1768: QMLinkSubscribeManager.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 552/1768: QMLinkSocketRequestMemoryPersistence.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 553/1768: QMapIntentsExtension-dummy.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 554/1768: QMLinkSocketRequestEncoder.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 555/1768: QMLinkSocketRequest.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 556/1768: QMLinkSendData.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 557/1768: QMLinkRequestResponse.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 558/1768: QMLinkRequestParameter.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 559/1768: QMLinkRequestManager.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 560/1768: QMLinkRequestJsonManager.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 561/1768: QMLinkRequestJCEManager.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 562/1768: QMLinkRequestEventTracker.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 563/1768: QMLinkReconnectTimer.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 564/1768: QMLinkPlugin.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 565/1768: QMLinkMessageDispatcher.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 566/1768: QMLinkMQTTRootEncoder.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 567/1768: QMLinkMQTTRootDecoder.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 568/1768: QMLinkMQTTManager.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 569/1768: QMLinkMQTTEventTracker.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 570/1768: QMLinkMQTTConnectTimer.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 571/1768: QMLinkMQTTConfig.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 572/1768: QMLinkJsonBodyDecoder.m
2025-07-09 02:18:12,284 - pipeline.steps.ast_step_all - INFO - 处理编译文件 573/1768: QMLinkEncodeTool.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 574/1768: QMLinkDispatchEventTracker.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 575/1768: QMLinkDebugWindow.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 576/1768: QMLinkDebugViewController.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 577/1768: QMLinkDebugRootController.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 578/1768: QMLinkDebugMessageCell.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 579/1768: QMLinkDebugMessage.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 580/1768: QMLinkDebugManager.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 581/1768: QMLinkDebugLabel.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 582/1768: QMLinkDebugFloatView.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 583/1768: QMLinkConfig.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 584/1768: QMLinkCommonDecoder.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 585/1768: QMLinkCMDControl.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 586/1768: QMLink.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 587/1768: QMLink+Injection.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 588/1768: QMLink+Factory.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 589/1768: QMLink+Adaptor.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 590/1768: LongLinkSDK-dummy.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 591/1768: GCDWebServerURLEncodedFormRequest.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 592/1768: GCDWebServerStreamedResponse.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 593/1768: GCDWebServerResponse.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 594/1768: GCDWebServerRequest.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 595/1768: GCDWebServerMultiPartFormRequest.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 596/1768: GCDWebServerFunctions.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 597/1768: GCDWebServerFileResponse.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 598/1768: GCDWebServerFileRequest.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 599/1768: GCDWebServerErrorResponse.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 600/1768: GCDWebServerDataResponse.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 601/1768: GCDWebServerDataRequest.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 602/1768: GCDWebServerConnection.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 603/1768: GCDWebServer.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 604/1768: GCDWebServer-dummy.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 605/1768: EDSunriseSet.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 606/1768: EDSunriseSet-dummy.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 607/1768: QMapSwiftBridge-iOS12.0-dummy.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 608/1768: UIView+DHSmartScreenshot.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 609/1768: UITableView+DHSmartScreenshot.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 610/1768: UIScrollView+DHSmartScreenshot.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 611/1768: UIImage+DHImageAdditions.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 612/1768: DHSmartScreenshot-dummy.m
2025-07-09 02:18:12,285 - pipeline.steps.ast_step_all - INFO - 处理编译文件 613/1768: GCDAsyncUdpSocket.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 614/1768: GCDAsyncSocket.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 615/1768: CocoaAsyncSocket-dummy.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 616/1768: AutoCoding.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 617/1768: AutoCoding-dummy.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 618/1768: Aspects.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 619/1768: QMapSwiftBridge-iOS12.0-dummy.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 620/1768: Aspects-dummy.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 621/1768: VLYogaValueCssValue.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 622/1768: VLYogaCalcCssValue.m
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 623/1768: VectorLayout.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 624/1768: VectorLayout+Debug.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 625/1768: VLViewWidget.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 626/1768: VLViewPaggerComponentController.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 627/1768: VLViewPagerWidget.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 628/1768: VLViewPagerWidget+JS.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 629/1768: VLViewPagerComponentAttributes.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 630/1768: VLViewPagerComponent.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 631/1768: VLViewPagerAttributeSetterFactory.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 632/1768: VLViewComponnetAttributes.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 633/1768: VLViewComponent.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 634/1768: VLViewAttributeSetterFactory.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 635/1768: VLVideoWidget.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 636/1768: VLVideoComponentController.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 637/1768: VLVideoComponentAttributes.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 638/1768: VLVideoComponent.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 639/1768: VLVideoAttributeSetterFactory.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 640/1768: VLTextWidget.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 641/1768: VLTextComponentView.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 642/1768: VLTextComponentLayerHighlighter.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 643/1768: VLTextComponentLayer.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 644/1768: VLTextComponentAttributes.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 645/1768: VLTextComponent.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 646/1768: VLTextAttributeSetterFactory.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 647/1768: VLStringHash.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 648/1768: VLStdWrapper.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 649/1768: VLStateUpdater.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 650/1768: VLSmartPagerWidget.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 651/1768: VLSmartPagerWidget+JS.mm
2025-07-09 02:18:12,286 - pipeline.steps.ast_step_all - INFO - 处理编译文件 652/1768: VLSmartPagerComponentController.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 653/1768: VLSmartPagerComponentAttributes.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 654/1768: VLSmartPagerComponent.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 655/1768: VLSmartPagerAttributeSetterFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 656/1768: VLSizeRangeProvider.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 657/1768: VLScrollViewWidget.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 658/1768: VLScrollViewWidget+JS.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 659/1768: VLScrollViewComponent.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 660/1768: VLScrollViewAttributeSetterFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 661/1768: VLRootView.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 662/1768: VLRootComponentController.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 663/1768: VLListWidget.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 664/1768: VLListWidget+JS.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 665/1768: VLListViewAdapter.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 666/1768: VLListView.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 667/1768: VLListFlexibleSizeRangeProvider.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 668/1768: VLListComponentAttributes.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 669/1768: VLListComponent.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 670/1768: VLListCKComponentFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 671/1768: VLListAttributeSetterFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 672/1768: VLLeafNodeComponent.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 673/1768: VLLayer.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 674/1768: VLJSDebugSessionManager.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 675/1768: VLInputWidget.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 676/1768: VLInputView.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 677/1768: VLInputComponentAttributes.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 678/1768: VLInputAttributeSetterFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 679/1768: VLImageWidget.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 680/1768: VLImageComponent.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 681/1768: VLImageAttributeSetterFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 682/1768: VLHTMLTagEnumHandler.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 683/1768: VLHTMLAttributedStringFactory.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 684/1768: VLFlexboxComponent.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 685/1768: VLFBUtils.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 686/1768: VLFBNode.mm
2025-07-09 02:18:12,287 - pipeline.steps.ast_step_all - INFO - 处理编译文件 687/1768: VLFBCssRules.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 688/1768: VLFBCssRule.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 689/1768: VLFBCssMedia.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 690/1768: VLFBCssKeyFrames.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 691/1768: VLFBCssKeyFrame.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 692/1768: VLFBCss.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 693/1768: VLFBCard.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 694/1768: VLExpressionExtractor.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 695/1768: VLEditTextWidget.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 696/1768: VLEditTextView.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 697/1768: VLEditTextComponentAttributes.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 698/1768: VLEditTextAttributeSetterFactory.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 699/1768: VLCustomWidget.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 700/1768: VLCustomComponent.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 701/1768: VLComponentYogaAttributeSetterFuncs.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 702/1768: VLComponentHostingView.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 703/1768: VLComponentAttributes.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 704/1768: VLComponentAttributeSetterFuncs.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 705/1768: VLCellWidget.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 706/1768: VLCellComponentAttributes.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 707/1768: VLCellAttributeSetterFactory.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 708/1768: VLCardViewV1.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 709/1768: VLCardViewDataProvider.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 710/1768: VLCardV1.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 711/1768: VLCardSizeInfo.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 712/1768: VLCardConfig.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 713/1768: VLBundleConfig.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 714/1768: VLBaseWidget.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 715/1768: VLBaseWidget+VLComponentTreeBuilder.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 716/1768: VLBaseWidget+JS.mm
2025-07-09 02:18:12,288 - pipeline.steps.ast_step_all - INFO - 处理编译文件 717/1768: VLBaseComponent.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 718/1768: VLAdaptiveFlowWidget.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 719/1768: VLAdaptiveFlowLayoutItem.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 720/1768: VLAdaptiveFlowComponentAttributes.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 721/1768: VLAdaptiveFlowComponent.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 722/1768: VLAdaptiveFlowAttributeSetterFactory.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 723/1768: UIView+TDFView.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 724/1768: UIView+TDFAccessibilty.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 725/1768: UIView+QMVLAddition.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 726/1768: UIView+QMCKAddition.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 727/1768: TDFYogaLayoutAttributes.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 728/1768: TDFViewPagerNode.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 729/1768: TDFViewPagerNode+JS.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 730/1768: TDFViewPagerNode+Feature.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 731/1768: TDFViewPagerNode+CKTree.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 732/1768: TDFViewPagerFeature.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 733/1768: TDFViewPagerDataSource.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 734/1768: TDFViewPagerComponent.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 735/1768: TDFViewPagerAdpater.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 736/1768: TDFViewNode.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 737/1768: TDFViewNode+VisualReport.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 738/1768: TDFViewNode+TouchEvent.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 739/1768: TDFViewNode+JS.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 740/1768: TDFViewNode+Debug.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 741/1768: TDFViewNode+CKTree.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 742/1768: TDFVariableDeclaractor.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 743/1768: TDFVariableDeclaraction.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 744/1768: TDFVarValue.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 745/1768: TDFVarValue+MediaMatch.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 746/1768: TDFVRReportInfo.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 747/1768: TDFUpdateExpression.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 748/1768: TDFUnknownValue.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 749/1768: TDFUnaryExpression.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 750/1768: TDFTransitionAttributeValue.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 751/1768: TDFTransformOriginAttributeValue.mm
2025-07-09 02:18:12,289 - pipeline.steps.ast_step_all - INFO - 处理编译文件 752/1768: TDFTransformAttributes.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 753/1768: TDFTransformAttributeValue.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 754/1768: TDFTransformApplier.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 755/1768: TDFTimeAttributeValue.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 756/1768: TDFTileViewNode.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 757/1768: TDFTileViewFeature.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 758/1768: TDFTextStyle.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 759/1768: TDFTextSizeCache.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 760/1768: TDFTextNode.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 761/1768: TDFTextNode+Debug.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 762/1768: TDFTextNode+CKTree.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 763/1768: TDFTextKitRenderer.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 764/1768: TDFTextDecoration.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 765/1768: TDFTextAttributes.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 766/1768: TDFTextAsyncLayer.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 767/1768: TDFSurfaceImageNode.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 768/1768: TDFSurfaceImageNode+JS.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 769/1768: TDFStyleSheet.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 770/1768: TDFStyleSheet+MediaMatch.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 771/1768: TDFStyleSheet+CSSMatch.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 772/1768: TDFStringAttributeValue.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 773/1768: TDFStateUpdater.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 774/1768: TDFSlotNode+CKTree.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 775/1768: TDFShadowAttributes.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 776/1768: TDFShadowAttributeValue.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 777/1768: TDFScrollNode.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 778/1768: TDFScrollNode+JS.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 779/1768: TDFRuleContainer.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 780/1768: TDFRootView.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 781/1768: TDFReportInfo.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 782/1768: TDFReportAttributes.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 783/1768: TDFProgram.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 784/1768: TDFPosition.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 785/1768: TDFPointValue.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 786/1768: TDFNumberValue.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 787/1768: TDFNodeInfo+FB.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 788/1768: TDFNode+FB.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 789/1768: TDFNode+Debug.mm
2025-07-09 02:18:12,290 - pipeline.steps.ast_step_all - INFO - 处理编译文件 790/1768: TDFNode+CKTree.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 791/1768: TDFNativeProxyNode.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 792/1768: TDFNativeProxyNode+CKTree.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 793/1768: TDFNativeComponentController.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 794/1768: TDFMemberExpression.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 795/1768: TDFLogicalExpression.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 796/1768: TDFLiteral.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 797/1768: TDFListViewAdapter.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 798/1768: TDFListView.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 799/1768: TDFListView+ScrollDelegate.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 800/1768: TDFListView+DelegateFlowLayout.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 801/1768: TDFListView+CKDataSourceListener.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 802/1768: TDFListNodeDataSource.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 803/1768: TDFListNode.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 804/1768: TDFListNode+JS.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 805/1768: TDFListNode+Feature.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 806/1768: TDFListNode+CKTree.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 807/1768: TDFListFeature.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 808/1768: TDFLengthUnitValue.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 809/1768: TDFLength.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 810/1768: TDFLabel.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 811/1768: TDFLabel+TDF.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 812/1768: TDFKeyExpression.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 813/1768: TDFKTQuickJS.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 814/1768: TDFJSContextEnvironment.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 815/1768: TDFInsetsValue.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 816/1768: TDFImageTransform.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 817/1768: TDFImageNode.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 818/1768: TDFImageNode+CKTree.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 819/1768: TDFImageInfo.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 820/1768: TDFImageComponentController.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 821/1768: TDFImageAttributes.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 822/1768: TDFIfStatement.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 823/1768: TDFIfNode.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 824/1768: TDFIfNode+Debug.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 825/1768: TDFIfNode+CKTree.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 826/1768: TDFIdentifier.mm
2025-07-09 02:18:12,291 - pipeline.steps.ast_step_all - INFO - 处理编译文件 827/1768: TDFHeaderNode.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 828/1768: TDFHeaderFeature.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 829/1768: TDFHTMLStringFactory.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 830/1768: TDFGradient.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 831/1768: TDFFractionValue.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 832/1768: TDFForNode.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 833/1768: TDFForNode+Debug.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 834/1768: TDFForNode+CKTree.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 835/1768: TDFFooterNode.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 836/1768: TDFFooterFeature.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 837/1768: TDFFlexChild.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 838/1768: TDFFlexBox.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 839/1768: TDFExpressionUtil.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 840/1768: TDFExpressionStatement.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 841/1768: TDFConstantComboValue.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 842/1768: TDFConditionalExpression.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 843/1768: TDFComponentNode.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 844/1768: TDFComponentNode+CKTree.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 845/1768: TDFComponentFile.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 846/1768: TDFComponentController.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 847/1768: TDFComponentAttributes.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 848/1768: TDFComponentAttributes+Debug.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 849/1768: TDFCompiledStyle.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 850/1768: TDFCompiledOtherStyle.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 851/1768: TDFCommonAttributesParser.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 852/1768: TDFColorValue.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 853/1768: TDFCellNode.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 854/1768: TDFCellFeature.mm
2025-07-09 02:18:12,292 - pipeline.steps.ast_step_all - INFO - 处理编译文件 855/1768: TDFCarouselView.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 856/1768: TDFCardView.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 857/1768: TDFCardProvider.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 858/1768: TDFCard.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 859/1768: TDFCard+Debug.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 860/1768: TDFCallExpression.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 861/1768: TDFCalcValue.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 862/1768: TDFCSSStruct.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 863/1768: TDFCSSSelector.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 864/1768: TDFCSSRule.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 865/1768: TDFCSSMediaRule.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 866/1768: TDFCSSKeyFrames.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 867/1768: TDFCSSKeyFrame.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 868/1768: TDFCSSAnimation.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 869/1768: TDFCKViewComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 870/1768: TDFCKTreeProvider.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 871/1768: TDFCKTileComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 872/1768: TDFCKTextComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 873/1768: TDFCKScrollComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 874/1768: TDFCKNativeComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 875/1768: TDFCKListComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 876/1768: TDFCKImageComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 877/1768: TDFCKFlexboxComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 878/1768: TDFCKComponentLife.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 879/1768: TDFCKComponent.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 880/1768: TDFBundle.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 881/1768: TDFBoxConstraints.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 882/1768: TDFBoxComponentController.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 883/1768: TDFBorderInfoValue.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 884/1768: TDFBorderAttributes.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 885/1768: TDFBlockStatement.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 886/1768: TDFBinaryExpression.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 887/1768: TDFBackgroundStretchParamValue.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 888/1768: TDFBackgroundImageValue.mm
2025-07-09 02:18:12,293 - pipeline.steps.ast_step_all - INFO - 处理编译文件 889/1768: TDFBackgroundImageAttributes.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 890/1768: TDFAttributesSetterFactory.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 891/1768: TDFAttributesSetter.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 892/1768: TDFAttributeValue.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 893/1768: TDFAttributeTextFactory.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 894/1768: TDFAssignmentExpression.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 895/1768: TDFAppearanceAttributes.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 896/1768: TDFAppearance.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 897/1768: TDFAnimationAttributes.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 898/1768: NSString+VectorLayout.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 899/1768: CKComponent+WidgetSetter.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 900/1768: CKComponent+TDFNodeSetter.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 901/1768: CKComponent+QMVLAddition.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 902/1768: CKComponent+Debug.mm
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 903/1768: VectorLayout-dummy.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 904/1768: VLWidgetUtil.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 905/1768: VLWatchData.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 906/1768: VLVirtualRichNode.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 907/1768: VLViewRichNode.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 908/1768: VLViewPagerRichNode.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 909/1768: VLViewPagerCell.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 910/1768: VLViewPagerAdpater.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 911/1768: VLView.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 912/1768: VLVideoWidgetAdpater.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 913/1768: VLVideoWidget+JS.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 914/1768: VLVideoViewContext.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 915/1768: VLVideoView.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 916/1768: VLVideoRichNode.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 917/1768: VLVideoCssValue.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 918/1768: VLVideoConfigData.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 919/1768: VLValueMediaCondition.m
2025-07-09 02:18:12,294 - pipeline.steps.ast_step_all - INFO - 处理编译文件 920/1768: VLUtils.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 921/1768: VLUnscrollableCarousel.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 922/1768: VLUnitConverter.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 923/1768: VLUndefinedRichNode.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 924/1768: VLTracers.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 925/1768: VLTracer.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 926/1768: VLTraceReport.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 927/1768: VLTimerSource.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 928/1768: VLTextRichNode.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 929/1768: VLTextLineHeightFix.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 930/1768: VLTextLayoutManager.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 931/1768: VLTextAttributesStringKeys.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 932/1768: VLTaskQueue.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 933/1768: VLStringCssValue.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 934/1768: VLStorageItem.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 935/1768: VLStorage.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 936/1768: VLSourceBundle.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 937/1768: VLSmartPagerRichNode.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 938/1768: VLSmartPagerCell.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 939/1768: VLSmartPagerAdpater.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 940/1768: VLSmartCarouselView.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 941/1768: VLSlotVirtualRichNode.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 942/1768: VLShadowData.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 943/1768: VLScrollViewRichNode.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 944/1768: VLScrollViewAdpater.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 945/1768: VLScrollView.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 946/1768: VLScreenScale.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 947/1768: VLSceneCache.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 948/1768: VLSampleUtils.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 949/1768: VLRoundedRect.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 950/1768: VLRichCssWildcardSelector.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 951/1768: VLRichCssSelectorType.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 952/1768: VLRichCssSelector.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 953/1768: VLRichCssRule.m
2025-07-09 02:18:12,295 - pipeline.steps.ast_step_all - INFO - 处理编译文件 954/1768: VLRichCssRootSelector.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 955/1768: VLRichCssParserFuncs.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 956/1768: VLRichCssParserFactory.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 957/1768: VLRichCssParserDefine.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 958/1768: VLRichCssParser.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 959/1768: VLRichCssMultipleSelector.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 960/1768: VLRichCssMediaRules.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 961/1768: VLRichCssMediaInfo.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 962/1768: VLRichCssMatchItem.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 963/1768: VLRichCssData.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 964/1768: VLRichCssCascadingSelector.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 965/1768: VLRichCssAttrsParserInitModel.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 966/1768: VLRichCssAttrsParser.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 967/1768: VLRichCssAttrs.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 968/1768: VLRichCssAttrDirectionParser.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 969/1768: VLRichCssAnimationTransformParser.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 970/1768: VLRichCssAliasParser.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 971/1768: VLRichCss.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 972/1768: VLRequestManager.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 973/1768: VLReportInfo.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 974/1768: VLRenderRichNode.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 975/1768: VLRangeMediaCondition.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 976/1768: VLQuickJSValue.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 977/1768: VLQuickJSRuntime.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 978/1768: VLQuickJSContext.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 979/1768: VLQuickJSConfiguration.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 980/1768: VLQuickJSCommonMacro.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 981/1768: VLPseudoStatus.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 982/1768: VLPropertyValue.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 983/1768: VLPrivatePageNodeInfo.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 984/1768: VLPointTypeString.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 985/1768: VLPlayerUtils.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 986/1768: VLPlayerSliderView.m
2025-07-09 02:18:12,296 - pipeline.steps.ast_step_all - INFO - 处理编译文件 987/1768: VLPlayerSliderPointInfo.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 988/1768: VLPlayerSliderPoint.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 989/1768: VLPlayerPosterView.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 990/1768: VLPlayerGestureView.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 991/1768: VLPlayerDynamicButton.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 992/1768: VLPlayerControlView.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 993/1768: VLPlayerBottomView.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 994/1768: VLPair.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 995/1768: VLOnceSpcificCache.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 996/1768: VLNodeProperties.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 997/1768: VLNodeFactoryUtil.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 998/1768: VLNodeFactory.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 999/1768: VLNativeWidgetBridge.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1000/1768: VLNativeWidget.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1001/1768: VLNativeDataWrapper.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1002/1768: VLModuleBridge.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1003/1768: VLMediaPlayer.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1004/1768: VLMediaManagerItem.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1005/1768: VLMediaManager.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1006/1768: VLMediaCondition.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1007/1768: VLMJRefreshStateHeader.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1008/1768: VLMJRefreshNormalHeader.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1009/1768: VLMJRefreshHeader.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1010/1768: VLMJRefreshFooter.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1011/1768: VLMJRefreshConst.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1012/1768: VLMJRefreshComponent.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1013/1768: VLMJRefreshBackStateFooter.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1014/1768: VLMJRefreshBackNormalFooter.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1015/1768: VLMJRefreshBackFooter.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1016/1768: VLListViewCKDataParams.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1017/1768: VLListRichNode.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1018/1768: VLListHeaderWidget.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1019/1768: VLListHeaderRichNode.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1020/1768: VLListFooterWidget.m
2025-07-09 02:18:12,297 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1021/1768: VLListFooterRichNode.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1022/1768: VLListCustomRefreshHeader.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1023/1768: VLListCustomRefreshFooter.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1024/1768: VLLengthCssValue.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1025/1768: VLLayoutStair.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1026/1768: VLKeyPath.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1027/1768: VLKeyPathElement.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1028/1768: VLJSONTraversalUtil.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1029/1768: VLJSMoudleManager.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1030/1768: VLJSMethodCall.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1031/1768: VLJSFunctionDefine.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1032/1768: VLJSExternObjProxy.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1033/1768: VLJSEngineManager.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1034/1768: VLJSDebugConfig.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1035/1768: VLJSDataModule.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1036/1768: VLIntegerCssValue.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1037/1768: VLInputRichNode.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1038/1768: VLInnerLayer.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1039/1768: VLImageStrechCssValue.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1040/1768: VLImageRichNode.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1041/1768: VLImageAttributes.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1042/1768: VLIfRichNode.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1043/1768: VLIfNodePartition.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1044/1768: VLHTMLNode.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1045/1768: VLGradientLayer.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1046/1768: VLFundationUtils.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1047/1768: VLFractionCssValue.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1048/1768: VLFraction.m
2025-07-09 02:18:12,298 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1049/1768: VLForRichNode.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1050/1768: VLForContext.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1051/1768: VLFloatCssValue.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1052/1768: VLFilterCssValue.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1053/1768: VLExpressionItem.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1054/1768: VLExpressionEvaluator.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1055/1768: VLEventBusSubcriberManager.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1056/1768: VLEventBusSubscriber.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1057/1768: VLEventBusModule.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1058/1768: VLEventBus.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1059/1768: VLEnumMapTabs.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1060/1768: VLElementReportPolicy.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1061/1768: VLEditTextRichNode.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1062/1768: VLEXTSelectorChecking.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1063/1768: VLEXTScope.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1064/1768: VLDomModule.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1065/1768: VLDom.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1066/1768: VLDocumentManager.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1067/1768: VLDefaultImageView.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1068/1768: VLDataSource.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1069/1768: VLDataMutation.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1070/1768: VLCustomWidget+JS.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1071/1768: VLCssValue.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1072/1768: VLCssDefine.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1073/1768: VLCssContext.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1074/1768: VLCssAnimationKeyFrames.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1075/1768: VLCssAnimationKeyFrame.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1076/1768: VLConvertReactiveToNormalData.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1077/1768: VLContextInitInfo.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1078/1768: VLContext.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1079/1768: VLComponentWidget.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1080/1768: VLComponentRichNode.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1081/1768: VLComponentPathResolver.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1082/1768: VLComponentNodeFactory.m
2025-07-09 02:18:12,299 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1083/1768: VLComponentDataSource.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1084/1768: VLComponentCssContext.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1085/1768: VLComponentAnimator.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1086/1768: VLComponentAnimator+QMAddition.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1087/1768: VLCommonUtilModule.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1088/1768: VLCommonRichNode.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1089/1768: VLCommonNodePartition.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1090/1768: VLCommonDataSource.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1091/1768: VLCommonAttributeSetterFactory.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1092/1768: VLCollectionViewLeftAlignedLayout.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1093/1768: VLCollectionViewBaseLayout.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1094/1768: VLCharQueue.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1095/1768: VLCellRichNode.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1096/1768: VLCarouselView.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1097/1768: VLCarousel.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1098/1768: VLCardViewV1+AutoReport.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1099/1768: VLCardView.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1100/1768: VLCardUpdateData.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1101/1768: VLCardRenderData.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1102/1768: VLCardInitInfo.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1103/1768: VLCardConfigurations.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1104/1768: VLCard.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1105/1768: VLCalcInterpreter.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1106/1768: VLCalcExpressionEvaluator.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1107/1768: VLBundleV1.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1108/1768: VLCHTCollectionViewWaterfallLayout.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1109/1768: VLBundleModule.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1110/1768: VLBundleLanguageUtils.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1111/1768: VLBundle.m
2025-07-09 02:18:12,300 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1112/1768: VLBorderShadowCssValue.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1113/1768: VLBorderDrawing.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1114/1768: VLBorderData.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1115/1768: VLBoolCssValue.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1116/1768: VLBaseWidget+Event.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1117/1768: VLBaseRichNode.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1118/1768: VLBaseRichNode+LifeCircle.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1119/1768: VLBaseAttributeSetterFactory.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1120/1768: VLBackgroundImageData.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1121/1768: VLBackgroundImageCssValue.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1122/1768: VLAppJSObject.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1123/1768: VLAnimInterpolateCssValue.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1124/1768: VLAdaptiveLayoutDataInternal.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1125/1768: VLAdaptiveFlowSectionLayoutData.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1126/1768: VLAdaptiveFlowRichNode.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1127/1768: VLAdaptiveFlowLayoutView.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1128/1768: UIView+VLVRReport.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1129/1768: UIView+VLUtils.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1130/1768: UIView+VLShortCut.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1131/1768: UIView+VLMJExtension.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1132/1768: UIView+TDFText.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1133/1768: UIView+QMEOVLExtend.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1134/1768: UIScrollView+VLMJRefresh.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1135/1768: UIScrollView+VLMJExtension.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1136/1768: UIImage+VLAdditions.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1137/1768: UIBezierPath+VL.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1138/1768: TDFWeakProxy.m
2025-07-09 02:18:12,301 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1139/1768: TDFViewNode+DynamicProperties.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1140/1768: TDFViewNode+Debug.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1141/1768: TDFViewNode+CSSNode.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1142/1768: TDFViewGestureHelper.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1143/1768: TDFView.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1144/1768: TDFValueConverter.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1145/1768: TDFTreeTraversal.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1146/1768: TDFTreeHouse.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1147/1768: TDFTextUtilities.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1148/1768: TDFTextNodeEventProxy.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1149/1768: TDFTextLine.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1150/1768: TDFTextLayout.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1151/1768: TDFTextInput.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1152/1768: TDFTextDebugOption.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1153/1768: TDFTextAttribute.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1154/1768: TDFTaskQueue.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1155/1768: TDFSwitchGrammar.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1156/1768: TDFSurfaceView.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1157/1768: TDFSurfaceImageAdapter.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1158/1768: TDFSlotNode.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1159/1768: TDFSlotGrammer.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1160/1768: TDFSignPost.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1161/1768: TDFScrollViewAdpater.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1162/1768: TDFRequestManager.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1163/1768: TDFReactiveAttributes.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1164/1768: TDFQuickJSRuntime.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1165/1768: TDFQJSBase.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1166/1768: TDFPropertyValue.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1167/1768: TDFNodeInfo.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1168/1768: TDFNodeContext.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1169/1768: TDFNode.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1170/1768: TDFNode+QMAddition.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1171/1768: TDFNode+LifeCircle.m
2025-07-09 02:18:12,302 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1172/1768: TDFNode+JS.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1173/1768: TDFNode+Debug.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1174/1768: TDFNode+Debug.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1175/1768: TDFNativeNodeBridge.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1176/1768: TDFNativeDataWrapper.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1177/1768: TDFMediaRuleAOTAlgorithm.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1178/1768: TDFLRUCache.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1179/1768: TDFJSValue.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1180/1768: TDFJSInterpreter.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1181/1768: TDFJSDataModule.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1182/1768: TDFJSContext.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1183/1768: TDFJSCFunctions.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1184/1768: TDFImageTransformer.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1185/1768: TDFIfGrammer.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1186/1768: TDFGeometry.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1187/1768: TDFForScope.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1188/1768: TDFForRef.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1189/1768: TDFForListAbstractNode.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1190/1768: TDFForGrammer.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1191/1768: TDFDomEnvironment.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1192/1768: TDFDefaultImageView.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1193/1768: TDFDataSource.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1194/1768: TDFComponentNode+Debug.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1195/1768: TDFCompiledStyle+Debug.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1196/1768: TDFCollectionViewFlowLayout.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1197/1768: TDFCardView+VisualReport.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1198/1768: TDFCard+QMAddition.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1199/1768: TDFBundle+Lock.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1200/1768: TDFBaseView.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1201/1768: TDFBaseJSContext.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1202/1768: TDFAttrituesUtility.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1203/1768: TDFAttributeValueUtils.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1204/1768: TDFAttributeValue+UnPack.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1205/1768: TDFAttributeValue+Private.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1206/1768: TDFAnimationAttributes+QMAddition.m
2025-07-09 02:18:12,303 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1207/1768: TDFAccessiblityConvert.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1208/1768: QMVideoPlayer.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1209/1768: QMVLVideoWidget.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1210/1768: QMVLVideoView.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1211/1768: QMVLPagWidget.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1212/1768: QMVLPagView.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1213/1768: QMVLJSCallBackCommand.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1214/1768: QMVLCardView.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1215/1768: QMVLBundlerScriptInterfaceHandler.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1216/1768: QMVLBundleScriptModuleInterfaceHandler.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1217/1768: QMVLBundleManager.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1218/1768: QMVLBaseNativeWidget.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1219/1768: QMGestureMonitorViewWidget.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1220/1768: QMGestureMonitorView.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1221/1768: NSTimer+VLUtils.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1222/1768: NSString+VLStringUtil.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1223/1768: NSString+VLJSON.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1224/1768: NSString+TDFCSS.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1225/1768: NSString+LengthPointType.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1226/1768: NSString+HTMLString.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1227/1768: NSParagraphStyle+TDFText.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1228/1768: NSObject+toJSValueStruct.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1229/1768: NSObject+VLJSONMutation.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1230/1768: NSObject+VLCore.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1231/1768: NSObject+JSONString.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1232/1768: NSMutableDictionary+VLThreadSafe.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1233/1768: NSData+VLJSON.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1234/1768: NSAttributedString+TDFText.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1235/1768: NSAttributedString+TDF.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1236/1768: NSArray+VLJSON.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1237/1768: CALayer+VLAnimationSupport.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1238/1768: TMLarkLiteDynamic.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1239/1768: TMDylibLazyLoadWrapper-dummy.m
2025-07-09 02:18:12,304 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1240/1768: TMDylibLazyLoadUtil.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1241/1768: TMDylibLazyLoadUtil+Log.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1242/1768: TMARNaviDynamic.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1243/1768: TMAISDKDynamic.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1244/1768: ResultSocket.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1245/1768: QQMusicUtils.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1246/1768: QPlayAutoSDK.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1247/1768: QPlayAutoSDK-dummy.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1248/1768: QPlayAutoManager.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1249/1768: QPlayAutoEntity.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1250/1768: QMRSA.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1251/1768: QMNetworkHelper.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1252/1768: HeartbeatSocket.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1253/1768: DiscoverSocket.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1254/1768: DataSocket.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1255/1768: CommandSocket.m
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1256/1768: SizingComponentBuilder.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1257/1768: RatioLayoutComponentBuilder.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1258/1768: RCDimension_SwiftBridge.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1259/1768: RCComponentSize_SwiftBridge.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1260/1768: FlexboxComponentBuilder.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1261/1768: CompositeComponentBuilder.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1262/1768: ComponentLayoutContext.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1263/1768: ComponentKit+QuickLook.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1264/1768: ComponentBuilder.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1265/1768: CKZStackComponent.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1266/1768: CKWritingDirection.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1267/1768: CKTrigger.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1268/1768: CKTreeVerificationHelpers.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1269/1768: CKTreeNode.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1270/1768: CKTransitions.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1271/1768: CKTransitionComponent.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1272/1768: CKTraitCollectionHelper.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1273/1768: CKThreadLocalComponentScope.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1274/1768: CKTextKitTailTruncater.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1275/1768: CKTextKitShadower.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1276/1768: CKTextKitRendererCache.mm
2025-07-09 02:18:12,305 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1277/1768: CKTextKitRenderer.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1278/1768: CKTextKitRenderer+TextChecking.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1279/1768: CKTextKitRenderer+Positioning.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1280/1768: CKTextKitEntityAttribute.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1281/1768: CKTextKitContext.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1282/1768: CKTextKitAttributes.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1283/1768: CKTextComponentViewControlTracker.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1284/1768: CKTextComponentView.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1285/1768: CKTextComponentLayerHighlighter.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1286/1768: CKTextComponentLayer.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1287/1768: CKTextComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1288/1768: CKSystraceScope.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1289/1768: CKSwiftComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1290/1768: CKStaticLayoutComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1291/1768: CKStatelessComponentContext.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1292/1768: CKStatelessComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1293/1768: CKStatefulViewReusePool.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1294/1768: CKStatefulViewComponentController.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1295/1768: CKStatefulViewComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1296/1768: CKSizingComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1297/1768: CKSizeRange_SwiftBridge.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1298/1768: CKRootTreeNode.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1299/1768: CKRenderHelpers.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1300/1768: CKRenderComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1301/1768: CKRatioLayoutComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1302/1768: CKOverlayLayoutComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1303/1768: CKOptimisticViewMutations.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1304/1768: CKNetworkImageComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1305/1768: CKLayoutComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1306/1768: CKLabelComponent.mm
2025-07-09 02:18:12,306 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1307/1768: CKInvalidChangesetOperationType.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1308/1768: CKInsetComponent.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1309/1768: CKIndexTransform.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1310/1768: CKIndexSetDescription.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1311/1768: CKImageComponent.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1312/1768: CKIdValueWrapperInternal.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1313/1768: CKHighlightOverlayLayer.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1314/1768: CKFlexboxComponent.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1315/1768: CKEmptyComponent.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1316/1768: CKDataSourceUpdateStateModification.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1317/1768: CKDataSourceUpdateConfigurationModification.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1318/1768: CKDataSourceState.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1319/1768: CKDataSourceSplitChangesetModification.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1320/1768: CKDataSourceReloadModification.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1321/1768: CKDataSourceQOSHelper.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1322/1768: CKDataSourceModificationHelper.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1323/1768: CKDataSourceListenerAnnouncer.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1324/1768: CKDataSourceItem.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1325/1768: CKDataSourceConfiguration.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1326/1768: CKDataSourceChangesetVerification.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1327/1768: CKDataSourceChangesetModification.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1328/1768: CKDataSourceChangesetApplicator.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1329/1768: CKDataSourceChangeset.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1330/1768: CKDataSourceChange.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1331/1768: CKDataSourceAppliedChanges.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1332/1768: CKDataSource.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1333/1768: CKCompositeComponent.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1334/1768: CKComponentViewConfiguration_SwiftBridge.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1335/1768: CKComponentViewAttribute_SwiftBridge.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1336/1768: CKComponentTreeDiff.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1337/1768: CKComponentScopeRootFactory.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1338/1768: CKComponentScopeRoot.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1339/1768: CKComponentScopeHandle.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1340/1768: CKComponentScope.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1341/1768: CKComponentRootView.mm
2025-07-09 02:18:12,307 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1342/1768: CKComponentPerfScope.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1343/1768: CKComponentLayoutBaseline.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1344/1768: CKComponentLayout.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1345/1768: CKComponentKey.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1346/1768: CKComponentHostingView.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1347/1768: CKComponentHostingContainerViewProvider.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1348/1768: CKComponentHierarchyDebugHelper.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1349/1768: CKComponentGestureActions.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1350/1768: CKComponentGestureActionHelper.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1351/1768: CKComponentGenerator.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1352/1768: CKComponentFlexibleSizeRangeProvider.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1353/1768: CKComponentEvents.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1354/1768: CKComponentDelegateForwarder.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1355/1768: CKComponentDelegateAttribute.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1356/1768: CKComponentDebugController.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1357/1768: CKComponentCreationValidation.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1358/1768: CKComponentControllerHelper.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1359/1768: CKComponentControllerEvents.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1360/1768: CKComponentController.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1361/1768: CKComponentContextHelper.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1362/1768: CKComponentBoundsAnimation.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1363/1768: CKComponentBoundsAnimation+UICollectionView.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1364/1768: CKComponentAttachController.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1365/1768: CKComponentAnnouncerHelper.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1366/1768: CKComponentAnnouncerBase.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1367/1768: CKComponentAnimationsController.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1368/1768: CKComponentAnimations.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1369/1768: CKComponentAnimationPredicates.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1370/1768: CKComponentAnimation.mm
2025-07-09 02:18:12,308 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1371/1768: CKComponentAccessibility.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1372/1768: CKComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1373/1768: CKComponent+Yoga.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1374/1768: CKComponent+UIView.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1375/1768: CKCollectionViewDataSourceListenerAnnouncer.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1376/1768: CKCollectionViewDataSourceCell.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1377/1768: CKCollectionViewDataSource.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1378/1768: CKClippingComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1379/1768: CKCenterLayoutComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1380/1768: CKCellDeallocUnmounter.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1381/1768: CKButtonComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1382/1768: CKBuildComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1383/1768: CKBlockSizeRangeProvider.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1384/1768: CKBackgroundLayoutComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1385/1768: CKAutoSizedImageComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1386/1768: CKAsyncTransactionGroup.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1387/1768: CKAsyncTransactionContainer.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1388/1768: CKAsyncTransaction.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1389/1768: CKAsyncLayer.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1390/1768: CKAsyncBlock.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1391/1768: CKAnimationComponentPassthroughView.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1392/1768: CKAnimationComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1393/1768: CKAnimation.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1394/1768: CKAnalyticsListenerHelpers.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1395/1768: CKAction.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1396/1768: CKAccessibilityContainerComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1397/1768: CKAccessibilityAwareComponent.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1398/1768: CKAccessibilityAggregation.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1399/1768: AutoSizedImageComponentBuilder.mm
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1400/1768: TMWeAppSDKClient.m
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1401/1768: TMWeAppSDKClient+Log.m
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1402/1768: TMWeAppClient-dummy.m
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1403/1768: ComponentKit-dummy.m
2025-07-09 02:18:12,309 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1404/1768: TMAISound.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1405/1768: TMAISound-dummy.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1406/1768: TMAISound+Log.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1407/1768: UIDevice+SPMExtension.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1408/1768: QMSpiderManWrapper-dummy.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1409/1768: QMSPMReportMananger.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1410/1768: QMSPMPackageModel.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1411/1768: QMSPMMonitor.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1412/1768: QMSPMModuleManager.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1413/1768: QMSPMLogHelper.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1414/1768: QMSPMError.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1415/1768: QMSPMEngine.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1416/1768: QMSpiderManDebugTool-dummy.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1417/1768: QMSPMTopNavView.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1418/1768: QMSPMRuntimeLoadedClassViewController.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1419/1768: QMSPMRuntimeLoadedClassMethodsViewController.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1420/1768: QMSPMMaskView.m
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1421/1768: MBIZTurnArrowStyleOption.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1422/1768: MBIZTurnArrowStyleOptionOCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1423/1768: MBIZTrafficLightStatus+OCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1424/1768: MBIZRouteCustomDrawInfoOCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1425/1768: MBIZRouteCustomDrawInfo.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1426/1768: MBIZRouteCallBackAdapter.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1427/1768: MBIZResourceCallBackAdapter.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1428/1768: MBIZOverLookParam.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1429/1768: MBIZOverLookParam+OCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1430/1768: MBIZMarkerNameStyleOptionOCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1431/1768: MBIZMarkerNameStyleOption.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1432/1768: MBIZMapTileMarkerDetailOCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1433/1768: MBIZMapTileLayerOCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1434/1768: MBIZMapTileLayer.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1435/1768: MBIZMapScaleConfigContainerOCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1436/1768: MBIZMapScaleConfigContainer.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1437/1768: MBIZManager.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1438/1768: MBIZMagicCameraStatus.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1439/1768: MBIZMagicCameraStatus+OCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1440/1768: MBIZMagicCameraData+OCCPP.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1441/1768: MBIZLocationFollowConfig.mm
2025-07-09 02:18:12,310 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1442/1768: MBIZLocationFollowConfig+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1443/1768: MBIZLayerBaseConfigOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1444/1768: MBIZLayerBaseConfig.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1445/1768: MBIZInitResourceCopyTaskAdapter.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1446/1768: MBIZHDScaleController.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1447/1768: MBIZHDScaleController+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1448/1768: MBIZHDScaleConfigs.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1449/1768: MBIZHDScaleConfigs+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1450/1768: MBIZHDControllerOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1451/1768: MBIZHDController.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1452/1768: MBIZHDAutoScaleConfigContainer.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1453/1768: MBIZHDAutoScaleConfigContainer+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1454/1768: MBIZGuideAreaReq+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1455/1768: MBIZEventListenerAdapter.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1456/1768: MBIZDynamicFrameRateConfigOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1457/1768: MBIZDynamicFrameRateConfig.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1458/1768: MBIZCreatedResultInfo.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1459/1768: MBIZCreatedResultInfo+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1460/1768: MBIZCreateCallBackAdapter.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1461/1768: MBIZCommonDebugInfo+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1462/1768: MBIZColorRouteStyleOptionOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1463/1768: MBIZColorRouteStyleOptionExOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1464/1768: MBIZColorRouteStyleOptionEx.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1465/1768: MBIZColorRouteStyleOption.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1466/1768: MBIZCenterLineDataOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1467/1768: MBIZBubbleLayerConfigs.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1468/1768: MBIZBubbleLayerConfigs+OCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1469/1768: MBIZBubbleLayerConfigContainerOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1470/1768: MBIZBubbleLayerConfigContainer.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1471/1768: MBIZBubbleDrawDescriptorOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1472/1768: MBIZBubbleCandidatePositionContainerOCCPP.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1473/1768: MBIZBubbleCandidatePositionContainer.mm
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1474/1768: MapBizOCMiddleware-dummy.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1475/1768: MBIZTrafficLightStatus.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1476/1768: MBIZOption.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1477/1768: MBIZMapTileMarkerDetail.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1478/1768: MBIZMapResourceContentDescriptor.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1479/1768: MBIZMapRectD.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1480/1768: MBIZMapRectD+OCCPP.m
2025-07-09 02:18:12,311 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1481/1768: MBIZMagicCameraData.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1482/1768: MBIZImageUtils.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1483/1768: MBIZGuideAreaReq.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1484/1768: MBIZGlobalConfig.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1485/1768: MBIZCommonDebugInfo.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1486/1768: MBIZCenterLineData.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1487/1768: MBIZBubbleDrawDescriptor.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1488/1768: QMExtraOrdinaryMap-dummy.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1489/1768: UIView+QMEOExtend.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1490/1768: QMEOWeakView.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1491/1768: QMEOViewOverlay+Animation.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1492/1768: QMEOViewCluster.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1493/1768: QMEOViewCanvas+Animation.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1494/1768: QMEOCommonTool.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1495/1768: QMEOClusterParam.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1496/1768: QMEOCanvasElementMaker.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1497/1768: QMEOAniTaskInfo.m
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1498/1768: view_canvas.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1499/1768: normal_2d_canvas.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1500/1768: QMEOViewOverlayManager.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1501/1768: QMEOViewOverlayManager+CppApi.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1502/1768: QMEOViewOverlay.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1503/1768: QMEOViewOverlay+CppApi.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1504/1768: QMEOViewInfoOCCPP.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1505/1768: QMEOViewInfo.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1506/1768: QMEOViewCanvasElement.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1507/1768: QMEOViewCanvas.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1508/1768: QMEOViewCanvas+Additions.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1509/1768: QMEOViewAniVideoCanvasElement.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1510/1768: QMEOViewAniPropCanvasElement.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1511/1768: QMEOViewAniPagCanvasElement.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1512/1768: QMEOViewAniCanvasElement.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1513/1768: QMEORenderEngine.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1514/1768: QMEOOverlayParamOCCPP.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1515/1768: QMEOOverlayParam.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1516/1768: QMEOOverlayAnimationBuilderInfoOCCPP.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1517/1768: QMEOOverlayAnimationBuilderInfo.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1518/1768: QMEOModelFileDescriptionOCCPP.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1519/1768: QMEOModelFileDescription.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1520/1768: QMEOModelDescriptionOCCPP.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1521/1768: QMEOModelDescription.mm
2025-07-09 02:18:12,312 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1522/1768: QMEOMapGlobalConfiger.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1523/1768: QMEOCustomOverlayPixelBufferManager.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1524/1768: QMEOCustomOverlay.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1525/1768: QMEOCustomCanvas.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1526/1768: QMEOConvertor.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1527/1768: QMEOClusterListener.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1528/1768: QMEOCanvasParamOCCPP.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1529/1768: QMEOCanvasParam.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1530/1768: QMEOCanvasElement.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1531/1768: QMEOAnimationAction.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1532/1768: QMEOAgileClusterListener.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1533/1768: QMEO3DOverlay.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1534/1768: QMEOAniComposition.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1535/1768: QMEO3DOverlayParam.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1536/1768: QMModelReportManager.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1537/1768: QMAgileAnimObserver.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1538/1768: DMMarkerUtils.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1539/1768: DMDataConvertor.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1540/1768: DMAgileMarkerLayoutTemplate.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1541/1768: DMAgileMarker.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1542/1768: DMAgileMapController.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1543/1768: DMAgileLayoutManager.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1544/1768: DMAgileClusterGroupLayout.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1545/1768: DMAgileClusterGroup.mm
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1546/1768: QMLocationManager+DMMap.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1547/1768: QMLandmarkerInfo.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1548/1768: QMLandmarkerDataManager.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1549/1768: QMEOMarkerCoverData.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1550/1768: NSValue+DMBasics.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1551/1768: NSMethodSignature+DMFFIType.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1552/1768: DragonMapKit-dummy.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1553/1768: DMTilePointLoadImp.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1554/1768: DMTileLayer.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1555/1768: DMTileLayer+Private.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1556/1768: DMTileDSLPresenter.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1557/1768: DMTextureRoute.m
2025-07-09 02:18:12,313 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1558/1768: DMTextAnnotationMarker.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1559/1768: DMSharedMapViewManager.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1560/1768: DMSharedMapViewHelper.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1561/1768: DMSharedMapView.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1562/1768: DMSharedMapView+Private.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1563/1768: DMSharedMapView+NaviMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1564/1768: DMSharedMapView+MapSkinMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1565/1768: DMSharedMapView+LocationMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1566/1768: DMSharedMapView+CustomTileMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1567/1768: DMSharedMapView+CommonMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1568/1768: DMSharedMapView+AnimationMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1569/1768: DMSharedMapSettingElement.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1570/1768: DMShadowSetting.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1571/1768: DMScaleUtil.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1572/1768: DMScaleLogoMiddleware.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1573/1768: DMRouteUtils.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1574/1768: DMRouteSection.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1575/1768: DMRouteScaleStyle.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1576/1768: DMRoutePoint.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1577/1768: DMRouteGroup.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1578/1768: DMRouteGradientInfo.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1579/1768: DMRouteElement.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1580/1768: DMRouteElement+MapBiz.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1581/1768: DMRouteElement+DMBaseRoutePoints.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1582/1768: DMRouteController.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1583/1768: DMRouteAdapter.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1584/1768: DMPrimitiveElement.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1585/1768: DMPrimitiveController.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1586/1768: DMPrimitiveAdapter.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1587/1768: DMPolyline.m
2025-07-09 02:18:12,314 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1588/1768: DMPolygon.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1589/1768: DMPerformanceMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1590/1768: DMParticleElement.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1591/1768: DMParticleController.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1592/1768: DMPagMarker.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1593/1768: DMOverlayElement.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1594/1768: DMOverlayController.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1595/1768: DMNaviMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1596/1768: DMMarkerLayer.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1597/1768: DMMarkerElement.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1598/1768: DMMarkerElement+Tile.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1599/1768: DMMarkerController.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1600/1768: DMMarkerAdapter.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1601/1768: DMMarkerAccessoryViewContainer.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1602/1768: DMMarkerAccessoryView.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1603/1768: DMMarker4KInfo.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1604/1768: DMMarkAnimateImageParam.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1605/1768: DMMapViewCleaner.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1606/1768: DMMapView.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1607/1768: DMMapView+Reset.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1608/1768: DMMapView+Private.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1609/1768: DMMapView+NaviMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1610/1768: DMMapView+MapSkinMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1611/1768: DMMapView+LocationMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1612/1768: DMMapView+DebugEnv.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1613/1768: DMMapView+CustomTileMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1614/1768: DMMapView+CommonMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1615/1768: DMMapView+AnimationMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1616/1768: DMMapThreadDispatchManager.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1617/1768: DMMapSkinMiddleware.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1618/1768: DMMapSkinDownLoader.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1619/1768: DMMapSkinController.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1620/1768: DMMapPOI.m
2025-07-09 02:18:12,315 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1621/1768: DMMapMethodRuntimeCall+Convert.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1622/1768: DMMapMethodParameterValue.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1623/1768: DMMapMethodParameterType.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1624/1768: DMMapMethodParameterObject.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1625/1768: DMMapMethodModuleRoot.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1626/1768: DMMapMethodModuleParser.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1627/1768: DMMapMethodModuleManager.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1628/1768: DMMapMethodContextStack.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1629/1768: DMMapMethodContext.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1630/1768: DMMapMethodClosureContextMediator.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1631/1768: DMMapMethodClosure.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1632/1768: DMMapMethodCheckDeallocating.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1633/1768: DMMapMember.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1634/1768: DMMapMaxScaleMiddleware.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1635/1768: DMMapFrameThread.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1636/1768: DMMapFrameEngine.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1637/1768: DMMapContextRuntime.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1638/1768: DMMapBundle.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1639/1768: DMMapBillBoardMiddleware.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1640/1768: DMMapBillBoardImageManager.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1641/1768: DMMapBillBoardDownLoader.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1642/1768: DMLocatorSpeedTextParam.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1643/1768: DMLocatorSpeedTextParam+DMOCC.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1644/1768: DMLocatorSkinThemeHelper.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1645/1768: DMLocatorSkinRingModel.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1646/1768: DMLocatorSkinResourceLoader.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1647/1768: DMLocatorSkinMiddleware.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1648/1768: DMLocatorSkinIndicatorModel.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1649/1768: DMLocatorSkinCompassModel.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1650/1768: DMLocatorSkin3DModel.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1651/1768: DMLocatorSectorLoader.m
2025-07-09 02:18:12,316 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1652/1768: DMLocatorInfo.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1653/1768: DMLocatorInfo+DMOCC.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1654/1768: DMLocatorController.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1655/1768: DMLocatorCompassLoader.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1656/1768: DMLocationCourseAdjustManager.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1657/1768: DMIndoorShowControlRule.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1658/1768: DMIndoorPointInfo.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1659/1768: DMImageUtility.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1660/1768: DMImageProvider.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1661/1768: DMImageManager.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1662/1768: DMGroupMarker.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1663/1768: DMGradientColorRoute.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1664/1768: DMGlobalDelegateManager.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1665/1768: DMFollowLocationPagMarker.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1666/1768: DMFollowLocationMarker.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1667/1768: DMEnlargeOverlayView.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1668/1768: DMElasticAnimation.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1669/1768: DMEOMarkerAnimationDataSource.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1670/1768: DMEOMarker.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1671/1768: DMEOMarker+Private.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1672/1768: DMEOMarker+KeyPoi.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1673/1768: DMEOMarker+Cluster.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1674/1768: DMEODSLOverlayParam.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1675/1768: DMEODSLCanvasViewParam.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1676/1768: DMEODSLAniTaskInfo.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1677/1768: DMEOClusterPresenter.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1678/1768: DMEOClusterParam.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1679/1768: DMEO3DMarker.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1680/1768: DMEO3DMarker+Trace.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1681/1768: DMEO3DMarker+Lod.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1682/1768: DMDynamicSkyboxWeatherInfo.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1683/1768: DMDynamicSkyboxMiddleware.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1684/1768: DMDynamicSkyboxFogParam.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1685/1768: DMDynamicSkyboxDownloader.m
2025-07-09 02:18:12,317 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1686/1768: DMDynamicSkyboxDayInfo.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1687/1768: DMDynamicSkyboxController.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1688/1768: DMDynamicSkyboxCityFilter.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1689/1768: DMDelegateImp.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1690/1768: DMDefaultLocatorSkinHelper.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1691/1768: DMDataUtility.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1692/1768: DMDashedColorRoute.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1693/1768: DMCustomTileModel3DStyle.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1694/1768: DMCustomTileModel3DLayer.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1695/1768: DMCustomTileMiddleware.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1696/1768: DMCustomTileLayer.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1697/1768: DMCustomTileData.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1698/1768: DMCustomTileDSLConfig.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1699/1768: DMCustomTileAdapter.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1700/1768: DMCustomMarker.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1701/1768: DMCustomImageGenerator.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1702/1768: DMCustomGridTileLayer.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1703/1768: DMCustomController.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1704/1768: DMCustomAgileMapMiddleware.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1705/1768: DMCoordinateSystemConverter.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1706/1768: DMCompositeTextureRoute.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1707/1768: DMCompassController.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1708/1768: DMCommonParticle.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1709/1768: DMCommonMiddleware.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1710/1768: DMCommonMarker.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1711/1768: DMCommonColorRoute.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1712/1768: DMColorUtility.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1713/1768: DMClusterTapInfo.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1714/1768: DMClusterAnnotationInfo.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1715/1768: DMClusterAnnotationAdapter.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1716/1768: DMClusterAnnotation.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1717/1768: DMCenterOffsetScaleYAxisGenerator.m
2025-07-09 02:18:12,318 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1718/1768: DMBuildingMiddleware.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1719/1768: DMBuildingLightInfo.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1720/1768: DMBuildingLightIdAttributeIndex.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1721/1768: DMBuildingLightAttribute.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1722/1768: DMBuildingController.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1723/1768: DMBillBoardController.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1724/1768: DMBasics.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1725/1768: DMBaseController.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1726/1768: DMAssistSimpleMarkerRoute.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1727/1768: DMAssistMarkerRouteGroup.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1728/1768: DMAssistMarkerRouteBase.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1729/1768: DMAssistMarkerRouteBase+Private.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1730/1768: DMAssistMarkerRoute.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1731/1768: DMAnimationMiddleware.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1732/1768: DMAgileTemplateDownLoadOperation.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1733/1768: DMAgileTemplateDownLoadManager.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1734/1768: DMAgileMarkerParam.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1735/1768: DMAgileMapMiddleware.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1736/1768: DMAgileLayoutParam.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1737/1768: DMAgileClusterGroupParam.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1738/1768: DMAgileClusterGroupDataManager.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1739/1768: DM3DObjectInfo.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1740/1768: QMVLService.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1741/1768: QMVLPlayerHostingView.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1742/1768: QMVLReportManager.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1743/1768: QMVLPlayer.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1744/1768: QMVLPerformanceReporter.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1745/1768: QMVLPerformanceManager.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1746/1768: QMVLMultiMapView.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1747/1768: QMVLMediaInfo.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1748/1768: QMVLMultiMapViewWidget.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1749/1768: QMVLMapViewWidget.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1750/1768: QMVLMapView+RouteExplain.m
2025-07-09 02:18:12,319 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1751/1768: QMVLMapView.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1752/1768: QMapVectorLayout-dummy.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1753/1768: QMVLMapView+OmnipotentMap.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1754/1768: QMVLMapView+Navi.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1755/1768: QMVLMapView+BestL4Camera.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1756/1768: QMVLLottieWidget.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1757/1768: QMVLLottieView.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1758/1768: QMVLLogger.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1759/1768: QMVLInjector.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1760/1768: QMVLImageView.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1761/1768: QMVLImageTransformer.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1762/1768: QMVLImageFetchManager.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1763/1768: QMVLFileItem.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1764/1768: QMVLCardViewCache.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1765/1768: QMDynamicMarkerService.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1766/1768: QMDynamicMarkerGenerator.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1767/1768: QMDynamicDSLPrebuildOperation.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1768/1768: QMapVectorLayout-dummy.m
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 发现主项目源文件
2025-07-09 02:18:12,320 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBusiness
2025-07-09 02:18:12,497 - pipeline.steps.ast_step_all - INFO - 在 QMapBusiness 中发现 1686 个源文件
2025-07-09 02:18:12,497 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapMiddlePlatform
2025-07-09 02:18:12,566 - pipeline.steps.ast_step_all - INFO - 在 QMapMiddlePlatform 中发现 1023 个源文件
2025-07-09 02:18:12,566 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapNaviKit
2025-07-09 02:18:12,774 - pipeline.steps.ast_step_all - INFO - 在 QMapNaviKit 中发现 461 个源文件
2025-07-09 02:18:12,774 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapHippy
2025-07-09 02:18:12,785 - pipeline.steps.ast_step_all - INFO - 在 QMapHippy 中发现 256 个源文件
2025-07-09 02:18:12,785 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DragonMapKit
2025-07-09 02:18:12,810 - pipeline.steps.ast_step_all - INFO - 在 DragonMapKit 中发现 207 个源文件
2025-07-09 02:18:12,810 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBaseline
2025-07-09 02:18:12,827 - pipeline.steps.ast_step_all - INFO - 在 QMapBaseline 中发现 135 个源文件
2025-07-09 02:18:12,827 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapFoundation
2025-07-09 02:18:12,834 - pipeline.steps.ast_step_all - INFO - 在 QMapFoundation 中发现 132 个源文件
2025-07-09 02:18:12,834 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapUIKit
2025-07-09 02:18:12,844 - pipeline.steps.ast_step_all - INFO - 在 QMapUIKit 中发现 66 个源文件
2025-07-09 02:18:12,844 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapRouteSearchKit
2025-07-09 02:18:12,846 - pipeline.steps.ast_step_all - INFO - 在 QMapRouteSearchKit 中发现 37 个源文件
2025-07-09 02:18:12,846 - pipeline.steps.ast_step_all - INFO - 扫描仓库: TencentMap
2025-07-09 02:18:12,856 - pipeline.steps.ast_step_all - INFO - 在 TencentMap 中发现 34 个源文件
2025-07-09 02:18:12,856 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DKProtocolsPool
2025-07-09 02:18:12,859 - pipeline.steps.ast_step_all - INFO - 在 DKProtocolsPool 中发现 12 个源文件
2025-07-09 02:18:12,859 - pipeline.steps.ast_step_all - INFO - 总共发现 4049 个项目源文件
2025-07-09 02:18:12,896 - pipeline.steps.ast_step_all - INFO - 补充处理符号映射中的项目文件
2025-07-09 02:18:12,939 - pipeline.steps.ast_step_all - INFO - 处理完成: 编译命令文件 1768 个, 发现文件 4049 个, 符号映射文件 402 个, 总计 6219 个文件
2025-07-09 02:18:12,970 - pipeline.steps.ast_step_all - INFO - 节点和边已保存到: ast_out_index_all/nodes_all.jsonl, ast_out_index_all/edges_all.jsonl
2025-07-09 02:18:12,976 - pipeline.core - INFO - 步骤 ast-all 执行成功: AST抽取完成，生成了 12036 个节点和 5817 条边
2025-07-09 02:18:12,976 - pipeline.core - INFO - 执行步骤 6/6: stats-all
2025-07-09 02:18:12,976 - pipeline.core - INFO - 开始执行步骤: stats-all
2025-07-09 02:18:12,976 - pipeline.steps.stats_step_all - INFO - 开始多仓库统计验证
2025-07-09 02:18:12,995 - pipeline.steps.stats_step_all - INFO - 加载了 12036 个节点和 5817 条边
2025-07-09 02:18:12,995 - pipeline.steps.stats_step_all - INFO - 计算综合统计信息
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 统计结果已保存到: ast_out_index_all/stats_all.json
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 多仓库统计结果
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 总节点数: 12,036
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 总边数: 5,817
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 跨仓库关系: 0
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 
节点类型分布:
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   File: 6,219
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   Class: 5,817
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 
仓库分布:
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapBusiness: 3,412 节点, 1,706 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapMiddlePlatform: 2,006 节点, 1,003 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   DragonMapKit: 1,047 节点, 636 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapNaviKit: 922 节点, 461 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapHippy: 512 节点, 256 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapFoundation: 337 节点, 205 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapBaseline: 270 节点, 135 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapUIKit: 132 节点, 66 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   TencentMap: 110 节点, 55 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   QMapRouteSearchKit: 74 节点, 37 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   DKProtocolsPool: 69 节点, 57 文件
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO - 
质量指标:
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   节点边比例: 0.48
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   平均节点度数: 1.00
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   数据完整性: 1.00
2025-07-09 02:18:14,243 - pipeline.steps.stats_step_all - INFO -   文件覆盖率: 1.54
2025-07-09 02:18:14,245 - pipeline.core - ERROR - 步骤 stats-all 执行失败: 统计验证失败: min_nodes, min_edges, min_cross_repo
2025-07-09 02:18:14,245 - pipeline.core - ERROR - 步骤 stats-all 执行失败，停止流水线
2025-07-09 02:18:14,245 - __main__ - ERROR - ❌ Pipeline-All执行失败: Pipeline执行失败
2025-07-09 02:28:16,791 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 02:28:16,791 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 02:28:16,791 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 02:28:16,813 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 02:28:16,823 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 02:28:16,871 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 02:28:16,906 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 02:28:16,910 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 02:28:16,984 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 02:28:17,061 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 02:28:17,270 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 02:28:17,742 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 02:28:18,044 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 02:28:18,045 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 02:28:18,046 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 02:28:18,046 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 02:28:18,046 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 02:28:18,046 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 02:28:18,046 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 02:28:18,046 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 02:28:18,046 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 02:28:18,046 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 02:28:18,046 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 02:28:18,046 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 02:28:18,046 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 02:28:18,046 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 02:28:18,046 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 02:28:18,046 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 02:28:18,046 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 02:28:23,186 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-09 02:28:23,187 - pipeline.steps.clean_step - INFO - 删除文件: index_symbols_all.jsonl
2025-07-09 02:28:23,187 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 63830 个项目，释放 9110093889 字节
2025-07-09 02:28:23,187 - pipeline.core - INFO - 执行步骤 2/6: index-store-all
2025-07-09 02:28:23,187 - pipeline.core - INFO - 开始执行步骤: index-store-all
2025-07-09 02:28:23,188 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-09 02:28:23,188 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 02:28:23,188 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-09 02:28:23,188 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 02:28:23,190 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-09 02:28:23,190 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成索引...
2025-07-09 02:28:23,191 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-09 02:28:23,191 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-09 02:28:23,192 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成索引...
2025-07-09 02:28:23,194 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-09 02:28:36,523 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapFoundation and configuration Debug
(1 failure)

2025-07-09 02:28:36,524 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-09 02:28:40,214 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DragonMapKit and configuration Debug
(1 failure)

2025-07-09 02:28:40,214 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-09 02:28:40,600 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-09 02:28:40,601 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-09 02:28:40,809 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-09 02:28:40,809 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-09 02:28:41,035 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-09 02:28:41,035 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-09 02:28:41,117 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-09 02:28:41,118 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-09 02:28:41,170 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-09 02:28:41,170 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-09 02:28:47,707 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-09 02:28:47,708 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-09 02:28:53,135 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-09 02:29:01,264 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-09 02:29:02,110 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(1 failure)

2025-07-09 02:29:02,998 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-09 02:29:03,024 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-09 02:29:03,171 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-09 02:29:05,440 - pipeline.steps.index_store_step_all - ERROR - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DKProtocolsPool and configuration Debug
(1 failure)

2025-07-09 02:29:06,060 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	PhaseScriptExecution [CP]\ Copy\ XCFrameworks /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/RouteGuidance.build/Debug-iphoneos/RouteGuidance.build/Script-66646D00000240.sh (in target 'RouteGuidance' from project 'RouteGuidance')
	PhaseScriptExecution [CP]\ Copy\ XCFrameworks /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/NerdApi.build/Debug-iphoneos/NerdApi.build/Script-8ECC9900000240.sh (in target 'NerdApi' from project 'NerdApi')
	ProcessInfoPlistFile /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphoneos/QCloudCOSXML/QCloudCOSXML.bundle/Info.plist /Users/<USER>/GRAG_iOS/hammmer-workspace/Pods/Target\ Support\ Files/QCloudCOSXML/ResourceBundle-QCloudCOSXML-QCloudCOSXML-Info.plist (in target 'QCloudCOSXML-QCloudCOSXML' from project 'QCloudCOSXML')
	Building workspace TencentMap with scheme QMapRouteSearchKit and configuration Debug
(4 failures)

2025-07-09 02:29:06,061 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:29:06,117 - pipeline.steps.index_store_step_all - INFO - 索引存储输出验证成功，找到 3505 个索引记录文件
2025-07-09 02:29:06,117 - pipeline.core - INFO - 步骤 index-store-all 执行成功: 索引生成部分完成，成功: 1, 失败: 15
2025-07-09 02:29:06,117 - pipeline.core - INFO - 执行步骤 3/6: usrs-all
2025-07-09 02:29:06,117 - pipeline.core - INFO - 开始执行步骤: usrs-all
2025-07-09 02:29:06,117 - pipeline.steps.usrs_step_all - INFO - 开始提取多仓库USR到文件路径映射
2025-07-09 02:29:06,117 - pipeline.steps.usrs_step_all - INFO - 检查索引存储目录: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:29:06,117 - pipeline.steps.usrs_step_all - INFO - 开始提取USR到文件路径映射
2025-07-09 02:29:06,117 - pipeline.steps.usrs_step_all - INFO - 使用索引存储路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 02:29:06,117 - pipeline.steps.usrs_step_all - INFO - 执行命令: tools/indexstore-db/.build/release/dump-usrs --store ast_out_index_all/DerivedData/Index.noindex/DataStore --out index_symbols_all.jsonl
2025-07-09 02:29:08,134 - pipeline.steps.usrs_step_all - INFO - USR提取命令执行成功
2025-07-09 02:29:08,147 - pipeline.steps.usrs_step_all - INFO - 成功提取 111372 个USR映射
2025-07-09 02:29:08,147 - pipeline.steps.usrs_step_all - INFO - 分析提取的USR数据...
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO - USR分析完成:
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO -   总USR数: 111,352
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO -   项目文件: 0
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO -   Pods文件: 19,735
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO -   系统文件: 0
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO -   仓库分布:
2025-07-09 02:29:08,444 - pipeline.steps.usrs_step_all - INFO -     unknown: 111,352
2025-07-09 02:29:08,444 - pipeline.core - INFO - 步骤 usrs-all 执行成功: USR提取完成，提取了 111372 个USR映射
2025-07-09 02:29:08,444 - pipeline.core - INFO - 执行步骤 4/6: cdb-all
2025-07-09 02:29:08,444 - pipeline.core - INFO - 开始执行步骤: cdb-all
2025-07-09 02:29:08,444 - pipeline.steps.cdb_step_all - INFO - 开始生成多仓库编译数据库
2025-07-09 02:29:08,445 - pipeline.steps.cdb_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 02:29:08,445 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成编译数据库...
2025-07-09 02:29:08,445 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成编译数据库...
2025-07-09 02:29:08,447 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成编译数据库...
2025-07-09 02:29:08,447 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成编译数据库...
2025-07-09 02:29:08,449 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成编译数据库...
2025-07-09 02:29:08,452 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成编译数据库...
2025-07-09 02:29:08,456 - pipeline.steps.cdb_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成编译数据库...
2025-07-09 02:29:08,458 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成编译数据库...
2025-07-09 02:29:18,730 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapUIKit 的scheme 'QMapUIKit' 构建失败，但继续解析日志
2025-07-09 02:29:18,730 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapUIKit 的构建日志，长度: 583833 字符
2025-07-09 02:29:18,731 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 25 个CompileC行, 122 个clang命令
2025-07-09 02:29:18,848 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 29/57 个文件通过过滤
2025-07-09 02:29:18,848 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 编译数据库生成成功，包含 29 个编译命令
2025-07-09 02:29:18,848 - pipeline.steps.cdb_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成编译数据库...
2025-07-09 02:29:19,086 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败，但继续解析日志
2025-07-09 02:29:19,086 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapMiddlePlatform 的构建日志，长度: 40585 字符
2025-07-09 02:29:19,086 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:19,086 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:19,086 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:19,086 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成编译数据库...
2025-07-09 02:29:19,477 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败，但继续解析日志
2025-07-09 02:29:19,477 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapHippy 的构建日志，长度: 43051 字符
2025-07-09 02:29:19,477 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:19,478 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:19,478 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapHippy 的scheme 'QMapHippy' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:19,478 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成编译数据库...
2025-07-09 02:29:19,673 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败，但继续解析日志
2025-07-09 02:29:19,673 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapFoundation 的构建日志，长度: 17208 字符
2025-07-09 02:29:19,673 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 5 个clang命令
2025-07-09 02:29:19,673 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:19,673 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapFoundation 的scheme 'QMapFoundation' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:19,673 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成编译数据库...
2025-07-09 02:29:20,298 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败，但继续解析日志
2025-07-09 02:29:20,298 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBusiness 的构建日志，长度: 54881 字符
2025-07-09 02:29:20,298 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:20,298 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:20,298 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBusiness 的scheme 'QMapBusiness' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:20,298 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成编译数据库...
2025-07-09 02:29:20,682 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败，但继续解析日志
2025-07-09 02:29:20,682 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapNaviKit 的构建日志，长度: 43239 字符
2025-07-09 02:29:20,682 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:20,683 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:20,683 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:20,683 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成编译数据库...
2025-07-09 02:29:21,173 - pipeline.steps.cdb_step_all - WARNING - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败，但继续解析日志
2025-07-09 02:29:21,173 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DragonMapKit 的构建日志，长度: 32187 字符
2025-07-09 02:29:21,173 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:21,174 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:21,174 - pipeline.steps.cdb_step_all - INFO - 仓库 DragonMapKit 的scheme 'DragonMapKit' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:21,174 - pipeline.steps.cdb_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成编译数据库...
2025-07-09 02:29:21,902 - pipeline.steps.cdb_step_all - WARNING - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败，但继续解析日志
2025-07-09 02:29:21,903 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapBaseline 的构建日志，长度: 48220 字符
2025-07-09 02:29:21,903 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:21,903 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:21,903 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapBaseline 的scheme 'QMapBaseline' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:21,903 - pipeline.steps.cdb_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成编译数据库...
2025-07-09 02:29:24,410 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 36344 字符
2025-07-09 02:29:24,411 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 1 个clang命令
2025-07-09 02:29:24,411 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:24,411 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'BuildInfo' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:34,554 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'TencentMap' 构建失败，但继续解析日志
2025-07-09 02:29:34,554 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89211 字符
2025-07-09 02:29:34,555 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:34,555 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:34,555 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'TencentMap' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:35,854 - pipeline.steps.cdb_step_all - WARNING - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败，但继续解析日志
2025-07-09 02:29:35,854 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 DKProtocolsPool 的构建日志，长度: 2277 字符
2025-07-09 02:29:35,854 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 2 个clang命令
2025-07-09 02:29:35,854 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:35,855 - pipeline.steps.cdb_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:36,016 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntents' 构建失败，但继续解析日志
2025-07-09 02:29:36,016 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89210 字符
2025-07-09 02:29:36,016 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:36,017 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:36,017 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntents' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:37,968 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败，但继续解析日志
2025-07-09 02:29:37,968 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89212 字符
2025-07-09 02:29:37,968 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:37,968 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:37,969 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMIntentsUI' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:39,245 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败，但继续解析日志
2025-07-09 02:29:39,245 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89222 字符
2025-07-09 02:29:39,246 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:39,246 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:39,246 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMNotificationService' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:40,097 - pipeline.steps.cdb_step_all - WARNING - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败，但继续解析日志
2025-07-09 02:29:40,097 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 TencentMap 的构建日志，长度: 89218 字符
2025-07-09 02:29:40,098 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 0 个CompileC行, 6 个clang命令
2025-07-09 02:29:40,098 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 0/0 个文件通过过滤
2025-07-09 02:29:40,098 - pipeline.steps.cdb_step_all - INFO - 仓库 TencentMap 的scheme 'QMWidgetExtension' 编译数据库生成成功，包含 0 个编译命令
2025-07-09 02:29:51,529 - pipeline.steps.cdb_step_all - INFO - 开始解析仓库 QMapRouteSearchKit 的构建日志，长度: 6422963 字符
2025-07-09 02:29:51,535 - pipeline.steps.cdb_step_all - INFO - 解析完成: 找到 269 个CompileC行, 1163 个clang命令
2025-07-09 02:29:52,821 - pipeline.steps.cdb_step_all - INFO - 过滤完成: 304/581 个文件通过过滤
2025-07-09 02:29:52,821 - pipeline.steps.cdb_step_all - INFO - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 编译数据库生成成功，包含 304 个编译命令
2025-07-09 02:29:52,822 - pipeline.steps.cdb_step_all - INFO - 合并编译命令完成，共 304 个唯一文件
2025-07-09 02:29:52,825 - pipeline.steps.cdb_step_all - INFO - 编译数据库已保存到: ast_out_index_all/compile_commands/compile_commands.json
2025-07-09 02:29:52,825 - pipeline.core - INFO - 步骤 cdb-all 执行成功: 编译数据库生成完成，处理了 16 个scheme，生成 304 个编译命令
2025-07-09 02:29:52,825 - pipeline.core - INFO - 执行步骤 5/6: ast-all
2025-07-09 02:29:52,825 - pipeline.core - INFO - 开始执行步骤: ast-all
2025-07-09 02:29:52,825 - pipeline.steps.ast_step_all - INFO - 开始多仓库AST抽取
2025-07-09 02:29:52,826 - pipeline.steps.ast_step_all - INFO - 从编译数据库加载了 304 个编译命令
2025-07-09 02:29:52,826 - pipeline.steps.ast_step_all - INFO - 加载了 304 个编译命令
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6045: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6046: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6047: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6048: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6049: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6050: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6051: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6052: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6053: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,832 - pipeline.steps.ast_step_all - WARNING - 跳过无效JSON行 6054: Expecting ',' delimiter: line 1 column 64 (char 63)
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - WARNING - 跳过了 20/111372 个无效的符号行
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 成功加载 111352 个符号映射
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理 304 个编译命令
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 1/304: APLAppObject.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 2/304: PBAPLProtocol.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 3/304: ApolloSDK.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 4/304: APLSafeObject.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 5/304: ApolloSDK-dummy.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 6/304: APLSDKHelper.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 7/304: APLSDKConfig.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 8/304: APLRegistry.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 9/304: APLPrivate.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 10/304: APLNodeDataRefresher.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 11/304: APLRespDTO.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 12/304: APLNode.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 13/304: APLNetworkSessionManager.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 14/304: APLNetworkSessionDataTask.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 15/304: APLNetworkSerialization.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 16/304: libMMKV.mm
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 17/304: MMKV-dummy.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 18/304: MMKVCore-dummy.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 19/304: APLNetworkRequest.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 20/304: APLNetworkEncryptor.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 21/304: APLNetworkAPI.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 22/304: APLModuleObject.m
2025-07-09 02:29:52,936 - pipeline.steps.ast_step_all - INFO - 处理编译文件 23/304: APLLog.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 24/304: APLInterface.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 25/304: APLConfigObject.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 26/304: APLCacheConfig.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 27/304: APLCache.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 28/304: APLBusinessObject.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 29/304: APLBasePO.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 30/304: QCloudCore-dummy.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 31/304: NSDate+QCLOUD.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 32/304: NSString+MJExtension.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 33/304: NSObject+MJProperty.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 34/304: NSObject+MJKeyValue.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 35/304: NSObject+MJCoding.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 36/304: NSDate+QCloudComapre.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 37/304: NSObject+MJClass.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 38/304: MJPropertyType.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 39/304: MJProperty.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 40/304: MJPropertyKey.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 41/304: MJFoundation.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 42/304: MJExtensionConst.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 43/304: MJExtension-dummy.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 44/304: NSObject+FBKVOController.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 45/304: KVOController-dummy.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 46/304: QCloudBundlePath.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 47/304: QCloudAuthentationV5Creator.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 48/304: NSObject+QCloudModelTool.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 49/304: NSObject+QCloudModel.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 50/304: NSObject+HTTPHeadersContainer.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 51/304: NSMutableData+QCloud_CRC.m
2025-07-09 02:29:52,937 - pipeline.steps.ast_step_all - INFO - 处理编译文件 52/304: NSHTTPCookie+QCloudNetworking.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 53/304: NSError+QCloudNetworking.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 54/304: NSDate+QCloudInternetDateTime.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 55/304: UIImage+WebP.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 56/304: SDWebImageWebPCoderDefine.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 57/304: SDWebImageWebPCoder-dummy.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 58/304: SDImageWebPCoder.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 59/304: FBKVOController.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 60/304: NSData+GZIP.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 61/304: GZIP-dummy.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 62/304: FMResultSet.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 63/304: FMDatabaseQueue.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 64/304: FMDatabasePool.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 65/304: FMDatabaseAdditions.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 66/304: FMDatabase.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 67/304: FMDB-dummy.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 68/304: UIViewController+DKApplication.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 69/304: UINavigationController+DKApplication.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 70/304: DKServiceStartMirror.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 71/304: DKServiceDigest.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 72/304: DKServiceContext.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 73/304: DKSchemeHandler.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 74/304: DKRulesChecker.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 75/304: DKRouterContext.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 76/304: DKRouter.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 77/304: DKRouter+URLRouter.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 78/304: DKRouter+Service.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 79/304: DKRouter+Application.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 80/304: DKRegistryCenter.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 81/304: DKMobileCore.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 82/304: DKMobile-dummy.m
2025-07-09 02:29:52,938 - pipeline.steps.ast_step_all - INFO - 处理编译文件 83/304: DKMachORegistry.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 84/304: DKLog.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 85/304: DKCoreDelegate.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 86/304: DKCoreDelegate+CarPlay.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 87/304: DKApplicationDigest.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 88/304: DKApplication.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 89/304: DKAppLifeDelegate.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 90/304: DDTTYLogger.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 91/304: DDOSLogger.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 92/304: DDMultiFormatter.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 93/304: DDLoggerNames.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 94/304: DDLog.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 95/304: DDFileLogger.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 96/304: DDFileLogger+Buffering.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 97/304: DDDispatchQueueLogFormatter.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 98/304: DDContextFilterLogFormatter.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 99/304: DDContextFilterLogFormatter+Deprecated.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 100/304: DDAbstractDatabaseLogger.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 101/304: DDASLLogger.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 102/304: DDASLLogCapture.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 103/304: CocoaLumberjack-dummy.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 104/304: CLIColor.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 105/304: QCloudUploadPartResult.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 106/304: QCloudUploadPartRequest.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 107/304: QCloudUploadPartRequest+Custom.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 108/304: QCloudUploadPartCopyRequest.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 109/304: QCloudUploadObjectResult.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 110/304: QCloudRequestData+COSXMLVersion.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 111/304: QCloudPutObjectRequest.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 112/304: QCloudPutObjectRequest+CustomBuild.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 113/304: QCloudPutObjectRequest+Custom.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 114/304: QCloudPutObjectCopyRequest.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 115/304: QCloudMultipartUploadPart.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 116/304: QCloudMultipartUploadOwner.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 117/304: QCloudMultipartUploadInitiator.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 118/304: QCloudMultipartInfo.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 119/304: QCloudLogManager.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 120/304: QCloudListPartsResult.m
2025-07-09 02:29:52,939 - pipeline.steps.ast_step_all - INFO - 处理编译文件 121/304: QCloudListMultipartRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 122/304: QCloudInitiateMultipartUploadResult.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 123/304: QCloudInitiateMultipartUploadRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 124/304: QCloudHeadObjectRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 125/304: QCloudGetObjectRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 126/304: QCloudGetObjectRequest+Custom.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 127/304: QCloudCopyObjectResult.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 128/304: QCloudCompleteMultipartUploadRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 129/304: QCloudCompleteMultipartUploadInfo.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 130/304: QCloudCOSXMLVersion.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 131/304: QCloudCOSXMLUploadObjectRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 132/304: QCloudCOSXMLServiceUtilities.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 133/304: QCloudCOSXMLService.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 134/304: QCloudCOSXMLService+Transfer.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 135/304: QCloudCOSXMLService+Quality.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 136/304: QCloudCOSXMLService+Configuration.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 137/304: QCloudCOSXMLEndPoint.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 138/304: QCloudCOSXMLDownloadObjectRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 139/304: QCloudCOSXMLCopyObjectRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 140/304: QCloudCOSXML-dummy.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 141/304: QCloudCOSTransferMangerService.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 142/304: QCloudCOSStorageClassEnum.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 143/304: QCloudAppendObjectRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 144/304: QCloudAbstractRequest+Quality.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 145/304: QCloudAbortMultipfartUploadRequest.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 146/304: NSString+RegularExpressionCategory.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 147/304: ReconnectTimer.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 148/304: MQTTWebsocketTransport.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 149/304: MQTTTransport.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 150/304: MQTTStrict.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 151/304: MQTTSessionSynchron.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 152/304: MQTTSessionManager.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 153/304: MQTTSessionLegacy.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 154/304: MQTTSession.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 155/304: MQTTSSLSecurityPolicyTransport.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 156/304: MQTTSSLSecurityPolicyEncoder.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 157/304: MQTTSSLSecurityPolicyDecoder.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 158/304: MQTTSSLSecurityPolicy.m
2025-07-09 02:29:52,940 - pipeline.steps.ast_step_all - INFO - 处理编译文件 159/304: MQTTProperties.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 160/304: MQTTMessage.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 161/304: MQTTLog.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 162/304: MQTTInMemoryPersistence.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 163/304: MQTTDecoder.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 164/304: MQTTCoreDataPersistence.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 165/304: MQTTClient-dummy.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 166/304: MQTTCFSocketTransport.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 167/304: MQTTCFSocketEncoder.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 168/304: MQTTCFSocketDecoder.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 169/304: GCDTimer.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 170/304: ForegroundReconnection.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 171/304: DKProtocolsPool-dummy.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 172/304: DKPServices.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 173/304: DKPQQMaps.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 174/304: DKPPerformanceStatService.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 175/304: DKPApplications.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 176/304: DKPQQMaps.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 177/304: DM_MAP_ZoomForNaviParameter.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 178/304: DragonMapKitC2OC-dummy.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 179/304: DM_MAP_UniversalModelTapInfo.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 180/304: DM_MAP_TXAnimationParam.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 181/304: DM_MAP_TXUploadLogArgs.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 182/304: DM_MAP_TMSize.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 183/304: DM_MAP_TMRect.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 184/304: DM_MAP_TMPoint.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 185/304: DM_MAP_TMBitmapContext.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 186/304: DM_MAP_ShadowSetting.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 187/304: DM_MAP_SectionDashedLineParam.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 188/304: DM_MAP_SimpleLandMarkMode.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 189/304: DM_MAP_RouteTurnArrow3DStyle.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 190/304: DM_MAP_RouteStyleAtScale.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 191/304: DM_MAP_RouteNameStyleAtScale.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 192/304: DM_MAP_RouteNameStyle.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 193/304: DM_MAP_RouteGradientParamForSegmentMode.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 194/304: DM_MAP_RouteGradientInfo.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 195/304: DM_MAP_RichCallbackInfo.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 196/304: DM_MAP_RGBADashedLineStyleAtScale.m
2025-07-09 02:29:52,941 - pipeline.steps.ast_step_all - INFO - 处理编译文件 197/304: DM_MAP_RGBADashedLineExtraParam.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 198/304: DM_MAP_RGBAColorLineExtraParam.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 199/304: DM_MAP_POIInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 200/304: DM_MAP_OverlookParam.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 201/304: DM_MAP_Object3DTapInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 202/304: DM_MAP_ModelID.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 203/304: DM_MAP_MarkerGroupIconAnchor.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 204/304: DM_MAP_Marker4KInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 205/304: DM_MAP_MapTree.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 206/304: DM_MAP_MapTileID.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 207/304: DM_MAP_MapTextDrawInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 208/304: DM_MAP_MapRouteSectionWithName.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 209/304: DM_MAP_MapTappedInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 210/304: DM_MAP_MapRouteSection.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 211/304: DM_MAP_MapRouteInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 212/304: DM_MAP_MapRouteDescInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 213/304: DM_MAP_MapRoadScanOptions.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 214/304: DM_MAP_MapRectF.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 215/304: DM_MAP_MapPrimitive.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 216/304: DM_MAP_MapPatternStyle.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 217/304: DM_MAP_MapNaviAccuracyCircleOptions.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 218/304: DM_MAP_MapNaviAccuracyCircleGradientNode.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 219/304: DM_MAP_MapModelReportFlag.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 220/304: DM_MAP_MapModel3DImageBuffer.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 221/304: DM_MAP_MapMarkerSubPoiInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 222/304: DM_MAP_MapMarkerLocatorInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 223/304: DM_MAP_MapMarkerImageLabelInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 224/304: DM_MAP_MapMarkerIconInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 225/304: DM_MAP_MapMarkerGroupIconPosInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 226/304: DM_MAP_MapMarkerGroupIconInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 227/304: DM_MAP_MapMarkerCustomIconInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 228/304: DM_MAP_MapMarkerClusterData.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 229/304: DM_MAP_MapMarkerAvoidRouteRule.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 230/304: DM_MAP_MapMarkerAvoidDetailedRule.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 231/304: DM_MAP_MapMarkerAnnotationInfo.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 232/304: DM_MAP_MapLocatorSpeedTextParam.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 233/304: DM_MAP_MapLocatorScanLightOptions.m
2025-07-09 02:29:52,942 - pipeline.steps.ast_step_all - INFO - 处理编译文件 234/304: DM_MAP_MapLocatorBreatheOptions.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 235/304: DM_MAP_MapLocatorBreatheNodes.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 236/304: DM_MAP_MapLocation.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 237/304: DM_MAP_MapLaneID.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 238/304: DM_MAP_MapHoleInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 239/304: DM_MAP_MapEdgeInsets.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 240/304: DM_MAP_MapDisplayParam.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 241/304: DM_MAP_MapCircleInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 242/304: DM_MAP_MapBitmapTileID.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 243/304: DM_MAP_MapBitmap.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 244/304: DM_MAP_JuncImageInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 245/304: DM_MAP_InterestScenicAreaInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 246/304: DM_MAP_InterestIndoorAreaInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 247/304: DM_MAP_InterestAreaInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 248/304: DM_MAP_IndoorTappedInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 249/304: DM_MAP_IndoorParkSpaceInfoBatchs.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 250/304: DM_MAP_IndoorParkSpaceInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 251/304: DM_MAP_GuidanceEventInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 252/304: DM_MAP_GlyphMetrics.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 253/304: DM_MAP_GLMapFloorName.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 254/304: DM_MAP_GLMapAnnotationText.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 255/304: DM_MAP_GLMapAnnotationIcon.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 256/304: DM_MAP_GLBuildingInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 257/304: DM_MAP_EngineRenderContent.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 258/304: DM_MAP_DynamicMapAnnotationObject.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 259/304: DM_MAP_DownloadData.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 260/304: DM_MAP_DayInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 261/304: DM_MAP_CustomTileRegionStyle.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 262/304: DM_MAP_CustomTileQueryInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 263/304: DM_MAP_CustomTilePointStyle.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 264/304: DM_MAP_CustomTileModel3DStyle.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 265/304: DM_MAP_CustomTileLineStyle.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 266/304: DM_MAP_Collision3DResult.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 267/304: DM_MAP_ClusterTappedInfo.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 268/304: DM_MAP_CameraOverlookParam.m
2025-07-09 02:29:52,943 - pipeline.steps.ast_step_all - INFO - 处理编译文件 269/304: DM_MAP_CameraAnimationParam.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 270/304: DM_MAP_BuildingLoadedParam.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 271/304: DM_MAP_BuildingLightInfo.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 272/304: DM_MAP_BuildingLightIdAttributeIndex.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 273/304: DM_MAP_BuildingLightAttribute.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 274/304: DM_MAP_BlackWhiteListRule.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 275/304: DM_MAP_BillboardInfo.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 276/304: DM_MAP_BaseObject.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 277/304: DM_MAP_AnimationParam.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 278/304: DM_MAP_AnimationContent.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 279/304: RGModelInterface.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 280/304: MapBaseOCModel-dummy.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 281/304: MapBaseModel.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 282/304: MapBaseModelInterface.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 283/304: MapBaseOCUtils.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 284/304: RGModelInterfaceOCCPP.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 285/304: MapBaseModelInterfaceOCCPP.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 286/304: CustomAgileMapManager.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 287/304: AgileMarkerCanvasLayoutParamOCCPP.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 288/304: AgileMapResourceCallbackAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 289/304: AgileMapManager.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 290/304: AgileCreateLayerCallBackAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 291/304: CustomAgileMapLayerOption.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 292/304: AgileMarkerCanvasLayoutParam.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 293/304: AgileLayerOption.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 294/304: RouteGuidanceOCMiddleware-dummy.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 295/304: RGWalkCycleListenerAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 296/304: RGWalkAPI.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 297/304: RGWalkCycleProtocol.m
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 298/304: RGVersion.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 299/304: RGListenerAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 300/304: RGDriveStatisticsListenerAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 301/304: RGDriveProvider.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 302/304: RGDriveEventListenerAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 303/304: RGDriveBehaviorEventListenerAdapter.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 处理编译文件 304/304: RGCycleAPI.mm
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 发现主项目源文件
2025-07-09 02:29:52,944 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBusiness
2025-07-09 02:29:53,095 - pipeline.steps.ast_step_all - INFO - 在 QMapBusiness 中发现 1686 个源文件
2025-07-09 02:29:53,095 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapMiddlePlatform
2025-07-09 02:29:53,157 - pipeline.steps.ast_step_all - INFO - 在 QMapMiddlePlatform 中发现 1023 个源文件
2025-07-09 02:29:53,157 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapNaviKit
2025-07-09 02:29:53,331 - pipeline.steps.ast_step_all - INFO - 在 QMapNaviKit 中发现 461 个源文件
2025-07-09 02:29:53,332 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapHippy
2025-07-09 02:29:53,340 - pipeline.steps.ast_step_all - INFO - 在 QMapHippy 中发现 256 个源文件
2025-07-09 02:29:53,340 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DragonMapKit
2025-07-09 02:29:53,359 - pipeline.steps.ast_step_all - INFO - 在 DragonMapKit 中发现 207 个源文件
2025-07-09 02:29:53,359 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapBaseline
2025-07-09 02:29:53,371 - pipeline.steps.ast_step_all - INFO - 在 QMapBaseline 中发现 135 个源文件
2025-07-09 02:29:53,371 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapFoundation
2025-07-09 02:29:53,377 - pipeline.steps.ast_step_all - INFO - 在 QMapFoundation 中发现 132 个源文件
2025-07-09 02:29:53,377 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapUIKit
2025-07-09 02:29:53,384 - pipeline.steps.ast_step_all - INFO - 在 QMapUIKit 中发现 66 个源文件
2025-07-09 02:29:53,384 - pipeline.steps.ast_step_all - INFO - 扫描仓库: QMapRouteSearchKit
2025-07-09 02:29:53,385 - pipeline.steps.ast_step_all - INFO - 在 QMapRouteSearchKit 中发现 37 个源文件
2025-07-09 02:29:53,385 - pipeline.steps.ast_step_all - INFO - 扫描仓库: TencentMap
2025-07-09 02:29:53,392 - pipeline.steps.ast_step_all - INFO - 在 TencentMap 中发现 34 个源文件
2025-07-09 02:29:53,393 - pipeline.steps.ast_step_all - INFO - 扫描仓库: DKProtocolsPool
2025-07-09 02:29:53,395 - pipeline.steps.ast_step_all - INFO - 在 DKProtocolsPool 中发现 12 个源文件
2025-07-09 02:29:53,395 - pipeline.steps.ast_step_all - INFO - 总共发现 4049 个项目源文件
2025-07-09 02:30:17,747 - pipeline.steps.ast_step_all - INFO - 补充处理符号映射中的项目文件
2025-07-09 02:30:17,775 - pipeline.steps.ast_step_all - INFO - 处理完成: 编译命令文件 304 个, 发现文件 4049 个, 符号映射文件 112 个, 总计 4465 个文件
2025-07-09 02:30:18,740 - pipeline.steps.ast_step_all - INFO - 节点和边已保存到: ast_out_index_all/nodes_all.jsonl, ast_out_index_all/edges_all.jsonl
2025-07-09 02:30:18,782 - pipeline.core - INFO - 步骤 ast-all 执行成功: AST抽取完成，生成了 80549 个节点和 518692 条边
2025-07-09 02:30:18,783 - pipeline.core - INFO - 执行步骤 6/6: stats-all
2025-07-09 02:30:18,783 - pipeline.core - INFO - 开始执行步骤: stats-all
2025-07-09 02:30:18,783 - pipeline.steps.stats_step_all - INFO - 开始多仓库统计验证
2025-07-09 02:30:19,878 - pipeline.steps.stats_step_all - INFO - 加载了 80549 个节点和 518692 条边
2025-07-09 02:30:19,878 - pipeline.steps.stats_step_all - INFO - 计算综合统计信息
2025-07-09 02:44:22,508 - pipeline.steps.stats_step_all - INFO - 统计结果已保存到: ast_out_index_all/stats_all.json
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 多仓库统计结果
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - ============================================================
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 总节点数: 80,549
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 总边数: 518,692
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 跨仓库关系: 15,754
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 
节点类型分布:
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   Method: 57,582
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   Property: 14,144
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   File: 4,465
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   Class: 4,353
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   Protocol: 3
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   Struct: 1
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   Enum: 1
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 
仓库分布:
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapBusiness: 38,787 节点, 1,706 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapMiddlePlatform: 17,024 节点, 1,003 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapNaviKit: 11,122 节点, 461 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   DragonMapKit: 4,581 节点, 309 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapHippy: 2,820 节点, 256 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapFoundation: 2,097 节点, 132 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapBaseline: 1,892 节点, 135 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapUIKit: 1,110 节点, 66 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   TencentMap: 291 节点, 34 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   QMapRouteSearchKit: 261 节点, 37 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   DKProtocolsPool: 58 节点, 17 文件
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO - 
质量指标:
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   节点边比例: 6.44
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   平均节点度数: 6.59
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   数据完整性: 1.00
2025-07-09 02:44:22,509 - pipeline.steps.stats_step_all - INFO -   文件覆盖率: 1.10
2025-07-09 02:44:22,583 - pipeline.core - INFO - 步骤 stats-all 执行成功: 统计验证成功: 80549 节点, 518692 边
2025-07-09 02:44:22,583 - pipeline.core - INFO - 流水线执行完成
2025-07-09 02:44:22,583 - __main__ - INFO - ✅ Pipeline执行成功
2025-07-09 02:44:22,583 - __main__ - INFO - 📍 阶段4: 结果汇总
2025-07-09 02:44:22,647 - __main__ - INFO - ============================================================
2025-07-09 02:44:22,648 - __main__ - INFO - Pipeline-All 执行结果
2025-07-09 02:44:22,648 - __main__ - INFO - ============================================================
2025-07-09 02:44:22,648 - __main__ - INFO - 状态: 成功
2025-07-09 02:44:22,648 - __main__ - INFO - 总耗时: 965.79秒
2025-07-09 02:44:22,648 - __main__ - INFO - 处理仓库: 11个
2025-07-09 02:44:22,648 - __main__ - INFO - 处理源文件: 4,049个
2025-07-09 02:44:22,648 - __main__ - INFO - 生成节点: 80,549个
2025-07-09 02:44:22,648 - __main__ - INFO - 生成边: 518,692条
2025-07-09 02:44:22,648 - __main__ - INFO - 🎉 节点数达到目标: 80,549 >= 30,000
2025-07-09 02:44:22,648 - __main__ - INFO - 🎉 边数达到目标: 518,692 >= 28,000
2025-07-09 02:44:22,648 - __main__ - INFO - 🎉 Pipeline-All执行完成!
2025-07-10 15:14:05,969 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-10 15:14:05,969 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-10 15:14:05,969 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-10 15:14:06,000 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-10 15:14:06,010 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-10 15:14:06,055 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-10 15:14:06,091 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-10 15:14:06,099 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-10 15:14:06,164 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-10 15:14:06,252 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-10 15:14:06,421 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-10 15:14:06,875 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-10 15:14:07,159 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-10 15:14:07,219 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-10 15:14:07,219 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-10 15:14:07,220 - __main__ - INFO - 📁 business: 1个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-10 15:14:07,220 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-10 15:14:07,221 - pipeline.core - INFO - 添加步骤: clean
2025-07-10 15:14:07,221 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-10 15:14:07,221 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-10 15:14:07,221 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-10 15:14:07,221 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-10 15:14:07,221 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-10 15:14:07,221 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-10 15:14:07,221 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-10 15:14:07,221 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-10 15:14:07,221 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-10 15:14:07,221 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-10 15:14:07,221 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-10 15:14:09,339 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-10 15:14:09,340 - pipeline.steps.clean_step - INFO - 删除文件: index_symbols_all.jsonl
2025-07-10 15:14:09,340 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 29529 个项目，释放 6813016220 字节
2025-07-10 15:14:09,340 - pipeline.core - INFO - 执行步骤 2/6: index-store-all
2025-07-10 15:14:09,340 - pipeline.core - INFO - 开始执行步骤: index-store-all
2025-07-10 15:14:09,341 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-10 15:14:09,341 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-10 15:14:09,341 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-10 15:14:09,341 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-10 15:14:09,341 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-10 15:14:09,341 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation' 生成索引...
2025-07-10 15:14:09,345 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-10 15:14:09,346 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-10 15:14:09,346 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit' 生成索引...
2025-07-10 15:14:09,352 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-10 15:14:21,914 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapFoundation and configuration Debug
(1 failure)

2025-07-10 15:14:21,915 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-10 15:14:24,364 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme DragonMapKit and configuration Debug
(1 failure)

2025-07-10 15:14:24,365 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-10 15:14:25,248 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-10 15:14:25,248 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-10 15:14:25,370 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-10 15:14:25,371 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-10 15:14:25,401 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-10 15:14:25,401 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-10 15:14:25,473 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-10 15:14:25,473 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-10 15:14:25,618 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-10 15:14:25,619 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-10 15:14:30,725 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-10 15:14:30,726 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-10 15:14:36,199 - pipeline.steps.index_store_step_all - ERROR - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 构建失败: 
2025-07-10 15:14:36,768 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-10 15:14:37,347 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: ** BUILD INTERRUPTED **


2025-07-10 15:14:37,358 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败: ** BUILD INTERRUPTED **


The following build commands failed:
	PhaseScriptExecution [CP]\ Copy\ XCFrameworks /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Intermediates.noindex/GLMapLibBusiness.build/Debug-iphoneos/GLMapLibBusiness.build/Script-72E5E500000240.sh (in target 'GLMapLibBusiness' from project 'GLMapLibBusiness')
	Building workspace TencentMap with scheme QMapRouteSearchKit and configuration Debug
(2 failures)

2025-07-10 15:14:37,433 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: ** BUILD INTERRUPTED **


2025-07-10 15:14:37,529 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: ** BUILD INTERRUPTED **


2025-07-10 15:14:37,551 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: ** BUILD INTERRUPTED **


2025-07-10 15:14:37,575 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: ** BUILD INTERRUPTED **


2025-07-10 15:14:37,575 - pipeline.core - ERROR - 流水线执行异常
Traceback (most recent call last):
  File "/Users/<USER>/GRAG_iOS/pipeline/core.py", line 118, in run
    result = self.execute()
  File "/Users/<USER>/GRAG_iOS/pipeline/steps/index_store_step_all.py", line 62, in execute
    results = self.process_schemes_parallel(all_schemes)
  File "/Users/<USER>/GRAG_iOS/pipeline/steps/index_store_step_all.py", line 163, in process_schemes_parallel
    for future in as_completed(futures):
                  ~~~~~~~~~~~~^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/_base.py", line 243, in as_completed
    waiter.event.wait(wait_timeout)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 659, in wait
    signaled = self._cond.wait(timeout)
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 359, in wait
    waiter.acquire()
    ~~~~~~~~~~~~~~^^
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/GRAG_iOS/pipeline/core.py", line 317, in run
    result = step.run()
  File "/Users/<USER>/GRAG_iOS/pipeline/core.py", line 140, in run
    result.duration = self.end_time - self.start_time
    ^^^^^^
UnboundLocalError: cannot access local variable 'result' where it is not associated with a value
2025-07-10 15:14:37,578 - __main__ - ERROR - ❌ Pipeline-All执行失败: Pipeline执行失败
