2025-07-09 01:52:41,461 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 01:52:41,462 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 01:52:41,462 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 01:52:41,495 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 01:52:41,498 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 01:52:41,560 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 01:52:41,653 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 01:52:41,666 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 01:52:41,792 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 01:52:41,907 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 01:52:42,189 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 01:52:42,781 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 01:52:43,040 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 01:52:43,112 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 01:52:43,112 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 01:52:43,113 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 01:52:43,113 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 01:52:43,113 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 01:52:43,113 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 01:52:43,113 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 01:52:43,113 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 01:52:43,113 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 01:52:43,113 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 01:52:43,113 - pipeline.steps.clean_step - ERROR - 清理过程中发生错误: 'PipelineAllConfig' object has no attribute 'derived_data_path'
2025-07-09 01:52:43,113 - pipeline.core - ERROR - 步骤 clean 执行失败: 清理失败: 'PipelineAllConfig' object has no attribute 'derived_data_path'
2025-07-09 01:52:43,113 - pipeline.core - ERROR - 步骤 clean 执行失败，停止流水线
2025-07-09 01:52:43,113 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
2025-07-09 01:53:15,423 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 01:53:15,423 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 01:53:15,423 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 01:53:15,456 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 01:53:15,459 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 01:53:15,524 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 01:53:15,604 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 01:53:15,617 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 01:53:15,744 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 01:53:15,855 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 01:53:16,120 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 01:53:16,715 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 01:53:16,971 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 01:53:17,042 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 01:53:17,042 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 01:53:17,043 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 01:53:17,043 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 01:53:17,043 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 01:53:17,043 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 01:53:17,043 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 01:53:17,043 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 01:53:17,043 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 01:53:17,043 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 01:53:17,043 - pipeline.steps.clean_step - ERROR - 清理过程中发生错误: 'PipelineAllConfig' object has no attribute 'index_symbols_file'
2025-07-09 01:53:17,043 - pipeline.core - ERROR - 步骤 clean 执行失败: 清理失败: 'PipelineAllConfig' object has no attribute 'index_symbols_file'
2025-07-09 01:53:17,043 - pipeline.core - ERROR - 步骤 clean 执行失败，停止流水线
2025-07-09 01:53:17,043 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
2025-07-09 01:54:00,249 - __main__ - INFO - 🚀 开始运行Pipeline-All完整流程...
2025-07-09 01:54:00,249 - __main__ - INFO - 📍 阶段1: 仓库发现和配置
2025-07-09 01:54:00,249 - pipeline.repository_discovery - INFO - 🔍 开始扫描 hammmer-workspace 目录...
2025-07-09 01:54:00,286 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapRouteSearchKit
2025-07-09 01:54:00,288 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DKProtocolsPool
2025-07-09 01:54:00,353 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapFoundation
2025-07-09 01:54:00,438 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapUIKit
2025-07-09 01:54:00,451 - pipeline.repository_discovery - INFO - ✅ 发现仓库: TencentMap
2025-07-09 01:54:00,572 - pipeline.repository_discovery - INFO - ✅ 发现仓库: DragonMapKit
2025-07-09 01:54:00,686 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapHippy
2025-07-09 01:54:00,956 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBaseline
2025-07-09 01:54:01,546 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapMiddlePlatform
2025-07-09 01:54:01,812 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapBusiness
2025-07-09 01:54:01,882 - pipeline.repository_discovery - INFO - ✅ 发现仓库: QMapNaviKit
2025-07-09 01:54:01,882 - pipeline.repository_discovery - INFO - 🎉 发现 11 个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - ✅ 发现并配置了 11 个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📊 总源文件数: 4,049
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 business: 1个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 ui_module: 6个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 foundation: 2个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 core_module: 1个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📁 tools: 1个仓库
2025-07-09 01:54:01,883 - __main__ - INFO - 📍 阶段2: 创建Pipeline实例
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: clean
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: index-store-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: usrs-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: cdb-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: ast-all
2025-07-09 01:54:01,883 - pipeline.core - INFO - 添加步骤: stats-all
2025-07-09 01:54:01,883 - __main__ - INFO - ✅ Pipeline实例创建完成，包含6个步骤
2025-07-09 01:54:01,883 - __main__ - INFO - 📍 阶段3: 执行Pipeline
2025-07-09 01:54:01,883 - pipeline.core - INFO - 开始运行流水线: QMap代码图谱生成流水线-All版本
2025-07-09 01:54:01,883 - pipeline.core - INFO - 执行步骤 1/6: clean
2025-07-09 01:54:01,883 - pipeline.core - INFO - 开始执行步骤: clean
2025-07-09 01:54:01,883 - pipeline.steps.clean_step - INFO - 开始清理流水线历史产物
2025-07-09 01:54:07,084 - pipeline.steps.clean_step - INFO - 删除目录: DerivedData/GraphIndex
2025-07-09 01:54:07,084 - pipeline.steps.clean_step - INFO - 删除目录: ast_out_index_all
2025-07-09 01:54:07,085 - pipeline.steps.clean_step - INFO - 删除目录: compile_commands
2025-07-09 01:54:07,085 - pipeline.steps.clean_step - INFO - 删除文件: compile_commands.json
2025-07-09 01:54:07,085 - pipeline.core - INFO - 步骤 clean 执行成功: 清理完成，删除了 61754 个项目，释放 9064538266 字节
2025-07-09 01:54:07,085 - pipeline.core - INFO - 执行步骤 2/6: index-store-all
2025-07-09 01:54:07,085 - pipeline.core - INFO - 开始执行步骤: index-store-all
2025-07-09 01:54:07,085 - pipeline.steps.index_store_step_all - INFO - 开始生成多仓库Clang索引存储
2025-07-09 01:54:07,085 - pipeline.steps.index_store_step_all - INFO - 发现 16 个schemes需要处理
2025-07-09 01:54:07,085 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBusiness 的scheme 'QMapBusiness' 生成索引...
2025-07-09 01:54:07,086 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 生成索引...
2025-07-09 01:54:07,086 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapBaseline 的scheme 'QMapBaseline' 生成索引...
2025-07-09 01:54:07,088 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapFoundation 的scheme 'QMapFoundation-Example' 生成索引...
2025-07-09 01:54:07,088 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapNaviKit 的scheme 'QMapNaviKit' 生成索引...
2025-07-09 01:54:07,088 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapHippy 的scheme 'QMapHippy' 生成索引...
2025-07-09 01:54:07,090 - pipeline.steps.index_store_step_all - INFO - 为仓库 DragonMapKit 的scheme 'DragonMapKit-Example' 生成索引...
2025-07-09 01:54:07,092 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapUIKit 的scheme 'QMapUIKit' 生成索引...
2025-07-09 01:54:11,545 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapFoundation 的scheme 'QMapFoundation-Example' 构建失败: 2025-07-09 01:54:10.417 xcodebuild[25482:1503269] Writing error result bundle to /var/folders/8_/0tvfq7fn43j534cqbz0xyccw0000gn/T/ResultBundle_2025-09-07_01-54-0010.xcresult
xcodebuild: error: The workspace named "TencentMap" does not contain a scheme named "QMapFoundation-Example". The "-list" option can be used to find the names of the schemes in the workspace.

2025-07-09 01:54:11,546 - pipeline.steps.index_store_step_all - INFO - 为仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 生成索引...
2025-07-09 01:54:11,549 - pipeline.steps.index_store_step_all - ERROR - 仓库 DragonMapKit 的scheme 'DragonMapKit-Example' 构建失败: 2025-07-09 01:54:10.421 xcodebuild[25485:1503276] Writing error result bundle to /var/folders/8_/0tvfq7fn43j534cqbz0xyccw0000gn/T/ResultBundle_2025-09-07_01-54-0010.xcresult
xcodebuild: error: The workspace named "TencentMap" does not contain a scheme named "DragonMapKit-Example". The "-list" option can be used to find the names of the schemes in the workspace.

2025-07-09 01:54:11,550 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'BuildInfo' 生成索引...
2025-07-09 01:54:24,861 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapMiddlePlatform 的scheme 'QMapMiddlePlatform' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapMiddlePlatform and configuration Debug
(1 failure)

2025-07-09 01:54:24,862 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntentsUI' 生成索引...
2025-07-09 01:54:25,001 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapHippy 的scheme 'QMapHippy' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapHippy and configuration Debug
(1 failure)

2025-07-09 01:54:25,001 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMWidgetExtension' 生成索引...
2025-07-09 01:54:25,192 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBaseline 的scheme 'QMapBaseline' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBaseline and configuration Debug
(1 failure)

2025-07-09 01:54:25,192 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMNotificationService' 生成索引...
2025-07-09 01:54:25,268 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapNaviKit 的scheme 'QMapNaviKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapNaviKit and configuration Debug
(1 failure)

2025-07-09 01:54:25,268 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'QMIntents' 生成索引...
2025-07-09 01:54:25,601 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'BuildInfo' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme BuildInfo and configuration Debug
(1 failure)

2025-07-09 01:54:25,601 - pipeline.steps.index_store_step_all - INFO - 为仓库 TencentMap 的scheme 'TencentMap' 生成索引...
2025-07-09 01:54:25,660 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapBusiness 的scheme 'QMapBusiness' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapBusiness and configuration Debug
(1 failure)

2025-07-09 01:54:25,661 - pipeline.steps.index_store_step_all - INFO - 为仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 生成索引...
2025-07-09 01:54:27,097 - pipeline.steps.index_store_step_all - ERROR - 仓库 QMapRouteSearchKit 的scheme 'QMapRouteSearchKit' 构建失败: ** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMapRouteSearchKit and configuration Debug
(1 failure)

2025-07-09 01:54:29,693 - pipeline.steps.index_store_step_all - INFO - 仓库 QMapUIKit 的scheme 'QMapUIKit' 索引生成成功
2025-07-09 01:54:33,404 - pipeline.steps.index_store_step_all - INFO - 仓库 DKProtocolsPool 的scheme 'DKProtocolsPool' 索引生成成功
2025-07-09 01:54:45,792 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMNotificationService' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMNotificationService and configuration Debug
(1 failure)

2025-07-09 01:54:46,187 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMWidgetExtension' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMWidgetExtension and configuration Debug
(1 failure)

2025-07-09 01:54:46,271 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntentsUI' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme QMIntentsUI and configuration Debug
(1 failure)

2025-07-09 01:54:46,428 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'TencentMap' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Building workspace TencentMap with scheme TencentMap and configuration Debug
(1 failure)

2025-07-09 01:57:38,478 - pipeline.steps.index_store_step_all - ERROR - 仓库 TencentMap 的scheme 'QMIntents' 构建失败: --- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
{ platform:iOS Simulator, id:F228D6DA-1F3D-453C-9BE0-895BAE5D80D7, OS:18.1, name:iPhone 15 }
** BUILD FAILED **


The following build commands failed:
	Ld /Users/<USER>/GRAG_iOS/ast_out_index_all/DerivedData/Build/Products/Debug-iphonesimulator/TencentMap.app/TencentMap normal (in target 'TencentMap' from project 'TencentMap')
	Building workspace TencentMap with scheme QMIntents and configuration Debug
(2 failures)

2025-07-09 01:57:38,483 - pipeline.steps.index_store_step_all - INFO - 验证索引存储输出路径: ast_out_index_all/DerivedData/Index.noindex/DataStore
2025-07-09 01:57:38,549 - pipeline.steps.index_store_step_all - WARNING - 未找到索引文件
2025-07-09 01:57:38,549 - pipeline.core - ERROR - 步骤 index-store-all 执行失败: 索引生成部分完成，成功: 2, 失败: 14
2025-07-09 01:57:38,549 - pipeline.core - ERROR - 步骤 index-store-all 执行失败，停止流水线
2025-07-09 01:57:38,549 - __main__ - ERROR - ❌ Pipeline-All执行失败: 'bool' object has no attribute 'success'
