#!/usr/bin/env python3
"""
Analyze Method-File Relationship

This script analyzes the relationship between Method nodes and File nodes.
"""

from neo4j import GraphDatabase
from pathlib import Path

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 深入分析Method节点和File节点的关系')
        print('=' * 50)
        
        # 检查Method节点总数
        result = session.run('MATCH (m:Method) RETURN count(m) as method_count')
        method_count = result.single()['method_count']
        print(f'📊 Method节点总数: {method_count:,}')
        
        # 检查有DEFINES关系的Method节点
        result = session.run('MATCH (f:File)-[:DEFINES]->(m:Method) RETURN count(m) as defined_methods')
        defined_methods = result.single()['defined_methods']
        print(f'📊 有DEFINES关系的Method节点: {defined_methods:,}')
        
        # 检查Method节点的file属性
        result = session.run('''
            MATCH (m:Method) 
            WHERE m.file IS NOT NULL
            RETURN count(m) as methods_with_file_attr
        ''')
        methods_with_file_attr = result.single()['methods_with_file_attr']
        print(f'📊 有file属性的Method节点: {methods_with_file_attr:,}')
        
        # 检查file属性为external的Method节点
        result = session.run('''
            MATCH (m:Method) 
            WHERE m.file = 'external'
            RETURN count(m) as external_methods
        ''')
        external_methods = result.single()['external_methods']
        print(f'📊 file属性为external的Method节点: {external_methods:,}')
        
        # 检查Method节点file属性的分布
        result = session.run('''
            MATCH (m:Method) 
            WHERE m.file IS NOT NULL
            RETURN m.file as file_path, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('\n📊 Method节点file属性分布 (Top 10):')
        for record in result:
            file_path = record['file_path']
            count = record['count']
            if file_path == 'external':
                print(f'  external: {count:,} 个方法')
            else:
                print(f'  {Path(file_path).name}: {count:,} 个方法')
        
        # 检查DEFINES关系的完整性
        result = session.run('''
            MATCH (m:Method)
            OPTIONAL MATCH (f:File)-[:DEFINES]->(m)
            RETURN 
                CASE WHEN f IS NULL THEN 'no_defines_relation' ELSE 'has_defines_relation' END as relation_status,
                count(m) as count
        ''')
        
        print('\n📊 Method节点DEFINES关系状态:')
        for record in result:
            status = record['relation_status']
            count = record['count']
            print(f'  {status}: {count:,} 个方法')
        
        # 检查File节点和Method节点file属性的匹配情况
        result = session.run('''
            MATCH (f:File)
            MATCH (m:Method)
            WHERE m.file = f.name
            RETURN count(m) as matched_methods
        ''')
        matched_methods = result.single()['matched_methods']
        print(f'\n🔗 file属性与File节点匹配的Method: {matched_methods:,}')
        
        # 分析跨文件调用的真实情况
        result = session.run('''
            MATCH (m1:Method)-[:CALLS]->(m2:Method)
            WHERE m1.file IS NOT NULL AND m2.file IS NOT NULL
            AND m1.file <> m2.file AND m2.file <> 'external'
            RETURN m1.file as caller_file, m2.file as callee_file, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('\n🌐 真实的跨文件调用 (排除external):')
        real_cross_file_calls = 0
        for record in result:
            caller_file = Path(record['caller_file']).name
            callee_file = Path(record['callee_file']).name
            count = record['count']
            real_cross_file_calls += count
            print(f'  {caller_file} -> {callee_file}: {count:,} 条')
        
        print(f'\n📈 真实跨文件调用总数: {real_cross_file_calls:,}')

    driver.close()

if __name__ == "__main__":
    main()
