#!/usr/bin/env python3
"""
Diagnose Cross-File Dependencies

This script diagnoses why cross-file dependency detection is failing.
"""

from neo4j import GraphDatabase
import json
from pathlib import Path

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 诊断跨文件依赖关系检测失败原因')
        print('=' * 60)
        
        # 1. 检查文件节点分布
        result = session.run('MATCH (f:File) RETURN count(f) as file_count')
        file_count = result.single()['file_count']
        print(f'📁 文件节点总数: {file_count:,}')
        
        # 2. 检查跨文件的CALLS关系
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_calls
        ''')
        cross_file_calls = result.single()['cross_file_calls']
        print(f'🌐 跨文件方法调用: {cross_file_calls:,} 条')
        
        # 3. 检查同文件内的CALLS关系
        result = session.run('''
            MATCH (f:File)-[:DEFINES]->(m1:Method)-[:CALLS]->(m2:Method)<-[:DEFINES]-(f)
            RETURN count(*) as same_file_calls
        ''')
        same_file_calls = result.single()['same_file_calls']
        print(f'📄 同文件内方法调用: {same_file_calls:,} 条')
        
        # 4. 检查Method节点的文件分布
        result = session.run('''
            MATCH (f:File)-[:DEFINES]->(m:Method)
            RETURN f.name as file_name, count(m) as method_count
            ORDER BY method_count DESC
            LIMIT 10
        ''')
        
        print('\n📊 文件的方法定义统计 (Top 10):')
        for record in result:
            file_name = record['file_name']
            method_count = record['method_count']
            print(f'  {Path(file_name).name}: {method_count} 个方法')
        
        # 5. 分析CALLS关系的文件归属问题
        result = session.run('''
            MATCH (caller)-[:CALLS]->(callee:Method)
            MATCH (f:File)-[:DEFINES]->(callee)
            RETURN labels(caller) as caller_labels, f.name as callee_file, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('\n📊 CALLS关系的调用者类型分析:')
        for record in result:
            caller_labels = ', '.join(record['caller_labels'])
            callee_file = Path(record['callee_file']).name
            count = record['count']
            print(f'  {caller_labels} -> {callee_file}: {count:,} 条')
        
        # 6. 检查是否存在跨文件的ACCESSES关系
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(accessor)-[:ACCESSES]->(property:Property)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as cross_file_accesses
        ''')
        cross_file_accesses = result.single()['cross_file_accesses']
        print(f'\n🔗 跨文件属性访问: {cross_file_accesses:,} 条')
        
        # 7. 分析Method节点的调用者信息
        result = session.run('''
            MATCH (m:Method)-[:CALLS]->(target:Method)
            WHERE m.file IS NOT NULL AND target.file IS NOT NULL
            AND m.file <> target.file
            RETURN m.file as caller_file, target.file as callee_file, count(*) as count
            ORDER BY count DESC
            LIMIT 10
        ''')
        
        print('\n🌐 Method节点间的跨文件调用:')
        cross_file_method_calls = 0
        for record in result:
            caller_file = Path(record['caller_file']).name if record['caller_file'] else 'unknown'
            callee_file = Path(record['callee_file']).name if record['callee_file'] else 'unknown'
            count = record['count']
            cross_file_method_calls += count
            print(f'  {caller_file} -> {callee_file}: {count:,} 条')
        
        print(f'\n📈 Method节点间跨文件调用总数: {cross_file_method_calls:,}')
        
        # 8. 检查编译数据库信息是否被保留
        result = session.run('''
            MATCH (f:File)
            WHERE f.compile_command IS NOT NULL
            RETURN count(f) as files_with_compile_info
        ''')
        files_with_compile_info = result.single()['files_with_compile_info']
        print(f'\n🔧 包含编译信息的文件: {files_with_compile_info:,}')
        
        # 9. 分析IMPORTS关系
        result = session.run('''
            MATCH (f1:File)-[:IMPORTS]->(f2:File)
            WHERE f1 <> f2
            RETURN count(*) as import_relationships
        ''')
        import_relationships = result.single()['import_relationships']
        print(f'📥 IMPORTS关系: {import_relationships:,} 条')
        
        # 10. 检查具体的跨文件调用示例
        result = session.run('''
            MATCH (m1:Method)-[:CALLS]->(m2:Method)
            WHERE m1.file IS NOT NULL AND m2.file IS NOT NULL
            AND m1.file <> m2.file
            RETURN m1.name as caller_method, m1.class as caller_class, m1.file as caller_file,
                   m2.name as callee_method, m2.class as callee_class, m2.file as callee_file
            LIMIT 5
        ''')
        
        print('\n🔍 跨文件调用示例:')
        for record in result:
            caller_method = record['caller_method']
            caller_class = record['caller_class']
            caller_file = Path(record['caller_file']).name if record['caller_file'] else 'unknown'
            callee_method = record['callee_method']
            callee_class = record['callee_class']
            callee_file = Path(record['callee_file']).name if record['callee_file'] else 'unknown'
            
            print(f'  {caller_class}::{caller_method} ({caller_file})')
            print(f'    -> {callee_class}::{callee_method} ({callee_file})')
            print()

    driver.close()

if __name__ == "__main__":
    main()
