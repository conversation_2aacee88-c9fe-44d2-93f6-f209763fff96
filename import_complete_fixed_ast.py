#!/usr/bin/env python3
"""
Import Complete Fixed AST Data to Neo4j

This script clears the Neo4j database and imports the complete fixed AST data.
"""

import json
from pathlib import Path
from neo4j import GraphDatabase

def main():
    print('🚀 导入完整修复后的AST数据到Neo4j')
    print('=' * 60)
    
    # Connect to Neo4j
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    with driver.session() as session:
        # Clear database
        print('🧹 清理Neo4j数据库...')
        session.run('MATCH (n) DETACH DELETE n')
        print('✅ 数据库已清理')
        
        # Import nodes
        print('📦 导入节点...')
        nodes_count = 0
        nodes_file = Path('test_p0_fix/nodes.jsonl')
        
        if not nodes_file.exists():
            print(f'❌ 节点文件不存在: {nodes_file}')
            return
        
        with open(nodes_file, 'r') as f:
            for line in f:
                if line.strip():
                    node = json.loads(line.strip())
                    node_id = node['id']
                    label = node['label']
                    attrs = node.get('attrs', {})
                    
                    # Create node
                    query = f'''
                    MERGE (n:{label} {{id: $id}})
                    SET n += $attrs
                    '''
                    session.run(query, id=node_id, attrs=attrs)
                    nodes_count += 1
                    
                    if nodes_count % 10000 == 0:
                        print(f'  已导入 {nodes_count} 个节点...')
        
        print(f'✅ 导入了 {nodes_count} 个节点')
        
        # Import edges
        print('🔗 导入关系...')
        edges_count = 0
        edge_type_counts = {}
        edges_file = Path('test_p0_fix/edges.jsonl')
        
        if not edges_file.exists():
            print(f'❌ 关系文件不存在: {edges_file}')
            return
        
        with open(edges_file, 'r') as f:
            for line in f:
                if line.strip():
                    edge = json.loads(line.strip())
                    edge_type = edge['type']
                    src_id = edge['src']
                    dst_id = edge['dst']
                    attrs = edge.get('attrs', {})
                    
                    # Create relationship
                    query = f'''
                    MATCH (src {{id: $src_id}})
                    MATCH (dst {{id: $dst_id}})
                    MERGE (src)-[r:{edge_type}]->(dst)
                    SET r += $attrs
                    '''
                    session.run(query, src_id=src_id, dst_id=dst_id, attrs=attrs)
                    edges_count += 1
                    edge_type_counts[edge_type] = edge_type_counts.get(edge_type, 0) + 1
                    
                    if edges_count % 10000 == 0:
                        print(f'  已导入 {edges_count} 条关系...')
        
        print(f'✅ 导入了 {edges_count} 条关系')
        print(f'📊 关系类型分布:')
        for edge_type, count in sorted(edge_type_counts.items(), key=lambda x: x[1], reverse=True):
            print(f'  {edge_type}: {count:,}')
        
        # Verify results
        print()
        print('🔍 验证导入结果...')
        
        # Check node count
        result = session.run('MATCH (n) RETURN count(n) as total_nodes')
        total_nodes = result.single()['total_nodes']
        print(f'✅ 总节点数: {total_nodes:,}')
        
        # Check relationship count
        result = session.run('MATCH ()-[r]->() RETURN count(r) as total_rels')
        total_rels = result.single()['total_rels']
        print(f'✅ 总关系数: {total_rels:,}')
        
        # Check relationship types
        result = session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC')
        print('🔗 Neo4j中的关系类型分布:')
        for record in result:
            print(f'  {record["rel_type"]}: {record["count"]:,}')
        
        # Check node types
        result = session.run('MATCH (n) RETURN labels(n) as labels, count(n) as count ORDER BY count DESC')
        print('📊 Neo4j中的节点类型分布:')
        for record in result:
            labels = ', '.join(record['labels'])
            print(f'  {labels}: {record["count"]:,}')
    
    driver.close()
    print()
    print('🎉 完整修复后的AST数据导入完成！')
    print('📈 成果总结:')
    print(f'  ✅ 成功导入 {nodes_count:,} 个节点')
    print(f'  ✅ 成功导入 {edges_count:,} 条关系')
    print(f'  ✅ 包含 {len(edge_type_counts)} 种关系类型')
    print('  ✅ 所有CALLS、ACCESSES、INHERITS、IMPLEMENTS关系都已正确导入')

if __name__ == "__main__":
    main()
