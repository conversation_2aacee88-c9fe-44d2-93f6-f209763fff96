#!/usr/bin/env python3
"""
Performance Comparison Test

This script compares the performance of the original vs optimized import methods.
"""

import time
import json
from pathlib import Path
from neo4j import GraphDatabase
import subprocess
import sys

class PerformanceComparison:
    
    def __init__(self):
        self.driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
        self.results = {}
    
    def close(self):
        self.driver.close()
    
    def clear_database(self):
        """Clear database for clean testing"""
        with self.driver.session() as session:
            session.run('MATCH (n) DETACH DELETE n')
    
    def count_data_files(self):
        """Count items in data files for baseline metrics"""
        nodes_file = Path('test_p0_fix/nodes.jsonl')
        edges_file = Path('test_p0_fix/edges.jsonl')
        
        nodes_count = 0
        edges_count = 0
        
        if nodes_file.exists():
            with open(nodes_file, 'r') as f:
                for line in f:
                    if line.strip():
                        nodes_count += 1
        
        if edges_file.exists():
            with open(edges_file, 'r') as f:
                for line in f:
                    if line.strip():
                        edges_count += 1
        
        return nodes_count, edges_count
    
    def verify_data_integrity(self, test_name):
        """Verify data was imported correctly"""
        with self.driver.session() as session:
            # Check node count
            result = session.run('MATCH (n) RETURN count(n) as total_nodes')
            total_nodes = result.single()['total_nodes']
            
            # Check relationship count
            result = session.run('MATCH ()-[r]->() RETURN count(r) as total_rels')
            total_rels = result.single()['total_rels']
            
            # Check for data corruption (nodes without required properties)
            result = session.run('MATCH (n) WHERE n.id IS NULL RETURN count(n) as corrupt_nodes')
            corrupt_nodes = result.single()['corrupt_nodes']
            
            integrity_score = 100.0 if corrupt_nodes == 0 else (total_nodes - corrupt_nodes) / total_nodes * 100
            
            self.results[test_name]['total_nodes'] = total_nodes
            self.results[test_name]['total_rels'] = total_rels
            self.results[test_name]['corrupt_nodes'] = corrupt_nodes
            self.results[test_name]['integrity_score'] = integrity_score
            
            return total_nodes, total_rels, integrity_score
    
    def test_original_import(self):
        """Test original import method performance"""
        print('🔍 测试原始导入方法性能...')
        print('-' * 50)
        
        self.clear_database()
        
        # Record start time
        start_time = time.time()
        
        # Run original import script
        try:
            result = subprocess.run([
                sys.executable, 'import_complete_fixed_ast.py'
            ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            if result.returncode != 0:
                print(f'❌ 原始导入失败: {result.stderr}')
                return None
                
        except subprocess.TimeoutExpired:
            print('❌ 原始导入超时 (>1小时)')
            return None
        
        # Record end time
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Store results
        self.results['original'] = {
            'elapsed_time': elapsed_time,
            'method': 'Single-transaction per item',
            'status': 'completed'
        }
        
        # Verify data integrity
        total_nodes, total_rels, integrity = self.verify_data_integrity('original')
        
        print(f'✅ 原始方法完成')
        print(f'  ⏱️ 耗时: {elapsed_time:.1f} 秒')
        print(f'  📊 节点: {total_nodes:,}')
        print(f'  🔗 关系: {total_rels:,}')
        print(f'  ✅ 完整性: {integrity:.1f}%')
        
        return elapsed_time
    
    def test_optimized_import(self):
        """Test optimized import method performance"""
        print('\n🚀 测试优化导入方法性能...')
        print('-' * 50)
        
        self.clear_database()
        
        # Record start time
        start_time = time.time()
        
        # Run optimized import script
        try:
            result = subprocess.run([
                sys.executable, 'import_optimized_performance.py'
            ], capture_output=True, text=True, timeout=1800)  # 30 minutes timeout
            
            if result.returncode != 0:
                print(f'❌ 优化导入失败: {result.stderr}')
                return None
                
        except subprocess.TimeoutExpired:
            print('❌ 优化导入超时 (>30分钟)')
            return None
        
        # Record end time
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Store results
        self.results['optimized'] = {
            'elapsed_time': elapsed_time,
            'method': 'Batch processing with UNWIND',
            'status': 'completed'
        }
        
        # Verify data integrity
        total_nodes, total_rels, integrity = self.verify_data_integrity('optimized')
        
        print(f'✅ 优化方法完成')
        print(f'  ⏱️ 耗时: {elapsed_time:.1f} 秒')
        print(f'  📊 节点: {total_nodes:,}')
        print(f'  🔗 关系: {total_rels:,}')
        print(f'  ✅ 完整性: {integrity:.1f}%')
        
        return elapsed_time
    
    def generate_performance_report(self):
        """Generate comprehensive performance comparison report"""
        print('\n' + '=' * 60)
        print('📊 性能对比报告')
        print('=' * 60)
        
        if 'original' not in self.results or 'optimized' not in self.results:
            print('❌ 无法生成报告：缺少测试数据')
            return
        
        original = self.results['original']
        optimized = self.results['optimized']
        
        # Calculate improvements
        time_improvement = (original['elapsed_time'] - optimized['elapsed_time']) / original['elapsed_time'] * 100
        speed_ratio = original['elapsed_time'] / optimized['elapsed_time']
        
        # Data file metrics
        nodes_count, edges_count = self.count_data_files()
        total_items = nodes_count + edges_count
        
        print(f'📁 数据规模:')
        print(f'  节点文件: {nodes_count:,} 个节点')
        print(f'  关系文件: {edges_count:,} 条关系')
        print(f'  总计: {total_items:,} 个数据项')
        
        print(f'\n⏱️ 性能对比:')
        print(f'  原始方法: {original["elapsed_time"]:.1f} 秒')
        print(f'  优化方法: {optimized["elapsed_time"]:.1f} 秒')
        print(f'  时间节省: {time_improvement:.1f}%')
        print(f'  速度提升: {speed_ratio:.1f}x')
        
        print(f'\n📊 吞吐量对比:')
        original_rate = total_items / original['elapsed_time']
        optimized_rate = total_items / optimized['elapsed_time']
        print(f'  原始方法: {original_rate:.0f} 项目/秒')
        print(f'  优化方法: {optimized_rate:.0f} 项目/秒')
        print(f'  吞吐量提升: {optimized_rate/original_rate:.1f}x')
        
        print(f'\n✅ 数据完整性验证:')
        print(f'  原始方法: {original["integrity_score"]:.1f}%')
        print(f'  优化方法: {optimized["integrity_score"]:.1f}%')
        
        # Success criteria check
        print(f'\n🎯 优化目标达成情况:')
        success_criteria = [
            ('导入速度提升≥50%', time_improvement >= 50, f'{time_improvement:.1f}%'),
            ('数据完整性=100%', optimized['integrity_score'] == 100, f'{optimized["integrity_score"]:.1f}%'),
            ('节点数量一致', original['total_nodes'] == optimized['total_nodes'], 
             f'{original["total_nodes"]:,} vs {optimized["total_nodes"]:,}'),
            ('关系数量一致', original['total_rels'] == optimized['total_rels'],
             f'{original["total_rels"]:,} vs {optimized["total_rels"]:,}')
        ]
        
        all_passed = True
        for criterion, passed, value in success_criteria:
            status = '✅' if passed else '❌'
            print(f'  {status} {criterion}: {value}')
            if not passed:
                all_passed = False
        
        print(f'\n🏆 总体评估: {"✅ 优化成功" if all_passed else "❌ 需要进一步优化"}')
        
        return all_passed

def main():
    print('🧪 Neo4j导入性能对比测试')
    print('=' * 60)
    
    # Check if data files exist
    nodes_file = Path('test_p0_fix/nodes.jsonl')
    edges_file = Path('test_p0_fix/edges.jsonl')
    
    if not nodes_file.exists() or not edges_file.exists():
        print('❌ 数据文件不存在，请先运行AST提取器')
        return
    
    comparison = PerformanceComparison()
    
    try:
        # Test original method (with timeout protection)
        print('⚠️ 注意：原始方法可能需要很长时间，设置了1小时超时')
        original_time = comparison.test_original_import()
        
        if original_time is None:
            print('❌ 原始方法测试失败，跳过对比')
            return
        
        # Test optimized method
        optimized_time = comparison.test_optimized_import()
        
        if optimized_time is None:
            print('❌ 优化方法测试失败')
            return
        
        # Generate comprehensive report
        success = comparison.generate_performance_report()
        
        if success:
            print('\n🎉 性能优化验证成功！')
        else:
            print('\n⚠️ 性能优化需要进一步调整')
            
    finally:
        comparison.close()

if __name__ == "__main__":
    main()
