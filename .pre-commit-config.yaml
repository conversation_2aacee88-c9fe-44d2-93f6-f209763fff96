# Pre-commit hooks配置
# 安装: pip install pre-commit && pre-commit install

repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: debug-statements

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black]

  # Python代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports]

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, pipeline/, -f, json, -o, bandit-report.json]
        pass_filenames: false

  # 自定义测试钩子
  - repo: local
    hooks:
      - id: run-tests
        name: Run unit tests
        entry: python run_tests.py --unit --no-parallel
        language: system
        pass_filenames: false
        stages: [commit]

      - id: check-pipeline-config
        name: Validate pipeline configuration
        entry: python -c "from pipeline.config import PipelineConfig; PipelineConfig().validate()"
        language: system
        pass_filenames: false
        files: ^pipeline/config\.py$

      - id: check-imports
        name: Check pipeline imports
        entry: python -c "import pipeline; print('Pipeline imports OK')"
        language: system
        pass_filenames: false
        files: ^pipeline/.*\.py$