#!/usr/bin/env python3
"""
Import Enhanced AST Data to Neo4j

This script imports the enhanced AST extraction results into Neo4j database,
replacing the previous low-quality data with high-quality relationship detection.
"""

from neo4j import GraphDatabase
import json
import sys
from pathlib import Path

def main():
    # Connect to Neo4j
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))
    
    print('🚀 导入增强型AST数据到Neo4j')
    print('=' * 50)
    
    try:
        with driver.session() as session:
            # Clear existing data completely
            print('🧹 完全清理现有数据...')

            # Drop all constraints first
            try:
                result = session.run('SHOW CONSTRAINTS')
                for record in result:
                    constraint_name = record.get('name', '')
                    if constraint_name:
                        try:
                            session.run(f'DROP CONSTRAINT {constraint_name}')
                        except:
                            pass
            except:
                pass

            # Delete all data
            session.run('MATCH ()-[r]->() DELETE r')
            session.run('MATCH (n) DELETE n')
            print('✅ 数据清理完成')

            # Create new constraints
            print('📋 创建新的约束和索引...')
            session.run('CREATE CONSTRAINT enhanced_file_id FOR (n:File) REQUIRE n.id IS UNIQUE')
            session.run('CREATE CONSTRAINT enhanced_class_id FOR (n:Class) REQUIRE n.id IS UNIQUE')
            session.run('CREATE CONSTRAINT enhanced_method_id FOR (n:Method) REQUIRE n.id IS UNIQUE')
            session.run('CREATE CONSTRAINT enhanced_property_id FOR (n:Property) REQUIRE n.id IS UNIQUE')
            session.run('CREATE CONSTRAINT enhanced_protocol_id FOR (n:Protocol) REQUIRE n.id IS UNIQUE')
            session.run('CREATE CONSTRAINT enhanced_enum_id FOR (n:Enum) REQUIRE n.id IS UNIQUE')
            print('✅ 约束和索引创建完成')
            
            # Import nodes with MERGE to handle duplicates
            print('📥 导入节点...')
            nodes_count = 0
            nodes_file = Path('test_enhanced_ast/nodes.jsonl')

            if not nodes_file.exists():
                print(f'❌ 节点文件不存在: {nodes_file}')
                return

            imported_ids = set()
            with open(nodes_file, 'r') as f:
                for line in f:
                    if line.strip():
                        node = json.loads(line.strip())
                        label = node['label']
                        node_id = node['id']
                        attrs = node['attrs']

                        # Skip duplicates
                        if node_id in imported_ids:
                            continue
                        imported_ids.add(node_id)

                        # Use MERGE to handle any remaining duplicates
                        query = f'MERGE (n:{label} {{id: $id}}) SET n += $attrs'
                        session.run(query, id=node_id, attrs=attrs)
                        nodes_count += 1

                        if nodes_count % 10000 == 0:
                            print(f'  已导入 {nodes_count} 个节点...')

            print(f'✅ 导入了 {nodes_count} 个节点')
            
            # Import edges with error handling
            print('🔗 导入关系...')
            edges_count = 0
            edges_file = Path('test_enhanced_ast/edges.jsonl')

            if not edges_file.exists():
                print(f'❌ 关系文件不存在: {edges_file}')
                return

            with open(edges_file, 'r') as f:
                for line in f:
                    if line.strip():
                        edge = json.loads(line.strip())
                        edge_type = edge['type']
                        src_id = edge['src']
                        dst_id = edge['dst']
                        attrs = edge.get('attrs', {})

                        # Create relationship with error handling
                        try:
                            query = f'''
                            MATCH (src {{id: $src_id}})
                            MATCH (dst {{id: $dst_id}})
                            MERGE (src)-[r:{edge_type}]->(dst)
                            SET r += $attrs
                            '''
                            session.run(query, src_id=src_id, dst_id=dst_id, attrs=attrs)
                            edges_count += 1
                        except Exception as e:
                            # Skip edges where nodes don't exist
                            pass

                        if edges_count % 5000 == 0:
                            print(f'  已导入 {edges_count} 条关系...')

            print(f'✅ 导入了 {edges_count} 条关系')
            
            # Verification
            print('\n🔍 验证导入结果...')
            
            # Check node counts by type
            result = session.run('MATCH (n) RETURN labels(n) as labels, count(n) as count ORDER BY count DESC')
            print('📊 节点类型分布:')
            total_nodes = 0
            for record in result:
                count = record['count']
                total_nodes += count
                labels = ', '.join(record['labels'])
                print(f'  {labels}: {count:,}')
            print(f'  总节点数: {total_nodes:,}')
            
            print()
            
            # Check relationship counts by type
            result = session.run('MATCH ()-[r]->() RETURN type(r) as type, count(r) as count ORDER BY count DESC')
            print('🔗 关系类型分布:')
            total_rels = 0
            for record in result:
                count = record['count']
                total_rels += count
                print(f'  {record["type"]}: {count:,}')
            print(f'  总关系数: {total_rels:,}')
            
    except Exception as e:
        print(f'❌ 导入过程中发生错误: {e}')
        return
    
    finally:
        driver.close()
    
    print('\n🎉 增强型AST数据导入完成!')
    print('✅ 高质量代码知识图谱已部署到Neo4j')

if __name__ == "__main__":
    main()
