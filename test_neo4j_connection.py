#!/usr/bin/env python3
"""
测试Neo4j连接
"""

import sys
from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable, AuthError

def test_neo4j_connection():
    """测试Neo4j连接"""
    uri = "bolt://localhost:7687"
    username = "neo4j"
    password = "6536772a"
    
    print(f"🔍 测试Neo4j连接...")
    print(f"URI: {uri}")
    print(f"用户名: {username}")
    print(f"密码: {'*' * len(password)}")
    print()
    
    try:
        # 尝试连接
        driver = GraphDatabase.driver(uri, auth=(username, password))
        
        # 测试查询
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]
            
            if test_value == 1:
                print("✅ Neo4j连接成功!")
                
                # 获取数据库信息
                db_info = session.run("CALL db.info() YIELD name, value RETURN name, value")
                print("\n📊 数据库信息:")
                for record in db_info:
                    print(f"  {record['name']}: {record['value']}")
                
                # 检查现有数据
                node_count = session.run("MATCH (n) RETURN count(n) as count").single()["count"]
                rel_count = session.run("MATCH ()-[r]->() RETURN count(r) as count").single()["count"]
                
                print(f"\n📈 当前数据:")
                print(f"  节点数量: {node_count:,}")
                print(f"  关系数量: {rel_count:,}")
                
                return True
            else:
                print("❌ 连接测试失败")
                return False
                
    except ServiceUnavailable as e:
        print(f"❌ Neo4j服务不可用: {e}")
        print("\n💡 请确保:")
        print("  1. Neo4j Desktop已启动")
        print("  2. 数据库实例正在运行")
        print("  3. 端口7687可访问")
        return False
        
    except AuthError as e:
        print(f"❌ 认证失败: {e}")
        print("\n💡 请检查:")
        print("  1. 用户名是否正确 (通常是 'neo4j')")
        print("  2. 密码是否正确")
        print("  3. 是否需要重置密码")
        return False
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    finally:
        try:
            driver.close()
        except:
            pass

def main():
    """主函数"""
    print("🚀 Neo4j连接测试工具")
    print("=" * 50)
    
    success = test_neo4j_connection()
    
    if success:
        print("\n🎉 Neo4j连接测试成功!")
        print("现在可以运行完整的数据导入:")
        print("  python neo4j_integration/main.py")
    else:
        print("\n❌ Neo4j连接测试失败")
        print("请先解决连接问题再运行数据导入")
        sys.exit(1)

if __name__ == "__main__":
    main()
