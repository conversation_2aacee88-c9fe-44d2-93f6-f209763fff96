#!/usr/bin/env python3
"""
仓库发现脚本 - 分析hammmer-workspace中的所有代码仓库
"""

import os
from pathlib import Path
import json

def discover_repositories():
    """发现所有代码仓库"""
    workspace_dir = Path("hammmer-workspace")
    repositories = []
    
    if not workspace_dir.exists():
        print("❌ hammmer-workspace目录不存在")
        return repositories
    
    print("🔍 扫描hammmer-workspace目录...")
    
    # 排除的目录
    excluded_dirs = {
        "Pods", "__pycache__", "build", "hammer-workspace", 
        ".git", "DerivedData", "TencentMap.xcworkspace"
    }
    
    for item in workspace_dir.iterdir():
        if item.is_dir() and item.name not in excluded_dirs:
            repo_info = analyze_repository(item)
            if repo_info:
                repositories.append(repo_info)
    
    return repositories

def analyze_repository(repo_path: Path):
    """分析单个仓库"""
    repo_name = repo_path.name
    
    print(f"📁 分析仓库: {repo_name}")
    
    # 统计源文件
    m_files = list(repo_path.rglob("*.m"))
    mm_files = list(repo_path.rglob("*.mm"))
    h_files = list(repo_path.rglob("*.h"))
    
    total_source_files = len(m_files) + len(mm_files)
    total_header_files = len(h_files)
    
    # 检查是否有Xcode项目文件
    has_xcodeproj = bool(list(repo_path.rglob("*.xcodeproj")))
    has_podspec = bool(list(repo_path.rglob("*.podspec")))
    
    # 检查是否有主要的源代码目录
    main_source_dirs = []
    for subdir in repo_path.iterdir():
        if subdir.is_dir() and subdir.name == repo_name:
            main_source_dirs.append(str(subdir))
    
    # 只包含有源文件的仓库
    if total_source_files == 0:
        print(f"   ⚠️  跳过 {repo_name}: 无源文件")
        return None
    
    repo_info = {
        "name": repo_name,
        "path": str(repo_path),
        "source_files": total_source_files,
        "header_files": total_header_files,
        "has_xcodeproj": has_xcodeproj,
        "has_podspec": has_podspec,
        "main_source_dirs": main_source_dirs,
        "m_files": [str(f) for f in m_files],
        "mm_files": [str(f) for f in mm_files],
        "h_files": [str(f) for f in h_files]
    }
    
    print(f"   ✅ {repo_name}: {total_source_files} 源文件, {total_header_files} 头文件")
    
    return repo_info

def categorize_repositories(repositories):
    """对仓库进行分类"""
    categories = {
        "core_modules": [],      # 核心模块
        "ui_modules": [],        # UI模块
        "foundation": [],        # 基础模块
        "business": [],          # 业务模块
        "third_party": [],       # 第三方模块
        "tools": []              # 工具模块
    }
    
    for repo in repositories:
        name = repo["name"]
        
        if "Foundation" in name or "Baseline" in name:
            categories["foundation"].append(repo)
        elif "UI" in name or "Kit" in name:
            categories["ui_modules"].append(repo)
        elif "Business" in name:
            categories["business"].append(repo)
        elif "Dragon" in name or "TencentMap" in name:
            categories["core_modules"].append(repo)
        elif "DK" in name or "GRAG" in name:
            categories["tools"].append(repo)
        else:
            categories["core_modules"].append(repo)  # 默认为核心模块
    
    return categories

def print_repository_summary(repositories, categories):
    """打印仓库摘要"""
    print(f"\n{'='*60}")
    print("仓库发现摘要")
    print('='*60)
    
    total_source_files = sum(repo["source_files"] for repo in repositories)
    total_header_files = sum(repo["header_files"] for repo in repositories)
    
    print(f"📊 总体统计:")
    print(f"   发现仓库数: {len(repositories)}")
    print(f"   总源文件数: {total_source_files:,}")
    print(f"   总头文件数: {total_header_files:,}")
    
    print(f"\n📋 仓库分类:")
    for category, repos in categories.items():
        if repos:
            category_name = {
                "core_modules": "核心模块",
                "ui_modules": "UI模块", 
                "foundation": "基础模块",
                "business": "业务模块",
                "third_party": "第三方模块",
                "tools": "工具模块"
            }.get(category, category)
            
            print(f"\n   {category_name} ({len(repos)}个):")
            for repo in repos:
                print(f"     - {repo['name']}: {repo['source_files']} 源文件")

def save_repository_config(repositories):
    """保存仓库配置"""
    config = {
        "repositories": repositories,
        "total_repositories": len(repositories),
        "total_source_files": sum(repo["source_files"] for repo in repositories),
        "discovery_timestamp": "2025-07-09"
    }
    
    with open("pipeline_all_repositories.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 仓库配置已保存到: pipeline_all_repositories.json")

if __name__ == "__main__":
    print("🚀 开始发现hammmer-workspace中的所有代码仓库...")
    
    repositories = discover_repositories()
    categories = categorize_repositories(repositories)
    
    print_repository_summary(repositories, categories)
    save_repository_config(repositories)
    
    print(f"\n✅ 仓库发现完成！")
