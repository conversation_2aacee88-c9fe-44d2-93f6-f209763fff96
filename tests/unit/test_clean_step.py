"""
测试CleanStep清理步骤 - TDD RED阶段

这些测试描述了CleanStep的期望行为，当前应该失败。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from pipeline.steps.clean_step import CleanStep
from pipeline.config import PipelineConfig
from pipeline.core import StepResult


class TestCleanStep:
    """测试CleanStep清理步骤"""

    def test_clean_step_initialization(self, test_config):
        """测试CleanStep初始化"""
        step = CleanStep(test_config)

        assert step.name == "clean"
        assert "清理" in step.description
        assert step.config == test_config

    def test_clean_step_validate_prerequisites_success(self, test_config):
        """测试前置条件验证成功"""
        step = CleanStep(test_config)

        # 清理步骤通常没有前置条件
        assert step.validate_prerequisites() == True

    def test_clean_step_execute_success(self, temp_dir, test_config):
        """测试清理步骤执行成功"""
        # 创建需要清理的目录和文件
        derived_data_dir = temp_dir / "DerivedData" / "GraphIndex"
        derived_data_dir.mkdir(parents=True)
        (derived_data_dir / "test_file.txt").write_text("test content")

        ast_out_dir = temp_dir / "ast_out_index"
        ast_out_dir.mkdir(parents=True)
        (ast_out_dir / "nodes.jsonl").write_text("test nodes")

        compile_commands_dir = temp_dir / "compile_commands"
        compile_commands_dir.mkdir(parents=True)
        (compile_commands_dir / "test.json").write_text("{}")

        index_symbols_file = temp_dir / "index_symbols.jsonl"
        index_symbols_file.write_text("test symbols")

        # 更新配置指向临时目录
        test_config.derived_data_path = str(derived_data_dir.parent.parent)
        test_config.output_dir = str(ast_out_dir)
        test_config.compile_commands_dir = str(compile_commands_dir)
        test_config.index_symbols_file = str(index_symbols_file)

        step = CleanStep(test_config)
        result = step.execute()

        # 验证执行结果
        assert result.success == True
        assert "清理完成" in result.message
        assert result.duration >= 0

        # 验证文件和目录被删除
        assert not derived_data_dir.exists()
        assert not ast_out_dir.exists()
        assert not compile_commands_dir.exists()
        assert not index_symbols_file.exists()

    def test_clean_step_execute_partial_cleanup(self, temp_dir, test_config):
        """测试部分文件存在时的清理"""
        # 只创建部分需要清理的文件
        ast_out_dir = temp_dir / "ast_out_index"
        ast_out_dir.mkdir(parents=True)
        (ast_out_dir / "nodes.jsonl").write_text("test nodes")

        test_config.output_dir = str(ast_out_dir)
        test_config.derived_data_path = str(temp_dir / "nonexistent")

        step = CleanStep(test_config)
        result = step.execute()

        # 应该成功，即使某些文件不存在
        assert result.success == True
        assert not ast_out_dir.exists()

    def test_clean_step_execute_with_permission_error(self, temp_dir, test_config):
        """测试权限错误处理"""
        # 创建一个目录
        protected_dir = temp_dir / "protected"
        protected_dir.mkdir()

        test_config.output_dir = str(protected_dir)

        step = CleanStep(test_config)

        # 模拟权限错误
        with patch('shutil.rmtree', side_effect=PermissionError("Permission denied")):
            result = step.execute()

            assert result.success == False
            assert "权限错误" in result.message
            assert result.error is not None

    def test_clean_step_get_cleanup_targets(self, test_config):
        """测试获取清理目标列表"""
        step = CleanStep(test_config)
        targets = step.get_cleanup_targets()

        # 应该包含所有需要清理的路径
        expected_targets = [
            test_config.derived_data_path,
            test_config.output_dir,
            test_config.compile_commands_dir,
            test_config.index_symbols_file
        ]

        for target in expected_targets:
            assert target in targets