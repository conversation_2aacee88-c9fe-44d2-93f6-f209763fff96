"""
测试IndexStoreStep索引生成步骤 - TDD RED阶段

这些测试描述了IndexStoreStep的期望行为，当前应该失败。
"""

import pytest
import subprocess
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from pipeline.steps.index_store_step import IndexStoreStep
from pipeline.config import PipelineConfig
from pipeline.core import StepResult


class TestIndexStoreStep:
    """测试IndexStoreStep索引生成步骤"""

    def test_index_store_step_initialization(self, test_config):
        """测试IndexStoreStep初始化"""
        step = IndexStoreStep(test_config)

        assert step.name == "index-store"
        assert "索引生成" in step.description
        assert step.config == test_config
        assert step.schemes == test_config.schemes

    def test_index_store_step_validate_prerequisites_success(self, test_config, mock_workspace):
        """测试前置条件验证成功"""
        test_config.workspace_path = str(mock_workspace)
        step = IndexStoreStep(test_config)

        with patch('shutil.which', return_value='/usr/bin/xcodebuild'):
            assert step.validate_prerequisites() == True

    def test_index_store_step_validate_prerequisites_no_xcodebuild(self, test_config):
        """测试缺少xcodebuild工具"""
        step = IndexStoreStep(test_config)

        with patch('shutil.which', return_value=None):
            assert step.validate_prerequisites() == False

    def test_index_store_step_validate_prerequisites_no_workspace(self, test_config):
        """测试工作区不存在"""
        test_config.workspace_path = "/nonexistent/workspace.xcworkspace"
        step = IndexStoreStep(test_config)

        with patch('shutil.which', return_value='/usr/bin/xcodebuild'):
            assert step.validate_prerequisites() == False

    def test_index_store_step_execute_success(self, test_config, mock_workspace, mock_derived_data):
        """测试索引生成执行成功"""
        test_config.workspace_path = str(mock_workspace)
        test_config.derived_data_path = str(mock_derived_data.parent)
        test_config.schemes = ["TestScheme"]

        step = IndexStoreStep(test_config)

        # 模拟xcodebuild成功执行
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "** BUILD SUCCEEDED **"
        mock_result.stderr = ""

        with patch('subprocess.run', return_value=mock_result):
            result = step.execute()

        assert result.success == True
        assert "索引生成完成" in result.message
        assert "schemes_processed" in result.metrics
        assert result.metrics["schemes_processed"] == 1

    def test_index_store_step_execute_build_failure(self, test_config, mock_workspace):
        """测试构建失败"""
        test_config.workspace_path = str(mock_workspace)
        test_config.schemes = ["FailingScheme"]

        step = IndexStoreStep(test_config)

        # 模拟xcodebuild失败
        mock_result = MagicMock()
        mock_result.returncode = 65  # Xcode build failure
        mock_result.stdout = ""
        mock_result.stderr = "** BUILD FAILED **"

        with patch('subprocess.run', return_value=mock_result):
            result = step.execute()

        assert result.success == False
        assert "构建失败" in result.message
        assert result.error is not None

    def test_index_store_step_execute_multiple_schemes(self, test_config, mock_workspace):
        """测试多个scheme的处理"""
        test_config.workspace_path = str(mock_workspace)
        test_config.schemes = ["Scheme1", "Scheme2", "Scheme3"]

        step = IndexStoreStep(test_config)

        # 模拟所有构建成功
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "** BUILD SUCCEEDED **"
        mock_result.stderr = ""

        with patch('subprocess.run', return_value=mock_result) as mock_run:
            result = step.execute()

        assert result.success == True
        assert mock_run.call_count == 3  # 应该调用3次xcodebuild
        assert result.metrics["schemes_processed"] == 3

    def test_index_store_step_build_command_generation(self, test_config, mock_workspace):
        """测试构建命令生成"""
        test_config.workspace_path = str(mock_workspace)
        test_config.derived_data_path = "/custom/derived/data"

        step = IndexStoreStep(test_config)

        cmd = step.build_xcodebuild_command("TestScheme")

        # 验证命令包含必要的参数
        assert "xcodebuild" in cmd
        assert "-workspace" in cmd
        assert str(mock_workspace) in cmd
        assert "-scheme" in cmd
        assert "TestScheme" in cmd
        assert "-derivedDataPath" in cmd
        assert "/custom/derived/data" in cmd
        assert "COMPILER_INDEX_STORE_ENABLE=YES" in cmd