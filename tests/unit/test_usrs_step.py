"""
测试UsrsStep USR提取步骤 - TDD RED阶段
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from pipeline.steps.usrs_step import UsrsStep
from pipeline.config import PipelineConfig
from pipeline.core import StepResult


class TestUsrsStep:
    """测试UsrsStep USR提取步骤"""

    def test_usrs_step_initialization(self, test_config):
        """测试UsrsStep初始化"""
        step = UsrsStep(test_config)

        assert step.name == "usrs"
        assert "USR提取" in step.description
        assert step.config == test_config

    def test_usrs_step_validate_prerequisites_success(self, test_config, mock_derived_data):
        """测试前置条件验证成功"""
        test_config.derived_data_path = str(mock_derived_data.parent)
        test_config.indexstore_db_path = str(mock_derived_data.parent / "indexstore-db")

        # 创建indexstore-db工具
        (mock_derived_data.parent / "indexstore-db").mkdir()

        step = UsrsStep(test_config)
        assert step.validate_prerequisites() == True

    def test_usrs_step_execute_success(self, test_config, mock_derived_data, temp_dir):
        """测试USR提取执行成功"""
        test_config.derived_data_path = str(mock_derived_data.parent)
        test_config.index_symbols_file = str(temp_dir / "index_symbols.jsonl")

        step = UsrsStep(test_config)

        # 模拟indexstore-db成功执行
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = '{"usr": "c:@F@testFunction", "path": "/test.m"}\n'

        with patch('subprocess.run', return_value=mock_result):
            result = step.execute()

        assert result.success == True
        assert "USR提取完成" in result.message
        assert "usrs_extracted" in result.metrics

    def test_usrs_step_execute_tool_failure(self, test_config, mock_derived_data):
        """测试工具执行失败"""
        test_config.derived_data_path = str(mock_derived_data.parent)

        step = UsrsStep(test_config)

        # 模拟工具失败
        mock_result = MagicMock()
        mock_result.returncode = 1
        mock_result.stderr = "Tool execution failed"

        with patch('subprocess.run', return_value=mock_result):
            result = step.execute()

        assert result.success == False
        assert "工具执行失败" in result.message