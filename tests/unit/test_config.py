"""
测试Pipeline配置管理
"""

import pytest
import json
import os
from pathlib import Path
from unittest.mock import patch

from pipeline.config import PipelineConfig, load_default_config


class TestPipelineConfig:
    """测试PipelineConfig类"""

    def test_default_initialization(self):
        """测试默认初始化"""
        config = PipelineConfig()

        assert config.workspace_path == "hammmer-workspace/TencentMap.xcworkspace"
        assert config.schemes == ["QMapBusiness", "QMapMiddlePlatform"]
        assert config.parallel_jobs == 4
        assert config.retry_attempts == 3
        assert config.timeout_seconds == 3600
        assert config.log_level == "INFO"
        assert config.min_nodes_count == 1000

    def test_custom_initialization(self):
        """测试自定义初始化"""
        config = PipelineConfig(
            workspace_path="/custom/workspace.xcworkspace",
            schemes=["CustomScheme"],
            parallel_jobs=8,
            retry_attempts=5
        )

        assert config.workspace_path == "/custom/workspace.xcworkspace"
        assert config.schemes == ["CustomScheme"]
        assert config.parallel_jobs == 8
        assert config.retry_attempts == 5

    def test_from_file(self, temp_dir):
        """测试从文件加载配置"""
        config_file = temp_dir / "test_config.json"
        config_data = {
            "workspace_path": "/test/workspace.xcworkspace",
            "schemes": ["TestScheme1", "TestScheme2"],
            "parallel_jobs": 6,
            "retry_attempts": 2
        }

        with open(config_file, 'w') as f:
            json.dump(config_data, f)

        config = PipelineConfig.from_file(str(config_file))

        assert config.workspace_path == "/test/workspace.xcworkspace"
        assert config.schemes == ["TestScheme1", "TestScheme2"]
        assert config.parallel_jobs == 6
        assert config.retry_attempts == 2

    def test_from_file_not_found(self):
        """测试加载不存在的配置文件"""
        with pytest.raises(FileNotFoundError):
            PipelineConfig.from_file("/nonexistent/config.json")

    def test_to_file(self, temp_dir):
        """测试保存配置到文件"""
        config = PipelineConfig(
            workspace_path="/test/workspace.xcworkspace",
            schemes=["TestScheme"],
            parallel_jobs=2
        )

        config_file = temp_dir / "output_config.json"
        config.to_file(str(config_file))

        assert config_file.exists()

        with open(config_file, 'r') as f:
            saved_data = json.load(f)

        assert saved_data["workspace_path"] == "/test/workspace.xcworkspace"
        assert saved_data["schemes"] == ["TestScheme"]
        assert saved_data["parallel_jobs"] == 2