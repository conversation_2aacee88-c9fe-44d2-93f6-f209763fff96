"""
测试错误处理和重试机制 - TDD RED阶段

这些测试描述了我们期望的错误处理和重试行为，
当前应该失败，因为相关功能尚未实现。
"""

import pytest
import time
from unittest.mock import Mock, patch
from pathlib import Path

from pipeline.core import PipelineStep, StepResult
from pipeline.error_handling import (
    RetryableError, NonRetryableError, ErrorClassifier,
    RetryStrategy, ExponentialBackoffStrategy, FixedDelayStrategy,
    ErrorRecovery, PartialFailureRecovery
)


class TestErrorClassification:
    """测试错误分类功能"""

    def test_retryable_error_creation(self):
        """测试可重试错误创建"""
        error = RetryableError("Network timeout", original_error=TimeoutError("timeout"))

        assert error.message == "Network timeout"
        assert isinstance(error.original_error, TimeoutError)
        assert error.is_retryable() == True
        assert error.suggested_delay > 0

    def test_non_retryable_error_creation(self):
        """测试不可重试错误创建"""
        error = NonRetryableError("Invalid configuration", original_error=ValueError("bad config"))

        assert error.message == "Invalid configuration"
        assert isinstance(error.original_error, ValueError)
        assert error.is_retryable() == False
        assert error.suggested_action is not None

    def test_error_classifier_network_errors(self):
        """测试网络错误分类"""
        classifier = ErrorClassifier()

        # 网络超时 - 可重试
        timeout_error = TimeoutError("Connection timeout")
        classified = classifier.classify(timeout_error)
        assert isinstance(classified, RetryableError)
        assert classified.suggested_delay >= 1.0

        # 连接错误 - 可重试
        connection_error = ConnectionError("Connection refused")
        classified = classifier.classify(connection_error)
        assert isinstance(classified, RetryableError)

    def test_error_classifier_configuration_errors(self):
        """测试配置错误分类"""
        classifier = ErrorClassifier()

        # 文件不存在 - 不可重试
        file_error = FileNotFoundError("Config file not found")
        classified = classifier.classify(file_error)
        assert isinstance(classified, NonRetryableError)
        assert "检查文件路径" in classified.suggested_action

        # 权限错误 - 不可重试
        permission_error = PermissionError("Permission denied")
        classified = classifier.classify(permission_error)
        assert isinstance(classified, NonRetryableError)

    def test_error_classifier_resource_errors(self):
        """测试资源错误分类"""
        classifier = ErrorClassifier()

        # 内存不足 - 可重试（等待后）
        memory_error = MemoryError("Out of memory")
        classified = classifier.classify(memory_error)
        assert isinstance(classified, RetryableError)
        assert classified.suggested_delay >= 5.0  # 较长等待时间

        # 磁盘空间不足 - 不可重试
        disk_error = OSError("No space left on device")
        classified = classifier.classify(disk_error)
        assert isinstance(classified, NonRetryableError)


class TestRetryStrategies:
    """测试重试策略"""

    def test_exponential_backoff_strategy(self):
        """测试指数退避策略"""
        strategy = ExponentialBackoffStrategy(
            initial_delay=1.0,
            max_delay=60.0,
            backoff_factor=2.0
        )

        # 第一次重试
        delay1 = strategy.get_delay(attempt=1)
        assert delay1 == 1.0

        # 第二次重试
        delay2 = strategy.get_delay(attempt=2)
        assert delay2 == 2.0

        # 第三次重试
        delay3 = strategy.get_delay(attempt=3)
        assert delay3 == 4.0

        # 超过最大延迟
        delay_max = strategy.get_delay(attempt=10)
        assert delay_max == 60.0

    def test_fixed_delay_strategy(self):
        """测试固定延迟策略"""
        strategy = FixedDelayStrategy(delay=5.0)

        assert strategy.get_delay(attempt=1) == 5.0
        assert strategy.get_delay(attempt=5) == 5.0
        assert strategy.get_delay(attempt=10) == 5.0

    def test_retry_strategy_should_retry(self):
        """测试重试策略的重试判断"""
        strategy = ExponentialBackoffStrategy(max_attempts=3)

        assert strategy.should_retry(attempt=1) == True
        assert strategy.should_retry(attempt=2) == True
        assert strategy.should_retry(attempt=3) == True
        assert strategy.should_retry(attempt=4) == False


class MockRetryableStep(PipelineStep):
    """用于测试重试的模拟步骤"""

    def __init__(self, name: str, fail_times: int = 2):
        super().__init__(name, f"Mock retryable step: {name}")
        self.fail_times = fail_times
        self.attempt_count = 0

    def validate_prerequisites(self) -> bool:
        return True

    def execute(self) -> StepResult:
        self.attempt_count += 1

        if self.attempt_count <= self.fail_times:
            # 模拟网络超时错误
            raise TimeoutError(f"Mock timeout on attempt {self.attempt_count}")

        return StepResult(
            success=True,
            message=f"Success on attempt {self.attempt_count}",
            duration=0.1
        )


class TestStepRetryMechanism:
    """测试步骤重试机制"""

    def test_step_with_retry_success_after_failures(self):
        """测试步骤在失败后重试成功"""
        step = MockRetryableStep("retry_step", fail_times=2)

        # 配置重试策略
        step.retry_strategy = ExponentialBackoffStrategy(
            initial_delay=0.01,  # 快速测试
            max_attempts=5
        )
        step.error_classifier = ErrorClassifier()

        result = step.run_with_retry()

        assert result.success == True
        assert step.attempt_count == 3  # 失败2次，第3次成功
        assert step.retry_count == 2
        assert "Success on attempt 3" in result.message

    def test_step_with_retry_max_attempts_exceeded(self):
        """测试步骤超过最大重试次数"""
        step = MockRetryableStep("retry_step", fail_times=10)  # 总是失败

        step.retry_strategy = ExponentialBackoffStrategy(
            initial_delay=0.01,
            max_attempts=3
        )
        step.error_classifier = ErrorClassifier()

        result = step.run_with_retry()

        assert result.success == False
        assert step.attempt_count == 3  # 最多尝试3次
        assert step.retry_count == 2  # 重试2次
        assert "超过最大重试次数" in result.message

    def test_step_with_non_retryable_error(self):
        """测试步骤遇到不可重试错误"""
        step = MockRetryableStep("retry_step")

        # 模拟不可重试错误
        def mock_execute():
            raise FileNotFoundError("Config file not found")
        step.execute = mock_execute

        step.retry_strategy = ExponentialBackoffStrategy(max_attempts=5)
        step.error_classifier = ErrorClassifier()

        result = step.run_with_retry()

        assert result.success == False
        assert step.retry_count == 0  # 不应该重试
        assert "不可重试错误" in result.message