"""
测试Pipeline核心功能
"""

import pytest
import time
from unittest.mock import Mock, patch
from pathlib import Path

from pipeline.core import (
    Pipeline, PipelineStep, StepResult, StepStatus, PipelineStatus
)


class MockStep(PipelineStep):
    """用于测试的模拟步骤"""

    def __init__(self, name: str, should_fail: bool = False, duration: float = 0.1):
        super().__init__(name, f"Mock step: {name}")
        self.should_fail = should_fail
        self.mock_duration = duration
        self.validate_called = False
        self.execute_called = False

    def validate_prerequisites(self) -> bool:
        self.validate_called = True
        return not self.should_fail

    def execute(self) -> StepResult:
        self.execute_called = True
        time.sleep(self.mock_duration)

        if self.should_fail:
            return StepResult(
                success=False,
                message="Mock failure",
                duration=self.mock_duration
            )
        else:
            return StepResult(
                success=True,
                message="Mock success",
                duration=self.mock_duration,
                output_files=[Path("/mock/output.txt")],
                metrics={"processed_files": 10}
            )


class TestPipelineStep:
    """测试PipelineStep基类"""

    def test_step_initialization(self):
        """测试步骤初始化"""
        step = MockStep("test_step")

        assert step.name == "test_step"
        assert step.description == "Mock step: test_step"
        assert step.status == StepStatus.NOT_STARTED
        assert step.start_time is None
        assert step.end_time is None
        assert step.result is None
        assert step.dependencies == []
        assert step.retry_count == 0
        assert step.max_retries == 3

    def test_step_successful_execution(self):
        """测试步骤成功执行"""
        step = MockStep("success_step")
        result = step.run()

        assert step.validate_called
        assert step.execute_called
        assert step.status == StepStatus.COMPLETED
        assert result.success
        assert result.message == "Mock success"
        assert step.start_time is not None
        assert step.end_time is not None
        assert step.duration is not None
        assert step.duration > 0

    def test_step_failed_execution(self):
        """测试步骤执行失败"""
        step = MockStep("fail_step", should_fail=True)
        result = step.run()

        assert step.validate_called
        assert not step.execute_called  # 前置条件失败，不执行
        assert step.status == StepStatus.FAILED
        assert not result.success

    def test_step_execution_with_exception(self):
        """测试步骤执行异常"""
        step = MockStep("exception_step")

        # 模拟execute方法抛出异常
        def mock_execute():
            raise RuntimeError("Test exception")

        step.execute = mock_execute
        result = step.run()

        assert step.status == StepStatus.FAILED
        assert not result.success
        assert "执行异常" in result.message
        assert result.error is not None

    def test_step_progress(self):
        """测试步骤进度"""
        step = MockStep("progress_step")

        # 未开始
        assert step.get_progress() == 0.0

        # 运行中
        step.status = StepStatus.RUNNING
        assert step.get_progress() == 0.5

        # 完成
        step.status = StepStatus.COMPLETED
        assert step.get_progress() == 1.0

        # 失败
        step.status = StepStatus.FAILED
        assert step.get_progress() == 0.0

    def test_step_dependencies(self):
        """测试步骤依赖"""
        step = MockStep("dep_step")

        step.add_dependency("step1")
        step.add_dependency("step2")
        step.add_dependency("step1")  # 重复添加

        assert step.dependencies == ["step1", "step2"]


class TestPipeline:
    """测试Pipeline类"""

    def test_pipeline_initialization(self):
        """测试流水线初始化"""
        pipeline = Pipeline("Test Pipeline")

        assert pipeline.name == "Test Pipeline"
        assert pipeline.steps == {}
        assert pipeline.step_order == []
        assert pipeline.status == PipelineStatus.NOT_STARTED
        assert pipeline.start_time is None
        assert pipeline.end_time is None
        assert pipeline.current_step is None

    def test_add_step(self):
        """测试添加步骤"""
        pipeline = Pipeline()
        step1 = MockStep("step1")
        step2 = MockStep("step2")

        pipeline.add_step(step1).add_step(step2)

        assert len(pipeline.steps) == 2
        assert "step1" in pipeline.steps
        assert "step2" in pipeline.steps
        assert pipeline.step_order == ["step1", "step2"]

    def test_add_duplicate_step(self):
        """测试添加重复步骤"""
        pipeline = Pipeline()
        step1 = MockStep("step1")
        step2 = MockStep("step1")  # 同名步骤

        pipeline.add_step(step1)

        with pytest.raises(ValueError, match="步骤 step1 已存在"):
            pipeline.add_step(step2)

    def test_pipeline_successful_run(self):
        """测试流水线成功运行"""
        pipeline = Pipeline()
        step1 = MockStep("step1", duration=0.01)
        step2 = MockStep("step2", duration=0.01)

        pipeline.add_step(step1).add_step(step2)

        success = pipeline.run()

        assert success
        assert pipeline.status == PipelineStatus.COMPLETED
        assert step1.status == StepStatus.COMPLETED
        assert step2.status == StepStatus.COMPLETED
        assert pipeline.start_time is not None
        assert pipeline.end_time is not None
        assert pipeline.duration is not None

    def test_pipeline_failed_run(self):
        """测试流水线运行失败"""
        pipeline = Pipeline()
        step1 = MockStep("step1", duration=0.01)
        step2 = MockStep("step2", should_fail=True)
        step3 = MockStep("step3", duration=0.01)

        pipeline.add_step(step1).add_step(step2).add_step(step3)

        success = pipeline.run()

        assert not success
        assert pipeline.status == PipelineStatus.FAILED
        assert step1.status == StepStatus.COMPLETED
        assert step2.status == StepStatus.FAILED
        assert step3.status == StepStatus.NOT_STARTED  # 未执行

    def test_pipeline_partial_run(self):
        """测试流水线部分运行"""
        pipeline = Pipeline()
        step1 = MockStep("step1", duration=0.01)
        step2 = MockStep("step2", duration=0.01)
        step3 = MockStep("step3", duration=0.01)

        pipeline.add_step(step1).add_step(step2).add_step(step3)

        # 从step2开始运行
        success = pipeline.run(start_from="step2", stop_at="step2")

        assert success
        assert step1.status == StepStatus.NOT_STARTED
        assert step2.status == StepStatus.COMPLETED
        assert step3.status == StepStatus.NOT_STARTED

    def test_pipeline_status(self):
        """测试流水线状态"""
        pipeline = Pipeline("Test Pipeline")
        step1 = MockStep("step1")
        step2 = MockStep("step2")

        pipeline.add_step(step1).add_step(step2)

        status = pipeline.get_status()

        assert status["name"] == "Test Pipeline"
        assert status["status"] == PipelineStatus.NOT_STARTED.value
        assert status["current_step"] is None
        assert status["progress"] == 0.0
        assert status["completed_steps"] == 0
        assert status["total_steps"] == 2
        assert "steps" in status

    def test_pipeline_validate_dependencies(self):
        """测试流水线依赖验证"""
        pipeline = Pipeline()
        step1 = MockStep("step1")
        step2 = MockStep("step2")

        step2.add_dependency("step1")
        step2.add_dependency("nonexistent")  # 不存在的依赖

        pipeline.add_step(step1).add_step(step2)

        assert not pipeline.validate_dependencies()

        # 移除不存在的依赖
        step2.dependencies = ["step1"]
        assert pipeline.validate_dependencies()