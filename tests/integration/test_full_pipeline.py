"""
集成测试 - 完整Pipeline流程测试
"""

import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock

from pipeline import Pipeline, PipelineConfig
from pipeline.steps import CleanStep, IndexStoreStep, UsrsStep
from pipeline.error_handling import ExponentialBackoffStrategy, ErrorClassifier


class TestFullPipeline:
    """测试完整的Pipeline流程"""

    def test_pipeline_creation_and_setup(self, test_config):
        """测试Pipeline创建和设置"""
        pipeline = Pipeline("Test Pipeline")

        # 创建步骤
        clean_step = CleanStep(test_config)
        index_store_step = IndexStoreStep(test_config)
        usrs_step = UsrsStep(test_config)

        # 设置依赖关系
        index_store_step.add_dependency("clean")
        usrs_step.add_dependency("index-store")

        # 添加到流水线
        pipeline.add_step(clean_step)
        pipeline.add_step(index_store_step)
        pipeline.add_step(usrs_step)

        # 验证设置
        assert len(pipeline.steps) == 3
        assert "clean" in pipeline.steps
        assert "index-store" in pipeline.steps
        assert "usrs" in pipeline.steps

        # 验证依赖关系
        assert "clean" in index_store_step.dependencies
        assert "index-store" in usrs_step.dependencies

    def test_pipeline_dry_run(self, test_config, mock_workspace, temp_dir):
        """测试Pipeline干运行"""
        # 设置配置
        test_config.workspace_path = str(mock_workspace)
        test_config.derived_data_path = str(temp_dir / "DerivedData")
        test_config.output_dir = str(temp_dir / "output")

        pipeline = Pipeline("Dry Run Test")

        # 创建步骤（干运行模式）
        clean_step = CleanStep(test_config, dry_run=True)
        pipeline.add_step(clean_step)

        # 运行干运行
        success = pipeline.run()

        assert success == True

        # 验证状态
        status = pipeline.get_status()
        assert status["status"] == "completed"
        assert status["completed_steps"] == 1

    def test_pipeline_status_reporting(self, test_config):
        """测试Pipeline状态报告"""
        pipeline = Pipeline("Status Test")

        # 添加步骤
        clean_step = CleanStep(test_config)
        pipeline.add_step(clean_step)

        # 获取初始状态
        initial_status = pipeline.get_status()
        assert initial_status["status"] == "not_started"
        assert initial_status["progress"] == 0.0
        assert initial_status["completed_steps"] == 0

        # 运行Pipeline
        success = pipeline.run()
        assert success == True

        # 获取最终状态
        final_status = pipeline.get_status()
        assert final_status["status"] == "completed"
        assert final_status["progress"] == 1.0
        assert final_status["completed_steps"] == 1
        assert final_status["duration"] is not None