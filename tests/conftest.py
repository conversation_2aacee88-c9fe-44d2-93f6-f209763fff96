"""
pytest配置文件 - 定义测试夹具和配置
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pipeline.config import PipelineConfig
from pipeline.state import PipelineState


@pytest.fixture
def temp_dir():
    """创建临时目录夹具"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def test_config(temp_dir):
    """创建测试配置夹具"""
    config = PipelineConfig(
        workspace_path=str(temp_dir / "test_workspace.xcworkspace"),
        derived_data_path=str(temp_dir / "DerivedData"),
        output_dir=str(temp_dir / "ast_out"),
        compile_commands_dir=str(temp_dir / "compile_commands"),
        schemes=["TestScheme1", "TestScheme2"],
        parallel_jobs=2,
        retry_attempts=1,
        timeout_seconds=60,
        clang_library_path="/usr/lib/libclang.so",  # 测试路径
        indexstore_db_path=str(temp_dir / "indexstore-db"),
        min_nodes_count=10,
        min_edges_count=10,
        min_cross_repo_calls=1
    )
    return config


@pytest.fixture
def test_state(temp_dir):
    """创建测试状态管理器夹具"""
    state_file = temp_dir / "test_pipeline_state.json"
    return PipelineState(str(state_file))


@pytest.fixture
def mock_workspace(temp_dir):
    """创建模拟工作区夹具"""
    workspace_dir = temp_dir / "test_workspace.xcworkspace"
    workspace_dir.mkdir(parents=True)

    # 创建模拟的工作区文件
    (workspace_dir / "contents.xcworkspacedata").write_text("""
    <?xml version="1.0" encoding="UTF-8"?>
    <Workspace version="1.0">
        <FileRef location="group:TestProject.xcodeproj"></FileRef>
    </Workspace>
    """)

    return workspace_dir


@pytest.fixture
def mock_derived_data(temp_dir):
    """创建模拟的DerivedData夹具"""
    derived_data_dir = temp_dir / "DerivedData" / "GraphIndex"
    index_store_dir = derived_data_dir / "Index.noindex" / "DataStore"
    index_store_dir.mkdir(parents=True)

    # 创建一些模拟的索引文件
    (index_store_dir / "v5").mkdir()
    (index_store_dir / "v5" / "units").mkdir()
    (index_store_dir / "v5" / "records").mkdir()

    return derived_data_dir


@pytest.fixture
def mock_compile_commands(temp_dir):
    """创建模拟的编译命令文件夹具"""
    compile_commands_dir = temp_dir / "compile_commands"
    compile_commands_dir.mkdir(parents=True)

    # 创建模拟的编译命令文件
    test_cdb = [
        {
            "directory": str(temp_dir),
            "command": "clang -c test.m -o test.o",
            "file": str(temp_dir / "test.m")
        }
    ]

    import json
    (compile_commands_dir / "TestScheme1_cdb.json").write_text(
        json.dumps(test_cdb, indent=2)
    )

    return compile_commands_dir


@pytest.fixture
def mock_index_symbols(temp_dir):
    """创建模拟的符号索引文件夹具"""
    symbols_file = temp_dir / "index_symbols.jsonl"

    # 创建模拟的符号数据
    symbols_data = [
        '{"usr": "c:@F@testFunction", "path": "/path/to/test.m"}',
        '{"usr": "c:@C@TestClass", "path": "/path/to/TestClass.h"}'
    ]

    symbols_file.write_text('\n'.join(symbols_data))
    return symbols_file


@pytest.fixture
def mock_ast_output(temp_dir):
    """创建模拟的AST输出文件夹具"""
    ast_dir = temp_dir / "ast_out"
    ast_dir.mkdir(parents=True)

    # 创建模拟的节点和边文件
    nodes_data = [
        '{"label": "File", "id": "file1", "attrs": {"path": "/test.m"}}',
        '{"label": "Method", "id": "method1", "attrs": {"name": "testMethod"}}'
    ]

    edges_data = [
        '{"type": "DEFINES", "src": "file1", "dst": "method1", "attrs": {}}'
    ]

    (ast_dir / "nodes.jsonl").write_text('\n'.join(nodes_data))
    (ast_dir / "edges.jsonl").write_text('\n'.join(edges_data))

    return ast_dir


@pytest.fixture
def mock_subprocess():
    """模拟subprocess调用夹具"""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = 0
        mock_run.return_value.stdout = "Mock output"
        mock_run.return_value.stderr = ""
        yield mock_run


@pytest.fixture
def mock_environment():
    """模拟环境变量夹具"""
    with patch.dict(os.environ, {
        'CLANG_LIBRARY_FILE': '/usr/lib/libclang.so',
        'PIPELINE_LOG_LEVEL': 'DEBUG'
    }):
        yield