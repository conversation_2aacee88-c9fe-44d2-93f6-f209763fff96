#!/usr/bin/env python3
"""
Analyze Property Node Value

This script analyzes the actual value and usage of Property nodes in the knowledge graph.
"""

from neo4j import GraphDatabase
from pathlib import Path

def main():
    driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '6536772a'))

    with driver.session() as session:
        print('🔍 深入分析Property节点的实际价值')
        print('=' * 60)
        
        # 1. Property节点的基本统计
        result = session.run('MATCH (p:Property) RETURN count(p) as total_properties')
        total_properties = result.single()['total_properties']
        print(f'📊 Property节点总数: {total_properties:,}')
        
        # 2. Property节点在架构分析中的作用
        result = session.run('''
            MATCH (c:Class)-[:DEFINES]->(p:Property)
            RETURN c.name as class_name, count(p) as property_count
            ORDER BY property_count DESC
            LIMIT 10
        ''')
        
        print('\n🏗️ 类的属性复杂度分析 (Top 10):')
        for record in result:
            class_name = record['class_name']
            property_count = record['property_count']
            print(f'  {class_name}: {property_count} 个属性')
        
        # 3. Property节点的访问模式分析
        result = session.run('''
            MATCH (accessor)-[:ACCESSES]->(p:Property)
            RETURN labels(accessor) as accessor_type, count(*) as access_count
            ORDER BY access_count DESC
        ''')
        
        print('\n📊 Property访问模式分析:')
        for record in result:
            accessor_type = ', '.join(record['accessor_type'])
            access_count = record['access_count']
            print(f'  {accessor_type} -> Property: {access_count:,} 次访问')
        
        # 4. 分析Property节点的数据质量
        result = session.run('''
            MATCH (p:Property)
            RETURN 
                CASE 
                    WHEN p.type = 'virtual_property' THEN 'virtual'
                    WHEN p.type IS NOT NULL THEN 'typed'
                    ELSE 'untyped'
                END as property_category,
                count(*) as count
        ''')
        
        print('\n📊 Property节点数据质量分析:')
        for record in result:
            category = record['property_category']
            count = record['count']
            print(f'  {category}: {count:,} 个')
        
        # 5. 分析Property节点对架构理解的贡献
        result = session.run('''
            MATCH (f:File)-[:DEFINES]->(c:Class)-[:DEFINES]->(p:Property)
            WHERE p.type <> 'virtual_property'
            RETURN f.name as file_name, c.name as class_name, 
                   collect(p.name) as properties, count(p) as prop_count
            ORDER BY prop_count DESC
            LIMIT 5
        ''')
        
        print('\n🔍 真实Property对类结构的贡献:')
        for record in result:
            file_name = Path(record['file_name']).name
            class_name = record['class_name']
            properties = record['properties'][:5]  # 只显示前5个
            prop_count = record['prop_count']
            print(f'  {file_name}::{class_name} ({prop_count}个属性)')
            print(f'    属性: {", ".join(properties)}{"..." if prop_count > 5 else ""}')
            print()
        
        # 6. 分析Property节点的关系网络价值
        result = session.run('''
            MATCH (p:Property)-[r]->(target)
            RETURN type(r) as relation_type, labels(target) as target_type, count(*) as count
            ORDER BY count DESC
        ''')
        
        print('📊 Property节点的关系网络:')
        for record in result:
            relation_type = record['relation_type']
            target_type = ', '.join(record['target_type'])
            count = record['count']
            print(f'  Property -> {relation_type} -> {target_type}: {count:,}')
        
        # 7. 对比有Property和无Property的类的复杂度
        result = session.run('''
            MATCH (c:Class)
            OPTIONAL MATCH (c)-[:DEFINES]->(p:Property)
            OPTIONAL MATCH (c)-[:DEFINES]->(m:Method)
            WITH c, count(p) as prop_count, count(m) as method_count
            RETURN
                CASE WHEN prop_count > 0 THEN 'with_properties' ELSE 'without_properties' END as class_type,
                avg(method_count) as avg_methods,
                count(c) as class_count
        ''')

        print('\n📊 有/无Property的类复杂度对比:')
        for record in result:
            class_type = record['class_type']
            avg_methods = record['avg_methods']
            class_count = record['class_count']
            print(f'  {class_type}: {class_count} 个类, 平均 {avg_methods:.1f} 个方法')
        
        # 8. 分析Property节点对依赖分析的价值
        result = session.run('''
            MATCH (f1:File)-[:DEFINES]->(c1:Class)-[:DEFINES]->(m:Method)-[:ACCESSES]->(p:Property)<-[:DEFINES]-(c2:Class)<-[:DEFINES]-(f2:File)
            WHERE f1 <> f2
            RETURN f1.name as caller_file, f2.name as callee_file, count(*) as property_dependencies
            ORDER BY property_dependencies DESC
            LIMIT 10
        ''')
        
        print('\n🌐 基于Property的跨文件依赖:')
        cross_file_property_deps = 0
        for record in result:
            caller_file = Path(record['caller_file']).name
            callee_file = Path(record['callee_file']).name
            property_dependencies = record['property_dependencies']
            cross_file_property_deps += property_dependencies
            print(f'  {caller_file} -> {callee_file}: {property_dependencies} 个属性依赖')
        
        print(f'\n📈 跨文件属性依赖总数: {cross_file_property_deps}')

    driver.close()

if __name__ == "__main__":
    main()
